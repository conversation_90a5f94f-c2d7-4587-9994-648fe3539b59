package com.sankuai.shangou.seashop.mall.server;

import com.sankuai.shangou.seashop.GatewayApplication;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.product.ProductExportGetter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/07/31 10:39
 */
@Slf4j
@SpringBootTest(classes = GatewayApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class ExportTaskTest {

    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;

    @Resource
    private ProductExportGetter productExportGetter;

    @Test
    public void main() {
        String body = "{\"taskId\":\"1811587475754549262\",\"taskType\":201150011,\"executeParam\":\"{\\\"operationShopId\\\":0,\\\"pageSize\\\":10,\\\"pageNo\\\":1,\\\"user\\\":{\\\"operationShopId\\\":0,\\\"userId\\\":677,\\\"userName\\\":\\\"caohaitao\\\"},\\\"shop\\\":{\\\"operationShopId\\\":0,\\\"shopId\\\":162},\\\"tab\\\":\\\"REFUND_ALL\\\"}\",\"cost\":0,\"count\":0,\"taskDate\":\"2024-07-31 10:27:38\"}";
        try {
            ExportTask task = JsonUtil.parseObject(body, ExportTask.class);
            sellerExportTaskBiz.execute(task);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void selectData() {
        String body = "{\"skuAutoId\":\"\",\"statusCode\":\"1\",\"checkTime\":null,\"productId\":null,\"productName\":null,\"productCode\":null,\"categoryIds\":null,\"startTime\":null,\"endTime\":null,\"brandName\":null,\"whetherBelowSafeStock\":false,\"freightTemplateId\":null,\"shopCategoryId\":null}";
        try {
            QueryProductReq task = JsonUtil.parseObject(body, QueryProductReq.class);
            productExportGetter.selectData(task);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
