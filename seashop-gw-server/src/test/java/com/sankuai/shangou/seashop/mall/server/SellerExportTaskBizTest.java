package com.sankuai.shangou.seashop.mall.server;

import com.sankuai.shangou.seashop.GatewayApplication;
import com.sankuai.shangou.seashop.base.export.writter.ExcelWriteDelegator;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.seller.core.service.export.impl.SellerExportTaskBizImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = GatewayApplication.class)
public class SellerExportTaskBizTest {
    @Resource
    private SellerExportTaskBizImpl sellerExportTaskBiz;
    @Resource
    private OrderQueryFeign orderQueryFeign;

    private ExcelWriteDelegator excelWriteDelegator = new ExcelWriteDelegator();

    @Test
    public void test() {
    }

}
