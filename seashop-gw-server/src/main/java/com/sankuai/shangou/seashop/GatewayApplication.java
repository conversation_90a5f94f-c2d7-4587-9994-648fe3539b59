package com.sankuai.shangou.seashop;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class,
    scanBasePackages = {
        "com.sankuai.shangou.seashop",
    })
@EnableFeignClients(basePackages = {
    "com.sankuai.shangou.seashop.user",
    "com.sankuai.shangou.seashop.base",
    "com.sankuai.shangou.seashop.product",
    "com.sankuai.shangou.seashop.order",
    "com.sankuai.shangou.seashop.promotion",
    "com.sankuai.shangou.seashop.pay",
    "com.sankuai.shangou.seashop.erp",
    "com.sankuai.shangou.seashop.trade",
        "com.hishop.himall.report.api"
})
@EnableTransactionManagement
@EnableDiscoveryClient
//开启aop
@EnableAspectJAutoProxy
public class GatewayApplication {
    private static final Logger log = LoggerFactory.getLogger(GatewayApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
        log.info("服务启动成功！");
    }
}