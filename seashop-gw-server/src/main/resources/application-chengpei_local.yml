server:
  port: 8081
spring:
  elasticsearch:
    uris: http://**************:9200
logging:
  level:
    org.apache.rocketmq: debug
    com.baomidou.mybatisplus: info
    com.sankuai.shangou.seashop: debug
s3:
  url:
    header: https://himall-obs.35hiw.com

venus:
  enabled: true
  bucket: seashopimagetest
  clientId: 8scckx46q4cjn4kq000000000054eb54
  hostName: http://p0.inf.test.sankuai.com/seashopimagetest/
  env: test
s3plus:
  hostName: https://msstest.sankuai.com
  bucketName: seashop-test

seashop:
  export:
    bizConfig:

hishop:
  storage:
    storage-type: OBS
    bucket-name: himall-test
    endpoint: https://obs.cn-south-1.myhuaweicloud.com
    access-key: UEDFC3T2O7J1RXQQIAVO
    secret-key: FzRPLhtiitbYO3MTrXxArFLUT3KBIkIdmw9MIKEL
    domain: https://himall-obs.35hiw.com
    base-path: /himall-base/rs/${spring.application.name}
    rest: true

squirrel:
  category: sg-seashop-cache
  clusterName: redis-sg-common_qa
erp:
  image:
    product-image-prefix: http://p0.inf.test.sankuai.com/seashopimagetest/
  mt:
    app-key: open9wxozsffy8a
    app-secret: oasigsab196d94piulmft
    encrypt-secret: rYveiVvfunsa0N34
    open-api-url: https://waimai-openapi.apigw.test.meituan.com/api/sgb2b/
    open-api-key: himall
    open-api-secret: yJJsuqr2DR7hkS6XzxD8HpseltPZuR5R
  jst:
    app-key: b0b7d1db226d4216a3d58df9ffa2dde5
    app-secret: 99c4cef262f34ca882975a7064de0b87
    api-url: https://dev-api.jushuitan.com/open/
    token-api-url: https://dev-api.jushuitan.com/openWeb/
    authorization-url: https://isv-openweb.jushuitan.com/auth/
  wdt:
    app-key: f8b652468b5badbe
    app-secret: 362f69faf8b652468b5badbe8116c5de
  blp:
    app-key: b106d477384d472098df5b3fadbfb0ce
    app-secret: 67730acce80048828f7663e8289ed8cb
  yjp:
    app-key: open9wxozsffy8a
    app-secret: oasqb56kj5h2t5c3g3n4f
    open-api-url: https://openapi-trading.test.yijiupidev.com/openapi/
    open-api-key: 15091370
    open-api-secret: e7KSi69A5dpmUNZx
  sms:
    jst-token-expire-notice-template-id: 2007969

task:
  export:
    enable: true
himall:
  #拦截前端页面埋点
  pageFilter: mallApi/apiTopic/getPCIndex,mallApi/apiTradeProduct/queryProductBaseInfo,mallApi/apiTopic/getSellerPCIndex,mallApi/apiTopic/getWapIndex
himall-trade:
  dev:
    url: http://localhost:8084
himall-order:
  dev:
    url: http://localhost:8083
himall-base:
  dev:
    url: http://localhost:8082
himall-report:
  dev:
    url: http://localhost:8085