package com.sankuai.sgb2b.seashop.mall.api.thrift.request.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

@ThriftEnum
public enum ClientTypeEnum {
    Default("0", "页脚服务");

    private String code;
    private String desc;

    ClientTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClientTypeEnum getByCode(String code) {
        for (ClientTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @ThriftEnumValue
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
