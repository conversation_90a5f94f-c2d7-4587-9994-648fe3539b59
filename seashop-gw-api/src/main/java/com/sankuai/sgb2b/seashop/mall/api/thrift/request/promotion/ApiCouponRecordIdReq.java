package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2024/2/1/001
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "优惠券查询请求对象")
public class ApiCouponRecordIdReq extends BaseParamReq {

    @FieldDoc(description = "优惠券记录ID")
    private Long couponRecordId;

    @Override
    public void checkParameter() {
        if (null == this.couponRecordId || this.couponRecordId <= 0) {
            throw new InvalidParamException("优惠券记录ID不能为空");
        }
    }


}
