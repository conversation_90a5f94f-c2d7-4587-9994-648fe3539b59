package com.sankuai.sgb2b.seashop.mall.api.thrift.request.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

@ThriftEnum
public enum CategoryTypeEnum {
    PageFootService(1, "底部帮助"),
    SystemMessage(2, "系统快报 "),
    InfoCenter(3, "商城公告"),
    PlatformNews(4, "供应商后台公告");

    private Integer code;
    private String desc;

    CategoryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CategoryTypeEnum getByCode(Integer code) {
        for (CategoryTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
