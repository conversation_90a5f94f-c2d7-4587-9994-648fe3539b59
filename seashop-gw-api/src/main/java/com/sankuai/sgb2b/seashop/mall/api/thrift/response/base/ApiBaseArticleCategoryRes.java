package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "文章分类返回")
@Data
public class ApiBaseArticleCategoryRes extends BaseThriftDto {

    @FieldDoc(description = "文章分类id")
    private Long id;

    /**
     * 父分类id
     */
    @FieldDoc(description = "父分类id")
    private Long parentCategoryId;

    /**
     * 分类名称
     */
    @FieldDoc(description = "分类名称")
    private String name;

    /**
     * 排序字段
     */
    @FieldDoc(description = "排序字段")
    private Long displaySequence;

    /**
     * 是否默认
     */
    @FieldDoc(description = "是否默认")
    private Boolean isDefault;

    @FieldDoc(description = "子分类")
    private List<ApiBaseArticleCategoryChildrenRes> childrens;


}
