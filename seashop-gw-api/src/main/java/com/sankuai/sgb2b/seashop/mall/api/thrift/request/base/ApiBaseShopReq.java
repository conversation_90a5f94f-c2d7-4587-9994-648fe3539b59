package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import lombok.Data;

@TypeDoc(description = "通用请求对象，适用于id与店铺id做联合主键的一些通用场景，如删除、获取等")
@Data
public class ApiBaseShopReq extends BaseReq {

    @FieldDoc(description = "店铺id")
    private Long shopId;

    public void checkParameter() {
        if (this.shopId == null) {
            throw new IllegalArgumentException("店铺id不能为空");
        }

    }


}
