package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "外观-查询支付方式请求入参")
public class ApiQueryPayChannelReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private List<String> orderIdList;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(orderIdList)) {
            throw new InvalidParamException("orderIdList不能为空");
        }
    }


}
