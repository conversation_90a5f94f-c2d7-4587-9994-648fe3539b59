package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiAddonSummaryDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiTradeProductDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "搜索凑单商品返回对象")
@ToString
@Data
public class ApiSearchAddonProductResp extends BaseThriftDto {

    @FieldDoc(description = "商品分页列表")
    private BasePageResp<ApiTradeProductDto> productPage;
    @FieldDoc(description = "凑单汇总描述")
    private ApiAddonSummaryDto addonSummary;


}
