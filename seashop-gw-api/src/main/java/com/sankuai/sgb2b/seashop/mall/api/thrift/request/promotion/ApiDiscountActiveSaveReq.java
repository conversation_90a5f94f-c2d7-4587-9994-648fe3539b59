package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保存折扣活动请求对象")
public class ApiDiscountActiveSaveReq extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @FieldDoc(description = "活动名称", requiredness = Requiredness.REQUIRED)
    private String activeName;

    @FieldDoc(description = "开始时间", requiredness = Requiredness.REQUIRED)
    private Date startTime;

    @FieldDoc(description = "结束时间", requiredness = Requiredness.REQUIRED)
    private Date endTime;

    @FieldDoc(description = "优化条件", requiredness = Requiredness.REQUIRED)
    private List<ApiDiscountActiveRuleReq> ruleList;

    @FieldDoc(description = "产品ID列表")
    private List<Long> productIdList;

    @Override
    public void valueInit() {

    }

    @Override
    public void checkParameter() {
        if (this.shopId == null) {
            throw new IllegalArgumentException("shopId不能为空");
        }
        if (StringUtils.isBlank(this.activeName)) {
            throw new IllegalArgumentException("activeName不能为空");
        }
        if (this.startTime == null) {
            throw new IllegalArgumentException("startTime不能为空");
        }
        if (this.endTime == null) {
            throw new IllegalArgumentException("endTime不能为空");
        }
        if (this.startTime.after(this.endTime)) {
            throw new IllegalArgumentException("startTime不能大于endTime");
        }
        if (this.ruleList == null || this.ruleList.isEmpty()) {
            throw new IllegalArgumentException("ruleList不能为空");
        }
        if (this.ruleList.size() > 5) {
            throw new IllegalArgumentException("ruleList不能超过5条");
        }
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
