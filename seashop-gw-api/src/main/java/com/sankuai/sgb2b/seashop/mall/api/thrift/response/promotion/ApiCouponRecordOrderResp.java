package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@TypeDoc(description = "订单优惠券记录响应体")
@Data
public class ApiCouponRecordOrderResp extends BaseThriftDto {

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "优惠券记录列表")
    private List<ApiCouponRecordSimpleResp> recordList;


}
