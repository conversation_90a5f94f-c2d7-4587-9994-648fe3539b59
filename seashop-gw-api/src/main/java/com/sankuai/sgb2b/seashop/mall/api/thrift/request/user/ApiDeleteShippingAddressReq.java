package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "删除收货地址请求入参")
@Getter
@Setter
public class ApiDeleteShippingAddressReq extends BaseParamReq {

    @FieldDoc(description = "需要删除的收货地址ID列表，列表是支持批量删除")

    private List<Long> idList;

    /**
     * 参数校验
     */
    public void checkParameter() {
        if (CollUtil.isEmpty(this.idList)) {
            throw new InvalidParamException("请选择要删除的数据");
        }
    }

}
