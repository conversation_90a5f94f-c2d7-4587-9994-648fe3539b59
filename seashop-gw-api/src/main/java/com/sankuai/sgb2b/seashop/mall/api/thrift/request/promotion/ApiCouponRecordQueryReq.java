package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "优惠券查询请求对象")
public class ApiCouponRecordQueryReq extends BasePageReq {

    @FieldDoc(description = "用户ID")
    private Long userId;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "优惠券活动ID")
    private Long couponId;

    @FieldDoc(description = "状态 0-未使用 1-已使用 2-已过期")
    private Integer status;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "优惠券名称")
    private String couponName;


}
