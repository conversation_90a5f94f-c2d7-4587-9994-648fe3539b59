package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/22 9:57
 */
@Data
@ToString
@TypeDoc(description = "查询投诉维权返参")
public class ApiQueryOrderRightsResp extends BaseThriftDto {

    @FieldDoc(description = "订单ID")
    private Long orderId;

    @FieldDoc(description = "下单时间")
    private Date createTime;

    @FieldDoc(description = "订单详情的商品主图地址")
    @JsonUrlFormat(deserializer = false)
    private List<ApiOrderRightImagePathResp> imagePath;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


}
