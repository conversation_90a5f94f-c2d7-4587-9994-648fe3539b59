package com.sankuai.sgb2b.seashop.mall.api.thrift.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询类目入参")
public class ApiQueryCategoryTreeReq extends BaseParamReq {

    @FieldDoc(description = "展示状态 0-全部 1-展示开启 2-展示关闭", requiredness = Requiredness.NONE)
    private Integer showStatus;

    @FieldDoc(description = "排除没有下级的类目", requiredness = Requiredness.NONE)
    private Boolean filterNoChildren;

    @FieldDoc(description = "排除没有销售商品的类目")
    private Boolean excludeNoSale;

}
