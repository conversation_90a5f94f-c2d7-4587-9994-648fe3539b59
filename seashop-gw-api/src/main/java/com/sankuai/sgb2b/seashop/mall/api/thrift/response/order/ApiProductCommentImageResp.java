package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/04 16:24
 */
@Data
@ToString
@TypeDoc(description = "商品评价图片对象")
public class ApiProductCommentImageResp extends BaseThriftDto {

    @FieldDoc(description = "评论图片")
    @JsonUrlFormat(deserializer = false)
    private String commentImage;

    @FieldDoc(description = "风控状态 0-待审核 1-审核通过 2-审核拒绝")
    private Integer riskStatus;

    @FieldDoc(description = "风控状态描述")
    private String riskStatusDesc;

    @FieldDoc(description = "风控不通过原因")
    private String riskMsg;


}
