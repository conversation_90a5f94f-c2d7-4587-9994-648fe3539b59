package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 发送验证码请求参数类
 * @author: LXH
 **/
@TypeDoc(description = "发送验证码请求参数类")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiCmdSendCodeReq {
    //联系方式
    @FieldDoc(description = "联系方式")
    private String contact;
    //联系方式类型
    @FieldDoc(description = "联系方式类型 SMS:短信 Email:邮箱")
    private String contactType;


}
