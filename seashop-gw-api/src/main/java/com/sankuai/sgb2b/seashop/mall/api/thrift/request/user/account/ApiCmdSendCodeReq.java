package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "发送验证码请求类")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiCmdSendCodeReq extends BaseThriftDto {
    @FieldDoc(description = "联系方式")
    private String contact;
    @FieldDoc(description = "联系方式类型 SMS:短信 Email:邮箱")
    private String contactType;
    @FieldDoc(description = "key")
    private String imageKey;
    @FieldDoc(description = "图片验证码")
    private String imageCode;


}
