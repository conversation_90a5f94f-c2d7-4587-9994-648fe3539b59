package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 20:26
 */
@Data
@ToString
@TypeDoc(description = "查询最后一级类目的返回值")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApiQueryLastCategoryResp extends BaseThriftDto {

    @FieldDoc(description = "类目列表")
    private List<CategoryDto> categoryList;

    @FieldDoc(description = "需要填写的表单数据")
    private List<CategoryApplyFormDto> formList;


}
