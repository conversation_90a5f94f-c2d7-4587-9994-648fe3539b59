package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @description: 供应商列表查询入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "商家添加商品到购物车请求入参")
@Getter
@Setter
public class ApiQueryShopPageReq extends BasePageReq {
    /**
     * 供应商ID列表
     */

    @FieldDoc(description = "供应商ID列表", requiredness = Requiredness.REQUIRED)
    private List<Long> shopIds;

    @FieldDoc(description = "商家用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
}
