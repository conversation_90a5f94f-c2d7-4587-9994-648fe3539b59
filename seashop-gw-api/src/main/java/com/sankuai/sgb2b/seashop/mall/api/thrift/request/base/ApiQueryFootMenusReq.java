package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:46
 */
@Data
@TypeDoc(description = "底部导航栏入参")
public class ApiQueryFootMenusReq extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "菜单类型（1代表微信、2代表小程序）")
    private Integer type;

    @FieldDoc(description = "店铺Id(0默认是平台)")
    private Long shopId = 0L;


}
