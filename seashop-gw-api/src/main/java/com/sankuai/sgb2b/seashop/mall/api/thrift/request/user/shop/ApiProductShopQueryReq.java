package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "店铺信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiProductShopQueryReq {
    //店铺id
    @FieldDoc(description = "店铺id")
    private Long shopId;
    //商品ID
    @FieldDoc(description = "商品ID")
    private String productId;
    //商品ID
    @FieldDoc(description = "商品ID")
    private Long userId;


}
