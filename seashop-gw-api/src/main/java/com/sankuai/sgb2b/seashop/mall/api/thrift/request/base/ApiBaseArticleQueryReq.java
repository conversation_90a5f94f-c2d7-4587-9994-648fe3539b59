package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

@TypeDoc(description = "文章查询对象")
@Data
public class ApiBaseArticleQueryReq extends BasePageReq {
    @FieldDoc(description = "文章标题")
    private String title;
    @FieldDoc(description = "文章分类id")
    private Long categoryId;
    /**
     * 是否显示
     */
    @FieldDoc(description = "是否显示")
    private Boolean isRelease;


}
