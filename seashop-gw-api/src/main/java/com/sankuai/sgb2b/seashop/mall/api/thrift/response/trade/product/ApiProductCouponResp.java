package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "商品优惠券返回值")
public class ApiProductCouponResp extends BaseThriftDto {

    @FieldDoc(description = "优惠券id")
    private Long id;

    @FieldDoc(description = "优惠券金额")
    private BigDecimal price;

    @FieldDoc(description = "最大可领取张数")
    private Integer perMax;

    @FieldDoc(description = "订单金额（满足多少钱才能使用）")
    private BigDecimal orderAmount;

    @FieldDoc(description = "发行张数")
    private Integer num;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "优惠券名称")
    private String couponName;

    @FieldDoc(description = "使用范围：0=全场通用，1=部分商品可用")
    private Integer useArea;

    @FieldDoc(description = "是否已领取")
    private Boolean userReceived;

    @FieldDoc(description = "用户领取的数量")
    private Integer userReceivedNum;


    public String getPriceString() {
        return this.bigDecimal2String(this.price);
    }


    public void setPriceString(String price) {
        this.price = this.string2BigDecimal(price);
    }


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
