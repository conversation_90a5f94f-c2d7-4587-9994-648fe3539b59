package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/6/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保存折扣活动规则请求对象")
public class ApiDiscountActiveRuleReq extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "活动ID")
    private Long activeId;

    @FieldDoc(description = "满减的条件")
    private BigDecimal quota;

    @FieldDoc(description = "满减的金额")
    private BigDecimal discount;


    public String getQuotaString() {
        return this.bigDecimal2String(this.quota);
    }


    public void setQuotaString(String quota) {
        this.quota = this.string2BigDecimal(quota);
    }


    public String getDiscountString() {
        return this.bigDecimal2String(this.discount);
    }


    public void setDiscountString(String discount) {
        this.discount = this.string2BigDecimal(discount);
    }
}