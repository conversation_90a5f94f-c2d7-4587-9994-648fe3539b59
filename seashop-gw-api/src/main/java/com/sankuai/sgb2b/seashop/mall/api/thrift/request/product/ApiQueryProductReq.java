package com.sankuai.sgb2b.seashop.mall.api.thrift.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/16 12:43
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询商品入参")
public class ApiQueryProductReq extends BasePageReq {

    @FieldDoc(description = "商品id", requiredness = Requiredness.NONE)
    private Long productId;

    @FieldDoc(description = "规格自增id", requiredness = Requiredness.NONE)
    private Long skuAutoId;

    @FieldDoc(description = "商品名称", requiredness = Requiredness.NONE)
    private String productName;

    @FieldDoc(description = "商品编码", requiredness = Requiredness.NONE)
    private String productCode;

    @FieldDoc(description = "店铺分类id", requiredness = Requiredness.NONE)
    private Long shopCategoryId;

    @FieldDoc(description = "品牌名称", requiredness = Requiredness.NONE)
    private String brandName;

    @FieldDoc(description = "开始时间", requiredness = Requiredness.NONE)
    private Date startTime;

    @FieldDoc(description = "结束时间", requiredness = Requiredness.NONE)
    private Date endTime;

    @FieldDoc(description = "警戒库存", requiredness = Requiredness.NONE)
    private Boolean whetherBelowSafeStock;

    @FieldDoc(description = "商品状态 1-销售中 2-仓库中 3-违规下架 4-草稿箱", requiredness = Requiredness.NONE)
    private Integer statusCode;

    @FieldDoc(description = "店铺名称", requiredness = Requiredness.NONE)
    private String shopName;

    @FieldDoc(description = "店铺id", requiredness = Requiredness.NONE)
    private Long shopId;

    @FieldDoc(description = "类目路径 | 隔开", requiredness = Requiredness.NONE)
    private String categoryPath;

    @FieldDoc(description = "排序字段", requiredness = Requiredness.NONE)
    private List<FieldSortReq> sortList;

    @FieldDoc(description = "商品id列表", requiredness = Requiredness.NONE)
    private List<Long> productIds;

    @FieldDoc(description = "类目id列表", requiredness = Requiredness.NONE)
    private List<Long> categoryIds;

    @FieldDoc(description = "运费模板id", requiredness = Requiredness.NONE)
    private Long freightTemplateId;

    @FieldDoc(description = "来源 1-商城 2-牵牛花 3-易久批", requiredness = Requiredness.NONE)
    private Integer source;

    @FieldDoc(description = "类目id", requiredness = Requiredness.OPTIONAL)
    private Long categoryId;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
