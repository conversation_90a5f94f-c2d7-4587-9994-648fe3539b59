package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Data
@TypeDoc(description = "会话信息")
@NoArgsConstructor
@AllArgsConstructor
public class HiChatSessionListResp extends BaseThriftDto {
    @FieldDoc(description = "店铺名")
    private List<HiChatSessionResp> sessions;


}
