package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/30 17:55
 */
@Data
public class ApiBaseMsgTemplateResp {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "模版标题")
    private String title;

    @FieldDoc(description = "模板编号")
    private String templateNum;

    @FieldDoc(description = "场景说明")
    private String description;

    @FieldDoc(description = "模板ID")
    private String templateId;

    @FieldDoc(description = "消息类别")
    private Integer messageType;


}
