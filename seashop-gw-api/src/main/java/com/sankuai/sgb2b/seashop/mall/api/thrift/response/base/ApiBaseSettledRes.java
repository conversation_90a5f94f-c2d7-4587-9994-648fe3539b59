package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@TypeDoc(description = "入驻设置")
@Data
public class ApiBaseSettledRes extends BaseThriftDto {
    private Long id;

    @FieldDoc(description = "商家类型 0、仅企业可入驻；1、仅个人可入驻；2、企业和个人均可", requiredness = Requiredness.REQUIRED)
    private int businessType;


    @FieldDoc(description = "商家结算类型 0、仅银行账户；1、仅微信账户；2、银行账户及微信账户均可", requiredness = Requiredness.REQUIRED)
    private int settlementAccountType;

    @FieldDoc(description = "试用天数", requiredness = Requiredness.REQUIRED)
    private Integer trialDays;

    @FieldDoc(description = "地址必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isCity;

    @FieldDoc(description = "数必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isPeopleNumber;

    @FieldDoc(description = "详细地址必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isAddress;

    @FieldDoc(description = "营业执照号必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isBusinessLicenseCode;

    @FieldDoc(description = "经营范围必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isBusinessScope;

    @FieldDoc(description = "营业执照必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isBusinessLicense;

    @FieldDoc(description = "机构代码必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isAgencyCode;

    @FieldDoc(description = "机构代码证必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isAgencyCodeLicense;

    @FieldDoc(description = "纳税人证明必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isTaxpayerToProve;

    @FieldDoc(description = "验证类型 0、验证手机；1、验证邮箱；2、均需验证", requiredness = Requiredness.REQUIRED)
    private int companyVerificationType;

    @FieldDoc(description = "个人姓名必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isSName;

    @FieldDoc(description = "个人地址必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isSCity;

    @FieldDoc(description = "个人详细地址必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isSAddress;

    @FieldDoc(description = "个人身份证必填 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isSidCard;

    @FieldDoc(description = "个人身份证上传 0、非必填；1、必填", requiredness = Requiredness.REQUIRED)
    private int isSidCardUrl;

    @FieldDoc(description = "个人验证类型 0、验证手机；1、验证邮箱；2、均需验证", requiredness = Requiredness.REQUIRED)
    private int selfVerificationType;

    @FieldDoc(description = "自定义表单Id（企业）")
    private String customFormJson;

    @FieldDoc(description = "自定义表单Id（个人）")
    private String personalCustomFormJson;


}
