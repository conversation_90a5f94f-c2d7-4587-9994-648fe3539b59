package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "预览订单请求入参")
public class PreviewOrderSelectSkuDto extends BaseParamReq {

    @FieldDoc(description = "购物车主键ID", requiredness = Requiredness.REQUIRED)
    private Long id;
    @FieldDoc(description = "商品ID", requiredness = Requiredness.REQUIRED)
    private Long productId;
    @FieldDoc(description = "skuId", requiredness = Requiredness.REQUIRED)
    private String skuId;
    @FieldDoc(description = "购买数量", requiredness = Requiredness.REQUIRED)
    private Long quantity;
    @FieldDoc(description = "实际售价", requiredness = Requiredness.REQUIRED)
    private BigDecimal realSalePrice;


    public String getRealSalePriceString() {
        return this.bigDecimal2String(this.realSalePrice);
    }


    public void setRealSalePriceString(String realSalePrice) {
        this.realSalePrice = this.string2BigDecimal(realSalePrice);
    }

}
