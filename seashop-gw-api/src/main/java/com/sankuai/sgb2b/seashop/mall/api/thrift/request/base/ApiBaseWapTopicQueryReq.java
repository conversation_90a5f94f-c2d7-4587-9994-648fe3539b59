package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@TypeDoc(description = "移动端专题查询对象")
@Data
public class ApiBaseWapTopicQueryReq extends BaseThriftDto {

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "客户端")
    private String client;

    /**
     * 详情 见TemplateClientTypeEnum类
     */
    @FieldDoc(description = "专题类型")
    private Integer type;


    /**
     * 当前店铺id
     */
    @FieldDoc(description = "当前店铺id")
    private Long currentShopId;


}
