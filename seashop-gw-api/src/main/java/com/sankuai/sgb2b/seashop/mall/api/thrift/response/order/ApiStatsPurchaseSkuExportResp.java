package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家采购统计导出返回结果")
@NoArgsConstructor
@AllArgsConstructor
public class ApiStatsPurchaseSkuExportResp extends BaseThriftDto {

    @FieldDoc(description = "导出文件下载地址")
    private String downloadUrl;


}
