package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 10:07
 */
@TypeDoc(description = "商城小程序微信通知接口入参")
@Data
public class ApiQueryAppletTemplateReq extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "消息类别")
    private Integer messageType;

    @FieldDoc(description = "消息模板编号")
    private String templateNum;

    @FieldDoc(description = "消息模板ID")
    private String templateId;


}
