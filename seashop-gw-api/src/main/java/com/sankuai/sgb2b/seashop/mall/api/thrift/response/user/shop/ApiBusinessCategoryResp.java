package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商信息返回参数")
public class ApiBusinessCategoryResp extends BaseThriftDto {
    @FieldDoc(description = "id")
    private Long id;
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "类目ID")
    private Long categoryId;
    @FieldDoc(description = "类目全名(包含上级)")
    private String categoryName;
    @FieldDoc(description = "蜂拥比例")
    private BigDecimal commisRate;
    @FieldDoc(description = "保证金")
    private BigDecimal bond;
    @FieldDoc(description = "是否冻结")
    private Boolean whetherFrozen;


    public String getCommisRateString() {
        return this.bigDecimal2String(this.commisRate);
    }


    public void setCommisRateString(String commisRate) {
        this.commisRate = this.string2BigDecimal(commisRate);
    }


    public String getBondString() {
        return this.bigDecimal2String(this.bond);
    }


    public void setBondString(String bond) {
        this.bond = this.string2BigDecimal(bond);
    }


}
