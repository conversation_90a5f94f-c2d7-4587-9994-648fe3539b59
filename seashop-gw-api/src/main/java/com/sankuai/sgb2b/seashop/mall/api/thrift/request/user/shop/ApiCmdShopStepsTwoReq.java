package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商入驻请求入参")
public class ApiCmdShopStepsTwoReq extends BaseParamReq {
    @FieldDoc(description = "id")
    private Long shopId;
    @FieldDoc(description = "银行账号")
    private String bankAccountNumber;
    @FieldDoc(description = "银行开户名")
    private String bankAccountName;
    @FieldDoc(description = "银行编码")
    private String bankCode;
    @FieldDoc(description = "开户银行所在地")
    private String bankName;
    @FieldDoc(description = "银行类型")
    private Integer bankType;
    @FieldDoc(description = "银行省份")
    private Integer bankRegionProv;
    @FieldDoc(description = "银行城市")
    private Integer bankRegionCity;
    @FieldDoc(description = "银行地区")
    private Integer bankRegionId;


}