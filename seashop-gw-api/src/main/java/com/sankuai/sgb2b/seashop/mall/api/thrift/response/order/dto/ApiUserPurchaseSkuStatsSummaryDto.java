package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "用户sku采购统计汇总")
public class ApiUserPurchaseSkuStatsSummaryDto extends BaseThriftDto {

    /**
     * 采购总金额
     */
    @FieldDoc(description = "采购总金额")
    private BigDecimal totalAmount;
    /**
     * 订单总数量
     */
    @FieldDoc(description = "订单总数量")
    private Long orderCount;
    /**
     * sku总数量
     */
    @FieldDoc(description = "sku总数量")
    private Long skuQuantitySum;


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


}
