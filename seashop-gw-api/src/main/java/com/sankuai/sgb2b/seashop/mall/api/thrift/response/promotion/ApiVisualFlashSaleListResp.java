package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/16/016
 * @description:
 */
@TypeDoc(description = "限时购集合活动响应体")
@Data
public class ApiVisualFlashSaleListResp extends BaseThriftDto {

    @FieldDoc(description = "限时购活动集合")
    private List<com.sankuai.shangou.seashop.promotion.thrift.core.response.VisualFlashSaleResp> flashSaleList;


}
