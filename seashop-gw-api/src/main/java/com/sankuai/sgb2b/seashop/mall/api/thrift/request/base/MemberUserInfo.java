package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.dto.EpAccountDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@Data
@TypeDoc(description = "商家账号信息")
@NoArgsConstructor
@AllArgsConstructor
public class MemberUserInfo extends EpAccountDto {

    @FieldDoc(description = "系统用户信息")
    private LoginMemberDto loginDto;

    public Long getUserId() {
        if (loginDto != null) {
            return loginDto.getId();
        }
        else {
            return null;
        }
    }
}
