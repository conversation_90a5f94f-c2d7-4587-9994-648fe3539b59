package com.sankuai.sgb2b.seashop.mall.api.thrift.request.topic;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "移动端专题页编辑对象")
public class ApiWapTopicReq extends BasePageReq {

    @FieldDoc(description = "专题id")
    private Long id;
    /**
     * 客户端类型
     */
    @FieldDoc(description = "客户端类型")
    private String client;

    /**
     * 专题类型
     */
    @FieldDoc(description = "专题类型 " +
        "WapIndex(1, \"移动端首页\"),\n" +
        "    SellerWapIndex(2, \"供应商移动端首页 \"),\n" +
        "    WXSmallProgramSellerWapIndex(17, \"供应商小程序端端首页\"),\n" +
        "    WapSpecial(11, \"移动端专题\"),\n" +
        "    SellerWapSpecial(12, \"供应商移动端专题\"),\n" +
        "    WXSmallProgram(13, \"微信小程序首页\"),\n" +
        "    AppIndex(14, \"APP首页\"),\n" +
        "    AppSpecial(15, \"APP专题\"),\n" +
        "    WXSmallProgramSpecial(16, \"小程序专题\"),\n" +
        "    SellerWxSmallProgramSpecial(18, \"供应商小程序专题\");")
    private Integer type;

    /**
     * 专题的json数据
     */
    @FieldDoc(description = "专题的json数据")
    private String content;

    /**
     * 获取商品分组的url
     */
    @FieldDoc(description = "获取商品分组的url")
    private String getGoodGroupUrl;

    /**
     * 获取商品的url
     */
    @FieldDoc(description = "获取商品的url")
    private String getGoodUrl;

    /**
     * 是否预览
     */
    @FieldDoc(description = "是否预览")
    private Boolean is_preview;


    public Boolean getIs_preview() {
        return is_preview;
    }


    public void setIs_preview(Boolean is_preview) {
        this.is_preview = is_preview;
    }
}
