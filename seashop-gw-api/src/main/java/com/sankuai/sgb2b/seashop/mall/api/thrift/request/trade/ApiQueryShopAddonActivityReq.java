package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询凑单活动参数")
public class ApiQueryShopAddonActivityReq extends BaseParamReq {

    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(shopId, "店铺id不能为空");
    }


}
