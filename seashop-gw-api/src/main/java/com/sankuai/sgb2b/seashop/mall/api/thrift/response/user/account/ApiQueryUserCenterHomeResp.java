package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.user.thrift.account.response.UserBaseResp;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/12/12 16:03
 */
@Data
@TypeDoc(description = "商家中心首页返参")
public class ApiQueryUserCenterHomeResp extends BaseThriftDto {

    @FieldDoc(description = "用户的基础信息")
    private UserBaseResp userBaseResp;

    @FieldDoc(description = "优惠券张数")
    private Integer userCoupon;

    @FieldDoc(description = "订单各状态的数量")
    private ApiEachOrderStatusCountResp eachOrderStatusCountResp;

    @FieldDoc(description = "商品推荐")
    private List<ApiProductBaseResp> recommendList;

    @FieldDoc(description = "浏览记录")
    private List<ApiProductBaseResp> browsingList;

    @FieldDoc(description = "收藏店铺")
    private List<ApiFavoriteShopResp> favoriteShopList;

    @FieldDoc(description = "商品关注")
    private List<ApiProductBaseResp> favoriteProductList;

    @FieldDoc(description = "收藏店铺数量")
    private Integer favoriteShopNum;

    @FieldDoc(description = "商品关注数量")
    private Integer favoriteProductNum;

    @FieldDoc(description = "客服电话")
    private String sitePhone;


}
