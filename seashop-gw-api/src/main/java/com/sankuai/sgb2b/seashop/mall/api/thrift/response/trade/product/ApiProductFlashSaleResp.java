package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "限时购返回值")
public class ApiProductFlashSaleResp extends BaseThriftDto {

    @FieldDoc(description = "活动名称")
    private String title;

    @FieldDoc(description = "活动开始日期")
    private Date beginDate;

    @FieldDoc(description = "活动结束日期")
    private Date endDate;

    @FieldDoc(description = "开始倒计时 为0表示活动已经开始")
    private Long startCountDown;

    @FieldDoc(description = "结束倒计时 为0表示活动已经结束")
    private Long endCountDown;

    @FieldDoc(description = "活动id")
    private Long flashSaleActivityId;

    @FieldDoc(description = "限购类型:1商品;2规格")
    private Integer limitType;

    @FieldDoc(description = "限购数量")
    private Integer limitCount;


    public Long getBeginDateLong() {
        return this.date2Long(this.beginDate);
    }


    public void setBeginDateLong(Long beginDate) {
        this.beginDate = this.long2Date(beginDate);
    }


    public Long getEndDateLong() {
        return this.date2Long(this.endDate);
    }


    public void setEndDateLong(Long endDate) {
        this.endDate = this.long2Date(endDate);
    }


}
