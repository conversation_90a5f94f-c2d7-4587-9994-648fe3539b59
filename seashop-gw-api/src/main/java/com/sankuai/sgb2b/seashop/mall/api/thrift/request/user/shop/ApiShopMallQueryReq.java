package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Data
@TypeDoc(description = "店铺ES查询")
public class ApiShopMallQueryReq extends BasePageReq {

    /**
     * 店铺名称
     */
    @FieldDoc(description = "店铺名称")
    private String shopName;

    /**
     * 类目ID
     */
    @FieldDoc(description = "类目ID")
    private Long categoryId;

    /**
     * 品牌id
     */
    @FieldDoc(description = "品牌id")
    private Long brandId;
    //商家ID
    @FieldDoc(description = "商家ID")
    private Long userId;

    /**
     * 是否检索最后一级类目
     */
    @FieldDoc(description = "是否检索最后一级类目")
    private Boolean searchLastCategory;


}
