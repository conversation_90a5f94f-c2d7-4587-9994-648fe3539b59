package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/17 18:25
 */
@TypeDoc(description = "限时购活动响应体")
@Data
public class ApiMallAppletFlashSaleResp extends BaseThriftDto {

    /**
     * 商品信息
     */
    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "商品图片")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "最小销售价")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "状态码")
    private Integer status;

    @FieldDoc(description = "状态")
    private String statusDesc;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "市场价")
    private BigDecimal marketPrice;

    /**
     * 限时购活动信息
     */
    @FieldDoc(description = "活动开始日期")
    private Date beginDate;

    @FieldDoc(description = "活动结束日期")
    private Date endDate;

    @FieldDoc(description = "限购类型:1商品;2规格")
    private Integer limitType;

    @FieldDoc(description = "限购数量")
    private Integer limitCount;

    @FieldDoc(description = "仅仅只计算在限时购里的销售数")
    private Integer saleCount;

    @FieldDoc(description = "活动id")
    private Long categoryId;

    @FieldDoc(description = "跳转路径")
    private String urlPath;

    @FieldDoc(description = "最小价格（多规格价格可能不一样）")
    private BigDecimal minPrice;

    @FieldDoc(description = "限时购id")
    private Long flashSaleId;

    @FieldDoc(description = "距离开始的时间（单位秒）")
    private Long remStartTime = 0L;

    @FieldDoc(description = "距离结束的时间（单位秒）")
    private Long remEndTime = 0L;


    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


    public String getMarketPriceString() {
        return this.bigDecimal2String(this.marketPrice);
    }


    public void setMarketPriceString(String marketPrice) {
        this.marketPrice = this.string2BigDecimal(marketPrice);
    }


    public Long getBeginDateLong() {
        return this.date2Long(this.beginDate);
    }


    public void setBeginDateLong(Long beginDate) {
        this.beginDate = this.long2Date(beginDate);
    }


    public Long getEndDateLong() {
        return this.date2Long(this.endDate);
    }


    public void setEndDateLong(Long endDate) {
        this.endDate = this.long2Date(endDate);
    }


    public String getMinPriceString() {
        return this.bigDecimal2String(this.minPrice);
    }


    public void setMinPriceString(String minPrice) {
        this.minPrice = this.string2BigDecimal(minPrice);
    }


}
