package com.sankuai.sgb2b.seashop.mall.api.thrift.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
@Data
@ToString
@TypeDoc(description = "s3文件上传获取签名数据请求")
public class S3PlusUploadSignRequest {
    /**
     * 对象名称,不传就默认生成时间戳名称
     */

    @FieldDoc(description = "对象名称", requiredness = Requiredness.OPTIONAL)
    private String objectName;
}
