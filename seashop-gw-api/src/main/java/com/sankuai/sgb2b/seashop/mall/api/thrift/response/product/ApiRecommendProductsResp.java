package com.sankuai.sgb2b.seashop.mall.api.thrift.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @description：
 * @author： liweisong
 * @create： 2023/12/13 14:08
 */
@Data
@TypeDoc(description = "商品推荐基本信息集合")
public class ApiRecommendProductsResp extends BaseThriftDto {

    @FieldDoc(description = "商品推荐基本信息集合")
    private List<ApiRecommendProductsDto> productsDtoList;


}
