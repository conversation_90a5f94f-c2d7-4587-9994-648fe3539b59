package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/07 17:08
 */
@Data
@ToString
@TypeDoc(description = "供应商id列表返回值")
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopIdsResp {

    @FieldDoc(description = "供应商id的集合")
    private List<Long> shopIds;


}
