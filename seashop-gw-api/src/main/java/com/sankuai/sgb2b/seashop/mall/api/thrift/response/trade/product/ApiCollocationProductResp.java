package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.CollocationSkuResp;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "组合购商品返回值")
public class ApiCollocationProductResp extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "最低售价")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "原价")
    private BigDecimal originalPrice;

    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;

    @FieldDoc(description = "是否是主商品")
    private Boolean mainFlag;

    @FieldDoc(description = "是否有sku")
    private Boolean hasSku;

    @FieldDoc(description = "skuId集合")
    private List<String> skuIds;

    @FieldDoc(description = "最大节省金额")
    private BigDecimal maxSaveAmount;

    @FieldDoc(description = "skuId集合")
    private List<CollocationSkuResp> skuRespList;
    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


    public String getOriginalPriceString() {
        return this.bigDecimal2String(this.originalPrice);
    }


    public void setOriginalPriceString(String originalPrice) {
        this.originalPrice = this.string2BigDecimal(originalPrice);
    }


    public String getMaxSaveAmountString() {
        return this.bigDecimal2String(this.maxSaveAmount);
    }


    public void setMaxSaveAmountString(String maxSaveAmount) {
        this.maxSaveAmount = this.string2BigDecimal(maxSaveAmount);
    }
}
