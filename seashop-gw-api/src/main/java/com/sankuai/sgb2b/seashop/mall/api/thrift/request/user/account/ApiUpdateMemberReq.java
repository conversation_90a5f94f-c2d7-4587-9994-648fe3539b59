package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 商家操作请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "商家插入操作请求入参")
public class ApiUpdateMemberReq extends BaseParamReq {
    @FieldDoc(description = "id")
    private Long id;
    @FieldDoc(description = "用户名")
    private String userName;
    @FieldDoc(description = "昵称")
    private String nick;
    @FieldDoc(description = "真实姓名")
    private String realName;
    @FieldDoc(description = "性别")
    private Integer sex;
    @FieldDoc(description = "头像")
    private String photo;
    @FieldDoc(description = "qq")
    private String qq;
    @FieldDoc(description = "occupation")
    private String occupation;
    @FieldDoc(description = "生日")
    private Date birthDay;


    public Long getBirthDayLong() {
        return this.date2Long(this.birthDay);
    }


    public void setBirthDayLong(Long birthDay) {
        this.birthDay = this.long2Date(birthDay);
    }

    @Override
    public void checkParameter() {
        super.checkParameter();
        if (StrUtil.isNotBlank(this.occupation) && occupation.length() > 15) {
            throw new IllegalArgumentException("输入职业值超长");
        }
    }
}
