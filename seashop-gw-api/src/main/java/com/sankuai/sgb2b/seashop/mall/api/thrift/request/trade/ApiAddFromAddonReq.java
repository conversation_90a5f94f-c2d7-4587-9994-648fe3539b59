package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "凑单页面加购入参")
public class ApiAddFromAddonReq extends BaseParamReq {

    @FieldDoc(description = "商品ID", requiredness = Requiredness.REQUIRED)
    private Long productId;
    @FieldDoc(description = "skuId", requiredness = Requiredness.REQUIRED)
    private String skuId;
    @FieldDoc(description = "加购数量。默认1", requiredness = Requiredness.REQUIRED)
    private Long quantity;
    /**
     * 凑单类型。1：折扣；2：满减
     */
    @FieldDoc(description = "凑单类型。1：折扣；2：满减", requiredness = Requiredness.REQUIRED)
    private Integer type;
    /**
     * 活动ID
     */
    @FieldDoc(description = "活动ID", requiredness = Requiredness.REQUIRED)
    private Long activityId;
    @FieldDoc(description = "店铺信息", requiredness = Requiredness.REQUIRED)
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表，前端传入是保持用户页面跳转看到的数据一致。需要传入商品的productId, realSalePrice, quantity", requiredness = Requiredness.REQUIRED)
    private List<ApiShoppingCartProductDto> productList;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (productId == null || productId <= 0) {
            throw new InvalidParamException("productId is null");
        }
        if (skuId == null || skuId.isEmpty()) {
            throw new InvalidParamException("skuId is null");
        }
        if (quantity == null || quantity <= 0) {
            throw new InvalidParamException("quantity is null");
        }
        if (type == null || type <= 0) {
            throw new InvalidParamException("type is null");
        }
        if (activityId == null || activityId <= 0) {
            throw new InvalidParamException("activityId is null");
        }
    }


}
