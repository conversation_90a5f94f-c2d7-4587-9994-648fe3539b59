package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "统计用户购买的sku请求入参")
public class ApiStatsUserPurchaseSkuReq extends BasePageReq {

    @FieldDoc(description = "订单ID")
    private String orderId;
    @FieldDoc(description = "订单开始时间")
    private Date orderStartTime;
    @FieldDoc(description = "订单结束时间")
    private Date orderEndTime;
    @FieldDoc(description = "skuId")
    private String skuId;
    @FieldDoc(description = "商品ID")
    private Long productId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "sku自增ID")
    private Long skuAutoId;


    public Long getOrderStartTimeLong() {
        return this.date2Long(this.orderStartTime);
    }


    public void setOrderStartTimeLong(Long orderStartTime) {
        this.orderStartTime = this.long2Date(orderStartTime);
    }


    public Long getOrderEndTimeLong() {
        return this.date2Long(this.orderEndTime);
    }


    public void setOrderEndTimeLong(Long orderEndTime) {
        this.orderEndTime = this.long2Date(orderEndTime);
    }


}
