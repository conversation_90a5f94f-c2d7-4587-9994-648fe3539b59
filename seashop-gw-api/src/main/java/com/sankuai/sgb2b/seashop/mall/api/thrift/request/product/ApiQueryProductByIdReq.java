package com.sankuai.sgb2b.seashop.mall.api.thrift.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 15:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "根据商品id查询商品入参")
public class ApiQueryProductByIdReq extends BaseParamReq {

    @FieldDoc(description = "商品id列表", requiredness = Requiredness.REQUIRED)
    private List<Long> productIds;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIds), "商品id不能为空");
        AssertUtil.throwInvalidParamIfTrue(productIds.size() > 200, "商品id不能超过200个");
    }


}
