package com.sankuai.sgb2b.seashop.mall.api.thrift.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @description：
 * @author： liweisong
 * @create： 2023/12/13 17:17
 */
@Data
@TypeDoc(description = "商品推荐店铺ID")
public class ApiRecommendProductsReq extends BaseThriftDto {

    @FieldDoc(description = "用户ID")
    private Long userId;

    @FieldDoc(description = "展示数量")
    private Integer limitNum;


}
