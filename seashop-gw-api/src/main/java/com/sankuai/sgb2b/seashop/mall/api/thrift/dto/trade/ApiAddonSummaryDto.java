package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "凑单汇总描述")
@ToString
@Data
public class ApiAddonSummaryDto extends BaseThriftDto {

    /**
     * 凑单tab对应已勾选商品sku数，从购物车进入，勾选的商品如果是活动中的，就会计入
     */
    @FieldDoc(description = "凑单tab对应已勾选商品sku数，从购物车进入，勾选的商品如果是活动中的，就会计入")
    private Integer selectedInActiveCount;
    /**
     * 凑单tab对应已勾选商品的实际金额，如果不满足活动，则是原总额；满足活动就是扣减玩活动后的
     */
    @FieldDoc(description = "凑单tab对应已勾选商品的实际金额，如果不满足活动，则是原总额；满足活动就是扣减玩活动后的")
    private BigDecimal selectedProductAmount;
    /**
     * 截止时间描述
     */
    @FieldDoc(description = "截止时间描述")
    private String deadlineDesc;
    /**
     * 活动描述
     */
    @FieldDoc(description = "活动描述")
    private String activityDesc;
    /**
     * 满足的营销描述
     */
    @FieldDoc(description = "满足的营销描述")
    private String matchPromotionDesc;
    @FieldDoc(description = "截止时间时间戳")
    private Long deadline;


    public String getSelectedProductAmountString() {
        return this.bigDecimal2String(this.selectedProductAmount);
    }


    public void setSelectedProductAmountString(String selectedProductAmount) {
        this.selectedProductAmount = this.string2BigDecimal(selectedProductAmount);
    }


}
