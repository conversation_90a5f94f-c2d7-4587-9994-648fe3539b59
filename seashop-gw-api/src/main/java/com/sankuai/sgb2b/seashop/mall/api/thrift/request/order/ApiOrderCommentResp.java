package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/04 12:05
 */
@Data
@ToString
@TypeDoc(description = "订单评价返回值对象")
public class ApiOrderCommentResp extends BaseThriftDto {

    @FieldDoc(description = "订单评价id")
    private Long id;

    @FieldDoc(description = "订单id")
    private String orderId;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "用户id")
    private Long userId;

    @FieldDoc(description = "用户名称")
    private String userName;

    @FieldDoc(description = "评价日期")
    private Date commentDate;

    @FieldDoc(description = "包装评分")
    private Integer packMark;

    @FieldDoc(description = "物流评分")
    private Integer deliveryMark;

    @FieldDoc(description = "服务评分")
    private Integer serviceMark;


    public Long getCommentDateLong() {
        return this.date2Long(this.commentDate);
    }


    public void setCommentDateLong(Long commentDate) {
        this.commentDate = this.long2Date(commentDate);
    }


}
