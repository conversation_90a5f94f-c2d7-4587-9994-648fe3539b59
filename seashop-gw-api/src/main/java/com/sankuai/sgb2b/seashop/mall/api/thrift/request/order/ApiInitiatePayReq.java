package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "外观-发起支付请求入参")
public class ApiInitiatePayReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private List<String> orderIdList;
    @FieldDoc(description = "选择的支付方式。1：支付宝扫码；3：微信小程序；4：h5微信支付；5：企业网银；6：个人网银； 11：微商城微信支付", requiredness = Requiredness.REQUIRED)
    private Integer payMethod;
    @FieldDoc(description = "openId.小程序支付必传")
    private String openId;
    @FieldDoc(description = "银行编号")
    private String bankCode;
    @FieldDoc(description = "支付成功后的回调地址")
    private String callbackUrl;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(orderIdList)) {
            throw new InvalidParamException("orderIdList不能为空");
        }
        if (payMethod == null) {
            throw new InvalidParamException("payMethod不能为空");
        }
    }


}
