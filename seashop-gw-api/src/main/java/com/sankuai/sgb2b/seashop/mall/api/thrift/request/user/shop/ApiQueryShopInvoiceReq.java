package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:55
 */
@Data
@TypeDoc(description = "店铺发票查询入参")
public class ApiQueryShopInvoiceReq extends BaseThriftDto {

    @FieldDoc(description = "店铺ID")
    private Long shopId;


    public void checkParameter() {
        if (shopId == null) {
            throw new IllegalArgumentException("店铺shopId不能为空");
        }
    }
}
