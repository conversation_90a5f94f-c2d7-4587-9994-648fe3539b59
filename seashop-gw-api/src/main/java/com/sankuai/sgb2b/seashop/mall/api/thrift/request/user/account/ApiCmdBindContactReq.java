package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "检查验证码请求类")
public class ApiCmdBindContactReq extends ApiCmdCheckCodeReq {
    @FieldDoc(description = "旧的联系方式")
    private String oldContact;

    @FieldDoc(description = "新的的联系方式 0email 1phone")
    private Integer usertype;

    @FieldDoc(description = "业务code用于鉴权")
    private String token;


}
