package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "类目申请信息返回参数")
public class ApiBusinessCategoryApplyResp extends BaseThriftDto {

    @FieldDoc(description = "id")
    private Long id;

    @FieldDoc(description = "申请时间")
    private Date applyDate;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "审核状态")
    private Integer auditedStatus;

    @FieldDoc(description = "审核时间")
    private Date auditedDate;

    @FieldDoc(description = "拒绝愿意")
    private String refuseReason;

    @FieldDoc(description = "签署状态")
    private Integer agreementStatus;

    @FieldDoc(description = "合同ID")
    private Long shopAgreementId;


    public Long getApplyDateLong() {
        return this.date2Long(this.applyDate);
    }


    public void setApplyDateLong(Long applyDate) {
        this.applyDate = this.long2Date(applyDate);
    }


    public Long getAuditedDateLong() {
        return this.date2Long(this.auditedDate);
    }


    public void setAuditedDateLong(Long auditedDate) {
        this.auditedDate = this.long2Date(auditedDate);
    }


}
