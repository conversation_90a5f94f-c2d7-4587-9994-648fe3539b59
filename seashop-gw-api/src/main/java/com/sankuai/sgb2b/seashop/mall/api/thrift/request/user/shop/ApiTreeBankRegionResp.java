package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@Data
@TypeDoc(description = "地区返回")
@NoArgsConstructor
@AllArgsConstructor
public class ApiTreeBankRegionResp {
    @FieldDoc(description = "美团区域编号")
    private Integer id;

    @FieldDoc(description = "美团区域编号")
    private String code;

    @FieldDoc(description = "区域名称")
    private String name;

    @FieldDoc(description = "地址级别")
    private int regionLevel;

    @FieldDoc(description = "父id")
    private Integer parentId;


}
