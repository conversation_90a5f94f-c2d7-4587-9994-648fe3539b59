package com.sankuai.sgb2b.seashop.mall.api.thrift.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:02
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "平台类目集合")
public class ApiCategoryListResp {

    @FieldDoc(description = "类目集合")
    private List<ApiCategoryResp> categoryList;


}
