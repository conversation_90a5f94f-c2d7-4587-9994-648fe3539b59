package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 供应商入驻协议请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商入驻协议请求入参")
public class ApiCmdAgreementReq {
    @FieldDoc(description = "是否同意协议")
    private Boolean agree;
    @FieldDoc(description = "入驻类型 0个人供应商 1企业供应商")
    private Integer businessType;
    @FieldDoc(description = "用户id")
    private Long userId;


}
