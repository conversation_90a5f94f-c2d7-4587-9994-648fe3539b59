package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiPreviewOrderSummaryDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiUserDto;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "选择收货地址入参")
public class ApiChooseShippingAddressReq extends BaseParamReq {

    @FieldDoc(description = "用户收货地址，可能为空", requiredness = Requiredness.REQUIRED)
    private Long shippingAddressId;
    @FieldDoc(description = "按店铺分组的商品列表", requiredness = Requiredness.REQUIRED)
    private List<ApiShoppingCartShopProductDto> shopProductList;
    @FieldDoc(description = "预览订单页所有商品总金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal totalAmount;
    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private ApiUserDto user;
    @FieldDoc(description = "预览订单相关汇总信息", requiredness = Requiredness.REQUIRED)
    private ApiPreviewOrderSummaryDto previewOrderSummaryDto;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.shippingAddressId == null || this.shippingAddressId <= 0) {
            throw new InvalidParamException("shippingAddressId不能为空");
        }
        if (this.shopProductList == null || this.shopProductList.isEmpty()) {
            throw new InvalidParamException("shopProductList不能为空");
        }
        if (this.totalAmount == null || this.totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParamException("totalAmount不能为空");
        }
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


}
