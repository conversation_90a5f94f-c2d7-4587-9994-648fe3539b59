package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.dto.ApiUserPurchaseSkuDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.dto.ApiUserPurchaseSkuStatsSummaryDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家订单sku采购统计返回对象")
public class ApiStatsUserPurchaseSkuResp extends BaseThriftDto {

    @FieldDoc(description = "分页数据")
    private BasePageResp<ApiUserPurchaseSkuDto> pageData;
    @FieldDoc(description = "汇总数据")
    private ApiUserPurchaseSkuStatsSummaryDto summary;


}
