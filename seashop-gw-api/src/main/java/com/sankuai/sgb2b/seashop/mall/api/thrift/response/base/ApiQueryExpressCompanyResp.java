package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.dto.ApiQueryExpressCompanyDto;
import lombok.Data;

import java.util.List;


@TypeDoc(description = "快递设置")
@Data
public class ApiQueryExpressCompanyResp {

    @FieldDoc(description = "快递公司集合")
    private List<ApiQueryExpressCompanyDto> queryExpressCompanyDtoList;


}
