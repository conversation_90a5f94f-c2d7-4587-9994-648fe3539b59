package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 凑单页面对应的活动对象
 *
 * <AUTHOR>
 */
@TypeDoc(description = "凑单页面对应的活动对象")
@ToString
@Data
public class ApiAddonActivityDto extends BaseThriftDto {

    /**
     * 活动类型。1：折扣；2：满减
     */
    @FieldDoc(description = "活动类型。1：折扣；2：满减")
    private Integer type;
    /**
     * 活动类型描述
     */
    @FieldDoc(description = "活动类型描述")
    private String typeDesc;
    /**
     * 活动ID
     */
    @FieldDoc(description = "活动ID")
    private Long activityId;
    /**
     * 活动名称
     */
    @FieldDoc(description = "活动名称")
    private String activityName;

    /**
     * 活动结束时间
     */
    @FieldDoc(description = "活动结束时间")
    private Date endTime;


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }
}
