package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "商家添加商品到购物车请求入参")
@Getter
@Setter
public class ApiQueryBusinessCategoryPageReq extends BasePageReq {

    @FieldDoc(description = "店铺Id", requiredness = Requiredness.OPTIONAL)
    private Long shopId;
}
