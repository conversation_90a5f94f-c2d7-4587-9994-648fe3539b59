package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: 发票抬头列表
 * @author: LXH
 **/
@TypeDoc(description = "发票抬头列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiInvoiceTitleRespList {
    @FieldDoc(description = "发票抬头列表")
    private List<ApiInvoiceTitleResp> respList;


}
