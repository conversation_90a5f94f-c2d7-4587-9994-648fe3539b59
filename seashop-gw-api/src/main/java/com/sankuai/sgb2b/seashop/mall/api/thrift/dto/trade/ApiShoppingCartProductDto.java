package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "购物车商品返回对象")
@ToString
@Data
public class ApiShoppingCartProductDto extends BaseThriftDto {

    @FieldDoc(description = "购物车主键ID")
    private String id;
    @FieldDoc(description = "商家用户ID")
    private Long userId;
    @FieldDoc(description = "商品ID")
    private String productId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "商品SKUID")
    private String skuId;
    @FieldDoc(description = "数量")
    private Long quantity;
    @FieldDoc(description = "添加时间")
    private Date addTime;
    @FieldDoc(description = "是否选中")
    private Boolean whetherSelected;

    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String mainImagePath;
    @FieldDoc(description = "sku库存")
    private Long skuStock;
    @FieldDoc(description = "原售价")
    private BigDecimal originSalePrice;
    @FieldDoc(description = "优惠售价，基于专享价和阶梯价得到")
    private BigDecimal discountSalePrice;
    @FieldDoc(description = "实际售价(最终价格)，在优惠售价的基础上，还有折扣后的价格")
    private BigDecimal realSalePrice;
    @FieldDoc(description = "最终售价。如果没有折扣，则=realSalePrice，如果有折扣，则=discountSalePrice")
    private BigDecimal finalSalePrice;
    @FieldDoc(description = "商品总金额")
    private BigDecimal totalAmount;
    @FieldDoc(description = "规格描述")
    private List<String> skuNameList;
    @FieldDoc(description = "起购量")
    private Integer minBuyCount;
    @FieldDoc(description = "限购数")
    private Integer maxBuyCount;
    @FieldDoc(description = "倍数起购量")
    private Integer multipleCount;
    @FieldDoc(description = "是否专享价")
    private Boolean whetherExclusive;
    @FieldDoc(description = "购物车sku状态")
    private String skuStatus;
    @FieldDoc(description = "校验失败的描述")
    private String errorMsg;
    @FieldDoc(description = "商品货号")
    private String productCode;
    /**
     * 是否允许7天无理由退货
     */
    @FieldDoc(description = "是否允许7天无理由退货")
    private Boolean enableNoReasonReturn;
    /**
     * 是否显示保障(保证金)标志
     */
    @FieldDoc(description = "是否显示保障(保证金)标志")
    private Boolean showGuaranteeFlag;
    /**
     * 是否显示闪电标识
     */
    @FieldDoc(description = "是否显示闪电标识")
    private Boolean showThunderFlag;

    @FieldDoc(description = "商品sku自增id")
    private Long skuAutoId;
    @FieldDoc(description = "计量单位")
    private String measureUnit;


    public Long getAddTimeLong() {
        return this.date2Long(this.addTime);
    }


    public void setAddTimeLong(Long addTime) {
        this.addTime = this.long2Date(addTime);
    }


    public String getOriginSalePriceString() {
        return this.bigDecimal2String(this.originSalePrice);
    }


    public void setOriginSalePriceString(String originSalePrice) {
        this.originSalePrice = this.string2BigDecimal(originSalePrice);
    }


    public String getDiscountSalePriceString() {
        return this.bigDecimal2String(this.discountSalePrice);
    }


    public void setDiscountSalePriceString(String discountSalePrice) {
        this.discountSalePrice = this.string2BigDecimal(discountSalePrice);
    }


    public String getRealSalePriceString() {
        return this.bigDecimal2String(this.realSalePrice);
    }


    public void setRealSalePriceString(String realSalePrice) {
        this.realSalePrice = this.string2BigDecimal(realSalePrice);
    }


    public String getFinalSalePriceString() {
        return this.bigDecimal2String(this.finalSalePrice);
    }


    public void setFinalSalePriceString(String finalSalePrice) {
        this.finalSalePrice = this.string2BigDecimal(finalSalePrice);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


}
