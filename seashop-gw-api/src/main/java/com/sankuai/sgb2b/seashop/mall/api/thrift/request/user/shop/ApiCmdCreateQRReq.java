package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "发送验证码请求参数类")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiCmdCreateQRReq {
    @FieldDoc(description = "地址")
    private String path;


}
