package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @description：删除商品收藏
 * @author： liweisong
 * @create： 2023/11/28 9:30
 */
@Data
@ToString
@TypeDoc(description = "删除商品收藏入参")
public class ApiDeleteFavoriteProductReq extends BaseThriftDto {

    @FieldDoc(description = "主键ID集合")
    @ExaminField
    private List<Long> ids;

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "用户id")
    private Long userId;


}
