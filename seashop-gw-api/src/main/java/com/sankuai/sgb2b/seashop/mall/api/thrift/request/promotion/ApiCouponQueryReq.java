package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "优惠券查询请求对象")
public class ApiCouponQueryReq extends BasePageReq {

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "优惠券名称")
    private String couponName;

    @FieldDoc(description = "店铺ID列表")
    private List<Long> shopIdList;

    @FieldDoc(description = "状态 0-未开始 1-进行中 2-已结束")
    private Integer status;


}
