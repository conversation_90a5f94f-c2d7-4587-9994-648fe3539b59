package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "外观-发起支付返回对象")
public class ApiInitiatePayResp extends BaseThriftDto {

    @FieldDoc(description = "汇付支付创建支付返回的发起参数的JSON字符串。不同的支付方式参数可能不一样，前端需要转json对象")
    private String payParamMap;


}
