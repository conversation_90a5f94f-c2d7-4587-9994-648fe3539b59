package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@TypeDoc(description = "弹窗广告响应体")
@ToString
@Data
public class ApiAdvanceResp extends BaseThriftDto {

    /**
     * 是否开启弹窗广告
     */
    @FieldDoc(description = "是否开启弹窗广告", requiredness = Requiredness.REQUIRED)
    private Boolean isEnable;

    /**
     * 广告位图片
     */
    @FieldDoc(description = "广告位图片", requiredness = Requiredness.REQUIRED)
    private String img;

    /**
     * 图片外联链接
     */
    @FieldDoc(description = "图片外联链接", requiredness = Requiredness.REQUIRED)
    private String link;

    /**
     * 开始时间
     */
    @FieldDoc(description = "开始时间", requiredness = Requiredness.REQUIRED)
    private Date startTime;

    /**
     * 结束时间
     */
    @FieldDoc(description = "结束时间", requiredness = Requiredness.REQUIRED)
    private Date endTime;

    /**
     * 是否重复播放
     */
    @FieldDoc(description = "是否重复播放", requiredness = Requiredness.REQUIRED)
    private Boolean isReplay;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
