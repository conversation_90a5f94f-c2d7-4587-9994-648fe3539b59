package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "商家购物车SKU数量返回对象")
@ToString
@Data
public class ApiShopProductResp extends BaseThriftDto {

    @FieldDoc(description = "店铺信息")
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表")
    private List<ApiShoppingCartProductDto> productList;
    @FieldDoc(description = "异常时可能适合的数量。错误码等于50030002有值")
    private Long errSuitQuantity;
    @FieldDoc(description = "异常提示")
    private String errDesc;


}
