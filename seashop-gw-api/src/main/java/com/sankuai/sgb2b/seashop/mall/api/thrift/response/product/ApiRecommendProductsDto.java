package com.sankuai.sgb2b.seashop.mall.api.thrift.response.product;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description：
 * @author： liweisong
 * @create： 2023/12/13 14:23
 */
@Data
@TypeDoc(description = "商品推荐基本信息")
public class ApiRecommendProductsDto extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "商品ID")
    private String productId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;

    @FieldDoc(description = "市场价")
    private BigDecimal marketPrice;

    @FieldDoc(description = "最小销售价(展示这个价格)")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "店铺ID")
    private Long shopId;


    public String getMarketPriceString() {
        return this.bigDecimal2String(this.marketPrice);
    }


    public void setMarketPriceString(String marketPrice) {
        this.marketPrice = this.string2BigDecimal(marketPrice);
    }


    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


}
