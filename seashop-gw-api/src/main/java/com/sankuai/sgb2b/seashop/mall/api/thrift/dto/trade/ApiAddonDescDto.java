package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "凑单对应的营销描述")
@ToString
@Data
public class ApiAddonDescDto {


    /**
     * 凑单类型。1：折扣；2：满减
     */
    @FieldDoc(description = "凑单类型。1：折扣；2：满减")
    private Integer type;
    /**
     * 活动类型描述
     */
    @FieldDoc(description = "活动类型描述")
    private String typeDesc;
    /**
     * 凑单对应的营销描述
     */
    @FieldDoc(description = "凑单对应的营销描述")
    private String promotionDesc;


}
