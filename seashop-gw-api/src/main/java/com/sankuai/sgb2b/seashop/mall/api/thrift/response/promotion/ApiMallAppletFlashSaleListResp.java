package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/17 19:06
 */
@TypeDoc(description = "限时购活动响应体")
@Data
public class ApiMallAppletFlashSaleListResp extends BaseThriftDto {

    @FieldDoc(description = "限时购活动响应体列表")
    private List<ApiMallAppletFlashSaleResp> respList;


}
