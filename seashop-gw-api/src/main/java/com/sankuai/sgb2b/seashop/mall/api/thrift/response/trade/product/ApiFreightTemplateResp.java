package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/01/04 15:54
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "运费模板相关返回值")
public class ApiFreightTemplateResp extends BaseThriftDto {

    @FieldDoc(description = "运费模板id")
    private Long freightTemplateId;

    @FieldDoc(description = "运费模板名称")
    private String name;

    @FieldDoc(description = "宝贝发货地")
    private Integer sourceAddress;

    @FieldDoc(description = "发送时间")
    private String sendTime;

    @FieldDoc(description = "是否商家负责运费 0-否 1-是")
    private Integer whetherFree;

    @FieldDoc(description = "是否在限购区域")
    private Boolean inRestrictedRegion;


}
