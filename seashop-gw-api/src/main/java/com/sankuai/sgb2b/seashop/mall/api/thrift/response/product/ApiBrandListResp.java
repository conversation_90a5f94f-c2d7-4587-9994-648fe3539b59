package com.sankuai.sgb2b.seashop.mall.api.thrift.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/28 10:44
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "品牌列表返回值")
public class ApiBrandListResp extends BaseThriftDto {

    @FieldDoc(description = "品牌列表")
    private List<ApiBrandResp> brandList;


}
