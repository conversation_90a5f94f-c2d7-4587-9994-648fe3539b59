package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiOrderAdditionalDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShippingAddressDto;
import lombok.Data;
import lombok.ToString;


import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "预览订单页-修改商品数量入参")
public class ApiPreviewChangeSkuQuantityReq extends BaseParamReq {

    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "用户收货地址，可能为空", requiredness = Requiredness.OPTIONAL)
    private Long shippingAddressId;
    @FieldDoc(description = "购物车ID，根据要需改的数据行的ID，有就传", requiredness = Requiredness.REQUIRED)
    private Long id;
    @FieldDoc(description = "商品ID，当前修改数量的productId", requiredness = Requiredness.REQUIRED)
    private Long productId;
    @FieldDoc(description = "skuId，当前修改数量的skuId", requiredness = Requiredness.REQUIRED)
    private String skuId;
    @FieldDoc(description = "变更后的数量，前端根据sku的基本规则先做校验后传入实际要修改的值", requiredness = Requiredness.REQUIRED)
    private Long quantity;
    @FieldDoc(description = "店铺信息，当前是整个对象传入，数据是为了保持用户前后看到的数据一致", requiredness = Requiredness.REQUIRED)
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表，当前是整个对象传入，数据是为了保持用户前后看到的数据一致", requiredness = Requiredness.REQUIRED)
    private List<ApiShoppingCartProductDto> productList;
    @FieldDoc(description = "附加信息，店铺订单的附件信息，包括选择的优惠券，发票等")
    private ApiOrderAdditionalDto additional;
    @FieldDoc(description = "限时购活动id。如果不为空，代表是限时购")
    private Long flashSaleId;
    /**
     * 组合购活动id。如果不为空，代表是组合购
     */
    @FieldDoc(description = "组合购活动id。如果不为空，代表是组合购")
    private Long collocationId;
    @FieldDoc(description = "是否立即购买。如果为true，代表是立即购买")
    private Boolean whetherBuyNow;

    // 兼容老的结构
    @FieldDoc(description = "用户收货地址，可能为空")
    private ShippingAddressDto shippingAddress;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.productId == null) {
            throw new InvalidParamException("请选择修改的商品");
        }
        if (StrUtil.isBlank(this.skuId)) {
            throw new InvalidParamException("请选择修改的商品");
        }
        if (this.quantity == null || this.quantity <= 0) {
            throw new InvalidParamException("商品数量不能为空");
        }
        if (shop == null) {
            throw new InvalidParamException("店铺信息不能为空");
        }
        if (CollUtil.isEmpty(productList)) {
            throw new InvalidParamException("店铺商品列表不能为空");
        }
    }


}
