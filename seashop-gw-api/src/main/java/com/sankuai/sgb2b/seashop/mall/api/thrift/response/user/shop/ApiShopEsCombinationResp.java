package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Data
@ToString
@TypeDoc(description = "店铺ES信息返回参数列表")
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopEsCombinationResp {

    @FieldDoc(description = "店铺列表")
    private BasePageResp<ApiShopEsResp> shopList;

    @FieldDoc(description = "店铺品牌列表")
    private List<ApiShopBrandEsResp> shopBrandList;

    @FieldDoc(description = "店铺经营类目列表")
    private List<ApiBusinessCategoryEsResp> businessCategoryList;

    @FieldDoc(description = "店铺品牌id列表")
    private List<Long> shopBrandIds;

    @FieldDoc(description = "店铺经营类目id列表")
    private List<Long> categoryIds;


}
