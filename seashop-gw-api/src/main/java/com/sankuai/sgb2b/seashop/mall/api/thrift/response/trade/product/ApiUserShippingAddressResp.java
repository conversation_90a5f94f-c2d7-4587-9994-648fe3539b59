package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/22 16:45
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "用户收货地址")
public class ApiUserShippingAddressResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "区域id")
    private Integer regionId;

    @FieldDoc(description = "收货人")
    private String shipTo;

    @FieldDoc(description = "收货地址")
    private String address;

    @FieldDoc(description = "详细地址")
    private String addressDetail;

    @FieldDoc(description = "收货人电话")
    private String phone;

    @FieldDoc(description = "区域全路径")
    private String regionPath;

    @FieldDoc(description = "区域全称")
    private String regionFullName;


}
