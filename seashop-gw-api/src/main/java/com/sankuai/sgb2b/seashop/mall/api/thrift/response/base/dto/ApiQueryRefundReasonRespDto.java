package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:26
 */
@TypeDoc(description = "售后原因查询返参")
@Data
public class ApiQueryRefundReasonRespDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "售后原因")
    private String afterSalesText;

    @FieldDoc(description = "排序")
    private Integer sequence;

    @FieldDoc(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;


}
