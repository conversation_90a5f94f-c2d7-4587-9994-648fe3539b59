package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "删除购物车sku请求入参")
public class ApiDeleteShoppingCartSkuReq extends BaseParamReq {

    @FieldDoc(description = "购物车唯一标识，目前是数据表主键ID，取购物车列表的ID字段", requiredness = Requiredness.REQUIRED)
    private Long id;

    /**
     * 基本参数校验
     */
    public void checkParameter() {
        if (this.id == null || this.id <= 0) {
            throw new InvalidParamException("id不能为空");
        }
    }


}
