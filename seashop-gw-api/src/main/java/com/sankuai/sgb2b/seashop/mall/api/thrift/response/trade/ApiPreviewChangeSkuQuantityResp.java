package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiOrderAdditionalDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiUserCouponRecordDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "预览订单-修改数量返回对象")
@ToString
@Data
public class ApiPreviewChangeSkuQuantityResp {

    @FieldDoc(description = "店铺信息", requiredness = Requiredness.REQUIRED)
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表", requiredness = Requiredness.REQUIRED)
    private List<ApiShoppingCartProductDto> productList;
    @FieldDoc(description = "附加信息")
    private ApiOrderAdditionalDto additional;
    @FieldDoc(description = "有效的优惠券信息")
    private List<ApiUserCouponRecordDto> validCouponList;
    @FieldDoc(description = "异常时可能适合的数量。错误码等于50030002有值")
    private Long errSuitQuantity;
    @FieldDoc(description = "异常提示")
    private String errDesc;


}
