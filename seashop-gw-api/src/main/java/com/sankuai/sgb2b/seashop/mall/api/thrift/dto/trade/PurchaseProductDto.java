package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * 购买的商品对象
 *
 * <AUTHOR>
 */
@TypeDoc(description = "购买的商品对象")
@ToString
@Data
public class PurchaseProductDto extends BaseParamReq {

    /**
     * 商品ID
     */
    @FieldDoc(description = "商品ID", requiredness = Requiredness.REQUIRED)
    private Long productId;
    /**
     * skuId
     */
    @FieldDoc(description = "skuId", requiredness = Requiredness.REQUIRED)
    private String skuId;
    /**
     * 购买数量。默认单位是1，如果用户指定了购买套数，前端需要计算
     */
    @FieldDoc(description = "购买数量。默认单位是1，如果用户指定了购买套数，前端需要计算", requiredness = Requiredness.REQUIRED)
    private Long quantity;

    @Override
    public void checkParameter() {
        if (this.productId == null || this.productId <= 0) {
            throw new InvalidParamException("productId不能为空");
        }
        if (StrUtil.isBlank(this.skuId)) {
            throw new InvalidParamException("skuId不能为空");
        }
        if (this.quantity == null || this.quantity <= 0) {
            throw new InvalidParamException("quantity不能为空");
        }
    }


}
