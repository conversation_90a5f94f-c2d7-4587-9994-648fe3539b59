package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 14:15
 */
@Data
@TypeDoc(description = "小程序表单入参")
public class ApiQueryWxAppletFormDataReq extends BaseThriftDto {

    @FieldDoc(description = "id")
    private Long id;

    @FieldDoc(description = "事件ID")
    private Long eventId;

    @FieldDoc(description = "事件值")
    private String eventValue;

    @FieldDoc(description = "事件的表单ID")
    private String formId;


}
