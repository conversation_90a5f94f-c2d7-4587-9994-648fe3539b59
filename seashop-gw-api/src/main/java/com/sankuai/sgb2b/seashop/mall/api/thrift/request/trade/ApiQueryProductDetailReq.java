package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/25 14:14
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询商品详情入参")
public class ApiQueryProductDetailReq extends BaseParamReq {

    @FieldDoc(description = "商品Id", requiredness = Requiredness.OPTIONAL)
    private Long productId;

    @FieldDoc(description = "组合购id(传了该id 表示从组合购进去详情)", requiredness = Requiredness.OPTIONAL)
    private Long collocationId;

    @FieldDoc(description = "限时购id(传了该id 表示从限时购进入)", requiredness = Requiredness.OPTIONAL)
    private Long flashSaleId;

    @Override
    public void checkParameter() {
    }


}
