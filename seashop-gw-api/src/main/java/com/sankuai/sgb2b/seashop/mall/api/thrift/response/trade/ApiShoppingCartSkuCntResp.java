package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "商家购物车SKU数量返回对象")
@ToString
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiShoppingCartSkuCntResp extends BaseThriftDto {

    @FieldDoc(description = "SKU数量")
    private Long skuCnt;


}
