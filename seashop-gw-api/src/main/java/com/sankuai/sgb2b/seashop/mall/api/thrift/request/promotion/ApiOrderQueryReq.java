package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "订单信息查询请求对象")
public class ApiOrderQueryReq extends BaseParamReq {

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @FieldDoc(description = "商品ID列表", requiredness = Requiredness.REQUIRED)
    private List<ApiProductQueryReq> productList;

    @Override
    public void checkParameter() {
        if (this.shopId == null || this.shopId <= 0) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (CollUtil.isEmpty(this.productList)) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
