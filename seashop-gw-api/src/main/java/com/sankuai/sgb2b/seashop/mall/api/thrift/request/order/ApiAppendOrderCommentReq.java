package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/04 9:34
 */
@Data
@ToString
@TypeDoc(description = "追加订单评论入参")
public class ApiAppendOrderCommentReq extends BaseParamReq {

    @FieldDoc(description = "订单评价id", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "追加商品评论", requiredness = Requiredness.REQUIRED)
    private List<ApiAppendProductCommentReq> productCommentList;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "订单评价id不能为空");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productCommentList), "追加商品评论不能为空");
        productCommentList.forEach(ApiAppendProductCommentReq::checkParameter);
    }


}
