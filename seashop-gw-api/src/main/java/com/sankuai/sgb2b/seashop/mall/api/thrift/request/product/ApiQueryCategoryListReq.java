package com.sankuai.sgb2b.seashop.mall.api.thrift.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询类目入参")
public class ApiQueryCategoryListReq extends BaseParamReq {

    @FieldDoc(description = "父类目id 一级传0", requiredness = Requiredness.REQUIRED)
    private Long parentId;


}
