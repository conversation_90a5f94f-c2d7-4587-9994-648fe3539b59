package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 13:41
 */
@Data
@TypeDoc(description = "底部导航栏返参")
public class ApiQueryFootMenusRespDto extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "导航名称")
    private String name;

    @FieldDoc(description = "链接地址")
    private String url;

    @FieldDoc(description = "显示图片")
    private String menuIcon;

    @FieldDoc(description = "未选中显示图片")
    private String menuIconSel;

    @FieldDoc(description = "菜单类型（1代表微信、2代表小程序）")
    private Integer type;

    @FieldDoc(description = "店铺Id(0默认是平台)")
    private Long shopId;


}
