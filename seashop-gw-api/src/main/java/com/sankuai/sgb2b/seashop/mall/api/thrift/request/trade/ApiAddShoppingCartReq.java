package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商家添加商品到购物车请求入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家添加商品到购物车请求入参")
@Getter
@Setter
public class ApiAddShoppingCartReq {


    @FieldDoc(description = "商品ID", requiredness = Requiredness.REQUIRED)
    private Long productId;

    @FieldDoc(description = "skuId", requiredness = Requiredness.REQUIRED)
    private String skuId;

    @FieldDoc(description = "加购数量", requiredness = Requiredness.REQUIRED)
    private Long quantity;

}
