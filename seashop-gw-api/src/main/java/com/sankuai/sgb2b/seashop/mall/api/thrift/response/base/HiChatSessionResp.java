package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@Data
@TypeDoc(description = "会话信息")
@NoArgsConstructor
@AllArgsConstructor
public class HiChatSessionResp extends BaseThriftDto {
    @FieldDoc(description = "店铺名")
    private String name;
    @FieldDoc(description = "hichat的appKey")
    private String appKey;
    @FieldDoc(description = "店铺logo")
    private String logo;
    @FieldDoc(description = "最后一条消息时间")
    private String lastTime;
    @FieldDoc(description = "消息内容")
    private String content;
    @FieldDoc(description = "未读条数")
    private Integer unRead;


}
