package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/13 10:51
 */
@Data
@TypeDoc(description = "统计各订单状态的数量返参")
public class ApiEachOrderStatusCountResp extends BaseThriftDto {

    @FieldDoc(description = "全部订单数量")
    private Integer allOrderCount;

    @FieldDoc(description = "待支付订单数量")
    private Integer waitingForPay;

    @FieldDoc(description = "待发货订单数量")
    private Integer waitingForDelivery;

    @FieldDoc(description = "待收货订单数量")
    private Integer waitingForRecieve;

    @FieldDoc(description = "待处理售后订单数量")
    private Integer refundCount;

    @FieldDoc(description = "待评论订单数量")
    private Integer waitingForComments;


}
