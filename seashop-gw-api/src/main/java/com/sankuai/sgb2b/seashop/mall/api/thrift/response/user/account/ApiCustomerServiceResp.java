package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "客服信息返回")
public class ApiCustomerServiceResp extends BaseParamReq {

    @FieldDoc(description = "客服id")
    private Long id;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "工具")
    private Integer tool;

    @FieldDoc(description = "类型")
    private Integer type;

    @FieldDoc(description = "名称")
    private String name;

    @FieldDoc(description = "通信账号")
    private String accountCode;

    @FieldDoc(description = "终端类型")
    private Integer terminalType;

    @FieldDoc(description = "客服状态")
    private Integer serverStatus;


}
