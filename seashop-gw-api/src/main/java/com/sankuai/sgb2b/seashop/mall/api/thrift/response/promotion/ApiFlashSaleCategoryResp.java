package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@TypeDoc(description = "限时购分类响应体")
@Data
public class ApiFlashSaleCategoryResp {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "分类名称")
    private String categoryName;


}
