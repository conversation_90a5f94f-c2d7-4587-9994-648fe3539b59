package com.sankuai.sgb2b.seashop.mall.api.thrift.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:02
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "平台类目信息")
public class ApiCategoryResp extends BaseParamReq {

    /**
     * 主键
     */
    @FieldDoc(description = "id")
    private Long id;

    /**
     * 类目名称
     */
    @FieldDoc(description = "类目名称")
    private String name;

    /**
     * 类目图标
     */
    @FieldDoc(description = "类目图标")
    private String icon;

    /**
     * 上级类目id
     */
    @FieldDoc(description = "上级类目id")
    private Long parentCategoryId;

    /**
     * 是否有下级
     */
    @FieldDoc(description = "是否有下级")
    private Boolean hasChildren;


}
