package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 供应商信息返回参数
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "供应商信息返回参数")
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopResp extends BaseThriftDto {

    /**
     * 供应商ID
     */
    @FieldDoc(description = "供应商ID")
    private Long id;

    /**
     * 供应商等级
     */
    @FieldDoc(description = "供应商等级")
    private Long gradeId;

    /**
     * 供应商名称
     */
    @FieldDoc(description = "供应商名称")
    private String shopName;

    /**
     * 店铺状态
     */
    @FieldDoc(description = "店铺状态 0默认 1不可用 2待审核 3待付款 4审核拒绝 5待确认 6冻结 7开启 -1已过期")
    private Integer shopStatus;

    /**
     * 汇付操作状态
     */
    @FieldDoc(description = "汇付操作状态 0默认 1不可用 8待审核 9审核中 10审核通过 11审核失败")
    private Integer adapayStatus;

    /**
     * 平台审核状态
     */
    @FieldDoc(description = "平台审核状态 0默认 1不可用 2待审核 4审核拒绝 7审核通过")
    private Integer plateStatus;

    /**
     * 店铺创建时间
     */
    @FieldDoc(description = "店铺创建时间")
    private Date createDate;

    /**
     * 店铺过期时间
     */
    @FieldDoc(description = "店铺过期时间")
    private Date endDate;

    /**
     * 联系人姓名
     */
    @FieldDoc(description = "联系人姓名")
    private String contactsName;

    /**
     * 联系人电话
     */
    @FieldDoc(description = "联系人电话")
    private String contactsPhone;

    /**
     * 联系人Email
     */
    @FieldDoc(description = "联系人Email")
    private String contactsEmail;

    /**
     * 银行开户名
     */
    @FieldDoc(description = "银行开户名")
    private String bankAccountName;

    /**
     * 银行账号
     */
    @FieldDoc(description = "银行账号")
    private String bankAccountNumber;

    /**
     * 银行名称
     */
    @FieldDoc(description = "银行名称")
    private String bankName;

    /**
     * 支行联行号
     */
    @FieldDoc(description = "支行联行号")
    private String bankCode;

    /**
     * 开户银行所在地
     */
    @FieldDoc(description = "开户银行所在地")
    private Integer bankRegionId;

    /**
     * 开户银行类型（1对公，2对私）
     */
    @FieldDoc(description = "开户银行类型（1对公，2对私）")
    private Integer bankType;

    /**
     * 商家发货人名称
     */
    @FieldDoc(description = "商家发货人名称")
    private String senderName;

    /**
     * 商家发货人地址
     */
    @FieldDoc(description = "商家发货人地址")
    private String senderAddress;

    /**
     * 商家发货人电话
     */
    @FieldDoc(description = "商家发货人电话")
    private String senderPhone;


    /**
     * 是否缴纳保证金
     */
    @FieldDoc(description = "是否缴纳保证金")
    private Boolean whetherPayBond;

    /**
     * 是否签署协议
     */
    @FieldDoc(description = "是否签署协议")
    private Boolean whetherAgreement;
    @FieldDoc(description = "是否是需要补充资料", requiredness = Requiredness.REQUIRED)
    private Boolean whetherSupply;
    @FieldDoc(description = "是否需要续签合同", requiredness = Requiredness.REQUIRED)
    private Boolean whetherAgainSign;
    @FieldDoc(description = "是否官方自营", requiredness = Requiredness.REQUIRED)
    private Boolean whetherSelf;


    public Long getCreateDateLong() {
        return this.date2Long(this.createDate);
    }


    public void setCreateDateLong(Long createDate) {
        this.createDate = this.long2Date(createDate);
    }


    public Long getEndDateLong() {
        return this.date2Long(this.endDate);
    }


    public void setEndDateLong(Long endDate) {
        this.endDate = this.long2Date(endDate);
    }


}