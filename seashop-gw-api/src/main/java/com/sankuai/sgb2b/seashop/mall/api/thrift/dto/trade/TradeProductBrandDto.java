package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "交易商品品牌对象")
@ToString
@Data
public class TradeProductBrandDto extends BaseThriftDto {

    @FieldDoc(description = "品牌ID")
    private Long brandId;
    @FieldDoc(description = "品牌名称")
    private String brandName;
    @FieldDoc(description = "品牌logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;


}
