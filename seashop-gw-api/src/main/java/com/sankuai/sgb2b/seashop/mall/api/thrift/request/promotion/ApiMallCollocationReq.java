package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 11:25
 */
@Data
@TypeDoc(description = "商品详情-组合购活动分组入参")
public class ApiMallCollocationReq extends BaseThriftDto {

    @FieldDoc(description = "商品ID")
    private Long productId;

    @FieldDoc(description = "店铺ID")
    private Long shopId;


    public void checkParameter() {
        AssertUtil.throwIfNull(productId, "productId不能为空");
        AssertUtil.throwIfNull(shopId, "shopId不能为空");
    }
}
