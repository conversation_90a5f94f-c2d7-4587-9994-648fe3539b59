package com.sankuai.sgb2b.seashop.mall.api.thrift.request;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: liu<PERSON>x
 * @date: 2023/11/7/007
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "ID请求对象")
public class ApiIdReq extends BaseParamReq {

    private Long id;

    @Override
    public void checkParameter() {
        if (this.id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
    }


}
