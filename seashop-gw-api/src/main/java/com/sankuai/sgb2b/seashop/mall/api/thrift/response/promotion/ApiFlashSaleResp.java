package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@TypeDoc(description = "限时购活动响应体")
@Data
public class ApiFlashSaleResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "活动名称")
    private String title;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "产品ID")
    private String productId;

    @FieldDoc(description = "产品名称")
    private String productName;

    @FieldDoc(description = "活动开始日期")
    private Date beginDate;

    @FieldDoc(description = "活动结束日期")
    private Date endDate;

    @FieldDoc(description = "限购类型:1商品;2规格")
    private Integer limitType;

    @FieldDoc(description = "限购数量")
    private Integer limitCount;

    @FieldDoc(description = "活动id")
    private Long categoryId;

    @FieldDoc(description = "活动分类：默认是限时购分类")
    private String categoryName;

    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;


    public Long getBeginDateLong() {
        return this.date2Long(this.beginDate);
    }


    public void setBeginDateLong(Long beginDate) {
        this.beginDate = this.long2Date(beginDate);
    }


    public Long getEndDateLong() {
        return this.date2Long(this.endDate);
    }


    public void setEndDateLong(Long endDate) {
        this.endDate = this.long2Date(endDate);
    }


}
