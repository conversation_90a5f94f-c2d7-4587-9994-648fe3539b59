package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/04 16:12
 */
@Data
@ToString
@TypeDoc(description = "删除订单评论入参")
public class ApiDeleteOrderCommentReq extends BaseParamReq {

    @FieldDoc(description = "订单评论id", requiredness = Requiredness.REQUIRED)
    private Long id;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "订单评论id不能为空");
    }


}
