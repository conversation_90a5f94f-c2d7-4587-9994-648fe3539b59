package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "用户收货地址对象")
@ToString
@Data
public class ApiShippingAddressDto extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;
    @FieldDoc(description = "用户ID")
    private Long userId;
    @FieldDoc(description = "区域ID")
    private Integer regionId;
    @FieldDoc(description = "收货人")
    private String shipTo;
    @FieldDoc(description = "收货地址")
    private String address;
    @FieldDoc(description = "详细地址")
    private String addressDetail;
    @FieldDoc(description = "收货人电话")
    private String phone;
    @FieldDoc(description = "是否为默认")
    private Boolean whetherDefault;
    @FieldDoc(description = "是否为轻松购地址")
    private Boolean whetherQuick;
    @FieldDoc(description = "经度")
    private BigDecimal longitude;
    @FieldDoc(description = "纬度")
    private BigDecimal latitude;
    @FieldDoc(description = "创建时间")
    private Date createTime;
    @FieldDoc(description = "修改时间")
    private Date updateTime;
    @FieldDoc(description = "地区名称")
    private String regionName;
    @FieldDoc(description = "收货地址")
    private String addressName;

    @FieldDoc(description = "省份名称")
    private String provinceName;
    @FieldDoc(description = "城市名称")
    private String cityName;
    @FieldDoc(description = "区县名称")
    private String districtName;
    @FieldDoc(description = "街道名称")
    private String streetName;
    @FieldDoc(description = "省市区街道全称")
    private String regionFullName;
    @FieldDoc(description = "省份ID")
    private Long provinceId;
    @FieldDoc(description = "城市ID")
    private Long cityId;
    @FieldDoc(description = "完整的区域路径(逗号隔开)")
    private String regionPath;


    public String getLongitudeString() {
        return this.bigDecimal2String(this.longitude);
    }


    public void setLongitudeString(String longitude) {
        this.longitude = this.string2BigDecimal(longitude);
    }


    public String getLatitudeString() {
        return this.bigDecimal2String(this.latitude);
    }


    public void setLatitudeString(String latitude) {
        this.latitude = this.string2BigDecimal(latitude);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


}
