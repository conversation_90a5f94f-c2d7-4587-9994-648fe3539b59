package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询订单详情请求入参")
public class ApiQueryOrderDetailReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;

    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (this.orderId == null) {
            throw new IllegalArgumentException("orderId不能为空");
        }
    }


}
