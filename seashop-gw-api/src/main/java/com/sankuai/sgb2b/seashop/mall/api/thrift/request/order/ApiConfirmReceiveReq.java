package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "外观-确认请求入参")
public class ApiConfirmReceiveReq extends BaseParamReq {

    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @Override
    public void checkParameter() {
        if (orderId == null) {
            throw new InvalidParamException("orderId不能为空");
        }
    }


}
