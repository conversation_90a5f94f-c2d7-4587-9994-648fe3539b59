package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/22 17:03
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "规格选择返回值")
public class ApiProductSpecResp extends BaseThriftDto {

    @FieldDoc(description = "specId")
    private Long specValueId;

    @FieldDoc(description = "规格值")
    private String value;

    @FieldDoc(description = "是否可选")
    private Boolean selectAble;

    @FieldDoc(description = "展示图片")
    @JsonUrlFormat(deserializer = false)
    private String showPic;


}
