package com.sankuai.sgb2b.seashop.mall.api.thrift.response.refund.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "售后对象")
public class ApiUserRefundDto extends BaseThriftDto {

    @FieldDoc(description = "退款ID，退款表主键ID")
    private Long refundId;
    @FieldDoc(description = "订单号")
    private String orderId;
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    @FieldDoc(description = "订单实付金额")
    private BigDecimal orderPayAmount;
    /**
     * 买家ID
     */
    @FieldDoc(description = "买家ID")
    private Long userId;
    /**
     * 买家账号
     */
    @FieldDoc(description = "买家账号")
    private String userName;
    /**
     * 退款数量
     */
    @FieldDoc(description = "退款数量")
    private Long refundQuantity;
    @FieldDoc(description = "退款金额")
    private BigDecimal refundAmount;
    @FieldDoc(description = "申请日期")
    private Date applyDate;
    @FieldDoc(description = "退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：待平台确认；6：退款成功；7：平台驳回；8：退款中；9：买家取消")
    private Integer refundStatus;
    @FieldDoc(description = "退款状态描述")
    private String refundStatusDesc;
    @FieldDoc(description = "是否可以重新申请")
    private Boolean canReapply;
    @FieldDoc(description = "退款商品明细列表")
    private List<ApiRefundItemDto> itemList;
    @FieldDoc(description = "是否订单全部退")
    private Boolean hasAllReturn;
    /**
     * 是否明细退，如果为true，重新申请的时候调用明细预览接口，否则调用订单预览接口
     */
    @FieldDoc(description = "是否明细退，如果为true，重新申请的时候调用明细预览接口，否则调用订单预览接口")
    private Boolean whetherItemRefund;
    /**
     * 订单项ID
     */
    @FieldDoc(description = "订单项ID")
    private Long orderItemId;

    /**
     * 是否已过售后维权期，简单的按钮，前端需要根据状态和这个字段综合判断是否显示
     */
    @FieldDoc(description = "是否已过售后维权期，简单的按钮，前端需要根据状态和这个字段综合判断是否显示")
    private Boolean hasOverAfterSales;

    /**
     * 是否可以取消售后。用于控制前端【取消售后】按钮显示
     * 1. 发起了售后，订单退款，或仅退款，且是待供应商审核或待平台审核时，显示
     * 2. 发起了售后，退货退款时，待供应商审核或待买家寄货时，显示
     */
    @FieldDoc(description = "是否可以取消售后。用于控制前端【取消售后】按钮显示")
    private Boolean showCancelRefundBtn;
    /**
     * 售后类型。1：订单退款
     */
    @FieldDoc(description = "售后类型。1：仅退款；2：退货退款")
    private Integer refundType;


    public String getOrderPayAmountString() {
        return this.bigDecimal2String(this.orderPayAmount);
    }


    public void setOrderPayAmountString(String orderPayAmount) {
        this.orderPayAmount = this.string2BigDecimal(orderPayAmount);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


    public Long getApplyDateLong() {
        return this.date2Long(this.applyDate);
    }


    public void setApplyDateLong(Long applyDate) {
        this.applyDate = this.long2Date(applyDate);
    }


}
