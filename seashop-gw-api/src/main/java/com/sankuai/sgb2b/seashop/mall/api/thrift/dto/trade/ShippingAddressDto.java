package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "用户收货地址对象")
@ToString
@Data
public class ShippingAddressDto extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;
    @FieldDoc(description = "用户ID")
    private Long userId;
    @FieldDoc(description = "区域ID")
    private Integer regionId;
    @FieldDoc(description = "收货人")
    private String shipTo;
    @FieldDoc(description = "收货地址")
    private String address;
    @FieldDoc(description = "详细地址")
    private String addressDetail;
    @FieldDoc(description = "收货人电话")
    private String phone;
    @FieldDoc(description = "是否为默认")
    private Boolean whetherDefault;

    @FieldDoc(description = "省份名称")
    private String provinceName;
    @FieldDoc(description = "城市名称")
    private String cityName;
    @FieldDoc(description = "区县名称")
    private String districtName;
    @FieldDoc(description = "街道名称")
    private String streetName;
    @FieldDoc(description = "省市区街道全称")
    private String regionFullName;
    @FieldDoc(description = "完整的区域路径(逗号隔开)")
    private String regionPath;


}
