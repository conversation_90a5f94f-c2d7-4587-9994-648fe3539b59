package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:46
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "组合购sku返回值")
public class ApiCollocationProductSkuResp extends BaseThriftDto {

    @Schema(description = "sku自增id")
    private Long skuAutoId;

    @Schema(description = "skuId")
    private String skuId;



    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "库存")
    private Long stock;



    @Schema(description = "sku图片")
    private String showPic;





}
