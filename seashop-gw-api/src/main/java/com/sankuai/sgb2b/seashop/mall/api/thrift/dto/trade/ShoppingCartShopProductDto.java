package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.OrderAdditionalDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/2
 */
@TypeDoc(description = "购物车商品返回对象")
@ToString
@Data
@Getter
@Setter
public class ShoppingCartShopProductDto {

    @FieldDoc(description = "店铺信息", requiredness = Requiredness.REQUIRED)

    private ShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表", requiredness = Requiredness.REQUIRED)

    private List<ShoppingCartProductDto> productList;
    @FieldDoc(description = "附加信息")

    private OrderAdditionalDto additional;

}
