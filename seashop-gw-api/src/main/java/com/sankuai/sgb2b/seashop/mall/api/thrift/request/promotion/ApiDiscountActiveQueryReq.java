package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保存折扣活动请求对象")
public class ApiDiscountActiveQueryReq extends BasePageReq {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @FieldDoc(description = "活动名称", requiredness = Requiredness.REQUIRED)
    private String activeName;

    @FieldDoc(description = "开始时间", requiredness = Requiredness.REQUIRED)
    private Date startTime;

    @FieldDoc(description = "结束时间", requiredness = Requiredness.REQUIRED)
    private Date endTime;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }
}
