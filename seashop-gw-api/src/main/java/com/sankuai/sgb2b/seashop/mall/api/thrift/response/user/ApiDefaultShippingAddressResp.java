package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ShippingAddressDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "用户收货地址返回对象，不能直接返回dto对象，所以包一层")
@ToString
@Data
public class ApiDefaultShippingAddressResp {

    @FieldDoc(description = "默认收货地址")
    private ShippingAddressDto defaultAddress;


}
