package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "删除购物车sku请求入参")
public class ApiClearInvalidShoppingCartSkuReq extends BaseParamReq {

    @FieldDoc(description = "购物车唯一标识，目前是数据表主键ID，取购物车列表的ID字段", requiredness = Requiredness.REQUIRED)
    private List<Long> idList;

    /**
     * 基本参数校验
     */
    public void checkParameter() {
        if (CollUtil.isEmpty(this.idList)) {
            throw new BusinessException("id不能为空");
        }
    }


}
