package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/22 17:03
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "规格选择返回值")
public class ApiProductSpecGroupResp extends BaseThriftDto {

    @FieldDoc(description = "规格 取值1、2、3")
    private Integer spec;

    @FieldDoc(description = "规格别名")
    private String specAlias;

    @FieldDoc(description = "规格值集合")
    private List<ApiProductSpecResp> specValueList;


}
