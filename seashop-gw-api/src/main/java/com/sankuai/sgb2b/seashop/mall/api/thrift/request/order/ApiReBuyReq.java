package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "外观-重新购买请求入参")
public class ApiReBuyReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @Override
    public void checkParameter() {
        if (StringUtils.isNotBlank(orderId)) {
            throw new IllegalArgumentException("orderId不能为空");
        }
    }


}
