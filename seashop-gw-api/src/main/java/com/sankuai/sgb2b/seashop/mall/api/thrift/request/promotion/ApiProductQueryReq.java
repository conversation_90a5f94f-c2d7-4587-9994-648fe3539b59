package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "商品信息查询请求对象")
public class ApiProductQueryReq extends BaseParamReq {

    @FieldDoc(description = "商品ID", requiredness = Requiredness.REQUIRED)
    private Long productId;

    @FieldDoc(description = "商品总额", requiredness = Requiredness.REQUIRED)
    private BigDecimal productAmount;

    @Override
    public void checkParameter() {
        if (this.productId == null || this.productId <= 0) {
            throw new InvalidParamException("productId不能为空");
        }
        if (this.productAmount == null || this.productAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("productAmount不能为空");
        }
    }


    public String getProductAmountString() {
        return this.bigDecimal2String(this.productAmount);
    }


    public void setProductAmountString(String productAmount) {
        this.productAmount = this.string2BigDecimal(productAmount);
    }
}
