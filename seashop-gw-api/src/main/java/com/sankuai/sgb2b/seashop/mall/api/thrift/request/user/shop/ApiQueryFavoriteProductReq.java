package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

/**
 * @description：查询商品收藏列表
 * @author： liweisong
 * @create： 2023/11/28 9:37
 */
@Data
@ToString
@TypeDoc(description = "查询商品收藏列表入参")
public class ApiQueryFavoriteProductReq extends BasePageReq {

    @FieldDoc(description = "用户ID")
    private Long userId;


}
