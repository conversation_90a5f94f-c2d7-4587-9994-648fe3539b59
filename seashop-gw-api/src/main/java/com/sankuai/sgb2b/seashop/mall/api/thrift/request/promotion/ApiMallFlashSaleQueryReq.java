package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "限时购查询请求对象")
public class ApiMallFlashSaleQueryReq extends BasePageReq {

    @FieldDoc(description = "活动类型id")
    private Long categoryId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "会员id")
    private Long memberId;


}
