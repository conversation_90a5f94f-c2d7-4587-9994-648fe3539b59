package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "店铺分页查询")
public class ApiShopQueryPagerReq extends BasePageReq {

    @FieldDoc(description = "状态", requiredness = Requiredness.REQUIRED)
    private Integer status;
    @FieldDoc(description = "审核状态", requiredness = Requiredness.REQUIRED)
    private Integer auditStatus;
    @FieldDoc(description = "店铺名", requiredness = Requiredness.REQUIRED)
    private String shopName;
    @FieldDoc(description = "状态", requiredness = Requiredness.REQUIRED)
    private String shopAccount;
    @FieldDoc(description = "分类ID", requiredness = Requiredness.REQUIRED)
    private Long categoryId;
    @FieldDoc(description = "品牌ID", requiredness = Requiredness.REQUIRED)
    private Long brandId;
    @FieldDoc(description = "创建时间", requiredness = Requiredness.REQUIRED)
    private Date createDateBegin;
    @FieldDoc(description = "结束时间", requiredness = Requiredness.REQUIRED)
    private Date createDateEnd;
    @FieldDoc(description = "过期时间", requiredness = Requiredness.REQUIRED)
    private Date expiredDateEnd;
    @FieldDoc(description = "步骤", requiredness = Requiredness.REQUIRED)
    private Integer stage;
    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private List<Long> shopIds;
    @FieldDoc(description = "是否签署合同", requiredness = Requiredness.REQUIRED)
    private Boolean whetherAgreement;
    @FieldDoc(description = "是否支付保证金", requiredness = Requiredness.REQUIRED)
    private Boolean whetherPayBond;
    @FieldDoc(description = "是否是供应商", requiredness = Requiredness.REQUIRED)
    private Boolean whetherSupply;
    @FieldDoc(description = "是否需要续签合同", requiredness = Requiredness.REQUIRED)
    private Boolean whetherAgainSign;
    @FieldDoc(description = "商家ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "是否前台搜索", requiredness = Requiredness.REQUIRED)
    private Boolean whetherFrontSearch;
    @FieldDoc(description = "店铺Id", requiredness = Requiredness.REQUIRED)
    private Long id;


    public Long getCreateDateBeginLong() {
        return this.date2Long(this.createDateBegin);
    }


    public void setCreateDateBeginLong(Long createDateBegin) {
        this.createDateBegin = this.long2Date(createDateBegin);
    }


    public Long getCreateDateEndLong() {
        return this.date2Long(this.createDateEnd);
    }


    public void setCreateDateEndLong(Long createDateEnd) {
        this.createDateEnd = this.long2Date(createDateEnd);
    }


    public Long getExpiredDateEndLong() {
        return this.date2Long(this.expiredDateEnd);
    }


    public void setExpiredDateEndLong(Long expiredDateEnd) {
        this.expiredDateEnd = this.long2Date(expiredDateEnd);
    }


}
