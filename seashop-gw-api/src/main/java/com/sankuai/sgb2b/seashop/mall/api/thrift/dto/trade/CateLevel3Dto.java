package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "第三级类目，thrift不支持嵌套对象，所以分开定义")
@ToString
@Data
public class CateLevel3Dto {

    @FieldDoc(description = "类目id")
    private Long id;
    @FieldDoc(description = "类目名称")
    private String name;
    @FieldDoc(description = "类目路径")
    private String path;


}
