package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/02 22:11
 */
@TypeDoc(
    description = "商品设置返回值"
)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ApiProductSettingResp extends BaseThriftDto {

    @FieldDoc(description = "是否开启审核")
    private Boolean productAuditOnOff;

    @FieldDoc(description = "是否开启销量显示")
    private Boolean productSaleCountOnOff;


}
