package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商信息返回参数")
public class ApiBusinessCategoryPageResp {
    @FieldDoc(description = "分页结果")
    private BasePageResp<ApiBusinessCategoryResp> pageResp;


}
