package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "文章分类返回")
@Data
public class ApiArticleCategoryListRes extends BaseThriftDto {

    @FieldDoc(description = "分类对象集合")
    private List<ApiBaseArticleCategoryRes> categorys;


}
