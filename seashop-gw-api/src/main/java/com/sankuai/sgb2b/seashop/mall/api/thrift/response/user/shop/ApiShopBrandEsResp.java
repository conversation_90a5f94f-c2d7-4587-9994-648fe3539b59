package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "店铺ES品牌列表响应体")
public class ApiShopBrandEsResp extends BaseThriftDto {

    /**
     * 主键
     */
    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 品牌ID
     */
    @FieldDoc(description = "品牌ID")
    private Long brandId;

    /**
     * 品牌名称
     */
    @FieldDoc(description = "品牌名称")
    private String brandName;

    /**
     * 品牌logo
     */
    @FieldDoc(description = "品牌logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;


}
