package com.sankuai.sgb2b.seashop.mall.api.thrift.response.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单退款预览信息对象")
public class ApiOrderRefundPreviewResp extends BaseThriftDto {

    @FieldDoc(description = "订单ID")
    private String orderId;
    @FieldDoc(description = "订单创建时间")
    private Date orderDate;
    /**
     * 订单总金额
     */
    @FieldDoc(description = "订单总金额")
    private BigDecimal orderTotalAmount;
    @FieldDoc(description = "订单可退款金额")
    private BigDecimal remainRefundAmount;
    @FieldDoc(description = "联系人姓名")
    private String contactUserName;
    @FieldDoc(description = "联系人电话")
    private String contactUserPhone;
    @FieldDoc(description = "可退金额描述(最多X元，包含运费Y元)")
    private String remainRefundAmountDesc;
    /**
     * 订单可退款数量
     */
    @FieldDoc(description = "订单可退款数量")
    private Long remainRefundQuantity;
    @FieldDoc(description = "退款ID")
    private Long refundId;
    /**
     * 售后类型。1：订单退款
     */
    @FieldDoc(description = "售后类型。1：仅退款；2：退货退款")
    private Integer refundType;
    /**
     * 售后类型描述
     */
    @FieldDoc(description = "售后类型描述")
    private String refundTypeDesc;
    /**
     * 退款模式。1：订单退款；2：货品退款；3：退货退款
     */
    @FieldDoc(description = "退款模式。1：订单退款；2：货品退款；3：退货退款")
    private Integer refundMode;
    /**
     * 退款模式描述
     */
    @FieldDoc(description = "退款模式描述")
    private String refundModeDesc;
    /**
     * 申请退款金额
     */
    @FieldDoc(description = "申请退款金额")
    private BigDecimal refundAmount;
    /**
     * 退款数量。退货退款时有值
     */
    @FieldDoc(description = "退款数量。退货退款时有值")
    private Long refundQuantity;
    /**
     * 退款原因类型
     */
    @FieldDoc(description = "退款原因类型")
    private String refundReasonDesc;
    /**
     * 退款说明
     */
    @FieldDoc(description = "退款说明")
    private String refundRemark;
    /**
     * 退款方式。1：原路返回
     */
    @FieldDoc(description = "退款方式。1：原路返回")
    private Integer refundPayType;
    /**
     * 退款方式描述
     */
    @FieldDoc(description = "退款方式描述")
    private String refundPayTypeDesc;
    /**
     * 售后凭证1。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证1。最多三张，与数据表保持一致分开")
    private String certPic1;
    /**
     * 售后凭证2。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证2。最多三张，与数据表保持一致分开")
    private String certPic2;
    /**
     * 售后凭证3。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证3。最多三张，与数据表保持一致分开")
    private String certPic3;
    @FieldDoc(description = "订单状态。1：待付款，2：待发货，3：待收货，4：已关闭，5：已完成，6：支付中")
    private Integer orderStatus;
    @FieldDoc(description = "订单状态描述")
    private String orderStatusDesc;


    public Long getOrderDateLong() {
        return this.date2Long(this.orderDate);
    }


    public void setOrderDateLong(Long orderDate) {
        this.orderDate = this.long2Date(orderDate);
    }


    public String getOrderTotalAmountString() {
        return this.bigDecimal2String(this.orderTotalAmount);
    }


    public void setOrderTotalAmountString(String orderTotalAmount) {
        this.orderTotalAmount = this.string2BigDecimal(orderTotalAmount);
    }


    public String getRemainRefundAmountString() {
        return this.bigDecimal2String(this.remainRefundAmount);
    }


    public void setRemainRefundAmountString(String remainRefundAmount) {
        this.remainRefundAmount = this.string2BigDecimal(remainRefundAmount);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


}
