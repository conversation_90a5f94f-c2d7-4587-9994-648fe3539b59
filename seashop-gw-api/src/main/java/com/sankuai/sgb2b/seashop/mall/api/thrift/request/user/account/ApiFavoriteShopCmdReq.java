package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "收藏店铺请求参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiFavoriteShopCmdReq {
    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "商家ID token中获取不需要传")
    private Long userId;

    @FieldDoc(description = "店铺id列表")
    private List<Long> shopIdList;


}
