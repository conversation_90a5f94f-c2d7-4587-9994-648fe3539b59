package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/12/13 15:28
 */
@Data
@TypeDoc(description = "商家中心收藏店铺返参")
public class ApiFavoriteShopResp extends BaseThriftDto {

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "店铺logo")
    private String shopLogo;

    @FieldDoc(description = "关注人数")
    private Integer popularity;

    @FieldDoc(description = "关注时间")
    private Date createTime;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }
}
