package com.sankuai.sgb2b.seashop.mall.api.thrift.request.refund;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "退款申请入参")
public class ApiApplyOrderRefundReq extends BaseParamReq {

    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "售后类型。1：仅退款；2：退货退款", requiredness = Requiredness.REQUIRED)
    private Integer refundType;
    @FieldDoc(description = "申请退款金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal refundAmount;
    @FieldDoc(description = "申请退款数量。整单退货/退款，选择退货退款时要传", requiredness = Requiredness.REQUIRED)
    private Long refundQuantity;
    @FieldDoc(description = "退款原因描述。基础服务提供接口获取选项", requiredness = Requiredness.REQUIRED)
    private String refundReasonDesc;
    @FieldDoc(description = "退款说明")
    private String refundRemark;
    @FieldDoc(description = "联系人姓名", requiredness = Requiredness.REQUIRED)
    private String contactUserName;
    @FieldDoc(description = "联系人电话", requiredness = Requiredness.REQUIRED)
    private String contactUserPhone;
    @FieldDoc(description = "退款方式。1：原路返回", requiredness = Requiredness.REQUIRED)
    private Integer refundPayType;
    @FieldDoc(description = "售后凭证1。最多三张，与数据表保持一致分开")
    private String certPic1;
    @FieldDoc(description = "售后凭证2。最多三张，与数据表保持一致分开")
    private String certPic2;
    @FieldDoc(description = "售后凭证3。最多三张，与数据表保持一致分开")
    private String certPic3;
    @FieldDoc(description = "登录用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;

    @Override
    public void checkParameter() {
        if (refundType == null) {
            throw new InvalidParamException("退款类型不能为空");
        }
        if (refundAmount == null || refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("退款金额不能为空");
        }
        if (StrUtil.isBlank(refundReasonDesc)) {
            throw new InvalidParamException("退款原因不能为空");
        }
        if (StrUtil.isBlank(contactUserName)) {
            throw new InvalidParamException("联系人姓名不能为空");
        }
        if (StrUtil.isBlank(contactUserPhone)) {
            throw new InvalidParamException("联系人电话不能为空");
        }
        if (refundPayType == null) {
            throw new InvalidParamException("退款方式不能为空");
        }
        if (this.userId == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        if (StrUtil.isNotBlank(refundRemark) && refundRemark.length() > 1000) {
            throw new InvalidParamException("退款说明不能超过1000个字符");
        }
    }

}
