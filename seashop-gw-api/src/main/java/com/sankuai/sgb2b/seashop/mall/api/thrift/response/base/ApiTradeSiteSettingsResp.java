package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * @author： liweisong
 * @create： 2023/11/22 17:06
 */
@TypeDoc(description = "交易参数设置返参")
@Data
@ToString
public class ApiTradeSiteSettingsResp extends BaseThriftDto {

    @FieldDoc(description = "自动收货完成前时间")
    private String beforeReceivingDays;

    @FieldDoc(description = "延迟收货时间")
    private String noReceivingDelayDays;

    @FieldDoc(description = "订单退货期限(订单完成后X天关闭售后通道)")
    private String salesReturnTimeout;


}
