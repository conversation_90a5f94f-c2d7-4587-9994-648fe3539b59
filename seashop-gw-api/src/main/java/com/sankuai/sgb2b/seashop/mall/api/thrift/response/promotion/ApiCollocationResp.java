package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CollocationProductResp;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Data
@ToString
@TypeDoc(description = "组合购信息")
public class ApiCollocationResp extends BaseThriftDto {

    @FieldDoc(description = "组合购主表ID")
    private Long id;

    @FieldDoc(description = "组合购标题")
    private String title;

    @FieldDoc(description = "开始日期")
    private Date startTime;

    @FieldDoc(description = "结束日期")
    private Date endTime;

    @FieldDoc(description = "组合描述")
    private String shortDesc;

    @FieldDoc(description = "组合购店铺ID")
    private Long shopId;

    @FieldDoc(description = "创建时间")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    private Date updateTime;

    @FieldDoc(description = "组合购商品集合")
    private List<CollocationProductResp> productRespList;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


}
