package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
@Data
@ToString
@TypeDoc(description = "迁移数据绑定手机号请求")
public class ApiTransferPhoneReq {

    @FieldDoc(description = "用户名", requiredness = Requiredness.OPTIONAL)
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @FieldDoc(description = "密码", requiredness = Requiredness.OPTIONAL)
    @NotBlank(message = "验证码不能为空")
    private String smsCod;

}
