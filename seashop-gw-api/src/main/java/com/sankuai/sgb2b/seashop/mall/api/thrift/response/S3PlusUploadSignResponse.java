package com.sankuai.sgb2b.seashop.mall.api.thrift.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Data
@ToString
@TypeDoc(description = "s3文件上传获取签名数据响应")
public class S3PlusUploadSignResponse {

    @FieldDoc(description = "过期时间", requiredness = Requiredness.REQUIRED)
    private String date;

    @FieldDoc(description = "签名内容", requiredness = Requiredness.REQUIRED)
    private String signature;

    @FieldDoc(description = "策略", requiredness = Requiredness.REQUIRED)
    private String policy;

    @FieldDoc(description = "访问ID", requiredness = Requiredness.REQUIRED)
    private String awsAccessKeyId;

    @FieldDoc(description = "key", requiredness = Requiredness.REQUIRED)
    private String key;
}
