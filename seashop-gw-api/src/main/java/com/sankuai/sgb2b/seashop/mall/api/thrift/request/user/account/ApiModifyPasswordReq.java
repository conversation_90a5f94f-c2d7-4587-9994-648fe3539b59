package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/05/24 9:38
 */
@TypeDoc(description = "重置密码入参")
@Data
public class ApiModifyPasswordReq extends BaseParamReq {

    @FieldDoc(description = "旧密码")
    private String oldPassword;

    @FieldDoc(description = "新密码")
    private String newPassword;

    @FieldDoc(description = "确认密码")
    private String confirmPassword;



    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(StringUtils.isBlank(newPassword), "密码不能为空");
        AssertUtil.throwIfTrue(StringUtils.isBlank(confirmPassword), "确认密码不能为空");
    }


}
