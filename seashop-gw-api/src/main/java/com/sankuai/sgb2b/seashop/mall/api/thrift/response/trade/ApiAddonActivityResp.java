package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiAddonActivityDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "凑单页面对应的活动对象")
@ToString
@Data
public class ApiAddonActivityResp extends BaseThriftDto {

    @FieldDoc(description = "活动列表")
    private List<ApiAddonActivityDto> activityList;


}
