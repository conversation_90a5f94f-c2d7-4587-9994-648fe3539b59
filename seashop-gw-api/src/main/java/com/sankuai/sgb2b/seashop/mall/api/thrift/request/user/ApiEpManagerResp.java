package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

/**
 * @description: 管理员信息
 * @author: LXH
 **/
public class ApiEpManagerResp extends BaseThriftDto {
    @FieldDoc(description = "管理员id")
    private Long managerId;
    @FieldDoc(description = "供应商id")
    private Long shopId;
    @FieldDoc(description = "供应商名称")
    private String shopName;
    @FieldDoc(description = "商家id")
    private Long userId;
    @FieldDoc(description = "商家名称")
    private String memberName;
    @FieldDoc(description = "权限组id")
    private Long roleId;
    @FieldDoc(description = "用户名")
    private String userName;
    @FieldDoc(description = "手机号码")
    private String cellphone;
    @FieldDoc(description = "真实姓名")
    private String realName;
    @FieldDoc(description = "微信用户ID")
    private String weiXinOpenId;
    @FieldDoc(description = "微信用户ID")
    private String token;
    @FieldDoc(description = "密码是否符合规则")
    private Boolean passwordCorrect;
    @FieldDoc(description = "用户名是否符合规则")
    private Boolean userNameCorrect;
    @FieldDoc(description = "是否已经拥有ep账户")
    private Boolean epExist;
    @FieldDoc(description = "现在的EP登录名称")
    private String epAccount;


}
