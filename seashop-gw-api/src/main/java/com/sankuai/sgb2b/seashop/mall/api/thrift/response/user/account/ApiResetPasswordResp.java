package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/05/24 9:44
 */
@Data
@ToString
@TypeDoc(description = "重置密码返回值")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResetPasswordResp extends BaseThriftDto {

    @FieldDoc(description = "是否已迁移")
    private Boolean whetherTransfer;

    @FieldDoc(description = "新密码")
    private String newPassword;


}
