package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiTradeProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.CateLevel1Dto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.TradeProductBrandDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "搜索交易商品返回对象")
@ToString
@Data
public class ApiSearchTradeProductResp extends BaseThriftDto {

    @FieldDoc(description = "品牌列表")
    private List<TradeProductBrandDto> brandList;
    @FieldDoc(description = "类目树")
    private List<CateLevel1Dto> categoryTreeList;
    @FieldDoc(description = "商品分页列表")
    private BasePageResp<ApiTradeProductDto> productPage;


}
