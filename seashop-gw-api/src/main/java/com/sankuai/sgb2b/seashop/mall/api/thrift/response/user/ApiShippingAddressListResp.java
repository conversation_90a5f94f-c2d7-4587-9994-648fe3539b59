package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ShippingAddressDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "用户收货地址返回对象")
@ToString
@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApiShippingAddressListResp {

    @FieldDoc(description = "收货地址列表")

    private List<ShippingAddressDto> addressList;

}
