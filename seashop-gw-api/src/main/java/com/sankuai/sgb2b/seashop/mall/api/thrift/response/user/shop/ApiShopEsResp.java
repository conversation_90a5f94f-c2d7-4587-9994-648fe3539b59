package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiTradeProductDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "店铺ES查询响应体")
public class ApiShopEsResp extends BaseThriftDto {

    /**
     * 店铺id
     */
    @FieldDoc(description = "店铺id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @FieldDoc(description = "店铺名称")
    private String shopName;

    /**
     * 店铺logo
     */
    @FieldDoc(description = "店铺logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;

    /**
     * 店铺状态
     */
    @FieldDoc(description = "店铺状态")
    private Integer shopStatus;

    /**
     * 是否开启专属商家
     */
    @FieldDoc(description = "是否开启专属商家")
    private Boolean whetherOpenExclusiveMember;

    /**
     * 订单量（已完成）
     */
    @FieldDoc(description = "订单量")
    private Long orderSaleCount;

    /**
     * 商品销量（实际销量+虚拟销量）
     */
    @FieldDoc(description = "商品销量（实际销量+虚拟销量）")
    private Long productSaleCount;

    /**
     * 商品实际销量
     */
    @FieldDoc(description = "商品实际销量")
    private Long productRealSaleCount;

    /**
     * 商品虚拟销量
     */
    @FieldDoc(description = "商品虚拟销量")
    private Long productVirtualSaleCount;

    /**
     * 评分数量
     */
    @FieldDoc(description = "评分数量")
    private Long markCount;

    /**
     * 包装评分
     */
    @FieldDoc(description = "包装评分")
    private BigDecimal packMark;

    /**
     * 服务评分
     */
    @FieldDoc(description = "服务评分")
    private BigDecimal serviceMark;

    /**
     * 综合评分
     */
    @FieldDoc(description = "综合评分")
    private BigDecimal comprehensiveMark;

    /**
     * 热门关注商品
     */
    @FieldDoc(description = "热门关注商品")
    private List<ApiTradeProductDto> productList;

    @FieldDoc(description = "商品总数")
    private Long productCount = 0L;

    @FieldDoc(description = "收藏数量")
    private Integer favoriteCount = 0;

    @FieldDoc(description = "是否收藏")
    private Boolean favoriteFlag = false;


    public String getPackMarkString() {
        return this.bigDecimal2String(this.packMark);
    }


    public void setPackMarkString(String packMark) {
        this.packMark = this.string2BigDecimal(packMark);
    }


    public String getServiceMarkString() {
        return this.bigDecimal2String(this.serviceMark);
    }


    public void setServiceMarkString(String serviceMark) {
        this.serviceMark = this.string2BigDecimal(serviceMark);
    }


    public String getComprehensiveMarkString() {
        return this.bigDecimal2String(this.comprehensiveMark);
    }


    public void setComprehensiveMarkString(String comprehensiveMark) {
        this.comprehensiveMark = this.string2BigDecimal(comprehensiveMark);
    }


}
