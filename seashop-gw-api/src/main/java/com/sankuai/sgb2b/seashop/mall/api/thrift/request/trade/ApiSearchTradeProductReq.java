package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "搜索交易商品请求入参")
@ToString
@Data
public class ApiSearchTradeProductReq extends BasePageReq {

    @FieldDoc(description = "搜索关键字，目前是针对商品名称")
    private String searchKey;
    @FieldDoc(description = "类目ID")
    private Long categoryId;
    @FieldDoc(description = "类目全路径")
    private String categoryPath;
    @FieldDoc(description = "品牌ID")
    private Long brandId;
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "优惠券ID")
    private Long couponId;


}
