package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "勾选购物车店铺入参")
public class ApiSelectShopReq extends BaseParamReq {

    @FieldDoc(description = "商家会员ID，用于校验是否操作的是自己的数据", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "是否选中", requiredness = Requiredness.REQUIRED)
    private Boolean whetherSelect;
    @FieldDoc(description = "店铺信息", requiredness = Requiredness.REQUIRED)
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表", requiredness = Requiredness.REQUIRED)
    private List<ApiShoppingCartProductDto> productList;

    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.whetherSelect == null) {
            throw new InvalidParamException("whetherSelect不能为空");
        }
        if (this.shop == null) {
            throw new InvalidParamException("shop不能为空");
        }
        if (CollUtil.isEmpty(productList)) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
