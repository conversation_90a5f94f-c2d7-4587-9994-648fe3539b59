package com.sankuai.sgb2b.seashop.mall.api.thrift.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * @author: lhx
 * @date: 2024/1/30/030
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "营销条件入参")
public class ApiQueryPromotionReq extends BaseThriftDto {

    @FieldDoc(description = "营销类型")
    private Integer promotionType;

    @FieldDoc(description = "营销状态：0未开始；1进行中；2已结束")
    private Integer promotionStatus;


}
