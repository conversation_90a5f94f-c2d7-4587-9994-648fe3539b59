package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopProductDto;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "全选购物车入参")
public class ApiSelectAllReq extends BaseParamReq {

    @FieldDoc(description = "商家会员ID，用于校验是否操作的是自己的数据", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "是否选中", requiredness = Requiredness.REQUIRED)
    private Boolean whetherSelect;
    @FieldDoc(description = "购物车数据", requiredness = Requiredness.REQUIRED)
    private List<ApiShoppingCartShopProductDto> shopProductList;

    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.whetherSelect == null) {
            throw new InvalidParamException("whetherSelect不能为空");
        }
        if (CollUtil.isEmpty(shopProductList)) {
            throw new InvalidParamException("购物车数据不能为空");
        }
    }


}
