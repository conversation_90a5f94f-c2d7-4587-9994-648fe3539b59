package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 14:14
 */
@Data
@TypeDoc(description = "小程序返参")
public class ApiQueryWxAppletFormDataResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "事件ID")
    private Long eventId;

    @FieldDoc(description = "事件值")
    private String eventValue;

    @FieldDoc(description = "事件的表单ID")
    private String formId;

    @FieldDoc(description = "事件时间")
    private Date eventTime;

    @FieldDoc(description = "FormId过期时间")
    private Date expireTime;


    public Long getEventTimeLong() {
        return this.date2Long(this.eventTime);
    }


    public void setEventTimeLong(Long eventTime) {
        this.eventTime = this.long2Date(eventTime);
    }


    public Long getExpireTimeLong() {
        return this.date2Long(this.expireTime);
    }


    public void setExpireTimeLong(Long expireTime) {
        this.expireTime = this.long2Date(expireTime);
    }
}
