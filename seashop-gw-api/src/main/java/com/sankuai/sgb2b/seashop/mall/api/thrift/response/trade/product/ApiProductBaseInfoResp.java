package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiProductSpecGroupResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiCommentSummaryResp;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/22 15:17
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "商品基本信息返回值")
public class ApiProductBaseInfoResp extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "货号")
    private String productCode;

    @FieldDoc(description = "品牌名称")
    private String brandName;

    @FieldDoc(description = "广告词")
    private String shortDescription;

    @FieldDoc(description = "分类集合")
    private List<ApiProductCategoryResp> categoryList;

    @FieldDoc(description = "销量")
    private Integer salesCount;

    @FieldDoc(description = "商品图片集合")
    @JsonUrlFormat(deserializer = false)
    private List<String> imageList;

    @FieldDoc(description = "是否收藏")
    private Boolean collectStatus;

    @FieldDoc(description = "默认收货地址")
    private ApiUserShippingAddressResp defaultShippingAddress;

    @FieldDoc(description = "是否有多规格")
    private Boolean hasSku;

    @FieldDoc(description = "规格组")
    private List<ApiProductSpecGroupResp> specGroupList;

    @FieldDoc(description = "规格集合")
    private List<ApiProductSkuResp> skuList;

    @FieldDoc(description = "商品活动信息")
    private ApiProductActivityInfoResp activityInfo;

    @FieldDoc(description = "是否可售")
    private Boolean saleAble;

    @FieldDoc(description = "商品评价信息")
    private ApiCommentSummaryResp commentSummary;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "销售价")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "市场价")
    private BigDecimal marketPrice;

    @FieldDoc(description = "预估价")
    private BigDecimal estimatePrice;

    @FieldDoc(description = "最大购买量")
    private Integer maxBuyCount;

    @FieldDoc(description = "倍数起购量")
    private Integer multipleCount;

    @FieldDoc(description = "运费模板信息")
    private ApiFreightTemplateResp freightTemplateInfo;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "咨询数量")
    private Integer consultCount;

    @FieldDoc(description = "视频地址")
    @JsonUrlFormat(deserializer = false)
    private String videoPath;

    @FieldDoc(description = "计量单位")
    private String measureUnit;

    @FieldDoc(description = "最大售价")
    private BigDecimal maxSalePrice;

    @FieldDoc(description = "销售价格范围")
    private String salePriceRange;

    @FieldDoc(description = "品牌logo")
    @JsonUrlFormat(deserializer = false)
    private String brandLogo;

    @FieldDoc(description = "交易状态 0-已售罄 1-销售中 2-已下架")
    private Integer tradeStatus;

    @FieldDoc(description = "总库存")
    private Long totalStock;

    @FieldDoc(description = "限时购库存")
    private Long flashSaleTotalStock;

    @FieldDoc(description = "移动端商品描述图片列表")
    private List<String> descriptionPicList;

    @FieldDoc(description = "OE号")
    private String oeCode;

    @FieldDoc(description = "品牌号")
    private String brandCode;

    @FieldDoc(description = "零件品质 [0 4S-原厂原包,1 进口-原厂原包 2 国产-原厂原包 3 原厂无包 4 品牌件]")
    private Integer partQuality;

    @FieldDoc(description = "质保时间")
    private Integer warrantyPeriod;

    @FieldDoc(description = "适用车型")
    private String adaptableCar;

    @FieldDoc(description = "零件规格")
    private String partSpec;

    @FieldDoc(description = "替换号")
    private String replaceNumber;

    @FieldDoc(description = "备注")
    private String remark;

    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


    public String getMarketPriceString() {
        return this.bigDecimal2String(this.marketPrice);
    }


    public void setMarketPriceString(String marketPrice) {
        this.marketPrice = this.string2BigDecimal(marketPrice);
    }


    public String getEstimatePriceString() {
        return this.bigDecimal2String(this.estimatePrice);
    }


    public void setEstimatePriceString(String estimatePrice) {
        this.estimatePrice = this.string2BigDecimal(estimatePrice);
    }


    public String getMaxSalePriceString() {
        return this.bigDecimal2String(this.maxSalePrice);
    }


    public void setMaxSalePriceString(String maxSalePrice) {
        this.maxSalePrice = this.string2BigDecimal(maxSalePrice);
    }


}
