package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/07 18:22
 */
@Data
@ToString
@TypeDoc(description = "商城端查询评论入参")
public class ApiMallQueryProductCommentReq extends BasePageReq {

    @FieldDoc(description = "商品id", requiredness = Requiredness.REQUIRED)
    private Long productId;

    @FieldDoc(description = "0-全部 1-好评 2-中评 3-差评 4-有图 5-追加评论", requiredness = Requiredness.REQUIRED)
    private Integer statusCode;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(productId == null || productId <= 0, "商品id不能为空");
        AssertUtil.throwIfTrue(statusCode == null, "评论状态不能为空");
    }


}
