package com.sankuai.sgb2b.seashop.mall.api.thrift.response.product;

import java.math.BigDecimal;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/16 13:56
 */
@TypeDoc(
        description = "商品分页信息"
)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ApiProductPageResp extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "商品图片")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "最小销售价")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "是否有sku")
    private Boolean hasSku;

    @FieldDoc(description = "商品状态 1-销售中 2-仓库中 3-违规下架 4-草稿箱 5-待审核 6-未通过")
    private String status;

    @FieldDoc(description = "库存")
    private Long stock;

    @FieldDoc(description = "skuId")
    private String skuId;

    @FieldDoc(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "广告词")
    private String shortDescription;

    @Schema(description = "oeCode")
    private String oeCode;


    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


    public String getMarketPriceString() {
        return this.bigDecimal2String(this.marketPrice);
    }


    public void setMarketPriceString(String marketPrice) {
        this.marketPrice = this.string2BigDecimal(marketPrice);
    }
}
