package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/04/19 14:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "根据id查询限时购详情")
public class ApiFlashSaleQueryByIdReq extends BaseParamReq {

    @FieldDoc(description = "限时购id列表", requiredness = Requiredness.REQUIRED)
    private List<Long> flashSaleIds;

    @Override
    public void checkParameter() {
        /*AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isNotEmpty(flashSaleIds) && flashSaleIds.size() > 200, "限时购id列表长度不能超过200");*/
    }


}
