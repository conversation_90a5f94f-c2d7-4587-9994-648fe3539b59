package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 14:14
 */
@Data
@TypeDoc(description = "小程序入参")
public class ApiCmdWxAppletFormDataReq extends BaseThriftDto {

    @FieldDoc(description = "事件ID取messageType")
    private Long eventId;

    @FieldDoc(description = "事件值取订单ID")
    private String eventValue;

    @FieldDoc(description = "事件的表单ID取模板ID")
    private String formId;

    @FieldDoc(description = "事件时间")
    private Date eventTime;

    @FieldDoc(description = "FormId过期时间")
    private Date expireTime;


    public Long getEventTimeLong() {
        return this.date2Long(this.eventTime);
    }


    public void setEventTimeLong(Long eventTime) {
        this.eventTime = this.long2Date(eventTime);
    }


    public Long getExpireTimeLong() {
        return this.date2Long(this.expireTime);
    }


    public void setExpireTimeLong(Long expireTime) {
        this.expireTime = this.long2Date(expireTime);
    }

    public void checkParameter() {
        AssertUtil.throwIfNull(eventId, "eventId不能为空");
        AssertUtil.throwIfNull(eventValue, "eventValue不能为空");
        AssertUtil.throwIfNull(formId, "formId不能为空");
    }
}
