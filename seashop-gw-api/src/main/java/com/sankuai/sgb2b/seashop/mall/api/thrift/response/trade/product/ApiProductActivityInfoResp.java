package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "活动信息返回值")
public class ApiProductActivityInfoResp extends BaseThriftDto {

    @FieldDoc(description = "是否参加了限时购")
    private Boolean hasFlashSale;

    @FieldDoc(description = "是否参加了专享价")
    private boolean hasExclusivePrice;

    @FieldDoc(description = "是否参加了阶梯价格")
    private boolean hasLadderPrice;

    @FieldDoc(description = "是否参加了组合购")
    private boolean hasCollectionBuy;

    @FieldDoc(description = "是否参加了折扣")
    private boolean hasDiscountActive;

    @FieldDoc(description = "是否参加了满减")
    private boolean hasFullReduction;

    @FieldDoc(description = "是否有优惠券")
    private boolean hasCoupon;

    @FieldDoc(description = "限时购规则")
    private ApiProductFlashSaleResp flashSaleRule;

    @FieldDoc(description = "阶梯价规则")
    private List<ApiProductLadderPriceResp> ladderPriceRuleList;

    @FieldDoc(description = "折扣规则")
    private List<ApiProductDiscountRuleResp> discountRuleList;

    @FieldDoc(description = "满减规则")
    private ApiProductReductionRuleResp fullReductionRule;

    @FieldDoc(description = "优惠券列表")
    private List<ApiProductCouponResp> couponList;

    @FieldDoc(description = "组合购列表")
    private List<ApiCollectionBuyResp> collectionBuyList;


    public boolean isHasExclusivePrice() {
        return hasExclusivePrice;
    }


    public boolean isHasLadderPrice() {
        return hasLadderPrice;
    }


    public boolean isHasCollectionBuy() {
        return hasCollectionBuy;
    }


    public boolean isHasDiscountActive() {
        return hasDiscountActive;
    }


    public boolean isHasFullReduction() {
        return hasFullReduction;
    }


    public boolean isHasCoupon() {
        return hasCoupon;
    }


}
