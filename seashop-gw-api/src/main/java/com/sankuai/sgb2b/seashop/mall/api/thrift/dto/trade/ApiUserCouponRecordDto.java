package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "用户优惠券")
@ToString
@Data
public class ApiUserCouponRecordDto extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "优惠券活动ID")
    private Long couponId;

    @FieldDoc(description = "优惠券名称")
    private String couponName;

    @FieldDoc(description = "优惠券优惠码")
    private String couponSn;

    @FieldDoc(description = "订单金额（满足多少钱才能使用）")
    private Long orderAmount;

    @FieldDoc(description = "面值(价格)")
    private Long price;

    @FieldDoc(description = "优惠券领用时间")
    private Date couponTime;

    @FieldDoc(description = "用户名称")
    private String userName;

    @FieldDoc(description = "用户ID")
    private Long userId;

    @FieldDoc(description = "优惠券使用时间")
    private Date usedTime;

    @FieldDoc(description = "订单ID")
    private String orderId;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "优惠券状态 0-未使用 1-已使用 2-已过期")
    private Integer couponStatus;

    @FieldDoc(description = "优惠券状态名称")
    private String couponStatusDesc;

    @FieldDoc(description = "优惠券创建时间")
    private Date createTime;

    @FieldDoc(description = "优惠券开始时间")
    private Date startTime;

    @FieldDoc(description = "优惠券结束时间")
    private Date endTime;

    @FieldDoc(description = "使用范围：0=全场通用，1=部分商品可用")
    private Integer useArea;

    @FieldDoc(description = "商品ID列表(部分商品可用时使用)")
    private List<Long> productIdList;

    @FieldDoc(description = "备注")
    private String remark;


    public Long getCouponTimeLong() {
        return this.date2Long(this.couponTime);
    }


    public void setCouponTimeLong(Long couponTime) {
        this.couponTime = this.long2Date(couponTime);
    }


    public Long getUsedTimeLong() {
        return this.date2Long(this.usedTime);
    }


    public void setUsedTimeLong(Long usedTime) {
        this.usedTime = this.long2Date(usedTime);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
