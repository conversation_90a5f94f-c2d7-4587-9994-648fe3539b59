package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/04 16:24
 */
@Data
@ToString
@TypeDoc(description = "商品评价返回值对象")
public class ApiProductCommentResp extends BaseThriftDto {

    @FieldDoc(description = "商品评价id")
    private String productCommentId;

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "商品规格名称")
    private String skuName;

    @FieldDoc(description = "商品缩略图")
    @JsonUrlFormat(deserializer = false)
    private String thumbnailsUrl;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "用户id")
    private Long userId;

    @FieldDoc(description = "用户名称(商家账号)")
    private String userName;

    @FieldDoc(description = "用户手机号(商家手机号)")
    private String userMobile;

    @FieldDoc(description = "评价内容")
    private String reviewContent;

    @FieldDoc(description = "评价日期")
    private Date reviewDate;

    @FieldDoc(description = "回复内容")
    private String replyContent;

    @FieldDoc(description = "回复日期")
    private Date replyDate;

    @FieldDoc(description = "追加评价内容")
    private String appendContent;

    @FieldDoc(description = "追加评价日期")
    private Date appendDate;

    @FieldDoc(description = "追加评论回复")
    private String replyAppendContent;

    @FieldDoc(description = "追加评论回复时间")
    private Date replyAppendDate;

    @FieldDoc(description = "是否回复")
    private Boolean hasReply;

    @FieldDoc(description = "是否隐藏")
    private Boolean hasHidden;

    @FieldDoc(description = "评价分数")
    private Integer reviewMark;

    @FieldDoc(description = "订单id")
    private String orderId;

    @FieldDoc(description = "规格1 别名")
    private String spec1Alias;

    @FieldDoc(description = "规格2 别名")
    private String spec2Alias;

    @FieldDoc(description = "规格3 别名")
    private String spec3Alias;

    @FieldDoc(description = "规格1 值")
    private String spec1Value;

    @FieldDoc(description = "规格2 值")
    private String spec2Value;

    @FieldDoc(description = "规格3 值")
    private String spec3Value;

    @FieldDoc(description = "首次评价图片列表")
    private List<String> commentImageList;

    @FieldDoc(description = "追评图片列表")
    private List<String> appendImageList;

    @FieldDoc(description = "用户头像")
    private String userAvatar;

    @FieldDoc(description = "用户昵称")
    private String userNickName;


    public Long getReviewDateLong() {
        return this.date2Long(this.reviewDate);
    }


    public void setReviewDateLong(Long reviewDate) {
        this.reviewDate = this.long2Date(reviewDate);
    }


    public Long getReplyDateLong() {
        return this.date2Long(this.replyDate);
    }


    public void setReplyDateLong(Long replyDate) {
        this.replyDate = this.long2Date(replyDate);
    }


    public Long getAppendDateLong() {
        return this.date2Long(this.appendDate);
    }


    public void setAppendDateLong(Long appendDate) {
        this.appendDate = this.long2Date(appendDate);
    }


    public Long getReplyAppendDateLong() {
        return this.date2Long(this.replyAppendDate);
    }


    public void setReplyAppendDateLong(Long replyAppendDate) {
        this.replyAppendDate = this.long2Date(replyAppendDate);
    }


}
