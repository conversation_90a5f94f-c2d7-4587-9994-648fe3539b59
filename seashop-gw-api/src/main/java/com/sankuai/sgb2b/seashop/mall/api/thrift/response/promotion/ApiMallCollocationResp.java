package com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 10:41
 */
@Data
@TypeDoc(description = "组合购查询列表返参")
@ToString
public class ApiMallCollocationResp extends BaseThriftDto {

    @FieldDoc(description = "组合购查询列表返参集合")
    private List<ApiCollocationResp> collocationRespList;


}
