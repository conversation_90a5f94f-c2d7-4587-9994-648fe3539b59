package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

@TypeDoc(description = "协议对象")
@Data
public class ApiBaseAgreementRes extends BaseThriftDto {
    private Long id;

    @FieldDoc(description = "协议类型 0买家注册协议，1卖家入驻协议 ,2APP关于我们,3隐私政策,4卖家入驻协议文件")
    private int agreementType;
    @FieldDoc(description = "最后修改时间")
    private Date lastUpdateTime;

    @FieldDoc(description = "协议内容")
    private String agreementContent;


    public Long getLastUpdateTimeLong() {
        return this.date2Long(this.lastUpdateTime);
    }


    public void setLastUpdateTimeLong(Long lastUpdateTime) {
        this.lastUpdateTime = this.long2Date(lastUpdateTime);
    }


}
