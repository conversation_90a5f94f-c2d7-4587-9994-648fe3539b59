package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * 无论什么角色
 *
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "用户信息对象")
public class ApiUserDto extends BaseParamReq {

    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "用户名称", requiredness = Requiredness.REQUIRED)
    private String userName;
    @FieldDoc(description = "手机号码", requiredness = Requiredness.REQUIRED)
    private String userPhone;

    @Override
    public void checkParameter() {
        if (userId == null || userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (userName == null || userName.isEmpty()) {
            throw new InvalidParamException("userName不能为空");
        }
    }


}
