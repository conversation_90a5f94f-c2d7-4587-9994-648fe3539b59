package com.sankuai.sgb2b.seashop.mall.api.thrift.request.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家查询售后记录请求参数")
public class ApiUserQueryRefundReq extends BasePageReq {

    @FieldDoc(description = "用户信息", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "搜索关键字")
    private String searchKey;
    @FieldDoc(description = "商品名称或商品ID")
    private String productNameOrId;
    @FieldDoc(description = "订单号")
    private String orderId;
    @FieldDoc(description = "退款单号")
    private Long refundId;
    @FieldDoc(description = "申请开始时间")
    private Date applyTimeStart;
    @FieldDoc(description = "申请结束时间")
    private Date applyTimeEnd;
    @FieldDoc(description = "退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；6：待平台确认；7：退款成功；8：平台驳回；9：退款中；-1：买家取消售后申请")
    private Integer refundStatus;
    @FieldDoc(description = "退款状态集合")
    private List<Integer> refundStatusList;
    @FieldDoc(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private Integer orderStatus;
    @FieldDoc(description = "查询类型。1：我申请的退款；2：我申请的退货", requiredness = Requiredness.REQUIRED)
    private Integer tab;

    @Override
    public void checkParameter() {
        if (this.userId == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        if (tab == null) {
            throw new InvalidParamException("查询类型不能为空");
        }
    }


    public Long getApplyTimeStartLong() {
        return this.date2Long(this.applyTimeStart);
    }


    public void setApplyTimeStartLong(Long applyTimeStart) {
        this.applyTimeStart = this.long2Date(applyTimeStart);
    }


    public Long getApplyTimeEndLong() {
        return this.date2Long(this.applyTimeEnd);
    }


    public void setApplyTimeEndLong(Long applyTimeEnd) {
        this.applyTimeEnd = this.long2Date(applyTimeEnd);
    }


}
