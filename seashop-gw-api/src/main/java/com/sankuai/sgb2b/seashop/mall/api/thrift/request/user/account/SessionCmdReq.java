package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@Data
@TypeDoc(description = "会话命令请求")
@NoArgsConstructor
@AllArgsConstructor
public class SessionCmdReq {
    @FieldDoc(description = "appKey")
    private String appKey;
    @FieldDoc(description = "name")
    private String name;
    @FieldDoc(description = "avatar")
    private String avatar;


}
