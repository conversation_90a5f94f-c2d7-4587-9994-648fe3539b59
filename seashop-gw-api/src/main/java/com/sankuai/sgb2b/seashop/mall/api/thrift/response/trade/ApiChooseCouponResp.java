package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopProductDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "选择优惠券时的返回")
@ToString
@Data
public class ApiChooseCouponResp extends BaseThriftDto {

    @FieldDoc(description = "当前店铺及商品信息")
    private ApiShoppingCartShopProductDto shopProduct;
    @FieldDoc(description = "预览订单页所有店铺以及对应商品总金额")
    private BigDecimal totalAmount;
    @FieldDoc(description = "限时购活动id。如果不为空，代表是限时购，商品列表只会有一条记录")
    private Long flashSaleId;


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


}
