package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * 评价汇总数据
 *
 * <AUTHOR>
 * @date 2023/12/25 11:01
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "商品评价信息返回值")
public class ApiCommentSummaryResp extends BaseThriftDto {

    @FieldDoc(description = "评价总数")
    private Integer totalCount;

    @FieldDoc(description = "5分评价数")
    private Integer fiveStarCount;

    @FieldDoc(description = "4分评价数")
    private Integer fourStarCount;

    @FieldDoc(description = "3分评价数")
    private Integer threeStarCount;

    @FieldDoc(description = "2分评价数")
    private Integer twoStarCount;

    @FieldDoc(description = "1分评价数")
    private Integer oneStarCount;

    @FieldDoc(description = "有图评价数")
    private Integer hasImageCount;

    @FieldDoc(description = "追评数")
    private Integer appendCount;

    @FieldDoc(description = "好评率")
    private BigDecimal goodRate;

    @FieldDoc(description = "中评率")
    private BigDecimal middleRate;

    @FieldDoc(description = "差评率")
    private BigDecimal badRate;

    @FieldDoc(description = "综合评分")
    private BigDecimal score;

    @FieldDoc(description = "好评数")
    private Integer goodsCount;

    @FieldDoc(description = "中评数")
    private Integer middleCount;

    @FieldDoc(description = "差评数")
    private Integer badCount;


    public String getGoodRateString() {
        return this.bigDecimal2String(this.goodRate);
    }


    public void setGoodRateString(String goodRate) {
        this.goodRate = this.string2BigDecimal(goodRate);
    }


    public String getMiddleRateString() {
        return this.bigDecimal2String(this.middleRate);
    }


    public void setMiddleRateString(String middleRate) {
        this.middleRate = this.string2BigDecimal(middleRate);
    }


    public String getBadRateString() {
        return this.bigDecimal2String(this.badRate);
    }


    public void setBadRateString(String badRate) {
        this.badRate = this.string2BigDecimal(badRate);
    }


    public String getScoreString() {
        return this.bigDecimal2String(this.score);
    }


    public void setScoreString(String score) {
        this.score = this.string2BigDecimal(score);
    }


}
