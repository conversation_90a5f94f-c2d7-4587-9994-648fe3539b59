package com.sankuai.sgb2b.seashop.mall.api.thrift.request.refund;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询订单")
public class ApiQueryOrderRefundPreviewReq extends BaseParamReq {

    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "退款ID")
    private Long refundId;

    @Override
    public void checkParameter() {
        if (this.userId == null) {
            throw new InvalidParamException("userId不能为空");
        }
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("orderId不能为空");
        }
    }


}
