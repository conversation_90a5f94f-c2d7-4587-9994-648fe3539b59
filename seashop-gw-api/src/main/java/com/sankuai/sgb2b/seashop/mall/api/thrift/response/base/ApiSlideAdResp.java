package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@TypeDoc(description = "轮播图响应体")
@ToString
@Data
public class ApiSlideAdResp extends BaseThriftDto {

    /**
     * 主键
     */
    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 图片保存URL
     */
    @FieldDoc(description = "图片保存URL")
    @JsonUrlFormat(deserializer = false)
    private String imageUrl;

    /**
     * 图片跳转URL
     */
    @FieldDoc(description = "图片跳转URL")
    @JsonUrlFormat(deserializer = false)
    private String url;

    /**
     * 排序
     */
    @FieldDoc(description = "排序")
    private Long displaySequence;


}
