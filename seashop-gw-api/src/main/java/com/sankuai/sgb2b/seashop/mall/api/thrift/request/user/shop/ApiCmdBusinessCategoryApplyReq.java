package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "个人供应商修改入参")
@Builder
public class ApiCmdBusinessCategoryApplyReq extends BaseParamReq {
    @FieldDoc(description = "店铺ID")
    @NotNull(message = "店铺ID为必填项")
    private Long shopId;
    @FieldDoc(description = "申请记录ID")
    @NotNull(message = "申请记录ID为必填项")
    private Long applyId;
    @FieldDoc(description = "状态")
    @NotNull(message = "状态为必填项")
    private Integer status;
    @FieldDoc(description = "拒绝原因")
    private String refuseReason;


}
