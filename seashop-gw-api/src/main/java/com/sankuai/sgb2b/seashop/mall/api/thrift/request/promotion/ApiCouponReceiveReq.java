package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "优惠券领用请求对象")
public class ApiCouponReceiveReq extends BaseParamReq {

    @FieldDoc(description = "优惠券ID", requiredness = Requiredness.REQUIRED)
    private Long couponId;

    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;

    @FieldDoc(description = "用户名", requiredness = Requiredness.REQUIRED)
    private String userName;

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @FieldDoc(description = "名称", requiredness = Requiredness.REQUIRED)
    private String shopName;


}
