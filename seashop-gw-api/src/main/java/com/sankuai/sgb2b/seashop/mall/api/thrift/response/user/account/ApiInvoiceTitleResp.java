package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@Data
@TypeDoc(description = "发票抬头")
@NoArgsConstructor
@AllArgsConstructor
public class ApiInvoiceTitleResp {

    @FieldDoc(description = "发票抬头ID")
    private Long id;
    @FieldDoc(description = "用户ID")
    private Long userId;
    @FieldDoc(description = "发票类型（1:普通发票、2:电子发票、3:增值税发票）")
    private Integer invoiceType;
    @FieldDoc(description = "抬头名称")
    private String name;
    @FieldDoc(description = "税号")
    private String code;
    @FieldDoc(description = "发票明细")
    private String invoiceContext;
    @FieldDoc(description = "注册地址")
    private String registerAddress;
    @FieldDoc(description = "注册电话")
    private String registerPhone;
    @FieldDoc(description = "开户银行")
    private String bankName;
    @FieldDoc(description = "银行帐号")
    private String bankNo;
    @FieldDoc(description = "收票人姓名")
    private String realName;
    @FieldDoc(description = "收票人手机号")
    private String cellPhone;
    @FieldDoc(description = "收票人邮箱")
    private String email;
    @FieldDoc(description = "收票人地址区域ID")
    private Integer regionId;
    @FieldDoc(description = "收票人详细地址")
    private String address;
    @FieldDoc(description = "是否默认")
    private Boolean whetherDefault;
    @FieldDoc(description = "发票主体类型（1:个人、2:公司）")
    private Integer invoiceSubjectType;


}
