package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "店铺发票配置")
@ToString
@Data
public class ApiShopInvoiceConfigDto extends BaseThriftDto {

    /**
     * 是否提供发票
     */
    @FieldDoc(description = "是否提供发票")
    private Boolean whetherInvoice;
    /**
     * 是否提供普通发票
     */
    @FieldDoc(description = "是否提供普通发票")
    private Boolean whetherPlainInvoice;
    /**
     * 是否提供电子发票
     */
    @FieldDoc(description = "是否提供电子发票")
    private Boolean whetherElectronicInvoice;
    /**
     * 是否提供增值税发票
     */
    @FieldDoc(description = "是否提供增值税发票")
    private Boolean whetherVatInvoice;
    /**
     * 普通发票税率
     */
    @FieldDoc(description = "普通发票税率")
    private BigDecimal plainInvoiceRate;
    /**
     * 增值税税率
     */
    @FieldDoc(description = "增值税税率")
    private BigDecimal vatInvoiceRate;
    /**
     * 订单完成后多少天开具增值税发票
     */
    @FieldDoc(description = "订单完成后多少天开具增值税发票")
    private Integer vatInvoiceDay;
    /**
     * 售后维权期天数
     */
    @FieldDoc(description = "售后维权期天数")
    private Integer warrantyDays;


    public String getPlainInvoiceRateString() {
        return this.bigDecimal2String(this.plainInvoiceRate);
    }


    public void setPlainInvoiceRateString(String plainInvoiceRate) {
        this.plainInvoiceRate = this.string2BigDecimal(plainInvoiceRate);
    }


    public String getVatInvoiceRateString() {
        return this.bigDecimal2String(this.vatInvoiceRate);
    }


    public void setVatInvoiceRateString(String vatInvoiceRate) {
        this.vatInvoiceRate = this.string2BigDecimal(vatInvoiceRate);
    }


}
