package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商入驻请求入参")
public class ApiCmdShopStepsThreeReq extends BaseParamReq {
    @FieldDoc(description = "id")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    @NotBlank(message = "店铺名称不能为空")
    private String shopName;
    @FieldDoc(description = "店铺等级")
    private Integer shopGrade;
    @FieldDoc(description = "选择类目")
    @Size(min = 1, message = "至少选择一个类目")
    @NotNull(message = "至少选择一个类目")
    private List<Long> categories;
    @FieldDoc(description = "所有关联自定义表单的分类数据")
    private List<ApiCmdBusinessCategoryFormReq> customFormCategory;
    @FieldDoc(description = "店铺logo")
    @JsonUrlFormat(deserializer = false)
    private String formImage;
    @FieldDoc(description = "店铺banner")
    private String formFile;


}
