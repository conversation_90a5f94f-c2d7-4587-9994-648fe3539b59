package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import java.math.BigDecimal;
import java.util.Date;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import lombok.Data;
import lombok.ToString;

/**
 * @author： liweisong
 * @create： 2023/11/28 10:31
 */
@Data
@ToString
@TypeDoc(description = "商品收藏列表")
public class ApiQueryFavoriteProductResp extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "用户ID")
    private Long userId;

    @FieldDoc(description = "商品ID")
    private String productId;

    @FieldDoc(description = "收藏日期")
    private Date date;

    @FieldDoc(description = "市场价")
    private BigDecimal marketPrice;

    @FieldDoc(description = "审核状态 1-审核中 2-审核通过 3-审核不通过 4-违规下架")
    private Integer auditStatus;

    @FieldDoc(description = "审核状态")
    private String auditStatusName;

    @FieldDoc(description = "销售状态")
    private Integer saleStatus;

    @FieldDoc(description = "销售状态名称")
    private String saleStatusName;

    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String mainImagePath;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "评价总数")
    private Integer totalCount;

    @FieldDoc(description = "最少销售价")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "是否下架 0-未下架 1-已下架")
    private Integer offShelf;

    @FieldDoc(description = "是否有库存 0-无库存 1-有库存")
    private Integer hasStock;

    @FieldDoc(description = "库存状态")
    private Integer stockStatus;

    @FieldDoc(description = "库存状态 0-已售罄 1-已下架，2审核通过现货")
    private String stockStatusName;

    @FieldDoc(description = "是否单价格商品")
    private Boolean singlePrice;


    public Long getDateLong() {
        return this.date2Long(this.date);
    }


    public void setDateLong(Long date) {
        this.date = this.long2Date(date);
    }


    public String getMarketPriceString() {
        return this.bigDecimal2String(this.marketPrice);
    }


    public void setMarketPriceString(String marketPrice) {
        this.marketPrice = this.string2BigDecimal(marketPrice);
    }


    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


}
