package com.sankuai.sgb2b.seashop.mall.api.thrift.request.enums;

/**
 * <AUTHOR>
 * @date 2024/01/02 18:23
 */
public class CommonConstant {

    /**
     * 查询限制
     */
    public static final Integer QUERY_LIMIT = 200;

    /**
     * 是
     */
    public static String YES_STR = "是";

    /**
     * 否
     */
    public static String NO_STR = "否";

    /**
     * 平台默认ID=0L
     */
    public static final Long PLATFORM_ID = 0L;

    /**
     * 供应商通知分类id
     */
    public static final Long SELLER_ARTICLE_CID = 4L;

    /**
     * 默认的数量值0L
     */
    public static final Long DEFAULT_COUNT = 0L;

    /**
     * 导出查询最大数量
     */
    public static final Integer EXPORT_MAX_COUNT = 5000;

    /**
     * 初始页码
     */
    public static final Integer INIT_PAGE_NO = 1;

    /**
     * 循环开始页码
     */
    public static final Integer LOOP_START_PAGE_NO = 2;

    public static final Long FOOTER_ARCTIC_CID = 1L;
}
