package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.group.AddCompanyGroup;
import com.sankuai.shangou.seashop.user.thrift.shop.group.AddPersonGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商入驻请求入参")
public class ApiCmdShopStepsOneReq extends BaseParamReq {

    @FieldDoc(description = "id", requiredness = Requiredness.REQUIRED)
    private Long shopId;
    //    公司信息
    @FieldDoc(description = "公司名称", requiredness = Requiredness.REQUIRED)
    @NotBlank(message = "公司名称不能为空", groups = {AddCompanyGroup.class})
    @Size(max = 60, message = "公司名称不能超过60个字符")
    private String companyName;
    @FieldDoc(description = "公司所在地", requiredness = Requiredness.REQUIRED)
    @NotNull(message = "公司所在地不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private Integer companyRegionId;
    @FieldDoc(description = "公司详细地址", requiredness = Requiredness.REQUIRED)
    @NotBlank(message = "公司详细地址不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    @Size(max = 100, message = "公司所在地不能超过100个字符", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String companyAddress;
    //    身份证信息
    @FieldDoc(description = "姓名", requiredness = Requiredness.REQUIRED)
    @Size(max = 10, min = 2, message = "姓名长度在2-10之间", groups = {AddPersonGroup.class})
    private String contactName;
    @FieldDoc(description = "公司法定代表人")
    @Size(max = 10, min = 2, message = "法人名称长度在2-10之间", groups = {AddCompanyGroup.class})
    private String legalPerson;
    @FieldDoc(description = "法人联系方式")
    @Size(max = 11, min = 11, message = "法人手机号不符合格式", groups = {AddCompanyGroup.class})
    private String contactsPhone;
    @FieldDoc(description = "身份证号", requiredness = Requiredness.REQUIRED)
    @NotBlank(message = "身份证号不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    @Size(max = 18, min = 18, message = "身份证号不符合格式", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String idCard;

    @FieldDoc(description = "身份证有效期类型，1:永久有效，2：时间段")
    @NotNull(message = "请选择有效期", groups = {AddCompanyGroup.class})
    private Integer idCardExpireType;
    @FieldDoc(description = "身份证有效期开始日期")
    @NotNull(message = "身份证有效期开始日期不能为空", groups = {AddCompanyGroup.class})
    private Date idCardStartDate;
    @FieldDoc(description = "身份证有效期结束日期")
    @NotNull(message = "身份证有效期结束日期不能为空", groups = {AddCompanyGroup.class})
    private Date idCardEndDate;
    @FieldDoc(description = "身份证正面照", requiredness = Requiredness.REQUIRED)
    @NotBlank(message = "身份证正面照不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String idCardUrl;
    @FieldDoc(description = "身份证反面照", requiredness = Requiredness.REQUIRED)
    @NotBlank(message = "身份证反面照不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String idCardUrl2;
    //    营业执照信息
    @FieldDoc(description = "营业执照号")
    @Size(max = 1000, message = "营业执照号不能超过100个字符", groups = {AddCompanyGroup.class})
    private String businessLicenseNumber;
    @FieldDoc(description = "营业执照所在地")
//    @NotNull(message = "营业执照所在地不能为空", groups = {AddCompanyGroup.class})
    private Integer businessLicenseArea;
    @FieldDoc(description = "营业执照起始有效期")
    @NotNull(message = "营业执照起始有效期不能为空", groups = {AddCompanyGroup.class})
    private Date businessLicenseStart;
    @FieldDoc(description = "营业执照截止有效期")
    @NotNull(message = "营业执照截止有效期不能为空", groups = {AddCompanyGroup.class})
    private Date businessLicenseEnd;
    @FieldDoc(description = "法定经营范围")
    @Size(max = 200, message = "法定经营范围不能超过200个字符", groups = {AddCompanyGroup.class})
    private String businessSphere;
    @FieldDoc(description = "营业执照号电子版")
    @NotBlank(message = "营业执照号电子版不能为空", groups = {AddCompanyGroup.class})
    private String businessLicenseNumberPhoto;
    @FieldDoc(description = "开户银行")
    @NotBlank(message = "营业执照号电子版不能为空", groups = {AddCompanyGroup.class})
    private String bankPhoto;
    //    其他信息
    @FieldDoc(description = "自定义数据")
    private String formData;
    //管理员信息
    @FieldDoc(description = "管理员姓名")
    @Size(max = 10, min = 2, message = "管理员姓名长度在2-10之间", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String realName;
    @FieldDoc(description = "管理员手机")
    private String memberPhone;
    @FieldDoc(description = "手机验证码")
    private String phoneCode;
    @FieldDoc(description = "管理员邮箱")
    private String memberEmail;
    @FieldDoc(description = "邮箱验证码")
    private String emailCode;


    public Long getIdCardStartDateLong() {
        return this.date2Long(this.idCardStartDate);
    }


    public void setIdCardStartDateLong(Long idCardStartDate) {
        this.idCardStartDate = this.long2Date(idCardStartDate);
    }


    public Long getIdCardEndDateLong() {
        return this.date2Long(this.idCardEndDate);
    }


    public void setIdCardEndDateLong(Long idCardEndDate) {
        this.idCardEndDate = this.long2Date(idCardEndDate);
    }


    public Long getBusinessLicenseStartLong() {
        return this.date2Long(this.businessLicenseStart);
    }


    public void setBusinessLicenseStartLong(Long businessLicenseStart) {
        this.businessLicenseStart = this.long2Date(businessLicenseStart);
    }


    public Long getBusinessLicenseEndLong() {
        return this.date2Long(this.businessLicenseEnd);
    }


    public void setBusinessLicenseEndLong(Long businessLicenseEnd) {
        this.businessLicenseEnd = this.long2Date(businessLicenseEnd);
    }


}