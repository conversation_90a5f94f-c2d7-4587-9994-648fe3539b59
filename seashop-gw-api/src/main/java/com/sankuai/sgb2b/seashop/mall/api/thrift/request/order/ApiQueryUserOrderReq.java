package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家搜索订单入参")
public class ApiQueryUserOrderReq extends BasePageReq {

    /**
     * 搜索关键字，支持 商品名称，订单编号，规格ID，商品ID
     */
    @FieldDoc(description = "搜索关键字，支持 商品名称，订单编号，规格ID，商品ID")
    private String searchKey;
    /**
     * 订单状态
     */
    @FieldDoc(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private Integer orderStatus;
    /**
     * 下单时间-开始
     */
    @FieldDoc(description = "下单时间-开始")
    private Date orderStartTime;
    /**
     * 下单时间-结束
     */
    @FieldDoc(description = "下单时间-结束")
    private Date orderEndTime;
    /**
     * 完成时间-开始
     */
    @FieldDoc(description = "完成时间-开始")
    private Date finishStartTime;
    /**
     * 完成时间-结束
     */
    @FieldDoc(description = "完成时间-结束")
    private Date finishEndTime;
    /**
     * 是否查询待评价订单
     */
    @FieldDoc(description = "是否查询待评价订单")
    private Boolean queryUnCommented;

    @FieldDoc(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private List<Integer> orderStatusList;
    @FieldDoc(description = "查询来源。1-商家小程序；2-商家PC端；3-卖家PC端；4-平台PC端")
    private OrderQueryFromEnum queryFrom;

    @Override
    public void checkParameter() {
        super.checkParameter();
    }


    public Long getOrderStartTimeLong() {
        return this.date2Long(this.orderStartTime);
    }


    public void setOrderStartTimeLong(Long orderStartTime) {
        this.orderStartTime = this.long2Date(orderStartTime);
    }


    public Long getOrderEndTimeLong() {
        return this.date2Long(this.orderEndTime);
    }


    public void setOrderEndTimeLong(Long orderEndTime) {
        this.orderEndTime = this.long2Date(orderEndTime);
    }


    public Long getFinishStartTimeLong() {
        return this.date2Long(this.finishStartTime);
    }


    public void setFinishStartTimeLong(Long finishStartTime) {
        this.finishStartTime = this.long2Date(finishStartTime);
    }


    public Long getFinishEndTimeLong() {
        return this.date2Long(this.finishEndTime);
    }


    public void setFinishEndTimeLong(Long finishEndTime) {
        this.finishEndTime = this.long2Date(finishEndTime);
    }


}
