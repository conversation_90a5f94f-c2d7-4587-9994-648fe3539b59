package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/04 16:24
 */
@Data
@ToString
@TypeDoc(description = "商品评价详情对象")
public class ApiProductCommentDetailResp extends BaseThriftDto {

    @FieldDoc(description = "商品评价id")
    private String productCommentId;

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "商品缩略图")
    @JsonUrlFormat(deserializer = false)
    private String thumbnailsUrl;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "用户id")
    private Long userId;

    @FieldDoc(description = "用户名称(商家账号)")
    private String userName;

    @FieldDoc(description = "用户手机号(商家手机号)")
    private String userMobile;

    @FieldDoc(description = "首次评价内容")
    private String reviewContent;

    @FieldDoc(description = "首次评价日期")
    private Date reviewDate;

    @FieldDoc(description = "首次回复内容")
    private String replyContent;

    @FieldDoc(description = "首次回复日期")
    private Date replyDate;

    @FieldDoc(description = "追加评价内容")
    private String appendContent;

    @FieldDoc(description = "追加评价日期")
    private Date appendDate;

    @FieldDoc(description = "追加评论回复")
    private String replyAppendContent;

    @FieldDoc(description = "追加评论回复时间")
    private Date replyAppendDate;

    @FieldDoc(description = "风控状态 0-待审核 1-审核通过 2-审核拒绝")
    private Integer riskStatus;

    @FieldDoc(description = "风控状态描述")
    private String riskStatusDesc;

    @FieldDoc(description = "风控不通过原因")
    private String riskMsg;

    @FieldDoc(description = "首次评价图片列表")
    private List<ApiProductCommentImageResp> firstCommentImageList;

    @FieldDoc(description = "追评风控状态 0-待审核 1-审核通过 2-审核拒绝")
    private Integer appendRiskStatus;

    @FieldDoc(description = "追评风控状态描述")
    private String appendRiskStatusDesc;

    @FieldDoc(description = "追评风控不通过原因")
    private String appendRiskMsg;

    @FieldDoc(description = "追评图片列表")
    private List<ApiProductCommentImageResp> appendCommentImageList;

    @FieldDoc(description = "购买时间")
    private Date buyDate;

    @FieldDoc(description = "是否回复")
    private Boolean hasReply;

    @FieldDoc(description = "是否隐藏")
    private Boolean hasHidden;

    @FieldDoc(description = "评分")
    private Integer reviewMark;

    @FieldDoc(description = "子订单id")
    private Long subOrderId;


    public Long getReviewDateLong() {
        return this.date2Long(this.reviewDate);
    }


    public void setReviewDateLong(Long reviewDate) {
        this.reviewDate = this.long2Date(reviewDate);
    }


    public Long getReplyDateLong() {
        return this.date2Long(this.replyDate);
    }


    public void setReplyDateLong(Long replyDate) {
        this.replyDate = this.long2Date(replyDate);
    }


    public Long getAppendDateLong() {
        return this.date2Long(this.appendDate);
    }


    public void setAppendDateLong(Long appendDate) {
        this.appendDate = this.long2Date(appendDate);
    }


    public Long getReplyAppendDateLong() {
        return this.date2Long(this.replyAppendDate);
    }


    public void setReplyAppendDateLong(Long replyAppendDate) {
        this.replyAppendDate = this.long2Date(replyAppendDate);
    }


    public Long getBuyDateLong() {
        return this.date2Long(this.buyDate);
    }


    public void setBuyDateLong(Long buyDate) {
        this.buyDate = this.long2Date(buyDate);
    }


}
