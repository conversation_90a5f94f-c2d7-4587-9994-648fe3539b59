package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
@Data
@ToString
@TypeDoc(description = "迁移数据登录请求")
public class ApiTransferAccountReq {

    @FieldDoc(description = "用户名", requiredness = Requiredness.OPTIONAL)
    @NotBlank(message = "用户名不能为空")
    private String userName;

    @FieldDoc(description = "密码", requiredness = Requiredness.OPTIONAL)
    @NotBlank(message = "密码不能为空")
    private String password;

}
