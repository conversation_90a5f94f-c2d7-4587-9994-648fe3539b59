package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/22 15:19
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "分类信息返回值")
public class ApiProductCategoryResp extends BaseThriftDto {

    @FieldDoc(description = "商品分类id")
    private Long categoryId;

    @FieldDoc(description = "商品分类名称")
    private String categoryName;


}
