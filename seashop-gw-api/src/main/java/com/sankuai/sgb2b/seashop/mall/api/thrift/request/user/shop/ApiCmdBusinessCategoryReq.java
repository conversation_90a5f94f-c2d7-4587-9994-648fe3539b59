package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description: 店铺类目修改请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "店铺类目修改请求入参")
public class ApiCmdBusinessCategoryReq extends BaseParamReq {
    @FieldDoc(description = "店铺类目ID")
    public Long id;
    @FieldDoc(description = "分拥比例")
    private BigDecimal commisRate;
    @FieldDoc(description = "保证金")
    private BigDecimal bond;


    public String getCommisRateString() {
        return this.bigDecimal2String(this.commisRate);
    }


    public void setCommisRateString(String commisRate) {
        this.commisRate = this.string2BigDecimal(commisRate);
    }


    public String getBondString() {
        return this.bigDecimal2String(this.bond);
    }


    public void setBondString(String bond) {
        this.bond = this.string2BigDecimal(bond);
    }
}
