package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.ValidationUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "修改收货地址请求入参")
public class ApiUpdateShippingAddressReq extends BaseParamReq {

    @FieldDoc(description = "收货地址主键ID", requiredness = Requiredness.REQUIRED)
    private Long id;
    @FieldDoc(description = "收货人", requiredness = Requiredness.REQUIRED)
    private String shipTo;
    @FieldDoc(description = "收货人联系方式", requiredness = Requiredness.REQUIRED)
    private String phone;
    @FieldDoc(description = "区域ID，省市区中的区ID", requiredness = Requiredness.REQUIRED)
    private Integer regionId;
    @FieldDoc(description = "商家用户ID，外观层从用户信息获取", requiredness = Requiredness.REQUIRED)
    private String address;
    @FieldDoc(description = "商家用户ID，外观层从用户信息获取", requiredness = Requiredness.REQUIRED)
    private String addressDetail;
    @FieldDoc(description = "商家用户ID，外观层从用户信息获取", requiredness = Requiredness.REQUIRED)
    private Boolean whetherDefault;

    /**
     * 参数校验
     */
    public void checkParameter() {
        if (StrUtil.isBlank(this.shipTo)) {
            throw new InvalidParamException("收货人不能为空");
        }
        if (shipTo.length() > 20) {
            throw new InvalidParamException("收货人长度不能超过20个字符");
        }
        if (StrUtil.isBlank(this.phone)) {
            throw new InvalidParamException("收货人联系方式不能为空");
        }
        if (!ValidationUtil.isPhone(phone)) {
            throw new InvalidParamException("手机号码格式不符合要求");
        }
        if (regionId == null || regionId <= 0) {
            throw new InvalidParamException("所在地区不能为空");
        }
        if (StrUtil.isBlank(this.address)) {
            throw new InvalidParamException("详细地址不能为空");
        }
        if (address.length() > 50) {
            throw new InvalidParamException("详细地址长度不能超过50个字符");
        }
    }


}
