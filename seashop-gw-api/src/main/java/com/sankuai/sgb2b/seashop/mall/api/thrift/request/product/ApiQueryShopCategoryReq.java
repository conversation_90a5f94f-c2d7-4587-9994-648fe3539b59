package com.sankuai.sgb2b.seashop.mall.api.thrift.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询店铺分类入参")
public class ApiQueryShopCategoryReq extends BaseParamReq {

    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
