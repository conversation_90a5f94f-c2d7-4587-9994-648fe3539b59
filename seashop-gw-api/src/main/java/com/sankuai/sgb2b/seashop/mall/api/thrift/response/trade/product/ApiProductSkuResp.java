package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:46
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "sku返回值")
public class ApiProductSkuResp extends BaseThriftDto {

    @FieldDoc(description = "sku自增id")
    private Long skuAutoId;

    @FieldDoc(description = "skuId")
    private String skuId;

    @FieldDoc(description = "规格1值")
    private String spec1Value;

    @FieldDoc(description = "规格2值")
    private String spec2Value;

    @FieldDoc(description = "规格3值")
    private String spec3Value;

    @FieldDoc(description = "销售价")
    private BigDecimal salePrice;

    @FieldDoc(description = "库存")
    private Long stock;

    @FieldDoc(description = "货号")
    private String skuCode;

    @FieldDoc(description = "限时购价格")
    private BigDecimal flashSalePrice;

    @FieldDoc(description = "限时购库存")
    private Long flashSaleStock;

    @FieldDoc(description = "限时购限购数量")
    private Integer flashSaleLimit;

    @FieldDoc(description = "sku图片")
    @JsonUrlFormat(deserializer = false)
    private String showPic;

    @FieldDoc(description = "计量单位")
    private String measureUnit;

    @FieldDoc(description = "是否是专享价规格")
    private Boolean exclusiveSku;

    @FieldDoc(description = "原始价格")
    private BigDecimal skuPirce;


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


    public String getFlashSalePriceString() {
        return this.bigDecimal2String(this.flashSalePrice);
    }


    public void setFlashSalePriceString(String flashSalePrice) {
        this.flashSalePrice = this.string2BigDecimal(flashSalePrice);
    }


}
