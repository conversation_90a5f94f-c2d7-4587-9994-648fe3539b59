package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "满减规则返回值")
public class ApiProductReductionRuleResp extends BaseThriftDto {

    @FieldDoc(description = "满减活动id")
    private Long activeId;

    @FieldDoc(description = "满减活动名称")
    private String activeName;

    @FieldDoc(description = "满减门槛")
    private BigDecimal moneyOffCondition;

    @FieldDoc(description = "单笔订单满减金额")
    private BigDecimal moneyOffFee;

    @FieldDoc(description = "是否叠加优惠")
    private Boolean moneyOffOverLay;

    @FieldDoc(description = "活动开始时间")
    private Date startTime;

    @FieldDoc(description = "活动结束时间")
    private Date endTime;


    public String getMoneyOffConditionString() {
        return this.bigDecimal2String(this.moneyOffCondition);
    }


    public void setMoneyOffConditionString(String moneyOffCondition) {
        this.moneyOffCondition = this.string2BigDecimal(moneyOffCondition);
    }


    public String getMoneyOffFeeString() {
        return this.bigDecimal2String(this.moneyOffFee);
    }


    public void setMoneyOffFeeString(String moneyOffFee) {
        this.moneyOffFee = this.string2BigDecimal(moneyOffFee);
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }
}
