package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.Data;
import lombok.ToString;

/**
 * @description：添加商品收藏
 * @author： liweisong
 * @create： 2023/11/28 9:30
 */
@Data
@ToString
@TypeDoc(description = "添加商品收藏入参")
public class ApiAddFavoriteProductReq extends BaseThriftDto {

    @FieldDoc(description = "用户ID")
    @ExaminField
    private Long userId;

    @FieldDoc(description = "商品ID")
    @ExaminField
    private String productId;


}
