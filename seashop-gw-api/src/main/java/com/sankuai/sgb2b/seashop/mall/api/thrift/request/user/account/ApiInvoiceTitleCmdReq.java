package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Add;
import com.sankuai.shangou.seashop.user.thrift.shop.group.AddPersonGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @description: 发票抬头添加请求类
 * @author: LXH
 **/
@Data
@TypeDoc(description = "发票抬头")
@NoArgsConstructor
@AllArgsConstructor
public class ApiInvoiceTitleCmdReq extends BaseParamReq {
    @FieldDoc(description = "发票抬头ID 编辑时候传")
    private Long id;
    @FieldDoc(description = "用户ID")
    @NotNull(message = "用户ID不能为空", groups = {Add.class})
    private Long userId;
    @FieldDoc(description = "发票类型（1:普通发票、2:电子发票、3:增值税发票）")
    @NotNull(message = "发票类型不能为空", groups = {Add.class})
    private Integer invoiceType;
    @FieldDoc(description = "发票主体类型（1:个人、2:公司）")
    private Integer invoiceSubjectType;
    @FieldDoc(description = "抬头名称")
    private String name;
    @FieldDoc(description = "税号")
    private String code;
    @FieldDoc(description = "发票明细类型")
    @NotBlank(message = "发票明细类型不能为空", groups = {Add.class})
    private String invoiceContext;
    @FieldDoc(description = "注册地址")
    private String registerAddress;
    @FieldDoc(description = "注册电话")
    private String registerPhone;
    @FieldDoc(description = "开户银行")
    private String bankName;
    @FieldDoc(description = "银行帐号")
    private String bankNo;
    @FieldDoc(description = "收票人姓名")
    private String realName;
    @FieldDoc(description = "收票人手机号")
    @NotBlank(message = "收票人手机号不能为空", groups = {AddPersonGroup.class})
    private String cellPhone;
    @FieldDoc(description = "收票人邮箱")
    @NotBlank(message = "收票人邮箱不能为空", groups = {AddPersonGroup.class})
    private String email;
    @FieldDoc(description = "收票人地址区域ID")
    private Integer regionId;
    @FieldDoc(description = "收票人详细地址")
    private String address;
    @FieldDoc(description = "是否默认")
    private Boolean whetherDefault;


}
