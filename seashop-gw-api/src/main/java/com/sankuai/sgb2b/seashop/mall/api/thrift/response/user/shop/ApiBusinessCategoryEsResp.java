package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "店铺ES经营类目列表响应体")
public class ApiBusinessCategoryEsResp extends BaseThriftDto {

    /**
     * 主键
     */
    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 类目ID
     */
    @FieldDoc(description = "类目ID")
    private Long categoryId;

    /**
     * 类目名称
     */
    @FieldDoc(description = "类目名称")
    private String categoryName;

    /**
     * 类目全名(包含上级)
     */
    @FieldDoc(description = "类目全名(包含上级)")
    private String fullCategoryName;

    /**
     * 是否冻结
     */
    @FieldDoc(description = "是否冻结")
    private Boolean whetherFrozen;


}
