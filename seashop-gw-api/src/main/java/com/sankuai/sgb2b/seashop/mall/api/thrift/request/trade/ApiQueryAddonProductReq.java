package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询凑单商品请求入参")
public class ApiQueryAddonProductReq extends BasePageReq {

    @FieldDoc(description = "用户信息", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "店铺信息")
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表")
    private List<ApiShoppingCartProductDto> productList;
    /**
     * 凑单类型。1：折扣；2：满减
     */
    @FieldDoc(description = "凑单类型。1：折扣；2：满减", requiredness = Requiredness.REQUIRED)
    private Integer type;
    /**
     * 活动ID
     */
    @FieldDoc(description = "活动ID", requiredness = Requiredness.REQUIRED)
    private Long activityId;


}
