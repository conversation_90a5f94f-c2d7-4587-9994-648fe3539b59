package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2024-02-20
 */
@Data
@TypeDoc(description = "银行信息列表")
@NoArgsConstructor
@AllArgsConstructor
public class ApiBanksListResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @FieldDoc(description = "银行id")
    private List<ApiBanksResp> banksList;


}
