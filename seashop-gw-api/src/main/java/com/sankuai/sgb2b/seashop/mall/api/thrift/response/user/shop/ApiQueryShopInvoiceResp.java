package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:55
 */
@Data
@TypeDoc(description = "店铺发票查询返回")
public class ApiQueryShopInvoiceResp extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "是否提供发票")
    private Boolean whetherInvoice;

    @FieldDoc(description = "是否提供普通发票")
    private Boolean whetherPlainInvoice;

    @FieldDoc(description = "是否提供电子发票")
    private Boolean whetherElectronicInvoice;

    @FieldDoc(description = "普通发票税率")
    private BigDecimal plainInvoiceRate;

    @FieldDoc(description = "是否提供增值税发票")
    private Boolean whetherVatInvoice;

    @FieldDoc(description = "订单完成后多少天开具增值税发票")
    private Integer vatInvoiceDay;

    @FieldDoc(description = "增值税税率")
    private BigDecimal vatInvoiceRate;

    @FieldDoc(description = "创建时间")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    private Date updateTime;


    public String getPlainInvoiceRateString() {
        return this.bigDecimal2String(this.plainInvoiceRate);
    }


    public void setPlainInvoiceRateString(String plainInvoiceRate) {
        this.plainInvoiceRate = this.string2BigDecimal(plainInvoiceRate);
    }


    public String getVatInvoiceRateString() {
        return this.bigDecimal2String(this.vatInvoiceRate);
    }


    public void setVatInvoiceRateString(String vatInvoiceRate) {
        this.vatInvoiceRate = this.string2BigDecimal(vatInvoiceRate);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }
}
