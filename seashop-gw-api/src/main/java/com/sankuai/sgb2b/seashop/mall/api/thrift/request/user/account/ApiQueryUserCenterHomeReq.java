package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/12/12 16:03
 */
@Data
@TypeDoc(description = "商家中心首页入参")
public class ApiQueryUserCenterHomeReq {

    @FieldDoc(description = "商家ID")
    private Long userId;

    @FieldDoc(description = "登录账号")
    private String userName;

    @FieldDoc(description = "最近浏览的四个商品ID")
    private List<String> productIds;

    public void checkParameter() {
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
    }


}
