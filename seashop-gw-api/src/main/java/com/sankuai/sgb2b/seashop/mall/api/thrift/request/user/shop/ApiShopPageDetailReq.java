//package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;
//
//import com.facebook.swift.codec.ThriftField;
//import lombok.Data;
//import com.meituan.servicecatalog.api.annotations.FieldDoc;
//import com.meituan.servicecatalog.api.annotations.TypeDoc;
//import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
//
///**
// * @author： liweisong
// * @create： 2023/12/14 16:30
// */
//@Data
//@TypeDoc(description = "店铺详情页入参")
//public class ApiShopPageDetailReq extends BaseThriftDto {
//
//    @FieldDoc(description = "店铺id")
//    private Long shopId;
//
//    @FieldDoc(description = "用户id")
//    private Long userId;
//
//    // 现在先不走风控
//    @FieldDoc(description = "1:商城，2供应商，商城端需要看风控通过的，默认供应商")
//    private Integer clientFrom = 2;
//
//
//    public Long getShopId() {
//        return shopId;
//    }
//
//
//    public void setShopId(Long shopId) {
//        this.shopId = shopId;
//    }
//
//
//    public Long getUserId() {
//        return userId;
//    }
//
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public void checkParameter() {
//        if (shopId == null) {
//            throw new IllegalArgumentException("shopId 不能为空");
//        }
//    }
//
//
//    public Integer getClientFrom() {
//        return clientFrom;
//    }
//
//
//    public void setClientFrom(Integer clientFrom) {
//        this.clientFrom = clientFrom;
//    }
//}
