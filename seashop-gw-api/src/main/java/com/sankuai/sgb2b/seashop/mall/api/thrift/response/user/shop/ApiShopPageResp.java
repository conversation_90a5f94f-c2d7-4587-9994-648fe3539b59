package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.*;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "店铺分页信息")
@Getter
@Setter
public class ApiShopPageResp {

    @FieldDoc(description = "分页结果", requiredness = Requiredness.REQUIRED)
    private BasePageResp<ApiShopResp> pageResp;
}
