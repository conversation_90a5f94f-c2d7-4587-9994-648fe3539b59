package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/04 9:34
 */
@Data
@ToString
@TypeDoc(description = "保存订单评价入参")
public class ApiSaveOrderCommentReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @FieldDoc(description = "包装评分 1-5分", requiredness = Requiredness.REQUIRED)
    private Integer packMark;

    @FieldDoc(description = "物流评分 1-5分", requiredness = Requiredness.REQUIRED)
    private Integer deliveryMark;

    @FieldDoc(description = "服务评分 1-5分", requiredness = Requiredness.REQUIRED)
    private Integer serviceMark;

    @FieldDoc(description = "商品评价", requiredness = Requiredness.REQUIRED)
    private List<ApiSaveProductCommentReq> productCommentList;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(orderId), "订单ID不能为空");
        AssertUtil.throwInvalidParamIfTrue(packMark == null, "包装评分不能为空");
        AssertUtil.throwInvalidParamIfTrue(packMark < 1 || packMark > 5, "包装评分只能在1-5分之间");
        AssertUtil.throwInvalidParamIfTrue(deliveryMark == null, "物流评分不能为空");
        AssertUtil.throwInvalidParamIfTrue(deliveryMark < 1 || deliveryMark > 5, "物流评分只能在1-5分之间");
        AssertUtil.throwInvalidParamIfTrue(serviceMark == null, "服务评分不能为空");
        AssertUtil.throwInvalidParamIfTrue(serviceMark < 1 || serviceMark > 5, "服务评分只能在1-5分之间");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productCommentList), "商品评价不能为空");
        productCommentList.forEach(ApiSaveProductCommentReq::checkParameter);

    }


}
