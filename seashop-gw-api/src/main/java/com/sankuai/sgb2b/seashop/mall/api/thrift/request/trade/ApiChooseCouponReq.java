package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiOrderAdditionalDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "选择优惠券入参")
public class ApiChooseCouponReq extends BaseParamReq {

    @FieldDoc(description = "商家会员ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "店铺信息", requiredness = Requiredness.REQUIRED)
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表", requiredness = Requiredness.REQUIRED)
    private List<ApiShoppingCartProductDto> productList;
    @FieldDoc(description = "订单附加信息。优惠券、运费、发票等")
    private ApiOrderAdditionalDto additional;
    @FieldDoc(description = "优惠券ID，这里是用户领用的记录ID，不是优惠券活动ID。如果为0，代表是去掉优惠券选择", requiredness = Requiredness.REQUIRED)
    private Long couponId;
    @FieldDoc(description = "限时购活动id。如果不为空，代表是限时购")
    private Long flashSaleId;
    /**
     * 组合购活动id。如果不为空，代表是组合购
     */
    @FieldDoc(description = "组合购活动id。如果不为空，代表是组合购")
    private Long collocationId;
    @FieldDoc(description = "是否立即购买。如果为true，代表是立即购买")
    private Boolean whetherBuyNow;
    @FieldDoc(description = "用户收货地址，可能为空", requiredness = Requiredness.OPTIONAL)
    private Long shippingAddressId;

    @Override
    public void checkParameter() {
        if (userId == null) {
            throw new InvalidParamException("userId不能为空");
        }
        if (shop == null) {
            throw new InvalidParamException("shop不能为空");
        }
        if (CollUtil.isEmpty(productList)) {
            throw new InvalidParamException("productList不能为空");
        }
        if (couponId == null) {
            throw new InvalidParamException("请选择优惠券");
        }
    }


}
