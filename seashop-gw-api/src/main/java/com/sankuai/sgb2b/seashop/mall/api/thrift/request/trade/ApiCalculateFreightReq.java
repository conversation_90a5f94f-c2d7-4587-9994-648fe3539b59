package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/01/04 18:47
 */
@Data
@ToString
@TypeDoc(description = "计算运费入参")
public class ApiCalculateFreightReq extends BaseParamReq {

    @FieldDoc(description = "规格id", requiredness = Requiredness.REQUIRED)
    private String skuId;

    @FieldDoc(description = "数量", requiredness = Requiredness.REQUIRED)
    private Long quantity;

    @FieldDoc(description = "金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal salePrice;

    @FieldDoc(description = "地址全路径", requiredness = Requiredness.REQUIRED)
    private String regionPath;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isEmpty(skuId), "规格id不能为空");
        AssertUtil.throwInvalidParamIfTrue(quantity == null || quantity <= 0, "数量不能为空");
        AssertUtil.throwInvalidParamIfTrue(salePrice == null || salePrice.compareTo(BigDecimal.ZERO) <= 0, "金额不能为空");
    }


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


}
