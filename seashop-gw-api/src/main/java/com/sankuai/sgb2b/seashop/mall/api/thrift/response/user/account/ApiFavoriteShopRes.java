package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiTradeProductDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(name = "收藏店铺结果", description = "收藏店铺结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiFavoriteShopRes extends BaseThriftDto {
    @FieldDoc(description = "收藏店铺id")
    private Long userId;
    @FieldDoc(description = "店铺id")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    @FieldDoc(description = "店铺logo")
    private String shopLogo;
    @FieldDoc(description = "人气")
    private Integer popularity;
    @FieldDoc(description = "收藏时间")
    private Date createTime;
    @FieldDoc(description = "热销商品列表")
    private List<ApiTradeProductDto> hotSaleProductList;
    @FieldDoc(description = "新上架商品列表")
    private List<ApiTradeProductDto> newProductList;
    @FieldDoc(description = "是否冻结")
    private Boolean freeze;
    @FieldDoc(description = "店铺状态")
    private Integer shopStatus;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


}
