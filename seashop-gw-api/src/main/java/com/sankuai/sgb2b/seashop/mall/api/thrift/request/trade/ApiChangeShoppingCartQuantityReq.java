package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "变更购物车sku数量请求入参")
@NoArgsConstructor
public class ApiChangeShoppingCartQuantityReq extends BaseParamReq {

    @FieldDoc(description = "购物车主键ID", requiredness = Requiredness.REQUIRED)
    private Long id;
    @FieldDoc(description = "变更后的数量(不管是增减还是输入框改数字，前端先计算后把数量传到后端)", requiredness = Requiredness.REQUIRED)
    private Integer quantity;
    @FieldDoc(description = "店铺信息")
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表")
    private List<ApiShoppingCartProductDto> productList;

    /**
     * 参数校验
     */
    public void checkParameter() {
        if (this.id == null || this.id <= 0) {
            throw new BusinessException("请选择要修改数量的商品");
        }
        if (this.quantity == null || this.quantity <= 0) {
            throw new BusinessException("购买数量不能为空");
        }
    }


}
