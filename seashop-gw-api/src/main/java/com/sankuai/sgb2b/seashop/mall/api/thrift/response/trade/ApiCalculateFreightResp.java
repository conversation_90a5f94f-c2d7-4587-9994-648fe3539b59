package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/01/04 19:02
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "组合购返回值")
public class ApiCalculateFreightResp extends BaseThriftDto {

    @FieldDoc(description = "运费")
    private BigDecimal freight;

    @FieldDoc(description = "是否在限购区域")
    private Boolean inRestrictedRegion;


    public String getFreightString() {
        return this.bigDecimal2String(this.freight);
    }


    public void setFreightString(String freight) {
        this.freight = this.string2BigDecimal(freight);
    }


}
