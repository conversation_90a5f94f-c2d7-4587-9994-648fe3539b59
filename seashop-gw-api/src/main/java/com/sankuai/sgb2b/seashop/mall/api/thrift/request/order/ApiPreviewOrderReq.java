package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.PreviewOrderSelectSkuDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "预览订单请求入参")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApiPreviewOrderReq {


    @FieldDoc(description = "商家用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;

    @FieldDoc(description = "选择提交订单的购物车sku", requiredness = Requiredness.REQUIRED)
    private List<PreviewOrderSelectSkuDto> selectedSkuList;

}
