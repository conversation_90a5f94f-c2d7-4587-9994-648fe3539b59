package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "立即购买请求入参")
@AllArgsConstructor
@NoArgsConstructor
public class ApiPreviewBuyNowReq extends BaseParamReq {

    @FieldDoc(description = "商家用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "商品ID", requiredness = Requiredness.REQUIRED)
    private Long productId;
    @FieldDoc(description = "skuId", requiredness = Requiredness.REQUIRED)
    private String skuId;
    @FieldDoc(description = "购买数量", requiredness = Requiredness.REQUIRED)
    private Long quantity;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.productId == null || this.productId <= 0) {
            throw new InvalidParamException("productId不能为空");
        }
        if (this.skuId == null || this.skuId.isEmpty()) {
            throw new InvalidParamException("skuId不能为空");
        }
        if (this.quantity == null || this.quantity <= 0) {
            throw new InvalidParamException("quantity不能为空");
        }
    }


}
