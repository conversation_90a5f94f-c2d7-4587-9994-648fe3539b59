package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "组合购返回值")
public class ApiCollectionBuyResp extends BaseThriftDto {

    @FieldDoc(description = "组合购ID")
    private Long id;

    @FieldDoc(description = "组合购标题")
    private String title;

    @FieldDoc(description = "组合购副标题")
    private String shortDesc;

    @FieldDoc(description = "组合购商品集合")
    private List<ApiCollocationProductResp> productRespList;


}
