package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/22 16:56
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "分类信息返回值")
public class ApiProductLadderPriceResp extends BaseThriftDto {

    @FieldDoc(description = "最小批量")
    private Integer minBath;

    @FieldDoc(description = "最大批量")
    private Integer maxBath;

    @FieldDoc(description = "价格")
    private BigDecimal price;


    public String getPriceString() {
        return this.bigDecimal2String(this.price);
    }


    public void setPriceString(String price) {
        this.price = this.string2BigDecimal(price);
    }
}
