package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/04 9:34
 */
@Data
@ToString
@TypeDoc(description = "追加商品评论入参")
public class ApiAppendProductCommentReq extends BaseParamReq {

    @FieldDoc(description = "商品评价id", requiredness = Requiredness.REQUIRED)
    private Long productCommentId;

    @FieldDoc(description = "追加内容", requiredness = Requiredness.REQUIRED)
    private String appendContent;

    @FieldDoc(description = "评价图片 最多五张", requiredness = Requiredness.NONE)
    private List<String> commentImageList;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productCommentId == null || productCommentId <= 0, "商品评价id不能为空");
        AssertUtil.throwIfTrue(StringUtils.isBlank(appendContent), "追加内容不能为空");
        AssertUtil.throwIfTrue(appendContent.length() > 1000, "追加内容不能超过1000字");
        if (commentImageList != null && commentImageList.size() > 5) {
            AssertUtil.throwIfTrue(commentImageList.size() > 5, "评价图片最多只能上传5张");
        }
    }


}
