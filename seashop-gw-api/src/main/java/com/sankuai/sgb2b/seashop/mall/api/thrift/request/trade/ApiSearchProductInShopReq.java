package com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "店铺内搜索商品请求入参")
public class ApiSearchProductInShopReq extends BasePageReq {

    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    /**
     * 搜索关键字，目前是针对商品名称
     */
    @FieldDoc(description = "搜索关键字，目前是针对商品名称")
    private String searchKey;
    /**
     * 金额区间开始，指商品原售价
     */
    @FieldDoc(description = "金额区间开始，指商品原售价")
    private BigDecimal minPrice;
    /**
     * 金额区间结束，指商品原售价
     */
    @FieldDoc(description = "金额区间结束，指商品原售价")
    private BigDecimal maxPrice;
    @FieldDoc(description = "店铺分类ID")
    private Long shopCategoryId;


    public String getMinPriceString() {
        return this.bigDecimal2String(this.minPrice);
    }


    public void setMinPriceString(String minPrice) {
        this.minPrice = this.string2BigDecimal(minPrice);
    }


    public String getMaxPriceString() {
        return this.bigDecimal2String(this.maxPrice);
    }


    public void setMaxPriceString(String maxPrice) {
        this.maxPrice = this.string2BigDecimal(maxPrice);
    }


}
