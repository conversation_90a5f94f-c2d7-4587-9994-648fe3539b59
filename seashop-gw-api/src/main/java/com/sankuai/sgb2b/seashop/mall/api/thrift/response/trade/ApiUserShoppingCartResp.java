package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopProductDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/2
 */
@TypeDoc(description = "商家购物车返回对象")
@ToString
@Data
public class ApiUserShoppingCartResp extends BaseThriftDto {

    @FieldDoc(description = "按店铺分组的商品列表")
    private List<ApiShoppingCartShopProductDto> shopProductList;
    @FieldDoc(description = "失效的商品列表")
    private List<ApiShoppingCartProductDto> invalidProductList;
    @FieldDoc(description = "所有勾选的商品总金额")
    private BigDecimal totalAmount;
    /**
     * 购物车中所有商品是否选中，也是所有店铺是否是选中
     */
    @FieldDoc(description = "购物车中所有商品是否选中，也是所有店铺是否是选中")
    private Boolean whetherAllSelected;
    /**
     * 所有勾选的商品总数量
     */
    @FieldDoc(description = "所有勾选的商品总数量")
    private Long totalSelectedQuantity;

    @FieldDoc(description = "所有勾选的商品优惠总金额")
    private BigDecimal totalDiscountAmount;


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    public String getTotalDiscountAmountString() {
        return this.bigDecimal2String(this.totalDiscountAmount);
    }


    public void setTotalDiscountAmountString(String totalDiscountAmount) {
        this.totalDiscountAmount = this.string2BigDecimal(totalDiscountAmount);
    }


    public static ApiUserShoppingCartResp defaultEmpty() {
        ApiUserShoppingCartResp resp = new ApiUserShoppingCartResp();
        resp.setShopProductList(Collections.emptyList());
        resp.setInvalidProductList(Collections.emptyList());
        resp.setTotalAmount(BigDecimal.ZERO);
        resp.setWhetherAllSelected(false);
        resp.setTotalSelectedQuantity(0L);
        resp.setTotalDiscountAmount(BigDecimal.ZERO);
        return resp;
    }


}
