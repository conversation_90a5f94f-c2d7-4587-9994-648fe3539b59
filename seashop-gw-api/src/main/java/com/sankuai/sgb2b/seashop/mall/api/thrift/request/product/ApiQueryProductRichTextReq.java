package com.sankuai.sgb2b.seashop.mall.api.thrift.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/25 16:45
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询商品版式入参")
public class ApiQueryProductRichTextReq extends BaseParamReq {

    @FieldDoc(description = "商品id")
    private Long productId;

    @FieldDoc(description = "版式类型 1-pc端 2-移动端(默认1)")
    private Integer type;


}
