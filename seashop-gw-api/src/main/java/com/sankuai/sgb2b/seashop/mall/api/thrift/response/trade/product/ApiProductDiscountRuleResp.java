package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "折扣规则返回值")
public class ApiProductDiscountRuleResp extends BaseThriftDto {

    @FieldDoc(description = "折扣门槛(订单金额需要满足多少)")
    private BigDecimal quota;

    @FieldDoc(description = "折扣比例(达到门槛后商品单价的优惠比例)")
    private BigDecimal discount;


    public String getQuotaString() {
        return this.bigDecimal2String(this.quota);
    }


    public void setQuotaString(String quota) {
        this.quota = this.string2BigDecimal(quota);
    }


    public String getDiscountString() {
        return this.bigDecimal2String(this.discount);
    }


    public void setDiscountString(String discount) {
        this.discount = this.string2BigDecimal(discount);
    }
}
