package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "店铺信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiProductShopInfoResp extends BaseThriftDto {
    @FieldDoc(description = "公司名称")
    private String companyName;
    @FieldDoc(description = "店id")
    private Long id;
    @FieldDoc(description = "包装评分")
    private BigDecimal packMark;
    @FieldDoc(description = "服务评分")
    private BigDecimal serviceMark;
    @FieldDoc(description = "综合评分")
    private BigDecimal comprehensiveMark;
    @FieldDoc(description = "手机")
    private String phone;
    @FieldDoc(description = "店铺名称")
    private String name;
    @FieldDoc(description = "店铺地址")
    private String address;
    @FieldDoc(description = "商品评分")
    private BigDecimal productMark = new BigDecimal(3);
    @FieldDoc(description = "是否自营")
    private Boolean whetherSelf;
    @FieldDoc(description = "品牌LOGO")
    @JsonUrlFormat(deserializer = false)
    private String brandLogo;
    @FieldDoc(description = "品牌ID")
    private Long brandId;
    @FieldDoc(description = "保证金")
    private BigDecimal cashDeposits = new BigDecimal(0);
    @FieldDoc(description = "店铺已缴纳保证金")
    private BigDecimal cashDepositPaid = new BigDecimal(0);
    @FieldDoc(description = "店铺保证金欠费金额")
    private BigDecimal cashDepositNeedPay = new BigDecimal(0);
    @FieldDoc(description = "是否支持7天无理由退货")
    private Boolean sevenDayNoReasonReturn;
    @FieldDoc(description = "是否支持消费者保障")
    private Boolean customerSecurity;
    @FieldDoc(description = "是否支持及时发货")
    private Boolean timelyDelivery;
    @FieldDoc(description = "是否虚拟品牌")
    private Boolean virtual;
    @FieldDoc(description = "店铺图片")
    @JsonUrlFormat(deserializer = false)
    private String logo;
    @FieldDoc(description = "店铺商品数")
    private Long productCount;
    @FieldDoc(description = "店铺关注人数")
    private Long followCount;
    @FieldDoc(description = "用户是否已关注当前店铺")
    private Boolean followFlag = false;


    public String getPackMarkString() {
        return this.bigDecimal2String(this.packMark);
    }


    public void setPackMarkString(String packMark) {
        this.packMark = this.string2BigDecimal(packMark);
    }


    public String getServiceMarkString() {
        return this.bigDecimal2String(this.serviceMark);
    }


    public void setServiceMarkString(String serviceMark) {
        this.serviceMark = this.string2BigDecimal(serviceMark);
    }


    public String getComprehensiveMarkString() {
        return this.bigDecimal2String(this.comprehensiveMark);
    }


    public void setComprehensiveMarkString(String comprehensiveMark) {
        this.comprehensiveMark = this.string2BigDecimal(comprehensiveMark);
    }


    public String getProductMarkString() {
        return this.bigDecimal2String(this.productMark);
    }


    public void setProductMarkString(String productMark) {
        this.productMark = this.string2BigDecimal(productMark);
    }


    public String getCashDepositsString() {
        return this.bigDecimal2String(this.cashDeposits);
    }


    public void setCashDepositsString(String cashDeposits) {
        this.cashDeposits = this.string2BigDecimal(cashDeposits);
    }


    public String getCashDepositNeedPayString() {
        return this.bigDecimal2String(this.cashDepositNeedPay);
    }


    public void setCashDepositNeedPayString(String cashDepositNeedPay) {
        this.cashDepositNeedPay = this.string2BigDecimal(cashDepositNeedPay);
    }


    public String getCashDepositPaidString() {
        return this.bigDecimal2String(this.cashDepositPaid);
    }


    public void setCashDepositPaidString(String cashDepositPaid) {
        this.cashDepositPaid = this.string2BigDecimal(cashDepositPaid);
    }
}
