package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseTopicModuleRes;
import lombok.*;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(
    description = "专题返回对象"
)
public class ApiBaseTopicRes extends BaseThriftDto {
    private Long id;

    @FieldDoc(description = "专题名称")
    private String name;

    @FieldDoc(description = "图表")
    @JsonUrlFormat(deserializer = false)
    private String frontCoverImage;

    @FieldDoc(description = "专题图片")
    @JsonUrlFormat(deserializer = false)
    private String topImage;

    @FieldDoc(description = "背景图片")
    @JsonUrlFormat(deserializer = false)
    private String backgroundImage;

    @FieldDoc(description = "使用终端")
    private Integer platForm;

    @FieldDoc(description = "标签")
    private String tags;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "是否推荐")
    private Boolean isRecommend;

    @FieldDoc(description = "自定义热点")
    private String selfDefinetext;

    @FieldDoc(description = "专题模块集合")
    private List<BaseTopicModuleRes> topicModules;


}
