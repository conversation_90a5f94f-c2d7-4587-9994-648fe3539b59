package com.sankuai.sgb2b.seashop.mall.api.thrift.response.refund.dto;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "售后明细对象")
public class ApiRefundItemDto extends BaseThriftDto {

    @FieldDoc(description = "退款商品id")
    private String productId;
    @FieldDoc(description = "SKU ID")
    private String skuId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String mainImagePath;
    @FieldDoc(description = "规格描述列表")
    private List<String> skuDescList;
    @FieldDoc(description = "规格描述")
    private String skuDesc;


}
