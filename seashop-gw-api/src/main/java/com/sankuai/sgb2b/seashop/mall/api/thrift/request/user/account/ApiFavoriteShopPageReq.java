package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@TypeDoc(description = "收藏店铺分页查询请求对象")
public class ApiFavoriteShopPageReq extends BasePageReq {

    @FieldDoc(description = "用户ID")
    private Long userId;
}
