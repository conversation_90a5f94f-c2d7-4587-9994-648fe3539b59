package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "迁移数据绑定手机号请求")
public class ApiTransferNameReq {
    @FieldDoc(description = "用户名", requiredness = Requiredness.OPTIONAL)
    @NotBlank(message = "用户名不能为空")
    private String accountName;
    @FieldDoc(description = "商家id", requiredness = Requiredness.OPTIONAL)
    private Long userId;
    @FieldDoc(description = "管理员id", requiredness = Requiredness.OPTIONAL)
    private Long managerId;


}
