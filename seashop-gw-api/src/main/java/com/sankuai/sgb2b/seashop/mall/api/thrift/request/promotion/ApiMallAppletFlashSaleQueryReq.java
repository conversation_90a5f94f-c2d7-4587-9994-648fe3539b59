package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/17 17:56
 */
@Data
@TypeDoc(description = "限时购查询请求对象")
public class ApiMallAppletFlashSaleQueryReq extends BasePageReq {

    @FieldDoc(description = "商品ID集合")
    private List<String> productIds;

    @FieldDoc(description = "限时购id列表")
    private List<Long> flashSaleIds;


}
