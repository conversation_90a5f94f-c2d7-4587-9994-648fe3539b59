package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(
    description = "程序设置返回对象"
)
public class ApiAppSitSettingRes extends BaseParamReq {
    /**
     * hi客服站点网址
     */
    @FieldDoc(description = "hi客服站点网址")
    private String chatDomain;


}
