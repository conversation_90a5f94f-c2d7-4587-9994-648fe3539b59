package com.sankuai.sgb2b.seashop.mall.api.thrift.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2024-02-20
 */
@Data
@TypeDoc(description = "银行信息")
@NoArgsConstructor
@AllArgsConstructor
public class ApiBanksResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @FieldDoc(description = "银行id")
    private Long id;

    /**
     * 银行编码
     */
    @FieldDoc(description = "银行编码")
    private String bankCode;

    /**
     * 银行名称
     */
    @FieldDoc(description = "银行名称")
    private String bankName;


}
