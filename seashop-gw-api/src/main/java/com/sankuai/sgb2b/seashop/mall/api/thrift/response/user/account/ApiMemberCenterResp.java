package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 标签信息
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "标签信息")
@AllArgsConstructor
@NoArgsConstructor
public class ApiMemberCenterResp extends BaseThriftDto {
    @FieldDoc(description = "id")
    private Long id;
    @FieldDoc(description = "用户名")
    private String userName;
    @FieldDoc(description = "昵称")
    private String nick;
    @FieldDoc(description = "性别")
    private Integer sex;
    @FieldDoc(description = "头像")
    @JsonUrlFormat(deserializer = false)
    private String photo;
    @FieldDoc(description = "是否绑定手机号")
    private Boolean bindPhone;
    @FieldDoc(description = "是否绑定邮箱")
    private Boolean bindEmail;
    @FieldDoc(description = "优惠券数量")
    private Integer couponCount;
    @FieldDoc(description = "待付款")
    private Integer waitPayCount;
    @FieldDoc(description = "待发货")
    private Integer waitSendCount;
    @FieldDoc(description = "待收货")
    private Integer waitReceiveCount;
    @FieldDoc(description = "待评价")
    private Integer waitCommentCount;
    @FieldDoc(description = "待售后")
    private Integer refundCount;


}
