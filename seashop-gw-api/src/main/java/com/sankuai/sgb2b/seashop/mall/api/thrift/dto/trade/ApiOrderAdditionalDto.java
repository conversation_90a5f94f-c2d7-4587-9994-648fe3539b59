package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.InvoiceDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;


/**
 * 订单附加信息：留言、优惠券、发票等
 *
 * <AUTHOR>
 */
@TypeDoc(description = "订单附加信息")
@ToString
@Data
public class ApiOrderAdditionalDto extends BaseThriftDto {

    @FieldDoc(description = "配送方式。目前是固定值。1：快递配送")
    private Integer deliveryType;
    @FieldDoc(description = "优惠券ID")
    private Long couponRecordId;
    @FieldDoc(description = "优惠券金额")
    private BigDecimal couponAmount;
    @FieldDoc(description = "折扣总金额")
    private BigDecimal discountAmount;
    @FieldDoc(description = "满减总金额")
    private BigDecimal reductionAmount;
    @FieldDoc(description = "运费")
    private BigDecimal freightAmount;
    @FieldDoc(description = "用户备注")
    private String remark;
    @FieldDoc(description = "税费")
    private BigDecimal taxAmount;
    @FieldDoc(description = "发票信息")
    private InvoiceDto invoice;
    @FieldDoc(description = "商品总金额，仅商品")
    private BigDecimal productAmount;
    @FieldDoc(description = "实付金额，计算各种营销")
    private BigDecimal payAmount;
    /**
     * 店铺发票配置，预览订单接口初始话一次，后续依赖前端传入后再返回或者前端保留
     */
    @FieldDoc(description = "店铺发票配置，预览订单接口初始话一次，后续依赖前端传入后再返回或者前端保留")
    private ApiShopInvoiceConfigDto shopInvoiceConfig;

    @FieldDoc(description = "第三方订单号(美团订单号)")
    private String sourceOrderId;


    public String getCouponAmountString() {
        return this.bigDecimal2String(this.couponAmount);
    }


    public void setCouponAmountString(String couponAmount) {
        this.couponAmount = this.string2BigDecimal(couponAmount);
    }


    public String getDiscountAmountString() {
        return this.bigDecimal2String(this.discountAmount);
    }


    public void setDiscountAmountString(String discountAmount) {
        this.discountAmount = this.string2BigDecimal(discountAmount);
    }


    public String getReductionAmountString() {
        return this.bigDecimal2String(this.reductionAmount);
    }


    public void setReductionAmountString(String reductionAmount) {
        this.reductionAmount = this.string2BigDecimal(reductionAmount);
    }


    public String getFreightAmountString() {
        return this.bigDecimal2String(this.freightAmount);
    }


    public void setFreightAmountString(String freightAmount) {
        this.freightAmount = this.string2BigDecimal(freightAmount);
    }


    public String getTaxAmountString() {
        return this.bigDecimal2String(this.taxAmount);
    }


    public void setTaxAmountString(String taxAmount) {
        this.taxAmount = this.string2BigDecimal(taxAmount);
    }


    public String getProductAmountString() {
        return this.bigDecimal2String(this.productAmount);
    }


    public void setProductAmountString(String productAmount) {
        this.productAmount = this.string2BigDecimal(productAmount);
    }


    public String getPayAmountString() {
        return this.bigDecimal2String(this.payAmount);
    }


    public void setPayAmountString(String payAmount) {
        this.payAmount = this.string2BigDecimal(payAmount);
    }


}
