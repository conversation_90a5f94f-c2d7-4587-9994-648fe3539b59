package com.sankuai.sgb2b.seashop.mall.api.thrift.request.topic;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "首页对象")
public class ApiWapIndexReq extends BaseThriftDto {
    /**
     * 首页的json数据
     */
    @FieldDoc(description = "首页的json数据")
    private String content;

    @FieldDoc(description = "客户端的模板id")
    private String client;

    /**
     * 获取商品分组的url
     */
    @FieldDoc(description = "获取商品分组的url")
    private String getGoodGroupUrl;

    /**
     * 获取商品的url
     */
    @FieldDoc(description = "获取商品的url")
    private String getGoodUrl;

    /**
     * 是否预览
     */
    @FieldDoc(description = "是否预览")
    private Boolean preview;

    public void checkParameter() {
        if (StringUtils.isBlank(this.content)) {
            throw new IllegalArgumentException("json数据不能为空");
        }

        if (StringUtils.isBlank(this.client)) {
            throw new IllegalArgumentException("图client不能为空");
        }
    }


}
