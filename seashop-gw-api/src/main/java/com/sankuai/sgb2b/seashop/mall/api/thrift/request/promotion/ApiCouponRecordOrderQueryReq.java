package com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "订单维度优惠券查询请求对象")
public class ApiCouponRecordOrderQueryReq extends BaseParamReq {

    @FieldDoc(description = "订单信息列表", requiredness = Requiredness.REQUIRED)
    private List<ApiOrderQueryReq> orderList;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(this.orderList)) {
            throw new InvalidParamException("orderList不能为空");
        }
        orderList.forEach(ApiOrderQueryReq::checkParameter);
    }


}
