package com.sankuai.sgb2b.seashop.mall.api.thrift.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopProductDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "预览订单请求入参")
public class ApiSubmitOrderReq extends BaseParamReq {

    @FieldDoc(description = "页面防重复提交token")
    private String submitToken;
    @FieldDoc(description = "收货地址")
    private ShippingAddressDto shippingAddress;
    @FieldDoc(description = "所有店铺总金额")
    private BigDecimal totalAmount;
    @FieldDoc(description = "按店铺分组的商品列表")
    private List<ShoppingCartShopProductDto> shopProductList;
    @FieldDoc(description = "限时购活动id，预览接口返回的回传")
    private Long flashSaleId;
    @FieldDoc(description = "组合购活动id，预览接口返回的回传")
    private Long collocationId;
    @FieldDoc(description = "是否立即购买")
    private Boolean whetherBuyNow;
    @FieldDoc(description = "订单平台，0：PC；2：小程序")
    private Integer platform;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(submitToken, "提交token不能为空");
        AssertUtil.throwIfNull(platform, "订单来源平台不能为空");
        AssertUtil.throwIfNull(shopProductList, "订单数据不能为空");
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


}
