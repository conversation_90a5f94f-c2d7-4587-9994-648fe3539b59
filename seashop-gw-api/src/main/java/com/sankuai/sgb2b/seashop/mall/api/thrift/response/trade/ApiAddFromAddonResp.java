package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiAddonSummaryDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "凑单加购返回")
@ToString
@Data
public class ApiAddFromAddonResp extends BaseThriftDto {


    @FieldDoc(description = "凑单汇总描述")
    private ApiAddonSummaryDto addonSummary;
    @FieldDoc(description = "店铺信息")
    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表")
    private List<ApiShoppingCartProductDto> productList;


}
