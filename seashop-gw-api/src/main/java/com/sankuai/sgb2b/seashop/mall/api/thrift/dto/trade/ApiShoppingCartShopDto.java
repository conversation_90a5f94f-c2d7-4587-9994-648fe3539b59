package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "购物车商品返回对象")
@ToString
@Data
public class ApiShoppingCartShopDto extends BaseThriftDto {

    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    @FieldDoc(description = "勾选的sku的总金额")
    private BigDecimal selectedTotalAmount;
    /**
     * 是否需要显示凑单按钮
     */
    @FieldDoc(description = "是否需要显示凑单按钮")
    private Boolean showAddonBtn;
    /**
     * 店铺当前满足的满减活动描述
     */
    @FieldDoc(description = "店铺当前满足的满减活动描述")
    private List<ApiAddonDescDto> promotionDescList;
    /**
     * 商品总金额
     */
    @FieldDoc(description = "商品总金额，不含优惠")
    private BigDecimal productTotalAmount;
    @FieldDoc(description = "店铺当前满足的满减活动描述")
    private List<String> reductionDescList;
    /**
     * 店铺是否是选中状态。如果店铺下的所有商品都是选中的，则店铺是选中状态
     */
    @FieldDoc(description = "店铺是否是选中状态。如果店铺下的所有商品都是选中的，则店铺是选中状态 ")
    private Boolean whetherSelected;
    @FieldDoc(description = "勾选的商品总数量")
    private Long selectedQuantity;
    @FieldDoc(description = "是否有与订单和商品匹配的有效的优惠券")
    private Boolean hasValidCoupon;


    public String getSelectedTotalAmountString() {
        return this.bigDecimal2String(this.selectedTotalAmount);
    }


    public void setSelectedTotalAmountString(String selectedTotalAmount) {
        this.selectedTotalAmount = this.string2BigDecimal(selectedTotalAmount);
    }


    public String getProductTotalAmountString() {
        return this.bigDecimal2String(this.productTotalAmount);
    }


    public void setProductTotalAmountString(String productTotalAmount) {
        this.productTotalAmount = this.string2BigDecimal(productTotalAmount);
    }


}
