package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "预览订单相关汇总信息")
@ToString
@Data
public class ApiPreviewOrderSummaryDto extends BaseThriftDto {

    @FieldDoc(description = "预览订单页总的应付金额")
    private BigDecimal totalPayAmount;
    @FieldDoc(description = "预览订单页总的商品金额")
    private BigDecimal totalProductAmount;
    @FieldDoc(description = "预览订单页总的商品数量")
    private Long totalSkuQuantity;
    @FieldDoc(description = "预览订单页总的折扣金额")
    private BigDecimal totalDiscountAmount;
    @FieldDoc(description = "预览订单页总的优惠券金额")
    private BigDecimal totalCouponAmount;
    @FieldDoc(description = "预览订单页总的运费金额")
    private BigDecimal totalFreightAmount;
    @FieldDoc(description = "预览订单页总的税费金额")
    private BigDecimal totalTaxAmount;
    @FieldDoc(description = "预览订单页总的满减金额")
    private BigDecimal totalReductionAmount;


    public String getTotalPayAmountString() {
        return this.bigDecimal2String(this.totalPayAmount);
    }


    public void setTotalPayAmountString(String totalPayAmount) {
        this.totalPayAmount = this.string2BigDecimal(totalPayAmount);
    }


    public String getTotalProductAmountString() {
        return this.bigDecimal2String(this.totalProductAmount);
    }


    public void setTotalProductAmountString(String totalProductAmount) {
        this.totalProductAmount = this.string2BigDecimal(totalProductAmount);
    }


    public String getTotalDiscountAmountString() {
        return this.bigDecimal2String(this.totalDiscountAmount);
    }


    public void setTotalDiscountAmountString(String totalDiscountAmount) {
        this.totalDiscountAmount = this.string2BigDecimal(totalDiscountAmount);
    }


    public String getTotalCouponAmountString() {
        return this.bigDecimal2String(this.totalCouponAmount);
    }


    public void setTotalCouponAmountString(String totalCouponAmount) {
        this.totalCouponAmount = this.string2BigDecimal(totalCouponAmount);
    }


    public String getTotalFreightAmountString() {
        return this.bigDecimal2String(this.totalFreightAmount);
    }


    public void setTotalFreightAmountString(String totalFreightAmount) {
        this.totalFreightAmount = this.string2BigDecimal(totalFreightAmount);
    }


    public String getTotalTaxAmountString() {
        return this.bigDecimal2String(this.totalTaxAmount);
    }


    public void setTotalTaxAmountString(String totalTaxAmount) {
        this.totalTaxAmount = this.string2BigDecimal(totalTaxAmount);
    }


    public String getTotalReductionAmountString() {
        return this.bigDecimal2String(this.totalReductionAmount);
    }


    public void setTotalReductionAmountString(String totalReductionAmount) {
        this.totalReductionAmount = this.string2BigDecimal(totalReductionAmount);
    }
}
