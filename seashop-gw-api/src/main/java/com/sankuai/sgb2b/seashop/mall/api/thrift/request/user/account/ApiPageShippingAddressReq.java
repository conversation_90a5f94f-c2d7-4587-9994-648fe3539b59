package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/21 9:34
 */
@Data
@TypeDoc(description = "用户收货地址分页查询请求对象")
public class ApiPageShippingAddressReq extends BasePageReq {

    @FieldDoc(description = "用户id")
    private Long userId;

    public void checkParameter() {
        if (userId == null) {
            throw new IllegalArgumentException("userId is null");
        }
    }


}
