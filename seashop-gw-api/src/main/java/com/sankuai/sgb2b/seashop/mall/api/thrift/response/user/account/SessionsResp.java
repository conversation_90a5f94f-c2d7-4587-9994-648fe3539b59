package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2023-11-30
 */
@Data
@TypeDoc(description = "店铺开放平台设置")
@NoArgsConstructor
@AllArgsConstructor
public class SessionsResp extends BaseThriftDto {
    @FieldDoc(description = "头像")
    private String avatar;
    @FieldDoc(description = "玩家头像")
    private String memberAvatar;
    @FieldDoc(description = "域名")
    private String domain;
    @FieldDoc(description = "令牌")
    private String token;
    @FieldDoc(description = "uri")
    private String uri;


}
