package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.dto.ApiQueryFootMenusRespDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:46
 */
@Data
@TypeDoc(description = "底部菜单栏")
public class ApiQueryFootMenusResp extends BaseThriftDto {

    @FieldDoc(description = "底部菜单栏集合")
    private List<ApiQueryFootMenusRespDto> menusRespDtoList;


}
