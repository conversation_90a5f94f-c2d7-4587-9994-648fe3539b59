package com.sankuai.sgb2b.seashop.mall.api.thrift.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/25 16:39
 */
@TypeDoc(
    description = "商品富文本信息"
)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ApiProductRichTextResp extends BaseThriftDto {

    @FieldDoc(description = "详情富文本")
    private String description;

    @FieldDoc(description = "顶部版式")
    private String descriptionPrefix;

    @FieldDoc(description = "底部版式")
    private String descriptionSuffix;


}
