package com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "购物车商品返回对象")
@ToString
@Data
@Getter
@Setter
public class ApiShoppingCartShopProductDto {

    @FieldDoc(description = "店铺信息", requiredness = Requiredness.REQUIRED)

    private ApiShoppingCartShopDto shop;
    @FieldDoc(description = "商品列表", requiredness = Requiredness.REQUIRED)

    private List<ApiShoppingCartProductDto> productList;
    @FieldDoc(description = "附加信息")

    private ApiOrderAdditionalDto additional;
    @FieldDoc(description = "有效的优惠券信息")

    private List<ApiUserCouponRecordDto> validCouponList;


}
