package com.sankuai.sgb2b.seashop.mall.api.thrift.response.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "退款日志对象")
public class ApiRefundLogResp extends BaseThriftDto {

    @FieldDoc(description = "退款ID")
    private Long refundId;

    @FieldDoc(description = "操作人")
    private String operator;

    @FieldDoc(description = "操作时间")
    private Date operateDate;

    @FieldDoc(description = "操作内容")
    private String operateContent;

    @FieldDoc(description = "操作步骤")
    private Integer step;

    @FieldDoc(description = "操作步骤中文")
    private String stepName;

    @FieldDoc(description = "快递公司")
    private String expressCompanyName;

    @FieldDoc(description = "快递公司编码")
    private String expressCompanyCode;

    @FieldDoc(description = "快递单号")
    private String shipOrderNumber;

    @FieldDoc(description = "内容")
    private String remark;

    @FieldDoc(description = "状态")
    private Integer status;

    @FieldDoc(description = "状态翻译")
    private String statusName;


    public Long getOperateDateLong() {
        return this.date2Long(this.operateDate);
    }


    public void setOperateDateLong(Long operateDate) {
        this.operateDate = this.long2Date(operateDate);
    }


}
