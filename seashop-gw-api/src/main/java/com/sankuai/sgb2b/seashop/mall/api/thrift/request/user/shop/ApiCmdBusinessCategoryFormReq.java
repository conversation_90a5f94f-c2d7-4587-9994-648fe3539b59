package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: z自定义表单分类请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "z自定义表单分类请求入参")
public class ApiCmdBusinessCategoryFormReq {
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "分类ID")
    private Long categoryId;
    @FieldDoc(description = "自定义表单ID")
    private Long formId;
    @FieldDoc(description = "自定义数据")
    private String formData;
    @FieldDoc(description = "自定义表单")
    private String customJson;
    @FieldDoc(description = "根据自定义Id获取字段值")
    private String fieldData;
    @FieldDoc(description = "自定义表单名称")
    private String formName;


}
