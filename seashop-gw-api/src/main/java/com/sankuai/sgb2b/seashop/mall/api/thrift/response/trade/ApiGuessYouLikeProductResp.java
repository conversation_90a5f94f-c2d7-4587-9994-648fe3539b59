package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiTradeProductDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/12 11:33
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "猜你喜欢返回值")
public class ApiGuessYouLikeProductResp {

    @FieldDoc(description = "商品列表")
    private List<ApiTradeProductDto> productList;


}
