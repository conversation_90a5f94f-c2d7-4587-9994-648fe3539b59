package com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "商家添加商品到购物车请求入参")
@Getter
@Setter
public class ApiQueryBusinessCategoryApplyPageReq extends BasePageReq {

    @FieldDoc(description = "店铺名称", requiredness = Requiredness.OPTIONAL)
    private String shopName;

    @FieldDoc(description = "店铺状态", requiredness = Requiredness.OPTIONAL)
    private Integer auditedStatus;

    @FieldDoc(description = "签署状态", requiredness = Requiredness.OPTIONAL)
    private Integer agreementStatus;

    @FieldDoc(description = "店铺Id", requiredness = Requiredness.OPTIONAL)
    private Long shopId;
}
