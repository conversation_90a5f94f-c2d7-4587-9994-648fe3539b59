package com.sankuai.sgb2b.seashop.mall.api.thrift.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.order.thrift.core.dto.PayMethodDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.PcPrePayOrderInfoDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "外观-订单支付方式返回对象")
public class ApiOrderPayMethodResp {

    @FieldDoc(description = "订单信息")
    private PcPrePayOrderInfoDto orderInfo;
    @FieldDoc(description = "支付方式列表。1：支付宝扫码；3：微信小程序；5：企业网银；6：个人网银")
    private List<PayMethodDto> payMethodList;


}
