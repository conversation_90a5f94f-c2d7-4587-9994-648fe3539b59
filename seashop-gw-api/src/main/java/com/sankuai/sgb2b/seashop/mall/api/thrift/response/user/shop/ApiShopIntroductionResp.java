package com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "指定可配送区域的详细地址")
@ToString
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopIntroductionResp extends BaseThriftDto {
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "店铺名")
    private String shopName;
    @FieldDoc(description = "店铺logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;
    @FieldDoc(description = "商品评分")
    private BigDecimal packMark;
    @FieldDoc(description = "服务评分")
    private BigDecimal serviceMark;
    @FieldDoc(description = "综合评分")
    private BigDecimal comprehensiveMark;
    @FieldDoc(description = "二维码")
    private String qrCode;
    @FieldDoc(description = "店铺关注人数")
    private Long followCount;
    @FieldDoc(description = "用户是否已关注当前店铺")
    private Boolean followFlag = false;
    @FieldDoc(description = "默认LOGO")
    private String memberLogo;


    public String getPackMarkString() {
        return this.bigDecimal2String(this.packMark);
    }


    public void setPackMarkString(String packMark) {
        this.packMark = this.string2BigDecimal(packMark);
    }


    public String getServiceMarkString() {
        return this.bigDecimal2String(this.serviceMark);
    }


    public void setServiceMarkString(String serviceMark) {
        this.serviceMark = this.string2BigDecimal(serviceMark);
    }


    public String getComprehensiveMarkString() {
        return this.bigDecimal2String(this.comprehensiveMark);
    }


    public void setComprehensiveMarkString(String comprehensiveMark) {
        this.comprehensiveMark = this.string2BigDecimal(comprehensiveMark);
    }


}
