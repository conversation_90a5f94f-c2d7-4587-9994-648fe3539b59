package com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiPreviewOrderSummaryDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShippingAddressDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopProductDto;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "预览订单返回对象")
@ToString
@Data
public class ApiPreviewOrderResp extends BaseThriftDto {

    @FieldDoc(description = "用户收货地址，可能为空")
    private ApiShippingAddressDto shippingAddress;
    @FieldDoc(description = "按店铺分组的商品列表")
    private List<ApiShoppingCartShopProductDto> shopProductList;
    @FieldDoc(description = "限时购活动id。如果不为空，代表是限时购，商品列表只会有一条记录")
    private Long flashSaleId;
    /**
     * 组合购活动id。如果不为空，代表是组合购
     */
    @FieldDoc(description = "组合购活动id。如果不为空，代表是组合购")
    private Long collocationId;
    @FieldDoc(description = "预览订单页汇总信息")
    private ApiPreviewOrderSummaryDto summary;
    @FieldDoc(description = "订单ID，创建订单成功会有")
    private List<String> orderIdList;
    @FieldDoc(description = "是否立即购买")
    private Boolean whetherBuyNow;
    /**
     * 预览订单页所有商品总金额
     */
    @FieldDoc(description = "预览订单页所有商品总金额")
    private BigDecimal totalAmount;
    @FieldDoc(description = "是否需要支付。如果实付金额时0元，则不需要调起支付")
    private Boolean needPay;
    @FieldDoc(description = "是否成功")
    private boolean success;


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    public boolean isSuccess() {
        return success;
    }


}
