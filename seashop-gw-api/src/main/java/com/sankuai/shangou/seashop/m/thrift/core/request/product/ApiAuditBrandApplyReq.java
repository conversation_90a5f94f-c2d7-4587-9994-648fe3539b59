package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/07 14:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "品牌申请审核入参")
public class ApiAuditBrandApplyReq extends BaseParamReq {

    @FieldDoc(description = "申请记录id", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "审核是否通过", requiredness = Requiredness.REQUIRED)
    private Boolean passFlag;

    @FieldDoc(description = "拒绝原因", requiredness = Requiredness.NONE)
    private String rejectReason;


}
