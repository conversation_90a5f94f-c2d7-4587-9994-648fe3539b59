package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @description: 商家操作请求入参
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "商家操作请求入参")
public class ApiCmdMemberReq extends BaseParamReq {
    /**
     * 商家ID
     */
    @FieldDoc(description = "商家ID")
    private Long id;

    /**
     * 密码
     */
    @FieldDoc(description = "密码")
    private String password;


    /**
     * 标签ID
     */
    @FieldDoc(description = "标签ID")
    private List<Long> labels;


}
