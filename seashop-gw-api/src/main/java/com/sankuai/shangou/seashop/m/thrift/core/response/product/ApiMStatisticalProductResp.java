package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @description：
 * @author： liweisong
 * @create： 2023/12/11 9:37
 */
@Data
@TypeDoc(description = "平台首页统计商品信息")
public class ApiMStatisticalProductResp extends BaseThriftDto {

    @FieldDoc(description = "商品总数")
    private Integer totalNum;

    @FieldDoc(description = "出售中")
    private Integer productsOnSale;

    @FieldDoc(description = "商品待审核")
    private Integer productsWaitForAuditing;

    @FieldDoc(description = "授权品牌待审核")
    private Integer productsBrands;

    @FieldDoc(description = "商品评论数")
    private Integer productsComment;


}
