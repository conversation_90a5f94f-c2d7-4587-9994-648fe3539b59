package com.sankuai.shangou.seashop.m.thrift.core.response.product.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.dto.SpecDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:46
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "商品详情sku")
public class ApiProductDetailSkuDto extends BaseThriftDto {

    @FieldDoc(description = "skuId 新增时不填/填0")
    private Long skuAutoId;

    @FieldDoc(description = "sku 拼接id 规格1ID_规格2ID_规格3_ID")
    private String skuId;

    @FieldDoc(description = "销售价")
    private BigDecimal salePrice;

    @FieldDoc(description = "库存")
    private Long stock;

    @FieldDoc(description = "货号")
    private String skuCode;

    @FieldDoc(description = "警戒库存")
    private Long safeStock;

    @FieldDoc(description = "计量单位")
    private String measureUnit;

    @FieldDoc(description = "规格值集合")
    private List<SpecDto> specList;


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


}
