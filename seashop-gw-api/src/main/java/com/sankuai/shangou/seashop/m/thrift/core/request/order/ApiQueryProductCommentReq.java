package com.sankuai.shangou.seashop.m.thrift.core.request.order;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/04 15:54
 */
@Data
@ToString
@TypeDoc(description = "查询订单评论入参")
public class ApiQueryProductCommentReq extends BasePageReq {

    @FieldDoc(description = "商品名称", requiredness = Requiredness.NONE)
    private String productName;

    @FieldDoc(description = "评分 1-5分", requiredness = Requiredness.NONE)
    private Integer reviewMark;

    @FieldDoc(description = "是否追加了评论", requiredness = Requiredness.NONE)
    private Boolean hasAppend;

    @FieldDoc(description = "评论状态 0-全部 1-未处理", requiredness = Requiredness.NONE)
    private Integer replyStatus;

    @FieldDoc(description = "评分范围", requiredness = Requiredness.NONE)
    private List<Integer> reviewMarkRange;

    @FieldDoc(description = "商品id", requiredness = Requiredness.NONE)
    private String productId;

    @FieldDoc(description = "规格ID", requiredness = Requiredness.NONE)
    private String skuId;

    @FieldDoc(description = "订单号", requiredness = Requiredness.NONE)
    private String orderId;

    @FieldDoc(description = "商家账号", requiredness = Requiredness.NONE)
    private String userName;

    @FieldDoc(description = "商家手机号", requiredness = Requiredness.NONE)
    private String userMobile;

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.NONE)
    private String shopId;

    @Override
    public void checkParameter() {
        productId = StrUtil.emptyToDefault(productId, null);
        AssertUtil.throwInvalidParamIfTrue(productId != null && !NumberUtil.isLong(productId), "请输入正确的商品id");
        shopId = StrUtil.emptyToDefault(shopId, null);
        AssertUtil.throwInvalidParamIfTrue(shopId != null && !NumberUtil.isLong(shopId), "请输入正确的店铺id");
    }


}
