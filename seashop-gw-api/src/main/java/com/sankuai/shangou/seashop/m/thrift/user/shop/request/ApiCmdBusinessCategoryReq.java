package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description: 店铺类目修改请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "店铺类目修改请求入参")
public class ApiCmdBusinessCategoryReq extends BaseParamReq {

    @FieldDoc(description = "店铺类目ID")
    public Long id;
    @FieldDoc(description = "分佣比例")
    private BigDecimal commissionRate;
    @FieldDoc(description = "保证金")
    private BigDecimal bond;


    public String getCommissionRateString() {
        return this.bigDecimal2String(this.commissionRate);
    }


    public void setCommissionRateString(String commissionRate) {
        this.commissionRate = this.string2BigDecimal(commissionRate);
    }


    public String getBondString() {
        return this.bigDecimal2String(this.bond);
    }


    public void setBondString(String bond) {
        this.bond = this.string2BigDecimal(bond);
    }
}
