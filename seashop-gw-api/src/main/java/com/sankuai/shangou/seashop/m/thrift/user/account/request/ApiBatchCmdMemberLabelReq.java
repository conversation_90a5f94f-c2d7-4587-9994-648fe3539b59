package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: 商家操作请求入参
 * @author: LXH
 **/
@Data
@NoArgsConstructor
@TypeDoc(description = "商家操作请求入参")
public class ApiBatchCmdMemberLabelReq extends BaseParamReq {
    /**
     * 商家ID
     */
    @FieldDoc(description = "商家ID")
    private List<Long> ids;


    /**
     * 标签ID
     */
    @FieldDoc(description = "标签ID")
    private List<Long> labels;


    /**
     * 操作类型
     */
    @FieldDoc(description = "操作类型 0:删除 1:新增")
    private Integer operatorType;


    public ApiBatchCmdMemberLabelReq(List<Long> ids, List<Long> labels, Integer operatorType) {
        this.ids = ids;
        this.labels = labels;
        this.operatorType = operatorType;
    }
}
