package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "个人供应商修改入参")
public class ApiPersonalCmdShopReq extends BaseParamReq {
    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    @NotNull(message = "店铺ID为必填项")
    private Long shopId;
    @FieldDoc(description = "店铺名称", requiredness = Requiredness.REQUIRED)
    @NotBlank(message = "店铺名称为必填项")
    @Size(max = 20, message = "店铺名称最多20个字符")
    private String shopName;
    //    店铺个人信息
    @FieldDoc(description = "店铺联系人", requiredness = Requiredness.REQUIRED)
    private String contactsName;
    //    身份证信息
    @FieldDoc(description = " 身份证号", requiredness = Requiredness.REQUIRED)
    private String idCard;
    @FieldDoc(description = "身份证正面照", requiredness = Requiredness.REQUIRED)
    private String idCardUrl;
    @FieldDoc(description = "身份证反面照", requiredness = Requiredness.REQUIRED)
    private String idCardUrl2;
    //    店铺账户信息
    @FieldDoc(description = "店铺账户", requiredness = Requiredness.REQUIRED)
    private String shopAccount;
    @FieldDoc(description = "店铺账户姓名", requiredness = Requiredness.REQUIRED)
    private String accountName;
    @FieldDoc(description = "联系人手机", requiredness = Requiredness.REQUIRED)
    private String contactsPhone;
    @FieldDoc(description = "联系人邮箱", requiredness = Requiredness.REQUIRED)
    private String contactsEmail;
    //    公司信息
    @FieldDoc(description = "公司名称", requiredness = Requiredness.REQUIRED)
    private String companyName;
    @FieldDoc(description = "公司所在地ID", requiredness = Requiredness.REQUIRED)
    private Integer companyRegionId;
    @FieldDoc(description = "公司详细地址", requiredness = Requiredness.REQUIRED)
    private String companyAddress;
    @FieldDoc(description = "头像", requiredness = Requiredness.REQUIRED)
    private String logo;
    @FieldDoc(description = "入驻表单", requiredness = Requiredness.REQUIRED)
    private String formData;


}
