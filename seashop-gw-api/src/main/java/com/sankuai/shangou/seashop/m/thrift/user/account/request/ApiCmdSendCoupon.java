package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "会员操作请求对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiCmdSendCoupon extends BaseParamReq {
    // 优惠券ID列表
    @FieldDoc(description = "优惠券ID列表")
    private List<Long> couponIds;
    // 会员ID列表
    @FieldDoc(description = "会员ID列表")
    private List<Long> memberIds;
    @FieldDoc(description = "消息ID")
    private Long msgId;


}
