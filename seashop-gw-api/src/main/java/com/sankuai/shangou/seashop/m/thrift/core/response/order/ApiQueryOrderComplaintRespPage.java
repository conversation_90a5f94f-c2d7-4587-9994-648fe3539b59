package com.sankuai.shangou.seashop.m.thrift.core.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description：
 * @author： liweisong
 * @create： 2023/11/21 19:22
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "申请记录返回列表分页")
public class ApiQueryOrderComplaintRespPage {

    @FieldDoc(description = "申请记录信息返回列表")
    private BasePageResp<ApiQueryOrderComplaintResp> pageResp;


}
