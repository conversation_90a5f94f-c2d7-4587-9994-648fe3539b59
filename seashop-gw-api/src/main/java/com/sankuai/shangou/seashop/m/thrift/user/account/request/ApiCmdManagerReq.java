package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

/**
 * @description: 编辑管理员请求入参
 * @author: LXH
 **/
@Data
public class ApiCmdManagerReq extends BaseParamReq {

    @FieldDoc(description = "管理员ID")
    private Long id;
    @FieldDoc(description = "供应商id")
    private Long shopId;
    @FieldDoc(description = "用户名")
    private String userName;
    @FieldDoc(description = "密码")
    private String password;
    @FieldDoc(description = "手机号")
    private String cellphone;
    @FieldDoc(description = "权限组id")
    private Long roleId;
    @FieldDoc(description = "操作人ID")
    private Long operatorId;
    @FieldDoc(description = "操作人名称")
    private Long operatorName;
    @FieldDoc(description = "批量管理员ID")
    private List<Long> batchId;


}
