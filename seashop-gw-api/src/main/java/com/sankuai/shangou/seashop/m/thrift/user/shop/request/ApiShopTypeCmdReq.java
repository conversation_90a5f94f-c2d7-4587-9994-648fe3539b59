package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@Data
@TypeDoc(description = "供应商类型入参")
@NoArgsConstructor
@AllArgsConstructor
public class ApiShopTypeCmdReq extends BaseParamReq {
    @FieldDoc(description = "供应商类型 1商城店铺 2牵牛花店铺 3易九批店铺")
    private Integer shopType;

    @FieldDoc(description = "供应商ID")
    private Long shopId;


}
