package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiBrandApplyDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:39
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "品牌详情返回值")
public class ApiBrandApplyDetailResp {

    @FieldDoc(description = "品牌详情")
    private ApiBrandApplyDto result;


}
