package com.sankuai.shangou.seashop.m.thrift.core.response.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "订单统计返回值列表")
public class ApiOrderStatisticsDataResp extends BaseThriftDto {

    @FieldDoc(description = "日期")
    private String dateStr;

    @FieldDoc(description = "交易额")
    private BigDecimal amount;

    @FieldDoc(description = "描述")
    private String desc;


    public String getAmountString() {
        return this.bigDecimal2String(this.amount);
    }


    public void setAmountString(String amount) {
        this.amount = this.string2BigDecimal(amount);
    }


}
