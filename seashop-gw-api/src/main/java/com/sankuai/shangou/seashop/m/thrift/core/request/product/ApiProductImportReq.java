package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/05 17:11
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "商品导入入参")
public class ApiProductImportReq extends BaseParamReq {

    @FieldDoc(description = "文件路径", requiredness = Requiredness.REQUIRED)
    private String filePath;

    @FieldDoc(description = "销售状态 1-销售中 2-仓库中", requiredness = Requiredness.OPTIONAL)
    private Integer saleStatus;


}
