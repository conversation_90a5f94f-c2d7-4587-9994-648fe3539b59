package com.sankuai.shangou.seashop.m.thrift.user.shop.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@Data
@ToString
@TypeDoc(description = "店铺简单查询返回对象")
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopSimpleResp extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "等级名称")
    private String gradeName;

    @FieldDoc(description = "最大保证金")
    private BigDecimal maxCashDeposit;

    @FieldDoc(description = "店铺首页链接")
    private String link;

    @FieldDoc(description = "pc店铺首页链接")
    private String pcLink;

    @FieldDoc(description = "是否需要续签合同")
    private Boolean needRenew;


    public String getMaxCashDepositString() {
        return this.bigDecimal2String(this.maxCashDeposit);
    }


    public void setMaxCashDepositString(String maxCashDeposit) {
        this.maxCashDeposit = this.string2BigDecimal(maxCashDeposit);
    }


}
