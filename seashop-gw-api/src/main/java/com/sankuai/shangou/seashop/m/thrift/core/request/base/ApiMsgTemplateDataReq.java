package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseMsgTemplateReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/26 16:29
 */
@Data
@TypeDoc(description = "消息模版数据请求入参")
public class ApiMsgTemplateDataReq extends BaseParamReq {

    @FieldDoc(description = "模版集合")
    private List<BaseMsgTemplateReq> items;


}
