package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

@TypeDoc(description = "文章返回")
@Data
public class ApiBaseArticleRes extends BaseThriftDto {

    @FieldDoc(description = "文章id")
    private Long id;

    /**
     * 文章分类id
     */
    @FieldDoc(description = "文章分类id")
    private Long categoryId;

    /**
     * 文章分类名称
     */
    @FieldDoc(description = "文章分类名称")
    private String categoryName;
    /**
     * 标题
     */
    @FieldDoc(description = "标题")
    private String title;

    /**
     * icon url
     */
    @FieldDoc(description = "icon url")
    @JsonUrlFormat(deserializer = false)
    private String iconUrl;

    /**
     * 添加时间
     */
    @FieldDoc(description = "添加时间")
    private Date addDate;

    /**
     * 排序字段
     */
    @FieldDoc(description = "排序字段")
    private Long displaySequence;

    /**
     * 是否显示
     */
    @FieldDoc(description = "是否显示")
    private Boolean isRelease;

    /**
     * 文章内容
     */
    @FieldDoc(description = "文章内容")
    private String content;

    /**
     * 文章seo标题
     */
    @FieldDoc(description = "文章seo标题")
    private String seoTitle;

    /**
     * 文档seo详情
     */
    @FieldDoc(description = "文档seo详情")
    private String seoDescription;

    /**
     * 文章seo关键字
     */
    @FieldDoc(description = "文章seo关键字")
    private String seoKeywords;


    public Long getAddDateLong() {
        return this.date2Long(this.addDate);
    }


    public void setAddDateLong(Long addDate) {
        this.addDate = this.long2Date(addDate);
    }


}
