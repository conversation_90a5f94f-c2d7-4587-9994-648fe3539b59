package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/24 14:00
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询商品详情入参")
public class ApiProductQueryDetailReq extends BaseParamReq {

    @FieldDoc(description = "商品详情id", requiredness = Requiredness.REQUIRED)
    private Long productId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productId == null || productId <= 0, "商品id不能为空");
    }


}
