package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.m.thrift.core.dto.promotion.ApiExclusivePriceProductDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@TypeDoc(description = "专享价活动响应体")
@ToString
@Data
public class ApiExclusivePriceResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "活动名称")
    private String name;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "状态")
    private Integer status;

    @FieldDoc(description = "专享价商品列表")
    private List<ApiExclusivePriceProductDto> productList;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
