package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "图片对象")
@Data
public class ApiUpdatePhotoSpaceReq extends BaseThriftDto {

    @FieldDoc(description = "id", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "店铺id")
    private Long shopId;


    @FieldDoc(description = "图片名称", requiredness = Requiredness.REQUIRED)
    private String photoName;

    public void checkParameter() {


        if (this.id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (StringUtils.isEmpty(this.photoName)) {
            throw new IllegalArgumentException("图片名称不能为空");
        }

    }


}
