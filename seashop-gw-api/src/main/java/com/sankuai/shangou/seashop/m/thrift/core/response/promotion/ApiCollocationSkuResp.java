package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "组合购SKU返回值")
public class ApiCollocationSkuResp extends BaseThriftDto {
    @FieldDoc(description = "组合购商品SKU表主键ID")
    private Long id;

    @FieldDoc(description = "商品ID")
    private String productId;

    @FieldDoc(description = "商品SkuId")
    private String skuId;

    @FieldDoc(description = "规格")
    private String specName;

    @FieldDoc(description = "组合商品表ID")
    private Long colloProductId;

    @FieldDoc(description = "组合购价格")
    private BigDecimal price;

    @FieldDoc(description = "原始价格")
    private BigDecimal skuPirce;

    @FieldDoc(description = "库存")
    private Long stock;
}
