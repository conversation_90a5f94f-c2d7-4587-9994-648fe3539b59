package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "已结算明细列表请求参数")
public class ApiSettledItemCountQryReq extends BasePageReq {

    @FieldDoc(description = "结算id", requiredness = Requiredness.REQUIRED)
    private Long detailId;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @Override
    public void checkParameter() {
        if (null == this.detailId) {
            throw new InvalidParamException("结算id不能为空");
        }
        /*if (null == this.shopId) {
            throw new InvalidParamException("店铺id不能为空");
        }*/
    }


}
