package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


/**
 * @author： liweisong
 * @create： 2023/11/23 9:23
 */
@TypeDoc(description = "售后原因设置入参")
@Data
public class ApiUpdateRefundReasonReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "售后原因")
    private String afterSalesText;

    @FieldDoc(description = "排序")
    private Integer sequence;


    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (StringUtils.isEmpty(afterSalesText)) {
            throw new IllegalArgumentException("售后原因不能为空");
        }
    }
}
