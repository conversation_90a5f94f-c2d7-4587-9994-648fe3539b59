package com.sankuai.shangou.seashop.m.thrift.core.response.base;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:26
 */
@TypeDoc(description = "售后原因查询返参")
@Data
public class ApiQueryRefundReasonResp {

    @FieldDoc(description = "售后原因查询返参集合")
    private List<ApiQueryRefundReasonRespDto> reasonRespDtos;


}
