package com.sankuai.shangou.seashop.m.thrift.user.account.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 标签信息
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "标签信息")
@AllArgsConstructor
@NoArgsConstructor
public class ApiMemberResp extends BaseThriftDto {
    @FieldDoc(description = "id")
    private Long id;
    @FieldDoc(description = "用户名")
    private String userName;
    @FieldDoc(description = "昵称")
    private String nick;
    @FieldDoc(description = "性别")
    private Integer sex;
    @FieldDoc(description = "邮箱")
    private String email;
    @FieldDoc(description = "创建日期")
    private Date createDate;
    @FieldDoc(description = "省份ID")
    private Integer topRegionId;
    @FieldDoc(description = "省市ID")
    private Integer regionId;
    @FieldDoc(description = "真实姓名")
    private String realName;
    @FieldDoc(description = "手机号")
    private String cellPhone;
    @FieldDoc(description = "qq")
    private String qq;
    @FieldDoc(description = "地址")
    private String address;
    @FieldDoc(description = "是否禁用")
    private Boolean disabled;
    @FieldDoc(description = "最后登录时间")
    private Date lastLoginDate;
    @FieldDoc(description = "订单数量")
    private Integer orderNumber;
    @FieldDoc(description = "总充值")
    private BigDecimal totalAmount;
    @FieldDoc(description = "总消费金额（不排除退款）")
    private BigDecimal expenditure;
    @FieldDoc(description = "积分")
    private Integer points;
    @FieldDoc(description = "头像")
    private String photo;
    @FieldDoc(description = "商家父账号id")
    private Long parentSellerId;
    @FieldDoc(description = "备注")
    private String remark;
    @FieldDoc(description = "邀请人")
    private Long inviteUserId;
    @FieldDoc(description = "生日")
    private Date birthDay;
    @FieldDoc(description = "职业")
    private String occupation;
    @FieldDoc(description = "净消费")
    private BigDecimal netAmount;
    @FieldDoc(description = "最后消费时间")
    private Date lastConsumptionTime;
    @FieldDoc(description = "平台")
    private Integer platform;
    @FieldDoc(description = "是否不提示入驻为供应商")
    private Boolean isNoticeJoin;
    @FieldDoc(description = "是否注销")
    private Boolean isLogOut;
    @FieldDoc(description = "注销时间")
    private Date logOutTime;
    @FieldDoc(description = "注册来源 0表示本系统，1表示牵牛花")
    private Integer registerSource;
    @FieldDoc(description = "微信OpenId")
    private String openId;
    @FieldDoc(description = "创建日期")
    private Date createTime;
    @FieldDoc(description = "更新日期")
    private Date updateTime;
    @FieldDoc(description = "近3个月消费次数")
    private Long consumptionFrequency;
    @FieldDoc(description = "近3个月净消费金额")
    private BigDecimal netConsumptionAmount;
    @FieldDoc(description = "是否为供应商")
    private Boolean seller;
    @FieldDoc(description = "标签")
    private String label;


    public Long getCreateDateLong() {
        return this.date2Long(this.createDate);
    }


    public void setCreateDateLong(Long createDate) {
        this.createDate = this.long2Date(createDate);
    }


    public Long getLastLoginDateLong() {
        return this.date2Long(this.lastLoginDate);
    }


    public void setLastLoginDateLong(Long lastLoginDate) {
        this.lastLoginDate = this.long2Date(lastLoginDate);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    public String getExpenditureString() {
        return this.bigDecimal2String(this.expenditure);
    }


    public void setExpenditureString(String expenditure) {
        this.expenditure = this.string2BigDecimal(expenditure);
    }


    public Long getBirthDayLong() {
        return this.date2Long(this.birthDay);
    }


    public void setBirthDayLong(Long birthDay) {
        this.birthDay = this.long2Date(birthDay);
    }


    public String getNetAmountString() {
        return this.bigDecimal2String(this.netAmount);
    }


    public void setNetAmountString(String netAmount) {
        this.netAmount = this.string2BigDecimal(netAmount);
    }


    public Long getLastConsumptionTimeLong() {
        return this.date2Long(this.lastConsumptionTime);
    }


    public void setLastConsumptionTimeLong(Long lastConsumptionTime) {
        this.lastConsumptionTime = this.long2Date(lastConsumptionTime);
    }


    public Long getLogOutTimeLong() {
        return this.date2Long(this.logOutTime);
    }


    public void setLogOutTimeLong(Long logOutTime) {
        this.logOutTime = this.long2Date(logOutTime);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


    public String getNetConsumptionAmountString() {
        return this.bigDecimal2String(this.netConsumptionAmount);
    }


    public void setNetConsumptionAmountString(String netConsumptionAmount) {
        this.netConsumptionAmount = this.string2BigDecimal(netConsumptionAmount);
    }


}
