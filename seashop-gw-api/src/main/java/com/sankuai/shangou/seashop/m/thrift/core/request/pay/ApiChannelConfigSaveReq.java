package com.sankuai.shangou.seashop.m.thrift.core.request.pay;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.pay.dto.ApiChannelConfigDto;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保存渠道配置请求对象")
public class ApiChannelConfigSaveReq extends BaseParamReq {

    @FieldDoc(description = "支付渠道 1：汇付天下", requiredness = Requiredness.REQUIRED)
    private Integer paymentChannel;

    @FieldDoc(description = "配置信息列表", requiredness = Requiredness.REQUIRED)
    private List<ApiChannelConfigDto> configList;

    @Override
    public void checkParameter() {
        if (this.paymentChannel == null) {
            throw new InvalidParamException("paymentChannel不能为空");
        }
        if (CollUtil.isEmpty(this.configList)) {
            throw new InvalidParamException("configList不能为空");
        }
    }


}
