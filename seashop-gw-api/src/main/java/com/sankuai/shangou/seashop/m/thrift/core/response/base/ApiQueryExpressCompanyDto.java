package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.Date;

@TypeDoc(description = "快递设置返回参数")
@Data
public class ApiQueryExpressCompanyDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "快递名称")
    private String name;

    @FieldDoc(description = "淘宝编号")
    private String taobaoCode;

    @FieldDoc(description = "快递100对应物流编号")
    private String kuaidi100Code;

    @FieldDoc(description = "快递鸟物流公司编号")
    private String kuaidiniaoCode;

    @FieldDoc(description = "快递面单宽度")
    private Integer width;

    @FieldDoc(description = "快递面单高度")
    private Integer height;

    @FieldDoc(description = "快递公司logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;

    @FieldDoc(description = "快递公司面单背景图片")
    @JsonUrlFormat(deserializer = false)
    private String backgroundImage;

    @FieldDoc(description = "快递公司状态（0：正常，1：删除）")
    private Integer status;

    @FieldDoc(description = "创建日期")
    private Date createDate;

    @FieldDoc(description = "旺店通code")
    private String wangdiantongCode;

    @FieldDoc(description = "聚水潭code")
    private String jushuitanCode;

    @FieldDoc(description = "菠萝派code")
    private String boluopaiCode;

    @FieldDoc(description = "美团code")
    private String meituanCode;


}
