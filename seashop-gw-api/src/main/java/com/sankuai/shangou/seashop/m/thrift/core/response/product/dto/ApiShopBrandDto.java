package com.sankuai.shangou.seashop.m.thrift.core.response.product.dto;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/08 11:37
 */
@TypeDoc(
    description = "商家品牌返回值"
)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ApiShopBrandDto extends BaseThriftDto {

    @FieldDoc(description = "商家品牌关联表id")
    private Long id;

    @FieldDoc(description = "商家id")
    private Long shopId;

    @FieldDoc(description = "品牌id")
    private Long brandId;

    @FieldDoc(description = "品牌名称")
    private String brandName;

    @FieldDoc(description = "排序")
    private Long displaySequence;

    @FieldDoc(description = "logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;

    @FieldDoc(description = "品牌简介")
    private String description;


}
