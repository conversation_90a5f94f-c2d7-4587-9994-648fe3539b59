package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 17:22
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "商品设置虚拟销量入参")
public class ApiProductUpdateVirtualSaleCountsReq extends BaseParamReq {

    @FieldDoc(description = "商品id的集合", requiredness = Requiredness.REQUIRED)
    private List<Long> productIdList;

    @FieldDoc(description = "虚拟销量类型 1-固定值 2-随机数", requiredness = Requiredness.REQUIRED)
    private Integer virtualSaleCountsTypeCode;

    @FieldDoc(description = "固定数", requiredness = Requiredness.OPTIONAL)
    private Long fixedNum;

    @FieldDoc(description = "最小随机数", requiredness = Requiredness.OPTIONAL)
    private Long minRandomNum;

    @FieldDoc(description = "最大随机数", requiredness = Requiredness.OPTIONAL)
    private Long maxRandomNum;


}
