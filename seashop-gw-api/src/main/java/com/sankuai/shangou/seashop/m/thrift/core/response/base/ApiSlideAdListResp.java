package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@TypeDoc(description = "轮播图响应体")
@ToString
@Data
public class ApiSlideAdListResp {

    @FieldDoc(description = "轮播图列表")
    private List<ApiSlideAdResp> list;


}
