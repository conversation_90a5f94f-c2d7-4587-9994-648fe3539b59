package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "发送邮件消息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiSendEmailMsgReq extends BaseParamReq {
    @FieldDoc(description = "标签ID")
    private List<Long> labelId;
    @FieldDoc(description = "是否发送全部")
    private Boolean sendAll;
    @FieldDoc(description = "标题")
    private String title;
    @FieldDoc(description = "内容")
    private String content;


}
