package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/13 14:23
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "店铺分类树节点")
public class ApiShopCategoryTreeResp extends BaseThriftDto {

    @FieldDoc(description = "类目集合")
    private String result;


}
