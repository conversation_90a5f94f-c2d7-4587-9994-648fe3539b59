package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(
    description = "图片对象返回结果"
)
public class ApiBasePhotoSpaceRes extends BaseThriftDto {
    private Long id;

    @FieldDoc(description = "分类id")
    private Long photoCategoryId;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "图片名称")
    private String photoName;

    @FieldDoc(description = "图片路径")
    @JsonUrlFormat(deserializer = false)
    private String photoPath;

    @FieldDoc(description = "图片大小")
    private Long fileSize;

    @FieldDoc(description = "上传时间")
    private Date uploadTime;

    @FieldDoc(description = "最后修改时间")
    private Date lastupdateTime;


    public Long getUploadTimeLong() {
        return this.date2Long(this.uploadTime);
    }


    public void setUploadTimeLong(Long uploadTime) {
        this.uploadTime = this.long2Date(uploadTime);
    }


    public Long getLastupdateTimeLong() {
        return this.date2Long(this.lastupdateTime);
    }


    public void setLastupdateTimeLong(Long lastupdateTime) {
        this.lastupdateTime = this.long2Date(lastupdateTime);
    }
}
