package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保证金明细查询条件对象")
public class ApiCashDepositDetailQueryReq extends BasePageReq {

    @FieldDoc(description = "保证金ID", requiredness = Requiredness.REQUIRED)
    private String cashDepositId;

    @FieldDoc(description = "操作人")
    private String operator;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "1平台，2供应商，3商家端")
    private Integer sourceFrom;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.cashDepositId)) {
            throw new InvalidParamException("cashDepositId不能为空");
        }
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
