package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/30/030
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "扩展了营销条件的查询商品入参")
public class ApiQueryProductPromotionExtReq extends ApiQueryProductReq {

    @FieldDoc(description = "营销条件入参")
    private ApiQueryPromotionReq promotion;


}
