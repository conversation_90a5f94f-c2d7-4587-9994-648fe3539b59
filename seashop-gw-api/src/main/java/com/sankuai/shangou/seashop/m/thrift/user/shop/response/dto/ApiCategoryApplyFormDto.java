package com.sankuai.shangou.seashop.m.thrift.user.shop.response.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "类目列表对象")
public class ApiCategoryApplyFormDto extends BaseThriftDto {

    @FieldDoc(description = "表单id")
    private Long formId;

    @FieldDoc(description = "表单名称")
    private String formName;

    @FieldDoc(description = "类目id")
    private Long categoryId;

    @FieldDoc(description = "表单数据")
    private List<ApiCategoryApplyFormFieldDto> fieldList;


}
