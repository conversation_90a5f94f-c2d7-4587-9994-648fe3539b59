package com.sankuai.shangou.seashop.m.thrift.user.shop.response.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/29 20:26
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "类目列表对象")
public class ApiCategoryDto extends BaseThriftDto {

    /**
     * 主键
     */
    @FieldDoc(description = "id")
    private Long id;

    /**
     * 类目名称
     */
    @FieldDoc(description = "类目名称")
    private String name;

    /**
     * 类目图标
     */
    @FieldDoc(description = "类目图标")
    private String icon;

    /**
     * 排序
     */
    @FieldDoc(description = "排序")
    private Long displaySequence;

    /**
     * 上级类目id
     */
    @FieldDoc(description = "上级类目id")
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    @FieldDoc(description = "分佣比例")
    private BigDecimal commissionRate;

    /**
     * 类目的深度
     */
    @FieldDoc(description = "类目的深度")
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    @FieldDoc(description = "类目的路径")
    private String path;

    /**
     * 自定义表单Id
     */
    @FieldDoc(description = "自定义表单Id")
    private Long customFormId;

    /**
     * 类目全路径
     */
    @FieldDoc(description = "类目全路径名称")
    private String fullCategoryName;

    /**
     * 保证金
     */
    @FieldDoc(description = "保证金")
    private BigDecimal cashDeposit;


    public String getCommissionRateString() {
        return this.bigDecimal2String(this.commissionRate);
    }


    public void setCommissionRateString(String commissionRate) {
        this.commissionRate = this.string2BigDecimal(commissionRate);
    }


    public String getCashDepositString() {
        return this.bigDecimal2String(this.cashDeposit);
    }


    public void setCashDepositString(String cashDeposit) {
        this.cashDeposit = this.string2BigDecimal(cashDeposit);
    }
}
