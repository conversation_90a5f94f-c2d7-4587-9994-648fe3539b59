package com.sankuai.shangou.seashop.m.thrift.user.shop.response.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@TypeDoc(description = "购物车商品返回对象")
@ToString
@Data
public class ApiQueryFreightTemplateDto extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "运费模板名称")
    private String name;

    @FieldDoc(description = "宝贝发货地")
    private Integer sourceAddress;

    @FieldDoc(description = "发送时间")
    private String sendTime;

    @FieldDoc(description = "是否商家负责运费")
    private Integer whetherFree;

    @FieldDoc(description = "定价方法(按体积、重量计算）")
    private Integer valuationMethod;

    @FieldDoc(description = "运送类型（物流、快递）")
    private Integer shippingMethod;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "非销售区域是否隐藏")
    private Boolean nonSalesAreaHide;

    @FieldDoc(description = "创建时间")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    private Date updateTime;

    @FieldDoc(description = "关联商品数量")
    private Integer productNum;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


}
