package com.sankuai.shangou.seashop.m.thrift.user.shop.response;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.dto.ApiCategoryApplyFormDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 供应商详情返回参数
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "供应商信息返回参数")
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopDetailResp extends BaseThriftDto {

    @FieldDoc(description = "供应商ID")
    private Long id;
    @FieldDoc(description = "供应商等级")
    private Long gradeId;
    @FieldDoc(description = "供应商名称")
    private String shopName;
    @FieldDoc(description = "是否官方自营店")
    private Boolean isSelf;
    @FieldDoc(description = "店铺状态 0默认 1不可用 2待审核 3待付款 4审核拒绝 5待确认 6冻结 7开启 -1已过期")
    private Integer shopStatus;
    @FieldDoc(description = "汇付操作状态 0默认 1不可用 8待审核 9审核中 10审核通过 11审核失败")
    private Integer adapayStatus;
    @FieldDoc(description = "平台审核状态 0默认 1不可用 2待审核 4审核拒绝 7审核通过")
    private Integer plateStatus;
    @FieldDoc(description = "店铺创建时间")
    private Date createDate;
    @FieldDoc(description = "店铺过期时间")
    private Date endDate;

    @FieldDoc(description = "身份证号码")
    private String idCard;
    @FieldDoc(description = "身份证正面")
    @JsonUrlFormat(deserializer = false)
    private String idCardUrl;
    @FieldDoc(description = "身份证反面")
    @JsonUrlFormat(deserializer = false)
    private String idCardUrl2;

    @FieldDoc(description = "身份证有效期类型")
    private Integer idCardExpireType;
    @FieldDoc(description = "身份证有效期开始")
    private Date idCardStartDate;
    @FieldDoc(description = "身份证有效期结束")
    private Date idCardEndDate;

    @FieldDoc(description = "联系人姓名")
    private String contactsName;
    @FieldDoc(description = "联系人电话")
    private String contactsPhone;
    @FieldDoc(description = "联系人Email")
    private String contactsEmail;
    @FieldDoc(description = "银行开户名")
    private String bankAccountName;
    @FieldDoc(description = "银行账号")
    private String bankAccountNumber;
    @FieldDoc(description = "银行名称")
    private String bankName;
    @FieldDoc(description = "支行联行号")
    private String bankCode;
    @FieldDoc(description = "开户银行所在地")
    private Integer bankRegionId;
    @FieldDoc(description = "开户银行类型（1对公，2对私）")
    private Integer bankType;
    @FieldDoc(description = "开户银行许可证")
    @JsonUrlFormat(deserializer = false)
    private String bankPhoto;
    @FieldDoc(description = "商家发货人名称")
    private String senderName;
    @FieldDoc(description = "商家发货人地址")
    private String senderAddress;
    @FieldDoc(description = "商家发货人电话")
    private String senderPhone;
    @FieldDoc(description = "是否缴纳保证金")
    private Boolean whetherPayBond;
    @FieldDoc(description = "是否签署协议")
    private Boolean whetherAgreement;
    @FieldDoc(description = "是否是需要补充资料", requiredness = Requiredness.REQUIRED)
    private Boolean whetherSupply;
    @FieldDoc(description = "是否需要续签合同", requiredness = Requiredness.REQUIRED)
    private Boolean whetherAgainSign;
    @FieldDoc(description = "是否官方自营", requiredness = Requiredness.REQUIRED)
    private Boolean whetherSelf;


    @FieldDoc(description = "公司名称(个人姓名)")
    private String companyName;
    @FieldDoc(description = "公司省市区(个人地址)")
    private Integer companyRegionId;
    @FieldDoc(description = "公司地址(个人地址)")
    private String companyAddress;
    @FieldDoc(description = "公司电话(个人电话)")
    private String companyPhone;
    @FieldDoc(description = "公司员工数量")
    private Integer companyEmployeeCount;
    @FieldDoc(description = "公司注册资金")
    private BigDecimal companyRegisteredCapital;
    @FieldDoc(description = "营业执照号")
    private String businessLicenseNumber;
    @FieldDoc(description = "营业执照")
    @JsonUrlFormat(deserializer = false)
    private String businessLicenseNumberPhoto;
    @FieldDoc(description = "营业执照所在地")
    private Integer businessLicenseRegionId;
    @FieldDoc(description = "营业执照有效期开始")
    private Date businessLicenseStart;
    @FieldDoc(description = "营业执照有效期")
    private Date businessLicenseEnd;
    @FieldDoc(description = "法定经营范围")
    private String businessSphere;
    @FieldDoc(description = "组织机构代码")
    private String organizationCode;
    @FieldDoc(description = "组织机构执照")
    private String organizationCodePhoto;
    @FieldDoc(description = "税务登记证")
    private String taxRegistrationCertificate;
    @FieldDoc(description = "税务登记证号")
    private String taxpayerId;
    @FieldDoc(description = "纳税人识别号")
    private String taxRegistrationCertificatePhoto;
    @FieldDoc(description = "法人代表")
    private String legalPerson;
    @FieldDoc(description = "公司成立日期")
    private Date companyFoundingDate;
    @FieldDoc(description = "公司类型")
    private String companyType;
    @FieldDoc(description = "公司联系电话（用作公司售后电话）")
    private String contactPhone;
    @FieldDoc(description = "执照地址")
    @JsonUrlFormat(deserializer = false)
    private String licenceCertAddr;
    @FieldDoc(description = "财务负责人")
    private String financeChief;
    @FieldDoc(description = "财务负责人联系方式")
    private String financeChiefPhone;
    @FieldDoc(description = "服务商简介")
    private String introduct;
    @FieldDoc(description = "经营类目")
    private List<ApiBusinessCategoryResp> businessCategory;
    @FieldDoc(description = "表单数据")
    private List<ApiCategoryApplyFormDto> fieldList;
    @FieldDoc(description = "业务类型 1个人供应商 0企业供应商")
    private Integer businessType;
    @FieldDoc(description = "店铺账户", requiredness = Requiredness.REQUIRED)
    private String shopAccount;
    @FieldDoc(description = "自定义数据", requiredness = Requiredness.REQUIRED)
    private String formData;
    @FieldDoc(description = "logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;
    @FieldDoc(description = "公司省市区名称")
    private String companyRegionName;
    @FieldDoc(description = "开户银行所在地名称")
    private String bankRegionName;
    @FieldDoc(description = "管理员姓名")
    private String realName;
    @FieldDoc(description = "管理员手机")
    private String memberPhone;
    @FieldDoc(description = "管理员邮箱")
    private String memberEmail;


    @FieldDoc(description = "省")
    private Long provinceId;
    @FieldDoc(description = "省")
    private String provinceName;
    @FieldDoc(description = "市")
    private Long cityId;
    @FieldDoc(description = "市")
    private String cityName;
    @FieldDoc(description = "区")
    private Long countyId;
    @FieldDoc(description = "区")
    private String countyName;
    @FieldDoc(description = "乡镇")
    private String townIds;
    @FieldDoc(description = "乡镇")
    private String townsNames;

    public Long getCreateDateLong() {
        return this.date2Long(this.createDate);
    }


    public void setCreateDateLong(Long createDate) {
        this.createDate = this.long2Date(createDate);
    }


    public Long getEndDateLong() {
        return this.date2Long(this.endDate);
    }


    public void setEndDateLong(Long endDate) {
        this.endDate = this.long2Date(endDate);
    }


    public String getCompanyRegisteredCapitalString() {
        return this.bigDecimal2String(this.companyRegisteredCapital);
    }


    public void setCompanyRegisteredCapitalString(String companyRegisteredCapital) {
        this.companyRegisteredCapital = this.string2BigDecimal(companyRegisteredCapital);
    }


    public Long getBusinessLicenseStartLong() {
        return this.date2Long(this.businessLicenseStart);
    }


    public void setBusinessLicenseStartLong(Long businessLicenseStart) {
        this.businessLicenseStart = this.long2Date(businessLicenseStart);
    }


    public Long getBusinessLicenseEndLong() {
        return this.date2Long(this.businessLicenseEnd);
    }


    public void setBusinessLicenseEndLong(Long businessLicenseEnd) {
        this.businessLicenseEnd = this.long2Date(businessLicenseEnd);
    }


    public Long getCompanyFoundingDateLong() {
        return this.date2Long(this.companyFoundingDate);
    }


    public void setCompanyFoundingDateLong(Long companyFoundingDate) {
        this.companyFoundingDate = this.long2Date(companyFoundingDate);
    }


    public Long getIdCardStartDateLong() {
        return this.date2Long(this.idCardStartDate);
    }


    public void setIdCardStartDateLong(Long idCardStartDate) {
        this.idCardStartDate = this.long2Date(idCardStartDate);
    }


    public Long getIdCardEndDateLong() {
        return this.date2Long(this.idCardEndDate);
    }


    public void setIdCardEndDateLong(Long idCardEndDate) {
        this.idCardEndDate = this.long2Date(idCardEndDate);
    }


}
