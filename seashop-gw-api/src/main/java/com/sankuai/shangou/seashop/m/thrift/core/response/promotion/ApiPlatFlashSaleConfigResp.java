package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@TypeDoc(description = "平台限时购配置响应体")
@Data
public class ApiPlatFlashSaleConfigResp extends BaseThriftDto {

    @FieldDoc(description = "是否需要审核（平台配置）")
    private Boolean needAuditFlag;


}
