package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@TypeDoc(description = "限时购响应体")
@Data
public class ApiFlashSaleSimpleResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "活动名称")
    private String title;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "产品ID")
    private String productId;

    @FieldDoc(description = "产品名称")
    private String productName;

    @FieldDoc(description = "跳转地址")
    private String urlPath;

    /**
     * 状态：1待审核,2进行中,3未通过,4已结束,5已取消,6未开始
     */
    @FieldDoc(description = "状态：1待审核 2进行中 3未通过 4已结束 5已取消 6未开始")
    private Integer status;

    @FieldDoc(description = "状态名称")
    private String statusName;

    @FieldDoc(description = "活动开始日期")
    private Date beginDate;

    @FieldDoc(description = "活动结束日期")
    private Date endDate;

    @FieldDoc(description = "限购数量")
    private Integer limitCount;

    @FieldDoc(description = "销售数量")
    private Integer saleCount;

    @FieldDoc(description = "最小价格")
    private BigDecimal minPrice;

    @FieldDoc(description = "前端是否显示")
    private Boolean frontFlag;


    public Long getBeginDateLong() {
        return this.date2Long(this.beginDate);
    }


    public void setBeginDateLong(Long beginDate) {
        this.beginDate = this.long2Date(beginDate);
    }


    public Long getEndDateLong() {
        return this.date2Long(this.endDate);
    }


    public void setEndDateLong(Long endDate) {
        this.endDate = this.long2Date(endDate);
    }


    public String getMinPriceString() {
        return this.bigDecimal2String(this.minPrice);
    }


    public void setMinPriceString(String minPrice) {
        this.minPrice = this.string2BigDecimal(minPrice);
    }


}
