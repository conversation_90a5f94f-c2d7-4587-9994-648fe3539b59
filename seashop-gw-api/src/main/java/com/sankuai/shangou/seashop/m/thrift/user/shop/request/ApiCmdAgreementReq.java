package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 供应商入驻协议请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商入驻协议请求入参")
public class ApiCmdAgreementReq extends BaseParamReq {
    @FieldDoc(description = "是否同意协议")
    private Boolean agree;
    @FieldDoc(description = "业务类型 1个人供应商 0企业供应商")
    private Integer businessType;
    @FieldDoc(description = "用户id")
    private Long userId;


}
