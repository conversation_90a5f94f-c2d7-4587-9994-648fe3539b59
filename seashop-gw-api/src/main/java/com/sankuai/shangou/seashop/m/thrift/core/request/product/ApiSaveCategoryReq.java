package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:05
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "新增类目入参")
public class ApiSaveCategoryReq extends BaseParamReq {

    /**
     * 主键
     */
    @FieldDoc(description = "类目id", requiredness = Requiredness.NONE)
    private Long id;

    /**
     * 类目名称
     */
    @FieldDoc(description = "类目名称", requiredness = Requiredness.REQUIRED)
    private String name;

    /**
     * 类目图标
     */
    @FieldDoc(description = "类目图标", requiredness = Requiredness.NONE)
    @JsonUrlFormat(deserializer = false)
    private String icon;

    /**
     * 上级类目id
     */
    @FieldDoc(description = "上级类目id 编辑是不可修改", requiredness = Requiredness.NONE)
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    @FieldDoc(description = "分佣比例 创建三级类目时才需要设置", requiredness = Requiredness.NONE)
    private BigDecimal commissionRate;


    public String getCommissionRateString() {
        return this.bigDecimal2String(this.commissionRate);
    }


    public void setCommissionRateString(String commissionRate) {
        this.commissionRate = this.string2BigDecimal(commissionRate);
    }
}
