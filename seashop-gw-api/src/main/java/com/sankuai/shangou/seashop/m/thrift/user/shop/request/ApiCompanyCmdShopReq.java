package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "个人供应商修改入参")
public class ApiCompanyCmdShopReq extends BaseParamReq {
    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    @NotNull(message = "店铺ID为必填项")
    private Long shopId;
    @FieldDoc(description = "店铺名称", requiredness = Requiredness.REQUIRED)
    @NotBlank(message = "店铺名称为必填项")
    @Size(max = 20, message = "店铺名称最多20个字符")
    private String shopName;
    //    公司信息
    @FieldDoc(description = "公司名称", requiredness = Requiredness.REQUIRED)
    private String companyName;
    @FieldDoc(description = "公司所在地ID", requiredness = Requiredness.REQUIRED)
    private Integer companyRegionId;
    @FieldDoc(description = "公司详细地址", requiredness = Requiredness.REQUIRED)
    private String companyAddress;
    @FieldDoc(description = "公司联系方式", requiredness = Requiredness.REQUIRED)
    private String companyPhone;
    @FieldDoc(description = "员工总数", requiredness = Requiredness.REQUIRED)
    private Integer companyEmployeeCount;
    @FieldDoc(description = "注册资金", requiredness = Requiredness.REQUIRED)
    private BigDecimal companyRegisteredCapital;
    //    法人信息(店铺个人信息)
    @FieldDoc(description = "店铺个人信息", requiredness = Requiredness.REQUIRED)
    private String contactsName;
    //    法人信息(店铺个人信息)
    @FieldDoc(description = "公司法人", requiredness = Requiredness.REQUIRED)
    private String legalPerson;
    //    身份证信息
    @FieldDoc(description = " 身份证号", requiredness = Requiredness.REQUIRED)
    private String idCard;
    @FieldDoc(description = "份证有效期开始日期", requiredness = Requiredness.REQUIRED)
    private Date idCardStartDate;
    @FieldDoc(description = "身份证有效期结束日期", requiredness = Requiredness.REQUIRED)
    private Date idCardEndDate;
    @FieldDoc(description = "身份证正面照", requiredness = Requiredness.REQUIRED)
    private String idCardUrl;
    @FieldDoc(description = "身份证反面照", requiredness = Requiredness.REQUIRED)
    private String idCardUrl2;
    //    营业执照信息
    @FieldDoc(description = "营业执照号", requiredness = Requiredness.REQUIRED)
    private String businessLicenseNumber;
    @FieldDoc(description = "营业执照URL", requiredness = Requiredness.REQUIRED)
    private String businessLicenseNumberPhoto;
    @FieldDoc(description = "营业执照所在地", requiredness = Requiredness.REQUIRED)
    private Integer businessLicenseRegionId;
    @FieldDoc(description = "营业执照起始有效期", requiredness = Requiredness.REQUIRED)
    private Date businessLicenseStart;
    @FieldDoc(description = "营业执照截止有效期", requiredness = Requiredness.REQUIRED)
    private Date businessLicenseEnd;
    @FieldDoc(description = "法定经营范围", requiredness = Requiredness.REQUIRED)
    private String businessSphere;

    //    店铺账户信息
    @FieldDoc(description = "店铺账户", requiredness = Requiredness.REQUIRED)
    private String shopAccount;
    @FieldDoc(description = "店铺账户姓名", requiredness = Requiredness.REQUIRED)
    private String accountName;
    @FieldDoc(description = "联系人手机", requiredness = Requiredness.REQUIRED)
    private String contactsPhone;
    @FieldDoc(description = "联系人邮箱", requiredness = Requiredness.REQUIRED)
    private String contactsEmail;
    @FieldDoc(description = "开户行", requiredness = Requiredness.REQUIRED)
    private String bankPhoto;
    @FieldDoc(description = "头像", requiredness = Requiredness.REQUIRED)
    private String logo;
    @FieldDoc(description = "入驻表单", requiredness = Requiredness.REQUIRED)
    private String formData;
    @FieldDoc(description = "身份证有效期类型")
    private Integer idCardExpireType;

    public String getCompanyRegisteredCapitalString() {
        return this.bigDecimal2String(this.companyRegisteredCapital);
    }


    public void setCompanyRegisteredCapitalString(String companyRegisteredCapital) {
        this.companyRegisteredCapital = this.string2BigDecimal(companyRegisteredCapital);
    }


    public Long getIdCardStartDateLong() {
        return this.date2Long(this.idCardStartDate);
    }


    public void setIdCardStartDateLong(Long idCardStartDate) {
        this.idCardStartDate = this.long2Date(idCardStartDate);
    }


    public Long getIdCardEndDateLong() {
        return this.date2Long(this.idCardEndDate);
    }


    public void setIdCardEndDateLong(Long idCardEndDate) {
        this.idCardEndDate = this.long2Date(idCardEndDate);
    }


    public Long getBusinessLicenseStartLong() {
        return this.date2Long(this.businessLicenseStart);
    }


    public void setBusinessLicenseStartLong(Long businessLicenseStart) {
        this.businessLicenseStart = this.long2Date(businessLicenseStart);
    }


    public Long getBusinessLicenseEndLong() {
        return this.date2Long(this.businessLicenseEnd);
    }


    public void setBusinessLicenseEndLong(Long businessLicenseEnd) {
        this.businessLicenseEnd = this.long2Date(businessLicenseEnd);
    }


}
