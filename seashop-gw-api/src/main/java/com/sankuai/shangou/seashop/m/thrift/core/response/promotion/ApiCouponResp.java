package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.m.thrift.core.dto.promotion.ApiCouponProductDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@TypeDoc(description = "优惠券活动响应体")
@ToString
@Data
public class ApiCouponResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "面值(价格)")
    private BigDecimal price;

    @FieldDoc(description = "最大可领取张数")
    private Integer perMax;

    @FieldDoc(description = "订单金额（满足多少钱才能使用）")
    private BigDecimal orderAmount;

    @FieldDoc(description = "发行张数")
    private Integer num;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "优惠券名称")
    private String couponName;

    @FieldDoc(description = "领取方式 0 店铺首页 1 积分兑换 2 主动发放")
    private Integer receiveType;

    @FieldDoc(description = "使用范围：0=全场通用，1=部分商品可用")
    private Integer useArea;

    @FieldDoc(description = "备注")
    private String remark;

    @FieldDoc(description = "产品ID列表")
    private List<Long> productIdList;

    @FieldDoc(description = "推广方式：0 平台；4 移动端(小程序)")
    private List<Integer> platForm;

    @FieldDoc(description = "优惠券对应的商品信息")
    private List<ApiCouponProductDto> productList;


    public String getPriceString() {
        return this.bigDecimal2String(this.price);
    }


    public void setPriceString(String price) {
        this.price = this.string2BigDecimal(price);
    }


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
