package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/16/016
 * @description:
 */
@TypeDoc(description = "限时购活动响应体")
@Data
public class ApiVisualFlashSaleResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "活动名称")
    private String title;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "跳转地址")
    private String urlPath;

    @FieldDoc(description = "产品ID")
    private String productId;

    @FieldDoc(description = "产品名称")
    private String productName;

    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;

    @FieldDoc(description = "活动开始日期")
    private Date beginDate;

    @FieldDoc(description = "活动结束日期")
    private Date endDate;

    @FieldDoc(description = "商城价格")
    private BigDecimal salePrice;

    @FieldDoc(description = "最小价格")
    private BigDecimal minPrice;

    @FieldDoc(description = "商城库存")
    private Long mallStock;

    @FieldDoc(description = "活动库存")
    private Long activityStock;

    @FieldDoc(description = "销量")
    private Integer saleCount;

    @FieldDoc(description = "距离开始的时间（单位秒）")
    private Long remStartTime = 0L;

    @FieldDoc(description = "距离结束的时间（单位秒）")
    private Long remEndTime = 0L;


    public Long getBeginDateLong() {
        return this.date2Long(this.beginDate);
    }


    public void setBeginDateLong(Long beginDate) {
        this.beginDate = this.long2Date(beginDate);
    }


    public Long getEndDateLong() {
        return this.date2Long(this.endDate);
    }


    public void setEndDateLong(Long endDate) {
        this.endDate = this.long2Date(endDate);
    }


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


    public String getMinPriceString() {
        return this.bigDecimal2String(this.minPrice);
    }


    public void setMinPriceString(String minPrice) {
        this.minPrice = this.string2BigDecimal(minPrice);
    }


}
