package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@TypeDoc(description = "店铺限时购配置响应体")
@Data
public class ApiShopFlashSaleConfigResp extends BaseThriftDto {

    /**
     * 店铺ID（平台的ID默认=0）
     */
    @FieldDoc(description = "店铺ID")
    private Long shopId;

    /**
     * 预热时间（店铺配置）
     */
    @FieldDoc(description = "预热时间")
    private Integer preheat;

    /**
     * 是否允许正常购买（店铺配置）
     */
    @FieldDoc(description = "是否允许正常购买")
    private Boolean normalPurchaseFlag;


}
