package com.sankuai.shangou.seashop.m.thrift.core.request.product;


import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/02 13:50
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "创建品牌请求参数")
public class ApiCreateBrandReq extends BaseParamReq {

    /**
     * 品牌名称
     */
    @FieldDoc(description = "品牌名称", requiredness = Requiredness.REQUIRED)
    private String name;

    /**
     * 品牌图片
     */
    @FieldDoc(description = "品牌图片", requiredness = Requiredness.REQUIRED)
    @JsonUrlFormat(deserializer = false)
    private String logo;

    /**
     * 品牌简介
     */
    @FieldDoc(description = "品牌简介", requiredness = Requiredness.NONE)
    private String description;


}
