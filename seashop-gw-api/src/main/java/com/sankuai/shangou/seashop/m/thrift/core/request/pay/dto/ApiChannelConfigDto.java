package com.sankuai.shangou.seashop.m.thrift.core.request.pay.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保存渠道配置请求对象")
public class ApiChannelConfigDto extends BaseParamReq {

    @FieldDoc(description = "配置Key", requiredness = Requiredness.REQUIRED)
    private String configKey;

    @FieldDoc(description = "配置Value", requiredness = Requiredness.REQUIRED)
    private String configValue;


}
