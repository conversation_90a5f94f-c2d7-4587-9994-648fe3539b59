package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@TypeDoc(description = "限时购活动明细响应体")
@Data
public class ApiFlashSaleDetailResp extends BaseThriftDto {

    @FieldDoc(description = "规格id")
    private String skuId;

    @FieldDoc(description = "规格名称")
    private String skuName;

    @FieldDoc(description = "规格1别名")
    private String spec1Alias;

    @FieldDoc(description = "规格2别名")
    private String spec2Alias;

    @FieldDoc(description = "规格3别名")
    private String spec3Alias;

    @FieldDoc(description = "销售价")
    private BigDecimal salePrice;

    @FieldDoc(description = "限时购时金额")
    private BigDecimal price;

    @FieldDoc(description = "商品库存")
    private Long stock;

    @FieldDoc(description = "活动库存")
    private Integer totalCount;

    @FieldDoc(description = "限购数量")
    private Integer limitCount;

    @FieldDoc(description = "商品主图")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


    public String getPriceString() {
        return this.bigDecimal2String(this.price);
    }


    public void setPriceString(String price) {
        this.price = this.string2BigDecimal(price);
    }


}
