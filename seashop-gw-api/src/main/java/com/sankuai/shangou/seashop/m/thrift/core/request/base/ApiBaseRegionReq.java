package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "入驻设置")
@Data
public class ApiBaseRegionReq extends BaseThriftDto {

    @FieldDoc(description = "地区id", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "地区编码", requiredness = Requiredness.REQUIRED)
    private String code;

    @FieldDoc(description = "地区名称", requiredness = Requiredness.REQUIRED)
    private String name;
    @FieldDoc(description = "地区简称")
    private String shortName;

    @FieldDoc(description = "上级地区id", requiredness = Requiredness.REQUIRED)
    private Long parentId;

    public void checkParameter() {

//        AssertUtil.throwInvalidParamIfTrue(this.id == null, "id不能为空");
        AssertUtil.throwInvalidParamIfTrue(this.parentId == null, "父级id不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(this.name), "请输入地址名称");
//        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(this.code), "code不能为空");
//        AssertUtil.throwInvalidParamIfTrue(this.id.equals(this.parentId), "id与父级id不能一致");
    }


}