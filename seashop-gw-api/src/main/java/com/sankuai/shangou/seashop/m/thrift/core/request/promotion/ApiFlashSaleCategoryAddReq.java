package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "新增限时购分类请求对象")
public class ApiFlashSaleCategoryAddReq extends BaseParamReq {

    @FieldDoc(description = "分类名称", requiredness = Requiredness.REQUIRED)
    private String categoryName;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.categoryName)) {
            throw new InvalidParamException("分类名称不能为空");
        }
    }


}
