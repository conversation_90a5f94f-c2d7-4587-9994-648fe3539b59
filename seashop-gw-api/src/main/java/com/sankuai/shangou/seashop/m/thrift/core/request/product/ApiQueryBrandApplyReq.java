package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询品牌申请分页列表入参")
public class ApiQueryBrandApplyReq extends BasePageReq {

    @FieldDoc(description = "品牌id", requiredness = Requiredness.NONE)
    private Long brandId;

    @FieldDoc(description = "品牌名称", requiredness = Requiredness.NONE)
    private String brandName;

    @FieldDoc(description = "审核状态 0-未审核 1-审核通过 2-审核拒绝", requiredness = Requiredness.NONE)
    private Integer auditStatusCode;

    @FieldDoc(description = "供应商名称", requiredness = Requiredness.NONE)
    private String shopName;

    @FieldDoc(description = "供应商id", requiredness = Requiredness.NONE)
    private Long shopId;


}
