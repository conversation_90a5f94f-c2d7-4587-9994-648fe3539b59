package com.sankuai.shangou.seashop.m.thrift.user.account.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@TypeDoc(description = "标签信息")
@AllArgsConstructor
@NoArgsConstructor
public class ApiRoleResp extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;
    @FieldDoc(description = "父级ID")
    private Long shopId;
    @FieldDoc(description = "角色名称")
    private String roleName;
    @FieldDoc(description = "角色描述")
    private String description;
    @FieldDoc(description = "创建时间")
    private Date createTime;
    @FieldDoc(description = "更新时间")
    private Date updateTime;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }
}
