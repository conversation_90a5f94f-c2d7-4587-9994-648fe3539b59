package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import org.apache.commons.lang3.StringUtils;

import lombok.Data;

@Data
public class ApiSaveWXAccountReq extends BaseParamReq {

    @FieldDoc(description = "公众号AppId",requiredness = Requiredness.REQUIRED)
    @ExaminField(description = "公众号AppId")
    private String weixinMpAppId;

    @FieldDoc(description = "公众号AppSecret",requiredness = Requiredness.REQUIRED)
    @ExaminField(description = "公众号AppSecret")
    private String weixinMpAppSecret;

    @Override
    public void checkParameter() {
        if (StringUtils.isEmpty(this.weixinMpAppId)) {
            throw new IllegalArgumentException("公众号AppId不能为空");
        }
        if(StringUtils.isEmpty(this.weixinMpAppSecret)){
            throw new IllegalArgumentException("公众号AppSecret不能为空");
        }
    }


}
