package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "图片移动对象")
@Data
public class ApiBaseMoveImageReq extends BaseThriftDto {
    @FieldDoc(description = "图片id", requiredness = Requiredness.REQUIRED)
    private List<Long> ids;
    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private Long shopId;
    @FieldDoc(description = "店铺id")
    private Long cId;

    public void checkParameter() {
        if (this.ids == null || this.ids.size() < 1) {
            throw new IllegalArgumentException("图片ids不能为空");
        }

        if (this.cId == null) {
            throw new IllegalArgumentException("分类id不能为空");
        }
    }


}
