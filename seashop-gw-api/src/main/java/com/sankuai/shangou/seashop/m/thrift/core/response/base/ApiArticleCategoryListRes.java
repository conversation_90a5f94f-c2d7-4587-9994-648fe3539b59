package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseArticleCategoryRes;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "文章分类返回")
@Data
public class ApiArticleCategoryListRes extends BaseThriftDto {

    @FieldDoc(description = "分类对象集合")
    private List<BaseArticleCategoryRes> categorys;


}
