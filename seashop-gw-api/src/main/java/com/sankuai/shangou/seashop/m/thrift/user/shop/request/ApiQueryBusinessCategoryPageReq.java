package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "查询店铺类目入参")
@Getter
@Setter
public class ApiQueryBusinessCategoryPageReq extends BasePageReq {

    @FieldDoc(description = "店铺Id", requiredness = Requiredness.OPTIONAL)
    private Long shopId;


    @FieldDoc(description = "经营类目名称", requiredness = Requiredness.OPTIONAL)
    private String categoryName;


    @FieldDoc(description = "经营类目ID", requiredness = Requiredness.OPTIONAL)
    private Long categoryId;

}
