package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "结算配置请求参数")
public class ApiSettlementConfigReq extends BaseParamReq {

    @FieldDoc(description = "结算周期", requiredness = Requiredness.REQUIRED)
    private Integer settlementInterval;

    @FieldDoc(description = "结算手续费率", requiredness = Requiredness.REQUIRED)
    private BigDecimal settlementFeeRate;

    @FieldDoc(description = "微信结算手续费率", requiredness = Requiredness.REQUIRED)
    private BigDecimal wxFeeRate;

    @Override
    public void checkParameter() {
        if (null == this.settlementInterval || this.settlementInterval <= 0) {
            throw new InvalidParamException("结算周期不能为空且不能小于0");
        }
        if (null == this.settlementFeeRate || this.settlementFeeRate.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("结算手续费率不能为空且不能小于0");
        }
        // 结算手续费不能大于等于100
        if (this.settlementFeeRate.compareTo(BigDecimal.valueOf(100)) >= 0) {
            throw new InvalidParamException("结算手续费率不能大于等于100");
        }
        if (null == this.wxFeeRate || this.wxFeeRate.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("微信结算手续费率不能为空且不能小于0");
        }
        // 结算手续费不能大于等于100
        if (this.wxFeeRate.compareTo(BigDecimal.valueOf(100)) >= 0) {
            throw new InvalidParamException("微信结算手续费率不能大于等于100");
        }
    }


    public String getSettlementFeeRateString() {
        return this.bigDecimal2String(this.settlementFeeRate);
    }


    public void setSettlementFeeRateString(String settlementFeeRate) {
        this.settlementFeeRate = this.string2BigDecimal(settlementFeeRate);
    }


    public String getWxFeeRateString() {
        return this.bigDecimal2String(this.wxFeeRate);
    }


    public void setWxFeeRateString(String wxFeeRate) {
        this.wxFeeRate = this.string2BigDecimal(wxFeeRate);
    }
}
