package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

/**
 * @description: 查询管理员请求入参
 * @author: LXH
 **/
@Data
public class ApiQueryManagerPageReq extends BasePageReq {
    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺ID 0为平台")
    private Long shopId;


}
