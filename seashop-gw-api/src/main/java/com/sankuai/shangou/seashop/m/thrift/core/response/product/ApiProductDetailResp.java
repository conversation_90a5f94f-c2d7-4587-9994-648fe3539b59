package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiProductDetailDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 13:43
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "商品详情返回值")
public class ApiProductDetailResp extends BaseThriftDto {

    @FieldDoc(description = "商品详情")

    private ApiProductDetailDto result;

}
