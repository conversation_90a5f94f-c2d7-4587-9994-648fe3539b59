package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@TypeDoc(description = "运费模版管理查询接口")
@ToString
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiQueryFreightTemplateReq {

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "模板ID")
    private Long templateId;

    @FieldDoc(description = "模板名称的集合")
    private List<String> templateNames;

    @FieldDoc(description = "定价方法(按体积、重量计算）")
    private Integer valuationMethod;

    public void checkParameter() {
        if (shopId == null) {
            throw new IllegalArgumentException("shopId不能为空");
        }
        if (CollectionUtils.isNotEmpty(templateNames)) {
            AssertUtil.throwIfTrue(templateNames.size() > 200, "模板名称不能超过200个");
        }
    }


}
