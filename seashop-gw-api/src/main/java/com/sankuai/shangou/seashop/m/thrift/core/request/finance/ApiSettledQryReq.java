package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "已结算列表请求参数")
public class ApiSettledQryReq extends BasePageReq {

    @FieldDoc(description = "结算时间-开始时间")
    private Date startSettlementTime;

    @FieldDoc(description = "结算时间-结束时间")
    private Date endSettlementTime;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @Override
    public void valueInit() {
        if (null == this.startSettlementTime) {
            // 如果没传入开始时间，则默认查询结束时间前一年的数据
            Date date = this.endSettlementTime;
            if (null == date) {
                date = new Date();
            }
            this.startSettlementTime = DateUtil.offset(date, DateField.YEAR, -1);
        }
    }


    public Long getStartSettlementTimeLong() {
        return this.date2Long(this.startSettlementTime);
    }


    public void setStartSettlementTimeLong(Long startSettlementTime) {
        this.startSettlementTime = this.long2Date(startSettlementTime);
    }


    public Long getEndSettlementTimeLong() {
        return this.date2Long(this.endSettlementTime);
    }


    public void setEndSettlementTimeLong(Long endSettlementTime) {
        this.endSettlementTime = this.long2Date(endSettlementTime);
    }


}
