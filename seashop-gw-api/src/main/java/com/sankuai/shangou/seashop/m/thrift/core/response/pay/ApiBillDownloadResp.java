package com.sankuai.shangou.seashop.m.thrift.core.response.pay;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description:
 */
@TypeDoc(description = "账单下载响应对象")
@ToString
@Data
public class ApiBillDownloadResp extends BaseThriftDto {

    @FieldDoc(description = "账单下载地址")
    private String billUrl;


}
