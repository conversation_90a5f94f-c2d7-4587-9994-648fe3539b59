package com.sankuai.shangou.seashop.m.thrift.core.dto.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "折扣规则响应对象")
public class ApiDiscountActiveProductDto extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "活动ID")
    private Long activeId;

    @FieldDoc(description = "商品ID")
    private String productId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "商品价格(最小售价)")
    private BigDecimal salePrice;


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }
}
