package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/26 16:27
 */
@Data
@TypeDoc(description = "消息模版app请求入参")
public class ApiMsgTemplateAppletReq extends BaseParamReq {

    @FieldDoc(description = "小程序AppId")
    private String weixinAppletId;

    @FieldDoc(description = "小程序AppSecret")
    private String weixinAppletSecret;


}
