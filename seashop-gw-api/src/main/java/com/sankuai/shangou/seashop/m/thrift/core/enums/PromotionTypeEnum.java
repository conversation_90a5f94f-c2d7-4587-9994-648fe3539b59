package com.sankuai.shangou.seashop.m.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2024/1/30/030
 * @description: 营销活动类型
 */
@ThriftEnum
public enum PromotionTypeEnum {

    /**
     * 1.优惠券活动
     * 2.满减活动
     * 3.折扣活动
     * 4.专享价活动
     * 5.限时购活动
     * 6.组合购活动
     */
    COUPON(1, "优惠券活动"),
    FULL_REDUCTION(2, "满减活动"),
    DISCOUNT(3, "折扣活动"),
    EXCLUSIVE_PRICE(4, "专享价活动"),
    LIMITED_TIME_PURCHASE(5, "限时购活动"),
    COMBINATION_PURCHASE(6, "组合购活动");

    private Integer code;
    private String desc;

    PromotionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PromotionTypeEnum getByCode(Integer code) {
        for (PromotionTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

//    public static final Integer COUPON_CODE = 1;
//    public static final Integer FULL_REDUCTION_CODE = FULL_REDUCTION.getCode();
//    public static final Integer DISCOUNT_CODE = DISCOUNT.getCode();
//    public static final Integer EXCLUSIVE_PRICE_CODE = EXCLUSIVE_PRICE.getCode();
//    public static final Integer LIMITED_TIME_PURCHASE_CODE = LIMITED_TIME_PURCHASE.getCode();
//    public static final Integer COMBINATION_PURCHASE_CODE = COMBINATION_PURCHASE.getCode();
}
