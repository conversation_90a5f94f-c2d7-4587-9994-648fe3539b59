package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 11:07
 */
@Data
@TypeDoc(description = "组合购查询列表返参")
public class ApiPageCollocationResp extends BaseParamReq {

    @FieldDoc(description = "活动ID")
    private Long activityId;

    @FieldDoc(description = "活动名称")
    private String activityName;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "主商品ID")
    private String mainProductId;

    @FieldDoc(description = "主商品名称")
    private String mainProductName;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "状态")
    private Integer status;

    @FieldDoc(description = "状态翻译")
    private String statusName;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
