package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "已结算明细列表请求参数")
public class ApiSettledItemQryReq extends BasePageReq {

    @Schema(description = "结算id", required = true)
    private Long detailId;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Schema(description = "订单完成时间-开始时间")
    private Date startOrderFinishTime;

    @Schema(description = "订单完成时间-结束时间")
    private Date endOrderFinishTime;

    @Schema(description = "结算时间-开始时间")
    private Date startSettlementTime;

    @Schema(description = "结算时间-结束时间")
    private Date endSettlementTime;

    @Schema(description = "订单id")
    private String orderId;

    @Schema(description = "支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银")
    private Integer paymentType;

    @Schema(description = "支付时间-开始时间")
    private Date startPayTime;

    @Schema(description = "支付时间-结束时间")
    private Date endPayTime;

    @Schema(description = "店铺名称")
    private String shopName;

    @Override
    public void checkParameter() {
        if (null == this.detailId) {
            throw new InvalidParamException("结算id不能为空");
        }
        /*if (null == this.shopId) {
            throw new InvalidParamException("店铺id不能为空");
        }*/
    }


    public Long getStartOrderFinishTimeLong() {
        return this.date2Long(this.startOrderFinishTime);
    }


    public void setStartOrderFinishTimeLong(Long startOrderFinishTime) {
        this.startOrderFinishTime = this.long2Date(startOrderFinishTime);
    }


    public Long getEndOrderFinishTimeLong() {
        return this.date2Long(this.endOrderFinishTime);
    }


    public void setEndOrderFinishTimeLong(Long endOrderFinishTime) {
        this.endOrderFinishTime = this.long2Date(endOrderFinishTime);
    }


    public Long getStartSettlementTimeLong() {
        return this.date2Long(this.startSettlementTime);
    }


    public void setStartSettlementTimeLong(Long startSettlementTime) {
        this.startSettlementTime = this.long2Date(startSettlementTime);
    }


    public Long getEndSettlementTimeLong() {
        return this.date2Long(this.endSettlementTime);
    }


    public void setEndSettlementTimeLong(Long endSettlementTime) {
        this.endSettlementTime = this.long2Date(endSettlementTime);
    }


    public Long getStartPayTimeLong() {
        return this.date2Long(this.startPayTime);
    }


    public void setStartPayTimeLong(Long startPayTime) {
        this.startPayTime = this.long2Date(startPayTime);
    }


    public Long getEndPayTimeLong() {
        return this.date2Long(this.endPayTime);
    }


    public void setEndPayTimeLong(Long endPayTime) {
        this.endPayTime = this.long2Date(endPayTime);
    }
}
