package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import java.util.List;

public class ApiCmdShopStepsThreeReq extends BaseParamReq {
    @FieldDoc(description = "id")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    @FieldDoc(description = "店铺等级")
    private Integer shopGrade;
    @FieldDoc(description = "选择类目")
    private List<Long> categories;
    @FieldDoc(description = "所有关联自定义表单的分类数据")
    private List<ApiCmdBusinessCategoryFormReq> customFormCategory;
    @FieldDoc(description = "店铺logo")
    private String formImage;
    @FieldDoc(description = "店铺banner")
    private String formFile;


}
