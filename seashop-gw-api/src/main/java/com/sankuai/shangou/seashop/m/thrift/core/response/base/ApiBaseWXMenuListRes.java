package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuRes;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "微信公众号菜单集合实体")
@Data
public class ApiBaseWXMenuListRes extends BaseThriftDto {
    @FieldDoc(description = "id")
    private Long id;

    @FieldDoc(description = "菜单名称")
    private String name;

    @FieldDoc(description = "链接类型")
    private Integer linkType;

    @FieldDoc(description = "链接值")
    private String linkValue;

    @FieldDoc(description = "父级ID")
    private Long parentId;

    @FieldDoc(description = "是否自定义链接")

    private Integer whetherCustom;

    @FieldDoc(description = "下级菜单")

    private List<BaseWXMenuRes> subs;


    @Override
    public String toString() {
        return "BaseWXMenuListRes{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", linkType=" + linkType +
            ", linkValue='" + linkValue + '\'' +
            ", parentId=" + parentId +
            ", whetherCustom=" + whetherCustom +
            ", subs=" + subs +
            '}';
    }
}
