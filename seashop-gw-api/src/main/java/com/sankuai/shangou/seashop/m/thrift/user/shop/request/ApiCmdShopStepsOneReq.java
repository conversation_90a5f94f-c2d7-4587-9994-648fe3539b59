package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商入驻请求入参")
public class ApiCmdShopStepsOneReq extends BaseParamReq {

    @FieldDoc(description = "id")
    private Long shopId;
    @FieldDoc(description = "公司名称")
//        [Required(ErrorMessage = "必须填写个人姓名名称")]
//            [StringLength(60, ErrorMessage = "最大长度不能超过60")]
//            [MinLength(2, ErrorMessage = "个人姓名不能小于2个字符")]
    private String companyName;
    @FieldDoc(description = "公司所在地")
    private Integer cityRegionId;
    @FieldDoc(description = "公司详细地址")
    private String address;
    @FieldDoc(description = " 身份证号")
    private String idCard;
    @FieldDoc(description = "份证有效期开始日期")
    private Date idCardStartDate;
    @FieldDoc(description = "身份证有效期结束日期")
    private Date idCardEndDate;
    @FieldDoc(description = "身份证正面照")
    private String idCardUrl;
    @FieldDoc(description = "身份证反面照")
    private String idCardUrl2;
    @FieldDoc(description = "管理员真实姓名")
    private String realName;
    @FieldDoc(description = "管理员手机")
    private String memberPhone;
    @FieldDoc(description = "手机验证码")
    private String phoneCode;
    @FieldDoc(description = "管理员邮箱")
    private String memberEmail;
    @FieldDoc(description = "邮箱验证码")
    private String emailCode;
    @FieldDoc(description = "业务类型 1个人供应商 0企业供应商")
    private Integer businessType;

    @FieldDoc(description = "公司法定代表人")
    private String legalPerson;
    @FieldDoc(description = "公司人数")
    private Integer employeeCount;

    @FieldDoc(description = "公司电话")
    private String phone;
    @FieldDoc(description = "联系人姓名")
    private String contactName;
    @FieldDoc(description = "联系人电话")
    private String contactPhone;
    @FieldDoc(description = "营业执照号")
    private String businessLicenceNumber;
    @FieldDoc(description = "营业执照所在地")
    private Integer businessLicenceArea;
    @FieldDoc(description = "营业执照起始有效期")
    private Date businessLicenceValidStart;
    @FieldDoc(description = "营业执照截止有效期")
    private Date businessLicenceValidEnd;
    @FieldDoc(description = "法定经营范围")
    private String businessSphere;
    @FieldDoc(description = "营业执照号电子版")
    private String businessLicenceNumberPhoto;
    @FieldDoc(description = "开户银行")
    private String bankPhoto;


    public Long getIdCardStartDateLong() {
        return this.date2Long(this.idCardStartDate);
    }


    public void setIdCardStartDateLong(Long idCardStartDate) {
        this.idCardStartDate = this.long2Date(idCardStartDate);
    }


    public Long getIdCardEndDateLong() {
        return this.date2Long(this.idCardEndDate);
    }


    public void setIdCardEndDateLong(Long idCardEndDate) {
        this.idCardEndDate = this.long2Date(idCardEndDate);
    }


    public Long getBusinessLicenceValidStartLong() {
        return this.date2Long(this.businessLicenceValidStart);
    }


    public void setBusinessLicenceValidStartLong(Long businessLicenceValidStart) {
        this.businessLicenceValidStart = this.long2Date(businessLicenceValidStart);
    }


    public Long getBusinessLicenceValidEndLong() {
        return this.date2Long(this.businessLicenceValidEnd);
    }


    public void setBusinessLicenceValidEndLong(Long businessLicenceValidEnd) {
        this.businessLicenceValidEnd = this.long2Date(businessLicenceValidEnd);
    }


}