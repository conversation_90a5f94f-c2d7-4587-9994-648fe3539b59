package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@TypeDoc(description = "图片移动对象")
@Data
public class ApiBaseMoveCateImageReq extends BaseThriftDto {
    @FieldDoc(description = "源id", requiredness = Requiredness.REQUIRED)
    private Long sourceId;
    @FieldDoc(description = "目标id", requiredness = Requiredness.REQUIRED)
    private Long targetId;
    @FieldDoc(description = "店铺id")
    private long shopId;


    public void checkParameter() {
        if (this.sourceId == null) {
            throw new IllegalArgumentException("源分类id不能为空");
        }

        if (this.targetId == null) {
            throw new IllegalArgumentException("目标分类id不能为空");
        }
    }


}
