package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:11
 */
@Data
@TypeDoc(description = "操作日志查询")
public class ApiLogMQueryReq extends BasePageReq {

    @FieldDoc(description = "操作名称")
    private String operationName;

    @FieldDoc(description = "业务板块")
    private Integer moduleId;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "操作人")
    private String userName;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "具体的操作功能")
    private String actionName;

    @FieldDoc(description = "操作内容")
    private String operationContent;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
