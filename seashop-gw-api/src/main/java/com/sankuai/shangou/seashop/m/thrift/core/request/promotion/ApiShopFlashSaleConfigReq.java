package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "店铺限时购配置请求对象")
public class ApiShopFlashSaleConfigReq extends BaseParamReq {

    /**
     * 店铺ID（平台的ID默认=0）
     */
    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    /**
     * 预热时间（店铺配置）
     */
    @FieldDoc(description = "预热时间", requiredness = Requiredness.REQUIRED)
    private Integer preheat;

    /**
     * 是否允许正常购买（店铺配置）
     */
    @FieldDoc(description = "是否允许正常购买", requiredness = Requiredness.REQUIRED)
    private Boolean normalPurchaseFlag;

    @Override
    public void checkParameter() {
        if (null == this.shopId) {
            throw new InvalidParamException("店铺ID不能为空");
        }
        if (null == this.preheat) {
            throw new InvalidParamException("预热时间不能为空");
        }
        if (null == this.normalPurchaseFlag) {
            throw new InvalidParamException("是否允许正常购买不能为空");
        }
    }


}
