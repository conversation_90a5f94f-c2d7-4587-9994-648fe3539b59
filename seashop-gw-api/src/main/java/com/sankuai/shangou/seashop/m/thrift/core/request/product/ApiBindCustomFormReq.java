package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/06 19:10
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "关联自定义表单入参")
public class ApiBindCustomFormReq extends BaseParamReq {

    @FieldDoc(description = "类目Id", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "自定义表单Id 传0表示取消关联", requiredness = Requiredness.REQUIRED)
    private Long customFormId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "类目Id不能为空");
        AssertUtil.throwInvalidParamIfTrue(customFormId == null || customFormId < 0, "请选择关联的表单");
    }


}
