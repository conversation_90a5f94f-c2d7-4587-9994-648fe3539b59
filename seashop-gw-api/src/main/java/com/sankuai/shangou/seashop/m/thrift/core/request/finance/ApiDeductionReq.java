package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.DeductionTypeEnum;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保证金扣减对象")
public class ApiDeductionReq extends BaseParamReq {

    @FieldDoc(description = "明细id", requiredness = Requiredness.REQUIRED)
    private Long cashDepositDetailId;

    @FieldDoc(description = "扣减金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal deductionAmount;

    @FieldDoc(description = "扣款类型：1罚款；2代收代付", requiredness = Requiredness.REQUIRED)
    private Integer deductionType;

    @FieldDoc(description = "扣减手续费")
    private BigDecimal deductionFee;

    @FieldDoc(description = "描述说明")
    private String description;

//    @FieldDoc(description = "操作人", requiredness = Requiredness.REQUIRED)
//    private String operator;

    @Override
    public void checkParameter() {
        if (this.cashDepositDetailId == null) {
            throw new InvalidParamException("cashDepositDetailId不能为空");
        }
        if (this.deductionAmount == null || this.deductionAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("deductionAmount不能为空且不能小于0");
        }
        if (this.deductionType == null) {
            throw new InvalidParamException("deductionType不能为空");
        }
        if (DeductionTypeEnum.getByType(this.deductionType) == null) {
            throw new InvalidParamException("deductionType不合法");
        }
        if (this.deductionFee != null && this.deductionFee.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("deductionFee不能为空且不能小于0");
        }
//        if (StrUtil.isBlank(this.operator)) {
//            throw new InvalidParamException("operator不能为空");
//        }
    }






    public String getDeductionAmountString() {
        return this.bigDecimal2String(this.deductionAmount);
    }


    public void setDeductionAmountString(String deductionAmount) {
        this.deductionAmount = this.string2BigDecimal(deductionAmount);
    }


    public String getDeductionFeeString() {
        return this.bigDecimal2String(this.deductionFee);
    }


    public void setDeductionFeeString(String deductionFee) {
        this.deductionFee = this.string2BigDecimal(deductionFee);
    }


//
//    public String getOperator() {
//        return operator;
//    }
//
//
//    public void setOperator(String operator) {
//        this.operator = operator;
//    }
}
