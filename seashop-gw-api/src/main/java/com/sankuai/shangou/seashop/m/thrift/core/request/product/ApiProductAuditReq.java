package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:00
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "商品审核入参")
public class ApiProductAuditReq extends BaseParamReq {

    @FieldDoc(description = "商品id的集合", requiredness = Requiredness.REQUIRED)
    private List<Long> productIdList;

    @FieldDoc(description = "是否通过", requiredness = Requiredness.REQUIRED)
    private Boolean pass;

    @FieldDoc(description = "备注", requiredness = Requiredness.OPTIONAL)
    private String auditReason;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIdList), "请至少选择一条待审核记录");
        AssertUtil.throwInvalidParamIfNull(pass, "请选择审核结果");
        if (!pass) {
            AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(auditReason), "请填写拒绝原因");
        }
    }


}
