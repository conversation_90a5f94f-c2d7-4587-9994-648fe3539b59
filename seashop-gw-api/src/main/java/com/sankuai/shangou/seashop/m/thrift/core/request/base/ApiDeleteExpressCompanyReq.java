package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

@TypeDoc(description = "快递公司入参")
@Data
public class ApiDeleteExpressCompanyReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    @PrimaryField
    private Long id;


    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
    }
}
