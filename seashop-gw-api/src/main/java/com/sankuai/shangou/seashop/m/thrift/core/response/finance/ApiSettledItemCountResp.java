package com.sankuai.shangou.seashop.m.thrift.core.response.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@TypeDoc(description = "已结算明细统计查询返回参数")
@ToString
@Data
public class ApiSettledItemCountResp extends BaseThriftDto {

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "结算明细id")
    private Long detailId;

    @FieldDoc(description = "结算金额")
    private BigDecimal settleAmount = BigDecimal.ZERO;

    @FieldDoc(description = "佣金金额")
    private BigDecimal commissionAmount = BigDecimal.ZERO;


    public String getSettleAmountString() {
        return this.bigDecimal2String(this.settleAmount);
    }


    public void setSettleAmountString(String settleAmount) {
        this.settleAmount = this.string2BigDecimal(settleAmount);
    }


    public String getCommissionAmountString() {
        return this.bigDecimal2String(this.commissionAmount);
    }


    public void setCommissionAmountString(String commissionAmount) {
        this.commissionAmount = this.string2BigDecimal(commissionAmount);
    }
}
