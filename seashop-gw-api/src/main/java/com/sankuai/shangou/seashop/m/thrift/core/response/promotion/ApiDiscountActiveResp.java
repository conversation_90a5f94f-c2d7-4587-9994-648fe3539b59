package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.m.thrift.core.dto.promotion.ApiDiscountActiveProductDto;
import com.sankuai.shangou.seashop.m.thrift.core.dto.promotion.ApiDiscountActiveRuleDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
@TypeDoc(description = "折扣活动响应体")
@ToString
@Data
public class ApiDiscountActiveResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "活动名称")
    private String activeName;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "是否全部商品")
    private Boolean izAllProduct;

    @FieldDoc(description = "折扣规则")
    private List<ApiDiscountActiveRuleDto> ruleList;

    @FieldDoc(description = "商品列表")
    private List<ApiDiscountActiveProductDto> productList;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
