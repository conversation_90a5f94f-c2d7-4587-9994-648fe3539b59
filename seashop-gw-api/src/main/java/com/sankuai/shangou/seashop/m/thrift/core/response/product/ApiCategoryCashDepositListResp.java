package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "保证金配置列表")
public class ApiCategoryCashDepositListResp {

    @FieldDoc(description = "保证金配置列表")
    private List<ApiCategoryCashDepositResp> list;


}
