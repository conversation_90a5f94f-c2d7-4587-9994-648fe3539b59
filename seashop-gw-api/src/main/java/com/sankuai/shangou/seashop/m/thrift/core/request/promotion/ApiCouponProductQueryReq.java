package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "优惠券关联商品查询请求对象")
public class ApiCouponProductQueryReq extends BasePageReq {

    @FieldDoc(description = "优惠券ID", requiredness = Requiredness.REQUIRED)
    private Long couponId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(couponId, "优惠券Id不能为空");
    }


}
