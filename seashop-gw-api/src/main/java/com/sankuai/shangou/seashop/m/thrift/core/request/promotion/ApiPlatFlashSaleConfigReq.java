package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "平台限时购配置请求对象")
public class ApiPlatFlashSaleConfigReq extends BaseParamReq {

    @FieldDoc(description = "是否需要审核（平台配置）", requiredness = Requiredness.REQUIRED)
    private Boolean needAuditFlag;

    @Override
    public void checkParameter() {
        if (null == this.needAuditFlag) {
            throw new InvalidParamException("是否需要审核不能为空");
        }
    }


}
