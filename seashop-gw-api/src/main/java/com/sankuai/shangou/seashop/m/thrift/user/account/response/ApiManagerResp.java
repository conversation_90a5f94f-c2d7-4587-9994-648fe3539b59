package com.sankuai.shangou.seashop.m.thrift.user.account.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import java.util.Date;

import lombok.Data;

/**
 * @description: 管理员信息
 * @author: LXH
 **/
@Data
public class ApiManagerResp extends BaseThriftDto {
    @FieldDoc(description = "管理员id")
    private Long id;
    @FieldDoc(description = "供应商id")
    private Long shopId;
    @FieldDoc(description = "权限组id")
    private Long roleId;
    @FieldDoc(description = "权限组名称")
    private String roleName;
    @FieldDoc(description = "用户名")
    private String userName;
    @FieldDoc(description = "手机号码")
    private String cellphone;
    @FieldDoc(description = "备注")
    private String remark;
    @FieldDoc(description = "真实姓名")
    private String realName;
    @FieldDoc(description = "加密方式")
    private String encryptionMode;
    @FieldDoc(description = "创建时间")
    private Date createTime;
    @FieldDoc(description = "更新时间")
    private Date updateTime;
    @FieldDoc(description = "创建人")
    private Long createUser;
    @FieldDoc(description = "更新人")
    private Long updateUser;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


}
