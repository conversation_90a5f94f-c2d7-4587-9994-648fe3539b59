package com.sankuai.shangou.seashop.m.thrift.core.request.user.shop;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

@Data
@TypeDoc(description = "平台发票查询入参")
public class ApiQueryShopInvoiceReq extends BaseParamReq {
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null, "店铺shopId不能为空");
    }
}
