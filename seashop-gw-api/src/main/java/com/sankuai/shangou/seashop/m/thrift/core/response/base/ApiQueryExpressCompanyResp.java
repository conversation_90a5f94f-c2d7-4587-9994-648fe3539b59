package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;


@TypeDoc(description = "快递设置")
@Data
public class ApiQueryExpressCompanyResp {

    @FieldDoc(description = "快递公司集合")
    private List<ApiQueryExpressCompanyDto> queryExpressCompanyDtoList;


}
