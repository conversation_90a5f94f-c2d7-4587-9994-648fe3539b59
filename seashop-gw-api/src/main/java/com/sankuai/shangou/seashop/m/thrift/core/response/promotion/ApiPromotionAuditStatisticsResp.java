package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2024/3/5/005
 * @description:
 */
@TypeDoc(description = "营销待审核统计响应体")
@Data
public class ApiPromotionAuditStatisticsResp extends BaseThriftDto {

    @FieldDoc(description = "待审核总数")
    private Long count = 0L;

    @FieldDoc(description = "限时购待审核总数")
    private Long flashSaleCount = 0L;


}
