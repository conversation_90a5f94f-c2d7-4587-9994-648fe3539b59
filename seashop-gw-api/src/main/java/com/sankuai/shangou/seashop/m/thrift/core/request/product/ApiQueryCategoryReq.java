package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询类目入参")
public class ApiQueryCategoryReq extends BaseThriftDto {

    @FieldDoc(description = "最大深度", requiredness = Requiredness.NONE)
    private Integer maxDepth;

    @FieldDoc(description = "父类目id", requiredness = Requiredness.NONE)
    private Long parentId;

    @FieldDoc(description = "展示状态 0-全部 1-展示开启 2-展示关闭 (默认1)", requiredness = Requiredness.NONE)
    private Integer showStatus;

    @FieldDoc(description = "排除没有下级的类目 (默认false)", requiredness = Requiredness.NONE)
    private Boolean filterNoChildren;


}
