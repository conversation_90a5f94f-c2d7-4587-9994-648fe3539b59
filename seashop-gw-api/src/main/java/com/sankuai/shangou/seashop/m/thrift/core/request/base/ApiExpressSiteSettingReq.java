package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "物流设置入参")
@Data
@ToString
public class ApiExpressSiteSettingReq extends BaseParamReq {

    @FieldDoc(description = "美团秘钥键")
    private String meiTuanAppKey;

    @FieldDoc(description = "美团密码")
    private String meiTuanAppSecret;

    public void checkParameter() {
        if (StringUtils.isEmpty(meiTuanAppKey)) {
            throw new IllegalArgumentException("meiTuanAppKey不能为空");
        }
        if (StringUtils.isEmpty(meiTuanAppSecret)) {
            throw new IllegalArgumentException("meiTuanAppSecret不能为空");
        }
    }


}
