package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/16/016
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "限时购可视化查询请求对象")
public class ApiVisualFlashSaleQueryReq extends BasePageReq {

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "类目路径 | 隔开")
    private String categoryPath;

    @FieldDoc(description = "店铺分类id")
    private Long shopCategoryId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "类目id列表")
    private List<Long> categoryIds;

    @FieldDoc(description = "商品id列表")
    private List<Long> productIds;

    @FieldDoc(description = "限时购id列表")
    private List<Long> flashSaleIds;


}
