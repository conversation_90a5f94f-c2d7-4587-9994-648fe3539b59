package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "个人供应商修改入参")
public class ApiCmdShopStatusReq extends BaseParamReq {
    @FieldDoc(description = "店铺ID")
    @NotNull(message = "店铺ID为必填项")
    private Long shopId;
    @FieldDoc(description = "状态 6冻结 7解冻")
    @NotNull(message = "状态为必填项")
    private Integer status;
    @FieldDoc(description = "拒绝原因")
    private String refuseReason;


}
