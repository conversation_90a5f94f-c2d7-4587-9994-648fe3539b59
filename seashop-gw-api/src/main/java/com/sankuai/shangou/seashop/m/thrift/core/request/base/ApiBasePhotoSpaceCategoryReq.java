package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "图片分类")
@Data
public class ApiBasePhotoSpaceCategoryReq extends BaseThriftDto {
    private Long id;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "分类名称", requiredness = Requiredness.REQUIRED)
    private String photoSpaceCatrgoryName;

    @FieldDoc(description = "排序")
    private Long displaysSequence;

    public void checkParameter() {
        if (StringUtils.isEmpty(this.photoSpaceCatrgoryName)) {
            throw new IllegalArgumentException("分类名称不能为空");
        }


    }


}
