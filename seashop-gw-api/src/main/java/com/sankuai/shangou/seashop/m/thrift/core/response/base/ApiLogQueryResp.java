package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:11
 */
@Data
@TypeDoc(description = "日志查询返回")
public class ApiLogQueryResp extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "业务板块id")
    private Integer moduleId;

    @FieldDoc(description = "业务板块名称")
    private String moduleName;

    @FieldDoc(description = "操作类型")
    private Integer operationType;

    @FieldDoc(description = "操作类型名称")
    private String operationName;

    @FieldDoc(description = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    @FieldDoc(description = "操作人id")
    private Long operationUserId;

    @FieldDoc(description = "操作人账号")
    private String operationUserAccount;

    @FieldDoc(description = "操作人名称")
    private String operationUserName;

//    @FieldDoc(description = "操作数据详情")
//    private String operationContent;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "具体的操作功能")
    private String actionName;


    public Long getOperationTimeLong() {
        return this.date2Long(this.operationTime);
    }


    public void setOperationTimeLong(Long operationTime) {
        this.operationTime = this.long2Date(operationTime);
    }


}
