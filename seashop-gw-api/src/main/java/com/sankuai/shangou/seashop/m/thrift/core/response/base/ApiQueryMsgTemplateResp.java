package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMsgTemplateResp;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:12
 */
@Data
public class ApiQueryMsgTemplateResp {

    @FieldDoc(description = "小程序AppId")
    private String weixinAppletId;

    @FieldDoc(description = "小程序AppSecret")
    private String weixinAppletSecret;

    @FieldDoc(description = "模版项")
    private List<BaseMsgTemplateResp> items;


}
