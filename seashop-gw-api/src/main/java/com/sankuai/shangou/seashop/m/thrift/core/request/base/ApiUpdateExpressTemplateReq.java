package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "快递公司入参")
@Data
public class ApiUpdateExpressTemplateReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "快递公司名称")
    private String name;

    @FieldDoc(description = "快递面单宽度")
    private Integer width;

    @FieldDoc(description = "快递面单高度")
    private Integer height;

    @FieldDoc(description = "快递公司logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;

    @FieldDoc(description = "快递公司面单背景图片")
    private String backgroundImage;


    public void checkParameter() {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(name.replaceAll(" ", ""))) {
            throw new IllegalArgumentException("公司名称不能为空");
        }
        if (name.length() > 20) {
            throw new IllegalArgumentException("公司名称长度不能超过20");
        }
    }
}
