package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "发送验证码请求参数类")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiCmdImportCategoryReq extends BaseParamReq {
    @FieldDoc(description = "地址")
    private String path;
    @FieldDoc(description = "店铺id")
    private Long shopId;


}
