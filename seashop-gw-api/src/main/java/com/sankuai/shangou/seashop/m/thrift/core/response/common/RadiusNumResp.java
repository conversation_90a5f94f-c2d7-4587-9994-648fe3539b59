package com.sankuai.shangou.seashop.m.thrift.core.response.common;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/02/19 13:43
 */
@TypeDoc(description = "角标数据返回值")
@ToString
@Data
public class RadiusNumResp extends BaseParamReq {
//
//    @FieldDoc(description = "保证金退款审核数量")
//    private Integer cashRefundNum;

    @FieldDoc(description = "店铺数量")
    private Integer shopCount;
//
//    @FieldDoc(description = "未支付保证金店铺数量")
//    private Integer noPayBondShopCount;

    @FieldDoc(description = "经营类目审核未完结数量")
    private Integer applyWaitFinishNum;

    @FieldDoc(description = "入驻待审核")
    private Integer residentAudit;


}
