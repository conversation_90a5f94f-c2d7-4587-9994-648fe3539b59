package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "标签单个操作请求入参")
public class ApiCmdBindMemberPhoneReq extends BaseParamReq {
    /**
     * 标签ID
     */
    @FieldDoc(description = "用户ID")
    private Long userId;
    /**
     * 标签名称
     */
    @FieldDoc(description = "用户手机号")
    private String phone;
    /**
     * 操作人ID
     */
    @FieldDoc(description = "操作人ID")
    private Long operatorId;

    @Override
    public void checkParameter() {
        if (userId == null) {
            throw new InvalidParamException("用户Id不能为空");
        }
        if (StringUtils.isBlank(phone)) {
            throw new InvalidParamException("用户手机号不能为空");
        }
    }
}
