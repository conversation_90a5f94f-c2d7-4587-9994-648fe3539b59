package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@TypeDoc(description = "图片对象")
@Data
public class ApiBasePhotoSpaceReq extends BaseParamReq {

    private Long id;

    @FieldDoc(description = "分类id", requiredness = Requiredness.REQUIRED)
    private Long photoCategoryId;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "图片名称", requiredness = Requiredness.REQUIRED)
    private String photoName;

    @FieldDoc(description = "图片路径", requiredness = Requiredness.REQUIRED)
    @JsonUrlFormat(deserializer = false)
    private String photoPath;

    @FieldDoc(description = "图片大小")
    private Long fileSize;

    @FieldDoc(description = "上传时间")
    private Date uploadTime;

    @FieldDoc(description = "最后修改时间")
    private Date lastupdateTime;

    @FieldDoc(description = "端口")
    private int clientType;

    public void checkParameter() {
        if (this.photoCategoryId == null) {
            throw new IllegalArgumentException("所属分类不能为空");
        }

        if (StringUtils.isEmpty(this.photoName)) {
            throw new IllegalArgumentException("图片名称不能为空");
        }
        if (StringUtils.isEmpty(this.photoPath)) {
            throw new IllegalArgumentException("图片路径不能为空");
        }
    }


    public Long getUploadTimeLong() {
        return this.date2Long(this.uploadTime);
    }


    public void setUploadTimeLong(Long uploadTime) {
        this.uploadTime = this.long2Date(uploadTime);
    }


    public Long getLastupdateTimeLong() {
        return this.date2Long(this.lastupdateTime);
    }


    public void setLastupdateTimeLong(Long lastupdateTime) {
        this.lastupdateTime = this.long2Date(lastupdateTime);
    }
}
