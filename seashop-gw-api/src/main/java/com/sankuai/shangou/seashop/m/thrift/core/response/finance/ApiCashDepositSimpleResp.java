package com.sankuai.shangou.seashop.m.thrift.core.response.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@TypeDoc(description = "支付渠道配置响应体")
@ToString
@Data
public class ApiCashDepositSimpleResp extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "店铺Id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "可用金额")
    private BigDecimal currentBalance;

    @FieldDoc(description = "已缴纳金额")
    private BigDecimal totalBalance;

    @FieldDoc(description = "欠费金额")
    private BigDecimal arrearsBalance;

    @FieldDoc(description = "最后一次缴纳时间")
    private Date date;

    @FieldDoc(description = "欠费状态")
    private Integer status;


    public String getCurrentBalanceString() {
        return this.bigDecimal2String(this.currentBalance);
    }


    public void setCurrentBalanceString(String currentBalance) {
        this.currentBalance = this.string2BigDecimal(currentBalance);
    }


    public String getTotalBalanceString() {
        return this.bigDecimal2String(this.totalBalance);
    }


    public void setTotalBalanceString(String totalBalance) {
        this.totalBalance = this.string2BigDecimal(totalBalance);
    }


    public String getArrearsBalanceString() {
        return this.bigDecimal2String(this.arrearsBalance);
    }


    public void setArrearsBalanceString(String arrearsBalance) {
        this.arrearsBalance = this.string2BigDecimal(arrearsBalance);
    }


    public Long getDateLong() {
        return this.date2Long(this.date);
    }


    public void setDateLong(Long date) {
        this.date = this.long2Date(date);
    }


}
