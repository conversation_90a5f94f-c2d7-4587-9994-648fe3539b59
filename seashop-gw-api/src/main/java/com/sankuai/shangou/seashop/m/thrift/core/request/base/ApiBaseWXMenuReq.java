package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "微信公众号菜单请求入参")
@Data
public class ApiBaseWXMenuReq extends BaseThriftDto {

    @FieldDoc(description = "id")
    private Long id;

    @FieldDoc(description = "菜单标题")
    private String name;


    @FieldDoc(description = "链接类型：0无链接，1微商城，2小程序")
    private Integer linkType;


    @FieldDoc(description = "链接值")
    private String linkValue;


    @FieldDoc(description = "父级id")
    private Long parentId;


    @FieldDoc(description = "是否自定义菜单")
    private Integer whetherCustom;


    public void checkParameter() {
        if (StringUtils.isEmpty(this.name)) {
            throw new IllegalArgumentException("菜单标题不能为空");
        }
    }


}
