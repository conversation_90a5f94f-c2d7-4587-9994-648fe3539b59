package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "文章分类")
@Data
public class ApiBaseArticleCategoryReq extends BaseThriftDto {

    @FieldDoc(description = "文章分类id")
    private Long id;

    /**
     * 父分类id
     */
    @FieldDoc(description = "父分类id", requiredness = Requiredness.REQUIRED)
    private Long parentCategoryId;

    /**
     * 分类名称
     */
    @FieldDoc(description = "分类名称", requiredness = Requiredness.REQUIRED)
    private String name;

    /**
     * 排序字段
     */
    @FieldDoc(description = "排序字段")
    private Long displaySequence;

    /**
     * 是否默认
     */
    @FieldDoc(description = "是否默认")
    private Boolean isDefault;

    public void checkParameter() {
        if (this.parentCategoryId == null) {
            throw new IllegalArgumentException("父分类不能为空");
        }
        if (StringUtils.isEmpty(this.name)) {
            throw new IllegalArgumentException("分类名称不能为空");
        }
    }


}
