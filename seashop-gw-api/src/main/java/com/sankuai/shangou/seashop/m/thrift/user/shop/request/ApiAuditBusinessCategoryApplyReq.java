package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/12/09 10:27
 */
@Data
@ToString
@TypeDoc(description = "经营类目审核入参")
public class ApiAuditBusinessCategoryApplyReq extends BaseParamReq {

    @FieldDoc(description = "经营类目申请id", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "审核是否通过", requiredness = Requiredness.REQUIRED)
    private Boolean pass;

    @FieldDoc(description = "拒绝原因", requiredness = Requiredness.OPTIONAL)
    private String refuseReason;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "经营类目申请id不能为空");
        AssertUtil.throwInvalidParamIfNull(pass, "审核是否通过不能为空");
        if (!pass) {
            AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(refuseReason), "拒绝原因不能为空");
        }
    }


}
