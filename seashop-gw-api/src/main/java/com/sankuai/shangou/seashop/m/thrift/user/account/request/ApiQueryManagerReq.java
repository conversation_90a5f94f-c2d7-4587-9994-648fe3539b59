package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * @description: 单个商家查询入参
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "单个商家查询入参")
public class ApiQueryManagerReq extends BaseParamReq {

    @FieldDoc(description = "商家名称")
    private String managerName;

    @FieldDoc(description = "商家Id")
    private Long userId;


}
