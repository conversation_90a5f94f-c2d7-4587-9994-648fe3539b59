package com.sankuai.shangou.seashop.m.thrift.user.shop.response.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "表单字段对象")
public class ApiCategoryApplyFormFieldDto extends BaseThriftDto {

    @FieldDoc(description = "表单字段id")
    private Long fieldId;

    @FieldDoc(description = "表单字段id")
    private String fieldName;

    @FieldDoc(description = "表单字段值")
    private String fieldValue;

    @FieldDoc(description = "表字段类型")
    private Integer type;

    @FieldDoc(description = "格式")
    private Integer format;

    @FieldDoc(description = "选项")
    private String option;

    @FieldDoc(description = "是否必填")
    private Boolean isRequired;

    @FieldDoc(description = "排序")
    private Long displaySequence;


}
