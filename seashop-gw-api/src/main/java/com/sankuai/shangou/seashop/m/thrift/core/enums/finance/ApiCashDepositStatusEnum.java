package com.sankuai.shangou.seashop.m.thrift.core.enums.finance;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@ThriftEnum
public enum ApiCashDepositStatusEnum {

    // 欠费
    ARREARS(1, "欠费"),
    // 正常
    NORMAL(0, "正常"),
    ;

    private Integer code;
    private String desc;

    ApiCashDepositStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
