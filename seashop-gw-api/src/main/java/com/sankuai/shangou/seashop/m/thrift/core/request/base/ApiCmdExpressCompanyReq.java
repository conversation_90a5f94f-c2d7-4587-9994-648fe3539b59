package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "快递公司入参")
@Data
public class ApiCmdExpressCompanyReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "快递公司名称")
    private String name;

    @FieldDoc(description = "快递鸟物流公司编号")
    private String kuaidiniaoCode;

    @FieldDoc(description = "旺店通code")
    private String wangdiantongCode;

    @FieldDoc(description = "聚水潭code")
    private String jushuitanCode;

    @FieldDoc(description = "菠萝派code")
    private String boluopaiCode;

    @FieldDoc(description = "美团code")
    private String meituanCode;


    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(name.replaceAll(" ", ""))) {
            throw new IllegalArgumentException("公司名称不能为空");
        }
        if (name.length() > 20) {
            throw new IllegalArgumentException("公司名称长度不能超过20");
        }
        boolean kuaidiniaoCodeEmpty = StringUtils.isEmpty(kuaidiniaoCode) || StringUtils.isEmpty(kuaidiniaoCode.replaceAll(" ", ""));
        boolean wangdiantongCodeEmpty = StringUtils.isEmpty(wangdiantongCode) || StringUtils.isEmpty(wangdiantongCode.replaceAll(" ", ""));
        boolean jushuitanCodeEmpty = StringUtils.isEmpty(jushuitanCode) || StringUtils.isEmpty(jushuitanCode.replaceAll(" ", ""));
        boolean boluopaiCodeEmpty = StringUtils.isEmpty(boluopaiCode) || StringUtils.isEmpty(boluopaiCode.replaceAll(" ", ""));
        boolean meituanCodeEmpty = StringUtils.isEmpty(meituanCode) || StringUtils.isEmpty(meituanCode.replaceAll(" ", ""));
        if (kuaidiniaoCodeEmpty && wangdiantongCodeEmpty && jushuitanCodeEmpty && boluopaiCodeEmpty && meituanCodeEmpty) {
            throw new IllegalArgumentException("快递公司Code，不能为空");
        }
    }
}
