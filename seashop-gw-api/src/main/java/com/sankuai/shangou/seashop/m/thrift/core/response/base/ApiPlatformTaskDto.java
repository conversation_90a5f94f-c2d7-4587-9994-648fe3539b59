package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "平台任务")
public class ApiPlatformTaskDto extends BaseThriftDto {

    /**
     * 任务id
     */
    @FieldDoc(description = "任务id")
    private Long id;

    /**
     * 业务类型。1：导出任务
     */
    @FieldDoc(description = "业务类型。1：导出任务")
    private Integer bizType;

    /**
     * 任务类型。具体的业务指定，需要唯一，最好具有一定的规则
     */
    @FieldDoc(description = "任务类型。具体的业务指定，需要唯一，最好具有一定的规则")
    private Integer taskType;

    /**
     * 任务名称
     */
    @FieldDoc(description = "任务名称")
    private String taskName;

    /**
     * 任务状态：10-准备就绪(初始状态)，20-执行中，30-执行成功，40-执行失败
     */
    @FieldDoc(description = "任务状态：10-准备就绪(初始状态)，20-执行中，30-执行成功，40-执行失败")
    private Integer taskStatus;
    @FieldDoc(description = "任务状态描述")
    private String taskStatusDesc;

    /**
     * 开始时间
     */
    @FieldDoc(description = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @FieldDoc(description = "结束时间")
    private Date endTime;

    /**
     * 任务执行耗时，单位毫秒
     */
    @FieldDoc(description = "任务执行耗时，单位毫秒")
    private Integer cost;

    /**
     * 总记录数。多sheet导出的是所有sheet的总数
     */
    @FieldDoc(description = "总记录数。多sheet导出的是所有sheet的总数")
    private Long totalNum;

    /**
     * 成功数
     */
    @FieldDoc(description = "成功数")
    private Long successNum;

    /**
     * 失败数
     */
    @FieldDoc(description = "失败数")
    private Long failedNum;

    /**
     * 任务执行参数
     */
    @FieldDoc(description = "任务执行参数")
    private String executeParam;

    /**
     * 任务执行结果。如果执行失败内容为部分异常内容
     */
    @FieldDoc(description = "任务执行结果。如果执行失败内容为部分异常内容")
    private String executeResult;

    /**
     * 操作人id
     */
    @FieldDoc(description = "操作人id")
    private Long operatorId;

    /**
     * 操作人账号
     */
    @FieldDoc(description = "操作人账号")
    private String operatorAccount;

    /**
     * 重试次数
     */
    @FieldDoc(description = "重试次数")
    private Integer retryTimes;

    /**
     * 创建时间
     */
    @FieldDoc(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @FieldDoc(description = "更新时间")
    private Date updateTime;

    /**
     * 环境。主要是区分uat和prd
     */
    @FieldDoc(description = "环境。主要是区分uat和prd")
    private String env;
    @FieldDoc(description = "文件路径")
    private String filePath;
    @FieldDoc(description = "下载地址")
    private String downloadUrl;


    public Long getBeginTimeLong() {
        return this.date2Long(this.beginTime);
    }


    public void setBeginTimeLong(Long beginTime) {
        this.beginTime = this.long2Date(beginTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


}
