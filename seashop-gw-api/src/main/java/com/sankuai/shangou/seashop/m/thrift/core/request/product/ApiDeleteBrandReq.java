package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/02 13:50
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Data
@ToString
@TypeDoc(description = "删除品牌请求")
public class ApiDeleteBrandReq extends BaseParamReq {

    /**
     * 品牌id
     */

    @FieldDoc(description = "品牌id", requiredness = Requiredness.REQUIRED)
    private Long id;

}
