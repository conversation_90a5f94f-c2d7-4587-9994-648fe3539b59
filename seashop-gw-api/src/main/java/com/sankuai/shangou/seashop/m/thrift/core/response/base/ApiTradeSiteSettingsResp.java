package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author： liweisong
 * @create： 2023/11/22 17:06
 */
@TypeDoc(description = "交易参数设置返参")
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ApiTradeSiteSettingsResp {

    @FieldDoc(description = "未付款超时")
    private String unpaidTimeout;

    @FieldDoc(description = "确认收货超时")
    private String noReceivingTimeout;

    @FieldDoc(description = "自动收货完成前时间")
    private String beforeReceivingDays;

    @FieldDoc(description = "延迟收货时间")
    private String noReceivingDelayDays;

    @FieldDoc(description = "关闭评价通道时限")
    private String orderCommentTimeout;

    @FieldDoc(description = "供应商未发货自动短信提醒时限")
    private String orderWaitDeliveryRemindTime;

    @FieldDoc(description = "企业网银限制金额")
    private String companyBankOrderAmount;

    @FieldDoc(description = "订单退货期限")
    private String salesReturnTimeout;

    @FieldDoc(description = "供应商自动确认售后时限")
    private String shopConfirmTimeout;

    @FieldDoc(description = "用户发货限时")
    private String sendGoodsCloseTimeout;

    @FieldDoc(description = "供应商确认到货时限")
    private String shopNoReceivingTimeout;


}
