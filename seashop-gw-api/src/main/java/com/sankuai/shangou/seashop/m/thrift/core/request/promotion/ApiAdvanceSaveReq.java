package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.Date;

/**
 * <p>
 * 首页广告设置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保存弹窗广告请求对象")
public class ApiAdvanceSaveReq extends BaseParamReq {

    /**
     * 是否开启弹窗广告
     */
    @FieldDoc(description = "是否开启弹窗广告", requiredness = Requiredness.REQUIRED)
    private Boolean isEnable;

    /**
     * 广告位图片
     */
    @FieldDoc(description = "广告位图片", requiredness = Requiredness.REQUIRED)
    @JsonUrlFormat(deserializer = false)
    private String img;

    /**
     * 图片外联链接
     */
    @FieldDoc(description = "图片外联链接", requiredness = Requiredness.REQUIRED)
    private String link;

    /**
     * 开始时间
     */
    @FieldDoc(description = "开始时间", requiredness = Requiredness.REQUIRED)
    private Date startTime;

    /**
     * 结束时间
     */
    @FieldDoc(description = "结束时间", requiredness = Requiredness.REQUIRED)
    private Date endTime;

    /**
     * 是否重复播放
     */
    @FieldDoc(description = "是否重复播放", requiredness = Requiredness.REQUIRED)
    private Boolean isReplay;

    @Override
    public void checkParameter() {
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
