package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "查询类目申请入参")
public class ApiQueryBusinessCategoryApplyPageReq extends BasePageReq {

    @FieldDoc(description = "店铺名称", requiredness = Requiredness.OPTIONAL)
    private String shopName;

    @FieldDoc(description = "店铺状态 0-待审核 1-已审核 2-审核拒绝", requiredness = Requiredness.OPTIONAL)
    private Integer auditedStatus;

    @FieldDoc(description = "签署状态 待签署=0,已签署=1", requiredness = Requiredness.OPTIONAL)
    private Integer agreementStatus;

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.OPTIONAL)
    private Long shopId;


}
