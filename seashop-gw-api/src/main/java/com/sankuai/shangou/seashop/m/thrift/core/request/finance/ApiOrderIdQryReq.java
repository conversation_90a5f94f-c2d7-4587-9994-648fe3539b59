package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/12/6/006
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "订单id查询请求参数")
public class ApiOrderIdQryReq extends BaseParamReq {

    @FieldDoc(description = "订单id", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("订单id不能为空");
        }
    }


}
