package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 查询权限列表请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "查询权限列表请求入参")
public class ApiCmdRoleReq extends BaseParamReq {

    /**
     * id
     */
    @FieldDoc(description = "id")
    private Long id;

    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺ID 0为平台")
    private Long shopId;

    /**
     * 角色名称
     */
    @FieldDoc(description = "角色名称")
    private String roleName;

    /**
     * 权限id
     */
    @FieldDoc(description = "权限id")
    private List<Long> privilegeIds;

    /**
     * 操作人id
     */
    @FieldDoc(description = "操作人id")
    private Long operatorId;

    /**
     * 操作人名称
     */
    @FieldDoc(description = "操作人名称")
    private String operatorName;


}
