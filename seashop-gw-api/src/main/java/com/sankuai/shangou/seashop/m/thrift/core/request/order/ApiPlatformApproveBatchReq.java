package com.sankuai.shangou.seashop.m.thrift.core.request.order;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "平台批量审核退款参数")
public class ApiPlatformApproveBatchReq extends BaseParamReq {

    @FieldDoc(description = "退款单号", requiredness = Requiredness.REQUIRED)
    private List<Long> refundIdList;
    @FieldDoc(description = "平台备注")
    private String remark;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollUtil.isEmpty(refundIdList), "请选择需要处理的订单");
    }


}
