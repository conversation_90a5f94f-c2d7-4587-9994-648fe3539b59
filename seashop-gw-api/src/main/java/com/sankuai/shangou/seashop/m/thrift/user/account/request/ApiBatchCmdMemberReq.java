package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @description: 商家批量操作请求入参
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "商家批量操作请求入参")
public class ApiBatchCmdMemberReq extends BaseParamReq {
    /**
     * 商家ID列表
     */
    @FieldDoc(description = "商家ID列表")
    private List<Long> ids;


}
