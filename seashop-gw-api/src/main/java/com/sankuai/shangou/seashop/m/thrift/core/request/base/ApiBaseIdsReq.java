package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@TypeDoc(description = "通用请求对象，适用于店铺id与主键id集合等一些通用场景，如删除、获取等")
@Data
public class ApiBaseIdsReq extends BaseParamReq {

    @FieldDoc(description = "id集合")
    public List<Long> ids;

    @FieldDoc(description = "店铺id")
    public Long shopId;

    public void checkParameter() {
        if (this.ids == null || this.ids.size() < 1) {
            throw new IllegalArgumentException("id集合不能为空");
        }

    }

    public boolean hasIntersection(List<Long> list) {
        Set<Long> set1 = new HashSet<>(list);
        Set<Long> set2 = new HashSet<>(this.ids);
        set1.retainAll(set2);
        return !set1.isEmpty();
    }


}
