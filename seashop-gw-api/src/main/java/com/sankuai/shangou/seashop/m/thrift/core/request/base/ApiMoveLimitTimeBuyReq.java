package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.thrift.core.enums.MoveTypeEnum;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@TypeDoc(description = "新增限时购轮播图请求对象")
@Data
public class ApiMoveLimitTimeBuyReq extends BaseParamReq {

    @FieldDoc(description = "主键", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "移动类型:1上移，-1下移", requiredness = Requiredness.REQUIRED)
    private Integer type;

    @Override
    public void checkParameter() {
        if (this.id == null || this.id <= 0) {
            throw new InvalidParamException("主键不能为空");
        }
        if (this.type == null) {
            throw new InvalidParamException("移动类型不能为空");
        }
        if (MoveTypeEnum.getEnumByType(this.type) == null) {
            throw new InvalidParamException("移动类型不正确");
        }
    }


}
