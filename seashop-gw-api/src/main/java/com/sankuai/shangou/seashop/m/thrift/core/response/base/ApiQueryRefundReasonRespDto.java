package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:26
 */
@TypeDoc(description = "售后原因查询返参")
@Data
public class ApiQueryRefundReasonRespDto extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "售后原因")
    private String afterSalesText;

    @FieldDoc(description = "排序")
    private Integer sequence;

    @FieldDoc(description = "创建时间")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    private Date updateTime;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }
}
