package com.sankuai.shangou.seashop.m.thrift.core.response.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@TypeDoc(description = "保证金查询响应体")
@ToString
@Data
public class ApiCashDepositRefundResp extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺ID")
    private Long shopId;

    /**
     * 店铺名称
     */
    @FieldDoc(description = "店铺名称")
    private String shopName;

    /**
     * 状态 0，待审核；1，已通过；2，已拒绝；3，退款处理中；
     */
    @FieldDoc(description = "状态 0，待审核；1，已通过；2，已拒绝；3，退款处理中；")
    private Integer status;

    /**
     * 保证金
     */
    @FieldDoc(description = "保证金")
    private BigDecimal bond;

    /**
     * 扣除金额
     */
    @FieldDoc(description = "扣除金额")
    private BigDecimal deduction;

    /**
     * 退款金额
     */
    @FieldDoc(description = "退款金额")
    private BigDecimal refund;

    /**
     * 申请时间
     */
    @FieldDoc(description = "申请时间")
    private Date applyDate;


    public String getBondString() {
        return this.bigDecimal2String(this.bond);
    }


    public void setBondString(String bond) {
        this.bond = this.string2BigDecimal(bond);
    }


    public String getDeductionString() {
        return this.bigDecimal2String(this.deduction);
    }


    public void setDeductionString(String deduction) {
        this.deduction = this.string2BigDecimal(deduction);
    }


    public String getRefundString() {
        return this.bigDecimal2String(this.refund);
    }


    public void setRefundString(String refund) {
        this.refund = this.string2BigDecimal(refund);
    }


    public Long getApplyDateLong() {
        return this.date2Long(this.applyDate);
    }


    public void setApplyDateLong(Long applyDate) {
        this.applyDate = this.long2Date(applyDate);
    }
}
