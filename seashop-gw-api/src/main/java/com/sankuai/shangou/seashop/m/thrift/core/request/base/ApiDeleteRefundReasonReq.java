package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;


/**
 * @author： liweisong
 * @create： 2023/11/23 9:23
 */
@TypeDoc(description = "售后原因设置入参")
@Data
public class ApiDeleteRefundReasonReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;


    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
    }
}
