package com.sankuai.shangou.seashop.m.thrift.user.account.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 角色信息返回值
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "角色信息返回值")
@AllArgsConstructor
@NoArgsConstructor
public class ApiRoleRespList {

    /**
     * 角色信息返回参数列表
     */
    @FieldDoc(description = "角色信息返回参数列表")
    private List<ApiRoleResp> apiRoleRespList;


}
