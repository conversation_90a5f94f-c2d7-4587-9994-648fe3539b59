package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

@TypeDoc(description = "快递公司入参")
@Data
public class ApiUpdateExpressStatusReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "快递公司状态（0：正常，1：删除）")
    private Integer status;


    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (status == null) {
            throw new IllegalArgumentException("status不能为空");
        }
    }
}
