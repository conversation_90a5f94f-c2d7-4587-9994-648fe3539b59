package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 查询权限列表请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "查询权限列表请求入参")
public class ApiQueryRoleReq extends BaseParamReq {

    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺ID 0为平台")
    private Long shopId;


}
