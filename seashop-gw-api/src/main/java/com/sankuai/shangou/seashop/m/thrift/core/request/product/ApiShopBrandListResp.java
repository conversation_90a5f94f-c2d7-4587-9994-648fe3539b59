package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiShopBrandDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/14 10:48
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "商家品牌返回值")
public class ApiShopBrandListResp extends BaseThriftDto {

    @FieldDoc(description = "品牌列表")
    private List<ApiShopBrandDto> brandList;


}
