package com.sankuai.shangou.seashop.m.thrift.core.response.product.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/06 14:57
 */
@TypeDoc(
    description = "阶梯价信息"
)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ApiLadderPriceDto extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private Long productId;

    @FieldDoc(description = "最小批量")
    private Integer minBath;

    @FieldDoc(description = "最大批量")
    private Integer maxBath;

    @FieldDoc(description = "价格")
    private BigDecimal price;


    public String getPriceString() {
        return this.bigDecimal2String(this.price);
    }


    public void setPriceString(String price) {
        this.price = this.string2BigDecimal(price);
    }
}
