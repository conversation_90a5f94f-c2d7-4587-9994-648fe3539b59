package com.sankuai.shangou.seashop.m.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@TypeDoc(description = "消息通知列表返回")
@Data
public class ApiMessageNoticeSettingRes extends BaseThriftDto {
    @FieldDoc(description = "消息类型", requiredness = Requiredness.REQUIRED)
    private Integer messageType;

    @FieldDoc(description = "消息类型")
    private String messageTypeName;

    @FieldDoc(description = "是否开启邮箱通知")
    private Boolean emaillNotice;

    @FieldDoc(description = "是否开启短信通知")
    private Boolean smsNotice;

    @FieldDoc(description = "是否开启微信通知")
    private Boolean wxNotice;


}

