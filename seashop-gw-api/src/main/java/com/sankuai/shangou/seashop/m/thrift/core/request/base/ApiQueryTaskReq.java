package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "任务分页查询参数，包括供应商任务和平台任务")
public class ApiQueryTaskReq extends BasePageReq {

    @FieldDoc(description = "操作人ID")
    private Long operatorId;
    @FieldDoc(description = "查询环境")
    private String env;
    @FieldDoc(description = "业务类型。1：导出任务")
    private Integer bizType;
    @FieldDoc(description = "任务类型。具体的业务指定，需要唯一，最好具有一定的规则")
    private Integer taskType;
    @FieldDoc(description = "任务状态列表")
    private List<Integer> taskStatusList;

    @Override
    public void checkParameter() {
        super.checkParameter();
        if (operatorId == null) {
            throw new InvalidParamException("operatorId不能为空");
        }
        if (StrUtil.isBlank(env)) {
            throw new InvalidParamException("env不能为空");
        }
    }


}
