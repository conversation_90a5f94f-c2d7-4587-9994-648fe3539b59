package com.sankuai.shangou.seashop.m.thrift.user.shop.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 供应商信息返回参数列表
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "供应商信息返回参数列表")
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopRespList {

    /**
     * 供应商信息返回参数列表
     */
    @FieldDoc(description = "供应商信息返回参数列表")
    private List<ApiShopResp> apiShopRespList;


}
