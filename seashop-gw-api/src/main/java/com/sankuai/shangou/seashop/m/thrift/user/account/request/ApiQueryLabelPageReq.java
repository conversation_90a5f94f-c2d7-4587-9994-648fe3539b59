package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

/**
 * @description: 供应商列表查询入参
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "商家添加商品到购物车请求入参")
public class ApiQueryLabelPageReq extends BasePageReq {
    /**
     * 标签名称
     */
    @FieldDoc(description = "标签名称")
    private String labelName;


}
