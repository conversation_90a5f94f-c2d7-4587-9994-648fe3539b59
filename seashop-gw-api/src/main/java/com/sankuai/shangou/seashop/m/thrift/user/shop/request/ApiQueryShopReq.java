package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 供应商名称公共入参， 后续有其他入参可以加在这里
 *
 * <AUTHOR>
 * @date 2023/11/07 17:05
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "供应商查询入参")
public class ApiQueryShopReq {

    @FieldDoc(description = "供应商名称")
    private String shopName;


}
