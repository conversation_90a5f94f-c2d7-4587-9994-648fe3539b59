package com.sankuai.shangou.seashop.m.thrift.core.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description：
 * @author： liweisong
 * @create： 2023/11/21 18:56
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "查询申请记录入参")
public class ApiQueryMComplaintReq extends BasePageReq {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "订单id")
    private String orderId;

    @FieldDoc(description = "审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束,5:取消)")
    private Integer status;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "商家id")
    private Long userId;

    @FieldDoc(description = "商家名称")
    private String userName;

    @FieldDoc(description = "创建时间起")
    private Date createTimeStart;

    @FieldDoc(description = "创建时间止")
    private Date createTimeEnd;


    public Long getCreateTimeStartLong() {
        return this.date2Long(this.createTimeStart);
    }


    public void setCreateTimeStartLong(Long createTimeStart) {
        this.createTimeStart = this.long2Date(createTimeStart);
    }


    public Long getCreateTimeEndLong() {
        return this.date2Long(this.createTimeEnd);
    }


    public void setCreateTimeEndLong(Long createTimeEnd) {
        this.createTimeEnd = this.long2Date(createTimeEnd);
    }
}
