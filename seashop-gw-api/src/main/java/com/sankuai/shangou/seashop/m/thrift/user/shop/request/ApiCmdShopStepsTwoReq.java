package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

public class ApiCmdShopStepsTwoReq extends BaseParamReq {
    @FieldDoc(description = "id")
    private Long shopId;
    @FieldDoc(description = "银行账号")
    private String bankAccountNumber;
    @FieldDoc(description = "银行开户名")
    private String bankAccountName;
    @FieldDoc(description = "银行编码")
    private String bankCode;
    @FieldDoc(description = "开户银行所在地")
    private String bankName;
    @FieldDoc(description = "银行类型")
    private Integer bankType;
    @FieldDoc(description = "银行省份")
    private Integer bankRegionProv;
    @FieldDoc(description = "银行城市")
    private Integer bankRegionCity;
    @FieldDoc(description = "银行地区")
    private Integer bankRegionId;


}