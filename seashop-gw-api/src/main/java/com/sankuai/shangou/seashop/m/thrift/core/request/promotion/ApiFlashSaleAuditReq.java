package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "审核限时购活动请求对象")
public class ApiFlashSaleAuditReq extends BaseParamReq {

    @FieldDoc(description = "活动ID", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "是否通过审核", requiredness = Requiredness.REQUIRED)
    private Boolean passFlag;

    @Override
    public void checkParameter() {
        if (id == null || id <= 0) {
            throw new InvalidParamException("活动ID不能为空");
        }
        if (passFlag == null) {
            throw new InvalidParamException("是否通过审核不能为空");
        }
    }


}
