package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询保证列表请求对象")
public class ApiCashDepositQueryReq extends BasePageReq {

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "店铺ID")
    private Long shopId;


}
