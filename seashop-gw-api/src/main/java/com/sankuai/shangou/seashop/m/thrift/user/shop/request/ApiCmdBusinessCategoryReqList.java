package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 店铺类目修改列表请求入参
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "店铺类目修改列表请求入参")
public class ApiCmdBusinessCategoryReqList extends BaseParamReq {
    @FieldDoc(description = "店铺类目列表")
    public List<ApiCmdBusinessCategoryReq> reqList;


}
