package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:05
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "更新类目属性参数")
public class ApiUpdateCategoryParamReq extends BaseParamReq {

    /**
     * 主键
     */
    @FieldDoc(description = "类目id", requiredness = Requiredness.REQUIRED)
    private Long id;

    /**
     * 类目名称
     */
    @FieldDoc(description = "类目名称", requiredness = Requiredness.NONE)
    private String name;

    /**
     * 排序
     */
    @FieldDoc(description = "排序", requiredness = Requiredness.NONE)
    private Long displaySequence;

    /**
     * 分佣比例
     */
    @FieldDoc(description = "分佣比例 创建三级类目时才需要设置", requiredness = Requiredness.NONE)
    private BigDecimal commissionRate;

    /**
     * 是否显示
     */
    @FieldDoc(description = "是否显示", requiredness = Requiredness.NONE)
    private Boolean whetherShow;


    public String getCommissionRateString() {
        return this.bigDecimal2String(this.commissionRate);
    }


    public void setCommissionRateString(String commissionRate) {
        this.commissionRate = this.string2BigDecimal(commissionRate);
    }


}
