package com.sankuai.shangou.seashop.m.thrift.core.dto.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "折扣规则响应对象")
public class ApiDiscountActiveRuleDto extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "活动ID")
    private Long activeId;

    @FieldDoc(description = "满减的条件")
    private BigDecimal quota;

    @FieldDoc(description = "满减的金额")
    private BigDecimal discount;


    public String getQuotaString() {
        return this.bigDecimal2String(this.quota);
    }


    public void setQuotaString(String quota) {
        this.quota = this.string2BigDecimal(quota);
    }


    public String getDiscountString() {
        return this.bigDecimal2String(this.discount);
    }


    public void setDiscountString(String discount) {
        this.discount = this.string2BigDecimal(discount);
    }
}
