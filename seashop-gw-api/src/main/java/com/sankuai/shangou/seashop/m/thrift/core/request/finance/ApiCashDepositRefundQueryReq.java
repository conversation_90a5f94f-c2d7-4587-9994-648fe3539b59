package com.sankuai.shangou.seashop.m.thrift.core.request.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保证金退款查询条件对象")
public class ApiCashDepositRefundQueryReq extends BasePageReq {

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    /**
     * TO_AUDIT(0, "待审核"),
     * PASS(1, "通过"),
     * REFUSE(2, "拒绝"),
     * REFUNDING(3, "退款处理中"),
     * FAIL(4, "退款失败");
     */
    @FieldDoc(description = "退款状态:0待审核,1通过,2拒绝,3退款处理中,4退款失败")
    private Integer status;


}
