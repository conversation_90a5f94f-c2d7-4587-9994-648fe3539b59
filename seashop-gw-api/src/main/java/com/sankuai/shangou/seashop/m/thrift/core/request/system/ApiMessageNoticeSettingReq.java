package com.sankuai.shangou.seashop.m.thrift.core.request.system;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@TypeDoc(description = "消息通知设置")
@Data
public class ApiMessageNoticeSettingReq extends BaseThriftDto {

    /**
     * 数据库维护字典 前端由列表接口中获取
     */

    @FieldDoc(description = "消息类型", requiredness = Requiredness.REQUIRED)
    private Integer messageType;

    @FieldDoc(description = "是否开启邮箱通知")
    private Boolean emaillNotice;

    @FieldDoc(description = "是否开启短信通知")
    private Boolean smsNotice;

    @FieldDoc(description = "是否开启微信通知")
    private Boolean wxNotice;


}
