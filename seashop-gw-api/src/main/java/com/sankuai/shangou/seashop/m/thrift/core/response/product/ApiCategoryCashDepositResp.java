package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "保证金配置")
public class ApiCategoryCashDepositResp extends BaseThriftDto {

    /**
     * 类目id
     */
    @FieldDoc(description = "类目id")
    private Long categoryId;

    /**
     * 类目名称
     */
    @FieldDoc(description = "类目名称")
    private String categoryName;

    /**
     * 需要缴纳保证金
     */
    @FieldDoc(description = "需要缴纳保证金")
    private BigDecimal needPayCashDeposit;

    /**
     * 允许七天无理由退货
     */
    @FieldDoc(description = "允许七天无理由退货")
    private Boolean enableNoReasonReturn;


    public String getNeedPayCashDepositString() {
        return this.bigDecimal2String(this.needPayCashDeposit);
    }


    public void setNeedPayCashDepositString(String needPayCashDeposit) {
        this.needPayCashDeposit = this.string2BigDecimal(needPayCashDeposit);
    }


}
