package com.sankuai.shangou.seashop.m.thrift.core.request.base;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(description = "地区修改对象")
@Data
public class ApiBaseUpdateRegionReq extends BaseThriftDto {
    private Long id;

    @FieldDoc(description = "地区名称", requiredness = Requiredness.REQUIRED)
    private String name;

    public void checkParameter() {

        AssertUtil.throwInvalidParamIfTrue(this.id == null, "id不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(this.name), "请输入地址名称");
    }


}
