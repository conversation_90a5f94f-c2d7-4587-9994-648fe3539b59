package com.sankuai.shangou.seashop.m.thrift.core.request.pay;

import cn.hutool.core.date.DateUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "账单下载请求对象")
public class ApiBillDownloadReq extends BaseParamReq {

    @FieldDoc(description = "账单日期，格式：yyyy-MM-dd", requiredness = Requiredness.REQUIRED)
    private Date billDate;

    @Override
    public void checkParameter() {
        if (null == this.billDate) {
            throw new InvalidParamException("billDate不能为空");
        }
        Date now = DateUtil.beginOfDay(new Date());
        if (this.billDate.after(now)) {
            throw new InvalidParamException("billDate不能大于今天");
        }

    }


    public Long getBillDateLong() {
        return this.date2Long(this.billDate);
    }


    public void setBillDateLong(Long billDate) {
        this.billDate = this.long2Date(billDate);
    }
}
