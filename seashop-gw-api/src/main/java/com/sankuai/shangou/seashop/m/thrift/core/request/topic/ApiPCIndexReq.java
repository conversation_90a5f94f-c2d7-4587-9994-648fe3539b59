package com.sankuai.shangou.seashop.m.thrift.core.request.topic;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "首页对象")
public class ApiPCIndexReq extends BaseThriftDto {
    /**
     * 首页的json数据
     */
    @FieldDoc(description = "首页的json数据")
    private String content;


    /**
     * 获取商品分组的url
     */
    @FieldDoc(description = "获取商品分组的url")
    private String getGoodGroupUrl;

    /**
     * 获取商品的url
     */
    @FieldDoc(description = "获取商品的url")
    private String getGoodUrl;


    public void checkParameter() {
        if (StringUtils.isBlank(this.content)) {
            throw new IllegalArgumentException("json数据不能为空");
        }


    }


}
