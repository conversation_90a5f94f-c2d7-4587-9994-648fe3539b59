package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@TypeDoc(description = "微信公众号菜单实体")
@Data
public class ApiBaseWXMenuRes extends BaseThriftDto {
    @FieldDoc(description = "id")
    private Long id;

    @FieldDoc(description = "菜单名称")
    private String name;

    @FieldDoc(description = "链接类型")
    private Integer linkType;

    @FieldDoc(description = "链接值")
    private String linkValue;

    @FieldDoc(description = "父级ID")
    private Long parentId;

    @FieldDoc(description = "是否自定义链接")
    private Integer whetherCustom;


    @Override
    public String toString() {
        return "BaseWXMenuRes{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", linkType=" + linkType +
            ", linkValue='" + linkValue + '\'' +
            ", parentId=" + parentId +
            ", whetherCustom=" + whetherCustom +
            '}';
    }
}
