package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "专享价活动查询请求对象")
public class ApiExclusivePriceQueryReq extends BasePageReq {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "专享价活动名称")
    private String name;

    @FieldDoc(description = "店铺ID列表")
    private List<Long> shopIdList;

    @FieldDoc(description = "状态 0-未开始 1-进行中 2-已结束")
    private Integer status;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }
}
