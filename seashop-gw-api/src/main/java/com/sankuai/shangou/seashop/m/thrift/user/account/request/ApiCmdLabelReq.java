package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * @description: 标签单个操作请求入参
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "标签单个操作请求入参")
public class ApiCmdLabelReq extends BaseParamReq {
    /**
     * 标签ID
     */
    @FieldDoc(description = "标签ID")
    private Long labelId;
    /**
     * 标签名称
     */
    @FieldDoc(description = "标签名称")
    private String labelName;
    /**
     * 操作人ID
     */
    @FieldDoc(description = "操作人ID")
    private Long operatorId;


}
