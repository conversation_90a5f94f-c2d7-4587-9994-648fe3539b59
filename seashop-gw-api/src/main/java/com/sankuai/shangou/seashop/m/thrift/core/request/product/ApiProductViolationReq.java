package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 17:46
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "商品违规下架入参")
public class ApiProductViolationReq extends BaseParamReq {

    @FieldDoc(description = "商品id的集合", requiredness = Requiredness.REQUIRED)
    private List<Long> productIdList;

    @FieldDoc(description = "违规原因", requiredness = Requiredness.REQUIRED)
    private String auditReason;


}
