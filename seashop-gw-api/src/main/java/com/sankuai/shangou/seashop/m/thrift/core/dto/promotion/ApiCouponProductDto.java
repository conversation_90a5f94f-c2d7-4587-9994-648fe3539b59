package com.sankuai.shangou.seashop.m.thrift.core.dto.promotion;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/20/020
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "优惠券对应的商品信息DTO")
public class ApiCouponProductDto extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private Long productId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "商品图片")
    @JsonUrlFormat(deserializer = false)
    private String imagePath;

    @FieldDoc(description = "库存")
    private Long stock;

    @FieldDoc(description = "最小销售价")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "货号")
    private String productCode;


    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


}
