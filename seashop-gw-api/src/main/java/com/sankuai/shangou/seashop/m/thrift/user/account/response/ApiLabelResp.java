package com.sankuai.shangou.seashop.m.thrift.user.account.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 标签信息
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "标签信息")
@AllArgsConstructor
@NoArgsConstructor
public class ApiLabelResp {
    @FieldDoc(description = "标签名称")
    private String labelName;
    @FieldDoc(description = "标签ID")
    private Long id;
    @FieldDoc(description = "商家数量")
    private Long memberCount;


}
