package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import cn.hutool.core.util.StrUtil;
import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@TypeDoc(description = "新增限时购轮播图请求对象")
@Data
public class ApiUpdateLimitTimeBuyReq extends BaseParamReq {

    @FieldDoc(description = "主键", requiredness = Requiredness.REQUIRED)
    private Long id;


    /**
     * 图片保存URL
     */
    @FieldDoc(description = "图片保存URL", requiredness = Requiredness.REQUIRED)
    @JsonUrlFormat(deserializer = false)
    private String imageUrl;

    /**
     * 图片跳转URL
     */
    @FieldDoc(description = "图片跳转URL", requiredness = Requiredness.REQUIRED)
    @JsonUrlFormat(deserializer = false)
    private String url;

    @Override
    public void checkParameter() {
        if (id == null || id <= 0) {
            throw new InvalidParamException("主键不能为空");
        }
        if (StrUtil.isBlank(this.imageUrl)) {
            throw new InvalidParamException("图片保存URL不能为空");
        }
        if (StrUtil.isBlank(this.url)) {
            throw new InvalidParamException("图片跳转URL不能为空");
        }
    }


}
