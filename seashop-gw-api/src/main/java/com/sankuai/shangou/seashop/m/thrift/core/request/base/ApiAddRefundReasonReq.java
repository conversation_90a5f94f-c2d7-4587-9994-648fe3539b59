package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


/**
 * @author： liweisong
 * @create： 2023/11/23 9:23
 */
@TypeDoc(description = "售后原因设置入参")
@Data
public class ApiAddRefundReasonReq extends BaseParamReq {

    @FieldDoc(description = "售后原因")
    private String afterSalesText;


    public void checkParameter() {
        if (StringUtils.isEmpty(afterSalesText)) {
            throw new IllegalArgumentException("售后原因不能为空");
        }
        if (afterSalesText.length() > 20) {
            throw new IllegalArgumentException("售后原因不能超过20个字符");
        }
    }
}
