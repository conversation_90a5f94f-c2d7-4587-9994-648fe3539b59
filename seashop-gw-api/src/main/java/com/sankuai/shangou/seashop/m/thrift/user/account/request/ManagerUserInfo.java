package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.dto.EpAccountDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@Data
@TypeDoc(description = "商家账号信息")
@NoArgsConstructor
@AllArgsConstructor
public class ManagerUserInfo extends EpAccountDto {

    @FieldDoc(description = "系统用户信息")
    private LoginManagerDto loginDto;


}
