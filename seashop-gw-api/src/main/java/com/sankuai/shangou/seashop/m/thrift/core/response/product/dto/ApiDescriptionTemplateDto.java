package com.sankuai.shangou.seashop.m.thrift.core.response.product.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:19
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "版式分页列表")
public class ApiDescriptionTemplateDto extends BaseThriftDto {

    @FieldDoc(description = "版式id")
    private Long id;

    @FieldDoc(description = "版式名称 最多30个字")
    private String name;

    @FieldDoc(description = "版式位置 1-顶部 2-底部")
    private Integer positionCode;

    @FieldDoc(description = "版式位置描述")
    private String positionDesc;


}
