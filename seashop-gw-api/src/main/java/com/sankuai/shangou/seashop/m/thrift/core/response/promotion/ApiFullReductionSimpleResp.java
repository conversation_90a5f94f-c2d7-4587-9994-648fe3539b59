package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@TypeDoc(description = "满减活动列表响应体")
@ToString
@Data
public class ApiFullReductionSimpleResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "活动名称")
    private String activeName;

    @FieldDoc(description = "单笔订单满减金额门槛")
    private BigDecimal moneyOffCondition;

    @FieldDoc(description = "单笔订单满减金额")
    private BigDecimal moneyOffFee;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "状态")
    private Integer status;

    @FieldDoc(description = "状态名称")
    private String statusDesc;

    @FieldDoc(description = "是否叠加优惠")
    private Boolean moneyOffOverLay;


    public String getMoneyOffConditionString() {
        return this.bigDecimal2String(this.moneyOffCondition);
    }


    public void setMoneyOffConditionString(String moneyOffCondition) {
        this.moneyOffCondition = this.string2BigDecimal(moneyOffCondition);
    }


    public String getMoneyOffFeeString() {
        return this.bigDecimal2String(this.moneyOffFee);
    }


    public void setMoneyOffFeeString(String moneyOffFee) {
        this.moneyOffFee = this.string2BigDecimal(moneyOffFee);
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
