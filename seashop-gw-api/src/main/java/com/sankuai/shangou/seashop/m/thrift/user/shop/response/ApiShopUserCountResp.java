package com.sankuai.shangou.seashop.m.thrift.user.shop.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "用户统计信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopUserCountResp {
    @FieldDoc(description = "今日新增商家")
    private Integer todayAddShopCount;
    @FieldDoc(description = "供应商总数")
    private Integer shopCount;
    @FieldDoc(description = "今日新增供应商")
    private Integer todayAddSupplierCount;
    @FieldDoc(description = "待审核供应商")
    private Integer waitAuditSupplierCount;
    @FieldDoc(description = "昨日新增供应商")
    private Integer yesterdayAddSupplierCount;
    @FieldDoc(description = "到期供应商")
    private Integer expireShopCount;
    @FieldDoc(description = "待处理提现店铺")
    private Integer waitWithdrawShopCount;


}
