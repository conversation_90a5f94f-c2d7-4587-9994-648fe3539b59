package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "会员操作请求对象")
@Data
public class ApiCmdPurchaseSendAllCoupon extends BaseParamReq {

    // 优惠券ID列表
    @FieldDoc(description = "优惠券ID列表")
    private List<Long> couponIds;

    // 优惠券ID列表
    @FieldDoc(description = "标签ID列表")
    private List<Long> labelId;

    @FieldDoc(description = "是否发给所有人")
    private Boolean sendAll;


}
