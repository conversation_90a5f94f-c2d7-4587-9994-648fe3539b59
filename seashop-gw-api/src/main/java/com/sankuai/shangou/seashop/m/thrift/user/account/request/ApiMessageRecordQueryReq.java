package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
@Data
@TypeDoc(description = "消息记录请求体")
@NoArgsConstructor
@AllArgsConstructor
public class ApiMessageRecordQueryReq extends BasePageReq {
    @FieldDoc(description = "消息类型 0微信 1邮件 2优惠券 3短信")
    private Integer messageType;
    @FieldDoc(description = "内容类型 0图文消息 1文本 2语音 3图片 4视频 5卡券")
    private Integer contentType;
    @FieldDoc(description = "消息状态 0发送失败 1发送成功")
    private Integer messageStatus;
    @FieldDoc(description = "发送开始时间")
    private Date sendStartTime;
    @FieldDoc(description = "发送结束时间")
    private Date sendEndTime;


    public Long getSendStartTimeLong() {
        return this.date2Long(this.sendStartTime);
    }


    public void setSendStartTimeLong(Long sendStartTime) {
        this.sendStartTime = this.long2Date(sendStartTime);
    }


    public Long getSendEndTimeLong() {
        return this.date2Long(this.sendEndTime);
    }


    public void setSendEndTimeLong(Long sendEndTime) {
        this.sendEndTime = this.long2Date(sendEndTime);
    }
}
