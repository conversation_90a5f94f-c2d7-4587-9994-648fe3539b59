package com.sankuai.shangou.seashop.m.thrift.user.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 19:10
 */
@TypeDoc(description = "店铺信息")
@Data
public class ApiShopQueryReq extends BaseParamReq {

    @FieldDoc(description = "店铺id",requiredness = Requiredness.REQUIRED)
    private List<Long> shopIds;


    public void checkParameter(){
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(shopIds), "shopIds不能为空");
        AssertUtil.throwIfTrue(shopIds.size()>200, "shopIds一次性查询数量不能超过200");
    }
}
