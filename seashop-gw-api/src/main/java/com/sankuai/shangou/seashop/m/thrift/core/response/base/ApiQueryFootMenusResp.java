package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.thrift.core.dto.QueryFootMenusRespDto;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:46
 */
@Data
@TypeDoc(description = "底部菜单栏")
public class ApiQueryFootMenusResp extends BaseThriftDto {

    @FieldDoc(description = "底部菜单栏集合")
    private List<QueryFootMenusRespDto> menusRespDtoList;


}
