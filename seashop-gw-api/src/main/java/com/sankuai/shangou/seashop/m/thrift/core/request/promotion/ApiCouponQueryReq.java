package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "优惠券查询请求对象")
public class ApiCouponQueryReq extends BasePageReq {

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "优惠券名称")
    private String couponName;

    @FieldDoc(description = "店铺ID列表")
    private List<Long> shopIdList;

    @FieldDoc(description = "状态 0-未开始 1-进行中 2-已结束")
    private Integer status;

    @FieldDoc(description = "可以领用的：true-可以领用的 false-不可以领用的（true时包含状态0、1的数据）")
    private Boolean claimable;

    @FieldDoc(description = "领取方式 0 店铺首页 1 积分兑换 2 主动发放")
    private List<Integer> receiveTypeList;


}
