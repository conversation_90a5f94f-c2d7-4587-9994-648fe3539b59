package com.sankuai.shangou.seashop.m.thrift.core.response.product.dto;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:42
 */
@TypeDoc(
    description = "品牌信息"
)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ApiBrandDto {

    @FieldDoc(description = "品牌id")
    private Long id;

    @FieldDoc(description = "品牌名称")
    private String name;

    @FieldDoc(description = "排序")
    private Long displaySequence;

    @FieldDoc(description = "logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;

    @FieldDoc(description = "品牌简介")
    private String description;

    @FieldDoc(description = "品牌来源")
    private String applyMode;


}
