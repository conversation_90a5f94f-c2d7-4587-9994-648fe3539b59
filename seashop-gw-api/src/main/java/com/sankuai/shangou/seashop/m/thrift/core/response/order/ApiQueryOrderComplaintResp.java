package com.sankuai.shangou.seashop.m.thrift.core.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description：
 * @author： liweisong
 * @create： 2023/11/21 18:56
 */
@Data
@ToString
@TypeDoc(description = "查询申请记录返参")
public class ApiQueryOrderComplaintResp extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "订单id")
    private String orderId;

    @FieldDoc(description = "审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束)")
    private Integer status;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "店铺联系方式")
    private String shopPhone;

    @FieldDoc(description = "商家id")
    private Long userId;

    @FieldDoc(description = "商家名称")
    private String userName;

    @FieldDoc(description = "商家联系方式")
    private String userPhone;

    @FieldDoc(description = "投诉日期")
    private Date complaintDate;

    @FieldDoc(description = "投诉原因")
    private String complaintReason;

    @FieldDoc(description = "店铺反馈信息")
    private String sellerReply;

    @FieldDoc(description = "投诉备注")
    private String platRemark;

    @FieldDoc(description = "创建时间")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    private Date updateTime;

    @FieldDoc(description = "实付金额")
    private BigDecimal totalAmount;

    @FieldDoc(description = "支付方式数值")
    private Integer payment;

    @FieldDoc(description = "支付方式")
    private String payMethod;


    public Long getComplaintDateLong() {
        return this.date2Long(this.complaintDate);
    }


    public void setComplaintDateLong(Long complaintDate) {
        this.complaintDate = this.long2Date(complaintDate);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


}
