package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "保证金配置列表")
public class ApiCategoryCashDepositListReq extends BaseParamReq {

    @FieldDoc(description = "保证金配置列表", requiredness = Requiredness.NONE)
    private List<ApiCategoryCashDepositReq> list;


}
