package com.sankuai.shangou.seashop.m.thrift.core.response.product.dto;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:16
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "商品详情")
public class ApiProductDetailDto extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "类目id")
    private Long categoryId;

    @FieldDoc(description = "类目名称")
    private String categoryName;

    @FieldDoc(description = "类目全路径名称")
    private String fullCategoryName;

    @FieldDoc(description = "类目全路径id")
    private List<Long> fullCategoryIds;

    @FieldDoc(description = "品牌id")
    private Long brandId;

    @FieldDoc(description = "商品名称(限100字)")
    private String productName;

    @FieldDoc(description = "广告词")
    private String shortDescription;

    @FieldDoc(description = "是否开启阶梯价")
    private Boolean whetherOpenLadder;

    @FieldDoc(description = "阶梯价")
    private List<ApiLadderPriceDto> ladderPriceList;

    @FieldDoc(description = "商城价(可不传,多个规格的最小商城价,会获取sku集合中的最小商城价)")
    private BigDecimal minSalePrice;

    @FieldDoc(description = "市场价")
    private BigDecimal marketPrice;

    @FieldDoc(description = "库存(可不传,会计算sku集合中的库存之和)")
    private Long stock;

    @FieldDoc(description = "商品货号(限100个字符)(同一店铺不能重复)")
    private String productCode;

    @FieldDoc(description = "计量单位")
    private String measureUnit;

    @FieldDoc(description = "限购数")
    private Integer maxBuyCount;

    @FieldDoc(description = "倍数起购量")
    private Integer multipleCount;

    @FieldDoc(description = "警戒库存(可不传, 会获取第一个规格的安全库存)")
    private Long safeStock;

    @FieldDoc(description = "店铺分类")
    private List<Long> shopCategoryIdList;

    @FieldDoc(description = "是否开启规格")
    private Boolean hasSku;

    @FieldDoc(description = "sku集合 单规格有一条默认值")
    private List<ApiProductDetailSkuDto> skuList;

    @FieldDoc(description = "运费模板")
    private Long freightTemplateId;

    @FieldDoc(description = "重量(根据运费模板确定是否必填)")
    private BigDecimal weight;

    @FieldDoc(description = "体积(根据运费模板确定是否必填)")
    private BigDecimal volume;

    @FieldDoc(description = "主图集合(最多五张)")
    @JsonUrlFormat(deserializer = false)
    private List<String> imageList;

    @FieldDoc(description = "主图视频")
    private String videoPath;

    @FieldDoc(description = "PC端描述")
    private String description;

    @FieldDoc(description = "移动端描述")
    private String mobileDescription;

    @FieldDoc(description = "顶部版式Id")
    private Long descriptionPrefixId;

    @FieldDoc(description = "底部版式Id")
    private Long descriptionSuffixId;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "来源 1-商城 2-牵牛花 3-易久批")
    private Integer source;

    @FieldDoc(description = "来源描述")
    private String sourceDesc;

    @FieldDoc(description = "是否绑定了牵牛花商品")
    private Boolean hasBindQnh;

    @FieldDoc(description = "规格1别名")
    private String spec1Alias;

    @FieldDoc(description = "规格2别名")
    private String spec2Alias;

    @FieldDoc(description = "规格3别名")
    private String spec3Alias;

    @FieldDoc(description = "商品状态 1-销售中 2-仓库中 3-违规下架 4-草稿箱")
    private Integer status;

    @FieldDoc(description = "商品状态描述")
    private String statusDesc;

    @FieldDoc(description = "第一个规格的规格自增id")
    private Long skuAutoId;

    @FieldDoc(description = "规格选择列表")
    private List<SpecificationResp> specSelectList;


    @Schema(description = "OE号")
    private String oeCode;
    @Schema(description = "品牌号")
    private String brandCode;
    @Schema(description = "零件品质 [0 4S-原厂原包,1 进口-原厂原包 2 国产-原厂原包 3 原厂无包 4 品牌件]")
    private Integer partQuality;
    @Schema(description = "质保时间")
    private Integer warrantyPeriod;
    @Schema(description = "适用车型")
    private String adaptableCar;
    @Schema(description = "零件规格")
    private String partSpec;
    @Schema(description = "替换号")
    private String replaceNumber;
    @Schema(description = "备注")
    private String remark;
    @FieldDoc(description = "移动端商品描述图片列表")
    private List<String> descriptionPicList;

    public String getMinSalePriceString() {
        return this.bigDecimal2String(this.minSalePrice);
    }


    public void setMinSalePriceString(String minSalePrice) {
        this.minSalePrice = this.string2BigDecimal(minSalePrice);
    }


    public String getMarketPriceString() {
        return this.bigDecimal2String(this.marketPrice);
    }


    public void setMarketPriceString(String marketPrice) {
        this.marketPrice = this.string2BigDecimal(marketPrice);
    }


    public String getWeightString() {
        return this.bigDecimal2String(this.weight);
    }


    public void setWeightString(String weight) {
        this.weight = this.string2BigDecimal(weight);
    }


    public String getVolumeString() {
        return this.bigDecimal2String(this.volume);
    }


    public void setVolumeString(String volume) {
        this.volume = this.string2BigDecimal(volume);
    }


}
