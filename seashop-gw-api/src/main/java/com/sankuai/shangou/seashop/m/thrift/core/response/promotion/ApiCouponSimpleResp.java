package com.sankuai.shangou.seashop.m.thrift.core.response.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@TypeDoc(description = "优惠券活动响应体")
@ToString
@Data
public class ApiCouponSimpleResp extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "优惠劵金额")
    private BigDecimal price;

    @FieldDoc(description = "最大可领取张数")
    private Integer perMax;

    @FieldDoc(description = "订单金额（满足多少钱才能使用）")
    private BigDecimal orderAmount;

    @FieldDoc(description = "发行张数")
    private Integer num;

    @FieldDoc(description = "开始时间")
    private Date startTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "优惠券名称")
    private String couponName;

    @FieldDoc(description = "领用人数")
    private Integer receiveCount;

    @FieldDoc(description = "领用张数")
    private Integer receiveNum;

    @FieldDoc(description = "已使用数量")
    private Integer useCount;

    @FieldDoc(description = "状态")
    private Integer status;

    @FieldDoc(description = "状态名称")
    private String statusDesc;

    @FieldDoc(description = "商品数量")
    private Long productCount;


    public String getPriceString() {
        return this.bigDecimal2String(this.price);
    }


    public void setPriceString(String price) {
        this.price = this.string2BigDecimal(price);
    }


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
