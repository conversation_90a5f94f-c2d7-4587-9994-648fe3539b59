package com.sankuai.shangou.seashop.m.thrift.core.request.express;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "物流轨迹查询对象")
public class ExpressQueryReq extends BaseParamReq {
    @FieldDoc(
        description = "快递公司code"
    )
    private String companyCode;
    @FieldDoc(
        description = "快递单号"
    )
    private String expressNo;

    @FieldDoc(
            description = "快递单号"
    )
    private String receiveMobile;
}
