package com.sankuai.shangou.seashop.m.thrift.core.response.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "订单统计返回值列表")
public class ApiOrderStatisticsListResp {

    @FieldDoc(description = "订单统计返回值列表")
    private List<ApiOrderStatisticsDataResp> list;


}
