package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author： liweisong
 * @create： 2023/11/22 17:06
 */
@TypeDoc(description = "交易参数设置入参")
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ApiTradeSiteSettingsReq extends BaseParamReq {

    @FieldDoc(description = "未付款超时")
    private Long unpaidTimeout;

    @FieldDoc(description = "确认收货超时")
    private Long noReceivingTimeout;

    @FieldDoc(description = "自动收货完成前时间")
    private Long beforeReceivingDays;

    @FieldDoc(description = "延迟收货时间")
    private Long noReceivingDelayDays;

    @FieldDoc(description = "关闭评价通道时限")
    private Long orderCommentTimeout;

    @FieldDoc(description = "供应商未发货自动短信提醒时限")
    private Long orderWaitDeliveryRemindTime;

    @FieldDoc(description = "企业网银限制金额")
    private BigDecimal companyBankOrderAmount;

    @FieldDoc(description = "订单退货期限")
    private Long salesReturnTimeout;

    @FieldDoc(description = "供应商自动确认售后时限")
    private Long shopConfirmTimeout;

    @FieldDoc(description = "用户发货限时")
    private Long sendGoodsCloseTimeout;

    @FieldDoc(description = "供应商确认到货时限")
    private Long shopNoReceivingTimeout;


    public String getCompanyBankOrderAmountString() {
        return this.bigDecimal2String(this.companyBankOrderAmount);
    }


    public void setCompanyBankOrderAmountString(String companyBankOrderAmount) {
        this.companyBankOrderAmount = this.string2BigDecimal(companyBankOrderAmount);
    }
}
