package com.sankuai.shangou.seashop.m.thrift.core.request.base;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseMsgTemplateReq;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:02
 */
@Data
public class ApiSaveMsgTemplateReq extends BaseParamReq {

    @FieldDoc(description = "小程序AppId")
    private String weixinAppletId;

    @FieldDoc(description = "小程序AppSecret")
    private String weixinAppletSecret;

    @FieldDoc(description = "模版集合")
    private List<BaseMsgTemplateReq> values;


}
