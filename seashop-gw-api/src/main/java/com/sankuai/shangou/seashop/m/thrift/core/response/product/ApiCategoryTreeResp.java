package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "平台类目树节点")
public class ApiCategoryTreeResp {

    @FieldDoc(description = "类目集合")
    private String result;


}
