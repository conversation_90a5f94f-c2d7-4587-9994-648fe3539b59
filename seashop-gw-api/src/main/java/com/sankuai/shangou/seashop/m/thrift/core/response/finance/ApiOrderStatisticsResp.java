package com.sankuai.shangou.seashop.m.thrift.core.response.finance;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "订单统计返回值")
public class ApiOrderStatisticsResp extends BaseThriftDto {

    /**
     * 取数逻辑
     * a.OrderDate >= begin && a.OrderDate < end
     */
    @FieldDoc(description = "订单数")
    private Long ordersNum;

    /**
     * 取数逻辑
     * a.PayDate >= begin && a.PayDate < end
     */
    @FieldDoc(description = "支付订单数")
    private Long payOrdersNum;

    /**
     * 取数逻辑
     * 基于支付订单的基础上
     * p.ProductTotalAmount + p.Freight + p.Tax - p.DiscountAmount - p.FullDiscount - p.MoneyOff
     */
    @FieldDoc(description = "交易金额")
    private BigDecimal saleAmount;


    public String getSaleAmountString() {
        return this.bigDecimal2String(this.saleAmount);
    }


    public void setSaleAmountString(String saleAmount) {
        this.saleAmount = this.string2BigDecimal(saleAmount);
    }
}
