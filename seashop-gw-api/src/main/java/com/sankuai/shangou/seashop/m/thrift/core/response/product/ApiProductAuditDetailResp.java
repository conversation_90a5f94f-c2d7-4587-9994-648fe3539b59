package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiProductDetailDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 13:43
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "商品审核详情返回值")
public class ApiProductAuditDetailResp extends BaseThriftDto {

    @FieldDoc(description = "商品审核详情")
    private ApiProductDetailDto productAudit;

    @FieldDoc(description = "商品原始详情")
    private ApiProductDetailDto originProduct;

    @FieldDoc(description = "是否展示对比")
    private Boolean showCompare;


}
