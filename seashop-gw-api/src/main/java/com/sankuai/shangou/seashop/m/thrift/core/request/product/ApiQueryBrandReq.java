package com.sankuai.shangou.seashop.m.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询品牌分页列表入参")
public class ApiQueryBrandReq extends BasePageReq {

    @FieldDoc(description = "品牌id", requiredness = Requiredness.NONE)
    private Long id;

    @FieldDoc(description = "品牌名称", requiredness = Requiredness.NONE)
    private String name;


}
