package com.sankuai.shangou.seashop.m.thrift.core.request.base;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 14:51
 */
@Data
public class ApiDeleteFootMenuReq extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    @PrimaryField
    private Long id;


    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id不能传空");
        }
    }
}
