package com.sankuai.shangou.seashop.m.thrift.core.response.product.dto;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:42
 */
@TypeDoc(
    description = "品牌申请信息"
)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ApiBrandApplyDto extends BaseThriftDto {

    @FieldDoc(description = "申请记录id")
    private Integer id;

    @FieldDoc(description = "供应商id")
    private Long shopId;

    @FieldDoc(description = "供应商名称")
    private String shopName;

    @FieldDoc(description = "品牌id")
    private Long brandId;

    @FieldDoc(description = "品牌名称")
    private String brandName;

    @FieldDoc(description = "品牌logo")
    @JsonUrlFormat(deserializer = false)
    private String logo;

    @FieldDoc(description = "品牌简介")
    private String description;

    @FieldDoc(description = "申请类型 1-平台已有品牌 2-新品牌")
    private Integer applyModeCode;

    @FieldDoc(description = "申请类型描述")
    private String applyModeDesc;

    @FieldDoc(description = "备注")
    private String remark;

    @FieldDoc(description = "审核状态 0-未审核 1-审核通过 2-审核拒绝")
    private Integer auditStatusCode;

    @FieldDoc(description = "审核状态描述")
    private String auditStatusDesc;

    @FieldDoc(description = "审核时间")
    private Date applyTime;

    @FieldDoc(description = "平台备注")
    private String platRemark;

    @FieldDoc(description = "品牌授权书")
    @JsonUrlFormat(deserializer = false)
    private List<String> authCertificateList;

    @FieldDoc(description = "审核时间")
    private Date auditTime;


    public Long getApplyTimeLong() {
        return this.date2Long(this.applyTime);
    }


    public void setApplyTimeLong(Long applyTime) {
        this.applyTime = this.long2Date(applyTime);
    }


    public Long getAuditTimeLong() {
        return this.date2Long(this.auditTime);
    }


    public void setAuditTimeLong(Long auditTime) {
        this.auditTime = this.long2Date(auditTime);
    }
}
