package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@TypeDoc(description = "图片分类")
@Data
public class ApiBasePhotoSpaceCategoryRes extends BaseThriftDto {
    private Long id;

    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @FieldDoc(description = "分类名称", requiredness = Requiredness.REQUIRED)
    private String photoSpaceCatrgoryName;

    @FieldDoc(description = "排序")
    private Long displaysSequence;

    @FieldDoc(description = "图片数量")
    private int imageCount;


}
