package com.sankuai.shangou.seashop.m.thrift.user.shop.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "类目申请详情")
public class ApiBusinessCategoryApplyDetailResp extends BaseThriftDto {

    @FieldDoc(description = "id")
    private Long id;

    @FieldDoc(description = "申请时间")
    private Date applyDate;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "审核状态 0-待审核 1-已审核 2-审核拒绝")
    private Integer auditedStatus;

    @FieldDoc(description = "审核状态描述")
    private String auditedStatusDesc;

    @FieldDoc(description = "审核时间")
    private Date auditedDate;

    @FieldDoc(description = "拒绝原因")
    private String refuseReason;

    @FieldDoc(description = "签署状态 0-待签署 1-已签署")
    private Integer agreementStatus;

    @FieldDoc(description = "签署状态描述")
    private String agreementStatusDesc;

    @FieldDoc(description = "合同ID")
    private Long shopAgreementId;

    @FieldDoc(description = "是否需要补充资料")
    private Boolean needSupply;

    @FieldDoc(description = "申请类目")
    private List<CategoryDto> applyCategoryList;

    @FieldDoc(description = "表单数据")
    private List<CategoryApplyFormDto> formList;

    @FieldDoc(description = "表单变动数据(补充数据时使用)")
    private List<CategoryApplyFormDto> changeFormList;


    public Long getApplyDateLong() {
        return this.date2Long(this.applyDate);
    }


    public void setApplyDateLong(Long applyDate) {
        this.applyDate = this.long2Date(applyDate);
    }


    public Long getAuditedDateLong() {
        return this.date2Long(this.auditedDate);
    }


    public void setAuditedDateLong(Long auditedDate) {
        this.auditedDate = this.long2Date(auditedDate);
    }


}
