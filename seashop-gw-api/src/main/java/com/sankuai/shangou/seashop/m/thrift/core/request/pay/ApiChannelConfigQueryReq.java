package com.sankuai.shangou.seashop.m.thrift.core.request.pay;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询渠道配置请求对象")
public class ApiChannelConfigQueryReq extends BaseParamReq {

    @FieldDoc(description = "支付渠道 1：汇付天下", requiredness = Requiredness.REQUIRED)
    private Integer paymentChannel;

    @FieldDoc(description = "配置Key值")
    private String configKey;

    @FieldDoc(description = "配置Key值列表")
    private List<String> configKeyList;

    @Override
    public void checkParameter() {
        if (null == this.paymentChannel) {
            throw new InvalidParamException("paymentChannel不能为空");
        }
//        if (StrUtil.isBlank(this.configKey) && CollUtil.isEmpty(this.configKeyList)) {
//            throw new InvalidParamException("configKey和configKeyList不能同时为空");
//        }
    }


}
