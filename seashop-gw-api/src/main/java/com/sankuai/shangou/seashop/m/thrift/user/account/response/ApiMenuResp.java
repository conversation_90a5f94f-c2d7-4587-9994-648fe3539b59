package com.sankuai.shangou.seashop.m.thrift.user.account.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.List;

/**
 * 菜单权限对象
 *
 * <AUTHOR>
 * @date 2024/10/09 10:17
 */
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ApiMenuResp {

    private String id;

    private String name;

    private String path;

    private String icon;

    private List<ApiMenuResp> menus;

    private Boolean izTab;

}
