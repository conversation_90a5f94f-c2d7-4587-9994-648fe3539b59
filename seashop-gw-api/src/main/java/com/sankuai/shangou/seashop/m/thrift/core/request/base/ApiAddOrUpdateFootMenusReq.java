package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:46
 */
@Data
public class ApiAddOrUpdateFootMenusReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "导航名称")
    private String name;

    @FieldDoc(description = "链接地址")
    private String url;

    @FieldDoc(description = "显示图片")
    @JsonUrlFormat(deserializer = false)
    private String menuIcon;

    @FieldDoc(description = "未选中显示图片")
    @JsonUrlFormat(deserializer = false)
    private String menuIconSel;

    @FieldDoc(description = "菜单类型（1代表微信、2代表小程序）")
    private Integer type;

    @FieldDoc(description = "店铺Id(0默认是平台)")
    private Long shopId;

    @FieldDoc(description = "创建时间")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    private Date updateTime;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }
}
