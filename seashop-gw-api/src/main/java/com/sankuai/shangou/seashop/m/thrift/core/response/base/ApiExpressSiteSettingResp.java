package com.sankuai.shangou.seashop.m.thrift.core.response.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

@TypeDoc(description = "物流设置返参")
@Data
@ToString
public class ApiExpressSiteSettingResp extends BaseParamReq {

    @FieldDoc(description = "美团秘钥键")
    private String meiTuanAppKey;

    @FieldDoc(description = "美团密码")
    private String meiTuanAppSecret;


}
