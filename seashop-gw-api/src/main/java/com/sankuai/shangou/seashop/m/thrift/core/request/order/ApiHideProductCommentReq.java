package com.sankuai.shangou.seashop.m.thrift.core.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:37
 */
@Data
@ToString
@TypeDoc(description = "隐藏商品评论入参")
public class ApiHideProductCommentReq extends BaseParamReq {

    @FieldDoc(description = "商品评论id", requiredness = Requiredness.REQUIRED)
    private Long productCommentId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productCommentId == null || productCommentId <= 0, "商品评论id不能为空");
    }


}
