package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@TypeDoc(description = "文章对象")
@Data
public class ApiBaseArticleReq extends BaseParamReq {

    @FieldDoc(description = "文章id")
    private Long id;

    /**
     * 文章分类id
     */
    @FieldDoc(description = "文章分类id", requiredness = Requiredness.REQUIRED)
    private Long categoryId;

    /**
     * 标题
     */
    @FieldDoc(description = "标题", requiredness = Requiredness.REQUIRED)
    private String title;

    /**
     * icon url
     */
    @FieldDoc(description = "icon url")
    @JsonUrlFormat(deserializer = false)
    private String iconUrl;

    /**
     * 添加时间
     */
    @FieldDoc(description = "添加时间")
    private Date addDate;

    /**
     * 排序字段
     */
    @FieldDoc(description = "排序字段")
    private Long displaySequence;

    /**
     * 是否显示
     */
    @FieldDoc(description = "是否显示")
    private Boolean isRelease;

    /**
     * 文章内容
     */
    @FieldDoc(description = "文章内容", requiredness = Requiredness.REQUIRED)
    private String content;

    /**
     * 文章seo标题
     */
    @FieldDoc(description = "文章seo标题")
    private String seoTitle;

    /**
     * 文档seo详情
     */
    @FieldDoc(description = "文档seo详情")
    private String seoDescription;

    /**
     * 文章seo关键字
     */
    @FieldDoc(description = "文章seo关键字")
    private String seoKeywords;

    public void checkParameter() {
        if (this.categoryId == null) {
            throw new IllegalArgumentException("所属分类不能为空");
        }
        if (StringUtils.isEmpty(this.title)) {
            throw new IllegalArgumentException("文章标题不能为空");
        }
//        if (StringUtils.isEmpty(this.content)) {
//            throw new IllegalArgumentException("文章内容不能为空");
//        }
    }


    public Long getAddDateLong() {
        return this.date2Long(this.addDate);
    }


    public void setAddDateLong(Long addDate) {
        this.addDate = this.long2Date(addDate);
    }


}
