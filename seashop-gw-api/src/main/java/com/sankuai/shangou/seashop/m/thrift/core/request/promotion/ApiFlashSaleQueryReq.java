package com.sankuai.shangou.seashop.m.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "限时购查询请求对象")
public class ApiFlashSaleQueryReq extends BasePageReq {

    @FieldDoc(description = "活动ID")
    private Long id;

    @FieldDoc(description = "商品ID")
    private Long productId;

    @FieldDoc(description = "活动名称")
    private String title;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "状态：1待审核 2进行中 3未通过 4已结束 5已取消 6未开始")
    private Integer status;


}
