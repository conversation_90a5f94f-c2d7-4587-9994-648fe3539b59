package com.sankuai.shangou.seashop.m.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiDescriptionTemplateDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:35
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "查询版式列表返回值")
public class ApiDescriptionTemplateListResp extends BaseThriftDto {

    @FieldDoc(description = "版式列表")
    private List<ApiDescriptionTemplateDto> templateList;


}
