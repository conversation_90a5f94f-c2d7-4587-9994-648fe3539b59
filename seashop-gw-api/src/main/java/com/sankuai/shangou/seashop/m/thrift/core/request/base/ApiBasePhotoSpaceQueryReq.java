package com.sankuai.shangou.seashop.m.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;


@TypeDoc(description = "图片分类查询对象")
@Data
public class ApiBasePhotoSpaceQueryReq extends BasePageReq {

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "分类id")
    private Long photoCategoryId;

    @FieldDoc(description = "图片名称")
    private String photoName;


}
