package com.sankuai.shangou.seashop.m.thrift.user.shop.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.dto.ApiQueryFreightTemplateDto;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@TypeDoc(description = "运费模版管理查询接口返回对象")
@ToString
@Data
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ApiQueryFreightTemplateResp {

    @FieldDoc(description = "运费模版管理查询接口返回对象列表")

    private List<ApiQueryFreightTemplateDto> result = new ArrayList<>();
}
