package com.sankuai.gw.common.thrift.requests;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 平台管理员登录入参
 * @author: LXH
 **/
@TypeDoc(description = "会员注册请求对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiRegisterReq extends BaseParamReq {
    @FieldDoc(description = "登录类型 MOBILE WX_CODE")
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    private String RegisterType;

    //    手机号注册参数
    @FieldDoc(description = "密码")
    private String password;
    @FieldDoc(description = "确认密码")
    private String confirmPassword;
    @FieldDoc(description = "key")
    private String imageKey;
    @FieldDoc(description = "图片验证码 base64")
    private String imageCode;
    @FieldDoc(description = "手机")
    private String phone;
    @FieldDoc(description = "手机验证码")
    private String phoneCode;
    //    微信小程序注册
    @FieldDoc(description = "微信注册code")
    private String weCode;
    @FieldDoc(description = "微信openId")
    private String openId;
    @FieldDoc(description = "微信unionId")
    private String unionId;


}
