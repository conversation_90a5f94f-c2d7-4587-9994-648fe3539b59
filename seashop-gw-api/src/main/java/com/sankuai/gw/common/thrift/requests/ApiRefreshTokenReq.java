package com.sankuai.gw.common.thrift.requests;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * @author: cdd
 * @date: 2024/5/17/017
 * @description: 发送登录短信对象
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ApiRefreshTokenReq {
    @FieldDoc(description = "原token")
    private TokenCache token;
    @FieldDoc(description = "角色类型 SHOP")
    private String roleType;

    public void checkParameter() {
        AssertUtil.throwIfNull(token, "原token");
        AssertUtil.throwIfNull(RoleEnum.nameOf(roleType), "角色类型不能为空");
    }
}
