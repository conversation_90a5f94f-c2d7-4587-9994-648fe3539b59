package com.sankuai.gw.common.thrift.requests;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 平台管理员登录入参
 * @author: LXH
 **/
@TypeDoc(description = "会员操作请求对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiLoginReq extends BaseParamReq {
    @FieldDoc(description = "登录类型 PASSWORD MOBILE WX_CODE")
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    private String loginType;

    @FieldDoc(description = "用户名")
    private String userName;
    @FieldDoc(description = "密码")
    private String password;
    @FieldDoc(description = "滑动验证码")
    private ApiAfsReq slideCode;


    @FieldDoc(description = "手机号")
    private String phone;
    @FieldDoc(description = "手机验证码")
    private String phoneCode;

    @FieldDoc(description = "微信登录code")
    private String weCode;
    @FieldDoc(description = "微信openId")
    private String openId;
    @FieldDoc(description = "微信unionId")
    private String unionId;

    @FieldDoc(description = "解密手机号的wx-code")
    private String mobileCode;

    @FieldDoc(description = "微信公众号code")
    private String code;


}
