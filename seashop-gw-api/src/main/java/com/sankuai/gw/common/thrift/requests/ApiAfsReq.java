package com.sankuai.gw.common.thrift.requests;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@TypeDoc(description = "滑块验证请求对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiAfsReq {
    /**
     * 签名串。必填参数，从前端获取，不可更改。
     */
    public String sig;

    /**
     * 客户端IP。必填参数，后端填写。
     */
    public String remoteIp;

    /**
     * 会话标识。必填参数，从前端获取，不可更改。
     */
    public String sessionId;

    /**
     * 请求唯一标识。必填参数，从前端获取，不可更改。
     */
    public String token;
    /**
     * 场景标识。必填参数，从前端获取，不可更改。
     */
    public String scene;
}
