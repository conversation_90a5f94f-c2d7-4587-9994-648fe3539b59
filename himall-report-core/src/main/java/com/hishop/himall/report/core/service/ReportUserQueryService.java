package com.hishop.himall.report.core.service;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.UserReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.UserNewOldSummaryResp;
import com.hishop.himall.report.api.response.UserResp;
import com.hishop.himall.report.api.response.UserSummaryResp;
import com.hishop.himall.report.dao.models.UserSummary;
import com.hishop.starter.util.model.PageResult;

public interface ReportUserQueryService {
    /**
     * 用户概况
     *
     * @param reportReq
     * @return
     */
    UserSummaryResp getUserSummary(ReportReq reportReq);

    /**
     * 用户新增趋势
     *
     * @param reportReq
     * @return
     */
    Echarts getNewUserEchats(ReportReq reportReq);

    /**
     * 用户省份分布
     *
     * @param reportReq
     * @return
     */
    Echarts getProvinceUserEchats(ReportReq reportReq);

    /**
     * 新老会员概览
     * @param userReq
     * @return
     */
    UserNewOldSummaryResp getNewOldSummary(ReportReq userReq);

    /**
     * 获取新老会员趋势
     *
     * @param userReq
     * @return
     */
    Echarts getNewOldUserEcharts(ReportReq userReq);

    /**
     * 获取用户列表
     *
     * @param userReq
     * @return
     */
    PageResult<UserResp> getUsers(UserReq userReq);

    /**
     * 导出用户报表
     *
     * @param userReq
     */
    void exportUsers(UserReq userReq);
}
