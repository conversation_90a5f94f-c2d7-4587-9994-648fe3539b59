package com.hishop.himall.report.core.service;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.response.Echarts;

import java.time.LocalDate;
import java.util.Date;

public interface ReportWechatService {

    /**
     * 获取用户画像
     * @param range
     * @param start
     * @param end
     */
    void getherPortrait(String range, Date start, Date end);

    /**
     * 获取浏览统计
     * @param range
     * @param start
     * @param end
     */
    void gatherVisits(String range, Date start, Date end);
    /**
     * 访问趋势图
     *
     * @param req
     * @return
     */
    Echarts getVisitsEcharts(ReportReq req);

    /**
     * 用户画像饼图
     *
     * @param req
     * @return
     */
    Echarts getPortraitEcharts(ReportReq req);
}
