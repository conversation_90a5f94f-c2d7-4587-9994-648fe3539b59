package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.core.service.ReportWechatService;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("report/wechat")
public class WechatController  {

    @Resource
    private ReportWechatService reportWechatService;
    /**
     * 访问走势图
     *
     */
    @PostMapping(value = "visits", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> visits(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("visits", reportReq, req -> {
            return reportWechatService.getVisitsEcharts(req);
        });
    }

    /**
     * 用户画像饼图
     */
    @PostMapping(value = "portrait", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> portrait(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("portrait", reportReq, req -> {
            return reportWechatService.getPortraitEcharts(req);
        });
    }
}
