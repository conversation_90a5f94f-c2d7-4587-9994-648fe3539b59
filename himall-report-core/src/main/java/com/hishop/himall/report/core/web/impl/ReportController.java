package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.api.request.BatchReportSourceCartReq;
import com.hishop.himall.report.api.request.BatchReportSourceCouponReq;
import com.hishop.himall.report.api.request.BatchReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.BatchReportSourceFollowShopReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderBillReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderItemReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderReq;
import com.hishop.himall.report.api.request.BatchReportSourceProductReq;
import com.hishop.himall.report.api.request.BatchReportSourceRefundReq;
import com.hishop.himall.report.api.request.BatchReportSourceRegionReq;
import com.hishop.himall.report.api.request.BatchReportSourceShopReq;
import com.hishop.himall.report.api.request.BatchReportSourceUserReq;
import com.hishop.himall.report.api.request.BatchReportSourceVisitReq;
import com.hishop.himall.report.api.request.CartReq;
import com.hishop.himall.report.api.request.ReportSourceCartReq;
import com.hishop.himall.report.api.request.ReportSourceCouponReq;
import com.hishop.himall.report.api.request.ReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.ReportSourceFollowShopReq;
import com.hishop.himall.report.api.request.ReportSourceOrderBillReq;
import com.hishop.himall.report.api.request.ReportSourceOrderItemReq;
import com.hishop.himall.report.api.request.ReportSourceOrderReq;
import com.hishop.himall.report.api.request.ReportSourceProductReq;
import com.hishop.himall.report.api.request.ReportSourceRefundReq;
import com.hishop.himall.report.api.request.ReportSourceRegionReq;
import com.hishop.himall.report.api.request.ReportSourceShopReq;
import com.hishop.himall.report.api.request.ReportSourceUserReq;
import com.hishop.himall.report.api.request.ReportSourceVisitReq;
import com.hishop.himall.report.api.request.VisitsReq;
import com.hishop.himall.report.api.service.ReportFeign;
import com.hishop.himall.report.core.service.ReportService;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("report")
public class ReportController implements ReportFeign {

    @Resource
    private ReportService reportService;

    /**
     * 添加购物车来源
     *
     * @param req
     * @return
     */
    @PostMapping(value = "createSourceCart", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceCart(@RequestBody ReportSourceCartReq req) {
        try {
            CartReq cartReq = JsonUtil.copy(req, CartReq.class);
            reportService.createCart(cartReq);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            CartReq cartReq = JsonUtil.copy(req, CartReq.class);
            reportService.updateCart(cartReq);
            return ResultDto.newWithData(BaseResp.of());
        }
    }


    @Override
    public ResultDto<BaseResp> batchCreateSourceCart(BatchReportSourceCartReq req) {
        reportService.batchCreateCart(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    /**
     * 添加访问来源
     *
     * @param req
     * @return
     */
    @PostMapping(value = "createSourceVisits", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceVisits(@RequestBody ReportSourceVisitReq req) {
        try {
            VisitsReq cartReq = JsonUtil.copy(req, VisitsReq.class);
            reportService.createVisit(cartReq);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            VisitsReq cartReq = JsonUtil.copy(req, VisitsReq.class);
            reportService.updateVisit(cartReq);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceVisits(BatchReportSourceVisitReq req) {
        reportService.batchCreateSourceVisit(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    /**
     * 优惠券来源
     *
     * @param req
     * @return
     */
    @PostMapping(value = "createSourceCoupon", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceCoupon(@RequestBody ReportSourceCouponReq req) {

        try {
            reportService.createCoupon(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateCoupon(req);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceCoupon(BatchReportSourceCouponReq req) {
        reportService.batchCreateSourceCoupon(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "createSourceFollowProduct", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceFollowProduct(@RequestBody ReportSourceFollowProductReq req) {
        try {
            reportService.createFollowProductReq(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateFollowProductReq(req);
            return ResultDto.newWithData(BaseResp.of());
        }
    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceFollowProduct(BatchReportSourceFollowProductReq req) {
        reportService.batchCreateSourceFollowProduct(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "createSourceFollowShop", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceFollowShop(@RequestBody ReportSourceFollowShopReq req) {
        try {
            reportService.createSourceFollowShop(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateSourceFollowShop(req);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceFollowShop(BatchReportSourceFollowShopReq req) {
        reportService.batchCreateSourceFollowShop(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "createSourceOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceOrder(@RequestBody ReportSourceOrderReq req) {
        try {
            reportService.createSourceOrder(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateSourceOrder(req);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceOrder(BatchReportSourceOrderReq req) {
        reportService.batchCreateSourceOrder(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "createSourceOrderItem", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceOrderItem(@RequestBody ReportSourceOrderItemReq req) {
        try {
            reportService.createSourceOrderItem(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateSourceOrderItem(req);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceOrderItem(BatchReportSourceOrderItemReq req) {
        reportService.batchCreateSourceOrderItem(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "createSourceProduct", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceProduct(@RequestBody ReportSourceProductReq req) {
        try {
            reportService.createSourceProduct(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateSourceProduct(req);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceProduct(BatchReportSourceProductReq req) {
        reportService.batchCreateSourceProduct(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "createSourceRefund", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceRefund(@RequestBody ReportSourceRefundReq req) {
        try {
            reportService.createSourceRefund(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateSourceRefund(req);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceRefund(BatchReportSourceRefundReq req) {
        reportService.batchCreateSourceRefund(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "createSourceShop", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceShop(@RequestBody ReportSourceShopReq req) {
        try {
            reportService.createSourceShop(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateSourceShop(req);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceShop(BatchReportSourceShopReq req) {
        reportService.batchCreateSourceShop(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "createSourceUser", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> createSourceUser(@RequestBody ReportSourceUserReq req) {
        try {
            reportService.createSourceUser(req);
            return ResultDto.newWithData(BaseResp.of());
        } catch (DuplicateKeyException exception) {
            reportService.updateSourceUser(req);
            return ResultDto.newWithData(BaseResp.of());
        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceUser(BatchReportSourceUserReq req) {
        reportService.batchCreateSourceUser(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @Override
    public ResultDto<BaseResp> createSourceRegion(ReportSourceRegionReq req) {
//        try {
            reportService.createSourceRegion(req);
            return ResultDto.newWithData(BaseResp.of());
//        } catch (DuplicateKeyException exception) {
//            reportService.updateSourceRegion(req);
//            return ResultDto.newWithData(BaseResp.of());
//        }

    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceRegion(BatchReportSourceRegionReq req) {
        reportService.batchCreateSourceRegion(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @Override
    public ResultDto<BaseResp> createSourceOrderBill(ReportSourceOrderBillReq req) {
        reportService.createSourceOrderBill(req);
        return ResultDto.newWithData(BaseResp.of());
    }

    @Override
    public ResultDto<BaseResp> batchCreateSourceOrderBill(BatchReportSourceOrderBillReq req) {
        reportService.batchCreateSourceOrderBill(req);
        return ResultDto.newWithData(BaseResp.of());
    }
}
