package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.UserReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.UserNewOldSummaryResp;
import com.hishop.himall.report.api.response.UserResp;
import com.hishop.himall.report.api.response.UserSummaryResp;
import com.hishop.himall.report.api.service.ReportUserFeign;
import com.hishop.himall.report.core.service.ReportUserQueryService;
import com.hishop.starter.util.model.PageResult;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("report/user")
public class UserController implements ReportUserFeign {

    @Resource
    private ReportUserQueryService reportUserQueryService;

    /**
     * 交易概览
     *
     * @param reportReq
     * @return
     */
    @PostMapping(value = "/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<UserSummaryResp> queryProductSummary(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("summary", reportReq, req -> {
            return reportUserQueryService.getUserSummary(req);
        });
    }

    /**
     * 用户新增趋势
     *
     * @param reportReq
     * @return
     */
    @PostMapping(value = "/increase/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryUserIncrease(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("increase/echarts", reportReq, req -> {
            return reportUserQueryService.getNewUserEchats(req);
        });
    }

    /**
     * 用户省份分布
     *
     * @param reportReq
     * @return
     */
    @PostMapping(value = "/province/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryProvince(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("province/echarts", reportReq, req -> {
            return reportUserQueryService.getProvinceUserEchats(req);
        });
    }

    /**
     * 新老会员概览
     * @param reportReq
     * @return
     */
    @PostMapping(value = "/newOld/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<UserNewOldSummaryResp> queryNewOldSummary(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("newOld/summary", reportReq, req -> {
            return reportUserQueryService.getNewOldSummary(req);
        });
    }

    /**
     * 新老会员走势图
     * @param reportReq
     * @return
     */
    @PostMapping(value = "/newOld/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryNewOldEcharts(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("newOld/echarts", reportReq, req -> {
            return reportUserQueryService.getNewOldUserEcharts(req);
        });
    }

    /**
     * 会员分析列表
     * @param userReq
     * @return
     */
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<PageResult<UserResp>> queryProducts(@RequestBody UserReq userReq) {
        return ThriftResponseHelper.responseInvoke("query", userReq, req -> {
            return reportUserQueryService.getUsers(req);
        });
    }

    /**
     * 会员分析导出
     * @param userReq
     * @return
     */
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> exportProduct(@RequestBody UserReq userReq) {
        return ThriftResponseHelper.responseInvoke("export", userReq, req -> {
            reportUserQueryService.exportUsers(req);
            return new BaseResp();
        });
    }

}
