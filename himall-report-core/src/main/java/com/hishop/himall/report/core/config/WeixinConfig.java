package com.hishop.himall.report.core.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedissonConfigImpl;
import com.hishop.himall.report.api.exception.ReportException;
import com.hishop.himall.report.common.remote.SiteSettingQueryClient;
import com.hishop.himall.report.common.remote.bo.AppSiteSettingBo;
import com.hishop.starter.util.model.ResultCode;
import com.hishop.starter.util.model.ResultInfo;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname WxaConfiguration
 * Description //TODO
 * @date 2023/3/21 16:27
 */
@Configuration
public class WeixinConfig {
    @Resource
    private SiteSettingQueryClient siteSettingQueryClient;
    @Resource
    private RedissonClient redissonClient;

    @Bean("wxMaService")
    public WxMaService wxMaService() {
        // 暂时无法获取到配置
//        ResultInfo<AppSiteSettingBo> appSettings = siteSettingQueryClient.getAppSettings();
//        if (!ResultCode.SUCCESS.getCode().equals(appSettings.getCode())) {
//            throw new ReportException(ResultCode.SERVER_ERROR.getCode(), "获取小程序配置失败");
//        }
//        AppSiteSettingBo data = appSettings.getData();
//        String appid = data.getAppKey();
//        String secret = data.getAppSecret();
        String appId = "wxfdbd7d7363546c98";
        String secret = "2d98deeb115aac5886e2a33d494bf355";
        WxMaService service = new WxMaServiceImpl();
        WxMaRedissonConfigImpl configStorage = new WxMaRedissonConfigImpl(redissonClient, "WXA");
        configStorage.setAppid(appId);
        configStorage.setSecret(secret);
        configStorage.useStableAccessToken(true);
        service.setWxMaConfig(configStorage);
        return service;
    }

}
