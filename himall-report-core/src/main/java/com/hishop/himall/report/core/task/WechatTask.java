package com.hishop.himall.report.core.task;

import com.hishop.himall.report.core.service.ReportWechatService;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

@Slf4j
@Component
public class WechatTask {
    @Resource
    private ReportWechatService reportWechatService;


    @XxlJob("WechatTaskSettlement")
    @XxlRegister(cron = "0 0 10-14 * * ?",
            author = "snow",
            jobDesc = "微信结算")
    public void execute() {
        log.info("WechatTaskSettlement 开始");
        try {
            this.settlement();
            log.info("WechatTaskSettlement 执行成功");
        } catch (Exception e) {
            log.error("WechatTaskSettlement 异常", e);
        }
    }


    public void settlement() {

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        Date yesterday = getDateWithTimeSetToZero(calendar);
        // 获取7天前的日期，时间设为0
        calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -7);
        Date last7Days = getDateWithTimeSetToZero(calendar);

        // 获取30天前的日期，时间设为0
        calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -30);
        Date last30Days = getDateWithTimeSetToZero(calendar);

        reportWechatService.gatherVisits("DAY", yesterday, yesterday);
        reportWechatService.getherPortrait("DAY", yesterday, yesterday);
        reportWechatService.getherPortrait("LAST7", last7Days, yesterday);
        reportWechatService.getherPortrait("LAST30", last30Days, yesterday);
    }

    private static Date getDateWithTimeSetToZero(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
}
