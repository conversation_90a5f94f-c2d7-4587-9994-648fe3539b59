package com.hishop.himall.report.core.convert;

import com.hishop.himall.report.api.demo.response.TestUserQueryResp;
import com.hishop.himall.report.dao.demo.domain.ReportAccessRecords;
import com.hishop.starter.web.convert.BeanConvert;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface TestUserConvert extends BeanConvert<ReportAccessRecords, TestUserQueryResp> {

//    @Mappings(
//            @Mapping(source = "", target = "")
//    )
//    ReportAccessRecords toEntity(TestUserQueryReq req);

}
