package com.hishop.himall.report.core.exports;

import com.hishop.himall.report.api.request.UserReq;
import com.hishop.himall.report.api.response.UserResp;
import com.hishop.himall.report.core.exports.models.UserExportVo;
import com.hishop.himall.report.core.service.ReportUserQueryService;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

@Service
public class UserExportWrapper extends PageExportWrapper<UserExportVo, UserReq>
{
    @Resource
    ReportUserQueryService reportUserQueryService;

    @Override
    public List<UserExportVo> getPageList(UserReq param) {
        List<UserResp> source = reportUserQueryService.getUsers(param).getData();
        List<UserExportVo> result = new ArrayList<>();
        for (UserResp item : source) {
            result.add(Map(item));
        }
        return result;
    }
    private UserExportVo Map(Object obj) {
        try {
            UserExportVo vo = new UserExportVo();
            BeanUtils.copyProperties(obj, vo);
            return vo;
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    public String sheetName() {
        return "会员分析";
    }
}
