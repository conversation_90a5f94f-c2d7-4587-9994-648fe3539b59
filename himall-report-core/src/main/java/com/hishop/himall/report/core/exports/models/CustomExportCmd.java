package com.hishop.himall.report.core.exports.models;

import com.hishop.himall.report.api.enums.Dimension;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class CustomExportCmd extends BasePageReq
{
    private String range;
    private Dimension dimension;
    private LocalDate start;
    private LocalDate end;

    private List<String> selectedFieldList;
}
