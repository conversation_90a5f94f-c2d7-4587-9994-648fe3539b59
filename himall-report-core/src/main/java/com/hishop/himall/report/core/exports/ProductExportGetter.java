package com.hishop.himall.report.core.exports;

import com.hishop.himall.report.api.request.ProductReq;
import com.hishop.himall.report.api.request.ShopReq;
import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
public class ProductExportGetter extends AbstractBaseDataGetter<ProductReq>
{
    @Override
    public DataContext selectData(ProductReq param) {
        DataContext context = new DataContext();
        context.setSheetDataList(Collections.singletonList(new ProductExportWrapper()));
        return context;
    }

    @Override
    public Integer getModule() {

        return 0;
    }

    @Override
    public String getFileName() {

        return "";
    }
}
