package com.hishop.himall.report.core.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.himall.report.api.request.ProductReq;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.response.EchartSeries;
import com.hishop.himall.report.api.response.EchartSeriesPieData;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.ProductResp;
import com.hishop.himall.report.api.response.ProductSummaryResp;
import com.hishop.himall.report.core.service.ReportProductQueryService;
import com.hishop.himall.report.dao.mapper.ReportProductTradeMapper;
import com.hishop.himall.report.dao.models.CategorySummary;
import com.hishop.himall.report.dao.models.ProductSummary;
import com.hishop.himall.report.dao.models.ProductTrade;
import com.hishop.himall.report.dao.models.VisitsSummary;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.web.util.PageUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
public class ReportProductQueryServiceImpl implements ReportProductQueryService {

    @Resource
    private ReportProductTradeMapper reportProductTradeMapper;

    /**
     * 获取商品概况
     *
     * @param req 入参
     * @return
     */
    public ProductSummaryResp getProductSummary(ReportReq req) {
        ProductSummary summary = reportProductTradeMapper.getSummary(req.getShopId(), req.getRange().toString(), req.getStart(),
                                                                     req.getEnd());
        req.getPrev();
        ProductSummary prevSummary = reportProductTradeMapper.getSummary(req.getShopId(), req.getRange().toString(),
                                                                         req.getStart(), req.getEnd());
        ProductSummaryResp result = Map(summary);
        result.setPrev(Map(prevSummary));
        return result;
    }

    private ProductSummaryResp  Map(ProductSummary source) {
        ProductSummaryResp result = new ProductSummaryResp();
        result.setVisitsProducts(source.getVisitsProducts());
        result.setSalesProducts(source.getSalesProducts());
        result.setVisitsCount(source.getVisitsCount());
        result.setVisitsUsers(source.getVisitsUsers());
        result.setCartQuantity(source.getCartQuantity());
        result.setOrderQuantity(source.getOrderQuantity());
        result.setPaymentQuantity(source.getPaymentQuantity());
        return result;
    }

    public Echarts getProductVisitsEchats(ReportReq req) {
        Echarts echarts = new Echarts();
        EchartSeries visitsProductSeries = echarts.addSeries("被访问商品数");
        EchartSeries salesProductSeries = echarts.addSeries("动销商品数");
        List<VisitsSummary> summaries = reportProductTradeMapper.getVisitsSummary(req.getShopId(),req.getRange().toString(),
                                                                                      req.getStartDate(), req.getEndDate());
        return echarts.each(req.getRange().toString(), req.getStart(), req.getEnd(), (date) -> {
            VisitsSummary item = summaries.stream().filter((summary) -> summary.getDate().equals(date)).findFirst().orElse(new VisitsSummary());
            visitsProductSeries.add(item.getVisitsProducts());
            salesProductSeries.add(item.getSalesProducts());
        });
    }

    /**
     * 商品类目分布
     *
     * @param reportReq
     * @return
     */
    public Echarts getProductCategoryEchats(ReportReq reportReq) {
        Echarts echarts = new Echarts();
        EchartSeries categoryCountSeries = echarts.addSeries("类目销量");
        EchartSeries categoryAmountSeries = echarts.addSeries("类目销售额");

        List<CategorySummary> categorySummaries = reportProductTradeMapper.getCategorySummary(reportReq.getShopId(),
                reportReq.getRange().toString(), reportReq.getStartDate(), reportReq.getEndDate());

        for (CategorySummary categorySummary : categorySummaries) {
            EchartSeriesPieData data = new EchartSeriesPieData();
            data.setName(categorySummary.getCategoryName());
            data.setValue(BigDecimal.valueOf(categorySummary.getQuantity()));
            categoryCountSeries.getData().add(data);

            EchartSeriesPieData amountData = new EchartSeriesPieData();
            amountData.setName(categorySummary.getCategoryName());
            amountData.setValue(categorySummary.getAmount());
            categoryAmountSeries.getData().add(amountData);
        }
        return echarts;
    }

    /**
     * 商品销售趋势
     *
     * @param productReq
     * @return
     */
    public PageResult<ProductResp> getProducts(ProductReq productReq) {
        Page<ProductTrade> products = reportProductTradeMapper.getProducts(productReq.getShopId(), Page.of(productReq.getPageNo(), productReq.getPageSize(), true),
                                                                           productReq.getRange().toString(),
                                                                           productReq.getStart(), productReq.getEnd(),
                                                                           productReq.getCategoryName(),
                                                                           productReq.getProductName());
        return PageUtil.buildPage(products,ProductResp.class);
    }

    /**
     * 导出商品报表
     *
     * @param productReq
     */
    public void exportProducts(ProductReq productReq) {
        List<ProductTrade> products = reportProductTradeMapper.getProducts(productReq.getShopId(),productReq.getRange().toString(),
                                                                           productReq.getStart(), productReq.getEnd(),
                                                                           productReq.getCategoryName(),
                                                                           productReq.getProductName());

    }
}
