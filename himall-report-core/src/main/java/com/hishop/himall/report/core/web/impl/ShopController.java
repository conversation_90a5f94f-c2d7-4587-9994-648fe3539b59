package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.ShopReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.ShopReportResp;
import com.hishop.himall.report.core.service.ReportShopQueryService;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.util.model.ResultInfo;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("report/shop")
public class ShopController   {

    @Resource
    private ReportShopQueryService reportShopQueryService;

    /**
     * 店铺新增趋势
     * @param req
     * @return
     */
    @PostMapping(value = "increase/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<Echarts> queryShopIncrease(@RequestBody ReportReq req) {
        return ResultInfo.success(reportShopQueryService.getNewShopEchats(req));
    }

    /**
     * 店铺省份分布
     * @param req
     * @return
     */
    @PostMapping(value = "province/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<Echarts> queryProvinceWithShop(@RequestBody ReportReq req) {
        return ResultInfo.success(reportShopQueryService.getProvinceWithShop(req));
    }

    /**
     * 查询门店分析报表
     * @param req
     * @return
     */
    @PostMapping(value = "query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<PageResult<ShopReportResp>> queryShops(@RequestBody ShopReq req) {
        return ResultInfo.success(reportShopQueryService.getShopTrade(req));
    }

    /**
     * 导出门店分析报表
     * @param req
     * @return
     */
    @PostMapping(value = "export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<Object> exportShops(@RequestBody ShopReq req) {
        reportShopQueryService.exportShopTrade(req);
        return ResultInfo.success();
    }
}
