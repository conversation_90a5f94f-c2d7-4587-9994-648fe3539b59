package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.api.request.ProductReq;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.ProductResp;
import com.hishop.himall.report.api.response.ProductSummaryResp;
import com.hishop.himall.report.api.service.ReportProductFeign;
import com.hishop.himall.report.core.service.ReportProductQueryService;


import com.hishop.starter.util.model.PageResult;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("report/product")
public class ProductController implements ReportProductFeign {

    @Resource
    private ReportProductQueryService reportProductQueryService;
    /**
     * 商品概览
     */
    @PostMapping(value = "/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<ProductSummaryResp> queryProductSummary(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("visits", reportReq, req -> {
            return reportProductQueryService.getProductSummary(req);
        });
    }

    /**
     * 商品走势图
     */
    @PostMapping(value = "/visitsEcharts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryVisits(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("visitsEcharts", reportReq, req -> {
            return reportProductQueryService.getProductVisitsEchats(req);
        });
    }

    /**
     * 分类饼图
     */
    @PostMapping(value = "/categoryEcharts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryCategory(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("categoryEcharts", reportReq, req -> {
            return reportProductQueryService.getProductCategoryEchats(req);
        });
    }

    /**
     * 商品分析列表
     */
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<PageResult<ProductResp>> queryProducts(@RequestBody ProductReq reportReq) {
        return ThriftResponseHelper.responseInvoke("query", reportReq, req -> {
            return reportProductQueryService.getProducts(req);
        });
    }

    /**
     * 商品分析导出
     */
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> exportProduct(@RequestBody ProductReq productReq) {
        return ThriftResponseHelper.responseInvoke("export", productReq, req -> {
            reportProductQueryService.exportProducts(req);
            return new BaseResp();
        });
    }
}
