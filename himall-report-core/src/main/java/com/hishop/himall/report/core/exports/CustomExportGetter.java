package com.hishop.himall.report.core.exports;

import com.hishop.himall.report.core.exports.models.CustomExportCmd;
import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
public class CustomExportGetter  extends AbstractBaseDataGetter<CustomExportCmd>
{
    @Override
    public DataContext selectData(CustomExportCmd param) {
        DataContext context = new DataContext();
        context.setSheetDataList(Collections.singletonList(new CustomExportWrapper()));
        return context;
    }

    @Override
    public Integer getModule() {
        return 0;
    }

    @Override
    public String getFileName() {
        return "";
    }
}
