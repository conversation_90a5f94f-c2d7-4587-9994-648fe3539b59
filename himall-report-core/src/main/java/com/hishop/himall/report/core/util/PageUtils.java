package com.hishop.himall.report.core.util;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname PageUtils
 * Description //TODO
 * @date 2024/12/5 11:10
 */
public class PageUtils {

    private static int PAGE_SIZE = 1000;

    /**
     * 拆分list
     *
     * @param list
     * @param <T>
     * @return <key:pageNo,value:List<T>>
     */
    public static <T> Map<Integer, List<T>> split(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(500, "Can't split List of empty");
        }
        Map<Integer, List<T>> map = new HashMap<>();
        int pages = list.size() / PAGE_SIZE;
        int balance = list.size() % PAGE_SIZE;
        for (int page = 1; page <= pages; page++) {
            map.put(page, list.subList((page - 1) * PAGE_SIZE, page * PAGE_SIZE));
        }
        if (balance > 0) {
            map.put(pages + 1, list.subList(pages * PAGE_SIZE, pages * PAGE_SIZE + balance));
        }
        return map;
    }

    /**
     * 拆分list
     *
     * @param list
     * @param convertFunc 转化函数
     * @return <key:pageNo,value:List<T>>
     */
    public static <T, R> Map<Integer, List<R>> split(List<T> list, Function<T, R> convertFunc) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(500, "Can't split List of empty");
        }
        Map<Integer, List<R>> map = new HashMap<>();
        int pages = list.size() / PAGE_SIZE;
        int balance = list.size() % PAGE_SIZE;
        for (int page = 1; page <= pages; page++) {
            List<R> result = new ArrayList<>(PAGE_SIZE);
            for (T t : list.subList((page - 1) * PAGE_SIZE, page * PAGE_SIZE)) {
                R r = convertFunc.apply(t);
                result.add(r);
            }
            map.put(page, result);
        }
        if (balance > 0) {
            List<R> result = new ArrayList<>(balance);
            for (T t : list.subList(pages * PAGE_SIZE, pages * PAGE_SIZE + balance)) {
                result.add(convertFunc.apply(t));
            }
            map.put(pages + 1, result);
        }
        return map;
    }

    public static void main(String[] args) {
        List<Integer> list= Arrays.asList(1,2,3,4,5,6,7,8,9,10);
        Map<Integer, List<Integer>> split = PageUtils.split(list, Integer::intValue);
        System.out.println(split);

    }

}
