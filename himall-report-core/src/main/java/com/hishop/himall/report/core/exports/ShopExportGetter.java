package com.hishop.himall.report.core.exports;

import com.hishop.himall.report.api.request.ShopReq;
import com.hishop.himall.report.api.request.UserReq;
import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
public class ShopExportGetter extends AbstractBaseDataGetter<ShopReq>
{
    @Override
    public DataContext selectData(ShopReq param) {
        DataContext context = new DataContext();
        context.setSheetDataList(Collections.singletonList(new ShopExportWrapper()));
        return context;
    }

    @Override
    public Integer getModule() {

        return 0;
    }

    @Override
    public String getFileName() {

        return "";
    }
}
