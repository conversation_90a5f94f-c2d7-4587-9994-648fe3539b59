package com.hishop.himall.report.core.exports.models;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class CustomExportVo
{
    @ExcelProperty(value = "日期")
    private LocalDate date;

    private Long shopId;
    @ExcelProperty(value = "店铺名称")
    private String shopName;

    private Long productId;
    @ExcelProperty(value = "商品名称")
    private String productName;
    @ExcelProperty(value = "商品编码")
    private String productSpu;

    //下单相关
    @ExcelProperty(value = "下单笔数")
    private Integer orderOrders;
    @ExcelProperty(value = "下单人数")
    private Integer orderUsers;
    @ExcelProperty(value = "下单金额")
    private BigDecimal orderAmount;
    //支付相关
    @ExcelProperty(value = "支付笔数")
    private Integer paymentOrders;
    @ExcelProperty(value = "支付人数")
    private Integer paymentUsers;
    @ExcelProperty(value = "支付金额")
    private Integer paymentAmount;
    @ExcelProperty(value = "支付件数")
    private Integer paymentQuantity;
    @ExcelProperty(value = "支付客单价")
    private Integer paymentUnitPrice;
    @ExcelProperty(value = "支付商品金额")
    private Integer paymentProductAmount;
    @ExcelProperty(value = "支付商品件数")
    private Integer paymentProductQuantity;
    //新老用户相关
    @ExcelProperty(value = "新客支付金额")
    private Integer newUserPaymentAmount;
    @ExcelProperty(value = "新客支付笔数")
    private Integer newUserPaymentOrders;
    @ExcelProperty(value = "新客支付人数")
    private Integer newUserPaymentUsers;
    @ExcelProperty(value = "老客支付金额")
    private Integer oldUserPaymentAmount;
    @ExcelProperty(value = "老客支付笔数")
    private Integer oldUserPaymentOrders;
    @ExcelProperty(value = "老客支付人数")
    private Integer oldUserPaymentUsers;
    //加购相关
    @ExcelProperty(value = "加购件数")
    private Integer cartQuantity;
    @ExcelProperty(value = "加购人数")
    private Integer cartUsers;
    //访问相关
    @ExcelProperty(value = "访客数")
    private Integer visitUsers;
    @ExcelProperty(value = "访问量")
    private Integer visitCount;
    @ExcelProperty(value = "店铺访客数")
    private Integer visitShopUsers;
    @ExcelProperty(value = "店铺访问量")
    private Integer visitShopCount;
    @ExcelProperty(value = "商品访客数")
    private Integer visitProductUsers;
    @ExcelProperty(value = "商品访问量")
    private Integer visitProductCount;
    //转化率相关
    @ExcelProperty(value = "访问-下单转化率")
    private BigDecimal visitOrderRate;
    @ExcelProperty(value = "访问-支付转化率")
    private BigDecimal visitPaymentRate;
    @ExcelProperty(value = "下单-支付转化率")
    private BigDecimal orderPaymentRate;
    //售后相关
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ExcelProperty(value = "退款笔数")
    private Integer refundOrders;
    @ExcelProperty(value = "退款件数")
    private Integer refundQuantity;
    @ExcelProperty(value = "退款人数")
    private Integer refundUsers;
    @ExcelProperty(value = "退款率")
    private BigDecimal refundRate;
    //包裹相关
    @ExcelProperty(value = "包裹发货数")
    private Integer packageDelivery;
    @ExcelProperty(value = "包裹完成数")
    private Integer packageFinish;
    @ExcelProperty(value = "发货时长(小时)")
    private BigDecimal packageDeliveryHour;
    @ExcelProperty(value = "完成时长(小时)")
    private BigDecimal packageFinishHour;
    //用户相关
    @ExcelProperty(value = "用户总数")
    private Integer userTotal;
    @ExcelProperty(value = "新增用户数")
    private Integer userNew;
    @ExcelProperty(value = "支付用户数")
    private Integer userPayment;
    @ExcelProperty(value = "领券用户数")
    private Integer userCoupon;
    @ExcelProperty(value = "加购用户数")
    private Integer userCart;
    //其他
    @ExcelProperty(value = "关注人数")
    private Integer followUsers;
    @ExcelProperty(value = "复购率")
    private BigDecimal repurchaseRate;
}
