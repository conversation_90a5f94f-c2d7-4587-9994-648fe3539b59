package com.hishop.himall.report.core.service;

import com.hishop.himall.report.api.request.CustomPageReq;
import com.hishop.himall.report.api.request.CustomRecordPageReq;
import com.hishop.himall.report.api.request.CustomReq;
import com.hishop.himall.report.api.response.CustomPageResp;
import com.hishop.himall.report.api.response.CustomRecordPageResp;
import com.hishop.himall.report.api.response.CustomResp;
import com.hishop.himall.report.core.exports.models.CustomExportCmd;
import com.hishop.himall.report.core.exports.models.CustomExportVo;
import com.hishop.starter.util.model.PageResult;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;

import java.util.List;

public interface ReportCustomService {

    List<CustomExportVo> process(Long id);

    BasePageResp<CustomPageResp> query(CustomPageReq req);

    CustomResp getCustom(Long id);

    void save(CustomReq req);

    List<CustomExportVo> queryExport(CustomExportCmd req);

    BasePageResp<CustomRecordPageResp> queryRecords(CustomRecordPageReq req);

    Long export(Long id,Long operatorId,String operatorName);

    void delete(Long id);

    CustomResp getByRecordId(Long id);
}
