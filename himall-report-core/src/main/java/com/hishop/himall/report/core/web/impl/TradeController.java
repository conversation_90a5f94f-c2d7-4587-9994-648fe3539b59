package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.TradeReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.TradeResp;
import com.hishop.himall.report.api.response.TradeSummaryResp;
import com.hishop.himall.report.api.service.ReportTradeFeign;
import com.hishop.himall.report.core.service.ReportShopQueryService;
import com.hishop.starter.util.model.PageResult;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("report/trade")
public class TradeController implements ReportTradeFeign {

    @Resource
    private ReportShopQueryService reportShopQueryService;

    /**
     * 交易概览
     *
     */
    @PostMapping(value = "/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<TradeSummaryResp> queryProductSummary(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("summary", reportReq, req -> {
            return reportShopQueryService.getTradeSummary(req);
        });
    }

    /**
     * 交易趋势
     *
     */
    @PostMapping(value = "/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> echarts(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("echarts", reportReq, req -> {
            return reportShopQueryService.getTradeEchats(req);
        });
    }

    /**
     * 交易省份分布
     *
     */
    @PostMapping(value = "/province", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryProvince(@RequestBody ReportReq reportReq) {
        return ThriftResponseHelper.responseInvoke("province", reportReq, req -> {
            return reportShopQueryService.getProvinceTradeEchats(req);
        });
    }

    /**
     * 交易分析报表
     *
     */
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<PageResult<TradeResp>> queryTrade(@RequestBody TradeReq tradeReq) {
        return ThriftResponseHelper.responseInvoke("query", tradeReq, req -> {
            return reportShopQueryService.getTrades(req);
        });
    }

    /**
     * 导出交易分析报表
     *
     */
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<PageResult<Object>> export(@RequestBody TradeReq tradeReq) {
        return ThriftResponseHelper.responseInvoke("export", tradeReq, req -> {
            reportShopQueryService.exportTrades(req);
            return null;
        });
    }
}
