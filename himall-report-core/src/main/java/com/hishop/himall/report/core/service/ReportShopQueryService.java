package com.hishop.himall.report.core.service;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.ShopReq;
import com.hishop.himall.report.api.request.TradeReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.ShopReportResp;
import com.hishop.himall.report.api.response.TradeResp;
import com.hishop.himall.report.api.response.TradeSummaryResp;
import com.hishop.starter.util.model.PageResult;

public interface ReportShopQueryService {
    /**
     * 店铺新增趋势
     *
     * @param reportReq
     * @return
     */
    Echarts getNewShopEchats(ReportReq reportReq);

    /**
     * 店铺省份分布
     *
     * @param reportReq
     */
    Echarts getProvinceWithShop(ReportReq reportReq);

    /**
     * 店铺交易导出
     *
     * @param shopReq
     */
    void exportShopTrade(ShopReq shopReq);

    /**
     * 店铺交易
     *
     * @param shopReq
     */
    PageResult<ShopReportResp> getShopTrade(ShopReq shopReq);

    /**
     * 店铺交易趋势
     *
     * @param req
     */
    TradeSummaryResp getTradeSummary(ReportReq req);

    /**
     * 店铺交易趋势
     *
     * @param reportReq
     * @return
     */
    Echarts getTradeEchats(ReportReq reportReq);

    /**
     * 店铺交易省份分布
     *
     * @param reportReq
     * @return
     */
    Echarts getProvinceTradeEchats(ReportReq reportReq);

    /**
     * 获取交易列表
     *
     * @param tradeReq
     * @return
     */
    PageResult<TradeResp> getTrades(TradeReq tradeReq);

    /**
     * 导出交易报表
     *
     * @param tradeReq
     */
    void exportTrades(TradeReq tradeReq);
}
