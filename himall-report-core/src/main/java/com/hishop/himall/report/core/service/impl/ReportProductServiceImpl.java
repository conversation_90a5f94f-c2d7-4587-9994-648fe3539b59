package com.hishop.himall.report.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hishop.himall.report.dao.domain.ReportProductTrade;
import com.hishop.himall.report.dao.domain.ReportSourceProduct;
import com.hishop.himall.report.dao.mapper.ReportProductTradeMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceCartMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceOrderItemMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceProductMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceRefundMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceVisitMapper;
import com.hishop.himall.report.dao.models.TradeSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch;

@Service
public class ReportProductServiceImpl implements com.hishop.himall.report.core.service.ReportProductService {

    @Resource
    private ReportSourceCartMapper reportSourceCartMapper;
    @Resource
    private ReportSourceOrderItemMapper reportSourceOrderItemMapper;
    @Resource
    private ReportSourceRefundMapper reportSourceRefundMapper;
    @Resource
    private ReportProductTradeMapper reportProductTradeMapper;
    @Resource
    private ReportSourceVisitMapper reportSourceVisitMapper;
    @Resource
    private ReportSourceProductMapper reportSourceProductMapper;

    @Override public void settlement(String range, LocalDate start, LocalDate end,List<Long> products) {
        settlement(range, start, end, null,products);
    }

    @Override public void settlement(String range, LocalDate start, LocalDate end, Integer platform,List<Long> products) {
        List<ReportProductTrade> trades = new ArrayList<>();
        // 访问统计
        fillVisits(trades, start, end,platform);
        // 加购统计
        fillCart(trades, start, end,platform,products);
        // 下单统计
        fillOrderTrades(trades, start, end,platform,products);
        // 支付统计
        fillPaymentTrades(trades, start, end,platform,products);
        // 售后申请统计
        fillApplyRefund(trades, start, end,platform,products);
        // 售后统计
        fillRefundTrades(trades, start, end,platform,products);
        // 保存
        batchSave(range, start, end, trades,platform);
    }


    private void fillVisits(List<ReportProductTrade> trades, LocalDate start, LocalDate end,Integer platform) {
        reportSourceVisitMapper.getProductVisitSummary(start, end,platform).forEach(summary -> {
            ReportProductTrade trade = Find(trades, summary,platform);
            trade.setVisitsCount(summary.getQuantity());
            trade.setVisitsUsers(summary.getUsers());
        });
    }

    private void fillApplyRefund(List<ReportProductTrade> trades, LocalDate start, LocalDate end,Integer platform,List<Long> products) {
        reportSourceRefundMapper.getProductApplyRefundSummary(start, end,products).forEach(summary -> {
            ReportProductTrade trade = Find(trades, summary,platform);
            trade.setApplyOrders(summary.getOrders());
            trade.setApplyUsers(summary.getUsers());
        });
    }

    private void fillRefundTrades(List<ReportProductTrade> trades, LocalDate start, LocalDate end,Integer platform,List<Long> products) {
        reportSourceRefundMapper.getProductRefundSummary(start, end,products).forEach(summary -> {
            ReportProductTrade trade = Find(trades, summary,platform);
            trade.setRefundOrders(summary.getOrders());
            trade.setRefundUsers(summary.getUsers());
            trade.setRefundAmount(summary.getAmount());
            trade.setRefundQuantity(summary.getQuantity());
        });
    }

    private void fillCart(List<ReportProductTrade> trades, LocalDate start, LocalDate end,Integer platform,List<Long> products) {
        reportSourceCartMapper.getProductCartSummary(start, end,products,platform).forEach(summary -> {
            ReportProductTrade trade = Find(trades, summary,platform);
            trade.setCartUsers(summary.getUsers());
            trade.setCartQuantity(summary.getQuantity());
        });
    }

    private void fillOrderTrades(List<ReportProductTrade> trades, LocalDate start, LocalDate end,Integer platform,List<Long> products) {
        reportSourceOrderItemMapper.getProductOrderSummary(start, end,platform,products,null).forEach(summary -> {
            ReportProductTrade trade = Find(trades, summary,platform);
            trade.setOrderUsers(summary.getUsers());
            trade.setOrderOrders(summary.getOrders());
            trade.setOrderQuantity(summary.getQuantity());
        });
    }

    private void fillPaymentTrades(List<ReportProductTrade> trades, LocalDate start, LocalDate end,Integer platform,List<Long> products) {
        reportSourceOrderItemMapper.getProductPaymentSummary(start, end,platform,products).forEach(summary -> {
            ReportProductTrade trade = Find(trades, summary,platform);
            trade.setPaymentUsers(summary.getUsers());
            trade.setPaymentAmount(summary.getAmount());
            trade.setPaymentOrders(summary.getOrders());
            trade.setPaymentQuantity(summary.getQuantity());
        });
    }

    private void batchSave(String range, LocalDate start, LocalDate end, List<ReportProductTrade> trades,Integer platform) {
        List<Long> product_list = trades.stream()
                .map(ReportProductTrade::getProductId)
                .collect(Collectors.toList());
        if(product_list.isEmpty())
            return;
        List<ReportSourceProduct> products = reportSourceProductMapper.selectBatchIds(product_list);
        trades.forEach(trade -> {
            products.stream()
                    .filter(p -> p.getProductId().equals(trade.getProductId()))
                    .findFirst()
                    .ifPresent(s ->{
                        trade.setCategoryFirst(s.getCategoryFirst());
                        trade.setCategorySecond(s.getCategorySecond());
                    });
            trade.setDate(start);
            trade.setRange(range);
        });

        // 删除已存在
        QueryWrapper<ReportProductTrade> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("`range`", range);
        queryWrapper.eq("date", start);
        if(platform!=null)
            queryWrapper.eq("platform", platform);
        reportProductTradeMapper.delete(queryWrapper);

        saveBatch(trades);
    }

    private ReportProductTrade Find(List<ReportProductTrade> trades, TradeSource summary,Integer platform) {
        return trades.stream().filter(p ->
        p.getProductId().equals(summary.getProductId())
                && p.getShopId().equals(summary.getShopId()))
                .findFirst().orElseGet(() -> {
                    ReportProductTrade newTrade = new ReportProductTrade();
                    newTrade.setDate(summary.getDate());
                    newTrade.setProductId(summary.getProductId());
                    newTrade.setShopId(summary.getShopId());
                    newTrade.setPlatform(platform);
                    trades.add(newTrade);
                    return newTrade;
                });

    }
}
