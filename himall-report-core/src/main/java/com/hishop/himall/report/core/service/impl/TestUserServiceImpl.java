package com.hishop.himall.report.core.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.himall.report.api.demo.request.TestUserQueryPageReq;
import com.hishop.himall.report.api.demo.request.TestUserQueryReq;
import com.hishop.himall.report.api.demo.response.TestUserQueryResp;
import com.hishop.himall.report.core.convert.TestUserConvert;
import com.hishop.himall.report.core.service.TestUserService;
import com.hishop.himall.report.dao.demo.domain.ReportAccessRecords;
import com.hishop.himall.report.dao.demo.mapper.ReportAccessRecordsMapper;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.web.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TestUserServiceImpl implements TestUserService {

    @Resource
    private ReportAccessRecordsMapper reportAccessRecordsMapper;
    @Resource
    private TestUserConvert testUserConvert;

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public TestUserQueryResp queryUser(TestUserQueryReq req) {
//        LambdaQueryWrapper<ReportAccessRecords> select = Wrappers.lambdaQuery(ReportAccessRecords.class)
//                .eq(req.getName() != null, ReportAccessRecords::getCreateUser, req.getName());
//        List<ReportAccessRecords> reportAccessRecords = reportAccessRecordsMapper.selectList(select);
//        IPage<ReportAccessRecords> reportAccessRecordsIPage = reportAccessRecordsMapper.selectPage(Page.of(1, 10),
//                                                                                                   select);
////        log.atInfo().addArgument(() -> JsonUtil.toJSONString(reportAccessRecords)).log("[queryUser] queryUser:{}");
//        log.info("[queryUser] queryUser:{}", JsonUtil.toJSONString(reportAccessRecords));
        TestUserQueryResp resp = new TestUserQueryResp();
        resp.setId(req.getId());
        return resp;
    }

    @Override
    public PageResult<TestUserQueryResp> queryPage(TestUserQueryPageReq req) {
        Page<ReportAccessRecords> page = reportAccessRecordsMapper.selectPage(PageUtil.toPage(req, true),
                                                                              Wrappers.<ReportAccessRecords>emptyWrapper());
        return PageUtil.buildPage(page, testUserConvert::toTargetList);
    }
}
