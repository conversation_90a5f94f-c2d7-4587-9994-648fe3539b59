package com.hishop.himall.report.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hishop.himall.report.api.enums.Dimension;
import com.hishop.himall.report.api.enums.Fields;
import com.hishop.himall.report.api.enums.Platform;
import com.hishop.himall.report.api.exception.ErrorCode;
import com.hishop.himall.report.api.exception.ReportException;
import com.hishop.himall.report.api.request.CustomPageReq;
import com.hishop.himall.report.api.request.CustomRecordPageReq;
import com.hishop.himall.report.api.request.CustomReq;
import com.hishop.himall.report.api.response.CustomPageResp;
import com.hishop.himall.report.api.response.CustomRecordPageResp;
import com.hishop.himall.report.api.response.CustomResp;
import com.hishop.himall.report.core.exports.models.CustomExportCmd;
import com.hishop.himall.report.core.exports.models.CustomExportVo;
import com.hishop.himall.report.core.service.ReportCustomService;
import com.hishop.himall.report.core.service.ReportProductService;
import com.hishop.himall.report.core.service.ReportShopService;
import com.hishop.himall.report.dao.domain.ReportCustom;
import com.hishop.himall.report.dao.domain.ReportCustomRecord;
import com.hishop.himall.report.dao.domain.ReportProductTrade;
import com.hishop.himall.report.dao.domain.ReportShopTrade;
import com.hishop.himall.report.dao.mapper.ReportCustomMapper;
import com.hishop.himall.report.dao.mapper.ReportCustomRecordMapper;
import com.hishop.himall.report.dao.mapper.ReportProductTradeMapper;
import com.hishop.himall.report.dao.mapper.ReportShopTradeMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceProductMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceShopMapper;
import com.hishop.himall.report.dao.models.CustomSource;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.web.util.PageUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.client.actuator.HasFeatures;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ReportCustomServiceImpl implements ReportCustomService {
    @Resource
    private ReportCustomMapper reportCustomMapper;
    @Resource
    private ReportCustomRecordMapper reportCustomRecordMapper;
    @Resource
    private ReportShopTradeMapper reportShopTradeMapper;
    @Resource
    private ReportProductTradeMapper reportProductTradeMapper;
    @Resource
    private ReportSourceProductMapper reportSourceProductMapper;
    @Resource
    private ReportSourceShopMapper reportSourceShopMapper;
    @Resource
    private ReportProductService reportProductService;
    @Resource
    private ReportShopService reportShopService;

    @Override
    public List<CustomExportVo> process(Long id) {
        ReportCustomRecord record = reportCustomRecordMapper.selectById(id);
        ReportCustom custom = reportCustomMapper.selectById(record.getCustomId());
        String myRange = "report" + id;
        String range = record.getRange();
        List<Long> shops=null;
        if(custom.getShops() != null && !custom.getShops().isEmpty())
            shops = Arrays.stream(custom.getShops().split(",")).map(Long::parseLong).collect(Collectors.toList());
        LocalDate start = record.getStartDate();
        LocalDate end = record.getEndDate();
        if (record.getAutomatic() || record.getPlatform() > 0) {
            Integer platform = null;
            if (record.getPlatform() > 0)
                platform = record.getPlatform();
            if (record.getDimension() == Dimension.PRODUCT.getValue()) {
                List<Long> products = null;
                if (custom.getProducts() != null)
                    products = Arrays.stream(custom.getProducts().split(",")).map(Long::valueOf)
                            .collect(Collectors.toList());
                if (range.indexOf("DAY") > 0) {
                    for (LocalDate date = start; date.isBefore(end); date = date.plusDays(1)) {
                        reportProductService.settlement(myRange, date, date, platform, products);
                    }
                } else if (range.indexOf("WEEK") > 0) {
                    //循环开始时间到结束时间
                    for (LocalDate date = start; date.isBefore(end); date = date.plusWeeks(1)) {
                        reportProductService.settlement(myRange, date, date.plusWeeks(1).minusDays(1), platform, products);
                    }
                } else if (range.indexOf("MONTH") > 0) {
                    //循环开始时间到结束时间
                    for (LocalDate date = start; date.isBefore(end); date = date.plusMonths(1)) {
                        reportProductService.settlement(myRange, date, date.plusMonths(1).minusDays(1), platform, products);
                    }
                }
            } else {
                if (range.indexOf("DAY") > 0) {
                    for (LocalDate date = start; date.isBefore(end); date = date.plusDays(1)) {
                        reportShopService.settlement(myRange, date, date, shops);
                    }
                } else if (range.indexOf("WEEK") > 0) {
                    //循环开始时间到结束时间
                    for (LocalDate date = start; date.isBefore(end); date = date.plusWeeks(1)) {
                        reportShopService.settlement(myRange, date, date.plusWeeks(1).minusDays(1), shops);
                    }
                } else if (range.indexOf("MONTH") > 0) {
                    //循环开始时间到结束时间
                    for (LocalDate date = start; date.isBefore(end); date = date.plusMonths(1)) {
                        reportShopService.settlement(myRange, date, date.plusMonths(1).minusDays(1), shops);
                    }
                }
            }
        }
        CustomExportCmd cmd = new CustomExportCmd();
        cmd.setDimension(Dimension.getDimension(record.getDimension()));
        cmd.setRange(myRange);
        cmd.setStart(start);
        cmd.setEnd(end);
        List<String> fields = new ArrayList<>();
        fields.add("date");
        if (cmd.getDimension() == Dimension.PRODUCT) {
            fields.add("productName");
            fields.add("productSpu");
        } else if (cmd.getDimension() == Dimension.SHOP) {
            fields.add("shopName");
        }
        Arrays.stream(custom.getFields().split(",")).forEach(item -> {
            fields.add(Fields.valueOf(Integer.parseInt(item)).getFieldName());
        });
        cmd.setSelectedFieldList(fields);
        return queryExport(cmd);
    }

    public List<CustomExportVo> queryExport(CustomExportCmd req) {
        List<CustomExportVo> result = new ArrayList<>();
        if (req.getDimension() == Dimension.PRODUCT) {
            QueryWrapper<ReportProductTrade> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("`range`", req.getRange());
            queryWrapper.between("date", req.getStart(), req.getEnd());
            queryWrapper.orderByAsc("date");
            reportProductTradeMapper.selectList(com.baomidou.mybatisplus.extension.plugins.pagination.Page.of(req.getPageNo(), req.getPageSize(), true), queryWrapper)
                    .forEach(item -> result.add(Map(item)));

            List<Long> list = result.stream().map(CustomExportVo::getProductId).collect(Collectors.toList());
            if (!list.isEmpty()) {
                reportSourceProductMapper.selectBatchIds(list).forEach(product -> {
                    result.stream()
                            .filter(s -> s.getProductId().equals(product.getProductId()))
                            .forEach(s -> {
                                s.setProductName(product.getProductName());
                                s.setProductSpu(product.getProductSpu());
                            });
                });
            }
        } else {
            QueryWrapper<ReportShopTrade> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("`range`", req.getRange());
            if (req.getDimension() == Dimension.PLATFORM)
                queryWrapper.eq("shop_id", 0);
            else
                queryWrapper.ne("shop_id", 0);
            queryWrapper.between("date", req.getStart(), req.getEnd());
            queryWrapper.orderByAsc("date");
            reportShopTradeMapper.selectList(com.baomidou.mybatisplus.extension.plugins.pagination.Page.of(req.getPageNo(), req.getPageSize(), true), queryWrapper)
                    .forEach(item -> result.add(Map(item)));
            if (req.getDimension() == Dimension.SHOP) {
                List<Long> list = result.stream().map(CustomExportVo::getShopId).collect(Collectors.toList());
                if (!list.isEmpty()) {
                    reportSourceShopMapper.selectBatchIds(list).forEach(shop -> {
                        result.stream()
                                .filter(s -> s.getShopId().equals(shop.getShopId()))
                                .forEach(s -> {
                                    s.setShopName(shop.getShopName());
                                });
                    });
                }
            }
        }
        return result;
    }

    @Override
    public BasePageResp<CustomPageResp> query(CustomPageReq req) {
        Page<ReportCustomRecord> recordPage = PageHelper.startPage(req.getPageNo(), req.getPageSize());
        QueryWrapper<ReportCustom> queryWrapper = new QueryWrapper<>();
        if(req.getShopId() != null)
            queryWrapper.eq("shop_id", req.getShopId());
        else
            queryWrapper.eq("shop_id", 0);

        if (req.getName() != null && !req.getName().isEmpty())
            queryWrapper.like("name", req.getName());
        if (req.getDimension() != null)
            queryWrapper.eq("dimension", req.getDimension());
        if (req.getAutomatic() != null)
            queryWrapper.eq("automatic", req.getAutomatic());
        if (req.getRange() != null && !req.getRange().isEmpty())
            queryWrapper.eq("`range`", req.getRange());
        queryWrapper.orderByDesc("update_time");
        recordPage.doSelectPage(() -> reportCustomMapper.selectList(queryWrapper));
        return PageResultHelper.transfer(recordPage, CustomPageResp.class);
    }

    @Override
    public CustomResp getCustom(Long id) {
        ReportCustom custom = reportCustomMapper.selectById(id);
        CustomResp resp = new CustomResp();
        resp.setId(custom.getId());
        resp.setName(custom.getName());
        resp.setPlatform(Platform.getPlatform(custom.getPlatform()));
        resp.setDimension(Dimension.getDimension(custom.getDimension()));
        resp.setGroups(Arrays.asList(custom.getGroups().split(",")));
        resp.setFields(Arrays.asList(custom.getFields().split(",")));
        if(custom.getShops() != null && !custom.getShops().isEmpty())
            resp.setShops(Arrays.stream(custom.getShops().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        if(custom.getProducts() != null && !custom.getProducts().isEmpty())
            resp.setProducts(Arrays.stream(custom.getProducts().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        resp.setRange(custom.getRange());
        resp.setAutomatic(custom.getAutomatic());
        resp.setTimes(custom.getTimes());
        resp.setStartDate(custom.getStartDate());
        resp.setEndDate(custom.getEndDate());
        return resp;
    }

    @Override
    public void save(CustomReq req) {
        if (req.getId() != null && req.getId() > 0) {
            ReportCustom custom = reportCustomMapper.selectById(req.getId());
            if (custom == null)
                throw new ReportException(ErrorCode.NotFound.getCode(), ErrorCode.NotFound.getMessage());
            mapper(req, custom);
            reportCustomMapper.updateById(custom);
        } else {
            ReportCustom custom = new ReportCustom();
            mapper(req, custom);
            reportCustomMapper.insert(custom);
        }
    }

    private void mapper(CustomReq req, ReportCustom custom) {
        custom.setName(req.getName());
        custom.setShopId(req.getShopId());
        custom.setPlatform(req.getPlatform().getValue());
        custom.setDimension(req.getDimension().getValue());
        custom.setGroups(req.getGroups().stream().map(String::valueOf).collect(Collectors.joining(",")));
        custom.setFields(req.getFields().stream().map(String::valueOf).collect(Collectors.joining(",")));
        custom.setShops(Optional.ofNullable(req.getShops()).map(shops -> shops.stream().map(String::valueOf).collect(Collectors.joining(","))).orElse(null));
        if (CollUtil.isNotEmpty(req.getProducts())) {
            custom.setProducts(req.getProducts().stream().map(String::valueOf).collect(Collectors.joining(",")));

        }
        custom.setRange(req.getRange());
        custom.setAutomatic(req.getAutomatic());
        custom.setTimes(req.getTimes());
        custom.setStartDate(req.getStartDate());
        custom.setEndDate(req.getEndDate());
    }

    @Override
    public BasePageResp<CustomRecordPageResp> queryRecords(CustomRecordPageReq req) {
        Page<ReportCustomRecord> recordPage = PageHelper.startPage(req.getPageNo(), req.getPageSize());
        QueryWrapper<ReportCustomRecord> queryWrapper = new QueryWrapper<>();
        if (req.getOperatorId() != null)
            queryWrapper.eq("operator_Id", req.getOperatorId());

//        Page<ReportCustomRecord> result = reportCustomRecordMapper.selectPage(PageUtil.toPage(req, true), queryWrapper);
        recordPage.doSelectPage(() -> reportCustomRecordMapper.selectList(queryWrapper));
        BasePageResp<CustomRecordPageResp> resultPage = PageResultHelper.transfer(recordPage, CustomRecordPageResp.class);
        return resultPage;
    }

    /**
     * 导出
     *
     * @param id
     * @param operatorId
     */
    @Override
    public Long export(Long id, Long operatorId, String operatorName) {
        ReportCustom custom = reportCustomMapper.selectById(id);
        ReportCustomRecord record = new ReportCustomRecord();
        record.setCustomId(id);
        record.setShopId(custom.getShopId());
        record.setName(custom.getName());
        record.setOperatorId(operatorId);
        record.setOperatorName(operatorName);
        record.setDimension(custom.getDimension());
        record.setPlatform(custom.getPlatform());
        record.setName(custom.getName());
        record.setAutomatic(custom.getAutomatic());
        record.setStatus(1);
        if (custom.getAutomatic()) {
            switch (custom.getRange()) {
                case "DAY":
                    record.setRange("lastDAY" + custom.getTimes());
                    record.setStartDate(LocalDate.now().minusDays(custom.getTimes()));
                    record.setEndDate(LocalDate.now());
                    break;
                case "WEEK":
                    record.setRange("lastWEEK" + custom.getTimes());
                    record.setStartDate(LocalDate.now().minusWeeks(custom.getTimes()));
                    record.setEndDate(LocalDate.now());
                    break;
                case "MONTH":
                    record.setRange("lastMONTH" + custom.getTimes());
                    record.setStartDate(LocalDate.now().minusMonths(custom.getTimes()));
                    record.setEndDate(LocalDate.now());
                    break;
            }
        } else {
            record.setRange(custom.getRange());
            record.setStartDate(custom.getStartDate());
            record.setEndDate(custom.getEndDate());
        }
        reportCustomRecordMapper.insert(record);
        return record.getId();
    }

    @Override
    public void delete(Long id) {
        reportCustomMapper.deleteById(id);
    }

    @Override
    public CustomResp getByRecordId(Long id) {
        ReportCustomRecord record = reportCustomRecordMapper.selectById(id);
        if (record == null) {
            return null;
        }
        return getCustom(record.getCustomId());
    }

    private CustomExportVo Map(Object obj) {
        try {
            CustomExportVo vo = new CustomExportVo();
            BeanUtils.copyProperties(obj, vo);
            return vo;
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }
}
