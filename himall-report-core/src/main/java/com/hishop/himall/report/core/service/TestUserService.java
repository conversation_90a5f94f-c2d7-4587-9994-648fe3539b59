package com.hishop.himall.report.core.service;

import com.hishop.himall.report.api.demo.request.TestUserQueryPageReq;
import com.hishop.himall.report.api.demo.request.TestUserQueryReq;
import com.hishop.himall.report.api.demo.response.TestUserQueryResp;
import com.hishop.starter.util.model.PageResult;

/**
 * <AUTHOR>
 */
public interface TestUserService {
    /**
     * 查询用户
     *
     * @param req 入参
     * @return 结果
     */
    TestUserQueryResp queryUser(TestUserQueryReq req);

    /**
     * 分页查询
     *
     * @param req 入参
     * @return 结果
     */
    PageResult<TestUserQueryResp> queryPage(TestUserQueryPageReq req);

}
