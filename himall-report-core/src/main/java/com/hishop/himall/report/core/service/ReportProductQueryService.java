package com.hishop.himall.report.core.service;

import com.hishop.himall.report.api.request.ProductReq;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.ProductResp;
import com.hishop.himall.report.api.response.ProductSummaryResp;
import com.hishop.starter.util.model.PageResult;

public interface ReportProductQueryService {

    ProductSummaryResp getProductSummary(ReportReq req);

    Echarts getProductVisitsEchats(ReportReq req);

    Echarts getProductCategoryEchats(ReportReq reportReq);

    PageResult<ProductResp> getProducts(ProductReq reportReq);

    void exportProducts(ProductReq reportReq);

}
