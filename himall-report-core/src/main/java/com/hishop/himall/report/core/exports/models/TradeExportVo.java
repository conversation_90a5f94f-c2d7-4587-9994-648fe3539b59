package com.hishop.himall.report.core.exports.models;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class TradeExportVo {
    @ExcelProperty(value = "日期")
    private Date date;
    @ExcelProperty(value = "下单笔数")
    private Integer orderOrders;
    @ExcelProperty(value = "下单笔数")
    private Integer orderUsers;
    @ExcelProperty(value = "支付笔数")
    private Integer paymentOrders;
    @ExcelProperty(value = "支付人数")
    private Integer paymentUsers;
    @ExcelProperty(value = "支付金额")
    private BigDecimal paymentAmount;
    @ExcelProperty(value = "支付转化率")
    public BigDecimal paymentRate;
}
