package com.hishop.himall.report.core.service.impl;

import com.baomidou.mybatisplus.core.batch.MybatisBatch;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hishop.himall.report.dao.domain.ReportShopTrade;
import com.hishop.himall.report.dao.domain.ReportSourceVisit;
import com.hishop.himall.report.dao.domain.ReportUserTrade;
import com.hishop.himall.report.dao.mapper.ReportShopTradeMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceOrderItemMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceOrderMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceRefundMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceVisitMapper;
import com.hishop.himall.report.dao.mapper.ReportUserTradeMapper;
import com.hishop.himall.report.dao.models.TradeSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch;

@Service
public class ReportUserServiceImpl implements com.hishop.himall.report.core.service.ReportUserService {
    @Resource
    private ReportSourceOrderMapper reportSourceOrderMapper;
    @Resource
    private ReportSourceOrderItemMapper reportSourceOrderItemMapper;
    @Resource
    private ReportSourceRefundMapper reportSourceRefundMapper;
    @Resource
    private ReportUserTradeMapper reportUserTradeMapper;

    public void settlement(String range,LocalDate start, LocalDate end) {
        List<ReportUserTrade> trades = new ArrayList<>();
        //下单统计
        fillUserOrderTrades(trades, start, end);
        //支付订单统计
        fillUserPaymentOrderTrades(trades, start, end);
        //支付统计
        fillUserPaymentItemsTrades(trades, start, end);
        //售后统计
        fillUserRefundTrades(trades, start, end);
        //保存
        batchSave(range, start, end, trades);
    }

    private void batchSave(String range, LocalDate start, LocalDate end, List<ReportUserTrade> trades) {
        trades.forEach(trade -> {
            trade.setRange(range);
            trade.setDate(start);
        });
        //删除已存在
        QueryWrapper<ReportUserTrade> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("`range`", range);
        queryWrapper.eq("`date`", start);
        reportUserTradeMapper.delete(queryWrapper);

        //批量新增
        saveBatch(trades);
    }


    private void fillUserRefundTrades(List<ReportUserTrade> trades, LocalDate start, LocalDate end){
        reportSourceRefundMapper.getCompletionSummary(start, end).forEach(summary -> {
            ReportUserTrade trade = Find(trades, summary);
            trade.setRefundOrders(summary.getOrders());
            trade.setRefundAmount(summary.getAmount());
        });
    }

    private void fillUserPaymentOrderTrades(List<ReportUserTrade> trades, LocalDate start, LocalDate end){
        reportSourceOrderMapper.getUserPaymentSummary(start, end).forEach(summary -> {
            ReportUserTrade trade = Find(trades, summary);
            trade.setPaymentOrders(summary.getOrders());
            trade.setPaymentOrderAmount(summary.getAmount());
        });
    }

    private void fillUserPaymentItemsTrades(List<ReportUserTrade> trades, LocalDate start, LocalDate end) {
        reportSourceOrderItemMapper.getUserPaymentSummary(start, end,null,null).forEach(summary -> {
            ReportUserTrade trade = Find(trades, summary);
            trade.setPaymentOrders(summary.getOrders());
            trade.setPaymentQuantity(summary.getQuantity());
            trade.setPaymentProductAmount(summary.getAmount());
        });
    }
    private void fillUserOrderTrades(List<ReportUserTrade> trades, LocalDate start, LocalDate end ) {
         reportSourceOrderMapper.getUserOrderSummary(start, end).forEach(summary -> {
             ReportUserTrade trade = Find(trades, summary);
             trade.setOrderOrders(summary.getOrders());
         });
    }

    private ReportUserTrade Find(List<ReportUserTrade> trades, TradeSource summary) {
        return trades.stream().filter(p -> p.getUserId().equals(summary.getUserId()))
                .findFirst().orElseGet(()->{
                    ReportUserTrade trade = new ReportUserTrade();
                    trade.setUserId(summary.getUserId());
                    trade.setShopId(summary.getShopId());
                    trades.add(trade);
                    return trade;
                });
    }
}