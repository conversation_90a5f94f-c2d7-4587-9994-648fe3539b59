package com.hishop.himall.report.core.task;

import com.hishop.himall.report.api.enums.RangeEnum;
import com.hishop.himall.report.core.service.ReportShopService;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

@Slf4j
@Component
public class ShopTask {
    @Resource
    private ReportShopService reportShopService;

    private LocalDate getDate(String param) {
        return param == null ? LocalDate.now().minusDays(1) : LocalDate.parse(param);
    }
    @XxlJob("shopWithDate")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "店铺结算")
    public void withDate(String param) {
        log.info("shopWithDate参数：{}",param);
        LocalDate date = getDate(param);
        reportShopService.settlement(RangeEnum.DAY.toString(), date, date,null);
    }

    @XxlJob("shopWithWeek")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "店铺结算（周）")
    public void withWeek(String param) {
        log.info("shopWithWeek参数：{}",param);
        LocalDate date = getDate(param);
        LocalDate monday = date.minusDays(date.getDayOfWeek().getValue() - 1);
        LocalDate sunday = monday.plusDays(7);
        reportShopService.settlement(RangeEnum.WEEK.toString(), monday, sunday,null);
    }

    @XxlJob("shopWithMonth")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "店铺结算（月）")
    public void withMonth(String param) {
        log.info("shopWithMonth参数：{}",param);
        LocalDate date = getDate(param);
        LocalDate firstDay = date.withDayOfMonth(1);
        LocalDate lastDay = firstDay.plusMonths(1).minusDays(1);
        reportShopService.settlement(RangeEnum.MONTH.toString(), firstDay, lastDay,null);
    }
}
