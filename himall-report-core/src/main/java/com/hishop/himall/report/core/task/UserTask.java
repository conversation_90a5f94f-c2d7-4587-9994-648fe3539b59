package com.hishop.himall.report.core.task;

import com.hishop.himall.report.api.enums.RangeEnum;
import com.hishop.himall.report.core.service.ReportUserService;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

@Slf4j
@Component
public class UserTask
{
    @Resource
    private ReportUserService userService;

    private LocalDate getDate(String param) {
        return param == null ? LocalDate.now().minusDays(1) : LocalDate.parse(param);
    }
    @XxlJob("userWithDate")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "用户结算")
    public void withDate(String param) {
        log.info("userWithDate 参数：{}",param);
        LocalDate date = getDate(param);
        userService.settlement(RangeEnum.DAY.toString(), date, date);
    }
    @XxlJob("userWithWeek")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "用户结算（周）")
    public void withWeek(String param) {
        log.info("userWithWeek 参数：{}",param);
        LocalDate date = getDate(param);
        LocalDate monday = date.minusDays(date.getDayOfWeek().getValue() - 1);
        LocalDate sunday = monday.plusDays(7);
        userService.settlement(RangeEnum.WEEK.toString(), monday, sunday);
    }

    @XxlJob("userWithMonth")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "用户结算（月）")
    public void withMonth(String param) {
        log.info("userWithMonth 参数：{}",param);
        LocalDate date = getDate(param);
        LocalDate firstDay = date.withDayOfMonth(1);
        LocalDate lastDay = firstDay.plusMonths(1).minusDays(1);
        userService.settlement(RangeEnum.MONTH.toString(), firstDay, lastDay);
    }
}
