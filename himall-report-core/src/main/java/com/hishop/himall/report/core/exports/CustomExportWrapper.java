package com.hishop.himall.report.core.exports;

import com.hishop.himall.report.core.exports.models.CustomExportCmd;
import com.hishop.himall.report.core.exports.models.CustomExportVo;
import com.hishop.himall.report.core.service.ReportCustomService;

import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CustomExportWrapper extends PageExportWrapper<CustomExportVo, CustomExportCmd>
{
    @Resource
    private ReportCustomService reportCustomService;

    @Override
    public List<CustomExportVo> getPageList(CustomExportCmd param)
    {
        return reportCustomService.queryExport(param);
    }


}
