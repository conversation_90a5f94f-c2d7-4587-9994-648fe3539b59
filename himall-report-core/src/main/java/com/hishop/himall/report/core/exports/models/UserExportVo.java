package com.hishop.himall.report.core.exports.models;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class UserExportVo {
    @ExcelProperty(value = "用户编号")
    private Integer userId;
    @ExcelProperty(value = "昵称")
    private String nickname;
    @ExcelProperty(value = "手机号")
    private String phone;
    @ExcelProperty(value = "注册时间")
    private LocalDateTime registrationTime;
    @ExcelProperty(value = "下单笔数")
    private Integer orderOrders;
    @ExcelProperty(value = "支付金额")
    private BigDecimal paymentAmount;
    @ExcelProperty(value = "支付笔数")
    private Integer paymentOrders;
    @ExcelProperty(value = "支付件数")
    private Integer paymentQuantity;
    @ExcelProperty(value = "支付订单金额")
    private BigDecimal paymentOrderAmount;
    @ExcelProperty(value = "支付商品金额")
    private BigDecimal paymentProductAmount;
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ExcelProperty(value = "退款订单数")
    private Integer refundOrders;
    @ExcelProperty(value = "消费金额")
    public BigDecimal consumptionAmount;
}
