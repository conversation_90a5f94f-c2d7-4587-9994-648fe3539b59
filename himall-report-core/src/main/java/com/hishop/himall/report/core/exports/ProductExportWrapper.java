package com.hishop.himall.report.core.exports;

import com.hishop.himall.report.api.request.ProductReq;
import com.hishop.himall.report.api.response.ProductResp;
import com.hishop.himall.report.core.exports.models.ProductExportVo;
import com.hishop.himall.report.core.service.ReportProductQueryService;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

@Service
public class ProductExportWrapper extends PageExportWrapper<ProductExportVo, ProductReq>
{
    @Resource
    ReportProductQueryService reportProductQueryService;

    @Override
    public List<ProductExportVo> getPageList(ProductReq param) {
        List<ProductResp> source = reportProductQueryService.getProducts(param).getData();
        List<ProductExportVo> result = new ArrayList<>();
        for (ProductResp item : source) {
            result.add(Map(item));
        }
        return result;
    }
    private ProductExportVo Map(Object obj) {
        try {
            ProductExportVo vo = new ProductExportVo();
            BeanUtils.copyProperties(obj, vo);
            return vo;
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    public String sheetName() {
        return "会员分析";
    }
}
