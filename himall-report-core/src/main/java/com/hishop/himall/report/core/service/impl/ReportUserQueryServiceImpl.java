package com.hishop.himall.report.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.UserReq;
import com.hishop.himall.report.api.response.EchartSeries;
import com.hishop.himall.report.api.response.EchartSeriesPieData;
import com.hishop.himall.report.api.response.Echarts;

import com.hishop.himall.report.api.response.UserNewOldSummaryResp;
import com.hishop.himall.report.api.response.UserResp;
import com.hishop.himall.report.api.response.UserSummaryResp;
import com.hishop.himall.report.dao.domain.ReportShopTrade;
import com.hishop.himall.report.dao.domain.ReportUserTrade;
import com.hishop.himall.report.dao.mapper.ReportShopTradeMapper;
import com.hishop.himall.report.dao.mapper.ReportUserTradeMapper;
import com.hishop.himall.report.dao.models.ProvinceSource;
import com.hishop.himall.report.dao.models.UserTrade;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.web.util.PageUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Service
public class ReportUserQueryServiceImpl implements com.hishop.himall.report.core.service.ReportUserQueryService {

    @Resource
    private ReportUserTradeMapper reportUserTradeMapper;
    @Resource
    private ReportShopTradeMapper reportShopTradeMapper;

    /**
     * 用户概况
     *
     * @param req
     * @return
     */
    @Override
    public UserSummaryResp getUserSummary(ReportReq req) {
        req.getPrev();
        ReportShopTrade trade = reportShopTradeMapper.selectOne(new QueryWrapper<ReportShopTrade>()
                                                                        .eq("`range`", req.getRange().toString())
                                                                        .eq("shop_id", 0)
                                                                        .eq("date", req.getStart()));
        ReportShopTrade prevTrade = reportShopTradeMapper.selectOne(new QueryWrapper<ReportShopTrade>()
                                                                            .eq("`range`",
                                                                                req.getRange().toString())
                                                                            .eq("shop_id", 0)
                                                                            .eq("date", req.getStart()));
        UserSummaryResp resp = getUserSummaryResp(trade);
        resp.setPrev(getUserSummaryResp(prevTrade));
        return resp;
    }

    private UserSummaryResp getUserSummaryResp(ReportShopTrade trade) {
        if (trade == null) {
            return new UserSummaryResp();
        }
        UserSummaryResp resp = new UserSummaryResp();
        resp.setUserTotal(trade.getUserTotal());
        resp.setUserNew(trade.getUserNew());
        resp.setPaymentUsers(trade.getPaymentUsers());
        resp.setOrderUsers(trade.getOrderUsers());
        resp.setCouponUsers(trade.getUserCoupons());
        resp.setUnitPrice(trade.getPaymentUsers() == 0 ? BigDecimal.ZERO
                                  : trade.getPaymentAmount()
                .divide(BigDecimal.valueOf(trade.getPaymentUsers()), 4, RoundingMode.HALF_UP));
        return resp;
    }

    /**
     * 用户新增趋势
     *
     * @param req
     * @return
     */
    @Override
    public Echarts getNewUserEchats(ReportReq req) {
        List<ReportShopTrade> trades = reportShopTradeMapper.selectList(new QueryWrapper<ReportShopTrade>()
                                                                                .eq("`range`",
                                                                                    req.getRange().toString())
                                                                                .eq("shop_id", 0)
                                                                                .between("date", req.getStart(),
                                                                                         req.getEnd()));
        Echarts echarts = new Echarts();
        EchartSeries newUsers = echarts.addSeries("新增会员数");
        return echarts.each(req.getRange().toString(), req.getStart(), req.getEnd(), (date) -> {
            ReportShopTrade current = trades.stream()
                    .filter(item -> item.getDate().equals(date))
                    .findFirst()
                    .orElse(new ReportShopTrade());
            newUsers.add(current.getUserNew());
        });
    }

    /**
     * 用户省份分布
     *
     * @param reportReq
     * @return
     */
    @Override
    public Echarts getProvinceUserEchats(ReportReq reportReq) {
        List<ProvinceSource> provinceUsers = reportUserTradeMapper.getProvinceUsers();
        Echarts echarts = new Echarts();
        EchartSeries series = echarts.addSeries("会员总数");
        for (ProvinceSource provinceSource : provinceUsers) {
            EchartSeriesPieData data = new EchartSeriesPieData();
            data.setName(provinceSource.getProvinceName());
            data.setValue(BigDecimal.valueOf(provinceSource.getUsers()));
            series.getData().add(data);
        }
        return echarts;
    }

    public UserNewOldSummaryResp getNewOldSummary(ReportReq req) {
        ReportShopTrade trade = reportShopTradeMapper.selectOne(new QueryWrapper<ReportShopTrade>()
                                                                        .eq("`range`", req.getRange().toString())
                                                                        .eq("shop_id", 0)
                                                                        .eq("date", req.getStart()));
        if (trade == null)
            return null;
        UserNewOldSummaryResp resp = new UserNewOldSummaryResp();
        resp.setPaymentUsers(trade.getPaymentUsers());
        resp.setPaymentOrders(trade.getPaymentOrders());
        resp.setPaymentAmount(trade.getPaymentAmount());
        resp.setUnitPrice(trade.getPaymentUsers() == 0 ? BigDecimal.ZERO
                                  : trade.getPaymentAmount().divide(BigDecimal.valueOf(trade.getPaymentUsers()), 4,
                                                                    RoundingMode.HALF_UP));
        resp.setPaymentNewUsers(trade.getPaymentNewUsers());
        resp.setPaymentNewOrders(trade.getPaymentNewOrders());
        resp.setPaymentNewAmount(trade.getPaymentNewAmount());
        resp.setNewUnitPrice(trade.getPaymentNewUsers() == 0 ? BigDecimal.ZERO
                                     : trade.getPaymentNewAmount()
                .divide(BigDecimal.valueOf(trade.getPaymentNewUsers()), 4,
                        RoundingMode.HALF_UP));
        resp.setNewProportion(trade.getPaymentUsers() == 0 ? BigDecimal.ZERO
                                      : BigDecimal.valueOf(trade.getPaymentNewUsers())
                .divide(BigDecimal.valueOf(trade.getPaymentUsers()), 4,
                        RoundingMode.HALF_UP));
        resp.setPaymentOldUsers(trade.getPaymentUsers() - trade.getPaymentNewUsers());
        resp.setPaymentOldOrders(trade.getPaymentOrders() - trade.getPaymentNewOrders());
        resp.setPaymentOldAmount(trade.getPaymentAmount().subtract(trade.getPaymentNewAmount()));
        resp.setOldUnitPrice(resp.getPaymentOldUsers() == 0 ? BigDecimal.ZERO
                                     : resp.getPaymentOldAmount()
                .divide(BigDecimal.valueOf(resp.getPaymentOldUsers()), 4,
                        RoundingMode.HALF_UP));
        resp.setOldProportion(trade.getPaymentUsers() == 0 ? BigDecimal.ZERO
                                      : BigDecimal.valueOf(resp.getPaymentOldUsers())
                .divide(BigDecimal.valueOf(trade.getPaymentUsers()), 4,
                        RoundingMode.HALF_UP));
        return resp;

    }

    /**
     * 获取新老会员趋势
     *
     * @param req
     * @return
     */
    @Override
    public Echarts getNewOldUserEcharts(ReportReq req) {
        List<ReportShopTrade> trades = reportShopTradeMapper.selectList(new QueryWrapper<ReportShopTrade>()
                                                                                .eq("`range`",
                                                                                    req.getRange().toString())
                                                                                .eq("shop_id", 0)
                                                                                .between("date", req.getStart(),
                                                                                         req.getEnd()));

        Echarts echarts = new Echarts();
        EchartSeries newPaymentUserSeries = echarts.addSeries("新成交会员数");
        EchartSeries newPaymentOrderSeries = echarts.addSeries("新支付订单数");
        EchartSeries newUnitPriceSeries = echarts.addSeries("新客单价");
        EchartSeries newPaymentAmountSeries = echarts.addSeries("新支付金额");
        EchartSeries oldPaymentUserSeries = echarts.addSeries("老成交会员数");
        EchartSeries oldPaymentOrderSeries = echarts.addSeries("老支付订单数");
        EchartSeries oldUnitPriceSeries = echarts.addSeries("老客单价");
        EchartSeries oldPaymentAmountSeries = echarts.addSeries("老支付金额");

        return echarts.each(req.getRange().toString(), req.getStart(), req.getEnd(), (date) -> {
            ReportShopTrade trade = trades.stream()
                    .filter(item -> item.getDate().equals(date))
                    .findFirst()
                    .orElse(null);
            if (trade == null) {
                newPaymentUserSeries.add(0);
                newPaymentOrderSeries.add(0);
                newPaymentAmountSeries.add(0);
                newUnitPriceSeries.add(0);
                oldPaymentUserSeries.add(0);
                oldPaymentOrderSeries.add(0);
                oldPaymentAmountSeries.add(0);
                oldUnitPriceSeries.add(0);
            }else {
                newPaymentUserSeries.add(trade.getPaymentNewUsers());
                newPaymentOrderSeries.add(trade.getPaymentNewOrders());
                newPaymentAmountSeries.add(trade.getPaymentNewAmount());
                newUnitPriceSeries.add(
                        trade.getPaymentNewUsers() == null || trade.getPaymentNewUsers() == 0 ? BigDecimal.ZERO
                                : trade.getPaymentNewAmount().divide(BigDecimal.valueOf(trade.getPaymentNewUsers()), 4,
                                                                     RoundingMode.HALF_UP));
                int oldUsers = trade.getPaymentUsers() - trade.getPaymentNewUsers();
                BigDecimal oldAmount = trade.getPaymentAmount().subtract(trade.getPaymentNewAmount());
                oldPaymentUserSeries.add(oldUsers);
                oldPaymentOrderSeries.add(trade.getPaymentOrders() - trade.getPaymentNewOrders());
                oldPaymentAmountSeries.add(oldAmount);
                oldUnitPriceSeries.add(oldUsers == 0 ? BigDecimal.ZERO
                                               : oldAmount.divide(BigDecimal.valueOf(oldUsers), 4,
                                                                  RoundingMode.HALF_UP));
            }
        });
    }

    /**
     * 获取用户列表
     *
     * @param req
     * @return
     */
    @Override
    public PageResult<UserResp> getUsers(UserReq req) {
        Page<UserTrade> source = reportUserTradeMapper.getUsers(Page.of(req.getPageNo(), req.getPageSize(), true),
                                                                req.getRange().toString(),
                                                                req.getStart(), req.getEnd());
        return PageUtil.buildPage(source, UserResp.class);
    }

    /**
     * 导出用户报表
     *
     * @param userReq
     */
    @Override
    public void exportUsers(UserReq userReq) {
        return;
    }
}
