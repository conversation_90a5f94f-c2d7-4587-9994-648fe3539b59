package com.hishop.himall.report.core.service;

import com.hishop.himall.report.api.request.BatchReportSourceCartReq;
import com.hishop.himall.report.api.request.BatchReportSourceCouponReq;
import com.hishop.himall.report.api.request.BatchReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.BatchReportSourceFollowShopReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderBillReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderItemReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderReq;
import com.hishop.himall.report.api.request.BatchReportSourceProductReq;
import com.hishop.himall.report.api.request.BatchReportSourceRefundReq;
import com.hishop.himall.report.api.request.BatchReportSourceRegionReq;
import com.hishop.himall.report.api.request.BatchReportSourceShopReq;
import com.hishop.himall.report.api.request.BatchReportSourceUserReq;
import com.hishop.himall.report.api.request.BatchReportSourceVisitReq;
import com.hishop.himall.report.api.request.CartReq;
import com.hishop.himall.report.api.request.ReportSourceCouponReq;
import com.hishop.himall.report.api.request.ReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.ReportSourceFollowShopReq;
import com.hishop.himall.report.api.request.ReportSourceOrderBillReq;
import com.hishop.himall.report.api.request.ReportSourceOrderItemReq;
import com.hishop.himall.report.api.request.ReportSourceOrderReq;
import com.hishop.himall.report.api.request.ReportSourceProductReq;
import com.hishop.himall.report.api.request.ReportSourceRefundReq;
import com.hishop.himall.report.api.request.ReportSourceRegionReq;
import com.hishop.himall.report.api.request.ReportSourceShopReq;
import com.hishop.himall.report.api.request.ReportSourceUserReq;
import com.hishop.himall.report.api.request.VisitsReq;

import javax.validation.constraints.NotNull;

public interface ReportService {

    /**
     * 商品加购记录
     *
     * @param req
     */
    void createCart(CartReq req);

    /**
     * 商品加购记录
     *
     * @param req
     */
    void batchCreateCart(@NotNull BatchReportSourceCartReq req);

    void updateCart(CartReq req);

    void createCoupon(ReportSourceCouponReq req);

    void batchCreateSourceCoupon(BatchReportSourceCouponReq req);


    void updateCoupon(ReportSourceCouponReq req);

    /**
     * 访问记录
     *
     * @param req
     */

    void updateVisit(VisitsReq req);

    void createFollowProductReq(ReportSourceFollowProductReq req);

    void batchCreateSourceFollowProduct(BatchReportSourceFollowProductReq req);

    void updateFollowProductReq(ReportSourceFollowProductReq req);

    void createSourceFollowShop(ReportSourceFollowShopReq req);

    void batchCreateSourceFollowShop(BatchReportSourceFollowShopReq req);

    void updateSourceFollowShop(ReportSourceFollowShopReq req);

    void createSourceOrder(ReportSourceOrderReq req);

    void batchCreateSourceOrder(@NotNull BatchReportSourceOrderReq req);

    void updateSourceOrder(ReportSourceOrderReq req);

    void createSourceOrderItem(ReportSourceOrderItemReq req);

    void batchCreateSourceOrderItem(BatchReportSourceOrderItemReq req);

    void updateSourceOrderItem(ReportSourceOrderItemReq req);

    void createSourceProduct(ReportSourceProductReq req);

    void batchCreateSourceProduct(BatchReportSourceProductReq req);

    void updateSourceProduct(ReportSourceProductReq req);

    void createSourceRefund(ReportSourceRefundReq req);

    void batchCreateSourceRefund(BatchReportSourceRefundReq req);

    void updateSourceRefund(ReportSourceRefundReq req);

    void createSourceShop(ReportSourceShopReq req);

    void batchCreateSourceShop(BatchReportSourceShopReq req);


    void updateSourceShop(ReportSourceShopReq req);

    void createSourceUser(ReportSourceUserReq req);

    void batchCreateSourceUser(BatchReportSourceUserReq req);

    void updateSourceUser(ReportSourceUserReq req);

    void createSourceRegion(ReportSourceRegionReq req);

    void batchCreateSourceRegion(BatchReportSourceRegionReq req);

    void createSourceOrderBill(ReportSourceOrderBillReq req);

    void batchCreateSourceOrderBill(BatchReportSourceOrderBillReq req);

    /**
     * 访问记录
     *
     * @param req
     */
    void createVisit(VisitsReq req);

    /**
     * 访问记录
     *
     * @param req
     */
    void batchCreateSourceVisit(BatchReportSourceVisitReq req);

}
