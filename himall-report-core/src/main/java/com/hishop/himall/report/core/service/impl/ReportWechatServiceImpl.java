package com.hishop.himall.report.core.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaAnalysisService;
import cn.binarywang.wx.miniapp.api.WxMaService;

import cn.binarywang.wx.miniapp.bean.analysis.WxMaSummaryTrend;
import cn.binarywang.wx.miniapp.bean.analysis.WxMaUserPortrait;
import cn.binarywang.wx.miniapp.bean.analysis.WxMaVisitDistribution;
import cn.binarywang.wx.miniapp.bean.analysis.WxMaVisitTrend;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.response.EchartSeries;
import com.hishop.himall.report.api.response.EchartSeriesPieData;
import com.hishop.himall.report.api.response.Echarts;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;

import com.hishop.himall.report.dao.domain.ReportWechatPortrait;
import com.hishop.himall.report.dao.domain.ReportWechatVisit;
import com.hishop.himall.report.dao.enums.WechatPortraitGroup;
import com.hishop.himall.report.dao.mapper.ReportWechatPortraitMapper;
import com.hishop.himall.report.dao.mapper.ReportWechatVisitMapper;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch;

@Service
public class ReportWechatServiceImpl implements com.hishop.himall.report.core.service.ReportWechatService {

    @Resource
    private WxMaService wechatService;
    @Resource
    private ReportWechatPortraitMapper reportWechatPortraitMapper;
    @Resource
    private ReportWechatVisitMapper reportWechatVisitMapper;

    public void getherPortrait(String range, Date start, Date end) {
        try {
            WxMaAnalysisService analysisService = wechatService.getAnalysisService();
            List<ReportWechatPortrait> portraits = new ArrayList<>();
            WxMaUserPortrait userPortrait = analysisService.getUserPortrait(start, end);
            WxMaUserPortrait.Item visitUv = userPortrait.getVisitUv();
            fillPortraits(portraits, visitUv);
            WxMaUserPortrait.Item visitUvNew = userPortrait.getVisitUvNew();
            fillPortraits(portraits, visitUvNew);

            WxMaVisitDistribution visitDistribution = analysisService.getVisitDistribution(start, end);
            Map<String, Map<Integer, Integer>> list = visitDistribution.getList();
            fillVisitDistribution(portraits, WechatPortraitGroup.StayTime, list.get("access_staytime_info"));
            fillVisitDistribution(portraits, WechatPortraitGroup.Depth, list.get("access_depth_info"));
            savePortrait(range, end, portraits);

        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    public void gatherVisits(String range, Date start, Date end) {
        try {
            WxMaAnalysisService analysisService = wechatService.getAnalysisService();
            List<ReportWechatVisit> visits = new ArrayList<>();
            List<WxMaVisitTrend> dailyVisitTrends = analysisService.getDailyVisitTrend(start, end);
            List<WxMaSummaryTrend> dailySummaryTrends = analysisService.getDailySummaryTrend(start, end);
            for (int i = 0; i < dailyVisitTrends.size(); i++) {
                WxMaVisitTrend trend = dailyVisitTrends.get(i);
                WxMaSummaryTrend summary = dailySummaryTrends.get(i);
                ReportWechatVisit visit = getReportWechatVisit(summary, trend);
                visits.add(visit);
            }
            saveVisits(range, end, visits);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private void saveVisits(String range, Date date, List<ReportWechatVisit> visits) {
        reportWechatVisitMapper.delete(new QueryWrapper<ReportWechatVisit>()
                                               .eq("range", range)
                                               .eq("date", date));
        saveBatch(visits);
    }

    private void savePortrait(String range, Date date, List<ReportWechatPortrait> portraits) {
        reportWechatPortraitMapper.delete(new QueryWrapper<ReportWechatPortrait>()
                                                  .eq("range", range)
                                                  .eq("date", date));
        saveBatch(portraits);
    }

    private void fillPortraits(List<ReportWechatPortrait> portraits, WxMaUserPortrait.Item visitUv) {
        fillReportWechatVisit(portraits, WechatPortraitGroup.Province, visitUv.getProvince());
        fillReportWechatVisit(portraits, WechatPortraitGroup.City, visitUv.getCity());
        fillReportWechatVisit(portraits, WechatPortraitGroup.Genders, visitUv.getGenders());
        fillReportWechatVisit(portraits, WechatPortraitGroup.Platforms, visitUv.getPlatforms());
        fillReportWechatVisit(portraits, WechatPortraitGroup.Devices, visitUv.getDevices());
        fillReportWechatVisit(portraits, WechatPortraitGroup.Ages, visitUv.getAges());
    }

    private void fillVisitDistribution(List<ReportWechatPortrait> portraits,
                                       WechatPortraitGroup group, Map<Integer, Integer> items) {
        if (group == WechatPortraitGroup.StayTime) {
            Map<Integer, String> stayTime = new HashMap<>();
            stayTime.put(1, "0-2s");
            stayTime.put(2, "3-5s");
            stayTime.put(3, "6-10s");
            stayTime.put(4, "11-20s");
            stayTime.put(5, "20-30s");
            stayTime.put(6, "30-50s");
            stayTime.put(7, "50-100s");
            stayTime.put(8, ">100s");
            setPortraitGroup(portraits, group, items, stayTime);
        } else if (group == WechatPortraitGroup.Depth) {
            Map<Integer, String> depth = new HashMap<>();
            depth.put(1, "1 页");
            depth.put(2, "2 页");
            depth.put(3, "3 页");
            depth.put(4, "4 页");
            depth.put(5, "5 页");
            depth.put(6, "6-10 页");
            depth.put(7, ">10 页");
            setPortraitGroup(portraits, group, items, depth);
        }
    }

    private void setPortraitGroup(List<ReportWechatPortrait> portraits, WechatPortraitGroup group,
                                  Map<Integer, Integer> items, Map<Integer, String> depth) {
        for (Map.Entry<Integer, Integer> entry : items.entrySet()) {
            ReportWechatPortrait portrait = new ReportWechatPortrait();
            portrait.setGroup(group.getCode());
            portrait.setName(depth.get(entry.getKey()));
            portrait.setValue(BigDecimal.valueOf(entry.getValue()));
            portraits.add(portrait);
        }
    }

    private void fillReportWechatVisit(List<ReportWechatPortrait> portraits,
                                       WechatPortraitGroup group, Map<String, Long> items) {
        for (Map.Entry<String, Long> entry : items.entrySet()) {
            Optional<ReportWechatPortrait> exist = portraits.stream()
                    .filter(p -> p.getGroup().equals(group.getCode()) && p.getName().equals(entry.getKey()))
                    .findFirst();
            if (exist.isPresent()) {
                ReportWechatPortrait portrait = exist.get();
                portrait.setValue(portrait.getValue().add(BigDecimal.valueOf(entry.getValue())));
            } else {
                ReportWechatPortrait portrait = new ReportWechatPortrait();
                portrait.setGroup(group.getCode());
                portrait.setName(entry.getKey());
                portrait.setValue(BigDecimal.valueOf(entry.getValue()));
                portraits.add(portrait);
            }
        }
    }

    private static ReportWechatVisit getReportWechatVisit(WxMaSummaryTrend summary, WxMaVisitTrend trend)
            throws ParseException {
        ReportWechatVisit visit = new ReportWechatVisit();
        visit.setDate(DateFormat.getInstance().parse(trend.getRefDate()));
        visit.setVisitors(Math.toIntExact(summary.getVisitTotal()));
        visit.setSharePv(Math.toIntExact(summary.getSharePv()));
        visit.setShareUv(Math.toIntExact(summary.getShareUv()));
        visit.setSessionCount(Math.toIntExact(trend.getSessionCnt()));
        visit.setStayTimeSession(trend.getStayTimeSession());
        visit.setStayTimeUv(trend.getStayTimeUv());
        visit.setVisitDepth(trend.getVisitDepth());
        visit.setVisitPv(Math.toIntExact(trend.getVisitPv()));
        visit.setVisitUv(Math.toIntExact(trend.getVisitUv()));
        visit.setVisitUvNew(Math.toIntExact(trend.getVisitUvNew()));
        return visit;
    }

    /**
     * 访问趋势图
     *
     * @param req
     * @return
     */
    @Override public Echarts getVisitsEcharts(ReportReq req) {
        List<ReportWechatVisit> visits = reportWechatVisitMapper.selectList(new QueryWrapper<ReportWechatVisit>()
                                                                                    .eq("`range`", req.getRange())
                                                                                    .between("date", req.getStart(),
                                                                                             req.getEnd()));
        Echarts echarts = new Echarts();
        EchartSeries visitors = echarts.addSeries("总访问人数");
        EchartSeries sessionCount = echarts.addSeries("打开次数");
        EchartSeries visitPv = echarts.addSeries("访问次数");
        EchartSeries visitUv = echarts.addSeries("访问人数");
        EchartSeries visitUvNew = echarts.addSeries("新用户数");
        EchartSeries stayTimeUv = echarts.addSeries("人均停留时长");
        EchartSeries stayTimeSession = echarts.addSeries("次均停留时长");
        EchartSeries visitDepth = echarts.addSeries("访问深度");
        EchartSeries sharePv = echarts.addSeries("转发次数");
        EchartSeries shareUv = echarts.addSeries("转发人数");

        return echarts.each(req.getRange().toString(), req.getStart(), req.getEnd(), date -> {
            ReportWechatVisit current = visits.stream()
                    .filter(item -> item.getDate().equals(date))
                    .findFirst()
                    .orElse(new ReportWechatVisit());

            visitors.add(current.getVisitors());
            sessionCount.add(current.getSessionCount());
            visitPv.add(current.getVisitPv());
            visitUv.add(current.getVisitUv());
            visitUvNew.add(current.getVisitUvNew());
            stayTimeUv.add(current.getStayTimeUv());
            stayTimeSession.add(current.getStayTimeSession());
            visitDepth.add(current.getVisitDepth());
            sharePv.add(current.getSharePv());
            shareUv.add(current.getShareUv());
        });
    }

    private void fillPortraitPieSeries(Echarts echarts, WechatPortraitGroup group, List<ReportWechatPortrait> source) {
        EchartSeries series = echarts.addSeries(group.getDesc());
        source.forEach(item -> {
            EchartSeriesPieData data = new EchartSeriesPieData();
            data.setName(item.getName());
            data.setValue(item.getValue());
            series.getData().add(data);
        });
    }

    /**
     * 用户画像饼图
     *
     * @param req
     * @return
     */
    @Override public Echarts getPortraitEcharts(ReportReq req) {
        List<ReportWechatPortrait> portraits = reportWechatPortraitMapper.selectList(
                new QueryWrapper<ReportWechatPortrait>()
                        .eq("`range`", req.getRange())
                        .between("date", req.getStart(), req.getEnd()));
        Map<Integer, List<ReportWechatPortrait>> groups = portraits.stream()
                .collect(Collectors.groupingBy(ReportWechatPortrait::getGroup));
        Echarts echarts = new Echarts();
        for (WechatPortraitGroup group : WechatPortraitGroup.values()) {
            List<ReportWechatPortrait> items = groups.get(group.getCode());
            if(items!=null)
                fillPortraitPieSeries(echarts, group, items);
        }
        return echarts;
    }
}
