package com.hishop.himall.report.core.exports.models;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductExportVo {
    @ExcelProperty(value = "商品编号")
    private Integer productId;
    @ExcelProperty(value = "商品名称")
    private String productName;
    @ExcelProperty(value = "商品主图")
    private String thumbnailUrl;
    @ExcelProperty(value = "加购人数")
    private Integer cartUsers;
    @ExcelProperty(value = "加购件数")
    private Integer cartQuantity;
    @ExcelProperty(value = "下单人数")
    private Integer orderUsers;
    @ExcelProperty(value = "下单笔数")
    private Integer orderOrders;
    @ExcelProperty(value = "下单件数")
    private Integer orderQuantity;
    @ExcelProperty(value = "下单金额")
    private BigDecimal orderAmount;
    @ExcelProperty(value = "支付人数")
    private Integer paymentUsers;
    @ExcelProperty(value = "支付笔数")
    private Integer paymentOrders;
    @ExcelProperty(value = "支付件数")
    private Integer paymentQuantity;
    @ExcelProperty(value = "支付金额")
    private BigDecimal paymentAmount;
    @ExcelProperty(value = "申请售后订单数")
    private Integer applyOrders;
    @ExcelProperty(value = "申请售后人数")
    private Integer applyUsers;
    @ExcelProperty(value = "退款订单数")
    private Integer refundOrders;
    @ExcelProperty(value = "退款人数")
    private Integer refundUsers;
    @ExcelProperty(value = "退款件数")
    private Integer refundQuantity;
    @ExcelProperty(value = "退款金额")
    private Integer refundAmount;
    @ExcelProperty(value = "退款率")
    public BigDecimal refundRate;
    @ExcelProperty(value = "访客数")
    private Integer visitsUsers;
    @ExcelProperty(value = "访问量")
    private Integer visitsCount;
    @ExcelProperty(value = "关注人数")
    private Integer followUsers;

}
