package com.hishop.himall.report.core.task;

import com.hishop.himall.report.api.enums.RangeEnum;
import com.hishop.himall.report.core.service.ReportProductService;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
@Slf4j
@Component
public class ProductTask {
    @Resource
    private ReportProductService reportProductService;

    private LocalDate getDate(String param) {
        return param == null ? LocalDate.now().minusDays(1) : LocalDate.parse(param);
    }

    @XxlJob("productWithDate")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "结算商品")
    public void withDate(String param) {
        log.info("productWithDate参数：{}",param);
        LocalDate date = getDate(param);
        reportProductService.settlement(RangeEnum.DAY.toString(), date, date,null);
    }

    @XxlJob("productWithWeek")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "结算商品（周）")
    public void withWeek(String param) {
        log.info("productWithWeek参数：{}",param);
        LocalDate date = getDate(param);
        LocalDate monday = date.minusDays(date.getDayOfWeek().getValue() - 1);
        LocalDate sunday = monday.plusDays(7);
        reportProductService.settlement(RangeEnum.WEEK.toString(), monday, sunday,null);
    }

    @XxlJob("productWithMonth")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "结算商品（月）")
    public void withMonth(String param) {
        log.info("productWithMonth参数：{}",param);
        LocalDate date = getDate(param);
        LocalDate firstDay = date.withDayOfMonth(1);
        LocalDate lastDay = firstDay.plusMonths(1).minusDays(1);
        reportProductService.settlement(RangeEnum.MONTH.toString(), firstDay, lastDay,null);
    }
}
