package com.hishop.himall.report.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hishop.himall.report.api.enums.Platform;
import com.hishop.himall.report.dao.domain.ReportShopTrade;
import com.hishop.himall.report.dao.domain.ReportSourceShop;
import com.hishop.himall.report.dao.domain.ReportSourceUser;
import com.hishop.himall.report.dao.mapper.ReportSourceCartMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceCouponMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceOrderItemMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceOrderMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceRefundMapper;
import com.hishop.himall.report.dao.mapper.ReportShopTradeMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceShopMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceUserMapper;
import com.hishop.himall.report.dao.mapper.ReportSourceVisitMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch;

@Service
public class ReportShopServiceImpl implements com.hishop.himall.report.core.service.ReportShopService {
    @Resource
    private ReportSourceOrderMapper reportSourceOrderMapper;
    @Resource
    private ReportSourceOrderItemMapper reportSourceOrderItemMapper;
    @Resource
    private ReportSourceRefundMapper reportSourceRefundMapper;
    @Resource
    private ReportShopTradeMapper reportShopTradeMapper;
    @Resource
    private ReportSourceVisitMapper reportSourceVisitMapper;
    @Resource
    private ReportSourceShopMapper reportSourceShopMapper;
    @Resource
    private ReportSourceUserMapper reportSourceUserMapper;
    @Resource
    private ReportSourceCartMapper reportSourceCartMapper;
    @Resource
    private ReportSourceCouponMapper reportSourceCouponMapper;

    @Override public void settlement(String range, LocalDate start, LocalDate end, List<Long> shops) {
        settlement(range, start, end, null, shops);
    }

    @Override public void settlement(String range, LocalDate start, LocalDate end, Integer platform, List<Long> shops) {
        List<ReportShopTrade> trades = new ArrayList<>();
        // 加购-领券
        fillCartCoupon(trades, start, end, platform, shops);
        // 访问统计
        fillVisits(trades, start, end, platform, shops);
        // 下单统计
        fillOrder(trades, start, end, platform, shops);
        // 订单支付统计
        fillPayment(trades, start, end, platform, shops);
        // 订单项支付统计
        fillPaymentItem(trades, start, end, platform, shops);
        // 包裹统计
        if (platform == null)//不统计渠道数据
        {
            // 售后统计
            fillRefund(trades, start, end, shops);
            // 会员统计
            fillUsers(trades, start, end);
            // 包裹统计
            fillPackage(trades, start, end,shops);
        }
        // 保存
        batchSave(range, start, end, trades);
    }

    private void fillCartCoupon(List<ReportShopTrade> trades, LocalDate start, LocalDate end, Integer platform,
                                List<Long> shops) {
        reportSourceCartMapper.getShopCartSummary(start, end, platform, shops, true).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setCartUsers(summary.getUsers());
        });
        reportSourceCartMapper.getShopCartSummary(start, end, platform, shops, false).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setCartUsers(summary.getUsers());
        });

        if (platform == null) {//仅在全平台统计时统计
            reportSourceCouponMapper.getShopCouponSummary(start, end, shops, true).forEach(summary -> {
                ReportShopTrade trade = Find(trades, summary.getShopId(), null);
                trade.setUserCoupons(summary.getUsers());
            });
            reportSourceCouponMapper.getShopCouponSummary(start, end, shops, false).forEach(summary -> {
                ReportShopTrade trade = Find(trades, summary.getShopId(), null);
                trade.setUserCoupons(summary.getUsers());
            });
        }
    }

    private void fillUsers(List<ReportShopTrade> trades, LocalDate start, LocalDate end) {
        QueryWrapper<ReportSourceUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.le("registration_time", end);
        Long total = reportSourceUserMapper.selectCount(queryWrapper);
        queryWrapper.ge("registration_time", start);
        Long newUsers = reportSourceUserMapper.selectCount(queryWrapper);
        ReportShopTrade trade = Find(trades, 0L, null);
        trade.setUserTotal(Math.toIntExact(total));
        trade.setUserNew(Math.toIntExact(newUsers));
    }

    private void fillPackage(List<ReportShopTrade> trades, LocalDate start, LocalDate end,List<Long> shops) {
        // 发货包裹统计
        reportSourceOrderItemMapper.getShopDeliverPackageSummary(start, end,shops).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), null);
            trade.setPackageDelivery(summary.getPackages());
            trade.setPackageDeliverySecond(summary.getDuration());
        });
        // 签收包裹统计
        reportSourceOrderItemMapper.getShopFinishPackageSummary(start, end,shops).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), null);
            trade.setPackageFinish(summary.getPackages());
            trade.setPackageFinishSecond(summary.getDuration());
        });
    }

    private void fillOrder(List<ReportShopTrade> trades, LocalDate start, LocalDate end, Integer platform,List<Long> shops) {
        reportSourceOrderMapper.getShopOrderSummary(start, end, platform,null,false).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setOrderUsers(summary.getUsers());
            trade.setOrderOrders(summary.getOrders());
            trade.setOrderAmount(summary.getAmount());
        });
        reportSourceOrderMapper.getShopOrderSummary(start, end, platform,null,true).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setOrderUsers(summary.getUsers());
            trade.setOrderOrders(summary.getOrders());
            trade.setOrderAmount(summary.getAmount());
        });
    }

    private void fillVisits(List<ReportShopTrade> trades, LocalDate start, LocalDate end, Integer platform,
                            List<Long> shops) {
        reportSourceVisitMapper.getShopVisitSummary(start, end, shops, platform).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setVisitsCount(summary.getQuantity());
            trade.setVisitsUsers(summary.getUsers());
        });
    }

    private void fillPayment(List<ReportShopTrade> trades, LocalDate start, LocalDate end, Integer platform,
                             List<Long> shops) {
        reportSourceOrderMapper.getShopPaymentSummary(start, end, platform, shops, true, false).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setPaymentUsers(summary.getUsers());
            trade.setPaymentOrders(summary.getOrders());
            trade.setPaymentAmount(summary.getAmount());
        });
        reportSourceOrderMapper.getShopPaymentSummary(start, end, platform, shops, true, true).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setPaymentNewAmount(summary.getAmount());
            trade.setPaymentNewUsers(summary.getUsers());
            trade.setPaymentNewOrders(summary.getOrders());
        });
        reportSourceOrderMapper.getShopPaymentSummary(start, end, platform, shops, false, false).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setPaymentUsers(summary.getUsers());
            trade.setPaymentOrders(summary.getOrders());
            trade.setPaymentAmount(summary.getAmount());
        });
        reportSourceOrderMapper.getShopPaymentSummary(start, end, platform, shops, false, true).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setPaymentNewAmount(summary.getAmount());
            trade.setPaymentNewUsers(summary.getUsers());
            trade.setPaymentNewOrders(summary.getOrders());
        });
    }

    private void fillPaymentItem(List<ReportShopTrade> trades, LocalDate start, LocalDate end, Integer platform,
                                 List<Long> shops) {
        reportSourceOrderItemMapper.getShopPaymentSummary(start, end, platform, shops, true).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setPaymentQuantity(summary.getQuantity());
            trade.setPaymentProductAmount(summary.getAmount());
        });
        reportSourceOrderItemMapper.getShopPaymentSummary(start, end, platform, shops, true).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), platform);
            trade.setPaymentQuantity(summary.getQuantity());
            trade.setPaymentProductAmount(summary.getAmount());
        });
    }

    private void fillRefund(List<ReportShopTrade> trades, LocalDate start, LocalDate end, List<Long> shops) {
        reportSourceRefundMapper.getShopRefundSummary(start, end, shops).forEach(summary -> {
            ReportShopTrade trade = Find(trades, summary.getShopId(), null);
            trade.setRefundOrders(summary.getOrders());
            trade.setRefundAmount(summary.getAmount());
            trade.setRefundQuantity(summary.getQuantity());
        });
    }

    private ReportShopTrade Find(List<ReportShopTrade> trades, Long shopId, Integer platform) {
        return trades.stream().filter(t -> t.getShopId().equals(shopId)).findFirst().orElseGet(() -> {
            ReportShopTrade trade = new ReportShopTrade();
            trade.setShopId(shopId);
            trade.setPlatform(platform);
            trades.add(trade);
            return trade;
        });
    }

    private void batchSave(String range, LocalDate start, LocalDate end, List<ReportShopTrade> trades) {
        // 获取门店ID
        List<Long> shop_list = trades.stream()
                .map(ReportShopTrade::getShopId)
                .collect(Collectors.toList());
        List<ReportSourceShop> shops = reportSourceShopMapper.selectBatchIds(shop_list);
        trades.forEach(trade -> {
            shops.stream()
                    .filter(s -> s.getShopId().equals(trade.getShopId()))
                    .findFirst()
                    .ifPresent(s -> trade.setProvinceId(s.getProvinceId()));
            trade.setDate(start);
            trade.setRange(range);
        });

        // 删除已存在
        QueryWrapper<ReportShopTrade> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("`range`", range);
        queryWrapper.eq("date", start);
        reportShopTradeMapper.delete(queryWrapper);

        // 批量新增
        saveBatch(trades);
    }
}
