package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.api.request.CartReq;
import com.hishop.himall.report.api.request.VisitsReq;
import com.hishop.himall.report.core.service.ReportService;
import com.hishop.starter.util.model.ResultInfo;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import org.bouncycastle.cert.ocsp.Req;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("report/log")
public class LogController  {
    @Resource
    private ReportService reportService;

    /**
     * 访问记录
     *
     * @return
     */
    @PostMapping(value = "visits", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> visits(@RequestBody VisitsReq visitsReq) {
        return ThriftResponseHelper.responseInvoke("visits", visitsReq, req -> {
            reportService.createVisit(req);
            return new BaseResp();
        });
    }

    /**
     * 商品加购记录
     *
     * @return
     */
    @PostMapping(value = "cart", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> cart(@RequestBody CartReq cartReq) {
        return ThriftResponseHelper.responseInvoke("cart", cartReq, req -> {
            reportService.createCart(req);
            return new BaseResp();
        });
    }
}
