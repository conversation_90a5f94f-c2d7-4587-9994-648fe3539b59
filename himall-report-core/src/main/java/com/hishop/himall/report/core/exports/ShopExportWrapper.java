package com.hishop.himall.report.core.exports;

import com.hishop.himall.report.api.request.ShopReq;
import com.hishop.himall.report.api.response.ShopReportResp;
import com.hishop.himall.report.core.exports.models.ShopExportVo;
import com.hishop.himall.report.core.service.ReportShopQueryService;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

@Service
public class ShopExportWrapper extends PageExportWrapper<ShopExportVo, ShopReq>
{
    @Resource
    ReportShopQueryService reportUserQueryService;

    @Override
    public List<ShopExportVo> getPageList(ShopReq param) {
        List<ShopReportResp> source = reportUserQueryService.getShopTrade(param).getData();
        List<ShopExportVo> result = new ArrayList<>();
        for (ShopReportResp item : source) {
            result.add(Map(item));
        }
        return result;
    }

    private ShopExportVo Map(Object obj) {
        try {
            ShopExportVo vo = new ShopExportVo();
            BeanUtils.copyProperties(obj, vo);
            return vo;
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String sheetName() {
        return "会员分析";
    }
}
