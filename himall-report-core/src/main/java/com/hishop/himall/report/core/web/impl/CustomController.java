package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.api.request.*;
import com.hishop.himall.report.api.response.CustomPageResp;
import com.hishop.himall.report.api.response.CustomProcessResp;
import com.hishop.himall.report.api.response.CustomRecordPageResp;
import com.hishop.himall.report.api.response.CustomResp;
import com.hishop.himall.report.api.service.ReportCustomFeign;
import com.hishop.himall.report.core.exports.models.CustomExportVo;
import com.hishop.himall.report.core.service.ReportCustomService;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/report/custom")
public class CustomController implements ReportCustomFeign {

    @Resource
    private ReportCustomService reportCustomService;

    /**
     * 自定义报表
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BasePageResp<CustomPageResp>> query(@RequestBody CustomPageReq req) {
        return ResultDto.newWithData(reportCustomService.query(req));
    }

    /**
     * 获取报表详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/get/{id}")
    public ResultDto<CustomResp> get(@PathVariable Long id) {
        return ResultDto.newWithData(reportCustomService.getCustom(id));
    }

    /**
     * 删除报表
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "/delete/{id}")
    public ResultDto<Object> delete(@PathVariable Long id) {
        reportCustomService.delete(id);
        return ResultDto.newWithData(null);
    }

    /**
     * 导出报表
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/export")
    public ResultDto<Long> export(@RequestBody ReportQueryReq req) {
        Long id = reportCustomService.export(req.getReportId(), req.getOperatorId(), req.getOperatorName());
        return ResultDto.newWithData(id);
    }

    /**
     * 保存报表
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Object> save(@RequestBody CustomReq req) {
        reportCustomService.save(req);
        return ResultDto.newWithData(null);
    }

    @PostMapping(value = "/record/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BasePageResp<CustomRecordPageResp>> query(@RequestBody CustomRecordPageReq req) {
        return ResultDto.newWithData(reportCustomService.queryRecords(req));
    }

    @PostMapping(value = "/process", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<List<CustomProcessResp>> process(@RequestBody @Valid CustomProcessReq req) {
        List<CustomExportVo> exportList = reportCustomService.process(req.getId());
        return ResultDto.newWithData(JsonUtil.copyList(exportList, CustomProcessResp.class));
    }

    @GetMapping(value = "/getByRecordId")
    public ResultDto<CustomResp> getByRecordId(@RequestParam("id") Long id) {
        return ResultDto.newWithData(reportCustomService.getByRecordId(id));
    }
}
