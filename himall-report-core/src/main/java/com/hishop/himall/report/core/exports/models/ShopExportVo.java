package com.hishop.himall.report.core.exports.models;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class ShopExportVo {
    @ExcelProperty(value = "门店编号")
    private Integer shopId;
    @ExcelProperty(value = "门店名称")
    private String shopName;
    @ExcelProperty(value = "支付金额")
    private BigDecimal paymentAmount;
    @ExcelProperty(value = "支付笔数")
    private Integer paymentOrders;
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ExcelProperty(value = "退款订单数")
    private Integer refundOrders;
    @ExcelProperty(value = "访客数")
    private Integer visitsUsers;
    @ExcelProperty(value = "访问量")
    private Integer visitsCount;
}
