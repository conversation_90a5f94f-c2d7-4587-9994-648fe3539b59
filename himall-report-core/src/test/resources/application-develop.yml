logging:
  level:
    org.apache.rocketmq: debug
    com.baomidou.mybatisplus: debug
    org.springframework.web.filter: debug
    com.hishop.starter: debug
    com.hishop.himall.report: debug

spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ******************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
        slave:
          url: ******************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1


mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.hishop.himall.report.dao
  global-config:
    banner: true
  check-config-location: true

hishop:
  web:
    request:
      log:
        enabled: true
        log-level: debug
        show-payload: true
        payload-size: 1KB
