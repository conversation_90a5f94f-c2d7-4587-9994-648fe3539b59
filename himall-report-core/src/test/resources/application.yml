# web服务端口号
server:
  port: 8080

spring:
  application:
    name: himall-report
  profiles:
    active: develop
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  redis:
    database: 1
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:124.71.221.117:8848}
      username: dev
      password: dev@hishop
      config:
        namespace: ${spring.profiles.active}
        group: 1.0.0
      discovery:
        namespace: ${spring.profiles.active}
        group: 1.0.0
  config:
    import:
      - optional:nacos:himall-common.yml
      - optional:nacos:${spring.application.name}.yml



