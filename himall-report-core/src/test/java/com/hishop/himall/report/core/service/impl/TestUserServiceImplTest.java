package com.hishop.himall.report.core.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.hishop.himall.report.StarterAppBaseTest;
import com.hishop.himall.report.api.demo.request.TestUserQueryPageReq;
import com.hishop.himall.report.api.demo.response.TestUserQueryResp;
import com.hishop.himall.report.core.service.TestUserService;
import com.hishop.starter.util.model.PageResult;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
class TestUserServiceImplTest extends StarterAppBaseTest {

    @Resource
    private TestUserService testUserService;

    @Test
    void queryUser() {

    }

    @Test
    void queryPage() {
//        TestUserQueryPageReq req = new TestUserQueryPageReq();
//        req.setPageNo(1L);
//        req.setPageSize(10L);
//        req.setId(10L);
//        PageResult<TestUserQueryResp> pageResult = testUserService.queryPage(req);
//        assertNotNull(pageResult, "page 返回null");
    }
}