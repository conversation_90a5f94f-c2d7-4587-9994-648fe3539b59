package com.hishop.himall.report.core.web.impl;

import com.hishop.himall.report.StarterAppTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import javax.annotation.Resource;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 */
class TestUserQueryControllerTest extends StarterAppTest {

    private static final Logger log = LoggerFactory.getLogger(TestUserQueryControllerTest.class);
    @Resource
    private MockMvc mockMvc;

    @DisplayName("testUserQuery-query")
    @ParameterizedTest(name = "[{index}] {0}")
    @CsvSource({
            "'参数校验id不能为null','{\"id\": null, \"name\": \"张三\"}',-1",
            "'业务必须大于0异常','{\"id\": 0, \"name\": \"张三\"}',-1",
            "'正常','{\"id\": 2, \"name\": \"张三\"}',0",
    })
    void query(String desc, String req, Integer expectedCode) throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/testuser/query")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(req))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(expectedCode))
                .andDo(print());
    }

    @DisplayName("testUserQuery-page")
    @ParameterizedTest(name = "[{index}] {0}")
    @CsvSource({
            "'正常请求','{\"pageNo\": 1, \"pageSize\": 10, \"id\": 10, \"name\": \"张三\"}',0",
            "'参数校验页码为0','{\"pageNo\": 0, \"pageSize\": 10, \"id\": 10, \"name\": \"张三\"}',-1",
            "'参数校验每页大小为null','{\"pageNo\": 1, \"pageSize\": null, \"id\": 10, \"name\": \"张三\"}',-1",
            "'业务排序orders','{\"pageNo\": 1, \"pageSize\": 10,\"orders\":[{\"column\":\"id\",\"asc\":false}], \"id\": 10, \"name\": \"张三\"}',0",
            "'参数校验每页大小为null','{\"pageNo\": 1, \"pageSize\": null, \"id\": 10, \"name\": \"张三\"}',-1",
            "'业务必须大于0异常','{\"pageNo\": 1, \"pageSize\": 10, \"id\": 0, \"name\": \"张三\"}',-1",
    })
//    @Test
    void page(String desc, String req, Integer expectedCode) throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/testuser/page")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(req))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(expectedCode))
                .andDo(print());
    }
}