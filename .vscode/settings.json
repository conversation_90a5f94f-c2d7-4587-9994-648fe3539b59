{"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "interactive", "maven.settingsFile": "E:\\apache-maven-3.6.2-bin\\apache-maven-3.6.2\\conf\\himall-settings.xml", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx16G -Xms100m -Xlog:disable", "java.configuration.maven.globalSettings": "", "java.configuration.maven.userSettings": "", "java.project.sourcePaths": ["src/main/java"], "java.project.outputPath": "target/classes", "java.project.referencedLibraries": ["lib/**/*.jar"], "java.import.maven.enabled": true, "java.import.maven.offline": false, "java.import.gradle.enabled": false, "java.maven.downloadSources": true, "java.maven.updateSnapshots": true}