root = true

[*]
charset = utf-8
end_of_line = crlf
indent_size = 4
indent_style = space
insert_final_newline = false
max_line_length = 180
tab_width = 4

[*.java]
max_line_length = 140

[*.less]
indent_size = 2

[*.proto]
indent_size = 2
tab_width = 2

[*.sass]
indent_size = 2

[*.scss]
indent_size = 2

[*.vue]
indent_size = 2
tab_width = 2

[{*.bash,*.sh,*.zsh}]
indent_size = 2
tab_width = 2

[{*.har,*.jsb2,*.jsb3,*.json,*.jsonc,*.postman_collection,*.postman_collection.json,*.postman_environment,*.postman_environment.json,.babelrc,.eslintrc,.prettierrc,.stylelintrc,bowerrc,jest.config}]
indent_size = 2

[{*.http,*.rest}]
indent_size = 0

[{*.yaml,*.yml}]
indent_size = 2
