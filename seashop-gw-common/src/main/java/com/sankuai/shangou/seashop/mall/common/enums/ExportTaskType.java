package com.sankuai.shangou.seashop.mall.common.enums;

import com.sankuai.shangou.seashop.base.export.enums.TaskType;

/**
 * <AUTHOR>
 */
public enum ExportTaskType implements TaskType {

    /**
     * 任务类型，具有一定规则的等长数字
     * <pre>
     *     类型规则：101150001 => 1 01 15 0001
     *     1. 第一位数字:发起外观。1-平台；2：供应商；3：商家
     *     2. 第二、三位数字：业务类型。01-导出任务
     *     3. 第四、五位数字：业务系统。10-基础服务；11-交易服务；12-营销服务；13-用户服务；14-商品服务；15-订单服务
     *     4，最后四位：具体业务
     * </pre>
     */
    ORDER(301150000, "订单列表导出"),
    USER_PURCHASE_SKU(301150001, "商家采购统计导出"),
    APPLY_REFUND(301150010, "我申请的退款"),
    APPLY_RETURN(301150011, "我申请的退货"),

    ;

    private final Integer type;
    private final String name;

    ExportTaskType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    @Override
    public Integer getType() {
        return type;
    }

    @Override
    public String getName() {
        return name;
    }
}
