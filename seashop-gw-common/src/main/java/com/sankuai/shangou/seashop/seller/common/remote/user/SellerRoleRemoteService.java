package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.account.RoleCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.RoleQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.RoleRespList;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class SellerRoleRemoteService {

    @Resource
    private RoleQueryFeign roleQueryFeign;

    @Resource
    private RoleCmdFeign roleCmdFeign;


    public RoleRespList queryRoleList(QueryRoleReq queryRoleReq) {
        //获取结果
        //转化结果
        return ThriftResponseHelper.executeThriftCall(() -> roleQueryFeign.queryRoleList(queryRoleReq));
    }

    public Long addRole(CmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> roleCmdFeign.addRole(cmdRoleReq1));
    }

    public Long editRole(CmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> roleCmdFeign.editRole(cmdRoleReq1));
    }

    public Long deleteRole(CmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> roleCmdFeign.deleteRole(cmdRoleReq1));
    }
}
