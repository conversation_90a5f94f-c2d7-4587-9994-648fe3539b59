package com.sankuai.shangou.seashop.seller.common.remote.user;

import java.util.List;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.user.thrift.shop.request.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.*;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdSendCodeReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCompanyCmdShopReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiPersonalCmdShopReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiProductShopInfoResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiShippingSettingsSaveReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiShippingSettingsResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiShopDetailResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.TreeBankRegionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class SellerShopRemoteService {
    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private ShopCmdFeign shopCmdFeign;


    public ShopSimpleListResp querySimpleList(ShopSimpleQueryReq request) {
        log.info("ShopRemoteService【querySimpleList】请求参数={}", JsonUtil.toJsonString(request));
        ShopSimpleListResp shopSimpleListResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(request));
        log.info("ShopRemoteService【querySimpleList】返回参数={}", JsonUtil.toJsonString(shopSimpleListResp));
        return shopSimpleListResp;
    }

    public ApiShopDetailResp queryDetail(BaseIdReq shopId) {
        ShopDetailResp shopDetailResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryDetail(shopId));
        return JsonUtil.copy(shopDetailResp, ApiShopDetailResp.class);
    }

    public Long editShopPersonal(ApiPersonalCmdShopReq cmdShopReq) {
        CmdShopReq cmdShopReq1 = JsonUtil.copy(cmdShopReq, CmdShopReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editShopPersonal(cmdShopReq1));
    }

    public Long editShopEnterprise(ApiCompanyCmdShopReq cmdShopReq) {
        CmdShopReq cmdShopReq1 = JsonUtil.copy(cmdShopReq, CmdShopReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editShopEnterprise(cmdShopReq1));
    }

    public BaseResp sendCode(ApiCmdSendCodeReq cmdSendCodeReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.sendCode(JsonUtil.copy(cmdSendCodeReq, CmdSendCodeReq.class)));
    }

    public ApiShippingSettingsResp getShippingSettings(BaseIdReq request) {
        ShippingSettingsResp shippingSettingsResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.getShippingSettings(request));
        return JsonUtil.copy(shippingSettingsResp, ApiShippingSettingsResp.class);
    }

    public BaseResp saveShippingSettings(ApiShippingSettingsSaveReq request) {
        ShippingSettingsSaveReq saveReq = JsonUtil.copy(request, ShippingSettingsSaveReq.class);
        saveReq.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.saveShippingSettings(saveReq));
    }

    public ApiProductShopInfoResp queryProductShop(Long shopId) {
        //转化参数
        ProductShopQueryReq productShopQueryReq = new ProductShopQueryReq();
        productShopQueryReq.setShopId(shopId);
        //调用服务
        ProductShopInfoResp productShopInfoResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryProductShop(productShopQueryReq));
        //转化返回值
        ApiProductShopInfoResp apiProductShopInfoResp = JsonUtil.copy(productShopInfoResp, ApiProductShopInfoResp.class);
        return apiProductShopInfoResp;
    }

    public Long modifyBankInfo(CmdShopStepsTwoReq cmdShopStepsTwoReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.modifyBankInfo(JsonUtil.copy(cmdShopStepsTwoReq, CmdShopStepsTwoReq.class)));
    }

    public Long modifyBaseInfo(CmdShopStepsOneReq cmdShopBaseReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.modifyBaseInfo(JsonUtil.copy(cmdShopBaseReq, CmdShopStepsOneReq.class)));
    }

    public Long modifyManagerInfo(CmdShopManagerReq cmdShopManagerReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.modifyManagerInfo(JsonUtil.copy(cmdShopManagerReq, CmdShopManagerReq.class)));
    }

    public String getBankRegion() {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.getBankRegion());
    }

    public List<TreeBankRegionResp> getRegionByParentId(BaseIdReq baseIdReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.getRegionByParentId(baseIdReq));
    }

    public List<TreeBankRegionResp> getRegionsById(BaseIdReq regionId) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.getRegionsById(regionId));
    }

    public List<ShopResp> queryShopsByIds(ShopQueryReq baseBatchIdReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryShopsByIds(baseBatchIdReq));
    }
}
