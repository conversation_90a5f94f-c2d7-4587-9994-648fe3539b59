package com.sankuai.shangou.seashop.mall.common.remote.user;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.account.response.TreeBankRegionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopEsQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdAgreementReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdCreateQRReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdSendCodeReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStepsOneReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStepsThreeReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStepsTwoReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ProductShopQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopEsQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopQueryPagerReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ProductShopInfoResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopIntroductionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsCombinationResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MallShopRemoteService {
    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private ShopCmdFeign shopCmdFeign;
    @Resource
    private ShopEsQueryFeign shopEsQueryFeign;

    /**
     * 默认排序字段
     */
//    private static final String DEFAULT_SORT_FIELD = "shopId";
    public String residencyApplication(CmdAgreementReq cmdAgreementReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.residencyApplication(JsonUtil.copy(cmdAgreementReq, CmdAgreementReq.class)));
    }

    public BaseResp sendCode(CmdSendCodeReq cmdSendCodeReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.sendCode(JsonUtil.copy(cmdSendCodeReq, CmdSendCodeReq.class)));
    }

    public Long editBaseInfo(CmdShopStepsOneReq cmdShopStepsOneReq) {
        ShopSimpleResp shopSimpleResp = shopInfoByUserId(new BaseIdReq() {
            {
                setId(cmdShopStepsOneReq.getShopId());
            }
        });
        cmdShopStepsOneReq.setShopId(shopSimpleResp.getId());
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editBaseInfo(JsonUtil.copy(cmdShopStepsOneReq, CmdShopStepsOneReq.class)));
    }

    public Long editBankInfo(CmdShopStepsTwoReq cmdShopStepsTwoReq) {
        ShopSimpleResp shopSimpleResp = shopInfoByUserId(new BaseIdReq() {
            {
                setId(cmdShopStepsTwoReq.getShopId());
            }
        });
        cmdShopStepsTwoReq.setShopId(shopSimpleResp.getId());
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editBankInfo(JsonUtil.copy(cmdShopStepsTwoReq, CmdShopStepsTwoReq.class)));
    }

    public Long editCategoryInfo(CmdShopStepsThreeReq cmdShopStepsThreeReq) {
        ShopSimpleResp shopSimpleResp = shopInfoByUserId(new BaseIdReq() {
            {
                setId(cmdShopStepsThreeReq.getShopId());
            }
        });
        cmdShopStepsThreeReq.setShopId(shopSimpleResp.getId());
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editCategoryInfo(JsonUtil.copy(cmdShopStepsThreeReq, CmdShopStepsThreeReq.class)));
    }

    public BasePageResp<ShopResp> queryPage(ShopQueryPagerReq request) {
        ShopQueryPagerReq shopQueryPagerReq = JsonUtil.copy(request, ShopQueryPagerReq.class);
        BasePageResp<ShopResp> shopPageResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryPage(shopQueryPagerReq));
        return PageResultHelper.transfer(shopPageResp, ShopResp.class);
    }


    public ProductShopInfoResp queryProductShop(ProductShopQueryReq baseIdReq) {
        //转化参数
        ProductShopQueryReq productShopQueryReq = JsonUtil.copy(baseIdReq, ProductShopQueryReq.class);
        //调用服务
        ProductShopInfoResp productShopInfoResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryProductShop(productShopQueryReq));
        //转化返回值
        return JsonUtil.copy(productShopInfoResp, ProductShopInfoResp.class);
    }


    /**
     * 通过ES查询店铺信息（主要是商城店铺查询使用）
     *
     * @param request
     * @return
     */
    public ShopEsCombinationResp queryByShopEs(ShopEsQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> shopEsQueryFeign.queryByShopEs(request));
    }

    /**
     * 查询店铺详情
     *
     * @param shopId
     * @return
     */
    public ShopDetailResp queryDetail(BaseIdReq shopId) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryDetail(shopId));
    }

    /**
     * 查询店铺详情
     *
     * @param shopId
     * @return
     */
    public ShopResp queryShop(BaseIdReq shopId) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryShop(shopId));
    }

    /**
     * 创建店铺二维码
     *
     * @param cmdCreateQRReq 请求体
     * @return 二维码地址
     */
    public String createQrCode(CmdCreateQRReq cmdCreateQRReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.createQR(cmdCreateQRReq));
    }

    /**
     * 查询店铺介绍
     *
     * @param shopId 店铺ID
     * @return 店铺介绍
     */
    public ShopIntroductionResp queryShopIntroduction(BaseIdReq shopId) {
        ShopIntroductionResp shopIntroductionResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.shopIntroduction(shopId));
        return shopIntroductionResp;
    }

    /**
     * 查询自营店铺介绍
     *
     * @return 店铺介绍
     */
    public ShopSimpleResp querySelfShopInfo() {
        ShopSimpleResp shopSimpleResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.selfShopInfo());
        return shopSimpleResp;
    }

    public String getBankRegion() {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.getBankRegion());
    }

    public List<TreeBankRegionResp> getRegionByParentId(BaseIdReq baseIdReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.getRegionByParentId(baseIdReq));
    }

    public List<TreeBankRegionResp> getRegionsById(BaseIdReq regionId) {
        return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.getRegionsById(regionId));
    }

    public ShopSimpleResp shopInfoByUserId(BaseIdReq baseIdReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.shopInfoByUserId(baseIdReq));
    }

    public ShopSimpleListResp querySimpleList(ShopSimpleQueryReq build) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(build));
    }

}
