package com.sankuai.shangou.seashop.mall.common.remote.user;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.CustomerServiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.CustomerEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CustomerServiceQueryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopOpenApiSettingResp;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: LXH
 **/
@Service
@Slf4j
public class MallCustomerServiceRemoteService {

    @Resource
    private CustomerServiceQueryFeign customerServiceQueryFeign;
    @Resource
    private MallShopRemoteService mallShopRemoteService;

    ///**
    // * 获取客服信息
    // *
    // * @return BaseShopSitSettingRes
    // */
    //public ShopOpenApiSettingResp getAppSetting(String appKey) {
    //    return ThriftResponseHelper.executeThriftCall(() -> customerServiceQueryFeign.queryOpenApiSetting(appKey));
    //}


    public String getToken(String chatDomain, String appKey, String appSecret, String openId) {

        String url = chatDomain + "/clientApi/OAuth/getToken";
        SortedMap<String, String> paramMap = new TreeMap<>();
        paramMap.put("appKey", appKey);
        paramMap.put("openId", openId);
        paramMap.put("timestamp", (DateUtil.currentSeconds()+28800)+"");
        String sign = getSign(paramMap,appSecret);
        paramMap.put("sign", sign);
        //参数使用key=value&间隔拼接
        StringBuilder sb = new StringBuilder();

        paramMap.forEach((k, v) -> {
            sb.append(k).append("=").append(URLEncodeUtil.encode(v)).append("&");
        });
        sb.deleteCharAt(sb.length() - 1);
        String urlResult = url + "?" + sb;
        String data = HttpUtil.get(urlResult);

        if (StrUtil.isNotBlank(data)) {
            log.error("获取token返回:{}", data);
            return JSONUtil.getByPath(JSONUtil.parse(data), "data.token").toString();
        } else {
            throw new BusinessException("获取会话列表失败");
        }
    }


    public String getSessionToken(String chatDomain, String name, String avatar, String appKey, String appSecret, String openId) {
        String url = chatDomain + "/clientApi/OAuth/GetSession";
        SortedMap<String, String> paramMap = new TreeMap<>();
        paramMap.put("name", name);
        paramMap.put("avatar", avatar);
        paramMap.put("appKey", appKey);
        paramMap.put("openId", openId);
        paramMap.put("timestamp", (DateUtil.currentSeconds() + 28800) + "");
        String sign = getSign(paramMap, appSecret);
        paramMap.put("sign", sign);
        //参数使用key=value&间隔拼接
        StringBuilder sb = new StringBuilder();

        paramMap.forEach((k, v) -> {
            sb.append(k).append("=").append(URLEncodeUtil.encode(v)).append("&");
        });
        sb.deleteCharAt(sb.length() - 1);
        String urlResult = url + "?" + sb;
        String data = HttpUtil.get(urlResult);

        if (StrUtil.isNotBlank(data)) {
            log.error("获取token返回:{}", data);
            return data;
        }
        else {
            throw new BusinessException("获取会话列表失败");
        }

    }

//    public static void main(String[] args) {
//        String str = "{\"success\":true,\"data\":{\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBrZXkiOiJvcGVuMTg5d2c3MXFtNGkiLCJvcGVuSWQiOiIxIiwiZXhwIjoxNzM5ODU4NTY2LCJpc3MiOiJoaWNoYXRTZXJ2ZXIxLjEiLCJhdWQiOiJoaWNoYXRDbGllbnQifQ.Vflkgc3x727hY4xPCDNb7Udjk1adbJ7wgin8UK44l10\"}}";
//        System.out.println(JSONUtil.getByPath(JSONUtil.parse(str), "data.token").toString());
//    }

    private static String getSign(Map<String, String> paramMap, String appSecret) {
        log.error("代签名字典:{}", JsonUtil.toJsonString(paramMap));
        //参数使用key=value&间隔拼接
        StringBuilder sb = new StringBuilder();
        paramMap.forEach((k, v) -> {
            sb.append(k).append(v);
        });
        //拼接appSecret
        sb.append(appSecret);
        //代签名字符串
        log.error("代签名字符串:{}", sb);
        //md5加密
        log.error("签名:{}", SecureUtil.md5(sb.toString()).toUpperCase());
        return SecureUtil.md5(sb.toString()).toUpperCase();
    }

    public BasePageResp<CustomerServiceResp> queryPage(BaseIdReq req) {
        CustomerServiceQueryPageReq customerServiceQueryPageReq = new CustomerServiceQueryPageReq();
        customerServiceQueryPageReq.setPageSize(100);
        customerServiceQueryPageReq.setShopId(req.getId());
        BasePageResp<CustomerServiceResp> basePageResp = ThriftResponseHelper.executeThriftCall(() -> customerServiceQueryFeign.queryPage(customerServiceQueryPageReq));
        ShopOpenApiSettingResp shopOpenApiSettingResp = ThriftResponseHelper.executeThriftCall(() -> customerServiceQueryFeign.queryOpenApiSettingByShop(req));
        if (shopOpenApiSettingResp == null) {
            return basePageResp;
        }
        CustomerServiceResp serviceResp = new CustomerServiceResp();
        serviceResp.setAccountCode(shopOpenApiSettingResp.getAppKey());
        serviceResp.setTool(CustomerEnum.Tool.HiChat.getValue());
        serviceResp.setServerStatus(CustomerEnum.ServiceStatusType.Open.getValue());
        basePageResp.getData().add(serviceResp);
        return basePageResp;
    }

    public ShopOpenApiSettingResp getSelfShopApi() {
        BaseIdReq req = new BaseIdReq();
        req.setId(mallShopRemoteService.querySelfShopInfo().getId());
        return ThriftResponseHelper.executeThriftCall(() -> customerServiceQueryFeign.queryOpenApiSettingByShop(req));
    }

}
