package com.sankuai.shangou.seashop.mall.common.remote.product.model;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/28 10:01
 */
@Setter
@Getter
public class RemoteCategoryBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 上级类目id
     */
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    private BigDecimal commissionRate;

    /**
     * 类目的深度
     */
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    private String path;

    /**
     * 是否已删除
     */
    private Boolean whetherDelete;

    /**
     * 自定义表单Id
     */
    private Long customFormId;

    /**
     * 类目全路径
     */
    private String fullCategoryName;

    /**
     * 保证金
     */
    private BigDecimal cashDeposit;

    /**
     * 是否有下级
     */
    private Boolean hasChildren;

    /**
     * 是否允许无理由退货
     */
    private Boolean enableNoReasonReturn;

}
