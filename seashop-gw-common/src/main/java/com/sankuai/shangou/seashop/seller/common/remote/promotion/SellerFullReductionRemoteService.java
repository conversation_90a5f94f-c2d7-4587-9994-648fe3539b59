package com.sankuai.shangou.seashop.seller.common.remote.promotion;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryFullReductionDetailReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FullReductionCmdFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FullReductionQueryFeign;

/**
 * @author: lhx
 * @date: 2024/1/15/015
 * @description:
 */
@Service
public class SellerFullReductionRemoteService {

    @Resource
    private FullReductionCmdFeign fullReductionCmdFeign;

    @Resource
    private FullReductionQueryFeign fullReductionQueryFeign;

    /**
     * 保存满减活动
     *
     * @param request
     * @return
     */
    public BaseResp save(FullReductionSaveReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> fullReductionCmdFeign.save(request));
    }

    /**
     * 结束满减活动
     *
     * @param request
     * @return
     */
    public BaseResp endActive(BaseIdReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> fullReductionCmdFeign.endActive(request));
    }

    /**
     * 查询满减活动列表
     *
     * @param request
     * @return
     */
    public BasePageResp<FullReductionSimpleResp> pageList(FullReductionQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> fullReductionQueryFeign.pageList(request));
    }

    /**
     * 查询满减活动
     *
     * @param request
     * @return
     */
    public FullReductionResp getById(QueryFullReductionDetailReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> fullReductionQueryFeign.getById(request));
    }

}
