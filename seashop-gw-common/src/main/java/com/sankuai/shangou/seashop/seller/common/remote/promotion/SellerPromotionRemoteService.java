package com.sankuai.shangou.seashop.seller.common.remote.promotion;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryProductPromotionReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.MallCollocationResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductIdListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductPromotionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.PromotionProductFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ShopUserPromotionQueryFeign;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiProductPromotionDto;

/**
 * @author: lhx
 * @date: 2024/3/7/007
 * @description:
 */
@Service
public class SellerPromotionRemoteService {

    @Resource
    PromotionProductFeign promotionProductFeign;

    @Resource
    ShopUserPromotionQueryFeign shopUserPromotionQueryFeign;

    public ProductIdListResp collocationFlashSaleProductId(ShopIdReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> promotionProductFeign.collocationFlashSaleProductId(request));
    }

    public ProductPromotionResp queryProductPromotion(QueryProductPromotionReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> shopUserPromotionQueryFeign.queryProductPromotion(request));
    }

    public ApiProductPromotionDto queryProductPromotionDto(Long productId, Long shopId) {
        QueryProductPromotionReq promotionReq = new QueryProductPromotionReq();
        promotionReq.setProductId(productId);
        promotionReq.setShopId(shopId);
        ProductPromotionResp promotionResp = queryProductPromotion(promotionReq);

        ApiProductPromotionDto promotionDto = new ApiProductPromotionDto();
        if (promotionResp == null) {
            return promotionDto;
        }

        // 是否有组合购
        MallCollocationResp mallCollocationResp = promotionResp.getMallCollocationResp();
        if (mallCollocationResp != null && CollectionUtils.isNotEmpty(mallCollocationResp.getCollocationRespList())) {
            promotionDto.setHasCollocation(true);
        }

        // 判断是否有限时购
        if (promotionResp.getFlashSaleResp() != null) {
            promotionDto.setHasFlashSale(true);
        }
        return promotionDto;
    }
}
