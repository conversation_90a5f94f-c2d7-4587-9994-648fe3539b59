package com.sankuai.shangou.seashop.seller.common.remote.promotion;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleAddReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleUpdateReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.ShopFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.VisualFlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleCategoryResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.VisualFlashSaleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCategoryQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCmdFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleConfigCmdFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleConfigQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryFeign;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@Service
public class SellerFlashSaleRemoteService {

    @Resource
    private FlashSaleCategoryQueryFeign flashSaleCategoryQueryFeign;

    @Resource
    private FlashSaleConfigCmdFeign flashSaleConfigCmdFeign;

    @Resource
    private FlashSaleConfigQueryFeign flashSaleConfigQueryFeign;

    @Resource
    private FlashSaleCmdFeign flashSaleCmdFeign;

    @Resource
    private FlashSaleQueryFeign flashSaleQueryFeign;

    /**
     * 限时购分类分页列表
     *
     * @param request
     * @return
     */
    public BasePageResp<FlashSaleCategoryResp> categoryPageList(BasePageReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleCategoryQueryFeign.pageList(request));
    }

    /**
     * 新增限时购
     *
     * @param request
     * @return
     */
    public BaseResp add(FlashSaleAddReq request) {
        request.valueInit();
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.add(request));
    }

    /**
     * 修改限时购
     *
     * @param request
     * @return
     */
    public BaseResp update(FlashSaleUpdateReq request) {
        request.valueInit();
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.update(request));
    }

    /**
     * 结束限时购
     *
     * @param request
     * @return
     */
    public BaseResp endActive(BaseIdReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.endActive(request));
    }

    /**
     * 修改店铺限时购配置
     *
     * @param request
     * @return
     */
    public BaseResp updateShopConfig(ShopFlashSaleConfigReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleConfigCmdFeign.updateShopConfig(request));
    }

    /**
     * 查询店铺限时购配置
     *
     * @param request
     * @return
     */
    public ShopFlashSaleConfigResp getShopConfig(ShopIdReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleConfigQueryFeign.getShopConfig(request));
    }

    /**
     * 限时购分页列表
     *
     * @param request
     * @return
     */
    public BasePageResp<FlashSaleSimpleResp> pageList(FlashSaleQueryReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.pageList(request));
    }

    /**
     * 限时购详情
     *
     * @param request
     * @return
     */
    public FlashSaleResp getById(BaseIdReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.getById(request));
    }

    /**
     * 前端可视化组件限时购活动列表查询
     *
     * @param request
     * @return
     */
    public BasePageResp<VisualFlashSaleResp> componentPageList(VisualFlashSaleQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.componentPageList(request));
    }
}
