package com.sankuai.shangou.seashop.mall.common.remote.product;


import java.util.Comparator;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.product.model.RemoteSimpleCategoryBo;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryListReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryTreeResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/28 9:49
 */
@Service
@Slf4j
public class MallCategoryRemoteService {

    @Resource
    private CategoryQueryFeign categoryQueryFeign;

    /**
     * 查询类目树
     *
     * @return 类目树
     */
    public List<RemoteSimpleCategoryBo> getCategoryTree(QueryCategoryReq queryCategoryReq) {
        CategoryTreeResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTree(queryCategoryReq));
        return JsonUtil.parseArray(resp.getResult(), RemoteSimpleCategoryBo.class);
    }

    public List<RemoteSimpleCategoryBo> queryCategoryTreeHideNoThreeLevel(QueryCategoryReq queryCategoryReq) {
        CategoryTreeResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTreeHideNoThreeLevel(queryCategoryReq));
        return JsonUtil.parseArray(resp.getResult(), RemoteSimpleCategoryBo.class);
    }

    /**
     * 查询类目列表
     *
     * @param parentId    父类目id
     * @param whetherShow 是否显示
     * @return 类目列表
     */
    public List<CategoryResp> getCategoryList(Long parentId, Boolean whetherShow) {
        QueryCategoryListReq remoteReq = new QueryCategoryListReq();
        remoteReq.setParentId(parentId);
        remoteReq.setWhetherShow(whetherShow);
        CategoryListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryList(remoteReq));
        List<CategoryResp> categoryList = resp.getCategoryRespList();
        categoryList.sort(Comparator.comparing(CategoryResp::getDisplaySequence));
        return categoryList;
    }

}
