package com.sankuai.shangou.seashop.m.common.remote.promotion;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryFeign;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@Service
public class MFlashSaleRemoteService {

    @Resource
    private FlashSaleQueryFeign flashSaleQueryFeign;

    ///**
    // * 新增限时购分类
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp addCategory(FlashSaleCategoryAddReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleCategoryCmdFeign.add(request));
    //}
    //
    ///**
    // * 删除限时购分类
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp deleteCategory(BaseIdReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleCategoryCmdFeign.delete(request));
    //}
    //
    ///**
    // * 分页查询限时购分类列表
    // *
    // * @param request
    // * @return
    // */
    //public BasePageResp<FlashSaleCategoryResp> categoryPageList(BasePageReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleCategoryQueryFeign.pageList(request));
    //}
    //
    ///**
    // * 修改平台限时购配置
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp updatePlatConfig(PlatFlashSaleConfigReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleConfigCmdFeign.updatePlatConfig(request));
    //}
    //
    ///**
    // * 查询平台限时购配置
    // *
    // * @return
    // */
    //public PlatFlashSaleConfigResp getPlatConfig() {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleConfigQueryFeign.getPlatConfig());
    //}
    //
    ///**
    // * 审核限时购活动
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp audit(FlashSaleAuditReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.audit(request));
    //}
    //
    ///**
    // * 前端显示限时购活动
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp show(FlashSaleShowReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.show(request));
    //}

    /**
     * 限时购活动列表查询
     *
     * @param request
     * @return
     */
    public BasePageResp<FlashSaleSimpleResp> pageList(FlashSaleQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.pageList(request));
    }

    ///**
    // * 通过id查询限时购活动信息
    // *
    // * @param request
    // * @return
    // */
    //public FlashSaleResp getById(BaseIdReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.getById(request));
    //}
    //
    ///**
    // * 前端可视化组件限时购活动列表查询
    // *
    // * @param request
    // * @return
    // */
    //public BasePageResp<VisualFlashSaleResp> componentPageList(VisualFlashSaleQueryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.componentPageList(request));
    //}
}
