package com.sankuai.shangou.seashop.mall.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import com.sankuai.shangou.seashop.base.boot.enums.Code;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@ThriftEnum
public enum MallResultCodeEnum implements Code {


    STORE_ADMINISTRATORS_ARE_NOT_ALLOWED_TO_LOGIN(40040406, "店铺管理员不允许登录商家端"),
    STORE_ADMINISTRATOR_HAS_BEEN_DELETED(40040407, "店铺管理员已经被删除"),
    //权限码过期请重新验证
    PERMISSION_CODE_EXPIRED(40040408, "权限码过期请重新验证"),
    SELLER_ADMIN_NOT_EXIST(50001002, "供应商子账号已经被删除,请联系管理员重新创建！"),

    ;


    private int code;
    private String msg;

    MallResultCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @ThriftEnumValue
    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    @Override
    public Integer value() {
        return code;
    }

    @Override
    public String desc() {
        return msg;
    }
}
