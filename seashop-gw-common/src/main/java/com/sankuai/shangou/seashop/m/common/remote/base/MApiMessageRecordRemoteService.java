package com.sankuai.shangou.seashop.m.common.remote.base;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MessageRecordCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.MessageRecordQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ContentTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.MessageTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ToUserEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.MessageRecordQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.MessageRecordCmdReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiMessageRecordQueryReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMessageRecordRes;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Service
public class MApiMessageRecordRemoteService {
    @Resource
    private MessageRecordQueryFeign messageRecordQueryFeign;
    @Resource
    private MessageRecordCmdFeign messageRecordCmdFeign;
    @Resource
    private CouponQueryFeign couponQueryFeign;

    public BasePageResp<ApiMessageRecordRes> queryPage(ApiMessageRecordQueryReq apiSendEmailMsgReq) {
        //转化入参
        MessageRecordQueryReq messageRecordQueryReq = JsonUtil.copy(apiSendEmailMsgReq, MessageRecordQueryReq.class);
        //调用远程服务
        BasePageResp<MessageRecordResp> recordResBasePageResp = ThriftResponseHelper.executeThriftCall(() -> messageRecordQueryFeign.queryPage(messageRecordQueryReq));
        //转化出参
        BasePageResp<ApiMessageRecordRes> recordRespBaseResp = PageResultHelper.transfer(recordResBasePageResp, ApiMessageRecordRes.class);
        //判空
        if (CollUtil.isNotEmpty(recordRespBaseResp.getData())) {
//            遍历数据
            recordRespBaseResp.getData().forEach(v -> {
//                计算使用率
                v.setUsageRate(ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.calculateUsageRate(new BaseIdReq() {
                    {
                        setId(v.getId());
                    }
                })));
            });
        }
        return recordRespBaseResp;
    }

    public MessageRecordDetailResp queryDetail(BaseIdReq baseIdReq) {
        return ThriftResponseHelper.executeThriftCall(() -> messageRecordQueryFeign.queryDetail(baseIdReq));
    }

    public Long addCouponRecord(List<Long> labelId, ToUserEnum toUserEnum, List<Long> couponIdList, Long userId) {
        String toUserLabel = getToUser(labelId, toUserEnum);
        MessageRecordCmdReq messageRecordCmdReq = new MessageRecordCmdReq();
        messageRecordCmdReq.setMessageType(MessageTypeEnum.Coupon.getCode());
        messageRecordCmdReq.setContentType(ContentTypeEnum.Card.getCode());
        messageRecordCmdReq.setToUserLabel(toUserLabel);
        messageRecordCmdReq.setSendContent("优惠券");
        messageRecordCmdReq.setSendState(1);
        messageRecordCmdReq.setSendTime(new Date());
        messageRecordCmdReq.setCouponIdList(couponIdList);
        messageRecordCmdReq.setOperationUserId(userId);
        return ThriftResponseHelper.executeThriftCall(() -> messageRecordCmdFeign.addRecord(messageRecordCmdReq));
    }

    private String getToUser(List<Long> labelId, ToUserEnum toUserEnum) {
        return toUserEnum.getDesc();
    }

    public Long addMessageRecord(List<Long> labelId, ToUserEnum toUserEnum, String content) {
        String toUserLabel = getToUser(labelId, toUserEnum);
        MessageRecordCmdReq messageRecordCmdReq = new MessageRecordCmdReq();
        messageRecordCmdReq.setMessageType(MessageTypeEnum.Coupon.getCode());
        messageRecordCmdReq.setContentType(ContentTypeEnum.Card.getCode());
        messageRecordCmdReq.setToUserLabel(toUserLabel);
        messageRecordCmdReq.setSendContent(content);
        messageRecordCmdReq.setSendState(1);
        messageRecordCmdReq.setSendTime(new Date());
        return ThriftResponseHelper.executeThriftCall(() -> messageRecordCmdFeign.addRecord(messageRecordCmdReq));
    }

    public Long addEmailMessageRecord(List<Long> labelId, ToUserEnum toUserEnum, String content) {
        String toUserLabel = getToUser(labelId, toUserEnum);
        MessageRecordCmdReq messageRecordCmdReq = new MessageRecordCmdReq();
        messageRecordCmdReq.setMessageType(MessageTypeEnum.Email.getCode());
        messageRecordCmdReq.setContentType(ContentTypeEnum.Text.getCode());
        messageRecordCmdReq.setToUserLabel(toUserLabel);
        messageRecordCmdReq.setSendContent(content);
        messageRecordCmdReq.setSendState(1);
        messageRecordCmdReq.setSendTime(new Date());
        return ThriftResponseHelper.executeThriftCall(() -> messageRecordCmdFeign.addRecord(messageRecordCmdReq));
    }

    public void deleteCouponRecord(Long msgId) {
        BaseIdReq baseIdReq = new BaseIdReq();
        baseIdReq.setId(msgId);
        ThriftResponseHelper.executeThriftCall(() -> messageRecordCmdFeign.deleteRecord(baseIdReq));
    }
}
