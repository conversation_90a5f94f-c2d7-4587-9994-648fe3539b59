package com.sankuai.shangou.seashop.mall.common.constant;

/**
 * <AUTHOR>
 * @date 2024/01/02 18:23
 */
public class CommonConstant {

    /**
     * 查询起始页
     */
    public static final Integer QUERY_START = 1;

    /**
     * 查询限制
     */
    public static final Integer QUERY_LIMIT = 200;

    /**
     * 是
     */
    public static String YES_STR = "是";

    /**
     * 否
     */
    public static String NO_STR = "否";

    /**
     * 平台默认ID=0L
     */
    public static final Long PLATFORM_ID = 0L;

    /**
     * 默认的数量值0L
     */
    public static final Long DEFAULT_COUNT = 0L;

    /**
     * 导出查询最大数量
     */
    public static final Integer EXPORT_MAX_COUNT = 5000;

    /**
     * 初始页码
     */
    public static final Integer INIT_PAGE_NO = 1;

    /**
     * 循环开始页码
     */
    public static final Integer LOOP_START_PAGE_NO = 2;

    /**
     * 优惠券全店通用
     */
    public static final String COUPON_ALL_SHOP = "全店通用";

    /**
     * 换绑手机权限码
     */
    public static final String PERMISSION_CHANGE_MOBILE = "member:token:change:";

}
