package com.sankuai.shangou.seashop.seller.common.convert.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.dto.SpecDto;
import com.sankuai.shangou.seashop.product.thrift.core.helper.SkuHelper;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailSkuDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;
import com.sankuai.shangou.seashop.seller.common.convert.SellerProductConvert;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiSpecificationResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiProductDetailDto;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiProductDetailSkuDto;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2024/07/17 17:35
 */
@Component
public class SellerProductConvertImpl implements SellerProductConvert {

    @Override
    public ApiProductDetailDto convertApiProductDetailDto(ProductDetailDto product) {
        if (product == null) {
            return null;
        }

        ApiProductDetailDto apiProduct = JsonUtil.copy(product, ApiProductDetailDto.class, "skuList");

        // 处理规格信息 用于给前端复显
        List<ProductDetailSkuDto> skuList = product.getSkuList();
        List<ApiProductDetailSkuDto> apiSkuList = new ArrayList<>();
        List<SpecDto> allSpecList = new ArrayList<>();
        Map<Long, String> imageMap = new HashMap<>();
        skuList.forEach(sku -> {
            ApiProductDetailSkuDto apiSku = JsonUtil.copy(sku, ApiProductDetailSkuDto.class);
            List<SpecDto> specList = SkuHelper.parseSpecJson(sku.getSpecValueJson());
            if (CollUtil.isNotEmpty(specList)) {
                imageMap.put(specList.get(0).getValueId(), sku.getShowPic());
            }

            apiSku.setSpecList(specList);
            allSpecList.addAll(specList);
            apiSkuList.add(apiSku);
        });
        apiProduct.setSkuList(apiSkuList);
        List<SpecificationResp> specifications = SkuHelper.parseSpecSelect(allSpecList, imageMap);
        apiProduct.setSpecSelectList(JsonUtil.copyList(specifications, ApiSpecificationResp.class));

        return apiProduct;
    }
}
