package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopShipperCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopShipperQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateShopShipperDefaultReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopShipperResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/1/13/013
 * @description:
 */
@Service
@Slf4j
public class SellerShopShipperRemoteService {

    @Resource
    private ShopShipperQueryFeign shopShipperQueryFeign;

    @Resource
    private ShopShipperCmdFeign shopShipperCmdFeign;

    public QueryShopShipperResp queryShopShipperList(QueryShopShipperReq queryShopShipperReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopShipperQueryFeign.queryShopShipperList(queryShopShipperReq));
    }

    public BaseResp addShopShipper(AddShopShipperReq addShopShipperReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopShipperCmdFeign.addShopShipper(addShopShipperReq));
    }

    public BaseResp updateShopShipper(UpdateShopShipperReq updateShopShipperReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopShipperCmdFeign.updateShopShipper(updateShopShipperReq));
    }

    public BaseResp updateShopShipperDefault(UpdateShopShipperDefaultReq updateShopShipperDefaultReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopShipperCmdFeign.updateShopShipperDefault(updateShopShipperDefaultReq));
    }

    public BaseResp deleteShopShipper(DeleteShopShipperReq deleteShopShipperReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopShipperCmdFeign.deleteShopShipper(deleteShopShipperReq));
    }
}
