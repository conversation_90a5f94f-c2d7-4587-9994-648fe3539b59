package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.erp.thrift.biz.ErpJstAuthQueryThriftService;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.jst.ErpJstAuthQueryUrlReq;
import com.sankuai.shangou.seashop.erp.thrift.biz.response.jst.ErpJstAuthQueryUrlResp;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopErpCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopErpQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.WdtTokenResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 11:42
 */
@Service
public class SellerShopErpRemoteService {

    @Resource
    private ShopErpCmdFeign shopErpCmdFeign;

    @Resource
    private ShopErpQueryFeign shopErpQueryFeign;

    @Resource
    private ErpJstAuthQueryThriftService erpJstAuthQueryThriftService;

    public QueryShopErpResp queryShopErp(QueryShopErpReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> shopErpQueryFeign.queryShopErp(req));
    }

    public WdtTokenResp queryWdtTokenByShopId(QueryShopErpReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> shopErpQueryFeign.queryWdtTokenByShopId(req));
    }

    public BaseResp saveShopErp(SaveShopErpReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> shopErpCmdFeign.saveShopErp(req));
    }

    public ErpJstAuthQueryUrlResp queryAuthUrl(ErpJstAuthQueryUrlReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> erpJstAuthQueryThriftService.queryAuthUrl(req));
    }
}
