package com.sankuai.shangou.seashop.m.common.remote.user;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MShopRemoteService {
    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private ShopCmdFeign shopCmdFeign;


    //public ShopIdsResp getShopIds(QueryShopReq queryShopReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.getShopIds(queryShopReq));
    //}
    //
    //public String residencyApplication(CmdAgreementReq cmdAgreementReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.residencyApplication(cmdAgreementReq));
    //}
    //
    //public Long editProfilesOne(CmdShopStepsOneReq cmdShopStepsOneReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editBaseInfo(cmdShopStepsOneReq));
    //}
    //
    //public Long editProfilesTwo(CmdShopStepsTwoReq cmdShopStepsTwoReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editBankInfo(cmdShopStepsTwoReq));
    //}
    //
    //public Long editCategoryInfo(CmdShopStepsThreeReq cmdShopStepsThreeReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editCategoryInfo(cmdShopStepsThreeReq));
    //}

    //public BasePageResp<ShopResp> queryPage(ShopQueryPagerReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryPage(request));
    //}
    //
    //public ShopDetailResp queryDetail(BaseIdReq shopId) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryDetail(shopId));
    //}
    //
    //public BaseResp auditing(CmdShopStatusReq shopId) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.auditing(shopId));
    //}
    //
    //public Long editShopPersonal(CmdShopReq cmdShopReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editShopPersonal(cmdShopReq));
    //}
    //
    //public Long editShopEnterprise(CmdShopReq cmdShopReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.editShopEnterprise(cmdShopReq));
    //}

    //public Long freezeShop(CmdShopStatusReq cmdShopStatusReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.freezeShop(cmdShopStatusReq));
    //}
    //
    //public BaseResp sendDepositRemind(BaseIdReq baseIdReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.sendDepositRemind(baseIdReq));
    //
    //}
    //
    //public ShopUserCountResp countShopUser() {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.countShopUser());
    //}
    //
    //public List<ShopResp> queryShopsByIds(ShopQueryReq baseBatchIdReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryShopsByIds(baseBatchIdReq));
    //}
    //
    //public BasePageResp<ShopSimpleResp> querySimplePage(ShopQueryPagerReq request) {
    //    BasePageResp<ShopSimpleResp> basePageResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimplePage(request));
    //    //判空
    //    if (basePageResp == null) {
    //        return new BasePageResp<>();
    //    }
    //    return basePageResp;
    //}

    public ShopSimpleListResp querySimpleList(ShopSimpleQueryReq build) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(build));
    }

    //public BaseResp setShopType(ShopTypeCmdReq baseIdReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.setShopType(baseIdReq));
    //}
    //
    //public String getBankRegion() {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.getBankRegion());
    //}

    ///**
    // * 创建店铺二维码
    // *
    // * @param cmdCreateQRReq 请求体
    // * @return 二维码地址
    // */
    //public String createQrCode(CmdCreateQRReq cmdCreateQRReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.createQR(cmdCreateQRReq));
    //}
    //
    //public BaseResp updateSeq(CmdShopSeqReq cmdShopSeqReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.updateSeq(cmdShopSeqReq));
    //}
    //
    //public String queryShopCategoryDetail(BaseIdReq shopId) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryShopCategoryDetail(shopId));
    //}
}
