package com.sankuai.shangou.seashop.m.common.remote.promotion;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.DiscountActiveQueryFeign;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2024/1/15/015
 * @description:
 */
@Service
public class MDiscountActiveRemoteService {

    @Resource
    private DiscountActiveQueryFeign discountActiveQueryFeign;

    ///**
    // * 分页查询活动列表
    // *
    // * @param request
    // * @return
    // */
    //public BasePageResp<DiscountActiveSimpleResp> pageList(DiscountActiveQueryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> discountActiveQueryFeign.pageList(request));
    //}

    /**
     * 根据id查询活动
     *
     * @param request
     * @return
     */
    public DiscountActiveResp getById(BaseIdReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> discountActiveQueryFeign.getById(request));
    }

    public BasePageResp<DiscountActiveProductDto> queryDiscountActiveProduct(QueryDiscountActiveProductReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> discountActiveQueryFeign.queryDiscountActiveProduct(request));
    }
}
