package com.sankuai.shangou.seashop.mall.common.remote.user;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.MemberUserInfo;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.trade.thrift.core.TradeProductQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.QueryHotSaleProductReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.QueryNewestProductReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.HotSaleProductResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.NewestProductResp;
import com.sankuai.shangou.seashop.user.thrift.account.FavoriteShopCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.FavoriteShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.FavoriteShopCmdReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.FavoriteShopPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryFavoriteCountReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.FavoriteShopRes;
import com.sankuai.shangou.seashop.user.thrift.account.response.QueryFavoriteCountListResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: LXH
 **/
@Service
@Slf4j
public class MallFavoriteShopRemoteService {
    @Resource
    private FavoriteShopCmdFeign favoriteShopCmdFeign;
    @Resource
    private FavoriteShopQueryFeign favoriteShopQueryFeign;
    @Resource
    private TradeProductQueryFeign tradeProductQueryFeign;

    public BaseResp addFavoriteShop(Long func, LoginMemberDto memberDto) {
        //转化参数
        FavoriteShopCmdReq req = new FavoriteShopCmdReq();
        req.setUserId(memberDto.getId());
        req.setShopId(func);
        if (func == null || CommonConstant.DEFAULT_COUNT.equals(func)) {
            return BaseResp.of();
        }
        return ThriftResponseHelper.executeThriftCall(() -> favoriteShopCmdFeign.addFavoriteShop(req));
    }

    public BaseResp deleteFavoriteShop(FavoriteShopCmdReq func) {
        //转化参数
        FavoriteShopCmdReq req = JsonUtil.copy(func, FavoriteShopCmdReq.class);
        //调用远程服务
        return ThriftResponseHelper.executeThriftCall(() -> favoriteShopCmdFeign.deleteFavoriteShop(req));
    }

    public BasePageResp<FavoriteShopRes> queryPage(FavoriteShopPageReq func) {
        //转化参数
        FavoriteShopPageReq req = JsonUtil.copy(func, FavoriteShopPageReq.class);
        //调用远程服务
        BasePageResp<FavoriteShopRes> resp = ThriftResponseHelper.executeThriftCall(() -> favoriteShopQueryFeign.queryPage(req));
        //转化结果
        return PageResultHelper.transfer(resp, FavoriteShopRes.class);
    }

    public List<TradeProductDto> queryNewestProduct(Long shopId, Long userId) {
        QueryHotSaleProductReq request = new QueryHotSaleProductReq();
        request.setShopId(shopId);
        request.setUserId(userId);
        QueryNewestProductReq remoteReq = JsonUtil.copy(request, QueryNewestProductReq.class);
        NewestProductResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.queryNewestProduct(remoteReq));
        return Optional.ofNullable(resp).map(NewestProductResp::getProductList).orElse(Collections.emptyList());
    }

    public List<TradeProductDto> queryHotSaleProduct(Long shopId, Long userId) {
        QueryHotSaleProductReq request = new QueryHotSaleProductReq();
        request.setShopId(shopId);
        request.setUserId(userId);
        QueryHotSaleProductReq remoteReq = JsonUtil.copy(request, QueryHotSaleProductReq.class);
        HotSaleProductResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.queryHotSaleProduct(remoteReq));
//        HotSaleProductResp HotSaleProductResp = JsonUtil.copy(resp, HotSaleProductResp.class);
        return Optional.ofNullable(resp).map(HotSaleProductResp::getProductList).orElse(Collections.emptyList());
    }

    public QueryFavoriteCountListResp queryCount(QueryFavoriteCountReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> favoriteShopQueryFeign.queryCount(request));
    }
}
