package com.sankuai.shangou.seashop.m.common.remote.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ToUserEnum;
import com.sankuai.shangou.seashop.m.common.constant.CouponConstant;
import com.sankuai.shangou.seashop.m.common.convert.ApiCouponConverter;
import com.sankuai.shangou.seashop.m.common.remote.base.MApiMessageRecordRemoteService;
import com.sankuai.shangou.seashop.m.thrift.system.message.request.ApiBindContactCmdReq;
import com.sankuai.shangou.seashop.m.thrift.system.message.request.ApiCheckCodeCmdReq;
import com.sankuai.shangou.seashop.m.thrift.system.message.request.ApiSendCodeCmdReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.*;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMemberGroupingResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMemberLabelResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMemberLabelRespList;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMemberResp;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.OrderStatisticsMemberReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.*;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberGroupingResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberLabelRespList;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CheckCodeCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdSendCodeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @description: 商家服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MApiMemberRemoteService {
    @Resource
    private ShopMemberQueryFeign shopMemberQueryFeign;

    @Resource
    private ShopMemberCmdFeign shopMemberCmdFeign;


    //优惠券服务
    @Resource
    private CouponCmdFeign couponCmdFeign;
    //转化器
    @Resource
    private ApiCouponConverter apiCouponConverter;
    //消息记录服务
    @Resource
    private MApiMessageRecordRemoteService MApiMessageRecordRemoteService;
    @Resource
    private OrderQueryFeign orderQueryFeign;

    public BasePageResp<ApiMemberResp> queryMemberPage(ApiQueryMemberPageReq queryMemberPageReq) {
        QueryMemberPageReq queryMemberPageReq1 = JsonUtil.copy(queryMemberPageReq, QueryMemberPageReq.class);
        queryMemberPageReq1.setSeller(queryMemberPageReq.getIzSeller());
        BasePageResp<MemberResp> memberRespPage = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMemberPage(queryMemberPageReq1));
        return PageResultHelper.transfer(memberRespPage, ApiMemberResp.class);
    }

    public BasePageResp<ApiMemberResp> queryMemberPageAndLabel(ApiQueryMemberPageReq queryMemberPageReq) {
        QueryMemberPageReq queryMemberPageReq1 = JsonUtil.copy(queryMemberPageReq, QueryMemberPageReq.class);
        queryMemberPageReq1.setSeller(queryMemberPageReq.getIzSeller());
        BasePageResp<MemberResp> memberRespPage = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMemberPageAndLabel(queryMemberPageReq1));
        return PageResultHelper.transfer(memberRespPage, ApiMemberResp.class);
    }


    public ApiMemberResp queryMember(ApiQueryMemberReq queryMemberReq) {
        //转化参数
        QueryMemberReq queryMemberReq1 = JsonUtil.copy(queryMemberReq, QueryMemberReq.class);
        Date beginTime = new Date();
        //获取结果
        MemberResp MemberResp = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMember(queryMemberReq1));
        ApiMemberResp apiMemberResp = JsonUtil.copy(MemberResp, ApiMemberResp.class);
        OrderStatisticsMemberReq orderStatisticsMemberReq = new OrderStatisticsMemberReq();
        orderStatisticsMemberReq.setUserId(queryMemberReq.getId());
        orderStatisticsMemberReq.setBeginTime(DateUtil.offsetMonth(beginTime, -3));
        orderStatisticsMemberReq.setEndTime(beginTime);
        OrderStatisticsResp statisticsResp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getOrderStatisticsByMember(orderStatisticsMemberReq));
        apiMemberResp.setConsumptionFrequency(statisticsResp.getPayOrdersNum());
        apiMemberResp.setNetConsumptionAmount(statisticsResp.getSaleAmount());
        //转化结果
        return apiMemberResp;
    }

    public Long freezeMember(ApiCmdMemberReq cmdMemberReq) {
        //转化参数
        CmdMemberReq cmdMemberReq1 = JsonUtil.copy(cmdMemberReq, CmdMemberReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.freezeMember(cmdMemberReq1));
    }

    public Long thawingMember(ApiCmdMemberReq cmdMemberReq) {
        //转化参数
        CmdMemberReq cmdMemberReq1 = JsonUtil.copy(cmdMemberReq, CmdMemberReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.thawingMember(cmdMemberReq1));
    }

    public Long changePassword(ApiCmdMemberReq cmdMemberReq) {
        //转化参数
        CmdMemberReq cmdMemberReq1 = JsonUtil.copy(cmdMemberReq, CmdMemberReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.changePassword(cmdMemberReq1));
    }

    public Long unbindPhone(ApiCmdMemberReq cmdMemberReq) {
        //转化参数
        CmdMemberReq cmdMemberReq1 = JsonUtil.copy(cmdMemberReq, CmdMemberReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.unbindPhone(cmdMemberReq1));
    }

    public Long batchDelete(ApiBatchCmdMemberReq batchCmdMemberReq) {
        //转化参数
        BatchCmdMemberReq batchCmdMemberReq1 = JsonUtil.copy(batchCmdMemberReq, BatchCmdMemberReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.batchDelete(batchCmdMemberReq1));
    }

    public ApiMemberLabelRespList getMemberLabel(ApiQueryMemberReq queryMemberReq) {
        //转化参数
        QueryMemberReq queryMemberReq1 = JsonUtil.copy(queryMemberReq, QueryMemberReq.class);
        //获取结果
        MemberLabelRespList memberLabelRespList = ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.getMemberLabel(queryMemberReq1));
        //转化结果
        return new ApiMemberLabelRespList(JsonUtil.copyList(memberLabelRespList.getMemberLabelRespList(), ApiMemberLabelResp.class));
    }

    public Long setMemberLabel(ApiCmdMemberReq cmdMemberReq) {
        //转化参数
        CmdMemberReq cmdMemberReq1 = JsonUtil.copy(cmdMemberReq, CmdMemberReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.setMemberLabel(cmdMemberReq1));
    }

    public BaseResp batchAddMemberLabel(ApiBatchCmdMemberLabelReq cmdMemberReq) {
        //转化参数
        BatchCmdMemberLabelReq batchCmdMemberLabelReq = JsonUtil.copy(cmdMemberReq, BatchCmdMemberLabelReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.batchAddMemberLabel(batchCmdMemberLabelReq));
    }

    public BaseResp sendAllCoupon(ApiCmdSendAllCoupon apiCmdSendAllCoupon) {
        //重新设置分页参数
        apiCmdSendAllCoupon.getQueryMemberReq().setPageNo(CouponConstant.DEFAULT_PAGE_NO);
        apiCmdSendAllCoupon.getQueryMemberReq().setPageSize(CouponConstant.DEFAULT_PAGE_SIZE);
        //分页获取查询条件下的会员
        BasePageResp<ApiMemberResp> apiMemberRespBasePageResp = queryMemberPage(apiCmdSendAllCoupon.getQueryMemberReq());
        //获取总页数
        Integer totalPage = apiMemberRespBasePageResp.getPages();
        //遍历所有页数
        Long msgId = null;
        //添加消息记录
        if (apiCmdSendAllCoupon.getQueryMemberReq().getLabel() != null) {
            msgId = MApiMessageRecordRemoteService.addCouponRecord(Collections.singletonList(apiCmdSendAllCoupon.getQueryMemberReq().getLabel()), ToUserEnum.Label, apiCmdSendAllCoupon.getCouponIds(), apiCmdSendAllCoupon.getOperationUserId());
        }
        else {
            msgId = MApiMessageRecordRemoteService.addCouponRecord(null, ToUserEnum.Criteria, apiCmdSendAllCoupon.getCouponIds(), apiCmdSendAllCoupon.getOperationUserId());
        }
        try {
            for (int i = 1; i <= totalPage; i++) {
                //重新设置分页参数
                apiCmdSendAllCoupon.getQueryMemberReq().setPageNo(i);
                apiCmdSendAllCoupon.getQueryMemberReq().setPageSize(CouponConstant.DEFAULT_PAGE_SIZE);
                //设置不查总页数
                apiCmdSendAllCoupon.getQueryMemberReq().setWhetherCount(false);
                //分页获取查询条件下的会员
                BasePageResp<ApiMemberResp> apiMemberRespBasePageResp1 = queryMemberPage(apiCmdSendAllCoupon.getQueryMemberReq());
                //遍历所有会员获取会员id
                List<Long> memberIds = apiMemberRespBasePageResp1.getData().stream().map(ApiMemberResp::getId).collect(java.util.stream.Collectors.toList());
                //发送优惠券
                sendCoupon(new ApiCmdSendCoupon(apiCmdSendAllCoupon.getCouponIds(), memberIds, msgId));
            }
        }
        catch (Exception e) {
            //发送失败删除发送记录
            MApiMessageRecordRemoteService.deleteCouponRecord(msgId);
            throw e;
        }
        return BaseResp.of();
    }

    public BaseResp sendCoupon(ApiCmdSendCoupon sendCoupon) {
        //判空
        if (CollUtil.isEmpty(sendCoupon.getMemberIds()) || CollUtil.isEmpty(sendCoupon.getCouponIds())) {
            return BaseResp.of();
        }
        //发送优惠券
        ThriftResponseHelper.executeThriftCall(() -> couponCmdFeign.sendCoupon(apiCouponConverter.apiCmdSendCoupon2CouponSendReq(sendCoupon)));
        return BaseResp.of();
    }

    public BaseResp sendEmailMsg(ApiSendEmailMsgReq apiSendEmailMsgReq) {
        //转化参数
        SendEmailMsgReq sendEmailMsgReq = JsonUtil.copy(apiSendEmailMsgReq, SendEmailMsgReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.sendEmailMsg(sendEmailMsgReq));
    }

    public BaseResp bindContact(ApiBindContactCmdReq apiCheckCodeCmdReq) {
        //转化参数
        BindContactCmdReq bindContactCmdReq = JsonUtil.copy(apiCheckCodeCmdReq, BindContactCmdReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.bindContact(bindContactCmdReq));
    }

    public ApiMemberGroupingResp queryMemberGrouping() {
        //获取结果
        MemberGroupingResp memberGroupingResp = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMemberGrouping());
        //转化结果
        return JsonUtil.copy(memberGroupingResp, ApiMemberGroupingResp.class);
    }

    public BaseResp sendCode(ApiSendCodeCmdReq apiSendCodeCmdReq) {
        //转化参数
        CmdSendCodeReq sendCodeCmdReq = JsonUtil.copy(apiSendCodeCmdReq, CmdSendCodeReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.sendCode(sendCodeCmdReq));
    }

    public BaseResp checkCode(ApiCheckCodeCmdReq apiCheckCodeCmdReq) {
        //转化参数
        CheckCodeCmdReq sendCodeCmdReq = JsonUtil.copy(apiCheckCodeCmdReq, CheckCodeCmdReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.checkCode(sendCodeCmdReq));
    }
}
