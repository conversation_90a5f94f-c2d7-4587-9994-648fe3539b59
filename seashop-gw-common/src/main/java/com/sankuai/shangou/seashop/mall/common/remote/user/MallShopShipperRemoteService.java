package com.sankuai.shangou.seashop.mall.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopShipperQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopDefaultShipperResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/1/13/013
 * @description:
 */
@Service
@Slf4j
public class MallShopShipperRemoteService {

    @Resource
    private ShopShipperQueryFeign shopShipperQueryFeign;

    public QueryShopDefaultShipperResp queryShopDefaultShipperList(QueryShopShipperReq queryShopShipperReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopShipperQueryFeign.queryShopDefaultShipperList(queryShopShipperReq));
    }

}
