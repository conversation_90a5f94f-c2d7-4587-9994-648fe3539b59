package com.sankuai.shangou.seashop.seller.common.remote.erp;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/01/16 13:48
 */
@Service
@Slf4j
public class SellerErpProductRemoteService {

    //@Resource
    //private ErpMtGoodsQueryFeign erpMtGoodsQueryFeign;

    public Boolean hasBind(Long shopId, Long productId) {
        return true;
        //try {
        //    ErpMtIsBindGoodsReq req = new ErpMtIsBindGoodsReq();
        //    req.setShopId(shopId);
        //    req.setProductId(productId);
        //    log.info("[MT] shopId:{} productId:{} 是否绑定牵牛花", shopId, productId);
        //    ErpMtIsBindGoodsResp resp = ThriftResponseHelper.executeThriftCall(() -> erpMtGoodsQueryFeign.isBind(req));
        //    log.info("[MT] shopId:{} productId:{} 是否绑定牵牛花结果:{}", shopId, productId, resp.getHasBind());
        //    return resp.getHasBind();
        //}
        //catch (Exception e) {
        //    log.error("[MT] shopId:{} productId:{} 是否绑定牵牛花异常", shopId, productId, e);
        //    throw new BusinessException("牵牛花接口异常, 查询是否绑定牵牛花商品失败");
        //}
    }
}
