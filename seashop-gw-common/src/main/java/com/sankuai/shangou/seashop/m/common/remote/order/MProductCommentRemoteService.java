package com.sankuai.shangou.seashop.m.common.remote.order;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/01/19 10:34
 */
@Service
public class MProductCommentRemoteService {

    //@Resource
    //private ProductCommentCmdFeign productCommentCmdFeign;

    @Resource
    private ProductCommentQueryFeign productCommentQueryFeign;

    //public BaseResp hideProductCommentForPlatForm(HideProductCommentReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> productCommentCmdFeign.hideProductCommentForPlatForm(request));
    //}

    public BasePageResp<ProductCommentResp> queryProductCommentForPlatform(QueryProductCommentReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCommentQueryFeign.queryProductCommentForPlatform(request));
    }

}
