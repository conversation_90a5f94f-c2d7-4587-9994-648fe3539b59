package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryApplyCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryApplyQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryApplyCategoryAndFormDataReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyDetailReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveBusinessCategoryApplyReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveSupplyCustomFormReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryLastCategoryResp;

/**
 * <AUTHOR>
 * @date 2024/01/19 11:00
 */
@Service
public class SellerBusinessCategoryApplyRemoteService {

    @Resource
    private BusinessCategoryApplyCmdFeign businessCategoryApplyCmdFeign;

    @Resource
    private BusinessCategoryApplyQueryFeign businessCategoryApplyQueryFeign;

    public BaseResp saveBusinessCategoryApply(SaveBusinessCategoryApplyReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyCmdFeign.saveBusinessCategoryApply(request));
    }

    public BaseResp supplyCustomForm(SaveSupplyCustomFormReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyCmdFeign.supplyCustomForm(request));
    }

    public BasePageResp<BusinessCategoryApplyResp> queryPageForSeller(QueryBusinessCategoryApplyPageReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyQueryFeign.queryPageForSeller(request));
    }

    public BusinessCategoryApplyDetailResp queryDetailForSeller(QueryBusinessCategoryApplyDetailReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyQueryFeign.queryDetailForSeller(request));
    }

    public QueryLastCategoryResp queryApplyCategoryAndFormData(QueryApplyCategoryAndFormDataReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyQueryFeign.queryApplyCategoryAndFormData(request));
    }

}
