package com.sankuai.shangou.seashop.seller.common.remote.product;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.product.thrift.core.ProductAuditQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductAuditQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditDetailResp;
import com.sankuai.shangou.seashop.seller.common.convert.SellerProductConvert;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductAuditDetailResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2024/1/16/016
 * @description:
 */
@Service
public class SellerProductAuditRemoteService {

    @Resource
    private ProductAuditQueryFeign productAuditQueryFeign;
    @Resource
    private SellerProductConvert SellerProductConvert;

    //public BasePageResp<ProductAuditPageResp> queryProductAudit(QueryProductAuditReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> productAuditQueryFeign.queryProductAudit(request));
    //}

    public ApiProductAuditDetailResp queryProductAuditDetail(ProductAuditQueryDetailReq request) {
        ProductAuditDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> productAuditQueryFeign.queryProductAuditDetail(request));
        ApiProductAuditDetailResp apiResp = new ApiProductAuditDetailResp();
        apiResp.setProductAudit(SellerProductConvert.convertApiProductDetailDto(resp.getProductAudit()));
        apiResp.setOriginProduct(SellerProductConvert.convertApiProductDetailDto(resp.getOriginProduct()));
        apiResp.setShowCompare(resp.getShowCompare());

        return apiResp;
    }
}
