package com.sankuai.shangou.seashop.seller.common.remote.user;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberQueryFeign;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.constant.SellerConstant;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdBindContactReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdCheckCodeReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdSendCodeReq;
import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberContactEnum;
import com.sankuai.shangou.seashop.user.thrift.account.request.BindContactCmdReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberListReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberListResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CheckCodeCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdSendCodeReq;

/**
 * @author: lhx
 * @date: 2024/1/15/015
 * @description:
 */
@Service
public class SellerMemberRemoteService {

    @Resource
    private ShopMemberQueryFeign shopMemberQueryFeign;
    @Resource
    private ShopMemberCmdFeign shopMemberCmdFeign;

    /**
     * 查询多个商家信息
     *
     * @param queryMemberReq
     * @return
     */
    public MemberListResp queryMemberList(QueryMemberListReq queryMemberReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMemberList(queryMemberReq));
    }

    public List<MemberResp> queryMemberListByNames(List<String> userNames) {
        if (CollectionUtils.isEmpty(userNames)) {
            return Collections.emptyList();
        }

        userNames = userNames.stream().distinct().collect(Collectors.toList());
        List<List<String>> userNamesArr = Lists.partition(userNames, SellerConstant.QUERY_LIMIT);
        List<MemberResp> respList = Collections.synchronizedList(new ArrayList<>());
        userNamesArr.parallelStream().forEach(subUserNames -> {
            QueryMemberListReq remoteReq = new QueryMemberListReq();
            remoteReq.setUserNames(subUserNames);
            MemberListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMemberList(remoteReq));
            if (resp != null && CollectionUtils.isNotEmpty(resp.getMemberRespList())) {
                respList.addAll(resp.getMemberRespList());
            }
        });
        return respList;
    }

    public MemberResp queryMember(Integer epAccountId) {
        QueryMemberReq queryMemberReq = new QueryMemberReq();
        queryMemberReq.setEpAccountId(epAccountId);
        return ThriftResponseHelper.executeThriftCall(() -> {
            // 业务逻辑处理
            return shopMemberQueryFeign.queryMember(queryMemberReq);
        });
    }


    public BaseResp sendCode(ApiCmdSendCodeReq apiCmdSendCodeReq) {
        CmdSendCodeReq cmdSendCodeReq = JsonUtil.copy(apiCmdSendCodeReq, CmdSendCodeReq.class);
        //调用服务
        ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.sendCode(cmdSendCodeReq));
        //转化出参
        return BaseResp.of();
    }

    public BaseResp checkCode(ApiCmdCheckCodeReq CmdCheckCodeReq) {
        //转化入参
        CheckCodeCmdReq sendCodeCmdReq = JsonUtil.copy(CmdCheckCodeReq, CheckCodeCmdReq.class);
        //调用服务
        ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.checkCode(sendCodeCmdReq));
        //转化出参
        return BaseResp.of();
    }

    public BaseResp bindContact(ApiCmdBindContactReq apiBindContactCmdReq) {
        BindContactCmdReq bindContactCmdReq = JsonUtil.copy(apiBindContactCmdReq, BindContactCmdReq.class);
        bindContactCmdReq.setUsertype(Objects.requireNonNull(MemberContactEnum.Provider.getEnumByCode(apiBindContactCmdReq.getType())).getValue());
        ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.bindContact(bindContactCmdReq));
        return BaseResp.of();
    }

    public BaseResp logout(Integer epAccountId) {
        BaseIdReq baseIdReq = new BaseIdReq() {
            {
                setId(epAccountId.longValue());
            }
        };
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.logout(baseIdReq));
    }

}
