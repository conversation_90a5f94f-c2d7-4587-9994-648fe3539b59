package com.sankuai.shangou.seashop.seller.common.remote.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class CompleteTaskBo {

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 总记录数。多sheet导出的是所有sheet的数据总数
     */
    private Integer totalNum;

    /**
     * 成功数。
     */
    private Integer successNum;

    /**
     * 失败数
     */
    private Integer failedNum;

    /**
     * 任务执行结果。如果执行失败内容为部分异常内容
     */
    private String executeResult;
    /**
     * 文件路径
     */
    private String filePath;

}
