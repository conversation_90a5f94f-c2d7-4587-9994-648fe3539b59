package com.sankuai.shangou.seashop.seller.common.remote;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCustomerServiceCmdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCustomerServiceQueryPageReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiLoginHiChatReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiCustomerServiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.CustomerServiceCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.CustomerServiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CustomerServiceCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CustomerServiceQueryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.LoginHiChatReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceResp;

/**
 * @description:
 * @author: LXH
 **/
@Service
public class SellerCustomerServiceRemoteService {
    @Resource
    private CustomerServiceCmdFeign customerServiceCmdFeign;
    @Resource
    private CustomerServiceQueryFeign customerServiceQueryFeign;


    /**
     * 查询客服列表
     *
     * @param baseIdReq 请求
     * @return 客服列表
     */
    public BasePageResp<ApiCustomerServiceResp> queryPage(ApiCustomerServiceQueryPageReq baseIdReq) {
        // 参数对象转换
        CustomerServiceQueryPageReq customerServiceQueryPageReq = JsonUtil.copy(baseIdReq, CustomerServiceQueryPageReq.class);
        BasePageResp<CustomerServiceResp> customerServiceRespList = ThriftResponseHelper.executeThriftCall(() -> customerServiceQueryFeign.queryPage(customerServiceQueryPageReq));
        return PageResultHelper.transfer(customerServiceRespList, ApiCustomerServiceResp.class);
    }

    /**
     * 查询客服详情
     *
     * @param baseIdReq 请求
     * @return 客服详情
     */
    public ApiCustomerServiceResp queryDetail(BaseIdReq baseIdReq) {
        CustomerServiceResp customerServiceResp = ThriftResponseHelper.executeThriftCall(() -> customerServiceQueryFeign.queryDetail(baseIdReq));
        return JsonUtil.copy(customerServiceResp, ApiCustomerServiceResp.class);
    }

    /**
     * 添加客服
     *
     * @param customerServiceCmdReq 请求
     * @return 添加结果
     */
    public BaseResp add(ApiCustomerServiceCmdReq customerServiceCmdReq) {
        // 参数对象转换
        CustomerServiceCmdReq customerServiceCmdReq1 = JsonUtil.copy(customerServiceCmdReq, CustomerServiceCmdReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> customerServiceCmdFeign.add(customerServiceCmdReq1));
    }

    /**
     * 更新客服
     *
     * @param customerServiceCmdReq 请求
     * @return 更新结果
     */
    public BaseResp update(ApiCustomerServiceCmdReq customerServiceCmdReq) {
        // 参数对象转换
        CustomerServiceCmdReq customerServiceCmdReq1 = JsonUtil.copy(customerServiceCmdReq, CustomerServiceCmdReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> customerServiceCmdFeign.update(customerServiceCmdReq1));
    }

    /**
     * 删除客服
     *
     * @param customerServiceCmdReq 请求
     * @return 删除结果
     */
    public BaseResp delete(ApiCustomerServiceCmdReq customerServiceCmdReq) {
        // 参数对象转换
        CustomerServiceCmdReq customerServiceCmdReq1 = JsonUtil.copy(customerServiceCmdReq, CustomerServiceCmdReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> customerServiceCmdFeign.delete(customerServiceCmdReq1));
    }

    /**
     * 登录HiChat
     *
     * @param loginHiChatReq 请求
     * @return 登录结果
     */
    public String loginHiChat(ApiLoginHiChatReq loginHiChatReq) {
        // 参数对象转换
        LoginHiChatReq loginHiChatReq1 = JsonUtil.copy(loginHiChatReq, LoginHiChatReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> customerServiceCmdFeign.loginHiChat(loginHiChatReq1));
    }

    public Long getHiChatUnReadCount(Long managerId) {
        return ThriftResponseHelper.executeThriftCall(() -> customerServiceCmdFeign.getHiChatUnReadCount(managerId));
    }

}
