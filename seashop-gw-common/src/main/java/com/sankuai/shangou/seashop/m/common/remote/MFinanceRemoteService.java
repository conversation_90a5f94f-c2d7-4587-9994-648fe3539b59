package com.sankuai.shangou.seashop.m.common.remote;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.thrift.finance.SettledQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@Service
public class MFinanceRemoteService {

    //@Resource
    //private CashDepositQueryFeign cashDepositQueryFeign;
    //
    //@Resource
    //private CashDepositRefundCmdFeign cashDepositRefundCmdFeign;
    //
    //@Resource
    //private CashDepositRefundQueryFeign cashDepositRefundQueryFeign;
    //
    //@Resource
    //private CashDepositDetailCmdFeign cashDepositDetailCmdFeign;
    //
    //@Resource
    //private CashDepositDetailQueryFeign cashDepositDetailQueryFeign;
    //
    //@Resource
    //private FinanceQueryFeign financeQueryFeign;
    //
    //@Resource
    //private PendingSettlementQueryFeign pendingSettlementQueryFeign;

    @Resource
    private SettledQueryFeign settledQueryFeign;

    //@Resource
    //private SettlementConfigQueryFeign settlementConfigQueryFeign;
    //
    //@Resource
    //private SettlementConfigCmdFeign settlementConfigCmdFeign;

    ///**
    // * 保证金查询
    // *
    // * @param shopIdListReq
    // * @return
    // */
    //public BasePageResp<CashDepositResp> queryCashDepositListByShopId(ShopIdListReq shopIdListReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> cashDepositQueryFeign.queryListByShopId(shopIdListReq));
    //}
    //
    ///**
    // * 保证金退款申请查询
    // *
    // * @param request
    // * @return
    // */
    //public BasePageResp<CashDepositRefundResp> queryCashDepositRefundList(CashDepositRefundQueryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundQueryFeign.refundList(request));
    //}
    //
    ///**
    // * 通过ID查询审核信息
    // *
    // * @param id
    // * @return
    // */
    //public CashDepositRefundDetailResp refundDetail(Long id) {
    //    return ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundQueryFeign.refundDetail(id));
    //}
    //
    ///**
    // * 保证金扣款
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp cashDepositDetailDeduction(DeductionReq request) {
    //    request.checkParameter();
    //    return ThriftResponseHelper.executeThriftCall(() -> cashDepositDetailCmdFeign.deduction(request));
    //}
    //
    ///**
    // * 保证金明细分页查询
    // *
    // * @param request
    // * @return
    // */
    //public BasePageResp<CashDepositDetailResp> cashDepositDetailPageList(CashDepositDetailQueryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> cashDepositDetailQueryFeign.pageList(request));
    //}
    //
    ///**
    // * 保证金退款申请拒绝
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp cashDepositRefundRefuse(CashDepositRefundRefuseReq request) {
    //    request.checkParameter();
    //    return ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundCmdFeign.refuse(request));
    //}
    //
    ///**
    // * 保证金退款申请确认
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp cashDepositRefundConfirm(CashDepositRefundConfirmReq request) {
    //    request.checkParameter();
    //    return ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundCmdFeign.confirm(request));
    //}
    //
    ///**
    // * 获取财务首页数据
    // *
    // * @return
    // */
    //public FinanceIndexResp getFinanceIndex() {
    //    BaseIdReq request = new BaseIdReq();
    //    return ThriftResponseHelper.executeThriftCall(() -> financeQueryFeign.getFinanceIndex(request));
    //}
    //
    ///**
    // * 获取待结算列表
    // *
    // * @param request
    // * @return
    // */
    //public BasePageResp<PendingSettlementResp> getPendingSettlementList(PendingSettlementQryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.getPendingSettlementList(request));
    //}
    //
    ///**
    // * 获取平台佣金总额
    // *
    // * @return
    // */
    //public PlatCommissionResp getPlatCommission() {
    //    return ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.getPlatCommission());
    //}
    //
    ///**
    // * 分页查询待结算订单列表
    // *
    // * @param request
    // * @return
    // */
    //public BasePageResp<PendingSettlementOrderResp> pendingSettlementOrderPageList(PendingSettlementOrderQryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.pageList(request));
    //}
    //
    ///**
    // * 根据订单id查询订单详情
    // *
    // * @param orderId
    // * @return
    // */
    //public PendingSettlementOrderResp getPendingSettlementOrderDetailByOrderId(String orderId) {
    //    OrderIdQryReq request = new OrderIdQryReq();
    //    request.setOrderId(orderId);
    //    return ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.getDetailByOrderId(request));
    //}

    /**
     * 已结算列表查询
     *
     * @param request
     * @return
     */
    public BasePageResp<SettledResp> settledPageList(SettledQryReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.pageList(request));
    }

    /**
     * 已结算明细列表查询
     *
     * @param request
     * @return
     */
    public BasePageResp<SettledItemResp> settledItemPageList(SettledItemQryReq request) {
//        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.itemPageList(request));
    }

    //public SettledItemCountResp settledItemCount(SettledItemCountQryReq request) {
    //    request.checkParameter();
    //    return ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.itemCount(request));
    //}
    //
    ///**
    // * 通过订单id查询已结算明细
    // *
    // * @param orderId
    // * @return
    // */
    //public SettlementDetailResp getSettledDetailByOrderId(String orderId) {
    //    OrderIdQryReq request = new OrderIdQryReq();
    //    request.setOrderId(orderId);
    //    request.checkParameter();
    //    return ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.getDetailByOrderId(request));
    //}
    //
    ///**
    // * 获取结算配置
    // *
    // * @return
    // */
    //public SettlementConfigResp getConfig() {
    //    return ThriftResponseHelper.executeThriftCall(() -> settlementConfigQueryFeign.getConfig());
    //}
    //
    ///**
    // * 更新结算配置
    // *
    // * @param request
    // * @return
    // */
    //public BaseResp update(SettlementConfigReq request) {
    //    request.checkParameter();
    //    return ThriftResponseHelper.executeThriftCall(() -> settlementConfigCmdFeign.update(request));
    //}
}
