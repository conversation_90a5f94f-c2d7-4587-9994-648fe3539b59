package com.sankuai.shangou.seashop.seller.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import com.sankuai.shangou.seashop.base.export.enums.TaskType;

/**
 * <AUTHOR>
 */
@ThriftEnum
public enum ExportTaskType implements TaskType {


    /**
     * 任务类型，具有一定规则的等长数字
     * <pre>
     *     类型规则：101150001 => 1 01 15 0001
     *     1. 第一位数字:发起外观。1-平台；2：供应商；3：商家
     *     2. 第二、三位数字：业务类型。01-导出任务
     *     3. 第四、五位数字：业务系统。10-基础服务；11-交易服务；12-营销服务；13-用户服务；14-商品服务；15-订单服务
     *     4，最后四位：具体业务
     * </pre>
     */

    // 订单售后相关
    REFUND_ALL_LIST(201150011, "退款记录-全部订单列表导出"),
    REFUND_WAIT_PROCESS_LIST(201150012, "退款-待处理订单列表导出"),
    RETURN_ALL_LIST(201150013, "退货记录-全部订单列表导出"),
    RETURN_WAIT_PROCESS_LIST(201150014, "退货-待处理订单列表导出"),
    REFUND_BUYER_CANCEL_LIST(201150015, "退款记录-买家取消订单列表导出"),
    RETURN_BUYER_CANCEL_LIST(201150016, "退货-买家取消订单列表导出"),

    // 订单发票导出
    ORDER_INVOICE_LIST(201150002, "订单发票导出"),
    EXPORT_ORDER_DISTRIBUTION(201150003, "导出订单配货单"),

    EXPORT_ORDER_PRODUCT_DISTRIBUTION(201150004, "导出商品配货单"),
    ORDER_PAGE_LIST(201150005, "订单列表导出"),

    // 财务相关
    // 待结算订单导出
    PEND_SETTLE_LIST(201150006, "待结算订单导出"),
    // 已结算列表导出
    SETTLED_LIST(201150007, "已结算列表导出"),
    // 已结算明细列表导出
    SETTLED_ITEM_LIST(201150008, "已结算明细列表导出"),

    // 营销相关
    // 优惠券领用列表
    COUPON_RECORD_LIST(201120010, "优惠券领用列表导出"),

    // 商品相关
    // 商品列表导出
    PRODUCT_PAGE_LIST(201140001, "商品列表导出"),

    // 报表相关
    // 商品报表导出
    PRODUCT_REPORT(201160001, "商品报表导出"),
    // 用户报表导出
    USER_REPORT(201160002, "用户报表导出"),
    // 交易报表导出
    TRADE_REPORT(201160003, "交易报表导出"),
    // 自定义报表导出
    CUSTOM_REPORT(201160004, "自定义报表导出")

    ;

    private final Integer type;
    private final String name;

    ExportTaskType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    @Override
    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    @Override
    public String getName() {
        return name;
    }
}
