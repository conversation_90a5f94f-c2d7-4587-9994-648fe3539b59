package com.sankuai.shangou.seashop.seller.common.remote.product;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.product.thrift.core.SpecificationFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecificationReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2024/07/26 14:11
 */
@Service
public class SpecificationRemote {

    @Resource
    private SpecificationFeign specificationFeign;

    public List<SpecificationResp> listByNameIds(List<Long> nameIds) {
        if (CollUtil.isEmpty(nameIds)) {
            return CollUtil.newArrayList();
        }

        SpecificationReq req = new SpecificationReq();
        req.setPageSize(Integer.MAX_VALUE);
        req.setIds(nameIds);
        BasePageResp<SpecificationResp> resp = ThriftResponseHelper.executeThriftCall(() -> specificationFeign.query(req));
        return resp == null || resp.getData() == null ? CollUtil.newArrayList() : resp.getData();
    }
}
