package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.account.PrivilegeQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.PrivilegeRespList;
import com.sankuai.shangou.seashop.user.thrift.account.response.UserPrivilegeResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class SellerPrivilegeRemoteService {
    @Resource
    private PrivilegeQueryFeign privilegeQueryFeign;


    public PrivilegeRespList queryPrivilegeList(QueryPrivilegeReq queryPrivilegePageReq) {
        //转化参数
        QueryPrivilegeReq queryPrivilegeReq = JsonUtil.copy(queryPrivilegePageReq, QueryPrivilegeReq.class);
        queryPrivilegeReq.setPlatform(1);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> privilegeQueryFeign.queryPrivilegeList(queryPrivilegeReq));
    }

    public UserPrivilegeResp queryUserPrivilege(QueryUserPrivilegeReq privilegeReq) {
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> privilegeQueryFeign.queryUserPrivilege(privilegeReq));
    }
}
