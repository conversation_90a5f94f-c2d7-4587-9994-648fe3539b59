package com.sankuai.shangou.seashop.mall.common.remote.product.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/29 16:53
 */
@Setter
@Getter
public class RemoteSimpleCategoryBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 上级类目id
     */
    private Long parentCategoryId;

    /**
     * 是否有下级
     */
    private Boolean hasChildren;

    /**
     * 子类目
     */
    private List<RemoteSimpleCategoryBo> children;

}
