package com.sankuai.shangou.seashop.m.common.remote.base;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.PlatformTaskCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.PlatformTaskQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.CompleteTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.CreateTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.ExceptionTaskReq;
import com.sankuai.shangou.seashop.m.common.remote.model.base.CompleteTaskBo;
import com.sankuai.shangou.seashop.m.common.remote.model.base.CreateTaskBo;
import com.sankuai.shangou.seashop.m.common.remote.model.base.ExceptionTaskBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MTaskRemoteService {

    @Resource
    private PlatformTaskCmdFeign platformTaskCmdFeign;
    @Resource
    private PlatformTaskQueryFeign platformTaskQueryFeign;

    /**
     * 创建任务。
     * 任务类型业务系统自己定义，需要唯一，最好具有一定的规则
     *
     * @param createTaskBo 入参
     */
    public Long createTask(CreateTaskBo createTaskBo) {
        CreateTaskReq req = JsonUtil.copy(createTaskBo, CreateTaskReq.class);
        log.info("【平台后台任务】创建任务, 请求参数={}", JsonUtil.toJsonString(req));
        return ThriftResponseHelper.executeThriftCall(() -> platformTaskCmdFeign.createTask(req));
    }

    /**
     * 开始任务
     * 开始任务是直接把任务状态修改为进行中
     *
     * @param taskId 任务id
     */
    public void startTask(Long taskId) {
        BaseIdReq baseIdReq = new BaseIdReq();
        baseIdReq.setId(taskId);
        log.info("【平台后台任务】开启任务, 请求参数={}", JsonUtil.toJsonString(baseIdReq));
        ThriftResponseHelper.executeThriftCall(() -> platformTaskCmdFeign.start(baseIdReq));
    }

    /**
     * 完成任务
     *
     * @param completeTaskBo 入参
     */
    public void completeTask(CompleteTaskBo completeTaskBo) {
        CompleteTaskReq req = JsonUtil.copy(completeTaskBo, CompleteTaskReq.class);
        log.info("【平台后台任务】完成任务, 请求参数={}", JsonUtil.toJsonString(req));
        ThriftResponseHelper.executeThriftCall(() -> platformTaskCmdFeign.complete(req));
    }

    /**
     * 任务异常
     *
     * @param exceptionTaskBo 入参
     */
    public void exceptionTask(ExceptionTaskBo exceptionTaskBo) {
        ExceptionTaskReq req = JsonUtil.copy(exceptionTaskBo, ExceptionTaskReq.class);
        log.info("【平台后台任务】任务异常, 请求参数={}", JsonUtil.toJsonString(req));
        ThriftResponseHelper.executeThriftCall(() -> platformTaskCmdFeign.exception(req));
    }

    //public BasePageResp<PlatformTaskDto> pageList(QueryTaskReq queryReq) {
    //    log.info("【平台后台任务】查询分页列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> platformTaskQueryFeign.pageList(queryReq));
    //}
    //
    //public List<PlatformTaskDto> queryList(QueryTaskReq queryReq) {
    //    log.info("【平台后台任务】查询任务列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
    //    PlatformTaskListResp resp = ThriftResponseHelper.executeThriftCall(() -> platformTaskQueryFeign.queryList(queryReq));
    //    log.info("【平台后台任务】查询任务列表, 返回结果={}", JsonUtil.toJsonString(resp));
    //    return resp.getTaskList();
    //}


}
