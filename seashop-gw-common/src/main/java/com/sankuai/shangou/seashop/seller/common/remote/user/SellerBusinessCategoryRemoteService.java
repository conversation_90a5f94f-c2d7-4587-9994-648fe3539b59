package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryBusinessCategoryTreeReq;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryApplyQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryWaitFinishContractNumReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryTreeResp;

/**
 * <AUTHOR>
 * @date 2024/01/19 11:07
 */
@Service
public class SellerBusinessCategoryRemoteService {

    @Resource
    private BusinessCategoryQueryFeign businessCategoryQueryFeign;
    @Resource
    private BusinessCategoryApplyQueryFeign businessCategoryApplyQueryFeign;

    public BusinessCategoryTreeResp queryBusinessCategoryTree(QueryBusinessCategoryTreeReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryQueryFeign.queryBusinessCategoryTree(req));
    }

    public BasePageResp<BusinessCategoryResp> queryPage(QueryBusinessCategoryPageReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryQueryFeign.queryPage(req));
    }

    public Integer queryWaitFinishContractNum(Long shopId) {
        QueryWaitFinishContractNumReq req = new QueryWaitFinishContractNumReq();
        req.setShopId(shopId);
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyQueryFeign.queryWaitFinishContractNum(req)).getApplyWaitFinishNum();
    }

}
