package com.sankuai.shangou.seashop.m.common.remote.product.model;

import java.math.BigDecimal;
import java.util.List;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:02
 */
@Getter
@Setter
@Builder
public class RemoteCategoryBo {

    /**
     * 类目Id
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 显示顺序
     */
    private Long displaySequence;

    /**
     * 父类目Id
     */
    private Long parentCategoryId;

    /**
     * 类目深度
     */
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    private String path;

    /**
     * 是否有子类目
     */
    private Boolean hasChildren;

    /**
     * 佣金比例
     */
    private BigDecimal commissionRate;

    /**
     * 是否显示
     */
    private Boolean whetherShow;

    /**
     * 自定义表单Id
     */
    private Long customFormId;

    /**
     * 自定义表单名称
     */
    private String customFormName;

    /**
     * 子类目
     */
    private List<RemoteCategoryBo> children;

    /**
     * 上级类目的id集合
     */
    private List<Long> parentIds;

    /**
     * 完整的路径 上级类目id的集合 + 当前类目id
     */
    private List<Long> fullIds;

    /**
     * 类目全路径名称
     */
    private String fullCategoryName;

    /**
     * 类目全路径名称集合
     */
    private List<String> categoryNameList;

    /**
     * 保证金
     */
    private BigDecimal cashDeposit;

    /**
     * 允许七天无理由退货
     */
    private Boolean enableNoReasonReturn;

    /**
     * 是否默认
     */
    private Boolean defaultStatus;

}
