package com.sankuai.shangou.seashop.m.common.convert;

import org.mapstruct.Mapper;

import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdSendCoupon;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.SendCouponCmdReq;

/**
 * @description:
 * @author: LXH
 **/
@Mapper(componentModel = "spring")
public interface ApiCouponConverter {

    SendCouponCmdReq apiCmdSendCoupon2CouponSendReq(ApiCmdSendCoupon sendCoupon);
}
