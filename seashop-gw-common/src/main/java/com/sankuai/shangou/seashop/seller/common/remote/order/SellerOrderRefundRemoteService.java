package com.sankuai.shangou.seashop.seller.common.remote.order;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SellerOrderRefundRemoteService {

    @Resource
    private OrderRefundQueryFeign orderRefundQueryFeign;
    //@Resource
    //private OrderRefundCmdFeign orderRefundCmdFeign;

    ///**
    // * 查询售后详情
    // *
    // * @param queryReq 请求参数
    // * @return 售后详情
    // */
    //public SellerRefundDetailResp queryDetail(SellerQueryRefundDetailReq queryReq) {
    //    log.info("【订单】供应商查询售后明细, 请求参数: {}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.sellerQueryDetail(queryReq));
    //}
    //
    ///**
    // * 查询售后日志
    // *
    // * @param queryReq 请求参数
    // * @return 售后日志
    // */
    //public RefundLogListResp queryRefundLog(BaseIdReq queryReq) {
    //    log.info("【订单】供应商查询售后日志, 请求参数: {}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryRefundLog(queryReq));
    //}

    /**
     * 供应商查询售后列表
     *
     * @param queryReq 请求参数
     * @return 售后物流
     */
    public BasePageResp<SellerRefundDto> sellerQueryRefundPage(SellerQueryRefundReq queryReq) {
        log.info("【订单】供应商查询售后列表, 请求参数: {}", JsonUtil.toJsonString(queryReq));
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.sellerQueryRefundPage(queryReq));
    }

    ///**
    // * 查询售后物流
    // *
    // * @param queryReq 请求参数
    // * @return 售后物流
    // */
    //public RefundUserDeliverExpressResp queryUserDeliverExpress(BaseIdReq queryReq) {
    //    log.info("【订单】供应商查询售后物流, 请求参数: {}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryUserDeliverExpress(queryReq));
    //}

    ///**
    // * 供应商审核售后
    // *
    // * @param sellerApproveReq 请求参数
    // * @return 售后物流
    // */
    //public BaseResp sellerApprove(SellerApproveReq sellerApproveReq) {
    //    log.info("【售后】供应商审核售后, 请求参数: {}", JsonUtil.toJsonString(sellerApproveReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.sellerApprove(sellerApproveReq));
    //}

    ///**
    // * 供应商确认收货
    // *
    // * @param sellerApproveReq 请求参数
    // * @return 售后物流
    // */
    //public BaseResp sellerConfirmReceive(SellerConfirmReceiveReq sellerApproveReq) {
    //    log.info("【售后】供应商确认收货, 请求参数: {}", JsonUtil.toJsonString(sellerApproveReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.sellerConfirmReceive(sellerApproveReq));
    //}

}
