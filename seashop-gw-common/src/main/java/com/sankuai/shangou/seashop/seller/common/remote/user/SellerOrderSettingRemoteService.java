package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.OrderSettingCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.OrderSettingQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryOrderSettingResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 11:38
 */
@Service
public class SellerOrderSettingRemoteService {

    @Resource
    private OrderSettingQueryFeign orderSettingQueryFeign;

    @Resource
    private OrderSettingCmdFeign orderSettingCmdFeign;

    public QueryOrderSettingResp queryOrderSetting(QueryOrderSettingReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> orderSettingQueryFeign.queryOrderSetting(req));
    }

    public BaseResp addOrUpdateOrderSetting(SaveOrderSettingReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> orderSettingCmdFeign.addOrUpdateOrderSetting(req));
    }
}
