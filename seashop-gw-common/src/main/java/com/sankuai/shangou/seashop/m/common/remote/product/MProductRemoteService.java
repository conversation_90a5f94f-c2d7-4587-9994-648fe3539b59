package com.sankuai.shangou.seashop.m.common.remote.product;

import com.google.common.collect.Lists;
import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.m.common.convert.MProductConvert;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiProductDetailDto;
import com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.SkuQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.common.EsScrollClearReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.MStatisticalProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductLadderPriceResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/02 18:17
 */
@Service
public class MProductRemoteService {

    @Resource
    private ProductQueryFeign productQueryFeign;
    @Resource
    private SkuQueryFeign skuQueryFeign;
    @Resource
    private ProductCmdFeign productCmdFeign;
    @Resource
    private MProductConvert mProductConvert;
    @Resource
    private StorageClient storageClient;

    /**
     * 查询商品列表
     *
     * @param request 查询条件
     * @return 商品列表
     */
    public BasePageResp<ProductPageResp> queryProduct(QueryProductReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProduct(request));
    }

    /**
     * 根据商品id 查询sku信息
     *
     * @param productIds 商品id
     * @return sku信息
     */
    public List<SkuQueryResp> querySku(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        List<SkuQueryResp> skuList = new ArrayList<>();
        List<List<Long>> productIdsArr = Lists.partition(productIds, CommonConstant.QUERY_LIMIT);
        productIdsArr.forEach(subProductIds -> {
            SkuQueryReq request = new SkuQueryReq();
            request.setProductIds(subProductIds);
            SkuListResp resp = ThriftResponseHelper.executeThriftCall(() -> skuQueryFeign.querySkuList(request));
            if (resp != null && CollectionUtils.isNotEmpty(resp.getSkuList())) {
                skuList.addAll(resp.getSkuList());
            }
        });
        return skuList;
    }

    /**
     * 根据商品id 查询阶梯价格
     *
     * @param productIds 商品id
     * @return 阶梯价格
     */
    public List<LadderPriceDto> queryLadderPrice(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        List<LadderPriceDto> ladderPriceList = new ArrayList<>();
        List<List<Long>> productIdsArr = Lists.partition(productIds, CommonConstant.QUERY_LIMIT);
        productIdsArr.forEach(subProductIds -> {
            QueryLadderPriceReq request = new QueryLadderPriceReq();
            request.setProductIds(subProductIds);
            ProductLadderPriceResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.getLadderPriceBoList(request));
            if (resp != null && CollectionUtils.isNotEmpty(resp.getLadderPriceList())) {
                ladderPriceList.addAll(resp.getLadderPriceList());
            }
        });
        return ladderPriceList;
    }


    public BaseResp batchSaveProductSequence(ProductUpdateSequenceReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchSaveProductSequence(request));
    }

    public BaseResp batchUpdateVirtualSales(ProductUpdateVirtualSaleCountsReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchUpdateVirtualSales(request));
    }

    public BaseResp batchViolationOffSale(ProductViolationReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.batchViolationOffSale(request));
    }

    public BaseImportResp importViolationOffSale(ProductImportReq request) {
        return dealImportResp(ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.importViolationOffSale(dealProductImport(request))));
    }

    public BaseImportResp platformImportProduct(ProductImportReq request) {
        return dealImportResp(ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.platformImportProduct(dealProductImport(request))));
    }

    public MStatisticalProductResp queryMStatisticalProduct() {
        return ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryMStatisticalProduct());
    }

    public void clearScrollId(String scrollId) {
        EsScrollClearReq req = new EsScrollClearReq();
        req.setScrollId(scrollId);
        ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.clearScrollId(req));
    }

    public ApiProductDetailDto queryProductDetail(ProductQueryDetailReq request) {
        ProductDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductDetail(request));
        if (resp == null) {
            return null;
        }
        return mProductConvert.convertApiProductDetailDto(resp.getResult());
    }

    private ProductImportReq dealProductImport(ProductImportReq remoteReq) {
        remoteReq.setFilePath(appendS3UrlHeader(remoteReq.getFilePath()));
        return remoteReq;
    }

    private BaseImportResp dealImportResp(BaseImportResp resp) {
        if (resp == null) {
            return null;
        }
        resp.setFilePath(StringUtils.isEmpty(resp.getFilePath()) ? resp.getFilePath() : appendS3UrlHeader(resp.getFilePath()));
        return resp;
    }

    private String appendS3UrlHeader(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        return storageClient.formatUrl(url);
    }
}
