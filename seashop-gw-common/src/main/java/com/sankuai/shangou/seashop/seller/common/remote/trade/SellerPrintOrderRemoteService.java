package com.sankuai.shangou.seashop.seller.common.remote.trade;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.trade.thrift.core.PrintOrderQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PrintOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PrintOrderResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 11:26
 */
@Service
public class SellerPrintOrderRemoteService {

    @Resource
    private PrintOrderQueryFeign printOrderQueryFeign;

    public PrintOrderResp getOrderPrint(PrintOrderReq printOrderReq) {
        return ThriftResponseHelper.executeThriftCall(() -> printOrderQueryFeign.getOrderPrint(printOrderReq));
    }
}
