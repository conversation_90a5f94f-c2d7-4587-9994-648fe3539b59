package com.sankuai.shangou.seashop.mall.common.remote.product.model;

import java.util.List;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:53
 */
@Getter
@Setter
@Builder
public class RemoteShopCategoryBo {

    /**
     * 店铺分类id
     */
    private Long id;

    /**
     * 店铺分类名称
     */
    private String name;

    /**
     * 上级店铺分类id
     */
    private Long parentCategoryId;

    /**
     * 子分类
     */
    private List<RemoteShopCategoryBo> childList;

}
