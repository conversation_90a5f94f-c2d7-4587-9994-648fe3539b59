package com.sankuai.shangou.seashop.seller.common.remote;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.RegionCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseRegionRes;

@Service
public class SellerRegionRemoteService {
    @Resource
    private RegionQueryFeign regionQueryFeign;

    @Resource
    private RegionCMDFeign regionCMDFeign;

    public List<BaseRegionRes> getRegionByParentId(long parentId) throws TException {
        return ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getRegionByParentId(parentId));
    }


    public String getTreeRegions() throws TException {
        return ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getTreeRegions());
    }

    public List<BaseRegionRes> getAllRegions() {
        return ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getAllRegions());
    }


    public List<BaseRegionRes> getParentRegions(Long id) throws TException {
        return ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getParentRegions(id));
    }


    public List<BaseRegionRes> getSubRegions(Long id) throws TException {
        return ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getSubRegions(id));
    }


    public List<BaseRegionRes> getTrackRegionsById(Long id) throws TException {
        return ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getTrackRegionsById(id));
    }

    public Map<String, AllPathRegionResp> getAllPathRegions(RegionIdsReq regionIdsReq) {
        return ThriftResponseHelper.executeThriftCall(() -> regionQueryFeign.getAllPathRegions(regionIdsReq));
    }
}
