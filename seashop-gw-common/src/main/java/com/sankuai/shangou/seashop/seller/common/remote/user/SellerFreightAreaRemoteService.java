package com.sankuai.shangou.seashop.seller.common.remote.user;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.FreightAreaCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.FreightAreaQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CopyFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateDetailReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 11:34
 */
@Service
public class SellerFreightAreaRemoteService {

    @Resource
    private FreightAreaCmdFeign freightAreaCmdFeign;

    @Resource
    private FreightAreaQueryFeign freightAreaQueryFeign;

    public BaseResp deleteFreightTemplate(DeleteFreightTemplateReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> freightAreaCmdFeign.deleteFreightTemplate(req));
    }

    public BaseResp addFreightTemplate(AddFreightReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> freightAreaCmdFeign.addFreightTemplate(req));
    }

    public BaseResp updateFreightTemplate(UpdateFreightReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> freightAreaCmdFeign.updateFreightTemplate(req));
    }

    public QueryFreightTemplateResp queryFreightTemplateList(QueryFreightTemplateReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> freightAreaQueryFeign.queryFreightTemplateList(req));
    }

    public QueryFreightTemplateDetailResp queryFreightTemplateDetail(QueryFreightTemplateDetailReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> freightAreaQueryFeign.queryFreightTemplateDetail(req));
    }

    public BaseResp copyFreightTemplate(CopyFreightReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> freightAreaCmdFeign.copyFreightTemplate(req));
    }
}
