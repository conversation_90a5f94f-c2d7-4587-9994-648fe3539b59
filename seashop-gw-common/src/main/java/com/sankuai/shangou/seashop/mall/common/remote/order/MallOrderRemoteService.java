package com.sankuai.shangou.seashop.mall.common.remote.order;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.OrderStatisticsQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryUserOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.StatsUserPurchaseSkuReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.OrderAndItemFlatListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.StatsUserPurchaseSkuResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MallOrderRemoteService {

    @Resource
    private OrderQueryFeign orderQueryFeign;
    @Resource
    private OrderStatisticsQueryFeign orderStatisticsQueryFeign;
    //@Resource
    //private OrderCmdFeign orderCmdFeign;


    /**
     * 分页查询用户采购SKU以及汇总信息
     *
     * @param req 查询参数
     * @return 订单列表
     */
    public StatsUserPurchaseSkuResp statsUserPurchaseSku(StatsUserPurchaseSkuReq req) {
        log.info("【订单统计】供应商采购统计, req={}", JsonUtil.toJsonString(req));
        return ThriftResponseHelper.executeThriftCall(() -> orderStatisticsQueryFeign.statsUserPurchaseSku(req));
    }

    ///**
    // * PC端商家订单分页列表
    // *
    // * @param queryReq 查询参数
    // * @return 订单列表
    // */
    //public BasePageResp<OrderInfoDto> pageQueryUserOrder(QueryUserOrderReq queryReq) {
    //    log.info("【订单】PC端商家订单分页列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.pageQueryUserOrder(queryReq));
    //}

    public OrderAndItemFlatListResp getScrollIdForUserExport(QueryUserOrderReq pageParam) {
        log.info("【订单】获取商家分页查询订单列表的scrollId, 请求参数: {}", JsonUtil.toJsonString(pageParam));
        OrderAndItemFlatListResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getScrollIdForUserExport(pageParam));
        log.debug("【订单】获取商家分页查询订单列表的scrollId, 返回结果: {}", JsonUtil.toJsonString(resp));
        return resp;
    }

    public List<OrderAndItemInfoDto> listOrderAndItemFlatByScroll(EsScrollQueryReq request) {
        log.info("【订单】通过scrollId查询订单和明细列表, 请求参数: {}", JsonUtil.toJsonString(request));
        OrderAndItemFlatListResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.listOrderAndItemFlatByScroll(request));
        if (resp == null || CollUtil.isEmpty(resp.getDataList())) {
            return null;
        }
        return resp.getDataList();
    }

    ///**
    // * 订单详情
    // *
    // * @param queryReq 查询参数
    // * @return 订单详情
    // */
    //public OrderDetailResp queryOrderDetail(QueryOrderDetailReq queryReq) {
    //    log.info("【订单】查询订单详情, 请求参数={}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryDetail(queryReq));
    //}
    //
    ///**
    // * 查询订单操作日志
    // *
    // * @param orderId 订单ID
    // * @return 订单操作日志
    // */
    //public List<OrderOperationLogResp> queryOrderOperationLog(String orderId) {
    //    log.info("【订单】查询订单操作日志, 请求参数={}", orderId);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryOrderLog(orderId));
    //}
    //
    ///**
    // * 取消支付
    // *
    // * @param cancelPayReq 取消订单请求参数
    // * @return 取消订单结果
    // */
    //public BaseResp cancelPay(CancelPayReq cancelPayReq) {
    //    log.info("【订单】取消支付, 请求参数={}", JsonUtil.toJsonString(cancelPayReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.cancelPay(cancelPayReq));
    //}
    //
    ///**
    // * 取消订单
    // *
    // * @param cancelOrderReq 取消订单请求参数
    // * @return 取消订单结果
    // */
    //public BaseResp cancelOrder(CancelOrderReq cancelOrderReq) {
    //    log.info("【订单】取消订单, 请求参数={}", JsonUtil.toJsonString(cancelOrderReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.cancelOrder(cancelOrderReq));
    //}
    //
    ///**
    // * 重新购买
    // *
    // * @param reBuyReq 请求参数
    // */
    //public BaseResp reBuy(ReBuyReq reBuyReq) {
    //    log.info("【订单】订单重新加购, 请求参数={}", JsonUtil.toJsonString(reBuyReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.reBuy(reBuyReq));
    //}
    //
    ///**
    // * 延迟收货
    // *
    // * @param delayReceiveReq 请求参数
    // */
    //public BaseResp delayReceive(DelayReceiveReq delayReceiveReq) {
    //    log.info("【订单】延迟收货, 请求参数={}", JsonUtil.toJsonString(delayReceiveReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.delayReceive(delayReceiveReq));
    //}
    //
    ///**
    // * 确认收货
    // *
    // * @param confirmReceiveReq 请求参数
    // */
    //public BaseResp confirmReceive(ConfirmReceiveReq confirmReceiveReq) {
    //    log.info("【订单】确认收货, 请求参数={}", JsonUtil.toJsonString(confirmReceiveReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.confirmReceive(confirmReceiveReq));
    //}
    //
    ///**
    // * 查询订单物流信息
    // *
    // * @param queryReq 请求参数
    // */
    //public OrderWayBillResp getUserOrderWayBill(QueryUserOrderWayBillReq queryReq) {
    //    log.info("【订单】获取用户订单物流信息, 请求参数={}", queryReq);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getUserOrderWayBill(queryReq));
    //}

}
