package com.sankuai.shangou.seashop.m.common.enums;

import com.sankuai.shangou.seashop.base.export.enums.TaskType;

/**
 * <AUTHOR>
 */
public enum ExportTaskType implements TaskType {

    ORDER_LIST(101150000, "订单列表导出"),
    REFUND_PAGE_LIST(101150010, "全部退款订单列表导出"),
    // 限时购列表导出
    FLASH_SALE_LIST(101150020, "限时购列表导出"),
    // 专项价列表导出
    EXCLUSIVE_PRICE_LIST(101150030, "专享价列表导出"),
    MEMBER_LIST(101130001, "商家列表导出"),
    // 订单发票导出
    ORDER_INVOICE_LIST(101150040, "订单发票导出"),
    // 商品导出
    PRODUCT_PAGE_LIST(101150050, "商品列表导出"),
    // 类目导出
    CATEGORY_LIST(101150051, "类目导出"),
    // 商品审核拒绝导出
    PRODUCT_AUDIT_REFUSE_LIST(101150060, "商品审核拒绝列表导出"),
    //待结算订单合计列表导出
    PEND_SETTLE_TOTAL_LIST(101150070, "待结算订单合计列表导出"),
    //待结算订单列表导出
    PEND_SETTLE_LIST(101150080, "待结算订单列表导出"),
    // 已结算明细列表导出
    SETTLED_ITEM_LIST(101150090, "已结算明细列表导出"),
    // 供应商维度已结算列表导出
    SETTLED_BY_SHOP_LIST(101150100, "供应商维度已结算列表导出"),
    // 订单维度已结算列表导出
    SETTLED_BY_ORDER_LIST(101150110, "订单维度已结算列表导出"),
    ;

    private final Integer type;
    private final String name;

    ExportTaskType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    @Override
    public Integer getType() {
        return type;
    }

    @Override
    public String getName() {
        return name;
    }
}
