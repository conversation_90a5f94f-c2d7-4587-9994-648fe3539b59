package com.sankuai.shangou.seashop.m.common.remote.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.OrderStatisticsReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.OrderAndItemFlatListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class MOrderRemoteService {

    @Resource
    private OrderQueryFeign orderQueryFeign;
    //@Resource
    //private OrderStatisticsQueryFeign orderStatisticsQueryFeign;

    /**
     * 获取昨日订单统计数据
     *
     * @return
     */
    public OrderStatisticsResp getYesterdayOrderStatistics() {
        OrderStatisticsReq request = new OrderStatisticsReq();
        DateTime yesterday = DateUtil.yesterday();
        request.setBeginTime(DateUtil.beginOfDay(yesterday));
        request.setEndTime(DateUtil.endOfDay(yesterday));
        log.info("【订单】获取昨日订单统计数据, 请求参数: {}", JsonUtil.toJsonString(request));
        return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getOrderStatistics(request));
    }

    ///**
    // * 订单统计列表（财务管理首页统计）
    // *
    // * @param request
    // * @return
    // */
    //public OrderStatisticsListResp getOrderStatisticsList(OrderStatisticsReq request) {
    //    log.info("【订单】订单统计列表（财务管理首页统计）, 请求参数: {}", JsonUtil.toJsonString(request));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getOrderStatisticsList(request));
    //}
    //
    //public BasePageResp<OrderAndItemInfoDto> exportForPlatform(QueryPlatformOrderReq pageParam) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.exportForPlatform(pageParam));
    //}
    //
    //public OrderAndItemFlatListResp getScrollIdForPlatformExport(QueryPlatformOrderReq pageParam) {
    //    log.info("【订单】获取平台分页查询订单列表的scrollId, 请求参数: {}", JsonUtil.toJsonString(pageParam));
    //    OrderAndItemFlatListResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getScrollIdForPlatformExport(pageParam));
    //    log.info("【订单】获取平台分页查询订单列表的scrollId, 返回结果: {}", JsonUtil.toJsonString(resp));
    //    return resp;
    //}

    //public void clearScrollId(String scrollId) {
    //    EsScrollClearReq req = new EsScrollClearReq();
    //    req.setScrollId(scrollId);
    //    log.info("【订单】清除scrollId, 请求参数: {}", JsonUtil.toJsonString(req));
    //    ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.clearScrollId(req));
    //}

    public List<OrderAndItemInfoDto> listOrderAndItemFlatByScroll(EsScrollQueryReq request) {
        log.info("【订单】通过scrollId查询订单和明细列表, 请求参数: {}", JsonUtil.toJsonString(request));
        OrderAndItemFlatListResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.listOrderAndItemFlatByScroll(request));
        if (resp == null || CollUtil.isEmpty(resp.getDataList())) {
            return null;
        }
        return resp.getDataList();
    }

    ///**
    // * 平台分页查询订单列表
    // *
    // * @param queryReq 查询条件
    // * @return 订单列表
    // */
    //public BasePageResp<OrderInfoDto> pageQueryPlatformOrder(QueryPlatformOrderReq queryReq) {
    //    log.info("【订单】平台分页查询订单列表, 请求参数: {}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.pageQueryPlatformOrder(queryReq));
    //}

    ///**
    // * 平台查询订单详情
    // *
    // * @param queryReq 查询条件
    // * @return 订单详情
    // */
    //public OrderDetailResp queryDetail(QueryOrderDetailReq queryReq) {
    //    log.info("【订单】平台查询订单详情, 请求参数: {}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryDetailForPlatform(queryReq));
    //}

    ///**
    // * 查询订单操作日志
    // *
    // * @param orderId 订单id
    // * @return 订单操作日志
    // */
    //public List<OrderOperationLogResp> queryOrderOperationLog(String orderId) {
    //    log.info("【订单】查询订单操作日志, 请求参数: {}", orderId);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryOrderLog(orderId));
    //}
    //
    ///**
    // * 平台首页交易数据统计
    // *
    // * @return 平台首页交易数据统计
    // */
    //public PlatformIndexTradeDataStatsResp statsPlatformIndexTradeData() {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderStatisticsQueryFeign.statsPlatformIndexTradeData());
    //}
    //
    //public OrderStatisticsResp getOrderStatisticsByMember(OrderStatisticsMemberReq orderStatisticsMemberReq) {
    //    log.info("【订单】获取会员订单统计数据, 请求参数: {}", JsonUtil.toJsonString(orderStatisticsMemberReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getOrderStatisticsByMember(orderStatisticsMemberReq));
    //}
}
