package com.sankuai.shangou.seashop.seller.common.constant;

/**
 * Mafka相关的常量，包括topic名称、消费者组名称等
 *
 * <AUTHOR>
 */
public class MafkaConst {

    // mafka命名空间，用于区分不同的业务，这里固定为waimai
    public static final String DEFAULT_NAMESPACE = "waimai";

    /*
     * topic名称，用于区分不同的业务
     */
    // 导出任务异步处理topic
    public static final String TOPIC_ASYNC_EXPORT_TASK = "seashop_seller_async_task_topic";
    // 导出任务异步处理消费组
    public static final String GROUP_ASYNC_EXPORT_TASK = "seashop_seller_async_task_consumer";


    // 延时时间，单位毫秒
    public static final long DELAY_TIME_ORDER_CHECK = 30 * 60 * 1000;

}
