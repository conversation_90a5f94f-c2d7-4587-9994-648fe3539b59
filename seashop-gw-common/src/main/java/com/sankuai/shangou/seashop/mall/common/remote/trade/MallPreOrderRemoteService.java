package com.sankuai.shangou.seashop.mall.common.remote.trade;

import com.sankuai.shangou.seashop.base.boot.enums.TResultCode;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.thrift.core.PreOrderCmdFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewChangeSkuQuantityReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SubmitOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewChangeSkuQuantityResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MallPreOrderRemoteService {

    //@Resource
    //private PreOrderQueryFeign preOrderQueryFeign;
    @Resource
    private PreOrderCmdFeign preOrderCmdFeign;

    ///**
    // * 获取订单提交防重token
    // *
    // * @param userId 用户ID
    // * @return token
    // */
    //public String getSubmitToken(Long userId) {
    //    log.info("【预订单】获取订单提交防重token");
    //    return ThriftResponseHelper.executeThriftCall(() -> preOrderQueryFeign.getSubmitToken(userId));
    //}

    /**
     * 提交订单
     *
     * @param submitOrderReq 提交订单请求
     * @return 预订单
     */
    public ResultDto<PreviewOrderResp> submitOrder(SubmitOrderReq submitOrderReq) {
        log.debug("【预订单】提交订单, 请求参数={}", JsonUtil.toJsonString(submitOrderReq));
        try {
            return preOrderCmdFeign.submitOrder(submitOrderReq);
        }
        catch (BusinessException e) {
            log.warn("business error", e);
            throw new BusinessException(e.getCode(), e.getMessage());
        }
        catch (Exception e) {
            log.error("thrift call failed", e);
            throw new BusinessException(TResultCode.SERVER_ERROR.value(), "服务端异常");
        }
    }

    ///**
    // * 选择优惠券
    // *
    // * @param chooseCouponReq 选择优惠券请求
    // * @return 优惠券
    // */
    //public ChooseCouponResp chooseCoupon(ChooseCouponReq chooseCouponReq) {
    //    log.debug("【预订单】选择优惠券, 请求参数={}", JsonUtil.toJsonString(chooseCouponReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> preOrderCmdFeign.chooseCoupon(chooseCouponReq));
    //}

    ///**
    // * 选择收货地址
    // *
    // * @param chooseShippingAddressReq 选择收货地址请求
    // * @return 收货地址
    // */
    //public PreviewOrderResp chooseShippingAddress(ChooseShippingAddressReq chooseShippingAddressReq) {
    //    log.debug("【预订单】选择收货地址, 请求参数={}", JsonUtil.toJsonString(chooseShippingAddressReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> preOrderCmdFeign.chooseShippingAddress(chooseShippingAddressReq));
    //}

    /**
     * 修改预订单商品数量
     *
     * @param req 修改预订单商品数量请求
     * @return 预订单
     */
    public ResultDto<PreviewChangeSkuQuantityResp> changePreviewSkuCount(PreviewChangeSkuQuantityReq req) {
        log.debug("【预订单】修改预订单商品数量, 请求参数={}", JsonUtil.toJsonString(req));
        //return ThriftResponseHelper.executeThriftCall(() -> preOrderCmdFeign.changePreviewSkuCount(req));
        try {
            return preOrderCmdFeign.changePreviewSkuCount(req);
        }
        catch (BusinessException e) {
            log.warn("business error", e);
            throw new BusinessException(e.getCode(), e.getMessage());
        }
        catch (Exception e) {
            log.error("thrift call failed", e);
            throw new BusinessException(TResultCode.SERVER_ERROR.value(), "服务端异常");
        }
    }

}
