package com.sankuai.shangou.seashop.seller.common.remote.promotion;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponCmdFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@Service
public class SellerCouponRemoteService {

    @Resource
    private CouponCmdFeign couponCmdFeign;

    @Resource
    private CouponQueryFeign couponQueryFeign;

    /**
     * 新增/编辑优惠券活动
     *
     * @param request
     * @return
     */
    public BaseResp save(CouponSaveReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> couponCmdFeign.save(request));
    }

    /**
     * 结束优惠券活动
     *
     * @param request
     * @return
     */
    public BaseResp endActive(BaseIdReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> couponCmdFeign.endActive(request));
    }

    /**
     * 优惠券分页列表
     *
     * @param request
     * @return
     */
    public BasePageResp<CouponSimpleResp> pageList(CouponQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.pageList(request));
    }

    /**
     * 优惠券详情
     *
     * @param request
     * @return
     */
    public CouponResp getById(BaseIdReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.getById(request));
    }

    /**
     * 查询优惠券关联商品
     *
     * @param request
     * @return
     */
    public BasePageResp<CouponProductDto> queryCouponProductPage(CouponProductQueryReq request) {
        request.checkParameter();

        return ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.queryCouponProductPage(request));
    }
}
