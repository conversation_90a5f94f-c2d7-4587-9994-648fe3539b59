package com.sankuai.shangou.seashop.m.common.remote.product;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.product.model.RemoteCategoryBo;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryTreeResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/19 9:31
 */
@Service
public class MCategoryRemoteService {

    //@Resource
    //private CategoryCmdFeign categoryCmdFeign;

    @Resource
    private CategoryQueryFeign categoryQueryFeign;

    //public BaseResp createCategory(SaveCategoryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.createCategory(request));
    //}
    //
    //public BaseResp updateCategory(SaveCategoryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.updateCategory(request));
    //}
    //
    //public BaseResp updateCategoryParam(UpdateCategoryParamReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.updateCategoryParam(request));
    //}
    //
    //public BaseResp deleteCategory(BaseIdReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.deleteCategory(request));
    //}
    //
    //public BaseResp bindCustomForm(BindCustomFormReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.bindCustomForm(request));
    //}

    public CategoryTreeResp queryCategoryTree(QueryCategoryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTree(request));
    }

    public List<RemoteCategoryBo> queryCategoryBo(QueryCategoryReq request) {
        CategoryTreeResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTree(request));
        if (resp == null || StringUtils.isEmpty(resp.getResult())) {
            return Collections.emptyList();
        }

        return JsonUtil.parseArray(resp.getResult(), RemoteCategoryBo.class);
    }

}
