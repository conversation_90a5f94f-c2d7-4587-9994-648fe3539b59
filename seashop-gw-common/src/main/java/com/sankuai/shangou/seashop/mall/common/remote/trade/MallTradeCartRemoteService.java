package com.sankuai.shangou.seashop.mall.common.remote.trade;

import com.sankuai.shangou.seashop.base.boot.enums.TResultCode;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.thrift.core.ShoppingCartCmdFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ChangeShoppingCartQuantityReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.ShopProductResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MallTradeCartRemoteService {

    //@Resource
    //private ShoppingCartQueryFeign shoppingCartQueryFeign;
    @Resource
    private ShoppingCartCmdFeign shoppingCartCmdFeign;

    ///**
    // * 获取用户购物车列表
    // *
    // * @param userId 用户ID
    // * @return 购物车数据
    // */
    //public UserShoppingCartResp getUserShoppingCartList(Long userId) {
    //    log.info("【购物车】获取用户购物车列表, userId={}", userId);
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.getUserShoppingCartList(userId));
    //}
    //
    ///**
    // * 获取用户购物车SKU数量
    // *
    // * @param userId 用户ID
    // * @return 购物车SKU数量
    // */
    //public Integer getUserShoppingCartCount(Long userId) {
    //    log.info("【购物车】获取用户购物车SKU数量, userId={}", userId);
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.getUserShoppingCartCount(userId));
    //}
    //
    ///**
    // * 获取订单预览信息
    // *
    // * @param previewReq 请求参数
    // * @return 订单预览信息
    // */
    //public PreviewOrderResp previewOrder(PreviewOrderReq previewReq) {
    //    log.debug("【购物车】获取订单预览信息, 请求参数: {}", JsonUtil.toJsonString(previewReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.previewOrder(previewReq));
    //}
    //
    ///**
    // * 获取限时购订单预览信息
    // *
    // * @param previewReq 请求参数
    // * @return 订单预览信息
    // */
    //public PreviewOrderResp previewFlashSaleOrder(PreviewFlashSaleOrderReq previewReq) {
    //    log.info("【购物车】获取限时购订单预览信息, 请求参数: {}", JsonUtil.toJsonString(previewReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.previewFlashSaleOrder(previewReq));
    //}
    //
    ///**
    // * 获取组合购订单预览信息
    // *
    // * @param previewReq 请求参数
    // * @return 订单预览信息
    // */
    //public PreviewOrderResp previewCollocationOrder(PreviewCollocationOrderReq previewReq) {
    //    log.info("【购物车】获取组合购订单预览信息, 请求参数: {}", JsonUtil.toJsonString(previewReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.previewCollocationOrder(previewReq));
    //}
    //
    ///**
    // * 获取立即购买订单预览信息
    // *
    // * @param previewReq 请求参数
    // * @return 订单预览信息
    // */
    //public PreviewOrderResp previewBuyNowOrder(PreviewBuyNowReq previewReq) {
    //    log.info("【购物车】获取立即购买订单预览信息, 请求参数: {}", JsonUtil.toJsonString(previewReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.previewBuyNowOrder(previewReq));
    //}
    //
    ///**
    // * 添加购物车
    // *
    // * @param addShoppingCartReq 请求参数
    // * @return 添加结果
    // */
    //public BaseResp addShoppingCart(AddShoppingCartReq addShoppingCartReq) {
    //    log.info("【购物车】添加购物车, 请求参数={}", JsonUtil.toJsonString(addShoppingCartReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.addShoppingCart(addShoppingCartReq));
    //}
    //
    ///**
    // * 凑单页面加入购物车，需要返回返回凑单汇总信息
    // *
    // * @param addReq 请求参数
    // * @return 添加结果
    // */
    //public AddFromAddonResp addFromAddon(AddFromAddonReq addReq) {
    //    log.info("【购物车】凑单添加购物车, 请求参数={}", JsonUtil.toJsonString(addReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.addFromAddon(addReq));
    //}
    //
    ///**
    // * 删除购物车
    // *
    // * @param tradeDeleteReq 请求参数
    // * @return 删除结果
    // */
    //public BaseResp deleteShoppingCart(DeleteShoppingCartSkuReq tradeDeleteReq) {
    //    log.info("【购物车】删除购物车sku, 请求参数={}", JsonUtil.toJsonString(tradeDeleteReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.deleteShoppingCart(tradeDeleteReq));
    //}
    //
    ///**
    // * 清楚购物车失效商品
    // *
    // * @param tradeClearReq 请求参数
    // * @return 变更结果
    // */
    //public BaseResp clearInvalid(ClearInvalidShoppingCartSkuReq tradeClearReq) {
    //    log.info("【购物车】清除购物车sku, 请求参数={}", JsonUtil.toJsonString(tradeClearReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.clearInvalid(tradeClearReq));
    //}

    /**
     * 变更购物车sku数量
     *
     * @param tradeChangeReq 请求参数
     * @return 变更结果
     */
    public ResultDto<ShopProductResp> changeShoppingCartSkuCnt(ChangeShoppingCartQuantityReq tradeChangeReq) {
        log.info("【购物车】变更购物车sku数量, 请求参数={}", JsonUtil.toJsonString(tradeChangeReq));
        try {
            return shoppingCartCmdFeign.changeShoppingCartSkuCnt(tradeChangeReq);
        }
        catch (BusinessException e) {
            log.warn("business error", e);
            throw new BusinessException(e.getCode(), e.getMessage());
        }
        catch (Exception e) {
            log.error("thrift call failed", e);
            throw new BusinessException(TResultCode.SERVER_ERROR.value(), "服务端异常");
        }
    }

    ///**
    // * 选中店铺
    // *
    // * @param selectReq 请求参数
    // * @return 选中结果
    // */
    //public ShopProductResp selectShopSku(SelectShoppingCartSkuReq selectReq) {
    //    log.info("【购物车】选中sku, 请求参数={}", JsonUtil.toJsonString(selectReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.selectShopSku(selectReq));
    //}
    //
    ///**
    // * 选中店铺
    // *
    // * @param selectReq 请求参数
    // * @return 选中结果
    // */
    //public ShopProductResp selectShop(SelectShopReq selectReq) {
    //    log.info("【购物车】选中店铺, 请求参数={}", JsonUtil.toJsonString(selectReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.selectShop(selectReq));
    //}
    //
    ///**
    // * 选中整个购物车
    // *
    // * @param selectReq 请求参数
    // * @return 选中结果
    // */
    //public UserShoppingCartResp selectAll(SelectAllReq selectReq) {
    //    log.info("【购物车】选中整个购物车, 请求参数={}", JsonUtil.toJsonString(selectReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.selectAll(selectReq));
    //}
    //
    ///**
    // * 校验购物车是否可以提交
    // *
    // * @param previewReq 请求参数
    // * @return 订单预览信息
    // */
    //public BaseResp checkCanSubmit(PreviewOrderReq previewReq) {
    //    log.info("【购物车】校验购物车是否可以提交, 请求参数: {}", JsonUtil.toJsonString(previewReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.checkCanSubmit(previewReq));
    //}

}
