package com.sankuai.shangou.seashop.seller.common.util;

import com.sankuai.shangou.seashop.seller.thrift.core.request.enums.ApiRiskPageTypeEnum;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PageNameHelp {

    public static String getPageName(ApiRiskPageTypeEnum typeEnum) {
        switch (typeEnum) {
            case PRODUCT_INFO:
                return "商品详情";
            case SHOP_LOGO_IMAGE:
            case SHOP_NAVI_FONT:
            case SHOP_FLOOR_IMAGE:
            case SHOP_FLOOR_FONT:
            case SHOP_FOOTER:
            case IMAGE_AD:
            case SHOP_CAROUSEL_IMAGE:
                return "店铺-页面编辑";
            case ORDER_APPRAISE_IMAGE:
            case ORDER_APPRAISE_FONT:
            case ORDER_APPEND_APPRAISE_IMAGE:
            case ORDER_APPEND_APPRAISE_FONT:
                return "订单评价";
            default:
                break;
        }
        return typeEnum.getDesc();
    }
}
