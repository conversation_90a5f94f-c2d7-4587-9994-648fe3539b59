package com.sankuai.shangou.seashop.mall.common.remote.order;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MallOrderRefundRemoteService {

    @Resource
    private OrderRefundQueryFeign orderRefundQueryFeign;
    //@Resource
    //private OrderRefundCmdFeign orderRefundCmdFeign;
    //
    ///**
    // * 获取订单退款预览
    // *
    // * @param queryReq 请求参数
    // * @return 预览信息
    // */
    //public OrderRefundPreviewResp getOrderRefundPreview(QueryOrderRefundPreviewReq queryReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.getOrderRefundPreview(queryReq));
    //}
    //
    ///**
    // * 获取订单退款预览
    // *
    // * @param queryReq 请求参数
    // * @return 预览信息
    // */
    //public OrderItemRefundPreviewResp getOrderItemRefundPreview(QueryOrderItemRefundPreviewReq queryReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.getOrderItemRefundPreview(queryReq));
    //}
    //
    ///**
    // * 获取售后详情
    // *
    // * @param queryReq 请求参数
    // * @return 预览信息
    // */
    //public UserRefundDetailResp userQueryDetail(UserQueryRefundDetailReq queryReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.userQueryDetail(queryReq));
    //}
    //
    ///**
    // * 获取售后详情
    // *
    // * @param queryReq 请求参数
    // * @return 预览信息
    // */
    //public UserRefundDetailExtResp userQueryDetailExt(UserQueryRefundDetailReq queryReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.userQueryDetailExt(queryReq));
    //}
    //
    ///**
    // * 获取售后操作日志
    // *
    // * @param queryReq 请求参数
    // * @return 预览信息
    // */
    //public RefundLogListResp queryRefundLog(BaseIdReq queryReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryRefundLog(queryReq));
    //}

    /**
     * 获取售后列表
     *
     * @param queryReq 请求参数
     * @return 预览信息
     */
    public BasePageResp<UserRefundDto> userQueryRefundPage(UserQueryRefundReq queryReq) {
        log.info("【售后】查询售后列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.userQueryRefundPage(queryReq));
    }

    ///**
    // * 获取售后物流信息
    // *
    // * @param queryReq 请求参数
    // * @return 预览信息
    // */
    //public RefundUserDeliverExpressResp queryUserDeliverExpress(BaseIdReq queryReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryUserDeliverExpress(queryReq));
    //}
    //
    ///**
    // * 发货前，申请订单退款
    // *
    // * @param applyReq 请求参数
    // * @return 预览信息
    // */
    //public Long applyRefund(ApplyOrderRefundReq applyReq) {
    //    log.info("【售后】待发货状态下，申请订单售后, 请求参数={}", JsonUtil.toJsonString(applyReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.applyRefund(applyReq));
    //}
    //
    ///**
    // * 发货后，申请订单整单退款
    // *
    // * @param applyReq 请求参数
    // * @return 预览信息
    // */
    //public Long applyWholeOrderRefund(ApplyOrderRefundReq applyReq) {
    //    log.info("【售后】待收货/已完成状态下，申请整单退款退货, 请求参数={}", JsonUtil.toJsonString(applyReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.applyWholeOrderRefund(applyReq));
    //}
    //
    ///**
    // * 重新申请售后
    // *
    // * @param applyReq 请求参数
    // * @return 预览信息
    // */
    //public BaseResp reapplyOrderRefund(ReapplyRefundReq applyReq) {
    //    log.info("【售后】重新申请售后, 请求参数={}", JsonUtil.toJsonString(applyReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.reapplyOrderRefund(applyReq));
    //}
    //
    ///**
    // * 订单明细申请退货/退款
    // *
    // * @param applyReq 请求参数
    // * @return 预览信息
    // */
    //public Long applyItemRefund(ApplyOrderItemRefundReq applyReq) {
    //    log.info("【售后】订单明细申请退货/退款, 请求参数={}", JsonUtil.toJsonString(applyReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.applyItemRefund(applyReq));
    //}
    //
    ///**
    // * 取消售后
    // *
    // * @param req 请求参数
    // * @return 预览信息
    // */
    //public BaseResp cancelRefund(CancelOrderRefundReq req) {
    //    log.info("【售后】取消售后, 请求参数={}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.cancelRefund(req));
    //}
    //
    ///**
    // * 买家寄货
    // *
    // * @param req 请求参数
    // * @return 预览信息
    // */
    //public BaseResp userDeliver(UserDeliverReq req) {
    //    log.info("【售后】买家寄货, 请求参数={}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.userDeliver(req));
    //}
}
