package com.sankuai.shangou.seashop.seller.common.remote.order;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.OrderAndItemFlatListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class SellerOrderRemoteService {

    @Resource
    private OrderQueryFeign orderQueryFeign;

    ///**
    // * 获取昨日订单统计数据
    // *
    // * @param shopId
    // * @return
    // */
    //public OrderStatisticsResp getYesterdayOrderStatistics(Long shopId) {
    //    OrderStatisticsReq request = new OrderStatisticsReq();
    //    DateTime yesterday = DateUtil.yesterday();
    //    request.setBeginTime(DateUtil.beginOfDay(yesterday));
    //    request.setEndTime(DateUtil.endOfDay(yesterday));
    //    request.setShopId(shopId);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getOrderStatistics(request));
    //}
    //
    ///**
    // * 订单统计列表（财务管理首页统计）
    // *
    // * @param request
    // * @return
    // */
    //public OrderStatisticsListResp getOrderStatisticsList(OrderStatisticsReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getOrderStatisticsList(request));
    //}
    //
    ///**
    // * 供应商首页交易数据统计
    // *
    // * @param shopId 店铺ID
    // * @return 首页统计数据
    // */
    //public SellerIndexTradeStatsResp statsSellerIndexTradeData(Long shopId) {
    //    log.info("【订单统计】供应商首页交易数据统计, shopId={}", shopId);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderStatisticsQueryFeign.statsSellerIndexTradeData(shopId));
    //}
    //
    ///**
    // * 供应商首页销售排行前N的商品统计
    // *
    // * @param req 请求参数
    // * @return 首页统计数据
    // */
    //public TopProductSaleStatsResp statsTopNSaleProduct(StatsShopTopNSaleProductReq req) {
    //    log.info("【订单统计】供应商首页销售排行前N的商品统计, 请求参数={}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderStatisticsQueryFeign.statsTopNSaleProduct(req));
    //}
    //
    ///**
    // * 供应商分页查询订单列表
    // *
    // * @param queryReq 请求参数
    // * @return 订单列表
    // */
    //public BasePageResp<OrderInfoDto> pageQuerySellerOrder(QuerySellerOrderReq queryReq) {
    //    log.info("【订单】供应商分页查询订单列表, 请求参数: {}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.pageQuerySellerOrder(queryReq));
    //}

    /**
     * 供应商查询订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    public OrderDetailResp queryDetail(String orderId, Long shopId) {
        log.info("【订单】供应商查询订单详情, orderId={}", orderId);
        QueryOrderDetailReq req = new QueryOrderDetailReq();
        req.setOrderId(orderId);
        req.setQueryFrom(OrderQueryFromEnum.SELLER_PC);
        OrderDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryDetail(req));
        if (!resp.getOrderInfo().getShopId().equals(shopId)) {
            throw new BusinessException("只能查询自己店铺的订单");
        }
        return resp;
    }

    ///**
    // * 供应商查询订单物流信息
    // *
    // * @param queryReq 请求参数
    // * @return 物流信息
    // */
    //public OrderWayBillListResp queryOrderWayBill(QueryOrderWayBillReq queryReq) {
    //    log.info("【订单】供应商查询订单物流信息, 请求参数={}", JsonUtil.toJsonString(queryReq));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryOrderWayBill(queryReq));
    //}

    ///**
    // * 查询订单操作日志
    // *
    // * @param orderId 订单号
    // */
    //public List<OrderOperationLogResp> queryOrderLog(String orderId) {
    //    log.info("【订单】供应商查询订单操作日志, orderId={}", orderId);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryOrderLog(orderId));
    //}

    ///**
    // * 供应商修改收货人信息
    // *
    // * @param req 请求参数
    // * @return 响应结果
    // */
    //public BaseResp updateReceiverBySeller(SellerUpdateReceiverReq req) {
    //    log.info("【订单】供应商修改收货人信息, 请求参数: {}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.updateReceiverBySeller(req));
    //}
    //
    ///**
    // * 供应商改价
    // *
    // * @param req 请求参数
    // * @return 响应结果
    // */
    //public BaseResp updateItemAmount(UpdateItemAmountReq req) {
    //    log.info("【订单】供应商改价, 请求参数: {}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.updateItemAmount(req));
    //}
    //
    ///**
    // * 供应商修改运费
    // *
    // * @param req 请求参数
    // * @return 响应结果
    // */
    //public BaseResp updateFreight(UpdateFreightReq req) {
    //    log.info("【订单】供应商修改运费, 请求参数: {}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.updateFreight(req));
    //}
    //
    ///**
    // * 供应商批量发货
    // *
    // * @param req 请求参数
    // * @return 响应结果
    // */
    //public BaseResp batchDeliverOrder(BatchDeliverOrderReq req) {
    //    log.info("【订单】供应商批量发货, 请求参数: {}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.batchDeliverOrder(req));
    //}
    //
    ///**
    // * 供应商修改物流信息
    // *
    // * @param req 请求参数
    // * @return 响应结果
    // */
    //public BaseResp updateExpress(UpdateExpressReq req) {
    //    log.info("【订单】供应商修改物流信息, 请求参数: {}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.updateExpress(req));
    //}
    //
    ///**
    // * 供应商添加备注
    // *
    // * @param req 请求参数
    // * @return 响应结果
    // */
    //public BaseResp sellerRemark(SellerRemarkReq req) {
    //    log.info("【订单】供应商添加备注, 请求参数: {}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.sellerRemark(req));
    //}
    //
    ///**
    // * 供应商取消订单
    // *
    // * @param req 请求参数
    // * @return 响应结果
    // */
    //public BaseResp cancelOrder(CancelOrderReq req) {
    //    log.info("【订单】供应商取消订单, 请求参数: {}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.sellerCancelOrder(req));
    //}
    //
    ///**
    // * 供应商将用户订单重新加入购物车
    // *
    // * @param req 请求参数
    // * @return 响应结果
    // */
    //public BaseResp reBuyBySeller(ReBuyBySellerReq req) {
    //    log.info("【订单】供应商将用户订单重新加入购物车, 请求参数: {}", JsonUtil.toJsonString(req));
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.reBuyBySeller(req));
    //}

    public OrderAndItemFlatListResp getScrollIdForSellerExport(QuerySellerOrderReq pageParam) {
        log.info("【订单】获取供应商分页查询订单列表的scrollId, 请求参数: {}", JsonUtil.toJsonString(pageParam));
        OrderAndItemFlatListResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getScrollIdForSellerExport(pageParam));
        log.info("【订单】获取供应商分页查询订单列表的scrollId, 返回结果: {}", JsonUtil.toJsonString(resp));
        return resp;
    }

    public List<OrderAndItemInfoDto> listOrderAndItemFlatByScroll(EsScrollQueryReq request) {
        log.info("【订单】通过scrollId查询订单和明细列表, 请求参数: {}", JsonUtil.toJsonString(request));
        OrderAndItemFlatListResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.listOrderAndItemFlatByScroll(request));
        if (resp == null || CollUtil.isEmpty(resp.getDataList())) {
            return null;
        }
        return resp.getDataList();
    }

}
