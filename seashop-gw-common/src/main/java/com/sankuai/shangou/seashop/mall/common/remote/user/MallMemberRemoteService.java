package com.sankuai.shangou.seashop.mall.common.remote.user;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.gw.common.thrift.requests.ApiRegisterReq;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.LoginPlatformEnum;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.*;
import com.sankuai.shangou.seashop.user.thrift.account.response.EpManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.ResetPasswordResp;
import com.sankuai.shangou.seashop.user.thrift.auth.LoginUserFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CheckCodeCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdSendCodeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class MallMemberRemoteService {
    @Resource
    private ShopMemberCmdFeign shopMemberCmdFeign;
    @Resource
    private ShopMemberQueryFeign shopMemberQueryFeign;
    @Resource
    private LoginUserFeign loginUserFeign;


    public MemberResp getMemberResp(QueryMemberReq queryMemberReq) {
        return ThriftResponseHelper.executeThriftCall(() -> {
            // 业务逻辑处理
            return shopMemberQueryFeign.queryMember(queryMemberReq);
        });
    }

    public BaseResp updateMember(UpdateMemberReq updateMemberReq) {
        ThriftResponseHelper.executeThriftCall(() -> {
            // 业务逻辑处理
            return shopMemberCmdFeign.updateMember(updateMemberReq);
        });
        return BaseResp.of();
    }

    public BaseResp sendCode(CmdSendCodeReq cmdSendCodeReq) {
        //调用服务
        ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.sendCode(cmdSendCodeReq));
        //转化出参
        return BaseResp.of();
    }


    public BaseResp bindContact(BindContactCmdReq bindContactCmdReq) {
        ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.bindContact(bindContactCmdReq));
        return BaseResp.of();
    }

    public ImageVerificationCodeResp imageVerificationCode() {
        //调用服务
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.imageVerificationCode());
    }

    public String getOpenId(String code) {
        //调用服务
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.getOpenId(new OpenIdQueryReq(code)));
    }

    public MemberResp getMemberById(Long id) {
        QueryMemberReq queryMemberReq = new QueryMemberReq();
        queryMemberReq.setId(id);
        return getMemberResp(queryMemberReq);
    }

    public BaseResp updateLoginTime(Long id) {
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.updateLoginTime(id));
    }

    public BaseResp logout(TokenCache tokenCache) {
        return ThriftResponseHelper.executeThriftCall(() -> loginUserFeign.logout(tokenCache));
    }

    public EpManagerResp checkAndCreateUser(Integer epId) {
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.checkAndCreateUser(epId));
    }

    public ResetPasswordResp resetPassword(ResetPasswordReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.resetPassword(req));
    }

    public BaseResp checkCode(CheckCodeCmdReq sendCodeCmdReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.checkCode(sendCodeCmdReq));
    }

    public LoginResp login(ApiLoginReq copy) {
        LoginReq copy1 = JsonUtil.copy(copy, LoginReq.class);
        copy1.setLoginTypeWithDefault(copy.getLoginType());
        copy1.setLoginPlatform(LoginPlatformEnum.BUSINESS_FE.name());
        return ThriftResponseHelper.executeThriftCall(() -> loginUserFeign.login(copy1));
    }

    public LoginResp register(ApiRegisterReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.register(JsonUtil.copy(req, RegisterReq.class)));
    }

    public ResetPasswordResp modifyPassword(ModifyPasswordReq copy) {
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.modifyPassword(copy));
    }
}
