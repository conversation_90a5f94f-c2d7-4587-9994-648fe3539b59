package com.sankuai.shangou.seashop.seller.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@ThriftEnum
public enum SellerResultCodeEnum {

    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * </pre>
     */


    // 数据异常
    SELLER_DATA_NOT_EXIST(50001001, "数据不存在"),
    // 供应商管理员已经被删除
    SELLER_ADMIN_NOT_EXIST(50001002, "供应商管理员已经被删除"),
    ;

    private Integer code;
    private String msg;

    SellerResultCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
