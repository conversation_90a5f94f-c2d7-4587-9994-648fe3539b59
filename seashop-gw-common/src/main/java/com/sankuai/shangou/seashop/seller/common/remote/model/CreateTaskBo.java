package com.sankuai.shangou.seashop.seller.common.remote.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class CreateTaskBo {

    /**
     * 业务类型。1：导出任务
     */
    private Integer bizType;

    /**
     * 任务类型。具体的业务指定，需要唯一，最好具有一定的规则
     */
    private Integer taskType;

    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 执行参数
     */
    private String executeParam;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人账号
     */
    private String operatorAccount;

    /**
     * 环境。主要是区分uat和prd
     */
    private String env;

}
