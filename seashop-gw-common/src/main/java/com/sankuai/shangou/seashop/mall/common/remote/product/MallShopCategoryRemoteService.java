package com.sankuai.shangou.seashop.mall.common.remote.product;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.product.model.RemoteShopCategoryBo;
import com.sankuai.shangou.seashop.product.thrift.core.ShopCategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.QueryShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory.ShopCategoryTreeResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/04 12:04
 */
@Service
@Slf4j
public class MallShopCategoryRemoteService {

    @Resource
    private ShopCategoryQueryFeign shopCategoryQueryFeign;


    public List<RemoteShopCategoryBo> getShopCategoryList(Long shopId, Boolean whetherShow) {
        QueryShopCategoryReq request = new QueryShopCategoryReq();
        request.setShopId(shopId);
        request.setWhetherShow(whetherShow);
        ShopCategoryTreeResp resp = ThriftResponseHelper.executeThriftCall(() -> shopCategoryQueryFeign.queryShopCategory(request));
        return JsonUtil.parseArray(resp.getResult(), RemoteShopCategoryBo.class);
    }

}
