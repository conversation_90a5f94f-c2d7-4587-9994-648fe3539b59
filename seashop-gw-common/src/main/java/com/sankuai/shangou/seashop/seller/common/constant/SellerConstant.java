package com.sankuai.shangou.seashop.seller.common.constant;

/**
 * @author: lhx
 * @date: 2023/11/16/016
 * @description:
 */
public class SellerConstant {

    /**
     * 专享价导入异常数据
     */
    public static final String EXCLUSIVE_PRICE_IMPORT_ERROR_DATA = "专享价导入异常数据";

    /**
     * 商品信息不存在
     */
    public static final String PRODUCT_NOT_EXIST = "商品信息不存在";
    /**
     * 商家信息不存在
     */
    public static final String MEMBER_NOT_EXIST = "商家信息不存在";

    /**
     * 查询起始页
     */
    public static final Integer QUERY_START = 1;

    /**
     * 每次查询的最大条数
     */
    public static final Integer QUERY_LIMIT = 200;

    /**
     * 最小页数
     */
    public static final Integer MIN_PAGE = 1;

    /**
     * 第二页
     */
    public static final Integer SECOND_PAGE = 2;

    /**
     * 是
     */
    public static final String YES_STR = "是";

    /**
     * 否
     */
    public static final String NO_STR = "否";

    /**
     * 店铺提醒缓存redis key
     */
    public static final String SHOP_REMIND_CACHE_KEY = "shop:remind:";

    //    缓存过期时间
    public static final Integer CACHE_EXPIRE_TIME = 3600 * 24;
}
