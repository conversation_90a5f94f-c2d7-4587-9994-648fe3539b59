package com.sankuai.shangou.seashop.seller.common.remote.promotion;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponRecordQueryFeign;

/**
 * @author: lhx
 * @date: 2024/1/15/015
 * @description:
 */
@Service
public class SellerCouponRecordRemoteService {

    @Resource
    private CouponRecordQueryFeign couponRecordQueryFeign;

    public BasePageResp<CouponRecordSimpleResp> pageList(CouponRecordQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> couponRecordQueryFeign.pageList(request));
    }
}
