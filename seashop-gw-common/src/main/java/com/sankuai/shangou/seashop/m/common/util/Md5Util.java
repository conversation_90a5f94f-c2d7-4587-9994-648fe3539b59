package com.sankuai.shangou.seashop.m.common.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Md5Util {
    /**
     * 默认的密码字符串组合，用来将字节转换成 16 进制表示的字符,apache校验下载的文件的正确性用的就是默认的这个组合
     */
    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};


    public static String toMd5String(String src) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = src.getBytes(StandardCharsets.UTF_8);
            StringBuilder sb = new StringBuilder();
            for (byte b : md.digest(bytes)) {
                sb.append(byteHeX(b));
            }
            return sb.toString();
        }
        catch (Exception e) {
            log.error("md5 encrypt error", e);
            throw new BusinessException("MD5-encrypt error");
        }
    }

    private static String byteHeX(byte ib) {
        char[] ob = new char[2];
        ob[0] = HEX_DIGITS[(ib >>> 4) & 0X0F];
        ob[1] = HEX_DIGITS[ib & 0X0F];
        return new String(ob);
    }
}
