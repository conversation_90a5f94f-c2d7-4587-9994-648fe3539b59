package com.sankuai.shangou.seashop.seller.common.remote.promotion;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductPageQryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductUpdateReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceCmdFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceProductQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceQueryFeign;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@Service
public class SellerExclusivePriceRemoteService {

    @Resource
    private ExclusivePriceProductQueryFeign exclusivePriceProductQueryFeign;

    @Resource
    private ExclusivePriceCmdFeign exclusivePriceCmdFeign;

    @Resource
    private ExclusivePriceQueryFeign exclusivePriceQueryFeign;

    /**
     * 专享价商品分页列表
     *
     * @param request
     * @return
     */
    public BasePageResp<ExclusivePriceProductDto> productPageList(ExclusivePriceProductPageQryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> exclusivePriceProductQueryFeign.pageList(request));
    }

    /**
     * 专享价活动保存
     *
     * @param request
     * @return
     */
    public BaseResp save(ExclusivePriceSaveReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> exclusivePriceCmdFeign.save(request));
    }

    /**
     * 专享价活动结束
     *
     * @param request
     * @return
     */
    public BaseResp endActive(BaseIdReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> exclusivePriceCmdFeign.endActive(request));
    }

    /**
     * 专享价活动分页列表
     *
     * @param request
     * @return
     */
    public BasePageResp<ExclusivePriceSimpleResp> pageList(ExclusivePriceQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> exclusivePriceQueryFeign.pageList(request));
    }

    /**
     * 专享价活动详情
     *
     * @param request
     * @return
     */
    public ExclusivePriceResp getById(BaseIdReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> exclusivePriceQueryFeign.getById(request));
    }

    /**
     * 专享价商品更新
     *
     * @param request
     * @return
     */
    public BaseResp updateProduct(ExclusivePriceProductUpdateReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> exclusivePriceCmdFeign.updateProduct(request));
    }
}
