package com.sankuai.shangou.seashop.m.common.remote.product;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.common.convert.MProductConvert;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiProductAuditDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.ProductAuditCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.ProductAuditQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductAuditQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.ProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.QueryProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditPageResp;

/**
 * <AUTHOR>
 * @date 2024/01/03 18:06
 */
@Service
public class MProductAuditRemoteService {

    @Resource
    private ProductAuditQueryFeign productAuditQueryFeign;

    @Resource
    private ProductAuditCmdFeign productAuditCmdFeign;

    @Resource
    private MProductConvert mProductConvert;

    public BasePageResp<ProductAuditPageResp> queryProductAudit(QueryProductAuditReq queryParam) {
        return ThriftResponseHelper.executeThriftCall(() -> productAuditQueryFeign.queryProductAudit(queryParam));
    }

    public BaseResp batchAuditProduct(ProductAuditReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> productAuditCmdFeign.batchAuditProduct(request));
    }

    public ApiProductAuditDetailResp queryProductAuditDetail(ProductAuditQueryDetailReq request) {
        ProductAuditDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> productAuditQueryFeign.queryProductAuditDetail(request));
        if (resp == null) {
            return null;
        }

        ApiProductAuditDetailResp result = new ApiProductAuditDetailResp();
        result.setShowCompare(resp.getShowCompare());
        result.setProductAudit(mProductConvert.convertApiProductDetailDto(resp.getProductAudit()));
        result.setOriginProduct(mProductConvert.convertApiProductDetailDto(resp.getOriginProduct()));
        return result;
    }


}
