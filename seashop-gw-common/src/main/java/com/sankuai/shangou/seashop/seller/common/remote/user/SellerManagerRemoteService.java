package com.sankuai.shangou.seashop.seller.common.remote.user;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.gw.common.thrift.requests.ApiRefreshTokenReq;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.LoginPlatformEnum;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.ServletUtil;
import com.sankuai.shangou.seashop.user.thrift.account.ManagerCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.ManagerQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.EpManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;
import com.sankuai.shangou.seashop.user.thrift.auth.LoginUserFeign;
import com.sankuai.shangou.seashop.user.thrift.auth.request.RefreshTokenReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 管理员服务实现类
 * @author: LXH
 **/
@Service
@Slf4j
public class SellerManagerRemoteService {
    @Resource
    private ManagerQueryFeign managerQueryFeign;
    @Resource
    private ManagerCmdFeign managerCmdFeign;
    @Resource
    private LoginUserFeign loginUserFeign;

    public EpManagerResp queryManager(Integer id) {
        return ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryEpManager(id));
    }

    public BasePageResp<ManagerResp> queryManagerPage(QueryManagerPageReq queryManagerPageReq) {
        BasePageResp<ManagerResp> managerRespPage = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryManagerPage(JsonUtil.copy(queryManagerPageReq, QueryManagerPageReq.class)));
        return PageResultHelper.transfer(managerRespPage, managerResp -> JsonUtil.copy(managerResp, ManagerResp.class));
    }

    public ManagerResp queryManager(QueryManagerReq queryManagerPageReq) {
        ManagerResp managerResp = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryManager(JsonUtil.copy(queryManagerPageReq, QueryManagerReq.class)));
        return JsonUtil.copy(managerResp, ManagerResp.class);
    }

    public Long addManager(CmdManagerReq cmdManagerReq) {
        return ThriftResponseHelper.executeThriftCall(() -> managerCmdFeign.addManager(JsonUtil.copy(cmdManagerReq, CmdManagerReq.class)));
    }

    public Long editManager(CmdManagerReq cmdManagerReq) {
        return ThriftResponseHelper.executeThriftCall(() -> managerCmdFeign.editManager(JsonUtil.copy(cmdManagerReq, CmdManagerReq.class)));
    }


    public Long deleteManager(CmdManagerReq cmdManagerReq) {
        return ThriftResponseHelper.executeThriftCall(() -> managerCmdFeign.deleteManager(JsonUtil.copy(cmdManagerReq, CmdManagerReq.class)));
    }


    public Integer batchDeleteManager(CmdManagerReq cmdManagerReq) {
        return ThriftResponseHelper.executeThriftCall(() -> managerCmdFeign.batchDeleteManager(JsonUtil.copy(cmdManagerReq, CmdManagerReq.class)));
    }

    public LoginResp login(ApiLoginReq loginReq) {
        LoginReq copy1 = JsonUtil.copy(loginReq, LoginReq.class);
        copy1.setLoginTypeWithDefault(loginReq.getLoginType());
        copy1.setLoginPlatform(LoginPlatformEnum.SELLER_BE.name());
        if (loginReq.getSlideCode()!=null){
            copy1.getSlideCode().setRemoteIp(ServletUtil.ip());
        }
        return ThriftResponseHelper.executeThriftCall(() -> loginUserFeign.login(copy1));
    }

    public LoginResp refreshToken(ApiRefreshTokenReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> loginUserFeign.refreshToken(JsonUtil.copy(req, RefreshTokenReq.class)));
    }

    public Boolean logout(TokenCache t) {
        ThriftResponseHelper.executeThriftCall(() -> loginUserFeign.logout(t));
        return false;
    }
}
