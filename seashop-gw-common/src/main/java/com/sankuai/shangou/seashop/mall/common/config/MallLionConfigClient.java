package com.sankuai.shangou.seashop.mall.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Getter;


/**
 *  改成自己项目中的 lion/mcc 配置
 * config 说明文档 <a href=https://km.sankuai.com/page/550965600></a>
 *
 * <AUTHOR>
 */

@Component
@Getter
public class MallLionConfigClient {

    /**
     * venus socket
     */
//    @ConfigValue(key = "venus.socket.timeout", defaultValue = "5000")
    @Value("${venus.socket.timeout:5000}")
    private int venusSocketTimeOut;


    /**
     * venus connect
     */
//    @ConfigValue(key = "venus.connect.timeout", defaultValue = "2000")
    @Value("${venus.connect.timeout:2000}")
    private int venusConnectTimeOut;

    //    @ConfigValue(key = "shop.default.image", defaultValue = "")
    @Value("${shop.default.image:''}")
    private String shopDefaultImage;
}