package com.sankuai.shangou.seashop.seller.common.remote.base;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.TopicCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.TopicQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicJsonFileReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWapTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWapTopicRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2024/1/26/026
 * @description:
 */
@Service
@Slf4j
public class SellerTopicRemoteService {

    @Resource
    private TopicQueryFeign topicQueryFeign;

    @Resource
    private TopicCMDFeign topicCMDFeign;

    //public BasePageResp<BaseTopicRes> query(BaseTopicQueryReq query) {
    //    return ThriftResponseHelper.executeThriftCall(() -> topicQueryFeign.query(query));
    //}
    //
    //public Long create(BaseTopicReq query) {
    //    return ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.create(query));
    //}

    public String uploadTemplate(BaseTopicJsonFileReq jsonFileReq) {
        return ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.uploadTemplate(jsonFileReq));
    }

    //public Boolean update(BaseTopicReq query) {
    //    return ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.update(query));
    //}

    public BaseWapTopicRes getWapTopicById(BaseWapTopicQueryReq query) {
        return ThriftResponseHelper.executeThriftCall(() -> topicQueryFeign.getWapTopicById(query));
    }

    //public Boolean editSellerIndexPage(TemplatePageIndexReq req) {
    //    return ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.editSellerIndex(req));
    //}

    //public String getSellerIndexPage(Long shopId) {
    //    return ThriftResponseHelper.executeThriftCall(() -> topicQueryFeign.getSellerIndex(shopId));
    //}

    //public String getSellerPCIndexPage(Long shopId) {
    //    return ThriftResponseHelper.executeThriftCall(() -> topicQueryFeign.getSellerPCIndex(shopId));
    //}
    //public Boolean setHome(BaseShopReq  shopReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.setHome(shopReq));
    //}
    //public Boolean delete(BaseShopReq req) {
    //    return ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.delete(req));
    //}

}
