# 微信OAuth2工具类使用说明

## 概述

本工具类模仿项目中的`WxMiniServiceImpl`风格，提供了完整的微信网页授权OAuth2功能，包括获取access_token、刷新token和获取用户信息等功能。

## 文件结构

```
seashop-base-user-auth/src/main/java/com/sankuai/shangou/seashop/user/service/
├── WxOAuth2Service.java                    # 服务接口
├── WxOAuth2AccessTokenResult.java          # access_token响应对象
├── WxOAuth2UserInfo.java                   # 用户信息响应对象
├── impl/
│   └── WxOAuth2ServiceImpl.java            # 服务实现类
└── controller/
    └── WxOAuth2Controller.java             # 控制器（使用示例）
```

## 核心功能

### 1. 获取access_token
通过微信授权码获取网页授权access_token：

```java
@Resource
private WxOAuth2Service wxOAuth2Service;

// 通过code获取access_token
WxOAuth2AccessTokenResult result = wxOAuth2Service.getAccessToken(code);
if (result.isAccessTokenValid()) {
    String accessToken = result.getAccessToken();
    String openId = result.getOpenId();
    String refreshToken = result.getRefreshToken();
    // 使用获取到的信息
}
```

### 2. 刷新access_token
使用refresh_token刷新access_token：

```java
// 刷新access_token
WxOAuth2AccessTokenResult result = wxOAuth2Service.refreshAccessToken(refreshToken);
if (result.isAccessTokenValid()) {
    String newAccessToken = result.getAccessToken();
    String newRefreshToken = result.getRefreshToken();
    // 使用新的token
}
```

### 3. 获取用户信息
使用access_token和openId获取用户详细信息：

```java
// 获取用户信息
WxOAuth2UserInfo userInfo = wxOAuth2Service.getUserInfo(accessToken, openId);
if (userInfo.isUserInfoValid()) {
    String nickname = userInfo.getNickname();
    String headImgUrl = userInfo.getHeadImgUrl();
    Integer sex = userInfo.getSex();
    String province = userInfo.getProvince();
    String city = userInfo.getCity();
    // 使用用户信息
}
```

## 配置说明

### 1. 在application.yml中添加配置

```yaml
# 微信公众号配置
wx:
  mp:
    appId: wx1234567890abcdef      # 您的微信公众号AppID
    appSecret: your_app_secret      # 您的微信公众号AppSecret
```

### 2. 兼容现有配置格式

如果项目中已有微信配置，也可以使用：

```yaml
wx:
  appKey: wx1234567890abcdef       # 微信公众号AppID
  appSecret: your_app_secret       # 微信公众号AppSecret
```

## API接口使用

### 1. 获取access_token接口

```http
GET /wxOAuth2/getAccessToken?code=微信授权码
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "accessToken": "ACCESS_TOKEN",
    "expiresIn": 7200,
    "refreshToken": "REFRESH_TOKEN",
    "openId": "OPENID",
    "scope": "snsapi_userinfo",
    "unionId": "UNIONID"
  },
  "success": true
}
```

### 2. 刷新access_token接口

```http
GET /wxOAuth2/refreshAccessToken?refresh_token=刷新令牌
```

### 3. 获取用户信息接口

```http
GET /wxOAuth2/getUserInfo?access_token=ACCESS_TOKEN&openid=OPENID
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "openId": "OPENID",
    "nickname": "用户昵称",
    "sex": 1,
    "province": "广东",
    "city": "深圳",
    "country": "中国",
    "headImgUrl": "http://wx.qlogo.cn/mmopen/...",
    "unionId": "UNIONID"
  },
  "success": true
}
```

### 4. 一键获取用户信息接口

```http
GET /wxOAuth2/getUserInfoByCode?code=微信授权码
```

此接口会自动完成获取access_token和用户信息的两个步骤。

## 设计特点

### 1. 模仿现有风格
- 参考`WxMiniServiceImpl`的代码风格和结构
- 使用相同的异常处理机制（`LoginException`）
- 采用相同的日志记录方式
- 使用项目现有的工具类（`OkHttpUtil`、`JsonUtil`）

### 2. 完善的错误处理
- 网络请求异常处理
- 微信API错误码处理
- 参数校验
- 详细的日志记录

### 3. 易于使用
- 提供简洁的接口方法
- 封装复杂的HTTP请求逻辑
- 提供便捷的判断方法（`isSuccess()`、`isAccessTokenValid()`等）

### 4. 扩展性好
- 接口与实现分离
- 支持配置化
- 易于添加新功能

## 微信网页授权流程

1. **引导用户授权**：将用户重定向到微信授权页面
2. **获取授权码**：用户授权后，微信会回调并带上code参数
3. **获取access_token**：使用code调用本工具类获取access_token
4. **获取用户信息**：使用access_token获取用户详细信息
5. **业务处理**：根据用户信息进行登录或注册等业务操作

## 注意事项

1. **AppID和AppSecret配置**：确保在配置文件中正确配置微信公众号的AppID和AppSecret
2. **网页授权域名**：需要在微信公众平台配置网页授权域名
3. **scope权限**：根据需要选择`snsapi_base`（静默授权）或`snsapi_userinfo`（用户信息授权）
4. **token有效期**：access_token有效期为2小时，refresh_token有效期为30天
5. **错误处理**：建议在业务代码中捕获`LoginException`并进行适当处理

## 示例：完整的授权流程

```java
@Service
public class WxLoginService {
    
    @Resource
    private WxOAuth2Service wxOAuth2Service;
    
    public LoginResult loginByWxCode(String code) {
        try {
            // 1. 获取access_token
            WxOAuth2AccessTokenResult tokenResult = wxOAuth2Service.getAccessToken(code);
            if (!tokenResult.isAccessTokenValid()) {
                throw new LoginException("获取微信授权失败");
            }
            
            // 2. 获取用户信息
            WxOAuth2UserInfo userInfo = wxOAuth2Service.getUserInfo(
                tokenResult.getAccessToken(), 
                tokenResult.getOpenId()
            );
            
            if (!userInfo.isUserInfoValid()) {
                throw new LoginException("获取用户信息失败");
            }
            
            // 3. 业务处理：查找或创建用户
            User user = findOrCreateUser(userInfo);
            
            // 4. 生成登录token
            String loginToken = generateLoginToken(user);
            
            return LoginResult.success(loginToken, user);
            
        } catch (LoginException e) {
            log.error("微信登录失败: {}", e.getMessage());
            return LoginResult.fail(e.getMessage());
        }
    }
}
```

这个工具类完全模仿了项目现有的代码风格，可以无缝集成到现有项目中使用。
