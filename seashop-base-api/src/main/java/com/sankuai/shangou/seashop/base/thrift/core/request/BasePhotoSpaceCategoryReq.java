package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BasePhotoSpaceCategoryReq extends BaseThriftDto {
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 分类名称
     */
    private String photoSpaceCatrgoryName;

    /**
     * 排序
     */
    private Long displaysSequence;

    public void checkParameter() {
        if (StringUtils.isEmpty(this.photoSpaceCatrgoryName)) {
            throw new IllegalArgumentException("分类名称不能为空");
        }


    }
}
