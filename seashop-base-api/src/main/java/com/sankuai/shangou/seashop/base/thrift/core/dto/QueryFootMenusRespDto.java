package com.sankuai.shangou.seashop.base.thrift.core.dto;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 13:41
 */
@Data
public class QueryFootMenusRespDto extends BaseParamReq {

    private Long id;
    /**
     * 导航名称
     */
    private String name;

    /**
     * 链接地址
     */
    private String url;

    private String urlName;

    /**
     * 显示图片
     */
    @JsonUrlFormat(deserializer = false)
    private String menuIcon;
    /**
     * 未选中显示图片
     */
    @JsonUrlFormat(deserializer = false)
    private String menuIconSel;
    /**
     * 菜单类型（1代表微信、2代表小程序）
     */
    private Integer type;
    /**
     * 店铺Id(0默认是平台)
     */
    private Long shopId;

}
