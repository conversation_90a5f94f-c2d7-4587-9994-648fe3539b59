package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 14:14
 */
@Data
public class CmdWxAppletFormDataReq extends BaseParamReq {

    /**
     * 事件ID取messageType
     */
    private Long eventId;

    /**
     * 事件值取订单ID
     */
    private String eventValue;

    /**
     * 事件的表单ID取模板ID
     */
    private String formId;

    /**
     * 事件时间
     */
    private Date eventTime;

    /**
     * FormId过期时间
     */
    private Date expireTime;

    public void checkParameter() {
        AssertUtil.throwIfNull(eventId, "eventId不能为空");
        AssertUtil.throwIfNull(eventValue, "eventValue不能为空");
        AssertUtil.throwIfNull(formId, "formId不能为空");
    }
}
