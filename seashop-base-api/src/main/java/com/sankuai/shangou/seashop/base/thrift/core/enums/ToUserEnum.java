package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum ToUserEnum {
    ALL(0, "全部"),
    Label(1, "标签用户"),
    User(2, "指定用户id"),
    Criteria(3, "指定查询条件用户")
    ;
    Integer code;
    String desc;

    ToUserEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ToUserEnum of(Integer code) {
        if (code == null) {
            return null;
        }
        for (ToUserEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}