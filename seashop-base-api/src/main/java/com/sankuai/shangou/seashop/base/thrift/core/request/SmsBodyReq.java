package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

@Data
public class SmsBodyReq extends BaseThriftDto {

    /**
     * 短信模板id
     */
    private Long templateId;

    /**
     * 参数  map类型的Json 参数 主要替换为短信模板中的 key value
     */
    private String param;

    /**
     * 唯一id 业务自己传入
     */
    private  Long requestId;

    /**
     * 收信人集合
     */
    private List<ContactReq> contactList;
}
