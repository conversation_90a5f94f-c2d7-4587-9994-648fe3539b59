package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BaseWXMenuReq  extends BaseThriftDto {

    private Long id;

    /**
     * 菜单标题
     */
    private String name;


    /**
     * 链接类型：0无链接，1微商城，2小程序
     */
    private Integer linkType;


    /**
     * 链接值
     */
    private String linkValue;


    /**
     * 父级id
     */
    private Long parentId;


    /**
     * 是否自定义菜单
     */
    private Integer whetherCustom;


    public void checkParameter() {
        if (StringUtils.isEmpty(this.name)) {
            throw new IllegalArgumentException("菜单标题不能为空");
        }
    }

}
