package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class UpdatePhotoSpaceReq extends BaseThriftDto {

    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;


    /**
     * 图片名称
     */
    private String photoName;

    public void checkParameter() {


        if (this.id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (StringUtils.isEmpty(this.photoName)) {
            throw new IllegalArgumentException("图片名称不能为空");
        }

    }
}
