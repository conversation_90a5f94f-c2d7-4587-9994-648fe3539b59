package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum PlateTypeEnum {
    PC(0, "PC"),
    WX(1, "微信"),
    Android(2, "android"),
    IOS(3, "IOS"),
    Touch(4, "触屏"),
    WXA(5, "商城小程序"),
    Mobile(99, "移动端");

    private Integer code;
    private String desc;

    PlateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PlateTypeEnum getByCode(Integer code) {
        for (PlateTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
