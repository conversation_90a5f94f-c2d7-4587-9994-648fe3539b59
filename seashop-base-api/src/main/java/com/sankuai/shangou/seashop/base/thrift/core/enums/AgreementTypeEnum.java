package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum AgreementTypeEnum {
    Buyers(0, "商家注册协议"),
    Seller(1, "卖家入驻说明"),
    APP(2, "APP关于我们"),
    PrivacyPolicy(3, "隐私政策"),
    SellerFile(4, "卖家入驻协议文件");

    private Integer code;
    private String desc;

    AgreementTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AgreementTypeEnum getByCode(Integer code) {
        for (AgreementTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
