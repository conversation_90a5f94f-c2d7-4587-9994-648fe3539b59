package com.sankuai.shangou.seashop.base.thrift.core.request.base;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class BaseIdsReq extends BaseParamReq {

    @ExaminField(description = "id集合")
    private List<Long> ids;

    @ExaminField(description = "店铺id")
    private Long shopId;

    public void checkParameter() {
        if (this.ids == null || this.ids.size() < 1) {
            throw new IllegalArgumentException("id集合不能为空");
        }

    }

    public  boolean hasIntersection(List<Long> list) {
        Set<Long> set1 = new HashSet<>(list);
        Set<Long> set2 = new HashSet<>(this.ids);
        set1.retainAll(set2);
        return !set1.isEmpty();
    }
}
