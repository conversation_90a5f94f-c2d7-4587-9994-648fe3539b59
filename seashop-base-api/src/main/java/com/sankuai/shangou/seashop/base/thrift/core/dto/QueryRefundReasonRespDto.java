package com.sankuai.shangou.seashop.base.thrift.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:26
 */
@Data
public class QueryRefundReasonRespDto {

    private Long id;

    /**
     * 售后原因
     */
    private String afterSalesText;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;
}
