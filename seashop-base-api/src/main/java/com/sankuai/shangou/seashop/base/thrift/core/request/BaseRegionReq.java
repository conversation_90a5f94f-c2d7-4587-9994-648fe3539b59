package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BaseRegionReq extends BaseThriftDto {

    /**
     * 地区id
     */
    private Long id;

    /**
     * 地区编码
     */
    private String code;

    /**
     * 地区名称
     */
    private String name;
    /**
     * 地区简称
     */
    private String shortName;

    /**
     * 上级地区id
     */
    private Long parentId;

    public void checkParameter() {

//        AssertUtil.throwInvalidParamIfTrue(this.id == null, "id不能为空");
        AssertUtil.throwInvalidParamIfTrue(this.parentId == null, "父级id不能为空");

        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(this.name), "请输入地址名称");
//        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(this.code), "code不能为空");
//        AssertUtil.throwInvalidParamIfTrue(this.id.equals(this.parentId), "id与父级id不能一致");
    }

}