package com.sankuai.shangou.seashop.base.thrift.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;


@Data
public class QueryExpressCompanyDto {

    private Long id;
    /**
     * 快递名称
     */
    private String name;
    /**
     * 淘宝编号
     */
    private String taobaoCode;
    /**
     * 快递100对应物流编号
     */
    private String kuaidi100Code;
    /**
     * 快递鸟物流公司编号
     */
    private String kuaidiniaoCode;
    /**
     * 快递面单宽度
     */
    private Integer width;
    /**
     * 快递面单高度
     */
    private Integer height;
    /**
     * 快递公司logo
     */
    private String logo;
    /**
     * 快递公司面单背景图片
     */
    private String backgroundImage;
    /**
     * 快递公司状态（0：正常，1：删除）
     */
    private Integer status;
    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createDate;
    /**
     * 旺店通code
     */
    private String wangdiantongCode;
    /**
     * 聚水潭code
     */
    private String jushuitanCode;
    /**
     * 菠萝派code
     */
    private String boluopaiCode;
    /**
     * 美团code
     */
    private String meituanCode;
}
