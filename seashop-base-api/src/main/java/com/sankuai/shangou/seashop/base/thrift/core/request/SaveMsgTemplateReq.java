package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:02
 */
@Data
public class SaveMsgTemplateReq extends BaseParamReq {

    @ExaminField
    private String weixinAppletId;

    @ExaminField
    private String weixinAppletSecret;

    /**
     * 模版集合
     */
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.base.dao.core.domain.BaseWeixinMsgTemplate")
    private List<BaseMsgTemplateReq> values;

}
