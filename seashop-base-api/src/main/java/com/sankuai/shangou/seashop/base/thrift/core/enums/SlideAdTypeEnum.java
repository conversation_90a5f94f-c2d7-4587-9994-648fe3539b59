package com.sankuai.shangou.seashop.base.thrift.core.enums;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
public enum SlideAdTypeEnum {

    INITIAL(0, "原数据"),
    PLATFORM_HOME(1, "平台首页轮播图"),
    PLATFORM_LIMIT_TIME(2, "平台限时购轮播图"),
    SHOP_HOME(3, "店铺首页轮播图"),
    V_SHOP_HOME(4, "微店轮播图"),
    WEIXIN_HOME(5, "微信首页轮播图"),
    WAP_HOME(6, "触屏版首页轮播图"),
    WAP_SHOP_HOME(7, "触屏版微店首页轮播图"),
    IOS_SHOP_HOME(8, "IOS首页轮播图"),
    APP_ICON(10, "APP首页图标图"),
    APP_GIFTS(11, "APP积分商城轮播图"),
    APP_GUIDE(12, "引导页图"),
    NEAR_SHOP_BRANCH_HOME(13, "周边门店轮播图"),
    NEAR_SHOP_BRANCH_ICON(14, "周边门店图标图"),
    NEAR_SHOP_BRANCH_SPECIAL(15, "周边门店广告位"),
    NEAR_SHOP_BRANCH_HOME2(16, "周边门店轮播图2");

    private Integer type;
    private String desc;

    public Integer getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    SlideAdTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
