package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BaseArticleCategoryReq  extends BaseThriftDto {

    /**
     * 文章分类id
     */
    private Long id;

    /**
     * 父分类id
     */
    private Long parentCategoryId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 排序字段
     */
    private Long displaySequence;

    /**
     * 是否默认
     */
    private Boolean isDefault;

    public void checkParameter() {
        if (this.parentCategoryId == null) {
            throw new IllegalArgumentException("父分类不能为空");
        }
        if (StringUtils.isEmpty(this.name)) {
            throw new IllegalArgumentException("分类名称不能为空");
        }
    }
}
