package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/02 21:40
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Data
public class SaveProductSettingReq extends BaseParamReq {

    /**
     * 是否开启审核
     */
    private Boolean productAuditOnOff;

    /**
     * 是否开启销量显示
     */
    private Boolean productSaleCountOnOff;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(productAuditOnOff, "请选择是否开启审核");
        AssertUtil.throwInvalidParamIfNull(productSaleCountOnOff, "请选择是否开启销量显示");
    }
}
