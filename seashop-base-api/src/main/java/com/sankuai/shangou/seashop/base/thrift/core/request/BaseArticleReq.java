package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class BaseArticleReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    /**
     * 文章分类id
     */
    @ExaminField(description = "文章分类id")
    private Long categoryId;

    /**
     * 标题
     */
    @ExaminField(description = "标题")
    private String title;

    /**
     * icon url
     */
    @ExaminField(description = "iconUrl")
    private String iconUrl;

    /**
     * 添加时间
     */
    @ExaminField(description = "添加时间")
    private Date addDate;

    /**
     * 排序字段
     */
    @ExaminField(description = "排序字段")
    private Long displaySequence;

    /**
     * 是否显示
     */
    @ExaminField(description = "是否显示")
    private Boolean isRelease;

    /**
     * 文章内容
     */
    @ExaminField(description = "文章内容")
    private String content;

    /**
     * 文章seo标题
     */
    @ExaminField(description = "文章seo标题")
    private String seoTitle;

    /**
     * 文档seo详情
     */
    @ExaminField(description = "文档seo详情")
    private String seoDescription;

    /**
     * 文章seo关键字
     */
    @ExaminField(description = "文章seo关键字")
    private String seoKeywords;

    public void checkParameter() {
        if (this.categoryId == null) {
            throw new IllegalArgumentException("所属分类不能为空");
        }
        if (StringUtils.isEmpty(this.title)) {
            throw new IllegalArgumentException("文章标题不能为空");
        }
//        if (StringUtils.isEmpty(this.content)) {
//            throw new IllegalArgumentException("文章内容不能为空");
//        }
    }
}
