package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class BaseAllAgreementReq extends BaseThriftDto {
    private Long id;

    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 买家协议内容
     */
    private String buyerAgreementContent;

    /**
     * 卖家协议内容
     */
    private String sellerAgreementContent;

    /**
     * 隐私协议内容
     */
    private String privacyAgreementContent;

    public void checkParameter() {

        if (StringUtils.isEmpty(this.buyerAgreementContent)) {
            throw new IllegalArgumentException("买家协议内容不能为空");
        }
        if (StringUtils.isEmpty(this.sellerAgreementContent)) {
            throw new IllegalArgumentException("卖家协议内容不能为空");
        }
        if (StringUtils.isEmpty(this.privacyAgreementContent)) {
            throw new IllegalArgumentException("隐私协议内容不能为空");
        }
    }


    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}
