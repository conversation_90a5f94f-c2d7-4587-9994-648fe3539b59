package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/26 9:31
 */
@Data
public class SendAppletMessageReq extends BaseParamReq {
    /**
     * （买家）商家ID
     */
    private Long userId;

    /**
     * 所需下发的订阅模板id
     */
    private String templateId;

    /**
     * 点击模板卡片后的跳转页面，仅限本小程序内的页面。支持带参数,（示例index?foo=bar）。该字段不填则模板无跳转
     */
    private String page;

    /**
     * 模板内容，格式形如 { "key1": { "value": any }, "key2": { "value": any } }的object
     */
    private String data;

    /**
     * 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版
     */
    private String miniprogramState = "formal";

    /**
     * 进入小程序查看”的语言类型，支持zh_CN(简体中文)、en_US(英文)、zh_HK(繁体中文)、zh_TW(繁体中文)，默认为zh_CN返回参数
     */
    private String lang = "zh_CN";
}
