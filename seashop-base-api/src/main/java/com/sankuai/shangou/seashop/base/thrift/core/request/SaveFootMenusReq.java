package com.sankuai.shangou.seashop.base.thrift.core.request;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:46
 */
@Data
public class SaveFootMenusReq extends BaseParamReq {

    @Schema(description = "底部导航菜单列表")
    private List<AddOrUpdateFootMenusReq> footMenuList;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(CollUtil.isEmpty(footMenuList), "请至少设置一个底部导航栏");
        AssertUtil.throwIfTrue(footMenuList.size() > 5, "最多设置5个底部导航栏");
        footMenuList.forEach(AddOrUpdateFootMenusReq::checkParameter);
    }

}
