package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/12/1 15:34
 */
@Data
public class LogDetailResp extends BaseThriftDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作人账号
     */
    private String operationUserAccount;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 操作类型名称
     */
    private String operationName;

    /**
     * 字段变化详情
     */
    private String items;

    /**
     * 操作用户id
     */
    private Long operationUserId;
}
