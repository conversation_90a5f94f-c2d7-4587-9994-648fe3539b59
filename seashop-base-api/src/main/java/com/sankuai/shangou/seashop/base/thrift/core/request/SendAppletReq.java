package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 16:41
 */
@Data
public class SendAppletReq extends BaseThriftDto {

    /**
     * 用户ID，用来获取openId
     */
    private Long userId;

    /**
     * 传WxTemplatesEnum.TemplateNumEnum枚举的code,下面两个参数根据这个枚举值来填
     */
    private Integer templateNum;

    /**
     * 退款通知入参
     */
    private SendRefundNoticeReq sendRefundNoticeReq;

    /**
     * 订单发货通知入参
     */
    private SendOrderShippingNoticeReq sendOrderShippingNoticeReq;
    public void checkParameter(){
        AssertUtil.throwIfNull(userId, "userId不能为空");
        AssertUtil.throwIfNull(templateNum, "templateNum不能为空");
        boolean flag = sendRefundNoticeReq == null && sendOrderShippingNoticeReq == null;
        AssertUtil.throwIfTrue(flag, "sendRefundNoticeReq和sendOrderShippingNoticeReq不能同时为空");
        if(Objects.nonNull(sendRefundNoticeReq)){
            boolean flag1 = sendRefundNoticeReq.getThing6() == null || sendRefundNoticeReq.getThing6().length() > 20;
            AssertUtil.throwIfTrue(flag1, "thing6不能为空且长度不能超过20");
        }
        if(Objects.nonNull(sendOrderShippingNoticeReq)){
            boolean flag2 = sendOrderShippingNoticeReq.getThing1() == null || sendOrderShippingNoticeReq.getThing1().length() > 20;
            AssertUtil.throwIfTrue(flag2, "thing1不能为空且长度不能超过20");
            boolean flag3 = sendOrderShippingNoticeReq.getThing4() == null || sendOrderShippingNoticeReq.getThing4().length() > 20;
            AssertUtil.throwIfTrue(flag3, "thing4不能为空且长度不能超过20");
            boolean flag4 = sendOrderShippingNoticeReq.getThing6() == null || sendOrderShippingNoticeReq.getThing6().length() > 20;
            AssertUtil.throwIfTrue(flag4, "thing6不能为空且长度不能超过20");
        }
    }
}
