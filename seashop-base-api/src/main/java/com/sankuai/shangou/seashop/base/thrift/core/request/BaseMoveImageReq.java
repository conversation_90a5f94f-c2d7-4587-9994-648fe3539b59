package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

@Data
public class BaseMoveImageReq extends BaseThriftDto {
    /**
     * 图片id
     */
    private List<Long> ids;
    /**
     * 店铺id
     */
    private Long shopId;

    @JsonProperty
    private Long cId;

    public void checkParameter() {
        if (this.ids == null || this.ids.size() < 1) {
            throw new IllegalArgumentException("图片ids不能为空");
        }

        if (this.cId == null) {
            throw new IllegalArgumentException("分类id不能为空");
        }
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getcId() {
        return cId;
    }

    public void setcId(Long cId) {
        this.cId = cId;
    }
}
