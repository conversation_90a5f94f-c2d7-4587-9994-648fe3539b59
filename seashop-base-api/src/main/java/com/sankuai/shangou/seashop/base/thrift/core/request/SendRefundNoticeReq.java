package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 10:16
 * 退款原因 {{thing6.DATA}}
 * 退款时间 {{date4.DATA}}
 * 支付金额 {{amount5.DATA}}
 */
@Data
public class SendRefundNoticeReq extends BaseThriftDto {

    /**
     * 退款原因(20个以内字符,可汉字、数字、字母或符号组合)
     */
    private String thing6;

    /**
     * 退款时间(例如：2019年10月1日，或：2019年10月1日 15:01)
     */
    private String date4;

    /**
     * 支付金额(可带小数，结尾带元)
     */
    private String amount5;
}
