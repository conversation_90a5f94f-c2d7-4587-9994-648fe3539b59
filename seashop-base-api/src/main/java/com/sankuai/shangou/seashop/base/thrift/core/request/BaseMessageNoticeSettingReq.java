package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@Data
public class BaseMessageNoticeSettingReq  extends BaseThriftDto {

    /**
     * 数据库维护字典 前端由列表接口中获取
     */

    private Integer messageType;

    /**
     * 是否开启邮箱通知
     */
    private Boolean emaillNotice;

    /**
     * 是否开启短信通知
     */
    private Boolean smsNotice;

    /**
     * 是否开启微信通知
     */
    private Boolean wxNotice;
    public void checkParameter() {

        if (this.messageType == null) {
            throw new IllegalArgumentException("请设置消息类型");
        }
        if (this.emaillNotice == null) {
            throw new IllegalArgumentException("请设置是否开启邮箱通知");
        }
        if (this.smsNotice == null) {
            throw new IllegalArgumentException("请设置是否开启短信通知");
        }

        if (this.wxNotice == null) {
            throw new IllegalArgumentException("请设置是否开启微信通知");
        }


    }
}
