package com.sankuai.shangou.seashop.base.thrift.core.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public enum TaskStatusEnum {

    // 准备中
    READY(10, "准备中"),
    // 进行中
    PROCESSING(20, "进行中"),
    // 执行成功（完成）
    SUCCESS(30, "执行成功"),
    // 执行失败
    FAILED(40, "执行失败"),
    ;

    private final Integer code;
    private final String desc;

    TaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(TaskStatusEnum::getDesc)
                .orElse("");
    }
}
