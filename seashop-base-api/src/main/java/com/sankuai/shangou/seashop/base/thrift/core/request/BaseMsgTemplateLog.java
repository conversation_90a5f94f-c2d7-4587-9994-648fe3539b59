package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 11:08
 */
@Data
public class BaseMsgTemplateLog extends BaseParamReq {

    @ExaminField(description = "退款审核后通知商家模板ID")
    private String returnMoneyTemplateId;

    @ExaminField(description = "快递配送订单发货后通知模板ID")
    private String deliverTemplateId;
}
