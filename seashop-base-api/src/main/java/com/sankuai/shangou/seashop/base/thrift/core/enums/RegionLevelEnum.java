package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum RegionLevelEnum {
    Province(1, "省级"),
    City(2, "市级"),
    County(3, "区级"),
    Town(4, "镇级"),
    Village(5, "村级");

    private Integer code;
    private String desc;

    RegionLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RegionLevelEnum getByCode(Integer code) {
        for (RegionLevelEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
