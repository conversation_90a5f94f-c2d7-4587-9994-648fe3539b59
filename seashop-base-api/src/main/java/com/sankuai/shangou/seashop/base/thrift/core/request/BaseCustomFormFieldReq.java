package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

@Data
public class BaseCustomFormFieldReq  extends BaseParamReq {
    @PrimaryField
    private Long id;

    @ExaminField(description = "自定义表单ID")
    private Long formId;
    @ExaminField(description = "字段名称")
    private String fieldName;

    @ExaminField(description = "类型")
    private Integer type;

    @ExaminField(description = "格式")
    private Integer format;

    @ExaminField(description = "选项")
    private String option;

    @ExaminField(description = "是否必填")
    private Boolean isRequired;

    @ExaminField(description = "排序")
    private Long displaySequence;

    @ExaminField(description = "添加时间")
    private Date addedDate;
}
