package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum MessageTypeEnum {
    WeiXin(0, "微信"),
    Email(1, "邮件"),
    Coupon(2, "优惠券"),
    SMS(3, "短信")
    ;
    Integer code;
    String desc;

    MessageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MessageTypeEnum of(Integer code) {
        if (code == null) {
            return null;
        }
        for (MessageTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}