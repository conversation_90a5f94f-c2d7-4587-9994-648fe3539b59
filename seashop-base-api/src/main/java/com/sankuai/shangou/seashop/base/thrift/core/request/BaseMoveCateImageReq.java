package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@Data
public class BaseMoveCateImageReq extends BaseThriftDto {
    private Long sourceId;
    private Long targetId;
    /**
     * 店铺id
     */
    private long shopId;


    public void checkParameter() {
        if (this.sourceId == null) {
            throw new IllegalArgumentException("源分类id不能为空");
        }

        if (this.targetId == null) {
            throw new IllegalArgumentException("目标分类id不能为空");
        }
    }
}
