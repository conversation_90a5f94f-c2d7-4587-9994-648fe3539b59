package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.thrift.core.enums.MoveTypeEnum;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Data
public class MoveLimitTimeBuyReq extends BaseParamReq {

    private Long id;

    /**
     * 移动类型:1上移，-1下移
     */
    private Integer type;

    @Override
    public void checkParameter() {
        if (this.id == null || this.id <= 0) {
            throw new InvalidParamException("主键不能为空");
        }
        if (this.type == null) {
            throw new InvalidParamException("移动类型不能为空");
        }
        if (MoveTypeEnum.getEnumByType(this.type) == null) {
            throw new InvalidParamException("移动类型不正确");
        }
    }

}
