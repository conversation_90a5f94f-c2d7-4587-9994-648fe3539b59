package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum TemplateClientTypeEnum {
    PCIndex(0, "PC首页"),
    PCIndex_SELLER(24, "供应商pc首页"),
    WapIndex(1, "移动端首页"),
    SellerWapIndex(2, "供应商移动端首页 "),
    WXSmallProgramSellerWapIndex(17, "供应商小程序端端首页"),
    WapSpecial(11, "移动端专题"),
    SellerWapSpecial(12, "供应商移动端专题"),
    WXSmallProgram(13, "微信小程序首页"),
    AppIndex(14, "APP首页"),
    AppSpecial(15, "APP专题"),
    WXSmallProgramSpecial(16, "小程序专题"),
    SellerWxSmallProgramSpecial(18, "供应商小程序专题"),
    <PERSON><PERSON>(19, "头部页面"),
    <PERSON><PERSON>(20, "底部页面"),
    PCTOPIC(22, "PC专题"),
    PCTOPIC_SELLER(23, "供应商PC专题"),
    HEADER_SELLER(25, "供应商PC专题"),
    FOOTER_SELLER(26, "供应商PC专题"),;
    private Integer code;
    private String desc;

    TemplateClientTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TemplateClientTypeEnum getByCode(Integer code) {
        for (TemplateClientTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
