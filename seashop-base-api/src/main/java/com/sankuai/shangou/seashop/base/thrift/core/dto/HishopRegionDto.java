package com.sankuai.shangou.seashop.base.thrift.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class HishopRegionDto {
    @JsonProperty("id")
    private long id;
    @JsonProperty("code")
    private String code;
    @JsonProperty("name")
    private String name;
    @JsonProperty("short_name")
    private String short_name;
    @JsonProperty("status")

    private int status;
    @JsonProperty("parent_id")
    private long parent_id;
    @JsonProperty("region_level")
    private int region_level;
    @JsonProperty("left")
    private int left;
    @JsonProperty("right")
    private int right;

    @JsonProperty("Childs")
    private List<HishopRegionDto> child;
}
