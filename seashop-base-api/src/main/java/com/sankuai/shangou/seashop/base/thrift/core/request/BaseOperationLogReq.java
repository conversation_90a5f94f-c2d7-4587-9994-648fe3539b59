package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

@Data
public class BaseOperationLogReq extends BaseThriftDto {
    private long id;

    /**
     * 模块id
     */
    private long moduleId;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 操作类型
     */
    private int operationType;

    /**
     * 操作类型名称
     */
    private String operationName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 操作用户id
     */
    private long  operationUserId;

    /**
     * 操作账号
     */
    private String  operationUserAccount;

    /**
     * 操作用户名称
     */
    private String  operationUserName;

    /**
     * 操作的json
     */
    private String  operationContent;

}
