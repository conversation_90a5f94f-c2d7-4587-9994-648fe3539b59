package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;


@Data
public class EmailBodyReq  extends BaseThriftDto {
    /**
     * 发件人名车
     */
    private String sendFrom;

    /**
     * 主题
     */
    private String subject;

    /**
     * 邮件正文
     */
    private String body;

    /**
     * 唯一id 业务自己传入
     */
    private  Long requestId;

    /**
     * 收件人集合
     */
    private List<ContactReq> contactList;

}
