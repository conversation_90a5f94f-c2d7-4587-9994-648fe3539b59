package com.sankuai.shangou.seashop.base.thrift.core.response.audit;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor

@Data
public class ContentAuditResp extends BaseThriftDto {

    /**
     * 风控id
     */
    private Long riskId;



}
