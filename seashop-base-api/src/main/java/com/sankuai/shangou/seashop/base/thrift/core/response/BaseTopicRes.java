package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.Date;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class BaseTopicRes  extends BaseThriftDto {
    private Long id;

    /**
     * 专题名称
     */
    private String name;

    /**
     * 图表
     */
    private String frontCoverImage;

    /**
     * 专题图片
     */
    private String topImage;

    /**
     * 背景图片
     */
    private String backgroundImage;

    /**
     * 使用终端
     */
    private Integer platForm;

    /**
     * 标签
     */
    private String tags;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 是否推荐
     */
    private Boolean isRecommend;

    /**
     * 自定义热点
     */
    private String selfDefinetext;

    /**
     * 专题模块集合
     */
    private List<BaseTopicModuleRes> topicModules;
    /**
     * 是否首页
     */
    private Boolean home;

    private Date createTime;
    private Date modifyTime;

}
