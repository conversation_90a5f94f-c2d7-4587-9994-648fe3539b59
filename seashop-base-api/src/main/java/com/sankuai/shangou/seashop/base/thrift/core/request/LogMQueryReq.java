package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:11
 */
@Data
public class LogMQueryReq extends BasePageReq {

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 业务板块
     */
    private Integer moduleId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 具体的操作功能
     */
    private String actionName;

    /**
     * 操作内容
     */
    private String operationContent;
}
