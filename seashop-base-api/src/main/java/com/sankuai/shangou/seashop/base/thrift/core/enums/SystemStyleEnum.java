package com.sankuai.shangou.seashop.base.thrift.core.enums;

import java.util.Arrays;

public enum SystemStyleEnum {
    SHOPTHEME("shopTheme", "店铺主题风格"),
    PRODUCTCATETORY("productCatetory", "商品分类风格"),
    USERCENTER("userCenter", "用户中心风格"),
    PCSHOPTHEME("PC_ShopTheme", "PC店铺主题"),
    OFFICE_MARK("officeMark", "官方水印");

    private final String key;
    private final String desc;

    SystemStyleEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String key) {
        return Arrays.stream(values())
                .filter(e -> e.getKey().equals(key))
                .findFirst()
                .map(SystemStyleEnum::getDesc)
                .orElse("");
    }
}
