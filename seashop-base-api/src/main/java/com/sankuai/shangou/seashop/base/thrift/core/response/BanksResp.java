package com.sankuai.shangou.seashop.base.thrift.core.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2024-02-20
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BanksResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 银行id
     */
    private Long id;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;
}
