package com.sankuai.shangou.seashop.base.thrift.core.enums.audit;

import lombok.Getter;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @link https://km.sankuai.com/page/1308847397
 * @since 2022/04/25
 */
public enum AuditTypeEnum {
    // 商品送审
    GOODS_CONTENT_AUDIT(70015, "商品信息内容送审"),
    GOODS_COMPLIANCE_AUDIT(70016, "商品信息合规(成人用品)送审"),
    GOODS_COMPLIANCE_AUDIT_TEST(70011, "商品信息送审-测试环境"),

    // 内容同步送审
    CONTENT_TEXT_SYSTEM_SYNC_AUDIT(11377, "内容文本同步-系统文案送审"),

    // 内容异步送审
    // 文本
    CONTENT_TEXT_ORDER_COMMENT_AND_MAN_ASYNC_AUDIT(11376, "内容文本异步和人工-订单评价文字送审"),
    CONTENT_TEXT_SUPPLIER_AD_AND_MAN_ASYNC_AUDIT(11378, "内容文本异步和人工-供应商文字送审"),
    CONTENT_TEXT_GOODS_QA_AND_MAN_ASYNC_AUDIT(11375, "内容文本异步和人工-商品咨询文字送审"),
    IM_TEXT_QA_AND_MAN_SYNC_AUDIT(11614, "客服IM-文字送审(同步机审)"),
    // 图片
    CONTENT_ORDER_PIC_AND_MAN_ASYNC_AUDIT(20884, "内容图片异步和人工-订单评价图片送审"),
    CONTENT_SUPPLIER_PIC_AND_MAN_ASYNC_AUDIT(20885, "内容图片异步和人工-供应商图片送审"),
    IM_PIC_AND_MAN_ASYNC_AUDIT(21021, "客服IM-图片送审(异步机审)"),
    // 视频
    CONTENT_VIDEO_AND_MAN_ASYNC_AUDIT(40216, "内容视频异步和人工送审");


    /**
     * 对应的是风控类型
     */
    @Getter
    private Integer type;

    private String desc;

    public static Set<Integer> typeSet = new HashSet<>();

    static {
        for (AuditTypeEnum auditTypeEnum : AuditTypeEnum.values()) {
            typeSet.add(auditTypeEnum.type);
        }
    }

    AuditTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static AuditTypeEnum findByType(Integer type) {
        for (AuditTypeEnum auditTypeEnum : AuditTypeEnum.values()) {
            if (Objects.equals(auditTypeEnum.type, type)) {
                return auditTypeEnum;
            }
        }
        return null;
    }
}
