package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class UpdateExpressTemplateReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    @ExaminField(description = "快递公司名称")
    private String name;

    @ExaminField(description = "快递面单宽度")
    private Integer width;

    @ExaminField(description = "快递面单高度")
    private Integer height;

    @ExaminField(description = "快递公司logo")
    private String logo;

    @ExaminField(description = "快递公司面单背景图片")
    private String backgroundImage;


    @ExaminField(description = "店铺ID")
    private Long shopId = 0L;

    public void checkParameter() {
        if(StringUtils.isEmpty(name) || StringUtils.isEmpty(name.replaceAll(" ", ""))){
            throw new IllegalArgumentException("公司名称不能为空");
        }
        if(name.length() > 20){
            throw new IllegalArgumentException("公司名称长度不能超过20");
        }
    }
}
