package com.sankuai.shangou.seashop.base.thrift.core.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2024-02-20
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BanksListResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 银行id
     */
    private List<BanksResp> banksList;


}
