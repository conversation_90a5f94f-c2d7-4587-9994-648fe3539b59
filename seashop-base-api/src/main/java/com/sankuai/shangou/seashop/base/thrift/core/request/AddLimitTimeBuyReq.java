package com.sankuai.shangou.seashop.base.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Data
public class AddLimitTimeBuyReq extends BaseParamReq {

    /**
     * 图片保存URL
     */
    private String imageUrl;

    /**
     * 图片跳转URL
     */
    private String url;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.imageUrl)) {
            throw new InvalidParamException("图片保存URL不能为空");
        }
        if (StrUtil.isBlank(this.url)) {
            throw new InvalidParamException("图片跳转URL不能为空");
        }
    }
}
