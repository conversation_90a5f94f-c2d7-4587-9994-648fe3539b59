package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:11
 */
@Data
public class LogQueryResp extends BaseThriftDto {

    private Long id;

    /**
     * 业务板块id
     */
    private Integer moduleId;

    /**
     * 业务板块名称
     */
    private String moduleName;

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 操作类型名称
     */
    private String operationName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 操作人id
     */
    private Long operationUserId;

    /**
     * 操作人账号
     */
    private String operationUserAccount;

    /**
     * 操作人名称
     */
    private String operationUserName;

//    @FieldDoc(description = "操作数据详情")
//    private String operationContent;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 具体的操作功能
     */
    private String actionName;
}
