package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.List;

@Data
public class BaseArticleQueryReq extends BasePageReq {
    /**
     * 文章标题
     */
    private String title;
    /**
     * 文章分类id
     */
    private Long categoryId;

    /**
     * 文章分类id
     */
    private List<Long> categoryIds ;
    /**
     * 是否显示
     */
    private Boolean isRelease;


}
