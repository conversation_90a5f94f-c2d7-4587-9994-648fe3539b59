package com.sankuai.shangou.seashop.base.thrift.core.enums.audit;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/06 14:54
 */
public enum TextAuditTypeEnum {

    /**
     * 文本送审类型
     * 1-普通文本 2-数字 3-时间类型
     */
    TEXT("1", "普通文本"),
    NUMBER("2", "数字"),
    TIME("3", "时间类型"),
    ;

    @Getter
    private String code;
    private String desc;

    TextAuditTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
