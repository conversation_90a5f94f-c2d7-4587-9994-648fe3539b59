package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum RegionStatusEnum {
    Normal(0, "正常"),
    Delete(9, "删除 ");

    private Integer code;
    private String desc;

    RegionStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RegionStatusEnum getByCode(Integer code) {
        for (RegionStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
