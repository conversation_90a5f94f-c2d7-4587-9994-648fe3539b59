package com.sankuai.shangou.seashop.base.thrift.core.response.task;

import com.sankuai.shangou.seashop.base.thrift.core.dto.PlatformTaskDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PlatformTaskListResp {

    /**
     * 平台任务列表
     */
    private List<PlatformTaskDto> taskList;

}
