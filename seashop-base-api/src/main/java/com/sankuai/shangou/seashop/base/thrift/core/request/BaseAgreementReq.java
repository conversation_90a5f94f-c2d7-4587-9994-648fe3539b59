package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class BaseAgreementReq extends BaseThriftDto {
    private Long id;

    /**
     * 协议类型 0买家注册协议，1卖家入驻协议
     */
    private int agreementType;
    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 协议内容
     */
    private String agreementContent;

    public void checkParameter() {

        if (StringUtils.isEmpty(this.agreementContent)) {
            throw new IllegalArgumentException("协议内容不能为空");
        }
    }
}
