package com.sankuai.shangou.seashop.base.thrift.core.request.base;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MessageRecordCmdReq extends BaseParamReq {
    @PrimaryField
    private Long id;

    /**
     * 消息类别
     */
    @ExaminField(description = "消息类别 1邮件 2优惠券 3短信")
    private Integer messageType;

    @ExaminField(description = "消息类别")
    private String messageTypeDesc;

    @ExaminField(description = "内容类型 1文本  5卡券")
    private Integer contentType;

    /**
     * 内容类型描述
     */
    private String contentTypeDesc;

    @ExaminField(description = "发送内容")
    private String sendContent;

    @ExaminField(description = "发送对象")
    private String toUserLabel;

    /**
     * 发送状态
     */
    private Integer sendState;

    /**
     * 发送状态描述
     */
    private String sendStateDesc;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 发送时间描述
     */
    private String sendTimeDesc;

    /**
     * 群发发送的优惠券Id列表
     */
    private List<Long> couponIdList;
}
