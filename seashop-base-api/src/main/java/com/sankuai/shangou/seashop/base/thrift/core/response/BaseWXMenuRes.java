package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@Data
public class BaseWXMenuRes extends BaseThriftDto {
    private Long id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 链接类型
     */
    private Integer linkType;

    /**
     * 链接值
     */
    private String linkValue;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 是否自定义链接
     */
    private Integer whetherCustom;
    @Override
    public String toString() {
        return "BaseWXMenuRes{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", linkType=" + linkType +
            ", linkValue='" + linkValue + '\'' +
            ", parentId=" + parentId +
            ", whetherCustom=" + whetherCustom +
            '}';
    }
}
