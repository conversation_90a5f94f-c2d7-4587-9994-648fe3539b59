package com.sankuai.shangou.seashop.base.thrift.core.dto.audit;

import lombok.Getter;
import lombok.Setter;

/**
 * 视频审核结构 一次只能审核一条
 *
 * <AUTHOR>
 * @date 2023/12/06 15:05
 */
@Setter
@Getter
public class VideoAuditInfoDto {

    /**
     * 视频id 业务端视频唯一标识 必填
     */
    private Long videoId;

    /**
     * 视频地址 必填
     */
    private String videoUrl;

    /**
     * 视频时长 单位秒 必填
     */
    private Integer duration;

    /**
     * 区分正常增量还是导入 0-正常增量 1-优先级导入 2-其他导入
     */
    private Integer auditLevel;

    /**
     * 0-走人工审核 1-不走人工审核
     */
    private Integer manAudit;

    /**
     * 大象鉴权token
     */
    private String token;

}
