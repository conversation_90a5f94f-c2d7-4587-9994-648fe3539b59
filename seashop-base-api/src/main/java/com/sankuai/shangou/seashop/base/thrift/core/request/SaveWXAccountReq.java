package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class SaveWXAccountReq extends BaseParamReq {

    @ExaminField(description = "公众号AppId")
    private String weixinMpAppId;

    @ExaminField(description = "公众号AppSecret")
    private String weixinMpAppSecret;

    @Override
    public void checkParameter() {
        if (StringUtils.isEmpty(this.weixinMpAppId)) {
            throw new IllegalArgumentException("公众号AppId不能为空");
        }
        if(StringUtils.isEmpty(this.weixinMpAppSecret)){
            throw new IllegalArgumentException("公众号AppSecret不能为空");
        }
    }

    public String getWeixinMpAppId() {
        return weixinMpAppId;
    }

    public void setWeixinMpAppId(String weixinMpAppId) {
        this.weixinMpAppId = weixinMpAppId;
    }

    public String getWeixinMpAppSecret() {
        return weixinMpAppSecret;
    }

    public void setWeixinMpAppSecret(String weixinMpAppSecret) {
        this.weixinMpAppSecret = weixinMpAppSecret;
    }
}
