package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Data
public class BaseCustomFormReq extends BaseParamReq {

    @PrimaryField
    private Long id;
    @ExaminField(description = "表单名称")
    private String name;

    @ExaminField(description = "创建时间")
    private Date createDate;

    @ExaminField(description = "修改时间")
    private Date updateDate;

    /**
     * 自定义表单项集合
     */
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField")
    private List<BaseCustomFormFieldReq> formFields;

    /**
     * 无意义，为了配合操作日志建立
     */
    private Long shopId;
    public void checkParameter() {
        if (StringUtils.isEmpty(this.name)) {
            throw new IllegalArgumentException("表单名称不能为空");
        }
        if (this.formFields == null || this.formFields.size() < 1) {
            throw new IllegalArgumentException("表单项不能为空");
        }
        for (BaseCustomFormFieldReq field : this.formFields) {
            if (StringUtils.isNotEmpty(field.getOption()) && field.getOption().length() >= 2000) {
                throw new IllegalArgumentException("选项长度最多2000");
            }
            if (StringUtils.isNotEmpty(field.getOption()) && field.getOption().indexOf(",") > -1) {
                String[] arr = field.getOption().split(",");
                if (arr.length > 50) {
                    throw new IllegalArgumentException("最多50个选项");
                }
            }
        }

    }
}
