package com.sankuai.shangou.seashop.base.thrift.core.enums;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
public enum MoveTypeEnum {

    UP(1, "上移"),
    DOWN(-1, "下移");

    private Integer type;
    private String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    MoveTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 通过type获取枚举
     */
    public static MoveTypeEnum getEnumByType(Integer type) {
        for (MoveTypeEnum e : MoveTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return null;
    }
}
