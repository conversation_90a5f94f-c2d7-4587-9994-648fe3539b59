package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum TResultCode implements Code {
    SUCCESS(200, "请求成功"),
    BAD_REQUEST(400, "客户端请求错误"),
    UNAUTHORIZED(401, "未授权请求"),
    SERVER_ERROR(500, "服务端异常");

    private int value;
    private String desc;

    private TResultCode(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int value() {
        return this.value;
    }

    public String desc() {
        return this.desc;
    }
}