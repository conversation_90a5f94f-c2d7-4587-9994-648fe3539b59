package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author： liweisong
 * @create： 2023/11/22 17:06
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TradeSiteSettingsReq extends BaseParamReq {

    /**
     * 未付款超时
     */
    @ExaminField(description = "未付款超时")
    private Long unpaidTimeout;

    @ExaminField(description = "确认收货超时")
    private Long noReceivingTimeout;

    @ExaminField(description = "自动收货完成前时间")
    private Long beforeReceivingDays;

    @ExaminField(description = "延迟收货时间")
    private Long noReceivingDelayDays;

    @ExaminField(description = "关闭评价通道时限")
    private Long orderCommentTimeout;

    @ExaminField(description = "供应商未发货自动短信提醒时限")
    private Long orderWaitDeliveryRemindTime;

    @ExaminField(description = "企业网银限制金额")
    private BigDecimal companyBankOrderAmount;

    @ExaminField(description = "订单退货期限")
    private Long salesReturnTimeout;

    @ExaminField(description = "供应商自动确认售后时限")
    private Long shopConfirmTimeout;

    @ExaminField(description = "用户发货限时")
    private Long sendGoodsCloseTimeout;

    @ExaminField(description = "供应商确认到货时限")
    private Long shopNoReceivingTimeout;

    @ExaminField(description = "店铺Id(0默认是平台)")
    private Long shopId;

}
