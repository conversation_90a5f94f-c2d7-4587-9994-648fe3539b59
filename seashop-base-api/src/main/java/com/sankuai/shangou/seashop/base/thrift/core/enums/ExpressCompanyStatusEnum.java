package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum ExpressCompanyStatusEnum {
    ENABLED(0, "正常"),
    DISABLED(1, "禁用 ");

    private Integer code;
    private String desc;

    ExpressCompanyStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ExpressCompanyStatusEnum getByCode(Integer code) {
        for (ExpressCompanyStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
