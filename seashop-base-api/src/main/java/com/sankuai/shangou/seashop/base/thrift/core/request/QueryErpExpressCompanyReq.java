package com.sankuai.shangou.seashop.base.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.stream.Stream;

@Data
public class QueryErpExpressCompanyReq extends BaseParamReq {

    /**
     * 旺店通code
     */
    private String wangdiantongCode;

    /**
     * 聚水潭code
     */
    private String jushuitanCode;

    /**
     * 菠萝派code
     */
    private String boluopaiCode;

    /**
     * 美团code
     */
    private String meituanCode;

    /**
     * 快递鸟code
     */
    private String kuaiDiNiaoCode;

    @Override
    public void checkParameter() {
        long count = Stream.of(wangdiantongCode, jushuitanCode, boluopaiCode, meituanCode)
                .filter(StrUtil::isNotBlank)
                .count();
        if (count== 0 || count > 1) {
            throw new IllegalArgumentException("只能传一个code");
        }
    }
}
