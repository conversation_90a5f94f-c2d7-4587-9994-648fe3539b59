package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 10:17
 * 商品名称 {{thing1.DATA}}
 * 订单号  {{character_string2.DATA}}
 * 订单金额 {{amount7.DATA}}
 * 快递公司 {{thing4.DATA}}
 * 备注   {{thing6.DATA}}
 */
@Data
public class SendOrderShippingNoticeReq extends BaseParamReq {

    /**
     * 商品名称(20个以内字符,可汉字、数字、字母或符号组合)
     */
    private String thing1;

    /**
     * 订单号(32位以内数字、字母或符号组合)
     */
    private String character_string2;

    /**
     * 订单金额(可带小数，结尾带元)
     */
    private String amount7;

    /**
     * 快递公司(20个以内字符,可汉字、数字、字母或符号组合)
     */
    private String thing4;

    /**
     * 备注(20个以内字符,可汉字、数字、字母或符号组合)
     */
    private String thing6;
}
