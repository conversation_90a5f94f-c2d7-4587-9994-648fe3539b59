package com.sankuai.shangou.seashop.base.thrift.core.enums.audit;

/**
 * <AUTHOR>
 * @date 2023/12/06 14:43
 */
public enum BusinessTypeEnum {

    /**
     * 1、文本，2、图片，3、视频
     */
    TEXT(1, "文本"),
    IMAGE(2, "图片"),
    VIDEO(3, "视频");

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    BusinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // 根据code 获取枚举
    public static BusinessTypeEnum getEnumByCode(Integer code) {
        for (BusinessTypeEnum value : BusinessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
