package com.sankuai.shangou.seashop.base.thrift.core.request;


import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


/**
 * @author： liweisong
 * @create： 2023/11/23 9:23
 */
@Data
public class AddRefundReasonReq extends BaseParamReq {

    @ExaminField(description = "售后原因")
    private String afterSalesText;

    @ExaminField(description = "店铺Id(0默认是平台)")
    private Long shopId;


    public void checkParameter(){
        if(StringUtils.isEmpty(afterSalesText)){
            throw new IllegalArgumentException("售后原因不能为空");
        }
        if(afterSalesText.length() > 20){
            throw new IllegalArgumentException("售后原因不能超过20个字符");
        }
    }
}
