package com.sankuai.shangou.seashop.base.thrift.core.enums.audit;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/03/21 12:07
 */
@Getter
public enum ContentAdviceEnum {

    // 1-展示 2-必须屏蔽 3-自行决定
    SHOW(1, "展示"),

    MUST_SHIELD(2, "必须屏蔽"),

    SELF_DECIDE(3, "自行决定");

    private Integer code;

    private String desc;

    ContentAdviceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
