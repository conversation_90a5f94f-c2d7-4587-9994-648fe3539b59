package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

@Data
public class UpdateExpressStatusReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    @ExaminField(description = "快递公司状态（0：正常，1：删除）")
    private Integer status;


    @ExaminField(description = "店铺ID")
    private Long shopId = 0L;

    public void checkParameter() {
        if(id == null){
            throw new IllegalArgumentException("id不能为空");
        }
        if(status == null){
            throw new IllegalArgumentException("status不能为空");
        }
    }
}
