package com.sankuai.shangou.seashop.base.thrift.core.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LoadSubscribedExpressReq {
    /**
     * 业务类型，区分业务方
     */
    private int bizType;
    /**
     * 业务方id，expressId（快递主表主键id，相当于一个快递单）:bizId = 1:n
     */
    private String bizId;
    /**
     * 快递公司code
     */
    private String companyCode;
    /**
     * 快递单号
     */
    private String expressNo;
    /**
     * 快递进度排序方式，不传默认降序排列
     */
    private int sortType;

    private String receiveMobile;

}
