package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum SettleAccountsTypeEnum {
    SettleBank(0, "银行账户"),
    SettleWeiXin(1, "微信账户"),
    SettleAll(2, "均需验证");

    private int value;
    private String desc;

    private SettleAccountsTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public enum VerificationStatusEnum {
        NonMust(0, "非必填"),
        Must(1, "必填");

        private Integer code;
        private String desc;

        VerificationStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static VerificationStatusEnum getByCode(Integer code) {
            for (VerificationStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum VerificationTypeEnum {
        VerifyPhone(0, "验证手机"),
        VerifyEmail(1, "验证邮箱"),
        VerifyAll(2, "均需验证");

        private Integer code;
        private String desc;

        VerificationTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static VerificationTypeEnum getByCode(Integer code) {
            for (VerificationTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
