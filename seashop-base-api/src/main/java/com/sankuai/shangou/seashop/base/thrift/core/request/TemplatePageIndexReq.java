package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class TemplatePageIndexReq extends BaseParamReq {


    /**
     * 店铺id
     */
    private Long shopId;


    /**
     * 类型
     */
    private Integer type ;


    /**
     * json内容
     */
    private String content;



    public void checkParameter() {
        if (this.shopId == null) {
            throw new IllegalArgumentException("店铺id不能为空");
        }
        if (this.type == null) {
            throw new IllegalArgumentException("店铺id不能为空");
        }

        if (StringUtils.isEmpty(this.content)) {
            throw new IllegalArgumentException("页面内容不能为空");
        }

    }
}
