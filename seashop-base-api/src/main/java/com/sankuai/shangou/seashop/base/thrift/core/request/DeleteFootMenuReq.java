package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 14:51
 */
@Data
public class DeleteFootMenuReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    @ExaminField(description = "店铺Id(0默认是平台)")
    private Long shopId;
}
