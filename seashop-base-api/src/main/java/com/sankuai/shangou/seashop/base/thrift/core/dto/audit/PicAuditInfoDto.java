package com.sankuai.shangou.seashop.base.thrift.core.dto.audit;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * 图片审核类型结构
 *
 * <AUTHOR>
 * @date 2023/12/06 14:58
 */
@Setter
@Getter
@Builder
public class PicAuditInfoDto {

    /**
     * 图片id 业务端图片唯一标识 必填
     */
    private Long picId;

    /**
     * 图片标题 非必填
     */
    private String picTitle;

    /**
     * 图片绝对地址 必填
     */
    private String picUrl;

    /**
     * 图片缩略图地址 必填 和picUrl保持一致
     */
    private String picThumbnailUrl;

    /**
     * 图片相对地址 非必填
     */
    private String picRelativeUrl;

    /**
     * 大象鉴权token 非必填
     */
    private String token;

}
