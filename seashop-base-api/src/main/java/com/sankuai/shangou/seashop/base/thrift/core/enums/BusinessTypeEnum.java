package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum BusinessTypeEnum {
    Enterprise(0, "请求成功"),
    Personal(1, "仅个人可入驻"),
    All(2, "企业和个人均可");

    private int value;
    private String desc;

    private BusinessTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int value() {
        return this.value;
    }

    public String desc() {
        return this.desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
