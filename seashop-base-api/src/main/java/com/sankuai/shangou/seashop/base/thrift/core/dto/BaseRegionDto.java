package com.sankuai.shangou.seashop.base.thrift.core.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sankuai.shangou.seashop.base.thrift.core.enums.RegionStatusEnum;

import lombok.*;

@JsonSerialize
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Data
public class BaseRegionDto   {

    /**
     * 地址编号
     */
    @JsonProperty("Id")
    private Long id;
    /**
     * 美团区域编号
     */
    @JsonProperty("Code")
    private String code;
    /**
     * 区域名称
     */
    @JsonProperty("Name")
    private String name;
    /**
     * 区域简称
     */
    @JsonProperty("ShortName")
    private String shortName;
    /**
     * 状态
     */
    @JsonProperty("Status")
    private RegionStatusEnum status;
    /**
     * 地址级别
     */
    private Long parentId;
    /**
     * 地址级别
     */
    private int regionLevel;
    /**
     * 下级地址集合
     */
    @JsonProperty("Sub")
    private List<BaseRegionDto> sub;
}