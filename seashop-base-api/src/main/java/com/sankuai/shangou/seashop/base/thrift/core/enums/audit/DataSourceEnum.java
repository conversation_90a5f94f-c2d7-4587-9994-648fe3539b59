package com.sankuai.shangou.seashop.base.thrift.core.enums.audit;

/**
 * <AUTHOR>
 * @date 2023/12/06 14:40
 */
public enum DataSourceEnum {

    /**
     * 生产来源 1-商户 4-运营
     */
    MERCHANT("1", "商户"),
    OPERATION("4", "运营"),
    ;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    private String code;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    private String desc;


    DataSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
