package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@ToString
@Data
public class ExpressSiteSettingReq extends BaseParamReq {

    /**
     * 美团秘钥键
     */
    private String meiTuanAppKey;

    /**
     * 美团密码
     */
    private String meiTuanAppSecret;

    public void checkParameter() {
        if(StringUtils.isEmpty(meiTuanAppKey)){
            throw new IllegalArgumentException("meiTuanAppKey不能为空");
        }
        if(StringUtils.isEmpty(meiTuanAppSecret)){
            throw new IllegalArgumentException("meiTuanAppSecret不能为空");
        }
    }

}
