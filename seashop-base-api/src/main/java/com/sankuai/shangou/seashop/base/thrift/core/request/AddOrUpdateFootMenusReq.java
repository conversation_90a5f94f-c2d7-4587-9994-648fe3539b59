package com.sankuai.shangou.seashop.base.thrift.core.request;

import java.util.Date;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:46
 */
@Data
public class AddOrUpdateFootMenusReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    /**
     * 导航名称
     */
    @ExaminField(description = "导航名称")
    private String name;

    /**
     * 链接地址
     */
    @ExaminField(description = "链接地址")
    private String url;

    /**
     * 链接名称
     */
    @ExaminField(description = "链接名称")
    private String urlName;

    /**
     * 显示图片
     */
    @ExaminField(description = "显示图片")
    private String menuIcon;

    /**
     * 未选中显示图片
     */
    @ExaminField(description = "未选中显示图片")
    private String menuIconSel;

    /**
     * 菜单类型（1代表微信、2代表小程序）
     */
    @ExaminField(description = "菜单类型（1代表微信、2代表小程序）")
    private Integer type;

    /**
     * 店铺Id(0默认是平台)
     */
    @ExaminField(description = "店铺Id(0默认是平台)")
    private Long shopId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(StrUtil.isEmpty(name), "导航名称不能为空");
        AssertUtil.throwIfTrue(name.length() > 5, "导航名称不能超过5个字符");
        AssertUtil.throwIfNull(url, "请选择链接地址");
    }
}
