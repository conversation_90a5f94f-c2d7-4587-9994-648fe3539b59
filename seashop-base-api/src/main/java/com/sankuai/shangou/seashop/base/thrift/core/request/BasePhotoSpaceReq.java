package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminNameField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class BasePhotoSpaceReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    /**
     * 分类id
     */
    @ExaminField
    private Long photoCategoryId;
    /**
     * 店铺id
     */
    @ExaminField
    private Long shopId;

    /**
     * 图片名称
     */
    @ExaminNameField
    private String photoName;

    /**
     * 图片路径
     */
    @ExaminField
    private String photoPath;

    /**
     * 图片大小
     */
    @ExaminField
    private Long fileSize;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 最后修改时间
     */
    private Date lastupdateTime;

    /**
     * 端口
     */
    private int clientType;

    public void checkParameter() {
        if (this.photoCategoryId == null) {
            throw new IllegalArgumentException("所属分类不能为空");
        }

        if (StringUtils.isEmpty(this.photoName)) {
            throw new IllegalArgumentException("图片名称不能为空");
        }
        if (StringUtils.isEmpty(this.photoPath)) {
            throw new IllegalArgumentException("图片路径不能为空");
        }
    }
}
