package com.sankuai.shangou.seashop.base.thrift.core.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


@ToString
@Getter
@Setter
public class TreeRegionDto {

    /**
     * 地址编号
     */
    private Long id;
    /**
     * 美团区域编号
     */
    private String code;
    /**
     * 区域名称
     */
    private String name;

    /**
     * 区域简称
     */
    private String shortName;

    /**
     * 状态
     */
    private int status;

    /**
     * 父id
     */
    private Long parentId;
    /**
     * 地址级别
     */
    private int regionLevel;

    /**
     * 下级地址集合
     */
    private List<TreeRegionDto> sub;


}
