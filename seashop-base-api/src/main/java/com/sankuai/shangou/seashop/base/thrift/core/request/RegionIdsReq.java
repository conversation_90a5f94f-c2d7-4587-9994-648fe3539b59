package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/12/16 10:10
 */
@Data
public class RegionIdsReq extends BaseParamReq {

    /**
     * 地区id集合
     */
    private List<Integer> regionIds;

    /**
     * 乡镇id集合
     */
    private List<String> townIds;

    public void checkParameter(){
        if (CollectionUtils.isEmpty(regionIds) && CollectionUtils.isEmpty(townIds)){
            throw new IllegalArgumentException("regionIds和townIds不能同时为空");
        }
    }

}
