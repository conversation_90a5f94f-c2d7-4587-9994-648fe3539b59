package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class BaseTopicReq extends BaseParamReq {
    @PrimaryField
    private Long id;

    @ExaminField(description = "专题名称")
    private String name;

    @ExaminField(description = "图标")
    private String frontCoverImage;

    @ExaminField(description = "专题图片")
    private String topImage;

    @ExaminField(description = "背景图片")
    private String backgroundImage;

    /**
     * 使用终端 0:PC 1:微信 2:android 3:IOS  4:触屏  5:商城小程序 99:移动端
     */
    @ExaminField(description = "使用终端")
    private Integer platForm;


    @ExaminField(description = "标签")
    private String tags;

    @ExaminField(description = "店铺ID")
    private Long shopId;

    @ExaminField(description = "是否推荐")
    private Boolean isRecommend;

    @ExaminField(description = "自定义热点")
    private String selfDefinetext;

    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicModule")
    private List<BaseTopicModuleReq> topicModules;

    public void checkParameter() {
        if (StringUtils.isEmpty(this.name)) {
            throw new IllegalArgumentException("专题不能为空");
        }
    }

}
