package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import java.util.List;

public class BaseTopicModuleReq extends BaseThriftDto {
    @PrimaryField
    private Long id;

    @ExaminField(description = "专题id")
    private Long topicId;

    @ExaminField(description = "专题名称")
    private String name;

    @ExaminField(description = "标题位置")
    private Integer titleAlign;

    /**
     * 模板绑定商品
     */
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.base.dao.core.domain.BaseModuleProduct")
    private List<BaseModuleProductReq> moduleProducts;

}
