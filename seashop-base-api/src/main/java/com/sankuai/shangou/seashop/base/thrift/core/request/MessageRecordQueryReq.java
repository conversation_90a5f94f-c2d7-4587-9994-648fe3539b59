package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MessageRecordQueryReq extends BasePageReq {
    /**
     * 消息类型 0微信 1邮件 2优惠券 3短信
     */
    private Integer messageType;
    /**
     * 内容类型 0图文消息 1文本 2语音 3图片 4视频 5卡券
     */
    private Integer contentType;
    /**
     * 消息状态 0发送失败 1发送成功
     */
    private Integer messageStatus;
    /**
     * 发送开始时间
     */
    private Date sendStartTime;
    /**
     * 发送结束时间
     */
    private Date sendEndTime;
}
