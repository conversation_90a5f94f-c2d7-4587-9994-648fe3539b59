package com.sankuai.shangou.seashop.base.thrift.core.request;


import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


@Data
public class BaseSettledReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    @ExaminField(description = "商家类型 0、仅企业可入驻；1、仅个人可入驻；2、企业和个人均可")
    private int businessType;


    @ExaminField(description = "商家类型")
    private int settlementAccountType;

    @ExaminField(description = "试用天数")
    private Integer trialDays;

    @ExaminField(description = "地址必填")
    private int isCity;

    @ExaminField(description = "数必填")
    private int isPeopleNumber;

    @ExaminField(description = "详细地址必填")
    private int isAddress;

    @ExaminField(description = "营业执照号必填")
    private int isBusinessLicenseCode;

    @ExaminField(description = "经营范围必填")
    private int isBusinessScope;

    @ExaminField(description = "营业执照必填")
    private int isBusinessLicense;

    @ExaminField(description = "机构代码必填")
    private int isAgencyCode;

    @ExaminField(description = "机构代码证必填")
    private int isAgencyCodeLicense;

    @ExaminField(description = "纳税人证明必填")
    private int isTaxpayerToProve;

    @ExaminField(description = "验证类型")
    private int companyVerificationType;

    @ExaminField(description = "个人姓名必填")
    private int isSName;

    @ExaminField(description = "个人地址必填")
    private int isSCity;

    @ExaminField(description = "个人详细地址必填")
    private int isSAddress;

    @ExaminField(description = "个人身份证必填")
    private int isSidCard;

    @ExaminField(description = "个人身份证上传")
    private int isSidCardUrl;

    @ExaminField(description = "个人验证类型")
    private int selfVerificationType;

    @ExaminField(description = "企业自定义表单")
    private String customFormJson;

    @ExaminField(description = "个人自定义表单")
    private String personalCustomFormJson;

    private Long shopId;
    public void checkParameter() {
        if (StringUtils.isNotEmpty(this.customFormJson)) {
            boolean json = JsonUtil.isJson(this.customFormJson);
            if (!json){
                throw  new IllegalArgumentException("请传入正确的企业表单内容");
            }
        }

        if (StringUtils.isNotEmpty(this.personalCustomFormJson)) {
            boolean json = JsonUtil.isJson(this.personalCustomFormJson);
            if (!json){
                throw  new IllegalArgumentException("请传入正确的个人表单内容");
            }
        }
    }
}
