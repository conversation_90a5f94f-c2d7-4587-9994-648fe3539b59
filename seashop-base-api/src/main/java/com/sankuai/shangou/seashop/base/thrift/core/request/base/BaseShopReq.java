package com.sankuai.shangou.seashop.base.thrift.core.request.base;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.Data;

@Data
public class BaseShopReq extends BaseReq {

    @ExaminField(description = "店铺id")
    private Long shopId;

    public void checkParameter() {
        if (this.shopId == null) {
            throw new IllegalArgumentException("店铺id不能为空");
        }

    }
}
