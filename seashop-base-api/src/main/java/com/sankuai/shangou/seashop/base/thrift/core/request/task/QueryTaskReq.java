package com.sankuai.shangou.seashop.base.thrift.core.request.task;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class QueryTaskReq extends BasePageReq {

    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 查询环境
     */
    private String env;
    /**
     * 业务类型。1：导出任务
     */
    private Integer bizType;
    /**
     * 任务类型。具体的业务指定，需要唯一，最好具有一定的规则
     */
    private Integer taskType;
    /**
     * 任务状态列表
     */
    private List<Integer> taskStatusList;

    @Override
    public void checkParameter() {
        super.checkParameter();
        if (operatorId == null) {
            throw new InvalidParamException("operatorId不能为空");
        }
        if (StrUtil.isBlank(env)) {
            throw new InvalidParamException("env不能为空");
        }
    }
}
