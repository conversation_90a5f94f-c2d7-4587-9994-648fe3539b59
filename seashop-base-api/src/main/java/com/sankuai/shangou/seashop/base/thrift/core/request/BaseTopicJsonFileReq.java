package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BaseTopicJsonFileReq extends BaseThriftDto {

    /**
     * 客户端  t1 t2 t13之类
     */
    private String client;

    /**
     * 模板类型
     */
    private Integer type;

    /**
     * 页面json对象
     */
    private String jsonContent;

    /**
     * 店铺id
     */
    private Long shopId;

    public void checkParameter() {
        if (StringUtils.isEmpty(this.client)) {
            throw new IllegalArgumentException("模板id不能为空");
        }

//        if (this.type == null) {
//            throw new IllegalArgumentException("模版类型不能为空");
//        }

        if (StringUtils.isEmpty(this.jsonContent)) {
            throw new IllegalArgumentException("模板json内容不能为空");
        }
        boolean json = JsonUtil.isJson(this.jsonContent);
        if (!json){
            throw new IllegalArgumentException("请传入正确的可视化内容");
        }
        if (this.shopId == null) {
            throw new IllegalArgumentException("店铺id不能为空");
        }

    }
}
