package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BaseUpdateRegionReq extends BaseThriftDto {
    private Long id;

    /**
     * 地区名称
     */
    private String name;

    public void checkParameter() {

        AssertUtil.throwInvalidParamIfTrue(this.id == null, "id不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(this.name), "请输入地址名称");
    }
}
