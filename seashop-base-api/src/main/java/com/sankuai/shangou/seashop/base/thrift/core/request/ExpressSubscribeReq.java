package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.dianping.sc.express.dto.ExpressReceiverDTO;
import com.dianping.sc.express.dto.ExpressSenderDTO;
import lombok.Data;

@Data
public class ExpressSubscribeReq {

    /**
     * 业务id
     */
    private String bizId;
    /**
     * 快递对接平台
     */
    private int platform;
    /**
     * 快递公司code
     */
    private String companyCode;
    /**
     * 快递单号
     */
    private String expressNo;
    /**
     * 发件人对象
     */
    private ExpressSenderDTO sender;
    /**
     * 收件人对象
     */
    private ExpressReceiverDTO receiver;

}
