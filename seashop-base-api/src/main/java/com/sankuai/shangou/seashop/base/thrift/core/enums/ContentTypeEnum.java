package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum ContentTypeEnum {
    Graphic(0, "图文消息"),
    Text(1, "文本"),
    Voice(2, "语音"),
    Image(3, "图片"),
    Video(4, "视频"),
    Card(5, "卡券")
    ;

    Integer code;
    String desc;

    ContentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ContentTypeEnum of(Integer code) {
        if (code == null) {
            return null;
        }
        for (ContentTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}