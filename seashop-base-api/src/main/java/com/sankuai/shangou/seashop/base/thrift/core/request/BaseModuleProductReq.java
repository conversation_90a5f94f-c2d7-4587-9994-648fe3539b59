package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

public class BaseModuleProductReq extends BaseThriftDto {
    @PrimaryField
    private Long id;

    @ExaminField(description = "模块id")
    private Long moduleId;

    @ExaminField(description = "商品id")
    private Long productId;

    @ExaminField(description = "排序")
    private Long displaySequence;
}
