package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class AddExpressCompanyReq extends BaseParamReq {

    @ExaminField(description = "快递公司名称")
    private String name;

    @ExaminField(description = "快递鸟物流公司编号")
    private String kuaidiniaoCode;

    @ExaminField(description = "旺店通code")
    private String wangdiantongCode;

    @ExaminField(description = "聚水潭code")
    private String jushuitanCode;

    @ExaminField(description = "菠萝派code")
    private String boluopaiCode;

    @ExaminField(description = "美团code")
    private String meituanCode;

    @ExaminField(description = "店铺ID")
    private Long shopId = 0L;

    public void checkParameter() {
        if(StringUtils.isEmpty(name) || StringUtils.isEmpty(name.replaceAll(" ", ""))){
            throw new IllegalArgumentException("公司名称不能为空");
        }
        if(name.length() > 20){
            throw new IllegalArgumentException("公司名称长度不能超过20");
        }
        boolean kuaidiniaoCodeEmpty = StringUtils.isEmpty(kuaidiniaoCode) || StringUtils.isEmpty(kuaidiniaoCode.replaceAll(" ", ""));
        boolean wangdiantongCodeEmpty = StringUtils.isEmpty(wangdiantongCode) || StringUtils.isEmpty(wangdiantongCode.replaceAll(" ", ""));
        boolean jushuitanCodeEmpty = StringUtils.isEmpty(jushuitanCode) || StringUtils.isEmpty(jushuitanCode.replaceAll(" ", ""));
        boolean boluopaiCodeEmpty = StringUtils.isEmpty(boluopaiCode) || StringUtils.isEmpty(boluopaiCode.replaceAll(" ", ""));
        boolean meituanCodeEmpty = StringUtils.isEmpty(meituanCode) || StringUtils.isEmpty(meituanCode.replaceAll(" ", ""));
        if(kuaidiniaoCodeEmpty && wangdiantongCodeEmpty && jushuitanCodeEmpty && boluopaiCodeEmpty && meituanCodeEmpty){
            throw new IllegalArgumentException("快递公司Code，不能为空");
        }
    }

}
