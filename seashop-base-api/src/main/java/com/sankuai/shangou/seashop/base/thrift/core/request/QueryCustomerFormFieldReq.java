package com.sankuai.shangou.seashop.base.thrift.core.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 14:03
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Data
public class QueryCustomerFormFieldReq extends BaseParamReq {

    /**
     * 字段id列表
     */
    private List<Long> fieldIds;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(fieldIds), "fieldIds不能为空");
    }
}
