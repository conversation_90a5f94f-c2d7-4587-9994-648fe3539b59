package com.sankuai.shangou.seashop.base.thrift.core.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;

import java.util.List;

@JsonSerialize
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Getter
@Setter
public class GdRegionDto {
   private String status;
   private String info;
   private String infocode;
   private String count;
//   private  Object suggestion;
   public List<GdRegionItem> districts;
}
