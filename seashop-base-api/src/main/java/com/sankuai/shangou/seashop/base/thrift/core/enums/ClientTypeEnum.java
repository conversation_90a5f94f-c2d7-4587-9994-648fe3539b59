package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum ClientTypeEnum {
    Default("t1", "页脚服务");

    private String code;
    private String desc;

    ClientTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClientTypeEnum getByCode(String code) {
        for (ClientTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
