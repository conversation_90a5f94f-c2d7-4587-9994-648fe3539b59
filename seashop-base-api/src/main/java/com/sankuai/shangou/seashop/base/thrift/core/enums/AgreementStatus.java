package com.sankuai.shangou.seashop.base.thrift.core.enums;

public enum AgreementStatus {
    Normal(0, "正常"),
    Expire(1, "过期");

    private Integer code;
    private String desc;

    AgreementStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AgreementStatus getByCode(Integer code) {
        for (AgreementStatus value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
