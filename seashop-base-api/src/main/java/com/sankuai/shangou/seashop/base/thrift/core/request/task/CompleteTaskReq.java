package com.sankuai.shangou.seashop.base.thrift.core.request.task;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class CompleteTaskReq extends BaseThriftDto {

    private Long taskId;

    /**
     * 总记录数。多sheet导出的是所有sheet的数据总数
     */
    private Long totalNum;

    /**
     * 成功数。
     */
    private Long successNum;

    /**
     * 失败数
     */
    private Long failedNum;

    /**
     * 任务执行结果。如果执行失败内容为部分异常内容
     */
    private String executeResult;
    /**
     * 文件路径
     */
    private String filePath;
}
