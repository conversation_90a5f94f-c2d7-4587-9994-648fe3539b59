2025-07-11 09:39:22.449 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 09:39:22.588 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 24704 (E:\work\himallWork\himall-gw\seashop-gw-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 09:39:22.592 |-DEBUG [main][] -  com.sankuai.shangou.seashop.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 09:39:22.593 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-11 09:39:22.995 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-gw.yml, group=1.0.0] success
2025-07-11 09:39:22.996 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 09:39:25.250 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-11 09:39:28.407 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 09:39:28.471 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 09:39:29.476 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 09:39:31.385 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-11 09:39:32.577 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-11 09:39:37.045 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-11 09:39:43.560 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-gw) init on namesrv 124.71.221.178:9876
2025-07-11 09:39:52.194 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:39:54.766 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:39:55.454 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 09:39:55.454 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 09:39:55.456 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 09:39:55.457 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 09:40:03.432 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:40:03.432 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:MExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 09:40:03.437 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:40:05.411 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:40:13.060 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:40:13.061 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:sendMessageListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 09:40:50.485 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 09:40:50.601 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 24184 (E:\work\himallWork\himall-gw\seashop-gw-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 09:40:50.605 |-DEBUG [main][] -  com.sankuai.shangou.seashop.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 09:40:50.606 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-11 09:40:50.967 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-gw.yml, group=1.0.0] success
2025-07-11 09:40:50.968 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 09:40:53.188 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-11 09:40:56.621 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 09:40:56.690 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 09:40:57.933 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 09:41:00.285 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-11 09:41:01.731 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-11 09:41:09.236 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-11 09:41:14.155 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-gw) init on namesrv 124.71.221.178:9876
2025-07-11 09:41:21.136 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:41:23.058 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:41:30.623 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:41:30.623 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:MExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 09:41:30.634 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:41:33.493 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:41:40.770 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:41:40.770 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:sendMessageListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 09:41:45.233 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 09:41:47.525 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:41:49.218 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:41:55.144 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:41:55.145 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:mallExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 09:41:57.650 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 09:41:57.834 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:42:04.301 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-11 09:42:04.317 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-11 09:42:05.828 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:24184, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-07-11 09:42:06.658 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=423, lastTimeStamp=1752198125836}] - instanceId:[InstanceId{instanceId=*******:24184, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-07-11 09:42:06.833 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-gw.__share__].
2025-07-11 09:42:06.836 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-11 09:42:06.836 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-11 09:42:06.836 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-gw.__share__] jobSize:[0].
2025-07-11 09:42:13.349 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-11 09:42:16.647 |-INFO  [Thread-172][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-11 09:42:16.820 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-11 09:42:16.893 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-11 09:42:16.927 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-11 09:42:17.122 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-11 09:42:17.326 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [61] -| Started StartApp in 92.019 seconds (JVM running for 94.612)
2025-07-11 09:42:17.356 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [40] -| 服务启动成功！
2025-07-11 09:42:17.593 |-INFO  [task-5][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-11 09:42:17.593 |-INFO  [task-5][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-11 09:42:17.715 |-INFO  [task-4][6e7fb4ba79b81b15] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-11 09:42:17.754 |-INFO  [task-4][6e7fb4ba79b81b15] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-gw.yml, group=1.0.0
2025-07-11 09:42:18.063 |-INFO  [task-5][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-gw *******:8081 register finished
2025-07-11 09:42:19.288 |-INFO  [RMI TCP Connection(12)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 09:42:23.760 |-WARN  [RMI TCP Connection(14)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-11 09:49:55.620 |-INFO  [XNIO-1 task-1][cc8c4677c608fe88] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 09:49:55.668 |-ERROR [XNIO-1 task-1][cc8c4677c608fe88] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [72] -| login aop method execute error:{}
com.sankuai.shangou.seashop.base.boot.exception.LoginException: 未登录，请先登录
	at com.sankuai.shangou.seashop.core.commmon.auth.ShopAuthenticationHandler.getAccountAndCheckToken(ShopAuthenticationHandler.java:89) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.forceCheckLogin(LoginAspect.java:109) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:67) [seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$dedabb46.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:49:55.707 |-ERROR [XNIO-1 task-1][cc8c4677c608fe88] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [84] -| [GLOBAL_EXCEPTION] BusinessException:
com.sankuai.shangou.seashop.base.boot.exception.LoginException: 未登录，请先登录
	at com.sankuai.shangou.seashop.core.commmon.auth.ShopAuthenticationHandler.getAccountAndCheckToken(ShopAuthenticationHandler.java:89) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.forceCheckLogin(LoginAspect.java:109) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:67) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$dedabb46.queryBusinessCategoryTree(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:50:30.486 |-INFO  [XNIO-1 task-1][52cb7306890389c1] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 09:50:30.505 |-ERROR [XNIO-1 task-1][52cb7306890389c1] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [72] -| login aop method execute error:{}
com.sankuai.shangou.seashop.base.boot.exception.LoginException: 未登录，请先登录
	at com.sankuai.shangou.seashop.core.commmon.auth.ShopAuthenticationHandler.getAccountAndCheckToken(ShopAuthenticationHandler.java:89) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.forceCheckLogin(LoginAspect.java:109) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:67) [seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$dedabb46.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:50:30.507 |-ERROR [XNIO-1 task-1][52cb7306890389c1] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [84] -| [GLOBAL_EXCEPTION] BusinessException:
com.sankuai.shangou.seashop.base.boot.exception.LoginException: 未登录，请先登录
	at com.sankuai.shangou.seashop.core.commmon.auth.ShopAuthenticationHandler.getAccountAndCheckToken(ShopAuthenticationHandler.java:89) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.forceCheckLogin(LoginAspect.java:109) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:67) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$dedabb46.queryBusinessCategoryTree(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:51:31.231 |-INFO  [XNIO-1 task-1][0e1e03720e0b62d6] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 09:51:35.980 |-ERROR [XNIO-1 task-1][0e1e03720e0b62d6] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [72] -| login aop method execute error:{}
com.sankuai.shangou.seashop.base.boot.exception.LoginException: 未登录，请先登录
	at com.sankuai.shangou.seashop.core.commmon.auth.ShopAuthenticationHandler.getAccountAndCheckToken(ShopAuthenticationHandler.java:89) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.forceCheckLogin(LoginAspect.java:109) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:67) [seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$dedabb46.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:51:35.983 |-ERROR [XNIO-1 task-1][0e1e03720e0b62d6] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [84] -| [GLOBAL_EXCEPTION] BusinessException:
com.sankuai.shangou.seashop.base.boot.exception.LoginException: 未登录，请先登录
	at com.sankuai.shangou.seashop.core.commmon.auth.ShopAuthenticationHandler.getAccountAndCheckToken(ShopAuthenticationHandler.java:89) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.forceCheckLogin(LoginAspect.java:109) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:67) ~[seashop-base-security-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$dedabb46.queryBusinessCategoryTree(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.3-SNAPSHOT.jar:1.0.3-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:52:28.304 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 09:52:28.304 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 09:52:28.304 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 09:52:28.305 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 09:52:28.305 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:52:28.305 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.305 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.305 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.305 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.305 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.305 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.305 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.306 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.304 |-INFO  [Thread-152][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 09:52:28.306 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.306 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.306 |-INFO  [Thread-152][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:52:28.306 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.306 |-INFO  [Thread-153][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:28.307 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 09:52:28.309 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=423, lastTimeStamp=1752198748309}] instanceId:[InstanceId{instanceId=*******:24184, stable=false}] @ namespace:[himall-gw].
2025-07-11 09:52:28.333 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:52:34.347 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-11 09:52:34.355 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11 09:52:34.453 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:52:34.453 |-INFO  [Thread-172][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-11 09:52:34.760 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='chengpei_himall-gw', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-11 09:52:34.760 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-11 09:52:34.760 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-11 09:52:34.760 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-11 09:52:34.761 |-INFO  [Thread-171][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-11 09:52:34.761 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-11 09:52:34.788 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-11 09:52:34.818 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:52:34.819 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:52:34.820 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:05:16.994 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 16:05:17.145 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 32028 (E:\work\himallWork\himall-gw\seashop-gw-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 16:05:17.151 |-DEBUG [main][] -  com.sankuai.shangou.seashop.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 16:05:17.151 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-11 16:05:17.715 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-gw.yml, group=1.0.0] success
2025-07-11 16:05:17.715 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 16:05:21.045 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-11 16:05:27.026 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 16:05:27.128 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 16:05:29.211 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 16:05:31.586 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-11 16:05:33.622 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-11 16:05:40.775 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-11 16:05:46.454 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-gw) init on namesrv 124.71.221.178:9876
2025-07-11 16:05:53.277 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:05:57.406 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:06:05.481 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:06:05.482 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:MExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 16:06:05.488 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:06:08.111 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:06:16.347 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:06:16.347 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:sendMessageListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 16:06:21.402 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 16:06:24.375 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:06:26.237 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:06:31.483 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:06:31.483 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:mallExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 16:06:34.131 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 16:06:34.330 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 16:06:40.093 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-11 16:06:40.108 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-11 16:06:41.928 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:32028, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-07-11 16:06:42.699 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=427, lastTimeStamp=1752221201937}] - instanceId:[InstanceId{instanceId=*******:32028, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-07-11 16:06:42.844 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-gw.__share__].
2025-07-11 16:06:42.846 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-11 16:06:42.847 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-11 16:06:42.847 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-gw.__share__] jobSize:[0].
2025-07-11 16:06:48.450 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-11 16:06:51.753 |-INFO  [Thread-202][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-11 16:06:51.919 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-11 16:06:51.989 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-11 16:06:52.020 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-11 16:06:52.180 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-11 16:06:52.376 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [61] -| Started StartApp in 104.342 seconds (JVM running for 109.83)
2025-07-11 16:06:52.393 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [40] -| 服务启动成功！
2025-07-11 16:06:52.656 |-INFO  [task-5][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-11 16:06:52.656 |-INFO  [task-5][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-11 16:06:52.767 |-INFO  [task-1][76e60ac28260457a] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-11 16:06:52.768 |-INFO  [task-1][76e60ac28260457a] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-gw.yml, group=1.0.0
2025-07-11 16:06:53.085 |-INFO  [task-5][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-gw *******:8081 register finished
2025-07-11 16:06:56.083 |-INFO  [RMI TCP Connection(12)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 16:06:58.373 |-WARN  [RMI TCP Connection(10)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-11 16:10:10.102 |-INFO  [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 16:10:10.426 |-INFO  [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"],"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"whetherDelete":false}
2025-07-11 16:10:10.428 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"managerId":712,"managerName":"selleradmin","roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"]}
2025-07-11 16:10:10.449 |-INFO  [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=null
2025-07-11 16:10:10.490 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-07-11 16:10:10.491 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 34
2025-07-11 16:10:10.491 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-07-11 16:10:10.491 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:10:10.491 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162}
2025-07-11 16:10:10.491 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (34-byte body)
2025-07-11 16:10:31.402 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- HTTP/1.1 200 OK (20909ms)
2025-07-11 16:10:31.402 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] connection: keep-alive
2025-07-11 16:10:31.402 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] content-type: application/json
2025-07-11 16:10:31.402 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] date: Fri, 11 Jul 2025 08:10:31 GMT
2025-07-11 16:10:31.402 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] traceid: 6c869de5495b8c17
2025-07-11 16:10:31.403 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] transfer-encoding: chunked
2025-07-11 16:10:31.403 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Origin
2025-07-11 16:10:31.403 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Method
2025-07-11 16:10:31.403 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Headers
2025-07-11 16:10:31.403 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:10:31.407 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"data":null,"code":500,"message":"服务端异常","success":false}
2025-07-11 16:10:31.407 |-DEBUG [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END HTTP (68-byte body)
2025-07-11 16:10:31.419 |-WARN  [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| queryBusinessCategoryTree business error. request=null
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 16:15:25.491 |-INFO  [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 16:15:25.546 |-INFO  [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"],"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"whetherDelete":false}
2025-07-11 16:15:25.547 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"managerId":712,"managerName":"selleradmin","roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"]}
2025-07-11 16:15:25.547 |-INFO  [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=null
2025-07-11 16:15:25.547 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-07-11 16:15:25.548 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 34
2025-07-11 16:15:25.548 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-07-11 16:15:25.548 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:15:25.548 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162}
2025-07-11 16:15:25.548 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (34-byte body)
2025-07-11 16:16:25.559 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- ERROR SocketTimeoutException: timeout (60010ms)
2025-07-11 16:16:25.562 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146)
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323)
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at com.sun.proxy.$Proxy230.queryBusinessCategoryTree(Unknown Source)
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.lambda$queryBusinessCategoryTree$0(SellerBusinessCategoryRemoteService.java:30)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60)
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
	... 133 more

2025-07-11 16:16:25.563 |-DEBUG [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END ERROR
2025-07-11 16:16:25.572 |-ERROR [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [68] -| thrift call failed
feign.RetryableException: timeout executing POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree
	at feign.FeignException.errorExecuting(FeignException.java:278) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:110) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
	at com.sun.proxy.$Proxy230.queryBusinessCategoryTree(Unknown Source) ~[?:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.lambda$queryBusinessCategoryTree$0(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_121]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_121]
	at okio.InputStreamSource.read(JvmOkio.kt:93) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
2025-07-11 16:16:25.575 |-WARN  [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| queryBusinessCategoryTree business error. request=null
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:69) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 16:16:50.160 |-INFO  [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 16:16:50.274 |-INFO  [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"],"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"whetherDelete":false}
2025-07-11 16:16:50.274 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"managerId":712,"managerName":"selleradmin","roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"]}
2025-07-11 16:16:50.274 |-INFO  [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=null
2025-07-11 16:16:50.275 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-07-11 16:16:50.275 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 34
2025-07-11 16:16:50.275 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-07-11 16:16:50.275 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:16:50.275 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162}
2025-07-11 16:16:50.275 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (34-byte body)
2025-07-11 16:17:50.278 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- ERROR SocketTimeoutException: Read timed out (60002ms)
2025-07-11 16:17:50.279 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323)
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at com.sun.proxy.$Proxy230.queryBusinessCategoryTree(Unknown Source)
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.lambda$queryBusinessCategoryTree$0(SellerBusinessCategoryRemoteService.java:30)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60)
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:745)

2025-07-11 16:17:50.280 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END ERROR
2025-07-11 16:17:50.280 |-ERROR [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [68] -| thrift call failed
feign.RetryableException: Read timed out executing POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree
	at feign.FeignException.errorExecuting(FeignException.java:278) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:110) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
	at com.sun.proxy.$Proxy230.queryBusinessCategoryTree(Unknown Source) ~[?:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.lambda$queryBusinessCategoryTree$0(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_121]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_121]
	at okio.InputStreamSource.read(JvmOkio.kt:93) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
2025-07-11 16:17:50.282 |-WARN  [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| queryBusinessCategoryTree business error. request=null
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:69) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 16:20:58.150 |-INFO  [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 16:20:58.201 |-INFO  [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"],"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"whetherDelete":false}
2025-07-11 16:20:58.201 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"managerId":712,"managerName":"selleradmin","roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"]}
2025-07-11 16:20:58.201 |-INFO  [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=null
2025-07-11 16:20:58.202 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-07-11 16:20:58.202 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 34
2025-07-11 16:20:58.202 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-07-11 16:20:58.202 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:20:58.202 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162}
2025-07-11 16:20:58.203 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (34-byte body)
2025-07-11 16:21:39.979 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- HTTP/1.1 200 OK (41776ms)
2025-07-11 16:21:39.980 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] connection: keep-alive
2025-07-11 16:21:39.980 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] content-type: application/json
2025-07-11 16:21:39.980 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] date: Fri, 11 Jul 2025 08:21:39 GMT
2025-07-11 16:21:39.980 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] traceid: 9fa27d2b54aaf342
2025-07-11 16:21:39.980 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] transfer-encoding: chunked
2025-07-11 16:21:39.981 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Origin
2025-07-11 16:21:39.981 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Method
2025-07-11 16:21:39.981 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Headers
2025-07-11 16:21:39.981 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:21:39.982 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"}]"},"code":0,"message":null,"success":true}
2025-07-11 16:21:39.982 |-DEBUG [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END HTTP (21102-byte body)
2025-07-11 16:21:39.991 |-INFO  [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"}]"},"code":0,"message":null}
2025-07-11 16:21:46.672 |-INFO  [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 16:21:46.799 |-INFO  [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"],"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"whetherDelete":false}
2025-07-11 16:21:46.799 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"managerId":712,"managerName":"selleradmin","roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"]}
2025-07-11 16:21:46.799 |-INFO  [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=null
2025-07-11 16:21:46.800 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-07-11 16:21:46.800 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 34
2025-07-11 16:21:46.800 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-07-11 16:21:46.800 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:21:46.801 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162}
2025-07-11 16:21:46.801 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (34-byte body)
2025-07-11 16:22:32.519 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- HTTP/1.1 200 OK (45717ms)
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] connection: keep-alive
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] content-type: application/json
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] date: Fri, 11 Jul 2025 08:22:32 GMT
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] traceid: 2f088654423a4083
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] transfer-encoding: chunked
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Origin
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Method
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Headers
2025-07-11 16:22:32.520 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:22:32.521 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"}]"},"code":0,"message":null,"success":true}
2025-07-11 16:22:32.522 |-DEBUG [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END HTTP (21102-byte body)
2025-07-11 16:22:32.523 |-INFO  [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"}]"},"code":0,"message":null}
2025-07-11 16:23:36.688 |-INFO  [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 16:23:36.922 |-INFO  [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"],"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"whetherDelete":false}
2025-07-11 16:23:36.922 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店_1","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"managerId":712,"managerName":"selleradmin","roles":[154,156],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/store/manage","/store/manage","/trade/verification"]}
2025-07-11 16:23:36.922 |-INFO  [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=null
2025-07-11 16:23:36.923 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-07-11 16:23:36.923 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 34
2025-07-11 16:23:36.923 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-07-11 16:23:36.924 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-07-11 16:23:36.924 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162}
2025-07-11 16:23:36.924 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (34-byte body)
2025-07-11 16:24:36.934 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- ERROR SocketTimeoutException: timeout (60009ms)
2025-07-11 16:24:36.934 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146)
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323)
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at com.sun.proxy.$Proxy230.queryBusinessCategoryTree(Unknown Source)
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.lambda$queryBusinessCategoryTree$0(SellerBusinessCategoryRemoteService.java:30)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60)
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
	... 133 more

2025-07-11 16:24:36.935 |-DEBUG [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END ERROR
2025-07-11 16:24:36.935 |-ERROR [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [68] -| thrift call failed
feign.RetryableException: timeout executing POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree
	at feign.FeignException.errorExecuting(FeignException.java:278) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:110) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
	at com.sun.proxy.$Proxy230.queryBusinessCategoryTree(Unknown Source) ~[?:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.lambda$queryBusinessCategoryTree$0(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_121]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_121]
	at okio.InputStreamSource.read(JvmOkio.kt:93) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
2025-07-11 16:24:36.938 |-WARN  [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| queryBusinessCategoryTree business error. request=null
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:69) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService.queryBusinessCategoryTree(SellerBusinessCategoryRemoteService.java:30) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.lambda$queryBusinessCategoryTree$0(SellerApiBusinessCategoryQueryController.java:44) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController.queryBusinessCategoryTree(SellerApiBusinessCategoryQueryController.java:40) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$FastClassBySpringCGLIB$$39efd68e.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop.SellerApiBusinessCategoryQueryController$$EnhancerBySpringCGLIB$$41afe320.queryBusinessCategoryTree(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 16:25:20.819 |-INFO  [Thread-183][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 16:25:20.819 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 16:25:20.819 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 16:25:20.819 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 16:25:20.819 |-INFO  [Thread-183][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 16:25:20.819 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.819 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.819 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.819 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.819 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.819 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 16:25:20.820 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.820 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.820 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.820 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.820 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.820 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.820 |-INFO  [Thread-184][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:20.821 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 16:25:20.823 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 16:25:20.826 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=427, lastTimeStamp=1752222320826}] instanceId:[InstanceId{instanceId=*******:32028, stable=false}] @ namespace:[himall-gw].
2025-07-11 16:25:20.853 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 16:25:26.867 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-11 16:25:26.879 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11 16:25:27.043 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 16:25:27.044 |-INFO  [Thread-202][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-11 16:25:27.339 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='chengpei_himall-gw', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-11 16:25:27.340 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-11 16:25:27.340 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-11 16:25:27.340 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-11 16:25:27.340 |-INFO  [Thread-201][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-11 16:25:27.341 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-11 16:25:27.369 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-11 16:25:27.417 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:25:27.417 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:25:27.418 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:25:38.447 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 17:25:38.599 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 17628 (E:\work\himallWork\himall-gw\seashop-gw-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 17:25:38.605 |-DEBUG [main][] -  com.sankuai.shangou.seashop.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 17:25:38.605 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-11 17:25:39.041 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-gw.yml, group=1.0.0] success
2025-07-11 17:25:39.041 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 17:25:42.354 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-11 17:25:46.421 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 17:25:46.496 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 17:25:48.384 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 17:25:51.436 |-INFO  [redisson-netty-1-4][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-11 17:25:53.109 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-11 17:25:58.390 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-11 17:26:07.692 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-gw) init on namesrv 124.71.221.178:9876
2025-07-11 17:26:16.761 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:26:19.739 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:26:30.151 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:26:30.152 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:MExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 17:26:30.157 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:26:32.193 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:26:39.408 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:26:39.408 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:sendMessageListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 17:26:44.483 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 17:26:46.259 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:26:49.541 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:26:59.535 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:26:59.535 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:mallExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 17:27:02.139 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 17:27:02.338 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 17:27:09.442 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-11 17:27:09.461 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-11 17:27:11.937 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:17628, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-07-11 17:27:12.818 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=428, lastTimeStamp=1752226031945}] - instanceId:[InstanceId{instanceId=*******:17628, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-07-11 17:27:13.058 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-gw.__share__].
2025-07-11 17:27:13.061 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-11 17:27:13.061 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-11 17:27:13.061 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-gw.__share__] jobSize:[0].
2025-07-11 17:27:18.807 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-11 17:27:23.178 |-INFO  [Thread-217][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-11 17:27:23.349 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-11 17:27:23.425 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-11 17:27:23.460 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-11 17:27:23.662 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-11 17:27:23.886 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [61] -| Started StartApp in 112.502 seconds (JVM running for 116.483)
2025-07-11 17:27:23.913 |-INFO  [main][] -  com.sankuai.shangou.seashop.StartApp [40] -| 服务启动成功！
2025-07-11 17:27:24.143 |-INFO  [task-2][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-11 17:27:24.143 |-INFO  [task-2][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-11 17:27:24.248 |-INFO  [task-8][837fa4a6e54b00e4] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-11 17:27:24.249 |-INFO  [task-8][837fa4a6e54b00e4] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-gw.yml, group=1.0.0
2025-07-11 17:27:24.519 |-INFO  [task-2][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-gw *******:8081 register finished
2025-07-11 17:27:26.002 |-INFO  [RMI TCP Connection(5)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 17:27:29.145 |-WARN  [RMI TCP Connection(6)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-11 17:28:46.053 |-INFO  [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 17:28:46.264 |-INFO  [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"weiXinUser":false,"id":136662,"name":"15873120490","roleType":"MEMBER","userPhone":"15873120490","disable":false,"whetherDelete":false}
2025-07-11 17:28:46.265 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":136662,"name":"15873120490","roleType":"MEMBER","userPhone":"15873120490","disable":false,"whetherDelete":false,"weiXinUser":false}
2025-07-11 17:28:46.288 |-INFO  [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryMallCollocationList 请求入参. request={"productId":16591,"shopId":162}
2025-07-11 17:28:46.340 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] ---> POST http://localhost:8084/himall-trade/collocation/queryMallCollocationList HTTP/1.1
2025-07-11 17:28:46.340 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] Content-Length: 52
2025-07-11 17:28:46.340 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] Content-Type: application/json
2025-07-11 17:28:46.340 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] 
2025-07-11 17:28:46.340 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] {"operationShopId":0,"productId":16591,"shopId":162}
2025-07-11 17:28:46.340 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] ---> END HTTP (52-byte body)
2025-07-11 17:28:50.717 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] <--- ERROR ConnectException: Failed to connect to localhost/0:0:0:0:0:0:0:1:8084 (4376ms)
2025-07-11 17:28:50.722 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] java.net.ConnectException: Failed to connect to localhost/0:0:0:0:0:0:0:1:8084
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at com.sun.proxy.$Proxy269.queryMallCollocationList(Unknown Source)
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.lambda$null$0(MallApiCollocationQueryController.java:39)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60)
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.lambda$queryMallCollocationList$1(MallApiCollocationQueryController.java:38)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29)
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.queryMallCollocationList(MallApiCollocationQueryController.java:35)
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController$$FastClassBySpringCGLIB$$933382c7.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController$$EnhancerBySpringCGLIB$$310dc5b5.queryMallCollocationList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:745)
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:8084
		... 131 more
	Caused by: java.net.ConnectException: Connection refused: connect
		at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
		at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
		at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
		at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
		at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
		at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
		at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
		at java.net.Socket.connect(Socket.java:589)
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
		... 130 more
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
	... 130 more

2025-07-11 17:28:50.722 |-DEBUG [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] <--- END ERROR
2025-07-11 17:28:50.734 |-ERROR [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [68] -| thrift call failed
feign.RetryableException: Failed to connect to localhost/0:0:0:0:0:0:0:1:8084 executing POST http://localhost:8084/himall-trade/collocation/queryMallCollocationList
	at feign.FeignException.errorExecuting(FeignException.java:278) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:110) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
	at com.sun.proxy.$Proxy269.queryMallCollocationList(Unknown Source) ~[?:?]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.lambda$null$0(MallApiCollocationQueryController.java:39) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.lambda$queryMallCollocationList$1(MallApiCollocationQueryController.java:38) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.queryMallCollocationList(MallApiCollocationQueryController.java:35) [classes/:?]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController$$FastClassBySpringCGLIB$$933382c7.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController$$EnhancerBySpringCGLIB$$310dc5b5.queryMallCollocationList(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.net.ConnectException: Failed to connect to localhost/0:0:0:0:0:0:0:1:8084
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 111 more
	Suppressed: java.net.ConnectException: Failed to connect to localhost/127.0.0.1:8084
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
		at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
		at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
		at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
		at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
		at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
		at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
		at com.sun.proxy.$Proxy269.queryMallCollocationList(Unknown Source) ~[?:?]
		at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.lambda$null$0(MallApiCollocationQueryController.java:39) ~[classes/:?]
		at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
		at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.lambda$queryMallCollocationList$1(MallApiCollocationQueryController.java:38) ~[classes/:?]
		at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
		at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.queryMallCollocationList(MallApiCollocationQueryController.java:35) [classes/:?]
		at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController$$FastClassBySpringCGLIB$$933382c7.invoke(<generated>) [classes/:?]
		at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
		at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
		at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
		at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
		at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
		at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController$$EnhancerBySpringCGLIB$$310dc5b5.queryMallCollocationList(<generated>) [classes/:?]
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
		at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
		at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
		at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
		at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
		at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
		at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
		at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
		at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
		at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
		at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
		at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
		at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
		at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
		at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
		at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
		at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
		at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
		at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
		at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
		at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
		at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
		at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
		at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
		at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
		at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
		at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
		at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
		at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
		at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
	Caused by: java.net.ConnectException: Connection refused: connect
		at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method) ~[?:1.8.0_121]
		at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85) ~[?:1.8.0_121]
		at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350) ~[?:1.8.0_121]
		at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206) ~[?:1.8.0_121]
		at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188) ~[?:1.8.0_121]
		at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172) ~[?:1.8.0_121]
		at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_121]
		at java.net.Socket.connect(Socket.java:589) ~[?:1.8.0_121]
		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:?]
		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:?]
		... 130 more
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method) ~[?:1.8.0_121]
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188) ~[?:1.8.0_121]
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172) ~[?:1.8.0_121]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_121]
	at java.net.Socket.connect(Socket.java:589) ~[?:1.8.0_121]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 111 more
2025-07-11 17:28:50.803 |-WARN  [XNIO-1 task-1][6a5f8bf5c38948ac] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| queryMallCollocationList business error. request=ApiMallCollocationReq(productId=16591, shopId=162)
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:69) ~[classes/:?]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.lambda$queryMallCollocationList$1(MallApiCollocationQueryController.java:38) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController.queryMallCollocationList(MallApiCollocationQueryController.java:35) [classes/:?]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController$$FastClassBySpringCGLIB$$933382c7.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion.MallApiCollocationQueryController$$EnhancerBySpringCGLIB$$310dc5b5.queryMallCollocationList(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 17:30:31.872 |-INFO  [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-11 17:30:31.921 |-INFO  [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"weiXinUser":false,"id":136662,"name":"15873120490","roleType":"MEMBER","userPhone":"15873120490","disable":false,"whetherDelete":false}
2025-07-11 17:30:31.921 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":136662,"name":"15873120490","roleType":"MEMBER","userPhone":"15873120490","disable":false,"whetherDelete":false,"weiXinUser":false}
2025-07-11 17:30:31.921 |-INFO  [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryMallCollocationList 请求入参. request={"productId":16591,"shopId":162}
2025-07-11 17:30:31.922 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] ---> POST http://localhost:8084/himall-trade/collocation/queryMallCollocationList HTTP/1.1
2025-07-11 17:30:31.922 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] Content-Length: 52
2025-07-11 17:30:31.922 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] Content-Type: application/json
2025-07-11 17:30:31.922 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] 
2025-07-11 17:30:31.922 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] {"operationShopId":0,"productId":16591,"shopId":162}
2025-07-11 17:30:31.923 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] ---> END HTTP (52-byte body)
2025-07-11 17:30:44.509 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] <--- HTTP/1.1 200 OK (12585ms)
2025-07-11 17:30:44.509 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] connection: keep-alive
2025-07-11 17:30:44.509 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] content-type: application/json
2025-07-11 17:30:44.509 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] date: Fri, 11 Jul 2025 09:30:44 GMT
2025-07-11 17:30:44.509 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] traceid: 1234c8b7945e7e8b
2025-07-11 17:30:44.510 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] transfer-encoding: chunked
2025-07-11 17:30:44.510 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] vary: Origin
2025-07-11 17:30:44.510 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] vary: Access-Control-Request-Method
2025-07-11 17:30:44.510 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] vary: Access-Control-Request-Headers
2025-07-11 17:30:44.510 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] 
2025-07-11 17:30:44.513 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] {"data":{"operationUserId":null,"operationShopId":0,"collocationRespList":null},"code":0,"message":null,"success":true}
2025-07-11 17:30:44.513 |-DEBUG [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign [72] -| [CollocationQueryFeign#queryMallCollocationList] <--- END HTTP (119-byte body)
2025-07-11 17:30:44.578 |-INFO  [XNIO-1 task-1][1234c8b7945e7e8b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryMallCollocationList success. 请求结果. response={"data":{"collocationRespList":null},"code":0,"message":null}
2025-07-11 17:31:00.473 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 17:31:00.473 |-INFO  [Thread-192][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 17:31:00.473 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 17:31:00.473 |-INFO  [Thread-192][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.473 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.473 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.474 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.474 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.474 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.474 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.474 |-INFO  [Thread-193][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:00.476 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 17:31:00.478 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=428, lastTimeStamp=1752226260478}] instanceId:[InstanceId{instanceId=*******:17628, stable=false}] @ namespace:[himall-gw].
2025-07-11 17:31:00.499 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 17:31:06.515 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-11 17:31:06.524 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11 17:31:06.691 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 17:31:06.691 |-INFO  [Thread-217][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-11 17:31:07.021 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='chengpei_himall-gw', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-11 17:31:07.021 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-11 17:31:07.021 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-11 17:31:07.022 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-11 17:31:07.022 |-INFO  [Thread-216][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-11 17:31:07.023 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-11 17:31:07.059 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-11 17:31:07.088 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:31:07.089 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:31:07.089 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
