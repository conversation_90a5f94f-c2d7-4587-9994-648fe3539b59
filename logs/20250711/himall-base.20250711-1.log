2025-07-11 09:29:12.251 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 09:29:12.402 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 25248 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 09:29:12.403 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 09:29:12.403 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-11 09:29:12.944 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-11 09:29:12.944 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 09:29:15.787 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-11 09:29:20.352 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 09:29:20.434 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 09:29:20.865 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-11 09:29:21.702 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-11 09:29:21.708 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-11 09:29:22.059 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-11 09:29:22.059 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-11 09:29:22.060 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-11 09:29:22.060 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-11 09:29:25.013 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@4c9ed40'
2025-07-11 09:29:25.363 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-11 09:29:25.472 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-11 09:29:25.480 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-11 09:29:25.489 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-11 09:29:25.496 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-11 09:29:25.505 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-11 09:29:25.514 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-11 09:29:25.530 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-11 09:29:25.535 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-11 09:29:25.551 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-11 09:29:25.558 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-11 09:29:25.563 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-11 09:29:25.574 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-11 09:29:25.582 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-11 09:29:25.591 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-11 09:29:25.600 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-11 09:29:25.610 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-11 09:29:25.617 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-11 09:29:25.639 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-11 09:29:25.658 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-11 09:29:25.675 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-11 09:29:25.690 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-11 09:29:25.705 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-11 09:29:25.718 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-11 09:29:25.733 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-11 09:29:25.746 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-11 09:29:25.758 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-11 09:29:25.774 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-11 09:29:25.793 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-11 09:29:25.812 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-11 09:29:25.829 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-11 09:29:25.850 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-11 09:29:25.867 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-11 09:29:25.890 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-11 09:29:25.904 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-11 09:29:25.919 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-11 09:29:25.935 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-11 09:29:25.960 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-11 09:29:25.980 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-11 09:29:25.999 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-11 09:29:26.017 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-11 09:29:26.039 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-11 09:29:26.046 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-11 09:29:26.055 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-11 09:29:26.062 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-11 09:29:26.085 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-11 09:29:26.094 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-11 09:29:26.111 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-11 09:29:26.129 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-11 09:29:26.145 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-11 09:29:26.161 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-11 09:29:26.178 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-11 09:29:26.211 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-11 09:29:26.232 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-11 09:29:26.259 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-11 09:29:26.279 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-11 09:29:26.295 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-11 09:29:26.310 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-11 09:29:26.324 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-11 09:29:26.343 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-11 09:29:26.366 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-11 09:29:26.383 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-11 09:29:26.400 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-11 09:29:26.441 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-11 09:29:26.461 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-11 09:29:26.480 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-11 09:29:26.498 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-11 09:29:26.501 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-11 09:29:26.513 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-11 09:29:27.186 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-11 09:29:31.542 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-11 09:29:39.298 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 09:29:41.187 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-11 09:29:42.421 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-11 09:29:42.775 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:29:45.799 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:29:51.858 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:29:51.858 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 09:29:53.197 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 09:29:54.764 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-11 09:29:55.742 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-07-11 09:30:02.872 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 09:30:03.734 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:30:04.138 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:30:04.230 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:30:05.951 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:30:11.130 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:30:11.130 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 09:30:11.139 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:30:13.138 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:30:18.578 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:30:18.578 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 09:30:18.588 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:30:20.948 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:30:27.392 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:30:27.392 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-11 09:30:27.401 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:30:29.678 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:30:35.094 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:30:35.094 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-11 09:30:36.341 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:30:38.571 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:30:44.890 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:30:44.891 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-11 09:30:44.912 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:30:46.608 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:30:51.513 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:30:51.513 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-11 09:30:51.523 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:30:53.144 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:30:59.149 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:30:59.150 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-11 09:30:59.158 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:31:00.865 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:31:05.803 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:31:05.804 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-11 09:31:05.812 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:31:07.610 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:31:12.782 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:31:12.782 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-11 09:31:12.792 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:31:14.465 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:31:19.100 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:31:19.100 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-11 09:31:19.111 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:31:20.658 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:31:25.453 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:31:25.453 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-11 09:31:25.463 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:31:27.308 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:31:32.636 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:31:32.637 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-11 09:31:32.646 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:31:34.136 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:31:39.278 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:31:39.279 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-11 09:31:39.287 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:31:40.746 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:31:47.230 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:31:47.231 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-11 09:31:53.743 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-11 09:31:53.761 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-11 09:31:55.079 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:25248, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 09:31:55.943 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=335, lastTimeStamp=1752197515088}] - instanceId:[InstanceId{instanceId=*******:25248, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 09:31:56.140 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-11 09:31:56.144 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-11 09:31:56.144 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-11 09:31:56.145 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-11 09:31:58.963 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-11 09:32:02.651 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@64a07d55[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-11 09:32:02.651 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5e9feddd[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-11 09:32:02.651 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6dfce79d[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-11 09:32:02.651 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5c1c44b9[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-11 09:32:02.687 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b31c0ba[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-11 09:32:02.687 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1ae12304[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-11 09:32:02.687 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@10abffca[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-11 09:32:09.379 |-INFO  [Thread-310][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-11 09:32:09.983 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-11 09:32:10.082 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-11 09:32:10.131 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-11 09:32:10.392 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-11 09:32:10.686 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 184.2 seconds (JVM running for 188.029)
2025-07-11 09:32:11.283 |-INFO  [task-14][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-11 09:32:11.283 |-INFO  [task-14][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-11 09:32:11.666 |-INFO  [task-14][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-11 09:32:14.464 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-11 09:32:14.471 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-11 09:32:14.477 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-11 09:32:14.620 |-INFO  [task-12][467fa42da3ff73cf] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-11 09:32:14.621 |-INFO  [task-12][467fa42da3ff73cf] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-11 09:32:14.842 |-INFO  [RMI TCP Connection(50)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 09:32:14.960 |-ERROR [task-15][672e13ab4f4e76fe] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-11 09:32:15.007 |-INFO  [task-15][672e13ab4f4e76fe] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:32:20.973 |-WARN  [RMI TCP Connection(53)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 09:34:48.903 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 09:34:48.903 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 09:34:48.903 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 09:34:48.903 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.903 |-INFO  [Thread-281][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:34:48.905 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 09:34:48.909 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=335, lastTimeStamp=1752197688909}] instanceId:[InstanceId{instanceId=*******:25248, stable=false}] @ namespace:[himall-base].
2025-07-11 09:34:48.938 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:35:09.144 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 09:35:09.292 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 26864 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 09:35:09.293 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 09:35:09.293 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-11 09:35:09.723 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-11 09:35:09.723 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 09:35:11.960 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-11 09:35:15.495 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 09:35:15.566 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 09:35:15.949 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-11 09:35:16.509 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-11 09:35:16.514 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-11 09:35:16.781 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-11 09:35:16.781 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-11 09:35:16.782 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-11 09:35:16.782 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-11 09:35:19.644 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1b3ba1e3'
2025-07-11 09:35:19.991 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-11 09:35:20.091 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-11 09:35:20.099 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-11 09:35:20.110 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-11 09:35:20.119 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-11 09:35:20.129 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-11 09:35:20.136 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-11 09:35:20.156 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-11 09:35:20.162 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-11 09:35:20.180 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-11 09:35:20.187 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-11 09:35:20.196 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-11 09:35:20.207 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-11 09:35:20.219 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-11 09:35:20.227 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-11 09:35:20.237 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-11 09:35:20.245 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-11 09:35:20.251 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-11 09:35:20.271 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-11 09:35:20.294 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-11 09:35:20.312 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-11 09:35:20.331 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-11 09:35:20.351 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-11 09:35:20.371 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-11 09:35:20.393 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-11 09:35:20.415 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-11 09:35:20.440 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-11 09:35:20.463 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-11 09:35:20.481 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-11 09:35:20.504 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-11 09:35:20.524 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-11 09:35:20.545 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-11 09:35:20.562 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-11 09:35:20.588 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-11 09:35:20.627 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-11 09:35:20.656 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-11 09:35:20.678 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-11 09:35:20.706 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-11 09:35:20.724 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-11 09:35:20.740 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-11 09:35:20.755 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-11 09:35:20.772 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-11 09:35:20.777 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-11 09:35:20.782 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-11 09:35:20.786 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-11 09:35:20.790 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-11 09:35:20.798 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-11 09:35:20.812 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-11 09:35:20.831 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-11 09:35:20.853 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-11 09:35:20.871 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-11 09:35:20.889 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-11 09:35:20.913 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-11 09:35:20.932 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-11 09:35:20.955 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-11 09:35:20.972 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-11 09:35:20.988 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-11 09:35:21.002 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-11 09:35:21.017 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-11 09:35:21.043 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-11 09:35:21.072 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-11 09:35:21.088 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-11 09:35:21.109 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-11 09:35:21.149 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-11 09:35:21.167 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-11 09:35:21.183 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-11 09:35:21.197 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-11 09:35:21.201 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-11 09:35:21.213 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-11 09:35:21.922 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-11 09:35:25.775 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-11 09:35:32.241 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 09:35:33.386 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-11 09:35:34.561 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-11 09:35:34.788 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:35:36.339 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:35:40.998 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:35:40.998 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 09:35:42.064 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 09:35:43.445 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-11 09:35:44.351 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-07-11 09:35:49.032 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 09:35:49.422 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:35:49.757 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:35:49.830 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:35:51.373 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:35:55.961 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:35:55.961 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 09:35:55.972 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:35:57.482 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:02.046 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:02.047 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 09:36:02.055 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:03.545 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:08.063 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:08.063 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-11 09:36:08.070 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:09.553 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:14.272 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:14.272 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-11 09:36:15.190 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:16.654 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:21.179 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:21.179 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-11 09:36:21.198 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:22.681 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:27.741 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:27.741 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-11 09:36:27.752 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:29.445 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:36.021 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:36.021 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-11 09:36:36.029 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:37.645 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:43.253 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:43.253 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-11 09:36:43.263 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:44.895 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:49.829 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:49.829 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-11 09:36:49.836 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:51.415 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:36:56.279 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:36:56.281 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-11 09:36:56.290 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:36:57.870 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:37:02.667 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:37:02.668 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-11 09:37:02.676 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:37:04.178 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:37:09.271 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:37:09.271 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-11 09:37:09.282 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:37:11.035 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:37:15.855 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:37:15.856 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-11 09:37:15.865 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:37:17.473 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:37:22.486 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:37:22.486 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-11 09:37:28.184 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-11 09:37:28.200 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-11 09:37:29.580 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:26864, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 09:37:30.442 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=335, lastTimeStamp=1752197849587}] - instanceId:[InstanceId{instanceId=*******:26864, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 09:37:30.610 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-11 09:37:30.614 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-11 09:37:30.614 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-11 09:37:30.614 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-11 09:37:33.459 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-11 09:37:36.967 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5379bea3[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-11 09:37:36.968 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7ffa2be[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-11 09:37:36.968 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5b4b3c51[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-11 09:37:36.968 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a4cfae4[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-11 09:37:37.011 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4ded3a8[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-11 09:37:37.011 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7e4e6bdf[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-11 09:37:37.011 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7ce579f3[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-11 09:37:42.583 |-INFO  [Thread-320][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-11 09:37:42.734 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-11 09:37:42.809 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-11 09:37:42.844 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-11 09:37:43.052 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-11 09:37:43.261 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 159.803 seconds (JVM running for 163.081)
2025-07-11 09:37:43.629 |-INFO  [task-10][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-11 09:37:43.629 |-INFO  [task-10][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-11 09:37:43.961 |-INFO  [task-10][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-11 09:37:45.847 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-11 09:37:45.851 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-11 09:37:45.855 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-11 09:37:45.949 |-INFO  [task-11][e7d70c806bbc2f62] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-11 09:37:45.950 |-INFO  [task-11][e7d70c806bbc2f62] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-11 09:37:46.315 |-ERROR [task-14][03f53580b4a52932] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-11 09:37:46.371 |-INFO  [task-14][03f53580b4a52932] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:37:46.717 |-INFO  [RMI TCP Connection(7)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 09:37:49.601 |-WARN  [RMI TCP Connection(8)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-11 09:38:15.965 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 09:38:15.965 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 09:38:15.965 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:38:15.965 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 09:38:15.965 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.965 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.965 |-INFO  [Thread-292][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-292][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.966 |-INFO  [Thread-293][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:38:15.967 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 09:38:15.970 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=335, lastTimeStamp=1752197895969}] instanceId:[InstanceId{instanceId=*******:26864, stable=false}] @ namespace:[himall-base].
2025-07-11 09:38:15.995 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:38:35.006 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 09:38:35.159 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 28268 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 09:38:35.159 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 09:38:35.160 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-11 09:38:35.773 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-11 09:38:35.773 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 09:38:38.082 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-11 09:38:41.596 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 09:38:41.686 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 09:38:42.381 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-11 09:38:43.101 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-11 09:38:43.107 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-11 09:38:43.366 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-11 09:38:43.366 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-11 09:38:43.366 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-11 09:38:43.366 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-11 09:38:46.943 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1e085e71'
2025-07-11 09:38:47.390 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-11 09:38:47.538 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-11 09:38:47.553 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-11 09:38:47.567 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-11 09:38:47.580 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-11 09:38:47.597 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-11 09:38:47.609 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-11 09:38:47.642 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-11 09:38:47.651 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-11 09:38:47.685 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-11 09:38:47.696 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-11 09:38:47.705 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-11 09:38:47.718 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-11 09:38:47.746 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-11 09:38:47.759 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-11 09:38:47.771 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-11 09:38:47.780 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-11 09:38:47.786 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-11 09:38:47.807 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-11 09:38:47.827 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-11 09:38:47.850 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-11 09:38:47.869 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-11 09:38:47.891 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-11 09:38:47.908 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-11 09:38:47.928 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-11 09:38:47.946 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-11 09:38:47.966 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-11 09:38:47.986 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-11 09:38:48.002 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-11 09:38:48.032 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-11 09:38:48.062 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-11 09:38:48.095 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-11 09:38:48.120 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-11 09:38:48.158 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-11 09:38:48.184 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-11 09:38:48.218 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-11 09:38:48.247 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-11 09:38:48.287 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-11 09:38:48.314 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-11 09:38:48.339 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-11 09:38:48.363 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-11 09:38:48.394 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-11 09:38:48.404 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-11 09:38:48.415 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-11 09:38:48.424 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-11 09:38:48.431 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-11 09:38:48.444 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-11 09:38:48.471 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-11 09:38:48.498 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-11 09:38:48.521 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-11 09:38:48.542 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-11 09:38:48.570 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-11 09:38:48.607 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-11 09:38:48.636 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-11 09:38:48.665 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-11 09:38:48.692 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-11 09:38:48.726 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-11 09:38:48.767 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-11 09:38:48.799 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-11 09:38:48.835 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-11 09:38:48.872 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-11 09:38:48.896 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-11 09:38:48.925 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-11 09:38:49.001 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-11 09:38:49.036 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-11 09:38:49.057 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-11 09:38:49.074 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-11 09:38:49.078 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-11 09:38:49.095 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-11 09:38:49.989 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-11 09:38:54.934 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-11 09:39:03.220 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 09:39:04.733 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-11 09:39:05.966 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-11 09:39:06.236 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:39:08.358 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:39:15.447 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:39:15.447 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 09:39:16.772 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 09:39:18.378 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-11 09:39:19.412 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-07-11 09:39:24.897 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 09:39:25.351 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:39:25.740 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:39:25.822 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:39:27.575 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:39:33.372 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:39:33.372 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 09:39:33.381 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:39:35.459 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:39:42.886 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:39:42.886 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 09:39:42.897 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:39:45.367 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:39:52.807 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:39:52.807 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-11 09:39:52.815 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:39:55.408 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:39:55.446 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 09:39:55.447 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 09:39:55.447 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 09:39:55.447 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 09:40:03.829 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:40:03.829 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-11 09:40:04.902 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:40:07.444 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:47:02.234 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 09:47:02.399 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 29264 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 09:47:02.399 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 09:47:02.399 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-11 09:47:02.862 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-11 09:47:02.862 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 09:47:07.171 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-11 09:47:07.171 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-11 09:47:07.270 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-11 09:47:10.957 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 09:47:11.017 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 09:47:11.392 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-11 09:47:13.377 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-11 09:47:13.382 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-11 09:47:15.133 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-11 09:47:15.133 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-11 09:47:15.134 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-11 09:47:15.134 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-11 09:47:17.117 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@471b7c9c'
2025-07-11 09:47:17.426 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-11 09:47:17.532 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-11 09:47:17.541 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-11 09:47:17.552 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-11 09:47:17.563 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-11 09:47:17.574 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-11 09:47:17.582 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-11 09:47:17.603 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-11 09:47:17.609 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-11 09:47:17.625 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-11 09:47:17.633 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-11 09:47:17.640 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-11 09:47:17.650 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-11 09:47:17.661 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-11 09:47:17.670 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-11 09:47:17.677 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-11 09:47:17.685 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-11 09:47:17.692 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-11 09:47:17.708 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-11 09:47:17.725 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-11 09:47:17.740 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-11 09:47:17.753 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-11 09:47:17.769 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-11 09:47:17.783 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-11 09:47:17.799 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-11 09:47:17.814 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-11 09:47:17.829 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-11 09:47:17.848 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-11 09:47:17.865 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-11 09:47:17.890 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-11 09:47:17.914 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-11 09:47:17.936 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-11 09:47:17.954 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-11 09:47:17.980 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-11 09:47:17.996 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-11 09:47:18.014 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-11 09:47:18.032 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-11 09:47:18.061 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-11 09:47:18.081 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-11 09:47:18.103 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-11 09:47:18.127 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-11 09:47:18.154 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-11 09:47:18.177 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-11 09:47:18.183 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-11 09:47:18.188 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-11 09:47:18.191 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-11 09:47:18.199 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-11 09:47:18.216 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-11 09:47:18.233 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-11 09:47:18.249 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-11 09:47:18.267 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-11 09:47:18.287 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-11 09:47:18.314 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-11 09:47:18.333 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-11 09:47:18.351 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-11 09:47:18.369 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-11 09:47:18.385 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-11 09:47:18.402 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-11 09:47:18.420 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-11 09:47:18.445 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-11 09:47:18.475 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-11 09:47:18.498 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-11 09:47:18.521 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-11 09:47:18.565 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-11 09:47:18.592 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-11 09:47:18.614 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-11 09:47:18.630 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-11 09:47:18.633 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-11 09:47:18.647 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-11 09:47:18.656 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [150] -| Use localhost address 
2025-07-11 09:47:18.657 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [155] -| Get DESKTOP-GBHOUCA/******* network interface 
2025-07-11 09:47:19.378 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [159] -| Get network interface info: name:net0 (Sangfor aTrust VNIC)
2025-07-11 09:47:19.389 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [103] -| Initialization Sequence datacenterId:15 workerId:5
2025-07-11 09:47:19.533 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-11 09:47:23.647 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-11 09:47:29.884 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 09:47:31.085 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-11 09:47:32.315 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-11 09:47:32.585 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:47:34.282 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:47:39.554 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:47:39.555 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 09:47:40.760 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 09:47:42.479 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-11 09:47:42.479 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-11 09:47:43.101 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-11 09:47:43.299 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinMpAppId(String), weixinMpAppSecret(String)
2025-07-11 09:47:44.081 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-11 09:47:44.156 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-11 09:47:45.329 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseWeixinMsgTemplateMapper.selectList [135] -| ==>  Preparing: SELECT id,message_type,template_num,template_id,open_flag,user_in_wx_applet,create_time,update_time,title,description FROM base_weixin_msg_template
2025-07-11 09:47:45.329 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseWeixinMsgTemplateMapper.selectList [135] -| ==> Parameters: 
2025-07-11 09:47:45.515 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseWeixinMsgTemplateMapper.selectList [135] -| <==      Total: 2
2025-07-11 09:47:45.516 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-11 09:47:45.517 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinAppletId(String), weixinAppletSecret(String)
2025-07-11 09:47:45.690 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-11 09:47:45.694 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [48] -| init wxa config, wxAppKey:wx06ef85741e60fe3d,wxAppSecret:d373b5835d638c62bf8b4aab4fbf493e
2025-07-11 09:47:50.685 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 09:47:51.078 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:47:51.423 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 09:47:51.496 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:47:53.107 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:47:58.122 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:47:58.122 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 09:47:58.130 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:47:59.758 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:48:06.006 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:48:06.007 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 09:48:06.015 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:48:07.804 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:48:12.810 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:48:12.810 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-11 09:48:12.822 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:48:14.638 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:48:20.015 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:48:20.015 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-11 09:48:21.216 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:48:22.935 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:48:28.289 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:48:28.289 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-11 09:48:28.307 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:48:30.031 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:48:35.432 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:48:35.432 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-11 09:48:35.441 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:48:37.200 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:48:42.382 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:48:42.382 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-11 09:48:42.392 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:48:43.974 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:48:48.834 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:48:48.834 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-11 09:48:48.844 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:48:50.438 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:48:55.075 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:48:55.075 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-11 09:48:55.085 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:48:56.547 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:49:01.113 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:49:01.113 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-11 09:49:01.124 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:49:02.641 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:49:07.213 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:49:07.213 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-11 09:49:07.221 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:49:08.688 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:49:13.336 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:49:13.336 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-11 09:49:13.345 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:49:14.877 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:49:19.465 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:49:19.465 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-11 09:49:19.473 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 09:49:20.952 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 09:49:25.666 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:49:25.667 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-11 09:49:31.023 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-11 09:49:31.039 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-11 09:49:32.348 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:29264, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 09:49:33.209 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=335, lastTimeStamp=1752198572355}] - instanceId:[InstanceId{instanceId=*******:29264, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 09:49:33.365 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-11 09:49:33.368 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-11 09:49:33.368 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-11 09:49:33.369 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-11 09:49:36.034 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-11 09:49:39.309 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4204ad9d[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-11 09:49:39.310 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4daaad61[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-11 09:49:39.310 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@295c834b[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-11 09:49:39.310 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b9d2ed7[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-11 09:49:39.347 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5c92147c[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-11 09:49:39.347 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@44cb6b2a[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-11 09:49:39.348 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@940fe23[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-11 09:49:45.064 |-ERROR [Thread-341][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:49:45.236 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-11 09:49:45.328 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-11 09:49:45.378 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-11 09:49:45.582 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-11 09:49:45.786 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 169.212 seconds (JVM running for 172.54)
2025-07-11 09:49:46.171 |-INFO  [task-9][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-11 09:49:46.171 |-INFO  [task-9][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-11 09:49:46.493 |-INFO  [task-9][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-11 09:49:48.186 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-11 09:49:48.190 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-11 09:49:48.194 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-11 09:49:48.306 |-INFO  [task-12][9cea1820bee59dc1] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-11 09:49:48.308 |-INFO  [task-12][9cea1820bee59dc1] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-11 09:49:48.637 |-ERROR [task-13][96dc69b2db969869] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-11 09:49:48.667 |-INFO  [task-13][96dc69b2db969869] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 09:49:49.637 |-INFO  [RMI TCP Connection(9)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 09:49:52.197 |-WARN  [RMI TCP Connection(10)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-11 09:52:25.588 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 09:52:25.588 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 09:52:25.588 |-INFO  [Thread-313][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 09:52:25.588 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:52:25.588 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.588 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 09:52:25.588 |-INFO  [Thread-313][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:52:25.588 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.589 |-INFO  [Thread-314][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 09:52:25.591 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 09:52:25.594 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=335, lastTimeStamp=1752198745594}] instanceId:[InstanceId{instanceId=*******:29264, stable=false}] @ namespace:[himall-base].
2025-07-11 09:52:25.616 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:53:07.674 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-11 09:53:07.704 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11 09:53:07.713 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 09:53:07.718 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-11 09:53:07.718 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-11 09:53:07.718 |-INFO  [Thread-340][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-11 09:53:07.719 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-11 09:53:07.747 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-11 09:53:07.784 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.784 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.784 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.784 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.784 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.784 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.785 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.785 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.785 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.785 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.785 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.785 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.785 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.786 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 09:53:07.805 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:06:16.082 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 16:06:16.227 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 30772 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 16:06:16.227 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 16:06:16.228 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-11 16:06:16.884 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-11 16:06:16.884 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 16:06:21.371 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-11 16:06:21.372 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-11 16:06:21.476 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-11 16:06:26.768 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 16:06:26.833 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 16:06:27.254 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-11 16:06:27.969 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-11 16:06:27.973 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-11 16:06:28.171 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-11 16:06:28.171 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-11 16:06:28.171 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-11 16:06:28.171 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-11 16:06:30.144 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@63c6579'
2025-07-11 16:06:30.467 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-11 16:06:30.570 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-11 16:06:30.579 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-11 16:06:30.589 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-11 16:06:30.597 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-11 16:06:30.607 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-11 16:06:30.613 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-11 16:06:30.631 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-11 16:06:30.639 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-11 16:06:30.657 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-11 16:06:30.662 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-11 16:06:30.669 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-11 16:06:30.681 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-11 16:06:30.692 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-11 16:06:30.701 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-11 16:06:30.710 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-11 16:06:30.718 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-11 16:06:30.725 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-11 16:06:30.747 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-11 16:06:30.770 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-11 16:06:30.803 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-11 16:06:30.818 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-11 16:06:30.835 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-11 16:06:30.850 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-11 16:06:30.866 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-11 16:06:30.879 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-11 16:06:30.893 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-11 16:06:30.913 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-11 16:06:30.930 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-11 16:06:30.964 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-11 16:06:30.989 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-11 16:06:31.013 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-11 16:06:31.032 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-11 16:06:31.054 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-11 16:06:31.070 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-11 16:06:31.088 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-11 16:06:31.113 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-11 16:06:31.147 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-11 16:06:31.170 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-11 16:06:31.190 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-11 16:06:31.208 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-11 16:06:31.233 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-11 16:06:31.238 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-11 16:06:31.244 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-11 16:06:31.248 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-11 16:06:31.251 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-11 16:06:31.261 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-11 16:06:31.276 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-11 16:06:31.294 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-11 16:06:31.310 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-11 16:06:31.331 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-11 16:06:31.351 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-11 16:06:31.379 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-11 16:06:31.399 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-11 16:06:31.421 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-11 16:06:31.438 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-11 16:06:31.452 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-11 16:06:31.468 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-11 16:06:31.484 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-11 16:06:31.516 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-11 16:06:31.549 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-11 16:06:31.566 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-11 16:06:31.585 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-11 16:06:31.627 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-11 16:06:31.649 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-11 16:06:31.681 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-11 16:06:31.693 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-11 16:06:31.696 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-11 16:06:31.707 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-11 16:06:31.712 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [150] -| Use localhost address 
2025-07-11 16:06:31.713 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [155] -| Get DESKTOP-GBHOUCA/******* network interface 
2025-07-11 16:06:32.290 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [159] -| Get network interface info: name:net0 (Sangfor aTrust VNIC)
2025-07-11 16:06:32.300 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [103] -| Initialization Sequence datacenterId:15 workerId:17
2025-07-11 16:06:32.452 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-11 16:06:36.510 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-11 16:06:44.617 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 16:06:46.309 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-11 16:06:47.668 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-11 16:06:47.911 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:06:49.966 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:06:57.376 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:06:57.376 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 16:06:58.542 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 16:07:00.198 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-11 16:07:00.199 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-11 16:07:00.628 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-11 16:07:00.799 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinMpAppId(String), weixinMpAppSecret(String)
2025-07-11 16:07:00.957 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-11 16:07:01.189 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-11 16:07:01.821 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-11 16:07:01.821 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinAppletId(String), weixinAppletSecret(String)
2025-07-11 16:07:01.851 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-11 16:07:01.851 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wxb16c375a8b35e176,wxAppSecret:8984e92a1e5bfe49772ae3846f746e44
2025-07-11 16:07:06.657 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 16:07:07.052 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 16:07:07.380 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 16:07:07.453 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:07:09.005 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:07:13.839 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:07:13.839 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 16:07:13.847 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:07:15.377 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:07:20.197 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:07:20.198 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 16:07:20.207 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:07:22.597 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:07:27.295 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:07:27.296 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-11 16:07:27.304 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:07:28.830 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:07:33.512 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:07:33.513 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-11 16:07:34.489 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:07:35.983 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:07:40.935 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:07:40.935 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-11 16:07:40.951 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:07:42.576 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:07:47.487 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:07:47.487 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-11 16:07:47.497 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:07:49.109 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:07:54.049 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:07:54.050 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-11 16:07:54.060 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:07:55.652 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:08:00.615 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:08:00.615 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-11 16:08:00.626 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:08:02.250 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:08:07.126 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:08:07.126 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-11 16:08:07.135 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:08:08.728 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:08:13.732 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:08:13.732 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-11 16:08:13.742 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:08:15.260 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:08:19.915 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:08:19.915 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-11 16:08:19.923 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:08:21.432 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:08:26.074 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:08:26.075 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-11 16:08:26.083 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:08:27.623 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:08:32.757 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:08:32.758 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-11 16:08:32.767 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 16:08:34.395 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 16:08:39.324 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 16:08:39.324 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-11 16:08:45.093 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-11 16:08:45.108 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-11 16:08:46.471 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:30772, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 16:08:47.345 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=335, lastTimeStamp=1752221326478}] - instanceId:[InstanceId{instanceId=*******:30772, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 16:08:47.501 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-11 16:08:47.504 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-11 16:08:47.504 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-11 16:08:47.505 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-11 16:08:50.315 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-11 16:08:53.911 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@22e1da08[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-11 16:08:53.912 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@69b9e106[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-11 16:08:53.912 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42effd02[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-11 16:08:53.912 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5322d77f[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-11 16:08:53.957 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7f3f7689[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-11 16:08:53.957 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@59a819b9[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-11 16:08:53.957 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6492f863[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-11 16:08:59.994 |-ERROR [Thread-348][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 16:09:00.204 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-11 16:09:00.294 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-11 16:09:00.335 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-11 16:09:00.523 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-11 16:09:00.739 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 173.266 seconds (JVM running for 185.25)
2025-07-11 16:09:01.156 |-INFO  [task-9][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-11 16:09:01.156 |-INFO  [task-9][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-11 16:09:01.520 |-INFO  [task-9][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-11 16:09:03.733 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-11 16:09:03.738 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-11 16:09:03.741 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-11 16:09:03.845 |-INFO  [task-13][9d44cb31750ad75b] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-11 16:09:03.847 |-INFO  [task-13][9d44cb31750ad75b] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-11 16:09:04.169 |-ERROR [task-15][d8c4fdaf541e9d18] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-11 16:09:04.197 |-INFO  [task-15][d8c4fdaf541e9d18] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 16:09:05.306 |-INFO  [RMI TCP Connection(7)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 16:09:08.204 |-WARN  [RMI TCP Connection(6)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-11 16:10:11.672 |-INFO  [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"operationUserId":null,"operationShopId":0}
2025-07-11 16:10:30.118 |-ERROR [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [68] -| thrift call failed
feign.RetryableException: Failed to connect to /127.0.0.1:8084 executing POST http://127.0.0.1:8084/himall-trade/category/queryCategoryTree
	at feign.FeignException.errorExecuting(FeignException.java:278) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:110) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
	at com.sun.proxy.$Proxy285.queryCategoryTree(Unknown Source) ~[?:?]
	at com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService.lambda$queryCategoryTree$9(RemoteCategoryService.java:205) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService.queryCategoryTree(RemoteCategoryService.java:205) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl.queryBusinessCategoryTree(BusinessCategoryServiceImpl.java:361) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl$$FastClassBySpringCGLIB$$cb3f868c.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl$$EnhancerBySpringCGLIB$$fc988cec.queryBusinessCategoryTree(<generated>) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.thrift.impl.BusinessCategoryQueryController.lambda$queryBusinessCategoryTree$2(BusinessCategoryQueryController.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.user.shop.thrift.impl.BusinessCategoryQueryController.queryBusinessCategoryTree(BusinessCategoryQueryController.java:57) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.net.ConnectException: Failed to connect to /127.0.0.1:8084
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 98 more
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method) ~[?:1.8.0_121]
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188) ~[?:1.8.0_121]
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172) ~[?:1.8.0_121]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_121]
	at java.net.Socket.connect(Socket.java:589) ~[?:1.8.0_121]
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 98 more
2025-07-11 16:10:31.187 |-WARN  [XNIO-1 task-1][6c869de5495b8c17] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| queryBusinessCategoryTree business error. request=QueryBusinessCategoryTreeReq(shopId=162)
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:69) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService.queryCategoryTree(RemoteCategoryService.java:205) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl.queryBusinessCategoryTree(BusinessCategoryServiceImpl.java:361) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl$$FastClassBySpringCGLIB$$cb3f868c.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl$$EnhancerBySpringCGLIB$$fc988cec.queryBusinessCategoryTree(<generated>) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.thrift.impl.BusinessCategoryQueryController.lambda$queryBusinessCategoryTree$2(BusinessCategoryQueryController.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.user.shop.thrift.impl.BusinessCategoryQueryController.queryBusinessCategoryTree(BusinessCategoryQueryController.java:57) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 16:15:25.553 |-INFO  [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"operationUserId":null,"operationShopId":0}
2025-07-11 16:15:36.818 |-INFO  [XNIO-1 task-2][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,51,57],"operationUserId":null,"operationShopId":0}
2025-07-11 16:15:36.908 |-DEBUG [XNIO-1 task-2][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-07-11 16:15:36.909 |-DEBUG [XNIO-1 task-2][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 51(Long), 57(Long)
2025-07-11 16:15:36.938 |-DEBUG [XNIO-1 task-2][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-07-11 16:15:36.946 |-INFO  [XNIO-1 task-2][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-07-11 16:16:30.122 |-WARN  [slave housekeeper][] -  com.zaxxer.hikari.pool.HikariPool [788] -| slave - Thread starvation or clock leap detected (housekeeper delta=58s843ms678µs100ns).
2025-07-11 16:16:30.122 |-WARN  [master housekeeper][] -  com.zaxxer.hikari.pool.HikariPool [788] -| master - Thread starvation or clock leap detected (housekeeper delta=58s843ms678µs600ns).
2025-07-11 16:16:30.254 |-INFO  [XNIO-1 task-1][63403b525d1e95c2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"}]"},"code":0,"message":null}
2025-07-11 16:16:50.278 |-INFO  [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"operationUserId":null,"operationShopId":0}
2025-07-11 16:17:52.848 |-ERROR [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [68] -| thrift call failed
feign.RetryableException: Read timed out executing POST http://127.0.0.1:8084/himall-trade/category/queryCategoryTree
	at feign.FeignException.errorExecuting(FeignException.java:278) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:110) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
	at com.sun.proxy.$Proxy285.queryCategoryTree(Unknown Source) ~[?:?]
	at com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService.lambda$queryCategoryTree$9(RemoteCategoryService.java:205) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService.queryCategoryTree(RemoteCategoryService.java:205) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl.queryBusinessCategoryTree(BusinessCategoryServiceImpl.java:361) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl$$FastClassBySpringCGLIB$$cb3f868c.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl$$EnhancerBySpringCGLIB$$fc988cec.queryBusinessCategoryTree(<generated>) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.thrift.impl.BusinessCategoryQueryController.lambda$queryBusinessCategoryTree$2(BusinessCategoryQueryController.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.user.shop.thrift.impl.BusinessCategoryQueryController.queryBusinessCategoryTree(BusinessCategoryQueryController.java:57) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_121]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_121]
	at okio.InputStreamSource.read(JvmOkio.kt:93) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 98 more
2025-07-11 16:17:52.850 |-WARN  [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| queryBusinessCategoryTree business error. request=QueryBusinessCategoryTreeReq(shopId=162)
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:69) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService.queryCategoryTree(RemoteCategoryService.java:205) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl.queryBusinessCategoryTree(BusinessCategoryServiceImpl.java:361) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl$$FastClassBySpringCGLIB$$cb3f868c.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.user.shop.service.impl.BusinessCategoryServiceImpl$$EnhancerBySpringCGLIB$$fc988cec.queryBusinessCategoryTree(<generated>) ~[classes/:?]
	at com.sankuai.shangou.seashop.user.shop.thrift.impl.BusinessCategoryQueryController.lambda$queryBusinessCategoryTree$2(BusinessCategoryQueryController.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.user.shop.thrift.impl.BusinessCategoryQueryController.queryBusinessCategoryTree(BusinessCategoryQueryController.java:57) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 16:19:04.112 |-INFO  [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,51,57],"operationUserId":null,"operationShopId":0}
2025-07-11 16:19:04.142 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-07-11 16:19:04.143 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 51(Long), 57(Long)
2025-07-11 16:19:04.173 |-DEBUG [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-07-11 16:19:04.174 |-INFO  [XNIO-1 task-1][8498dc6c8d0cc00d] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-07-11 16:20:58.206 |-INFO  [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"operationUserId":null,"operationShopId":0}
2025-07-11 16:21:39.837 |-INFO  [XNIO-1 task-2][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,51,57],"operationUserId":null,"operationShopId":0}
2025-07-11 16:21:39.871 |-DEBUG [XNIO-1 task-2][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-07-11 16:21:39.872 |-DEBUG [XNIO-1 task-2][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 51(Long), 57(Long)
2025-07-11 16:21:39.903 |-DEBUG [XNIO-1 task-2][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-07-11 16:21:39.905 |-INFO  [XNIO-1 task-2][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-07-11 16:21:39.977 |-INFO  [XNIO-1 task-1][9fa27d2b54aaf342] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"}]"},"code":0,"message":null}
2025-07-11 16:21:46.803 |-INFO  [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"operationUserId":null,"operationShopId":0}
2025-07-11 16:22:32.384 |-INFO  [XNIO-1 task-2][2f088654423a4083] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,51,57],"operationUserId":null,"operationShopId":0}
2025-07-11 16:22:32.413 |-DEBUG [XNIO-1 task-2][2f088654423a4083] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-07-11 16:22:32.413 |-DEBUG [XNIO-1 task-2][2f088654423a4083] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 51(Long), 57(Long)
2025-07-11 16:22:32.447 |-DEBUG [XNIO-1 task-2][2f088654423a4083] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-07-11 16:22:32.448 |-INFO  [XNIO-1 task-2][2f088654423a4083] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-07-11 16:22:32.517 |-INFO  [XNIO-1 task-1][2f088654423a4083] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"}]"},"code":0,"message":null}
2025-07-11 16:23:36.928 |-INFO  [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"operationUserId":null,"operationShopId":0}
2025-07-11 16:24:35.019 |-INFO  [XNIO-1 task-2][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,51,57],"operationUserId":null,"operationShopId":0}
2025-07-11 16:24:35.049 |-DEBUG [XNIO-1 task-2][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-07-11 16:24:35.050 |-DEBUG [XNIO-1 task-2][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 51(Long), 57(Long)
2025-07-11 16:24:35.081 |-DEBUG [XNIO-1 task-2][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-07-11 16:24:35.083 |-INFO  [XNIO-1 task-2][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-07-11 16:24:45.524 |-INFO  [XNIO-1 task-1][2a1785ae22e76d5e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"},{\"id\":2629,\"parentCategoryId\":0,\"name\":\"测试类目A\",\"fullIds\":[2629],\"children\":[{\"id\":2630,\"parentCategoryId\":2629,\"name\":\"测试类目A-1\",\"fullIds\":[2629,2630],\"children\":[{\"id\":2631,\"parentCategoryId\":2630,\"name\":\"测试类目A-1-1\",\"fullIds\":[2629,2630,2631],\"depth\":3,\"fullCategoryName\":\"测试类目A,测试类目A-1,测试类目A-1-1\"}],\"depth\":2,\"fullCategoryName\":\"测试类目A,测试类目A-1\"}],\"depth\":1,\"fullCategoryName\":\"测试类目A\"}]"},"code":0,"message":null}
2025-07-11 16:25:18.138 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 16:25:18.138 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 16:25:18.138 |-INFO  [Thread-318][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 16:25:18.138 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 16:25:18.138 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 16:25:18.138 |-INFO  [Thread-318][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 16:25:18.138 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.138 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.138 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.138 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.138 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.139 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.139 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 16:25:18.139 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.139 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.139 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.139 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.139 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.140 |-INFO  [Thread-319][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 16:25:18.141 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 16:25:18.144 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=335, lastTimeStamp=1752222318144}] instanceId:[InstanceId{instanceId=*******:30772, stable=false}] @ namespace:[himall-base].
2025-07-11 16:25:18.178 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 17:25:36.966 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-11 17:25:37.117 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 31584 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-11 17:25:37.117 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-11 17:25:37.118 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-11 17:25:37.572 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-11 17:25:37.573 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-11 17:25:42.934 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-11 17:25:42.934 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-11 17:25:43.045 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-11 17:25:48.306 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-11 17:25:48.387 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-11 17:25:48.852 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-11 17:25:49.522 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-11 17:25:49.528 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-11 17:25:49.851 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-11 17:25:49.851 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-11 17:25:49.851 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-11 17:25:49.851 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-11 17:25:52.187 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@19b6b7e7'
2025-07-11 17:25:52.596 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-11 17:25:52.700 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-11 17:25:52.714 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-11 17:25:52.724 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-11 17:25:52.734 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-11 17:25:52.746 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-11 17:25:52.754 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-11 17:25:52.772 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-11 17:25:52.780 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-11 17:25:52.799 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-11 17:25:52.807 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-11 17:25:52.817 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-11 17:25:52.829 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-11 17:25:52.839 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-11 17:25:52.847 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-11 17:25:52.855 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-11 17:25:52.862 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-11 17:25:52.868 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-11 17:25:52.884 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-11 17:25:52.902 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-11 17:25:52.919 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-11 17:25:52.933 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-11 17:25:52.952 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-11 17:25:52.969 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-11 17:25:52.986 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-11 17:25:53.005 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-11 17:25:53.022 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-11 17:25:53.038 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-11 17:25:53.057 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-11 17:25:53.076 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-11 17:25:53.093 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-11 17:25:53.117 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-11 17:25:53.140 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-11 17:25:53.165 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-11 17:25:53.184 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-11 17:25:53.203 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-11 17:25:53.227 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-11 17:25:53.256 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-11 17:25:53.278 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-11 17:25:53.299 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-11 17:25:53.318 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-11 17:25:53.346 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-11 17:25:53.353 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-11 17:25:53.361 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-11 17:25:53.367 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-11 17:25:53.374 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-11 17:25:53.388 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-11 17:25:53.418 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-11 17:25:53.449 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-11 17:25:53.479 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-11 17:25:53.503 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-11 17:25:53.530 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-11 17:25:53.573 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-11 17:25:53.600 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-11 17:25:53.620 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-11 17:25:53.638 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-11 17:25:53.659 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-11 17:25:53.676 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-11 17:25:53.697 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-11 17:25:53.724 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-11 17:25:53.748 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-11 17:25:53.763 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-11 17:25:53.782 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-11 17:25:53.821 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-11 17:25:53.841 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-11 17:25:53.859 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-11 17:25:53.879 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-11 17:25:53.883 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-11 17:25:53.895 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-11 17:25:53.901 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [150] -| Use localhost address 
2025-07-11 17:25:53.902 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [155] -| Get DESKTOP-GBHOUCA/******* network interface 
2025-07-11 17:25:54.824 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [159] -| Get network interface info: name:net0 (Sangfor aTrust VNIC)
2025-07-11 17:25:54.839 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [103] -| Initialization Sequence datacenterId:15 workerId:15
2025-07-11 17:25:54.997 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-11 17:26:01.590 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-11 17:26:14.224 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-11 17:26:16.094 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-11 17:26:17.765 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-11 17:26:18.030 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:26:21.444 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:26:31.409 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:26:31.409 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-11 17:26:32.643 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-11 17:26:34.388 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-11 17:26:34.388 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-11 17:26:34.816 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-11 17:26:34.996 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinMpAppId(String), weixinMpAppSecret(String)
2025-07-11 17:26:35.154 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-11 17:26:35.430 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-11 17:26:36.147 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-11 17:26:36.148 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinAppletId(String), weixinAppletSecret(String)
2025-07-11 17:26:36.174 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-11 17:26:36.174 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wxb16c375a8b35e176,wxAppSecret:8984e92a1e5bfe49772ae3846f746e44
2025-07-11 17:26:41.744 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-11 17:26:42.214 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 17:26:42.597 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-11 17:26:42.681 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:26:45.171 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:26:54.844 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:26:54.844 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-11 17:26:54.853 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:26:58.169 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:27:06.739 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:27:06.740 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-11 17:27:06.748 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:27:09.897 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:27:18.187 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:27:18.187 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-11 17:27:18.196 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:27:21.509 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:27:33.140 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:27:33.140 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-11 17:27:34.188 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:27:36.685 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:27:44.516 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:27:44.516 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-11 17:27:44.538 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:27:47.027 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:27:57.044 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:27:57.045 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-11 17:27:57.057 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:27:59.584 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:28:06.878 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:28:06.878 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-11 17:28:06.888 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:28:08.729 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:28:16.148 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:28:16.149 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-11 17:28:16.157 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:28:18.580 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:28:25.770 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:28:25.770 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-11 17:28:25.785 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:28:28.252 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:28:34.628 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:28:34.628 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-11 17:28:34.636 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:28:36.865 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:28:45.014 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:28:45.014 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-11 17:28:45.023 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:28:47.133 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:28:52.514 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:28:52.514 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-11 17:28:52.522 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:28:54.196 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:29:03.576 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:29:03.577 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-11 17:29:03.593 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-11 17:29:06.608 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-11 17:29:11.241 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-11 17:29:11.242 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-11 17:29:16.753 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-11 17:29:16.768 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-11 17:29:18.016 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:31584, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 17:29:18.886 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=335, lastTimeStamp=1752226158024}] - instanceId:[InstanceId{instanceId=*******:31584, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-11 17:29:19.114 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-11 17:29:19.117 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-11 17:29:19.117 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-11 17:29:19.117 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-11 17:29:21.746 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-11 17:29:24.985 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@674c1672[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-11 17:29:24.985 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4190f278[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-11 17:29:24.985 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@caaf5b[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-11 17:29:24.985 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@56e71576[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-11 17:29:25.022 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@519f665e[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-11 17:29:25.022 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@17da4d27[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-11 17:29:25.022 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42bf98f0[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-11 17:29:30.452 |-ERROR [Thread-437][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 17:29:30.622 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-11 17:29:30.704 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-11 17:29:30.740 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-11 17:29:30.924 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-11 17:29:31.146 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 241.981 seconds (JVM running for 246.637)
2025-07-11 17:29:31.538 |-INFO  [task-12][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-11 17:29:31.538 |-INFO  [task-12][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-11 17:29:32.347 |-INFO  [task-12][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-11 17:29:33.922 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-11 17:29:33.926 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-11 17:29:33.929 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-11 17:29:34.028 |-INFO  [task-12][42713f438b4905f8] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-11 17:29:34.030 |-INFO  [task-12][42713f438b4905f8] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-11 17:29:34.377 |-ERROR [task-9][d9ffbd7abe0f6f29] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-11 17:29:34.409 |-INFO  [task-9][d9ffbd7abe0f6f29] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-11 17:29:35.768 |-INFO  [RMI TCP Connection(18)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 17:29:38.490 |-WARN  [RMI TCP Connection(17)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 17:31:03.164 |-WARN  [Thread-11][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-11 17:31:03.164 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-410][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-WARN  [Thread-11][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-11 17:31:03.164 |-INFO  [Thread-410][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.164 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.166 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.166 |-INFO  [Thread-411][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-11 17:31:03.167 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-11 17:31:03.170 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=335, lastTimeStamp=1752226263169}] instanceId:[InstanceId{instanceId=*******:31584, stable=false}] @ namespace:[himall-base].
2025-07-11 17:31:03.191 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
