2025-08-01 17:27:39.669 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-08-01 17:27:39.931 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [55] -| Starting BaseApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 45272 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-08-01 17:27:39.931 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-08-01 17:27:39.932 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-08-01 17:27:40.701 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-08-01 17:27:40.702 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-08-01 17:27:46.973 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-08-01 17:27:46.977 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-08-01 17:27:47.178 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-08-01 17:27:55.356 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 17:27:55.493 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-08-01 17:27:56.166 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-08-01 17:27:57.882 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-08-01 17:27:57.892 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-08-01 17:27:58.211 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-08-01 17:27:58.211 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-08-01 17:27:58.211 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-08-01 17:27:58.212 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 17:28:01.259 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@5a75bad4'
2025-08-01 17:28:01.748 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-08-01 17:28:01.952 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-08-01 17:28:01.979 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-08-01 17:28:01.993 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-08-01 17:28:02.004 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-08-01 17:28:02.026 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-08-01 17:28:02.035 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-08-01 17:28:02.062 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-08-01 17:28:02.070 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-08-01 17:28:02.093 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-08-01 17:28:02.100 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-08-01 17:28:02.109 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-08-01 17:28:02.128 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.Insert [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-01 17:28:02.142 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.SelectById [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-01 17:28:02.144 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-08-01 17:28:02.153 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-08-01 17:28:02.163 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-08-01 17:28:02.173 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-08-01 17:28:02.183 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-08-01 17:28:02.191 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-08-01 17:28:02.216 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-08-01 17:28:02.243 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-08-01 17:28:02.279 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-08-01 17:28:02.307 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-08-01 17:28:02.337 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-08-01 17:28:02.363 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-08-01 17:28:02.394 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-08-01 17:28:02.423 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-08-01 17:28:02.449 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-08-01 17:28:02.481 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-08-01 17:28:02.513 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-08-01 17:28:02.549 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-08-01 17:28:02.577 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-08-01 17:28:02.612 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-08-01 17:28:02.642 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-08-01 17:28:02.680 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-08-01 17:28:02.709 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-08-01 17:28:02.748 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-08-01 17:28:02.788 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-08-01 17:28:02.842 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-08-01 17:28:02.882 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-08-01 17:28:02.926 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-08-01 17:28:02.968 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-08-01 17:28:03.005 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-08-01 17:28:03.014 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-08-01 17:28:03.023 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-08-01 17:28:03.032 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-08-01 17:28:03.038 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-08-01 17:28:03.054 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-08-01 17:28:03.087 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-08-01 17:28:03.116 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-08-01 17:28:03.144 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-08-01 17:28:03.169 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-08-01 17:28:03.194 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-08-01 17:28:03.223 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-08-01 17:28:03.244 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-08-01 17:28:03.270 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-08-01 17:28:03.293 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-08-01 17:28:03.310 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-08-01 17:28:03.331 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-08-01 17:28:03.356 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-08-01 17:28:03.389 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-08-01 17:28:03.420 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-08-01 17:28:03.452 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-08-01 17:28:03.490 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-08-01 17:28:03.548 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-08-01 17:28:03.576 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-08-01 17:28:03.602 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-08-01 17:28:03.624 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-08-01 17:28:03.628 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-08-01 17:28:03.647 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-08-01 17:28:03.657 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [150] -| Use localhost address 
2025-08-01 17:28:03.659 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [155] -| Get DESKTOP-GBHOUCA/******* network interface 
2025-08-01 17:28:04.669 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [159] -| Get network interface info: name:net0 (Sangfor aTrust VNIC)
2025-08-01 17:28:04.690 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [103] -| Initialization Sequence datacenterId:15 workerId:8
2025-08-01 17:28:04.992 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-08-01 17:28:12.101 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-08-01 17:28:24.604 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-08-01 17:28:26.380 |-INFO  [redisson-netty-2-8][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-08-01 17:28:30.231 |-INFO  [redisson-netty-2-5][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-08-01 17:28:32.335 |-INFO  [redisson-netty-2-18][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-08-01 17:28:34.652 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.mq.listener.LogMafkaConsumer [31] -| seashop_operation_log_topic_prod ： 消费者启动
2025-08-01 17:28:34.730 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:28:37.621 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:34:13.681 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-08-01 17:34:13.833 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [55] -| Starting BaseApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 17568 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-08-01 17:34:13.833 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-08-01 17:34:13.834 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-08-01 17:34:14.275 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-08-01 17:34:14.275 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-08-01 17:34:19.705 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-08-01 17:34:19.705 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-08-01 17:34:19.794 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-08-01 17:34:23.064 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 17:34:23.136 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-08-01 17:34:23.472 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-08-01 17:34:24.203 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-08-01 17:34:24.208 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-08-01 17:34:24.474 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-08-01 17:34:24.475 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-08-01 17:34:24.475 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-08-01 17:34:24.476 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 17:34:26.155 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@35e0d91e'
2025-08-01 17:34:26.515 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-08-01 17:34:26.614 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-08-01 17:34:26.623 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-08-01 17:34:26.632 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-08-01 17:34:26.642 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-08-01 17:34:26.651 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-08-01 17:34:26.657 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-08-01 17:34:26.674 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-08-01 17:34:26.681 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-08-01 17:34:26.697 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-08-01 17:34:26.703 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-08-01 17:34:26.709 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-08-01 17:34:26.728 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.Insert [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-01 17:34:26.743 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.SelectById [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-01 17:34:26.745 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-08-01 17:34:26.756 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-08-01 17:34:26.762 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-08-01 17:34:26.771 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-08-01 17:34:26.779 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-08-01 17:34:26.785 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-08-01 17:34:26.803 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-08-01 17:34:26.821 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-08-01 17:34:26.836 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-08-01 17:34:26.853 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-08-01 17:34:26.869 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-08-01 17:34:26.883 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-08-01 17:34:26.897 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-08-01 17:34:26.910 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-08-01 17:34:26.924 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-08-01 17:34:26.941 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-08-01 17:34:26.961 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-08-01 17:34:26.981 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-08-01 17:34:27.002 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-08-01 17:34:27.022 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-08-01 17:34:27.040 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-08-01 17:34:27.063 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-08-01 17:34:27.078 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-08-01 17:34:27.095 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-08-01 17:34:27.110 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-08-01 17:34:27.133 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-08-01 17:34:27.152 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-08-01 17:34:27.171 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-08-01 17:34:27.187 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-08-01 17:34:27.208 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-08-01 17:34:27.213 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-08-01 17:34:27.220 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-08-01 17:34:27.224 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-08-01 17:34:27.229 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-08-01 17:34:27.240 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-08-01 17:34:27.257 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-08-01 17:34:27.274 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-08-01 17:34:27.291 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-08-01 17:34:27.307 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-08-01 17:34:27.324 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-08-01 17:34:27.347 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-08-01 17:34:27.373 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-08-01 17:34:27.395 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-08-01 17:34:27.417 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-08-01 17:34:27.451 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-08-01 17:34:27.467 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-08-01 17:34:27.483 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-08-01 17:34:27.503 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-08-01 17:34:27.525 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-08-01 17:34:27.540 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-08-01 17:34:27.557 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-08-01 17:34:27.592 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-08-01 17:34:27.609 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-08-01 17:34:27.630 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-08-01 17:34:27.642 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-08-01 17:34:27.645 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-08-01 17:34:27.655 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-08-01 17:34:27.662 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [150] -| Use localhost address 
2025-08-01 17:34:27.662 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [155] -| Get DESKTOP-GBHOUCA/******* network interface 
2025-08-01 17:34:28.111 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [159] -| Get network interface info: name:net0 (Sangfor aTrust VNIC)
2025-08-01 17:34:28.120 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [103] -| Initialization Sequence datacenterId:15 workerId:13
2025-08-01 17:34:28.247 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-08-01 17:34:31.353 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-08-01 17:34:36.503 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-08-01 17:34:37.588 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-08-01 17:34:39.543 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-08-01 17:34:39.723 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.mq.listener.LogMafkaConsumer [31] -| seashop_operation_log_topic_prod ： 消费者启动
2025-08-01 17:34:39.800 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:34:41.092 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:34:45.392 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:34:45.392 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-01 17:34:46.431 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-08-01 17:34:48.102 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-08-01 17:34:48.102 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-08-01 17:34:48.583 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-08-01 17:34:48.765 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinMpAppId(String), weixinMpAppSecret(String)
2025-08-01 17:34:49.109 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-08-01 17:34:49.167 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-08-01 17:34:49.809 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-08-01 17:34:49.810 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinAppletId(String), weixinAppletSecret(String)
2025-08-01 17:34:49.842 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-08-01 17:34:49.843 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-08-01 17:34:54.497 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-08-01 17:34:54.868 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-08-01 17:34:55.200 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-08-01 17:34:55.268 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:34:56.509 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:00.341 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:00.342 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-01 17:35:00.350 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:01.616 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:05.754 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:05.755 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-01 17:35:05.763 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:07.007 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:11.103 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:11.104 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-01 17:35:11.113 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:12.381 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:16.247 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:16.247 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-01 17:35:17.246 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:18.508 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:22.570 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:22.570 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-08-01 17:35:22.588 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:23.839 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:27.655 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:27.655 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-08-01 17:35:27.669 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:28.963 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:32.888 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:32.889 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-08-01 17:35:32.898 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:34.133 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:37.964 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:37.964 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-08-01 17:35:37.971 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:39.212 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:43.347 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:43.347 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-08-01 17:35:43.358 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:44.792 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:50.357 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:50.357 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-08-01 17:35:50.366 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:35:51.947 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:35:58.535 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:35:58.535 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-08-01 17:35:58.547 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:36:01.080 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:36:07.231 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:36:07.231 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-08-01 17:36:07.240 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:36:08.880 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:36:13.737 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:36:13.737 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-08-01 17:36:13.745 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:36:15.320 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:36:20.487 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:36:20.487 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-08-01 17:36:27.480 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-08-01 17:36:27.501 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-08-01 17:36:30.358 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:17568, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-08-01 17:36:31.229 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=441, lastTimeStamp=1754040990365}] - instanceId:[InstanceId{instanceId=*******:17568, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-08-01 17:36:31.391 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-08-01 17:36:31.400 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-08-01 17:36:31.400 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-08-01 17:36:31.401 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-08-01 17:36:35.335 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-08-01 17:36:40.707 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:syncRegionData, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d01c709[class com.sankuai.shangou.seashop.base.core.task.RegionTask#syncRegionData]
2025-08-01 17:36:40.708 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2bfaa2d0[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-08-01 17:36:40.708 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@422790a1[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-08-01 17:36:40.708 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4d8ee285[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-08-01 17:36:40.708 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6e945db2[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-08-01 17:36:40.743 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@10869e92[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-08-01 17:36:40.743 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6edab6bb[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-08-01 17:36:40.743 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@547aeb2f[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-08-01 17:36:47.931 |-ERROR [Thread-308][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-08-01 17:36:48.158 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-08-01 17:36:48.251 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-08-01 17:36:48.294 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-08-01 17:36:48.499 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-08-01 17:36:48.718 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [61] -| Started BaseApplication in 159.943 seconds (JVM running for 162.755)
2025-08-01 17:36:49.112 |-INFO  [task-16][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 17:36:49.113 |-INFO  [task-16][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 17:36:49.450 |-INFO  [task-16][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-08-01 17:36:52.513 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-08-01 17:36:52.518 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-08-01 17:36:52.522 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [32] -| 服务启动成功！
2025-08-01 17:36:52.652 |-INFO  [task-11][658c3c1eaf9bcaf5] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-08-01 17:36:52.653 |-INFO  [task-11][658c3c1eaf9bcaf5] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-08-01 17:36:52.954 |-ERROR [task-12][e626306274e5429b] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-08-01 17:36:52.979 |-INFO  [task-12][e626306274e5429b] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-08-01 17:36:56.402 |-WARN  [RMI TCP Connection(15)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-08-01 17:36:57.205 |-INFO  [RMI TCP Connection(18)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 17:47:51.548 |-INFO  [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"categoryName":"A工厂","operationUserId":null,"operationShopId":0}
2025-08-01 17:47:54.858 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:47:54.956 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? ) )
2025-08-01 17:47:54.957 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long)
2025-08-01 17:47:54.990 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:47:55.001 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:47:55.703 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:47:55.737 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? ) )
2025-08-01 17:47:55.738 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long)
2025-08-01 17:47:55.771 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:47:55.772 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:47:55.959 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:47:55.960 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? ) )
2025-08-01 17:47:55.961 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long)
2025-08-01 17:47:55.994 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:47:55.994 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:47:56.186 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,57,51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:47:56.187 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-08-01 17:47:56.188 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 57(Long), 51(Long)
2025-08-01 17:47:56.221 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:47:56.222 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:47:56.441 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51,39],"operationUserId":null,"operationShopId":0}
2025-08-01 17:47:56.442 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? ) )
2025-08-01 17:47:56.442 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long), 39(Long)
2025-08-01 17:47:56.476 |-DEBUG [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:47:56.477 |-INFO  [XNIO-1 task-2][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:47:56.549 |-INFO  [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null}
2025-08-01 17:48:10.997 |-INFO  [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"categoryName":"","operationUserId":null,"operationShopId":0}
2025-08-01 17:48:17.193 |-INFO  [XNIO-1 task-2][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,51,57],"operationUserId":null,"operationShopId":0}
2025-08-01 17:48:17.226 |-DEBUG [XNIO-1 task-2][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-08-01 17:48:17.226 |-DEBUG [XNIO-1 task-2][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 51(Long), 57(Long)
2025-08-01 17:48:17.260 |-DEBUG [XNIO-1 task-2][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:48:17.261 |-INFO  [XNIO-1 task-2][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:48:17.316 |-INFO  [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"},{\"id\":2629,\"parentCategoryId\":0,\"name\":\"测试类目A\",\"fullIds\":[2629],\"children\":[{\"id\":2630,\"parentCategoryId\":2629,\"name\":\"测试类目A-1\",\"fullIds\":[2629,2630],\"children\":[{\"id\":2631,\"parentCategoryId\":2630,\"name\":\"测试类目A-1-1\",\"fullIds\":[2629,2630,2631],\"depth\":3,\"fullCategoryName\":\"测试类目A,测试类目A-1,测试类目A-1-1\"}],\"depth\":2,\"fullCategoryName\":\"测试类目A,测试类目A-1\"}],\"depth\":1,\"fullCategoryName\":\"测试类目A\"}]"},"code":0,"message":null}
2025-08-01 17:49:09.444 |-INFO  [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"categoryName":"A工厂","operationUserId":null,"operationShopId":0}
2025-08-01 17:49:09.584 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:49:09.585 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? ) )
2025-08-01 17:49:09.585 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long)
2025-08-01 17:49:09.618 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:49:09.618 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:49:09.702 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:49:09.703 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? ) )
2025-08-01 17:49:09.704 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long)
2025-08-01 17:49:09.736 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:49:09.737 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:49:09.817 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:49:09.817 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? ) )
2025-08-01 17:49:09.818 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long)
2025-08-01 17:49:09.850 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:49:09.851 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:49:10.000 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,57,51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:49:10.001 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-08-01 17:49:10.001 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 57(Long), 51(Long)
2025-08-01 17:49:10.035 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:49:10.035 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:49:10.179 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51,39],"operationUserId":null,"operationShopId":0}
2025-08-01 17:49:10.181 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? ) )
2025-08-01 17:49:10.181 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long), 39(Long)
2025-08-01 17:49:10.214 |-DEBUG [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:49:10.215 |-INFO  [XNIO-1 task-2][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:49:10.252 |-INFO  [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null}
2025-08-01 17:49:31.863 |-INFO  [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request={"shopId":162,"categoryName":"X1部门","operationUserId":null,"operationShopId":0}
2025-08-01 17:49:32.363 |-INFO  [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[23,42,54,47,52,55,19,38,48,39,56,57,51],"operationUserId":null,"operationShopId":0}
2025-08-01 17:49:32.365 |-DEBUG [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) )
2025-08-01 17:49:32.365 |-DEBUG [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 23(Long), 42(Long), 54(Long), 47(Long), 52(Long), 55(Long), 19(Long), 38(Long), 48(Long), 39(Long), 56(Long), 57(Long), 51(Long)
2025-08-01 17:49:32.397 |-DEBUG [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:49:32.398 |-INFO  [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:49:32.541 |-INFO  [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryCustomFormByFormIds 请求入参. request={"formIds":[51,39],"operationUserId":null,"operationShopId":0}
2025-08-01 17:49:32.542 |-DEBUG [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==>  Preparing: select id, name, create_date, update_date from base_custom_form WHERE ( id in ( ? , ? ) )
2025-08-01 17:49:32.542 |-DEBUG [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| ==> Parameters: 51(Long), 39(Long)
2025-08-01 17:49:32.574 |-DEBUG [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper.selectByExample [135] -| <==      Total: 0
2025-08-01 17:49:32.575 |-INFO  [XNIO-1 task-2][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryCustomFormByFormIds success. 请求结果. response={"data":{"formList":[]},"code":0,"message":null}
2025-08-01 17:49:32.617 |-INFO  [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null}
2025-08-01 17:53:00.136 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-08-01 17:53:00.136 |-INFO  [Thread-279][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-08-01 17:53:00.136 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-08-01 17:53:00.136 |-INFO  [Thread-279][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-08-01 17:53:00.136 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.136 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.136 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.136 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.136 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.137 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.137 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.137 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.137 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.137 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.137 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.137 |-INFO  [Thread-280][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.139 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-08-01 17:53:00.139 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 17:53:00.139 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-08-01 17:53:00.150 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-08-01 17:53:00.156 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=441, lastTimeStamp=1754041980156}] instanceId:[InstanceId{instanceId=*******:17568, stable=false}] @ namespace:[himall-base].
2025-08-01 17:53:00.191 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
