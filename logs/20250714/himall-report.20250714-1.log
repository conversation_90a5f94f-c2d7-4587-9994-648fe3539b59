2025-07-14 16:37:55.326 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 16:37:55.638 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 20780 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 16:37:55.654 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 16:37:55.654 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 16:37:56.571 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 16:37:56.571 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 16:38:02.337 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 16:38:02.440 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 16:38:03.043 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 16:38:03.924 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 16:38:03.929 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 16:38:04.184 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 16:38:04.185 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 16:38:04.185 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 16:38:04.185 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 16:38:07.170 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 16:38:09.047 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 16:38:10.622 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 16:38:11.307 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@45c73e09'
2025-07-14 16:38:11.629 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 16:38:11.665 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 16:38:11.885 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 16:38:11.925 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 16:38:11.949 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 16:38:11.977 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 16:38:12.005 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 16:38:12.024 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 16:38:12.050 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 16:38:12.089 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 16:38:12.123 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 16:38:12.157 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 16:38:12.185 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 16:38:12.208 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 16:38:12.234 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 16:38:12.255 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 16:38:12.278 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 16:38:12.309 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 16:38:12.314 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 16:38:12.319 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 16:38:14.106 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 16:38:15.232 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 16:38:19.411 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 16:38:26.017 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 16:38:27.764 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 16:38:32.748 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 16:38:32.763 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 16:38:34.837 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:20780, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 16:38:35.569 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752482314843}] - instanceId:[InstanceId{instanceId=*******:20780, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 16:38:35.714 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 16:38:35.717 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 16:38:35.717 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 16:38:35.717 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 16:38:41.033 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1fb0c411[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@34784a86[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3c053ff2[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@a59e0e4[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@13d02df2[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@650a6974[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@70653088[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@357eb0a0[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@27d3a440[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 16:38:41.034 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@11a43807[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 16:38:41.522 |-INFO  [Thread-114][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 16:38:41.587 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 16:38:41.606 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 16:38:41.669 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 16:38:41.735 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 16:38:41.943 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 16:38:42.101 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 16:38:42.398 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 16:38:42.399 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 16:38:42.729 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 16:38:43.987 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 56.716 seconds (JVM running for 59.508)
2025-07-14 16:38:43.993 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 16:38:43.995 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 16:38:46.026 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 16:38:46.028 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 16:38:46.030 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 16:38:47.376 |-INFO  [RMI TCP Connection(10)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 16:43:51.586 |-DEBUG [XNIO-1 task-1][4750f5d4efce5c6b] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/newOld/echarts method:POST start
2025-07-14 16:43:51.586 |-DEBUG [XNIO-1 task-5][95a291c2a97cddad] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/summary method:POST start
2025-07-14 16:43:51.586 |-DEBUG [XNIO-1 task-4][3b6fd3064c1db161] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/increase/echarts method:POST start
2025-07-14 16:43:51.586 |-DEBUG [XNIO-1 task-3][e784e4fa4970d89f] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/newOld/summary method:POST start
2025-07-14 16:43:51.586 |-DEBUG [XNIO-1 task-2][285a72ae7fe5fb0b] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/province/echarts method:POST start
2025-07-14 16:43:52.228 |-INFO  [XNIO-1 task-4][3b6fd3064c1db161] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| increase/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:43:52.228 |-INFO  [XNIO-1 task-2][285a72ae7fe5fb0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| province/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:43:52.228 |-INFO  [XNIO-1 task-5][95a291c2a97cddad] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:43:52.228 |-INFO  [XNIO-1 task-1][4750f5d4efce5c6b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| newOld/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:43:52.228 |-INFO  [XNIO-1 task-3][e784e4fa4970d89f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| newOld/summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:43:53.077 |-INFO  [XNIO-1 task-3][e784e4fa4970d89f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| newOld/summary success. 请求结果. response={"data":null,"code":0,"message":null}
2025-07-14 16:43:53.091 |-INFO  [XNIO-1 task-1][4750f5d4efce5c6b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| newOld/echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"新成交会员数","data":[0]},{"name":"新支付订单数","data":[0]},{"name":"新客单价","data":[0]},{"name":"新支付金额","data":[0]},{"name":"老成交会员数","data":[0]},{"name":"老支付订单数","data":[0]},{"name":"老客单价","data":[0]},{"name":"老支付金额","data":[0]}]},"code":0,"message":null}
2025-07-14 16:43:53.091 |-INFO  [XNIO-1 task-2][285a72ae7fe5fb0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| province/echarts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"会员总数","data":[]}]},"code":0,"message":null}
2025-07-14 16:43:53.091 |-INFO  [XNIO-1 task-4][3b6fd3064c1db161] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| increase/echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"新增会员数","data":[0]}]},"code":0,"message":null}
2025-07-14 16:43:53.106 |-INFO  [XNIO-1 task-5][95a291c2a97cddad] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| summary success. 请求结果. response={"data":{"userTotal":null,"userNew":null,"paymentAmount":null,"paymentUsers":null,"orderUsers":null,"couponUsers":null,"unitPrice":null,"prev":{"userTotal":null,"userNew":null,"paymentAmount":null,"paymentUsers":null,"orderUsers":null,"couponUsers":null,"unitPrice":null,"prev":null}},"code":0,"message":null}
2025-07-14 16:43:53.245 |-DEBUG [XNIO-1 task-3][e784e4fa4970d89f] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/newOld/summary cost:1672ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:43:53.245 |-DEBUG [XNIO-1 task-1][4750f5d4efce5c6b] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/newOld/echarts cost:1672ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","series":["Members"],"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:43:53.245 |-DEBUG [XNIO-1 task-5][95a291c2a97cddad] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/summary cost:1672ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:43:53.245 |-DEBUG [XNIO-1 task-4][3b6fd3064c1db161] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/increase/echarts cost:1672ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:43:53.245 |-DEBUG [XNIO-1 task-2][285a72ae7fe5fb0b] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/province/echarts cost:1672ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:43:59.208 |-DEBUG [XNIO-1 task-1][796b2897a22d4fda] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/increase/echarts method:POST start
2025-07-14 16:43:59.270 |-DEBUG [XNIO-1 task-1][796b2897a22d4fda] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/increase/echarts cost:62ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:43:59.421 |-DEBUG [XNIO-1 task-1][b47eb41ed58c8cbd] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/province/echarts method:POST start
2025-07-14 16:43:59.449 |-DEBUG [XNIO-1 task-1][b47eb41ed58c8cbd] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/province/echarts cost:28ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:01.082 |-DEBUG [XNIO-1 task-1][0c5bec147d2882de] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/summary method:POST start
2025-07-14 16:44:01.083 |-INFO  [XNIO-1 task-1][0c5bec147d2882de] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:01.145 |-INFO  [XNIO-1 task-1][0c5bec147d2882de] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| summary success. 请求结果. response={"data":{"userTotal":null,"userNew":null,"paymentAmount":null,"paymentUsers":null,"orderUsers":null,"couponUsers":null,"unitPrice":null,"prev":{"userTotal":null,"userNew":null,"paymentAmount":null,"paymentUsers":null,"orderUsers":null,"couponUsers":null,"unitPrice":null,"prev":null}},"code":0,"message":null}
2025-07-14 16:44:01.146 |-DEBUG [XNIO-1 task-1][0c5bec147d2882de] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/summary cost:64ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:01.407 |-DEBUG [XNIO-1 task-1][06e30766b29dd1b9] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/newOld/echarts method:POST start
2025-07-14 16:44:01.407 |-DEBUG [XNIO-1 task-5][a8c4bcea8c5ab578] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/province/echarts method:POST start
2025-07-14 16:44:01.409 |-INFO  [XNIO-1 task-1][06e30766b29dd1b9] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| newOld/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:01.409 |-INFO  [XNIO-1 task-5][a8c4bcea8c5ab578] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| province/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:01.430 |-INFO  [XNIO-1 task-1][06e30766b29dd1b9] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| newOld/echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"新成交会员数","data":[0]},{"name":"新支付订单数","data":[0]},{"name":"新客单价","data":[0]},{"name":"新支付金额","data":[0]},{"name":"老成交会员数","data":[0]},{"name":"老支付订单数","data":[0]},{"name":"老客单价","data":[0]},{"name":"老支付金额","data":[0]}]},"code":0,"message":null}
2025-07-14 16:44:01.432 |-DEBUG [XNIO-1 task-1][06e30766b29dd1b9] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/newOld/echarts cost:25ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","series":["Members"],"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:01.457 |-INFO  [XNIO-1 task-5][a8c4bcea8c5ab578] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| province/echarts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"会员总数","data":[]}]},"code":0,"message":null}
2025-07-14 16:44:01.459 |-DEBUG [XNIO-1 task-5][a8c4bcea8c5ab578] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/province/echarts cost:52ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:01.478 |-DEBUG [XNIO-1 task-5][347312aa97f54bfb] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/increase/echarts method:POST start
2025-07-14 16:44:01.479 |-INFO  [XNIO-1 task-5][347312aa97f54bfb] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| increase/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:01.485 |-DEBUG [XNIO-1 task-1][0d6f1b61cb573cf4] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/newOld/summary method:POST start
2025-07-14 16:44:01.487 |-INFO  [XNIO-1 task-1][0d6f1b61cb573cf4] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| newOld/summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:01.505 |-INFO  [XNIO-1 task-5][347312aa97f54bfb] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| increase/echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"新增会员数","data":[0]}]},"code":0,"message":null}
2025-07-14 16:44:01.506 |-DEBUG [XNIO-1 task-5][347312aa97f54bfb] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/increase/echarts cost:28ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:01.507 |-INFO  [XNIO-1 task-1][0d6f1b61cb573cf4] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| newOld/summary success. 请求结果. response={"data":null,"code":0,"message":null}
2025-07-14 16:44:01.509 |-DEBUG [XNIO-1 task-1][0d6f1b61cb573cf4] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/newOld/summary cost:24ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:02.309 |-DEBUG [XNIO-1 task-1][ee3f76d901cdc9c6] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/query method:POST start
2025-07-14 16:44:02.327 |-INFO  [XNIO-1 task-1][ee3f76d901cdc9c6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| query 请求入参. request={"shopId":null,"range":"MONTH","start":1751299200000,"end":1753891200000,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-14 16:44:02.399 |-INFO  [XNIO-1 task-1][ee3f76d901cdc9c6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| query success. 请求结果. response={"data":{"pageNo":1,"pageSize":10,"totalCount":0,"orders":[],"data":[null]},"code":0,"message":null}
2025-07-14 16:44:02.400 |-DEBUG [XNIO-1 task-1][ee3f76d901cdc9c6] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/query cost:91ms input:
{"pageNo":1,"pageSize":10,"total":0,"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:03.230 |-DEBUG [XNIO-1 task-1][1c23a01ff6fec4d9] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/summary method:POST start
2025-07-14 16:44:03.231 |-INFO  [XNIO-1 task-1][1c23a01ff6fec4d9] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:03.296 |-INFO  [XNIO-1 task-1][1c23a01ff6fec4d9] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| summary success. 请求结果. response={"data":{"userTotal":null,"userNew":null,"paymentAmount":null,"paymentUsers":null,"orderUsers":null,"couponUsers":null,"unitPrice":null,"prev":{"userTotal":null,"userNew":null,"paymentAmount":null,"paymentUsers":null,"orderUsers":null,"couponUsers":null,"unitPrice":null,"prev":null}},"code":0,"message":null}
2025-07-14 16:44:03.298 |-DEBUG [XNIO-1 task-1][1c23a01ff6fec4d9] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/summary cost:68ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:03.555 |-DEBUG [XNIO-1 task-1][4fbe07d395d6252f] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/increase/echarts method:POST start
2025-07-14 16:44:03.558 |-INFO  [XNIO-1 task-1][4fbe07d395d6252f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| increase/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:03.562 |-DEBUG [XNIO-1 task-5][1c0771e889db9976] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/newOld/summary method:POST start
2025-07-14 16:44:03.566 |-INFO  [XNIO-1 task-5][1c0771e889db9976] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| newOld/summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:03.582 |-INFO  [XNIO-1 task-1][4fbe07d395d6252f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| increase/echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"新增会员数","data":[0]}]},"code":0,"message":null}
2025-07-14 16:44:03.583 |-DEBUG [XNIO-1 task-1][4fbe07d395d6252f] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/increase/echarts cost:28ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:03.616 |-INFO  [XNIO-1 task-5][1c0771e889db9976] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| newOld/summary success. 请求结果. response={"data":null,"code":0,"message":null}
2025-07-14 16:44:03.618 |-DEBUG [XNIO-1 task-5][1c0771e889db9976] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/newOld/summary cost:56ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:03.637 |-DEBUG [XNIO-1 task-5][179b0f799daa3ab5] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/province/echarts method:POST start
2025-07-14 16:44:03.640 |-DEBUG [XNIO-1 task-1][2093b575b9f72aec] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/newOld/echarts method:POST start
2025-07-14 16:44:03.641 |-INFO  [XNIO-1 task-5][179b0f799daa3ab5] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| province/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:03.642 |-INFO  [XNIO-1 task-1][2093b575b9f72aec] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| newOld/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:03.665 |-INFO  [XNIO-1 task-1][2093b575b9f72aec] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| newOld/echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"新成交会员数","data":[0]},{"name":"新支付订单数","data":[0]},{"name":"新客单价","data":[0]},{"name":"新支付金额","data":[0]},{"name":"老成交会员数","data":[0]},{"name":"老支付订单数","data":[0]},{"name":"老客单价","data":[0]},{"name":"老支付金额","data":[0]}]},"code":0,"message":null}
2025-07-14 16:44:03.666 |-INFO  [XNIO-1 task-5][179b0f799daa3ab5] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| province/echarts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"会员总数","data":[]}]},"code":0,"message":null}
2025-07-14 16:44:03.666 |-DEBUG [XNIO-1 task-1][2093b575b9f72aec] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/newOld/echarts cost:26ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","series":["Members"],"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:03.667 |-DEBUG [XNIO-1 task-5][179b0f799daa3ab5] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/province/echarts cost:30ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:04.419 |-DEBUG [XNIO-1 task-5][a07f533365d4b962] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/province/echarts method:POST start
2025-07-14 16:44:04.419 |-DEBUG [XNIO-1 task-1][a754830dff425abd] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/newOld/summary method:POST start
2025-07-14 16:44:04.427 |-INFO  [XNIO-1 task-5][a07f533365d4b962] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| province/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:04.428 |-INFO  [XNIO-1 task-1][a754830dff425abd] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| newOld/summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:04.436 |-DEBUG [XNIO-1 task-2][08d9f21b77673ad9] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/summary method:POST start
2025-07-14 16:44:04.438 |-INFO  [XNIO-1 task-2][08d9f21b77673ad9] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:04.471 |-INFO  [XNIO-1 task-1][a754830dff425abd] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| newOld/summary success. 请求结果. response={"data":null,"code":0,"message":null}
2025-07-14 16:44:04.472 |-DEBUG [XNIO-1 task-1][a754830dff425abd] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/newOld/summary cost:53ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:04.476 |-INFO  [XNIO-1 task-5][a07f533365d4b962] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| province/echarts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"会员总数","data":[]}]},"code":0,"message":null}
2025-07-14 16:44:04.478 |-DEBUG [XNIO-1 task-5][a07f533365d4b962] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/province/echarts cost:58ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:04.509 |-INFO  [XNIO-1 task-2][08d9f21b77673ad9] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| summary success. 请求结果. response={"data":{"userTotal":null,"userNew":null,"paymentAmount":null,"paymentUsers":null,"orderUsers":null,"couponUsers":null,"unitPrice":null,"prev":{"userTotal":null,"userNew":null,"paymentAmount":null,"paymentUsers":null,"orderUsers":null,"couponUsers":null,"unitPrice":null,"prev":null}},"code":0,"message":null}
2025-07-14 16:44:04.509 |-DEBUG [XNIO-1 task-5][34e5f7d741f99738] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/newOld/echarts method:POST start
2025-07-14 16:44:04.511 |-INFO  [XNIO-1 task-5][34e5f7d741f99738] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| newOld/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:04.512 |-DEBUG [XNIO-1 task-2][08d9f21b77673ad9] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/summary cost:76ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:04.517 |-DEBUG [XNIO-1 task-2][6ed379bf2ca1c15b] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/user/increase/echarts method:POST start
2025-07-14 16:44:04.519 |-INFO  [XNIO-1 task-2][6ed379bf2ca1c15b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| increase/echarts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:04.539 |-INFO  [XNIO-1 task-5][34e5f7d741f99738] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| newOld/echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"新成交会员数","data":[0]},{"name":"新支付订单数","data":[0]},{"name":"新客单价","data":[0]},{"name":"新支付金额","data":[0]},{"name":"老成交会员数","data":[0]},{"name":"老支付订单数","data":[0]},{"name":"老客单价","data":[0]},{"name":"老支付金额","data":[0]}]},"code":0,"message":null}
2025-07-14 16:44:04.540 |-DEBUG [XNIO-1 task-5][34e5f7d741f99738] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/newOld/echarts cost:32ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","series":["Members"],"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:04.545 |-INFO  [XNIO-1 task-2][6ed379bf2ca1c15b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| increase/echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"新增会员数","data":[0]}]},"code":0,"message":null}
2025-07-14 16:44:04.546 |-DEBUG [XNIO-1 task-2][6ed379bf2ca1c15b] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/user/increase/echarts cost:29ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:27.896 |-DEBUG [XNIO-1 task-2][033af72c234e4ddb] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/summary method:POST start
2025-07-14 16:44:27.906 |-INFO  [XNIO-1 task-2][033af72c234e4ddb] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visits 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:27.982 |-INFO  [XNIO-1 task-2][033af72c234e4ddb] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visits success. 请求结果. response={"data":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":null}},"code":0,"message":null}
2025-07-14 16:44:27.984 |-DEBUG [XNIO-1 task-2][033af72c234e4ddb] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/summary cost:88ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:28.202 |-DEBUG [XNIO-1 task-2][c335961b5b8db395] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/categoryEcharts method:POST start
2025-07-14 16:44:28.207 |-INFO  [XNIO-1 task-2][c335961b5b8db395] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| categoryEcharts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:28.209 |-DEBUG [XNIO-1 task-5][c3952290921c1a57] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/visitsEcharts method:POST start
2025-07-14 16:44:28.212 |-INFO  [XNIO-1 task-5][c3952290921c1a57] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visitsEcharts 请求入参. request={"range":"MONTH","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:28.234 |-INFO  [XNIO-1 task-2][c335961b5b8db395] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| categoryEcharts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"类目销量","data":[]},{"name":"类目销售额","data":[]}]},"code":0,"message":null}
2025-07-14 16:44:28.236 |-DEBUG [XNIO-1 task-2][c335961b5b8db395] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/categoryEcharts cost:34ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:28.262 |-INFO  [XNIO-1 task-5][c3952290921c1a57] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visitsEcharts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"被访问商品数","data":[0]},{"name":"动销商品数","data":[0]}]},"code":0,"message":null}
2025-07-14 16:44:28.263 |-DEBUG [XNIO-1 task-5][c3952290921c1a57] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/visitsEcharts cost:54ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:29.927 |-DEBUG [XNIO-1 task-5][7c62ed59afb0cfb5] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/summary method:POST start
2025-07-14 16:44:29.936 |-INFO  [XNIO-1 task-5][7c62ed59afb0cfb5] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| summary 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:30.022 |-INFO  [XNIO-1 task-5][7c62ed59afb0cfb5] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| summary success. 请求结果. response={"data":{"visitsCount":null,"visitsUsers":null,"orderAmount":null,"orderOrders":null,"orderUsers":null,"paymentAmount":null,"paymentQuantity":null,"paymentOrders":null,"paymentUsers":null,"unitPrice":null,"orderVisitsRate":null,"paymentOrderRate":null,"paymentVisitsRate":null,"prev":{"visitsCount":null,"visitsUsers":null,"orderAmount":null,"orderOrders":null,"orderUsers":null,"paymentAmount":null,"paymentQuantity":null,"paymentOrders":null,"paymentUsers":null,"unitPrice":null,"orderVisitsRate":null,"paymentOrderRate":null,"paymentVisitsRate":null,"prev":null}},"code":0,"message":null}
2025-07-14 16:44:30.024 |-DEBUG [XNIO-1 task-5][7c62ed59afb0cfb5] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/summary cost:97ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:30.157 |-DEBUG [XNIO-1 task-5][27fbe92e43de44b8] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/echarts method:POST start
2025-07-14 16:44:30.160 |-INFO  [XNIO-1 task-5][27fbe92e43de44b8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| echarts 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:30.190 |-INFO  [XNIO-1 task-5][27fbe92e43de44b8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-05","2025-07-06","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-12","2025-07-13","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-19","2025-07-20","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-26","2025-07-27","2025-07-28","2025-07-29","2025-07-30"]},"series":[{"name":"支付金额","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付件数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"订单-访客转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付-下单转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付-访客转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}]},"code":0,"message":null}
2025-07-14 16:44:30.193 |-DEBUG [XNIO-1 task-5][27fbe92e43de44b8] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/echarts cost:36ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:30.383 |-DEBUG [XNIO-1 task-5][115ec26febdbf58a] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/province method:POST start
2025-07-14 16:44:30.385 |-INFO  [XNIO-1 task-5][115ec26febdbf58a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| province 请求入参. request={"range":"MONTH","shopId":0,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:30.423 |-INFO  [XNIO-1 task-5][115ec26febdbf58a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| province success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"支付金额","data":[]},{"name":"支付订单笔数","data":[]},{"name":"支付人数","data":[]}]},"code":0,"message":null}
2025-07-14 16:44:30.425 |-DEBUG [XNIO-1 task-5][115ec26febdbf58a] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/province cost:42ms input:
{"range":"MONTH","day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:31.454 |-DEBUG [XNIO-1 task-5][5e69cf842101cc7c] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/visits method:POST start
2025-07-14 16:44:31.463 |-INFO  [XNIO-1 task-5][5e69cf842101cc7c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visits 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:31.528 |-INFO  [XNIO-1 task-5][5e69cf842101cc7c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visits success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-05","2025-07-06","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-12","2025-07-13","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-19","2025-07-20","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-26","2025-07-27","2025-07-28","2025-07-29","2025-07-30"]},"series":[{"name":"总访问人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"打开次数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"访问次数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"访问人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"新用户数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"人均停留时长","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"次均停留时长","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"访问深度","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"转发次数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"转发人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}]},"code":0,"message":null}
2025-07-14 16:44:31.529 |-DEBUG [XNIO-1 task-5][5e69cf842101cc7c] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/visits cost:75ms input:
{"range":"DAY","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:31.781 |-DEBUG [XNIO-1 task-2][2fd7d4188c00c4dd] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/portrait method:POST start
2025-07-14 16:44:31.781 |-DEBUG [XNIO-1 task-5][0d57c92447e4b2be] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/portrait method:POST start
2025-07-14 16:44:31.783 |-INFO  [XNIO-1 task-2][2fd7d4188c00c4dd] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| portrait 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1752336000000,"end":1752336000000}
2025-07-14 16:44:31.783 |-INFO  [XNIO-1 task-5][0d57c92447e4b2be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| portrait 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1752336000000,"end":1752336000000}
2025-07-14 16:44:31.794 |-DEBUG [XNIO-1 task-1][987c4434cc7ec1f2] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/portrait method:POST start
2025-07-14 16:44:31.794 |-DEBUG [XNIO-1 task-4][1f0f76c54840d99a] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/portrait method:POST start
2025-07-14 16:44:31.795 |-INFO  [XNIO-1 task-1][987c4434cc7ec1f2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| portrait 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1752336000000,"end":1752336000000}
2025-07-14 16:44:31.795 |-INFO  [XNIO-1 task-4][1f0f76c54840d99a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| portrait 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1752336000000,"end":1752336000000}
2025-07-14 16:44:31.810 |-INFO  [XNIO-1 task-5][0d57c92447e4b2be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| portrait success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[]},"code":0,"message":null}
2025-07-14 16:44:31.811 |-DEBUG [XNIO-1 task-5][0d57c92447e4b2be] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/portrait cost:30ms input:
{"range":"DAY","start":"2025-07-13","end":"2025-07-13","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:31.828 |-INFO  [XNIO-1 task-2][2fd7d4188c00c4dd] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| portrait success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[]},"code":0,"message":null}
2025-07-14 16:44:31.829 |-DEBUG [XNIO-1 task-2][2fd7d4188c00c4dd] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/portrait cost:48ms input:
{"range":"DAY","start":"2025-07-13","end":"2025-07-13","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:31.835 |-INFO  [XNIO-1 task-1][987c4434cc7ec1f2] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| portrait success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[]},"code":0,"message":null}
2025-07-14 16:44:31.836 |-DEBUG [XNIO-1 task-1][987c4434cc7ec1f2] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/portrait cost:41ms input:
{"range":"DAY","start":"2025-07-13","end":"2025-07-13","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:31.836 |-INFO  [XNIO-1 task-4][1f0f76c54840d99a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| portrait success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[]},"code":0,"message":null}
2025-07-14 16:44:31.838 |-DEBUG [XNIO-1 task-4][1f0f76c54840d99a] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/portrait cost:44ms input:
{"range":"DAY","start":"2025-07-13","end":"2025-07-13","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:34.248 |-DEBUG [XNIO-1 task-4][716813975715ae3f] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:44:34.351 |-ERROR [XNIO-1 task-4][716813975715ae3f] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
javax.validation.ConstraintDeclarationException: HV000151: A method overriding another method must not redefine the parameter constraint configuration, but method CustomController#process(CustomProcessReq) redefines the configuration of ReportCustomFeign#process(CustomProcessReq).
	at org.hibernate.validator.internal.metadata.aggregated.rule.OverridingMethodMustNotAlterParameterConstraints.apply(OverridingMethodMustNotAlterParameterConstraints.java:24) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.assertCorrectnessOfConfiguration(ExecutableMetaData.java:462) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.build(ExecutableMetaData.java:380) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder$BuilderDelegate.build(BeanMetaDataBuilder.java:260) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder.build(BeanMetaDataBuilder.java:133) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.createBeanMetaData(BeanMetaDataManagerImpl.java:206) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.getBeanMetaData(BeanMetaDataManagerImpl.java:165) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:267) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:235) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:110) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.hishop.himall.report.core.web.impl.CustomController$$EnhancerBySpringCGLIB$$9b30c8a3.query(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 16:44:34.389 |-DEBUG [XNIO-1 task-4][716813975715ae3f] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:141ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:40.017 |-DEBUG [XNIO-1 task-4][c3f5e99fe17a14ca] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/visits method:POST start
2025-07-14 16:44:40.019 |-INFO  [XNIO-1 task-4][c3f5e99fe17a14ca] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visits 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 16:44:40.063 |-INFO  [XNIO-1 task-4][c3f5e99fe17a14ca] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visits success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-05","2025-07-06","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-12","2025-07-13","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-19","2025-07-20","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-26","2025-07-27","2025-07-28","2025-07-29","2025-07-30"]},"series":[{"name":"总访问人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"打开次数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"访问次数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"访问人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"新用户数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"人均停留时长","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"次均停留时长","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"访问深度","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"转发次数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"转发人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}]},"code":0,"message":null}
2025-07-14 16:44:40.065 |-DEBUG [XNIO-1 task-4][c3f5e99fe17a14ca] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/visits cost:48ms input:
{"range":"DAY","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:40.358 |-DEBUG [XNIO-1 task-4][a8c1f846d3095923] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/portrait method:POST start
2025-07-14 16:44:40.361 |-INFO  [XNIO-1 task-4][a8c1f846d3095923] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| portrait 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1752336000000,"end":1752336000000}
2025-07-14 16:44:40.366 |-DEBUG [XNIO-1 task-1][5ad3e0c13c3be3d1] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/portrait method:POST start
2025-07-14 16:44:40.366 |-DEBUG [XNIO-1 task-2][282d228cfa4d287a] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/portrait method:POST start
2025-07-14 16:44:40.370 |-INFO  [XNIO-1 task-1][5ad3e0c13c3be3d1] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| portrait 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1752336000000,"end":1752336000000}
2025-07-14 16:44:40.370 |-INFO  [XNIO-1 task-2][282d228cfa4d287a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| portrait 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1752336000000,"end":1752336000000}
2025-07-14 16:44:40.372 |-DEBUG [XNIO-1 task-5][893a491408d7eaed] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/wechat/portrait method:POST start
2025-07-14 16:44:40.375 |-INFO  [XNIO-1 task-5][893a491408d7eaed] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| portrait 请求入参. request={"range":"DAY","shopId":0,"day":null,"start":1752336000000,"end":1752336000000}
2025-07-14 16:44:40.388 |-INFO  [XNIO-1 task-4][a8c1f846d3095923] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| portrait success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[]},"code":0,"message":null}
2025-07-14 16:44:40.390 |-DEBUG [XNIO-1 task-4][a8c1f846d3095923] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/portrait cost:32ms input:
{"range":"DAY","start":"2025-07-13","end":"2025-07-13","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:40.416 |-INFO  [XNIO-1 task-1][5ad3e0c13c3be3d1] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| portrait success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[]},"code":0,"message":null}
2025-07-14 16:44:40.417 |-DEBUG [XNIO-1 task-1][5ad3e0c13c3be3d1] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/portrait cost:51ms input:
{"range":"DAY","start":"2025-07-13","end":"2025-07-13","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:40.423 |-INFO  [XNIO-1 task-2][282d228cfa4d287a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| portrait success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[]},"code":0,"message":null}
2025-07-14 16:44:40.424 |-DEBUG [XNIO-1 task-2][282d228cfa4d287a] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/portrait cost:58ms input:
{"range":"DAY","start":"2025-07-13","end":"2025-07-13","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:40.433 |-INFO  [XNIO-1 task-5][893a491408d7eaed] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| portrait success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[]},"code":0,"message":null}
2025-07-14 16:44:40.434 |-DEBUG [XNIO-1 task-5][893a491408d7eaed] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/wechat/portrait cost:62ms input:
{"range":"DAY","start":"2025-07-13","end":"2025-07-13","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:44:43.814 |-DEBUG [XNIO-1 task-5][cf6c9bc2ebd13e25] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:44:43.819 |-ERROR [XNIO-1 task-5][cf6c9bc2ebd13e25] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
javax.validation.ConstraintDeclarationException: HV000151: A method overriding another method must not redefine the parameter constraint configuration, but method CustomController#process(CustomProcessReq) redefines the configuration of ReportCustomFeign#process(CustomProcessReq).
	at org.hibernate.validator.internal.metadata.aggregated.rule.OverridingMethodMustNotAlterParameterConstraints.apply(OverridingMethodMustNotAlterParameterConstraints.java:24) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.assertCorrectnessOfConfiguration(ExecutableMetaData.java:462) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.build(ExecutableMetaData.java:380) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder$BuilderDelegate.build(BeanMetaDataBuilder.java:260) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder.build(BeanMetaDataBuilder.java:133) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.createBeanMetaData(BeanMetaDataManagerImpl.java:206) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.getBeanMetaData(BeanMetaDataManagerImpl.java:165) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:267) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:235) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:110) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.hishop.himall.report.core.web.impl.CustomController$$EnhancerBySpringCGLIB$$9b30c8a3.query(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 16:44:43.822 |-DEBUG [XNIO-1 task-5][cf6c9bc2ebd13e25] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:8ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:45:16.454 |-DEBUG [XNIO-1 task-5][90074f4124f47ceb] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:45:16.459 |-ERROR [XNIO-1 task-5][90074f4124f47ceb] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
javax.validation.ConstraintDeclarationException: HV000151: A method overriding another method must not redefine the parameter constraint configuration, but method CustomController#process(CustomProcessReq) redefines the configuration of ReportCustomFeign#process(CustomProcessReq).
	at org.hibernate.validator.internal.metadata.aggregated.rule.OverridingMethodMustNotAlterParameterConstraints.apply(OverridingMethodMustNotAlterParameterConstraints.java:24) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.assertCorrectnessOfConfiguration(ExecutableMetaData.java:462) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.build(ExecutableMetaData.java:380) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder$BuilderDelegate.build(BeanMetaDataBuilder.java:260) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder.build(BeanMetaDataBuilder.java:133) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.createBeanMetaData(BeanMetaDataManagerImpl.java:206) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.getBeanMetaData(BeanMetaDataManagerImpl.java:165) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:267) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:235) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:110) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.hishop.himall.report.core.web.impl.CustomController$$EnhancerBySpringCGLIB$$9b30c8a3.query(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 16:45:16.461 |-DEBUG [XNIO-1 task-5][90074f4124f47ceb] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:8ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:45:30.062 |-DEBUG [XNIO-1 task-5][f2d4d80bde6e1866] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:45:30.067 |-ERROR [XNIO-1 task-5][f2d4d80bde6e1866] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
javax.validation.ConstraintDeclarationException: HV000151: A method overriding another method must not redefine the parameter constraint configuration, but method CustomController#process(CustomProcessReq) redefines the configuration of ReportCustomFeign#process(CustomProcessReq).
	at org.hibernate.validator.internal.metadata.aggregated.rule.OverridingMethodMustNotAlterParameterConstraints.apply(OverridingMethodMustNotAlterParameterConstraints.java:24) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.assertCorrectnessOfConfiguration(ExecutableMetaData.java:462) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.build(ExecutableMetaData.java:380) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder$BuilderDelegate.build(BeanMetaDataBuilder.java:260) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder.build(BeanMetaDataBuilder.java:133) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.createBeanMetaData(BeanMetaDataManagerImpl.java:206) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.getBeanMetaData(BeanMetaDataManagerImpl.java:165) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:267) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:235) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:110) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.hishop.himall.report.core.web.impl.CustomController$$EnhancerBySpringCGLIB$$9b30c8a3.query(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 16:45:30.069 |-DEBUG [XNIO-1 task-5][f2d4d80bde6e1866] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:7ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:47:49.267 |-DEBUG [XNIO-1 task-5][de32f0546fbba3dc] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:47:49.274 |-ERROR [XNIO-1 task-5][de32f0546fbba3dc] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
javax.validation.ConstraintDeclarationException: HV000151: A method overriding another method must not redefine the parameter constraint configuration, but method CustomController#process(CustomProcessReq) redefines the configuration of ReportCustomFeign#process(CustomProcessReq).
	at org.hibernate.validator.internal.metadata.aggregated.rule.OverridingMethodMustNotAlterParameterConstraints.apply(OverridingMethodMustNotAlterParameterConstraints.java:24) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.assertCorrectnessOfConfiguration(ExecutableMetaData.java:462) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.build(ExecutableMetaData.java:380) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder$BuilderDelegate.build(BeanMetaDataBuilder.java:260) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder.build(BeanMetaDataBuilder.java:133) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.createBeanMetaData(BeanMetaDataManagerImpl.java:206) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.getBeanMetaData(BeanMetaDataManagerImpl.java:165) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:267) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:235) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:110) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.hishop.himall.report.core.web.impl.CustomController$$EnhancerBySpringCGLIB$$9b30c8a3.query(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 16:47:49.276 |-DEBUG [XNIO-1 task-5][de32f0546fbba3dc] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:9ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:50:27.122 |-DEBUG [XNIO-1 task-5][a4ffe6961fd7b008] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:50:27.125 |-ERROR [XNIO-1 task-5][a4ffe6961fd7b008] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
javax.validation.ConstraintDeclarationException: HV000151: A method overriding another method must not redefine the parameter constraint configuration, but method CustomController#process(CustomProcessReq) redefines the configuration of ReportCustomFeign#process(CustomProcessReq).
	at org.hibernate.validator.internal.metadata.aggregated.rule.OverridingMethodMustNotAlterParameterConstraints.apply(OverridingMethodMustNotAlterParameterConstraints.java:24) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.assertCorrectnessOfConfiguration(ExecutableMetaData.java:462) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.ExecutableMetaData$Builder.build(ExecutableMetaData.java:380) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder$BuilderDelegate.build(BeanMetaDataBuilder.java:260) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.aggregated.BeanMetaDataBuilder.build(BeanMetaDataBuilder.java:133) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.createBeanMetaData(BeanMetaDataManagerImpl.java:206) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.metadata.BeanMetaDataManagerImpl.getBeanMetaData(BeanMetaDataManagerImpl.java:165) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:267) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.hibernate.validator.internal.engine.ValidatorImpl.validateParameters(ValidatorImpl.java:235) ~[hibernate-validator-6.2.3.Final.jar:6.2.3.Final]
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:110) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.33.jar:5.3.33]
	at com.hishop.himall.report.core.web.impl.CustomController$$EnhancerBySpringCGLIB$$9b30c8a3.query(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 16:50:27.127 |-DEBUG [XNIO-1 task-5][a4ffe6961fd7b008] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:5ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:54:31.013 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 16:54:31.013 |-INFO  [Thread-97][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 16:54:31.013 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 16:54:31.013 |-INFO  [Thread-97][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.013 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 16:54:31.015 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 16:54:31.017 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752483271017}] instanceId:[InstanceId{instanceId=*******:20780, stable=false}] @ namespace:[himall-report].
2025-07-14 16:54:31.045 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 16:54:31.049 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 16:54:31.054 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 16:54:31.195 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 16:54:31.195 |-INFO  [Thread-114][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-14 16:54:31.536 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-14 16:54:31.536 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-14 16:54:31.536 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 16:54:31.536 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 16:54:31.536 |-INFO  [Thread-113][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 16:54:31.537 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 16:54:31.572 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 16:54:34.614 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 16:54:34.624 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 16:54:34.635 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 16:54:34.635 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 16:54:34.635 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 16:54:34.638 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 16:54:34.638 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 16:54:34.638 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 16:54:57.047 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 16:54:57.184 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 33248 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 16:54:57.187 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 16:54:57.188 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 16:54:57.569 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 16:54:57.570 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 16:55:00.980 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 16:55:01.029 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 16:55:01.309 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 16:55:01.892 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 16:55:01.897 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 16:55:02.205 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 16:55:02.207 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 16:55:02.208 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 16:55:02.208 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 16:55:04.528 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 16:55:06.359 |-INFO  [redisson-netty-1-8][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 16:55:08.239 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 16:55:08.817 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@77e2a5d3'
2025-07-14 16:55:09.119 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 16:55:09.139 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 16:55:09.250 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 16:55:09.272 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 16:55:09.288 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 16:55:09.305 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 16:55:09.318 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 16:55:09.337 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 16:55:09.353 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 16:55:09.370 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 16:55:09.386 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 16:55:09.399 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 16:55:09.414 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 16:55:09.426 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 16:55:09.441 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 16:55:09.457 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 16:55:09.472 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 16:55:09.499 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 16:55:09.504 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 16:55:09.508 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 16:55:10.968 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 16:55:11.833 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 16:55:15.765 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 16:55:21.572 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 16:55:22.559 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 16:55:26.280 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 16:55:26.292 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 16:55:27.661 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:33248, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 16:55:28.287 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752483327667}] - instanceId:[InstanceId{instanceId=*******:33248, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 16:55:28.422 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 16:55:28.424 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 16:55:28.424 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 16:55:28.425 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 16:55:32.773 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6a76eef[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@244cdad1[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3c81f484[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@62c6c467[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5fab1ec6[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1d73ddb0[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2e652f4e[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a543319[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d0e5fbb[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 16:55:32.774 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7e1383f7[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 16:55:33.235 |-INFO  [Thread-95][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 16:55:33.289 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 16:55:33.299 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 16:55:33.348 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 16:55:33.595 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 16:55:33.628 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 16:55:33.786 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 16:55:34.010 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 16:55:34.010 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 16:55:34.275 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 16:55:35.393 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 43.619 seconds (JVM running for 46.175)
2025-07-14 16:55:35.399 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 16:55:35.400 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 16:55:36.824 |-INFO  [RMI TCP Connection(5)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 16:55:37.144 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 16:55:37.145 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 16:55:37.147 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 16:55:41.818 |-DEBUG [XNIO-1 task-1][33938920aa892a8b] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:55:47.729 |-DEBUG [XNIO-1 task-1][33938920aa892a8b] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:5923ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:55:57.856 |-DEBUG [XNIO-1 task-1][41287165a4b357e0] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:55:57.950 |-DEBUG [XNIO-1 task-1][41287165a4b357e0] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:94ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:56:00.961 |-DEBUG [XNIO-1 task-1][d874eebb597a2812] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/record/query method:POST start
2025-07-14 16:56:01.021 |-DEBUG [XNIO-1 task-1][d874eebb597a2812] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/record/query cost:60ms input:
{"pageNo":1,"pageSize":10,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 16:56:03.768 |-DEBUG [XNIO-1 task-1][36e16c1f4ac04fec] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 16:56:03.854 |-DEBUG [XNIO-1 task-1][36e16c1f4ac04fec] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:86ms input:
{"pageNo":1,"pageSize":10,"total":0,"name":null,"dimension":null,"range":null,"automatic":null,"operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-14 17:17:51.446 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 17:17:51.446 |-INFO  [Thread-80][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:17:51.446 |-INFO  [Thread-80][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:17:51.446 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:17:51.446 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 17:17:51.448 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 17:17:51.448 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:17:51.449 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.449 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.449 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.449 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 17:17:51.449 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.449 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.450 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.450 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.450 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.450 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.450 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.450 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.450 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:17:51.451 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752484671451}] instanceId:[InstanceId{instanceId=*******:33248, stable=false}] @ namespace:[himall-report].
2025-07-14 17:17:51.553 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:17:51.558 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 17:17:51.563 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 17:17:51.741 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:17:51.743 |-INFO  [Thread-95][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-14 17:17:52.047 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-14 17:17:52.047 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-14 17:17:52.047 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 17:17:52.048 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 17:17:52.048 |-INFO  [Thread-94][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 17:17:52.049 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 17:17:52.074 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 17:17:55.123 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 17:17:55.131 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 17:17:55.145 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 17:17:55.145 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 17:17:55.145 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 17:17:55.149 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 17:17:55.149 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 17:17:55.149 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 17:18:29.786 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 17:18:29.971 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 32344 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 17:18:29.978 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 17:18:29.979 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 17:18:30.578 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 17:18:30.579 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 17:18:35.724 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 17:18:35.794 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 17:18:36.231 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 17:18:37.007 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 17:18:37.016 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 17:18:37.222 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 17:18:37.222 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 17:18:37.222 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 17:18:37.222 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 17:18:40.138 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 17:18:42.161 |-INFO  [redisson-netty-1-12][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:18:43.679 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:18:44.293 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@5292d417'
2025-07-14 17:18:44.643 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 17:18:44.665 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 17:18:44.764 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 17:18:44.782 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 17:18:44.802 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 17:18:44.816 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 17:18:44.835 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 17:18:44.859 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 17:18:44.883 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 17:18:44.908 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 17:18:44.927 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 17:18:44.941 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 17:18:44.957 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 17:18:44.969 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 17:18:44.982 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 17:18:44.994 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 17:18:45.006 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 17:18:45.022 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 17:18:45.025 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 17:18:45.028 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 17:18:46.611 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 17:18:47.539 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 17:18:53.413 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 17:19:01.931 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 17:19:02.987 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 17:19:07.363 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 17:19:07.378 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 17:19:09.282 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:32344, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:19:10.014 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752484749288}] - instanceId:[InstanceId{instanceId=*******:32344, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:19:10.226 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 17:19:10.229 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 17:19:10.229 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 17:19:10.229 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 17:19:15.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@51aae61a[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 17:19:15.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2cd9488b[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 17:19:15.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@f4694f8[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 17:19:15.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@12a16e42[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 17:19:15.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@143dc03a[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 17:19:15.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@72464f26[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 17:19:15.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@491ce4c2[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 17:19:15.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@50d640dd[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 17:19:15.862 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2a1066ed[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 17:19:15.862 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6aef33f6[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 17:19:16.372 |-INFO  [Thread-115][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 17:19:16.461 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 17:19:16.479 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 17:19:16.541 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 17:19:16.892 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 17:19:16.930 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 17:19:17.157 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 17:19:17.460 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 17:19:17.460 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 17:19:17.778 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 17:19:20.389 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 57.437 seconds (JVM running for 60.634)
2025-07-14 17:19:20.397 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 17:19:20.401 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 17:19:22.342 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 17:19:22.343 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 17:19:22.346 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 17:19:22.828 |-INFO  [RMI TCP Connection(8)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 17:19:35.597 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 17:19:35.597 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:19:35.597 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:19:35.597 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:19:35.597 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 17:19:35.597 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.597 |-INFO  [Thread-98][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:19:35.597 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.597 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.597 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.597 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 17:19:35.597 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.597 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.598 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.598 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.598 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.598 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.598 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.598 |-INFO  [Thread-99][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:19:35.599 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 17:19:35.602 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752484775602}] instanceId:[InstanceId{instanceId=*******:32344, stable=false}] @ namespace:[himall-report].
2025-07-14 17:19:35.627 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:19:35.631 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 17:19:35.657 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 17:19:35.806 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:19:35.807 |-INFO  [Thread-115][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-14 17:19:36.116 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-14 17:19:36.116 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-14 17:19:36.116 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 17:19:36.116 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 17:19:36.116 |-INFO  [Thread-114][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 17:19:36.117 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 17:19:36.148 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 17:19:39.192 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 17:19:39.200 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 17:19:39.214 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 17:19:39.215 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 17:19:39.215 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 17:19:39.219 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 17:19:39.220 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 17:19:39.220 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 17:20:24.548 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 17:20:24.703 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 29396 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 17:20:24.707 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 17:20:24.708 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 17:20:25.111 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 17:20:25.111 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 17:20:28.745 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 17:20:28.796 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 17:20:29.108 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 17:20:29.682 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 17:20:29.686 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 17:20:29.943 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 17:20:29.943 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 17:20:29.943 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 17:20:29.944 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 17:20:32.363 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 17:20:33.937 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:20:35.573 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:20:36.145 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@4842a683'
2025-07-14 17:20:36.447 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 17:20:36.470 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 17:20:36.564 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 17:20:36.584 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 17:20:36.601 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 17:20:36.615 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 17:20:36.630 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 17:20:36.646 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 17:20:36.671 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 17:20:36.696 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 17:20:36.728 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 17:20:36.742 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 17:20:36.755 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 17:20:36.767 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 17:20:36.778 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 17:20:36.791 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 17:20:36.808 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 17:20:36.824 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 17:20:36.827 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 17:20:36.831 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 17:20:38.166 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 17:20:39.024 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 17:20:42.749 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 17:20:48.603 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 17:20:49.591 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 17:20:52.757 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 17:20:52.768 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 17:20:54.120 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:29396, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:20:54.797 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752484854125}] - instanceId:[InstanceId{instanceId=*******:29396, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:20:55.047 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 17:20:55.049 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 17:20:55.049 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 17:20:55.050 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 17:20:59.472 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b51cbf7[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1d4d1621[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7145469[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@646e17b9[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@515576b0[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@245c8ea5[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@14e215b[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@15093d14[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f9329f3[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 17:20:59.473 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2f7aab02[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 17:20:59.907 |-ERROR [Thread-95][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 17:20:59.969 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 17:20:59.981 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 17:21:00.023 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 17:21:00.077 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 17:21:00.101 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 17:21:00.238 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 17:21:00.622 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 17:21:00.622 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 17:21:01.007 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 17:21:02.142 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 43.848 seconds (JVM running for 46.347)
2025-07-14 17:21:02.148 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 17:21:02.149 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 17:21:03.912 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 17:21:03.913 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 17:21:03.915 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 17:21:04.367 |-INFO  [RMI TCP Connection(6)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 17:22:52.388 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:22:52.388 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-80][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-80][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.388 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.389 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:22:52.390 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 17:22:52.392 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752484972391}] instanceId:[InstanceId{instanceId=*******:29396, stable=false}] @ namespace:[himall-report].
2025-07-14 17:22:52.426 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:22:52.430 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 17:22:52.452 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 17:22:52.624 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:22:52.627 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 17:22:52.628 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 17:22:52.628 |-INFO  [Thread-94][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 17:22:52.628 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 17:22:52.655 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 17:22:55.741 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 17:22:55.748 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 17:22:55.761 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 17:22:55.762 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 17:22:55.762 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 17:22:55.765 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 17:22:55.765 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 17:22:55.765 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 17:23:22.297 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 17:23:22.433 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 27292 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 17:23:22.439 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 17:23:22.439 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 17:23:22.846 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 17:23:22.846 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 17:23:26.528 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 17:23:26.581 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 17:23:26.888 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 17:23:27.514 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 17:23:27.518 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 17:23:27.800 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 17:23:27.800 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 17:23:27.801 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 17:23:27.801 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 17:23:30.089 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 17:23:31.800 |-INFO  [redisson-netty-1-8][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:23:33.557 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:23:34.103 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@77e2a5d3'
2025-07-14 17:23:34.369 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 17:23:34.387 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 17:23:34.474 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 17:23:34.499 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 17:23:34.527 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 17:23:34.544 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 17:23:34.556 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 17:23:34.567 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 17:23:34.581 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 17:23:34.596 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 17:23:34.611 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 17:23:34.625 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 17:23:34.641 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 17:23:34.653 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 17:23:34.665 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 17:23:34.679 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 17:23:34.694 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 17:23:34.710 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 17:23:34.714 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 17:23:34.717 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 17:23:36.004 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 17:23:36.791 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 17:23:40.497 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 17:23:45.927 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 17:23:46.878 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 17:23:49.826 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 17:23:49.837 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 17:23:51.072 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:27292, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:23:51.673 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752485031078}] - instanceId:[InstanceId{instanceId=*******:27292, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:23:51.788 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 17:23:51.790 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 17:23:51.790 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 17:23:51.790 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7e765313[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@b2a688d[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@296ac298[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5a1b205[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@49038769[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1135f0c4[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6eb49e78[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@660d3831[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@33e0d356[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 17:23:55.854 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d6db63e[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 17:23:56.307 |-ERROR [Thread-94][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 17:23:56.369 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 17:23:56.380 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 17:23:56.424 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 17:23:56.479 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 17:23:56.504 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 17:23:56.815 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 17:23:57.023 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 17:23:57.023 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 17:23:57.390 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 17:23:58.444 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 41.808 seconds (JVM running for 44.331)
2025-07-14 17:23:58.450 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 17:23:58.452 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 17:24:00.349 |-INFO  [RMI TCP Connection(4)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 17:24:00.478 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 17:24:00.479 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 17:24:00.481 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 17:36:07.416 |-INFO  [Thread-80][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:36:07.416 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 17:36:07.416 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 17:36:07.416 |-INFO  [Thread-80][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:36:07.416 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:36:07.416 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:36:07.416 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.417 |-INFO  [Thread-81][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:36:07.418 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 17:36:07.420 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752485767420}] instanceId:[InstanceId{instanceId=*******:27292, stable=false}] @ namespace:[himall-report].
2025-07-14 17:36:07.445 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:36:07.448 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 17:36:07.472 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 17:36:07.639 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:36:07.643 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 17:36:07.643 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 17:36:07.643 |-INFO  [Thread-93][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 17:36:07.644 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 17:36:07.667 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 17:36:10.746 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 17:36:10.757 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 17:36:10.773 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 17:36:10.773 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 17:36:10.773 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 17:36:10.778 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 17:36:10.779 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 17:36:10.779 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 17:36:42.983 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 17:36:43.145 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 21580 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 17:36:43.151 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 17:36:43.152 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 17:36:43.579 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 17:36:43.579 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 17:36:47.779 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 17:36:47.845 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 17:36:48.272 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 17:36:48.950 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 17:36:48.955 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 17:36:49.228 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 17:36:49.228 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 17:36:49.228 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 17:36:49.228 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 17:36:52.132 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 17:36:53.915 |-INFO  [redisson-netty-1-4][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:36:55.531 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:36:56.156 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@445adbd7'
2025-07-14 17:36:56.650 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 17:36:56.689 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 17:36:56.810 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 17:36:56.833 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 17:36:56.850 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 17:36:56.869 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 17:36:56.889 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 17:36:56.917 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 17:36:56.938 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 17:36:56.964 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 17:36:56.984 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 17:36:56.997 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 17:36:57.013 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 17:36:57.026 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 17:36:57.040 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 17:36:57.052 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 17:36:57.065 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 17:36:57.091 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 17:36:57.094 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 17:36:57.099 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 17:36:58.631 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 17:36:59.539 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 17:37:06.792 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 17:37:15.357 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 17:37:16.431 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 17:37:20.882 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 17:37:20.897 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 17:37:22.846 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:21580, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:37:23.556 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752485842852}] - instanceId:[InstanceId{instanceId=*******:21580, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:37:23.693 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 17:37:23.695 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 17:37:23.695 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 17:37:23.695 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 17:37:29.232 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4c5a9ddc[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 17:37:29.232 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@b61c70e[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 17:37:29.232 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a2daa29[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 17:37:29.233 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e9f0660[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 17:37:29.233 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2633d812[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 17:37:29.233 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@23ef226d[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 17:37:29.233 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3484b8db[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 17:37:29.233 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@726e142d[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 17:37:29.233 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4aa5a13a[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 17:37:29.233 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@10031e49[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 17:37:29.701 |-INFO  [Thread-111][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 17:37:29.769 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 17:37:29.782 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 17:37:30.007 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 17:37:30.066 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 17:37:30.100 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 17:37:30.283 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 17:37:30.519 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 17:37:30.520 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 17:37:30.885 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 17:37:32.180 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 56.35 seconds (JVM running for 59.502)
2025-07-14 17:37:32.188 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 17:37:32.189 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 17:37:34.018 |-INFO  [RMI TCP Connection(5)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 17:37:34.125 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 17:37:34.126 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 17:37:34.127 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 17:39:51.645 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 17:39:51.645 |-INFO  [Thread-94][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:39:51.645 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:39:51.646 |-INFO  [Thread-94][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:39:51.646 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.645 |-WARN  [Thread-4][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.646 |-INFO  [Thread-95][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:39:51.647 |-WARN  [Thread-4][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 17:39:51.649 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752485991649}] instanceId:[InstanceId{instanceId=*******:21580, stable=false}] @ namespace:[himall-report].
2025-07-14 17:39:51.678 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:39:51.681 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 17:39:51.709 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 17:39:51.865 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:39:51.865 |-INFO  [Thread-111][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-14 17:39:52.171 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-14 17:39:52.172 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-14 17:39:52.172 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 17:39:52.172 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 17:39:52.172 |-INFO  [Thread-110][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 17:39:52.172 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 17:39:52.198 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 17:39:52.265 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 17:39:52.275 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 17:39:52.287 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 17:39:52.287 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 17:39:52.287 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 17:39:52.291 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 17:39:52.292 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 17:39:52.292 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 17:40:16.513 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 17:40:16.681 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 27304 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 17:40:16.686 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 17:40:16.686 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 17:40:17.095 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 17:40:17.095 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 17:40:21.352 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 17:40:21.433 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 17:40:21.803 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 17:40:23.316 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 17:40:23.320 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 17:40:23.650 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 17:40:23.650 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 17:40:23.650 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 17:40:23.650 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 17:40:26.514 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 17:40:28.281 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:40:29.820 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:40:30.521 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@537e52de'
2025-07-14 17:40:30.838 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 17:40:30.866 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 17:40:30.975 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 17:40:30.993 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 17:40:31.015 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 17:40:31.036 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 17:40:31.055 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 17:40:31.073 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 17:40:31.104 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 17:40:31.125 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 17:40:31.139 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 17:40:31.158 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 17:40:31.173 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 17:40:31.186 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 17:40:31.200 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 17:40:31.214 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 17:40:31.232 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 17:40:31.254 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 17:40:31.258 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 17:40:31.262 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 17:40:32.740 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 17:40:33.756 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 17:40:37.931 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 17:40:45.286 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 17:40:46.364 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 17:40:50.828 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 17:40:50.842 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 17:40:52.700 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:27304, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:40:53.369 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752486052706}] - instanceId:[InstanceId{instanceId=*******:27304, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:40:53.563 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 17:40:53.565 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 17:40:53.565 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 17:40:53.565 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@23885f6a[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5556813d[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@450897d[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@425b142e[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@23497000[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42f7523d[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42ec207f[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@580cb668[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 17:40:59.089 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@302d0419[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 17:40:59.090 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@48b3ce9b[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 17:40:59.594 |-INFO  [Thread-109][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 17:40:59.655 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 17:40:59.668 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 17:40:59.717 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 17:40:59.778 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 17:40:59.809 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 17:41:00.119 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 17:41:00.345 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 17:41:00.345 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 17:41:00.672 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 17:41:02.228 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 51.714 seconds (JVM running for 54.258)
2025-07-14 17:41:02.236 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 17:41:02.237 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 17:41:03.897 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 17:41:03.898 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 17:41:03.901 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 17:41:04.421 |-INFO  [RMI TCP Connection(4)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 17:44:02.613 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:44:02.614 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:44:02.614 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 17:44:02.614 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 17:44:02.614 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:44:02.614 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:44:02.614 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.614 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 17:44:02.614 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.614 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.614 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.614 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.614 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.614 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.615 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.615 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.615 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.615 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.615 |-INFO  [Thread-93][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:44:02.616 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 17:44:02.618 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752486242618}] instanceId:[InstanceId{instanceId=*******:27304, stable=false}] @ namespace:[himall-report].
2025-07-14 17:44:02.639 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:44:02.642 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 17:44:02.667 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 17:44:02.845 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:44:02.845 |-INFO  [Thread-109][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-14 17:44:03.162 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-14 17:44:03.162 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-14 17:44:03.162 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 17:44:03.163 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 17:44:03.163 |-INFO  [Thread-108][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 17:44:03.163 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 17:44:03.196 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 17:44:06.240 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 17:44:06.248 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 17:44:06.261 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 17:44:06.261 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 17:44:06.261 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 17:44:06.265 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 17:44:06.265 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 17:44:06.265 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 17:44:30.172 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 17:44:30.329 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 32788 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 17:44:30.333 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 17:44:30.334 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 17:44:30.805 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 17:44:30.805 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 17:44:35.443 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 17:44:35.510 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 17:44:35.856 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 17:44:36.504 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 17:44:36.508 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 17:44:36.813 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 17:44:36.814 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 17:44:36.814 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 17:44:36.814 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 17:44:39.447 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 17:44:41.247 |-INFO  [redisson-netty-1-4][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:44:43.655 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 17:44:44.360 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@468dbd07'
2025-07-14 17:44:44.804 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 17:44:44.831 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 17:44:44.962 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 17:44:44.985 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 17:44:45.005 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 17:44:45.024 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 17:44:45.042 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 17:44:45.062 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 17:44:45.081 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 17:44:45.107 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 17:44:45.127 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 17:44:45.154 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 17:44:45.177 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 17:44:45.213 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 17:44:45.229 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 17:44:45.251 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 17:44:45.276 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 17:44:45.298 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 17:44:45.303 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 17:44:45.307 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 17:44:47.057 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 17:44:48.262 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 17:44:52.820 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 17:44:58.882 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 17:45:00.032 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 17:45:03.611 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 17:45:03.626 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 17:45:05.006 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:32788, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:45:05.795 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752486305012}] - instanceId:[InstanceId{instanceId=*******:32788, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 17:45:06.020 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 17:45:06.023 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 17:45:06.023 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 17:45:06.023 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 17:45:10.926 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7aa5de8c[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@59253557[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@618dbfd4[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@14fda568[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@407858c5[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@30075210[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3cf828db[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@45571cc9[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@476bdad6[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 17:45:10.927 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4d7687f9[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 17:45:11.428 |-INFO  [Thread-101][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 17:45:11.491 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 17:45:11.506 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 17:45:11.737 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 17:45:11.805 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 17:45:11.830 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 17:45:11.976 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 17:45:12.236 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 17:45:12.237 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 17:45:12.562 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 17:45:13.739 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 50.138 seconds (JVM running for 52.841)
2025-07-14 17:45:13.746 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 17:45:13.748 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 17:45:15.677 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 17:45:15.679 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 17:45:15.683 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 17:45:16.151 |-INFO  [RMI TCP Connection(10)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 17:49:52.546 |-INFO  [Thread-85][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:49:52.546 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 17:49:52.547 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 17:49:52.547 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-85][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.547 |-INFO  [Thread-86][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 17:49:52.549 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 17:49:52.552 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752486592552}] instanceId:[InstanceId{instanceId=*******:32788, stable=false}] @ namespace:[himall-report].
2025-07-14 17:49:52.675 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:49:52.678 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 17:49:52.711 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 17:49:52.889 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 17:49:52.890 |-INFO  [Thread-101][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-14 17:49:53.185 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-14 17:49:53.185 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-14 17:49:53.185 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 17:49:53.186 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 17:49:53.186 |-INFO  [Thread-100][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 17:49:53.186 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 17:49:53.243 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 17:49:56.296 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 17:49:56.308 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 17:49:56.324 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 17:49:56.324 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 17:49:56.324 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 17:49:56.328 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 17:49:56.328 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 17:49:56.328 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 18:12:07.111 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 18:12:07.297 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 32004 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 18:12:07.306 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 18:12:07.307 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 18:12:07.863 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 18:12:07.863 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 18:12:13.624 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 18:12:13.703 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 18:12:14.610 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 18:12:15.563 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 18:12:15.568 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 18:12:15.892 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 18:12:15.892 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 18:12:15.892 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 18:12:15.892 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 18:12:19.420 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 18:12:21.898 |-INFO  [redisson-netty-1-8][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 18:12:23.929 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 18:12:24.656 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@44ad3c5e'
2025-07-14 18:12:25.015 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 18:12:25.042 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 18:12:25.172 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 18:12:25.193 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 18:12:25.211 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 18:12:25.240 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 18:12:25.267 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 18:12:25.290 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 18:12:25.307 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 18:12:25.329 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 18:12:25.361 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 18:12:25.412 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 18:12:25.447 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 18:12:25.471 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 18:12:25.491 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 18:12:25.512 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 18:12:25.542 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 18:12:25.570 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 18:12:25.573 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 18:12:25.577 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 18:12:27.562 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 18:12:28.659 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 18:12:35.090 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 18:12:44.510 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 18:12:45.822 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 18:12:50.959 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 18:12:50.977 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 18:12:53.152 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:32004, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 18:12:54.126 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752487973199}] - instanceId:[InstanceId{instanceId=*******:32004, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 18:12:54.329 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 18:12:54.332 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 18:12:54.332 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 18:12:54.333 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6ed238c2[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5c96d290[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e908a0b[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@35853c37[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4aee166d[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@20865ff2[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a7c593b[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@684aac7[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3c638904[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 18:13:00.563 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2e8828b5[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 18:13:01.162 |-INFO  [Thread-127][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 18:13:01.271 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 18:13:01.291 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 18:13:01.395 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 18:13:01.477 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 18:13:01.771 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 18:13:01.953 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 18:13:02.280 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 18:13:02.281 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 18:13:02.587 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 18:13:03.886 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 63.736 seconds (JVM running for 68.365)
2025-07-14 18:13:03.894 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 18:13:03.896 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 18:13:05.988 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 18:13:05.989 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 18:13:05.991 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 18:13:06.414 |-INFO  [RMI TCP Connection(8)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 18:15:36.309 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 18:15:36.309 |-INFO  [Thread-111][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 18:15:36.309 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 18:15:36.309 |-INFO  [Thread-111][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 18:15:36.309 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 18:15:36.309 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 18:15:36.309 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.310 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:15:36.311 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 18:15:36.313 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752488136313}] instanceId:[InstanceId{instanceId=*******:32004, stable=false}] @ namespace:[himall-report].
2025-07-14 18:15:36.337 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 18:15:36.341 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 18:15:36.375 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 18:15:36.584 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 18:15:36.585 |-INFO  [Thread-127][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-14 18:15:36.896 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-14 18:15:36.896 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-14 18:15:36.896 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 18:15:36.897 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 18:15:36.897 |-INFO  [Thread-126][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 18:15:36.897 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 18:15:36.927 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 18:15:39.997 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 18:15:40.010 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 18:15:40.026 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 18:15:40.027 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 18:15:40.027 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 18:15:40.031 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 18:15:40.031 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 18:15:40.031 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 18:18:15.777 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 18:18:15.978 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 29120 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 18:18:15.985 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 18:18:15.986 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 18:18:16.530 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 18:18:16.530 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 18:18:21.777 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 18:18:21.857 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 18:18:22.321 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 18:18:23.076 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 18:18:23.081 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 18:18:23.421 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 18:18:23.421 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 18:18:23.422 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 18:18:23.422 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 18:18:26.783 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 18:18:29.140 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 18:18:30.790 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 18:18:31.559 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@468dbd07'
2025-07-14 18:18:31.964 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 18:18:31.990 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 18:18:32.113 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 18:18:32.143 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 18:18:32.168 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 18:18:32.190 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 18:18:32.213 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 18:18:32.240 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 18:18:32.279 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 18:18:32.312 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 18:18:32.329 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 18:18:32.352 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 18:18:32.373 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 18:18:32.392 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 18:18:32.410 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 18:18:32.430 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 18:18:32.448 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 18:18:32.475 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 18:18:32.478 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 18:18:32.483 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 18:18:34.562 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 18:18:35.780 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 18:18:41.893 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 18:18:51.377 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 18:18:52.688 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 18:18:57.759 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 18:18:57.777 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 18:18:59.913 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:29120, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 18:19:00.691 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752488339921}] - instanceId:[InstanceId{instanceId=*******:29120, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 18:19:00.861 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 18:19:00.864 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 18:19:00.865 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 18:19:00.865 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 18:19:06.914 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@402c3dce[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 18:19:06.914 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5d0e1f13[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 18:19:06.914 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@47e9542a[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 18:19:06.915 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4af2f2ae[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 18:19:06.915 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6b916473[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 18:19:06.915 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@464a1895[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 18:19:06.915 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4b0f5c72[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 18:19:06.915 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1635b03f[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 18:19:06.915 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7667fc76[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 18:19:06.915 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@60ffdc9f[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 18:19:07.561 |-INFO  [Thread-129][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 18:19:07.744 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 18:19:07.777 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 18:19:08.064 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 18:19:08.141 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 18:19:08.173 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 18:19:08.383 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 18:19:08.664 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 18:19:08.665 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 18:19:08.954 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8080 register finished
2025-07-14 18:19:10.858 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 61.846 seconds (JVM running for 66.209)
2025-07-14 18:19:10.865 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 18:19:10.870 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 18:19:12.010 |-INFO  [RMI TCP Connection(3)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 18:19:12.725 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 18:19:12.726 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 18:19:12.731 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 18:21:54.856 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 18:21:54.856 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 18:21:54.856 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 18:21:54.856 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 18:21:54.856 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 18:21:54.856 |-INFO  [Thread-111][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-111][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.857 |-INFO  [Thread-112][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 18:21:54.858 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 18:21:54.860 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752488514860}] instanceId:[InstanceId{instanceId=*******:29120, stable=false}] @ namespace:[himall-report].
2025-07-14 18:21:54.985 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 18:21:54.989 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 18:21:55.024 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 18:21:55.223 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 18:21:55.224 |-INFO  [Thread-129][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-14 18:21:55.560 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-14 18:21:55.560 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-14 18:21:55.560 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 18:21:55.560 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 18:21:55.561 |-INFO  [Thread-128][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 18:21:55.561 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 18:21:55.589 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 18:21:58.639 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 18:21:58.653 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 18:21:58.671 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 18:21:58.671 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 18:21:58.671 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 18:21:58.675 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 18:21:58.676 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 18:21:58.676 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 18:22:20.778 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 18:22:20.954 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 26752 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 18:22:20.958 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 18:22:20.959 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 18:22:21.414 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-14 18:22:21.415 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 18:22:25.926 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 18:22:25.991 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 18:22:26.368 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 18:22:26.978 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 18:22:26.987 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 18:22:27.253 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 18:22:27.253 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 18:22:27.253 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 18:22:27.253 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 18:22:30.175 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 18:22:32.248 |-INFO  [redisson-netty-1-10][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 18:22:33.822 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-14 18:22:34.506 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@3c48bbf3'
2025-07-14 18:22:34.853 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-14 18:22:34.875 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-14 18:22:35.002 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-14 18:22:35.024 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-14 18:22:35.042 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-14 18:22:35.060 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-14 18:22:35.079 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-14 18:22:35.103 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-14 18:22:35.122 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-14 18:22:35.143 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-14 18:22:35.162 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-14 18:22:35.181 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-14 18:22:35.207 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-14 18:22:35.226 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-14 18:22:35.265 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-14 18:22:35.284 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-14 18:22:35.310 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-14 18:22:35.334 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-14 18:22:35.339 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-14 18:22:35.342 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-14 18:22:36.885 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 18:22:37.994 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-14 18:22:42.411 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-14 18:22:48.462 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 18:22:49.686 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-14 18:22:53.019 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 18:22:53.032 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 18:22:54.479 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:26752, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 18:22:55.153 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=124, lastTimeStamp=1752488574487}] - instanceId:[InstanceId{instanceId=*******:26752, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-14 18:22:55.306 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-14 18:22:55.308 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 18:22:55.308 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 18:22:55.308 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-14 18:23:00.142 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7bd804ed[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-14 18:23:00.142 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@13fd5aaa[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-14 18:23:00.142 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4d55b63d[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-14 18:23:00.142 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5c5432d9[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-14 18:23:00.142 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6790aad2[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-14 18:23:00.142 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1e418480[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-14 18:23:00.143 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7e452d2b[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-14 18:23:00.143 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@37a1ac0[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-14 18:23:00.143 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@615ef647[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-14 18:23:00.143 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@73a91b68[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-14 18:23:00.629 |-INFO  [Thread-106][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-14 18:23:00.698 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-14 18:23:00.713 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-14 18:23:00.761 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 18:23:01.028 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 18:23:01.063 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 18:23:01.247 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 18:23:01.494 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 18:23:01.495 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 18:23:02.017 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8085 register finished
2025-07-14 18:23:03.226 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 48.716 seconds (JVM running for 51.683)
2025-07-14 18:23:03.235 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 18:23:03.238 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 18:23:04.334 |-INFO  [RMI TCP Connection(1)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 18:23:05.037 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-14 18:23:05.039 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 18:23:05.040 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-14 18:23:11.024 |-DEBUG [XNIO-1 task-1][e1b36a2c5d6f09ba] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:GET start
2025-07-14 18:23:11.118 |-ERROR [XNIO-1 task-1][e1b36a2c5d6f09ba] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:260) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:499) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1266) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1048) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 18:23:11.391 |-DEBUG [XNIO-1 task-1][e1b36a2c5d6f09ba] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:380ms input:
{}
2025-07-14 18:23:16.544 |-DEBUG [XNIO-1 task-1][349ce92415cace00] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 18:23:20.220 |-DEBUG [XNIO-1 task-1][349ce92415cace00] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:3676ms input:
{}
2025-07-14 18:23:25.947 |-DEBUG [XNIO-1 task-1][1f0978289ad96474] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 18:23:27.018 |-DEBUG [XNIO-1 task-1][1f0978289ad96474] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:1071ms input:
{"operationShopId":0,"sortList":[],"pageSize":10,"pageNo":1,"shopId":162}
2025-07-14 18:27:02.802 |-DEBUG [XNIO-1 task-1][22e680f874f50c39] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/summary method:POST start
2025-07-14 18:27:02.865 |-INFO  [XNIO-1 task-1][22e680f874f50c39] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visits 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:27:02.974 |-INFO  [XNIO-1 task-1][22e680f874f50c39] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visits success. 请求结果. response={"data":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":null}},"code":0,"message":null}
2025-07-14 18:27:02.976 |-DEBUG [XNIO-1 task-1][22e680f874f50c39] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/summary cost:174ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:27:02.988 |-DEBUG [XNIO-1 task-1][0cfa7875af0912de] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/visitsEcharts method:POST start
2025-07-14 18:27:02.991 |-INFO  [XNIO-1 task-1][0cfa7875af0912de] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visitsEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:27:03.039 |-INFO  [XNIO-1 task-1][0cfa7875af0912de] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visitsEcharts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"被访问商品数","data":[0]},{"name":"动销商品数","data":[0]}]},"code":0,"message":null}
2025-07-14 18:27:03.042 |-DEBUG [XNIO-1 task-1][0cfa7875af0912de] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/visitsEcharts cost:53ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:27:03.095 |-DEBUG [XNIO-1 task-1][8bd05be7d23fb103] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/categoryEcharts method:POST start
2025-07-14 18:27:03.100 |-INFO  [XNIO-1 task-1][8bd05be7d23fb103] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| categoryEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:27:03.124 |-INFO  [XNIO-1 task-1][8bd05be7d23fb103] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| categoryEcharts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"类目销量","data":[]},{"name":"类目销售额","data":[]}]},"code":0,"message":null}
2025-07-14 18:27:03.125 |-DEBUG [XNIO-1 task-1][8bd05be7d23fb103] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/categoryEcharts cost:30ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:27:09.094 |-DEBUG [XNIO-1 task-1][442d818576904473] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 18:27:09.152 |-DEBUG [XNIO-1 task-1][442d818576904473] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:58ms input:
{"operationShopId":0,"sortList":[],"pageSize":10,"pageNo":1,"shopId":162}
2025-07-14 18:28:27.689 |-DEBUG [XNIO-1 task-1][6365e3a968c30cdf] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/query method:POST start
2025-07-14 18:28:27.760 |-DEBUG [XNIO-1 task-1][6365e3a968c30cdf] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/query cost:71ms input:
{"operationShopId":0,"sortList":[],"pageSize":10,"pageNo":1,"shopId":162}
2025-07-14 18:28:35.389 |-DEBUG [XNIO-1 task-1][df31667e9ca7f7bb] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/custom/record/query method:POST start
2025-07-14 18:28:35.471 |-DEBUG [XNIO-1 task-1][df31667e9ca7f7bb] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/custom/record/query cost:83ms input:
{"operationShopId":0,"pageSize":10,"pageNo":1,"shopId":162}
2025-07-14 18:28:44.812 |-DEBUG [XNIO-1 task-2][d7aa085ff8f2c2bd] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/echarts method:POST start
2025-07-14 18:28:44.824 |-DEBUG [XNIO-1 task-1][1415a484cd9ebbd7] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/summary method:POST start
2025-07-14 18:28:44.842 |-INFO  [XNIO-1 task-2][d7aa085ff8f2c2bd] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| echarts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:44.842 |-INFO  [XNIO-1 task-1][1415a484cd9ebbd7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| summary 请求入参. request={"range":"MONTH","shopId":162,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:44.935 |-INFO  [XNIO-1 task-2][d7aa085ff8f2c2bd] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-05","2025-07-06","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-12","2025-07-13","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-19","2025-07-20","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-26","2025-07-27","2025-07-28","2025-07-29","2025-07-30"]},"series":[{"name":"支付金额","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付件数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"订单-访客转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付-下单转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付-访客转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}]},"code":0,"message":null}
2025-07-14 18:28:44.938 |-DEBUG [XNIO-1 task-2][d7aa085ff8f2c2bd] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/echarts cost:126ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:44.949 |-DEBUG [XNIO-1 task-2][fbd1dd8dae1470d3] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/province method:POST start
2025-07-14 18:28:44.953 |-INFO  [XNIO-1 task-2][fbd1dd8dae1470d3] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| province 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:44.975 |-INFO  [XNIO-1 task-1][1415a484cd9ebbd7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| summary success. 请求结果. response={"data":{"visitsCount":null,"visitsUsers":null,"orderAmount":null,"orderOrders":null,"orderUsers":null,"paymentAmount":null,"paymentQuantity":null,"paymentOrders":null,"paymentUsers":null,"unitPrice":null,"orderVisitsRate":null,"paymentOrderRate":null,"paymentVisitsRate":null,"prev":{"visitsCount":null,"visitsUsers":null,"orderAmount":null,"orderOrders":null,"orderUsers":null,"paymentAmount":null,"paymentQuantity":null,"paymentOrders":null,"paymentUsers":null,"unitPrice":null,"orderVisitsRate":null,"paymentOrderRate":null,"paymentVisitsRate":null,"prev":null}},"code":0,"message":null}
2025-07-14 18:28:44.976 |-DEBUG [XNIO-1 task-1][1415a484cd9ebbd7] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/summary cost:152ms input:
{"range":"MONTH","shopId":162,"day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:44.995 |-INFO  [XNIO-1 task-2][fbd1dd8dae1470d3] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| province success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"支付金额","data":[]},{"name":"支付订单笔数","data":[]},{"name":"支付人数","data":[]}]},"code":0,"message":null}
2025-07-14 18:28:44.998 |-DEBUG [XNIO-1 task-2][fbd1dd8dae1470d3] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/province cost:48ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:46.373 |-DEBUG [XNIO-1 task-2][4ef5a847c904963a] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/summary method:POST start
2025-07-14 18:28:46.374 |-INFO  [XNIO-1 task-2][4ef5a847c904963a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| summary 请求入参. request={"range":"MONTH","shopId":162,"day":1751299200000,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:46.499 |-INFO  [XNIO-1 task-2][4ef5a847c904963a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| summary success. 请求结果. response={"data":{"visitsCount":null,"visitsUsers":null,"orderAmount":null,"orderOrders":null,"orderUsers":null,"paymentAmount":null,"paymentQuantity":null,"paymentOrders":null,"paymentUsers":null,"unitPrice":null,"orderVisitsRate":null,"paymentOrderRate":null,"paymentVisitsRate":null,"prev":{"visitsCount":null,"visitsUsers":null,"orderAmount":null,"orderOrders":null,"orderUsers":null,"paymentAmount":null,"paymentQuantity":null,"paymentOrders":null,"paymentUsers":null,"unitPrice":null,"orderVisitsRate":null,"paymentOrderRate":null,"paymentVisitsRate":null,"prev":null}},"code":0,"message":null}
2025-07-14 18:28:46.501 |-DEBUG [XNIO-1 task-2][4ef5a847c904963a] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/summary cost:128ms input:
{"range":"MONTH","shopId":162,"day":"2025-07-01","start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:46.607 |-DEBUG [XNIO-1 task-2][9d981f552f998995] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/echarts method:POST start
2025-07-14 18:28:46.608 |-INFO  [XNIO-1 task-2][9d981f552f998995] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| echarts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:46.623 |-DEBUG [XNIO-1 task-1][b7be488995056072] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/trade/province method:POST start
2025-07-14 18:28:46.624 |-INFO  [XNIO-1 task-1][b7be488995056072] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| province 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:46.649 |-INFO  [XNIO-1 task-2][9d981f552f998995] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| echarts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-05","2025-07-06","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-12","2025-07-13","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-19","2025-07-20","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-26","2025-07-27","2025-07-28","2025-07-29","2025-07-30"]},"series":[{"name":"支付金额","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付人数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付件数","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"订单-访客转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付-下单转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"支付-访客转化率","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}]},"code":0,"message":null}
2025-07-14 18:28:46.651 |-DEBUG [XNIO-1 task-2][9d981f552f998995] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/echarts cost:44ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:46.704 |-INFO  [XNIO-1 task-1][b7be488995056072] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| province success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"支付金额","data":[]},{"name":"支付订单笔数","data":[]},{"name":"支付人数","data":[]}]},"code":0,"message":null}
2025-07-14 18:28:46.705 |-DEBUG [XNIO-1 task-1][b7be488995056072] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/trade/province cost:82ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:48.155 |-DEBUG [XNIO-1 task-1][1d1d8306667a7658] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/categoryEcharts method:POST start
2025-07-14 18:28:48.156 |-INFO  [XNIO-1 task-1][1d1d8306667a7658] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| categoryEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:48.216 |-DEBUG [XNIO-1 task-2][d26085ad322de25c] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/summary method:POST start
2025-07-14 18:28:48.222 |-INFO  [XNIO-1 task-2][d26085ad322de25c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visits 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:48.222 |-DEBUG [XNIO-1 task-3][fbdba14c05527f6a] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/visitsEcharts method:POST start
2025-07-14 18:28:48.224 |-INFO  [XNIO-1 task-3][fbdba14c05527f6a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visitsEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:48.233 |-INFO  [XNIO-1 task-1][1d1d8306667a7658] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| categoryEcharts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"类目销量","data":[]},{"name":"类目销售额","data":[]}]},"code":0,"message":null}
2025-07-14 18:28:48.236 |-DEBUG [XNIO-1 task-1][1d1d8306667a7658] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/categoryEcharts cost:81ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:48.319 |-INFO  [XNIO-1 task-3][fbdba14c05527f6a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visitsEcharts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"被访问商品数","data":[0]},{"name":"动销商品数","data":[0]}]},"code":0,"message":null}
2025-07-14 18:28:48.320 |-DEBUG [XNIO-1 task-3][fbdba14c05527f6a] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/visitsEcharts cost:98ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:48.336 |-INFO  [XNIO-1 task-2][d26085ad322de25c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visits success. 请求结果. response={"data":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":null}},"code":0,"message":null}
2025-07-14 18:28:48.337 |-DEBUG [XNIO-1 task-2][d26085ad322de25c] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/summary cost:121ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:49.338 |-DEBUG [XNIO-1 task-2][715c37a9332fecd3] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/summary method:POST start
2025-07-14 18:28:49.341 |-INFO  [XNIO-1 task-2][715c37a9332fecd3] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visits 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:49.466 |-INFO  [XNIO-1 task-2][715c37a9332fecd3] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visits success. 请求结果. response={"data":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":null}},"code":0,"message":null}
2025-07-14 18:28:49.467 |-DEBUG [XNIO-1 task-2][715c37a9332fecd3] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/summary cost:129ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:49.715 |-DEBUG [XNIO-1 task-2][bacd066a8196b12e] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/visitsEcharts method:POST start
2025-07-14 18:28:49.716 |-DEBUG [XNIO-1 task-3][b5c6eb5e5216efaf] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/categoryEcharts method:POST start
2025-07-14 18:28:49.718 |-INFO  [XNIO-1 task-2][bacd066a8196b12e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visitsEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:49.718 |-INFO  [XNIO-1 task-3][b5c6eb5e5216efaf] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| categoryEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-14 18:28:49.760 |-INFO  [XNIO-1 task-2][bacd066a8196b12e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visitsEcharts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"被访问商品数","data":[0]},{"name":"动销商品数","data":[0]}]},"code":0,"message":null}
2025-07-14 18:28:49.760 |-DEBUG [XNIO-1 task-2][bacd066a8196b12e] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/visitsEcharts cost:45ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-14 18:28:49.817 |-INFO  [XNIO-1 task-3][b5c6eb5e5216efaf] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| categoryEcharts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"类目销量","data":[]},{"name":"类目销售额","data":[]}]},"code":0,"message":null}
2025-07-14 18:28:49.820 |-DEBUG [XNIO-1 task-3][b5c6eb5e5216efaf] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/categoryEcharts cost:104ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
