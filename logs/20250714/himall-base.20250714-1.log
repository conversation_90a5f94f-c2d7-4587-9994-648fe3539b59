2025-07-14 08:55:06.979 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 08:55:07.211 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 25328 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 08:55:07.211 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 08:55:07.212 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-14 08:55:07.903 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-14 08:55:07.903 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 08:55:13.930 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-14 08:55:13.932 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-14 08:55:14.070 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-14 08:55:19.351 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 08:55:19.458 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 08:55:20.056 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 08:55:20.978 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 08:55:20.982 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 08:55:21.297 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 08:55:21.298 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 08:55:21.298 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 08:55:21.298 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 08:55:24.217 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@676c3745'
2025-07-14 08:55:24.579 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-14 08:55:24.691 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-14 08:55:24.703 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-14 08:55:24.714 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-14 08:55:24.727 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-14 08:55:24.737 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-14 08:55:24.747 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-14 08:55:24.766 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-14 08:55:24.772 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-14 08:55:24.794 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-14 08:55:24.801 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-14 08:55:24.810 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-14 08:55:24.839 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-14 08:55:24.849 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-14 08:55:24.857 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-14 08:55:24.865 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-14 08:55:24.874 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-14 08:55:24.881 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-14 08:55:24.900 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-14 08:55:24.917 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-14 08:55:24.936 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-14 08:55:24.955 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-14 08:55:24.975 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-14 08:55:24.993 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-14 08:55:25.016 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-14 08:55:25.034 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-14 08:55:25.050 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-14 08:55:25.067 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-14 08:55:25.083 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-14 08:55:25.103 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-14 08:55:25.128 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-14 08:55:25.158 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-14 08:55:25.181 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-14 08:55:25.220 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-14 08:55:25.247 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-14 08:55:25.267 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-14 08:55:25.291 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-14 08:55:25.327 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-14 08:55:25.353 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-14 08:55:25.376 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-14 08:55:25.400 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-14 08:55:25.427 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-14 08:55:25.433 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-14 08:55:25.441 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-14 08:55:25.448 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-14 08:55:25.454 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-14 08:55:25.466 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-14 08:55:25.490 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-14 08:55:25.512 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-14 08:55:25.532 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-14 08:55:25.555 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-14 08:55:25.581 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-14 08:55:25.614 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-14 08:55:25.639 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-14 08:55:25.665 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-14 08:55:25.684 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-14 08:55:25.701 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-14 08:55:25.720 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-14 08:55:25.742 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-14 08:55:25.776 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-14 08:55:25.804 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-14 08:55:25.822 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-14 08:55:25.847 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-14 08:55:25.893 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-14 08:55:25.917 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-14 08:55:25.944 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-14 08:55:25.965 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-14 08:55:25.970 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-14 08:55:25.985 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-14 08:55:25.993 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [150] -| Use localhost address 
2025-07-14 08:55:25.994 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [155] -| Get DESKTOP-GBHOUCA/******* network interface 
2025-07-14 08:55:26.872 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [159] -| Get network interface info: name:net0 (Sangfor aTrust VNIC)
2025-07-14 08:55:26.900 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [103] -| Initialization Sequence datacenterId:15 workerId:18
2025-07-14 08:55:27.071 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-14 08:55:33.485 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-14 08:55:42.942 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 08:55:44.794 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-14 08:55:45.998 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-14 08:55:46.260 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:55:49.618 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:56:04.746 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:56:04.747 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-14 08:56:06.357 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 08:56:08.154 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-14 08:56:08.155 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-14 08:56:08.653 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-14 08:56:08.868 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinMpAppId(String), weixinMpAppSecret(String)
2025-07-14 08:56:09.236 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-14 08:56:09.299 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-14 08:56:10.072 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-14 08:56:10.073 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinAppletId(String), weixinAppletSecret(String)
2025-07-14 08:56:10.101 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-14 08:56:10.101 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wxb16c375a8b35e176,wxAppSecret:8984e92a1e5bfe49772ae3846f746e44
2025-07-14 08:56:16.091 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 08:56:16.569 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-14 08:56:16.969 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-14 08:56:17.056 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:56:20.106 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:56:30.912 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:56:30.913 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-14 08:56:30.921 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:56:33.411 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:56:41.578 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:56:41.578 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-14 08:56:41.586 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:56:44.965 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:56:53.433 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:56:53.434 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-14 08:56:53.443 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:56:56.595 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:57:07.973 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:57:07.974 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-14 08:57:09.064 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:57:12.166 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:57:19.799 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:57:19.799 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-14 08:57:19.817 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:57:22.259 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:57:29.291 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:57:29.292 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-14 08:57:29.308 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:57:31.837 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:57:39.459 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:57:39.459 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-14 08:57:39.468 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:57:43.721 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:57:51.006 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:57:51.006 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-14 08:57:51.014 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:57:53.356 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:58:00.437 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:58:00.438 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-14 08:58:00.447 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:58:02.754 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:58:09.020 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:58:09.020 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-14 08:58:09.028 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:58:11.430 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:58:18.850 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:58:18.850 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-14 08:58:18.860 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:58:21.329 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:58:29.156 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:58:29.157 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-14 08:58:29.178 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:58:31.465 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:58:37.917 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:58:37.917 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-14 08:58:37.928 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 08:58:39.583 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 08:58:45.527 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 08:58:45.529 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-14 08:58:52.110 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 08:58:52.128 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 08:58:54.315 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:25328, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-14 08:58:55.239 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=412, lastTimeStamp=1752454734322}] - instanceId:[InstanceId{instanceId=*******:25328, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-14 08:58:55.386 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-14 08:58:55.389 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 08:58:55.389 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 08:58:55.389 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-14 08:58:58.217 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-14 08:59:01.750 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2b4ab7[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-14 08:59:01.751 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@58721f69[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-14 08:59:01.751 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5e91142c[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-14 08:59:01.751 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@72a3b822[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-14 08:59:01.840 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@28872a33[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-14 08:59:01.840 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6046e11d[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-14 08:59:01.841 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3620b94d[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-14 08:59:07.662 |-ERROR [Thread-466][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 08:59:07.830 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 08:59:07.913 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 08:59:07.950 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 08:59:08.126 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 08:59:08.321 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 251.063 seconds (JVM running for 255.855)
2025-07-14 08:59:08.708 |-INFO  [task-11][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 08:59:08.708 |-INFO  [task-11][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 08:59:09.067 |-INFO  [task-11][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-14 08:59:11.170 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-14 08:59:11.174 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-14 08:59:11.178 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-14 08:59:11.291 |-INFO  [task-10][79d828fab9ed970c] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 08:59:11.293 |-INFO  [task-10][79d828fab9ed970c] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-14 08:59:11.620 |-ERROR [task-11][d5097dcfb1bf4f84] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-14 08:59:11.649 |-INFO  [task-11][d5097dcfb1bf4f84] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 08:59:13.811 |-INFO  [RMI TCP Connection(21)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 08:59:14.722 |-WARN  [RMI TCP Connection(17)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-14 09:07:41.954 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 09:07:41.954 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 09:07:41.954 |-INFO  [Thread-440][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-440][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.955 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 09:07:41.955 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:07:41.954 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 09:07:41.957 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 09:07:41.961 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=412, lastTimeStamp=1752455261961}] instanceId:[InstanceId{instanceId=*******:25328, stable=false}] @ namespace:[himall-base].
2025-07-14 09:07:41.996 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 09:08:27.066 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 09:08:27.097 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 09:08:27.105 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 09:08:27.108 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 09:08:27.108 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 09:08:27.109 |-INFO  [Thread-465][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 09:08:27.109 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 09:08:27.140 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 09:08:27.171 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.172 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.172 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.172 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.172 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.172 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.172 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.173 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.173 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.173 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.173 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.173 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.173 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.173 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:27.195 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:08:30.294 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 09:08:30.303 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 09:08:30.316 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 09:08:30.316 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 09:08:30.316 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 09:08:30.320 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 09:08:30.320 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 09:08:30.320 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 09:17:48.346 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 09:17:48.611 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 11520 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 09:17:48.612 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 09:17:48.612 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 09:17:49.105 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-14 09:17:49.105 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 09:17:52.190 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-14 09:17:58.308 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 09:17:58.383 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 09:17:58.958 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 09:18:00.412 |-ERROR [main][] -  com.zaxxer.hikari.pool.HikariPool [594] -| master - Exception during pool initialization.
java.sql.SQLSyntaxErrorException: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) [spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585) [spring-context-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.sankuai.shangou.seashop.base.StartApp.main(StartApp.java:31) [classes/:?]
2025-07-14 09:18:00.449 |-WARN  [main][] -  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext [599] -| Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
2025-07-14 09:18:00.593 |-ERROR [main][] -  org.springframework.boot.SpringApplication [818] -| Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.sankuai.shangou.seashop.base.StartApp.main(StartApp.java:31) [classes/:?]
Caused by: java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:257) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:794) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:628) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:794) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException(HikariPool.java:596) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:582) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: java.sql.SQLSyntaxErrorException: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
2025-07-14 09:18:00.599 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 09:18:00.599 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 09:18:00.599 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 09:18:00.600 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 09:20:24.315 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 09:20:24.480 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 29152 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 09:20:24.480 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 09:20:24.481 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 09:20:24.955 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-14 09:20:24.955 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 09:20:27.479 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-14 09:20:31.503 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 09:20:31.574 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 09:20:31.972 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 09:20:33.407 |-ERROR [main][] -  com.zaxxer.hikari.pool.HikariPool [594] -| master - Exception during pool initialization.
java.sql.SQLSyntaxErrorException: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) [spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585) [spring-context-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.sankuai.shangou.seashop.base.StartApp.main(StartApp.java:31) [classes/:?]
2025-07-14 09:20:33.428 |-WARN  [main][] -  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext [599] -| Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
2025-07-14 09:20:33.523 |-ERROR [main][] -  org.springframework.boot.SpringApplication [818] -| Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.sankuai.shangou.seashop.base.StartApp.main(StartApp.java:31) [classes/:?]
Caused by: java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:257) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:794) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:628) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:794) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException(HikariPool.java:596) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:582) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: java.sql.SQLSyntaxErrorException: Access denied for user 'himall'@'%' to database 'histore_base_bbc'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
2025-07-14 09:20:33.529 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 09:20:33.529 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 09:20:33.530 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 09:20:33.530 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 09:23:15.124 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 09:23:15.263 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 29344 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 09:23:15.263 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 09:23:15.263 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 09:23:15.671 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-14 09:23:15.671 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 09:23:17.853 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-14 09:23:21.164 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 09:23:21.225 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 09:23:21.605 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 09:23:22.304 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 09:23:22.307 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 09:23:22.516 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 09:23:22.517 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 09:23:22.517 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 09:23:22.517 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 09:23:27.018 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-14 09:23:30.768 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-14 09:23:36.955 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 09:23:38.092 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-14 09:23:39.365 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-14 09:23:39.587 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:23:41.195 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:23:46.070 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:23:46.070 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-14 09:23:47.134 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 09:23:48.560 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-14 09:23:49.465 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-07-14 09:23:54.256 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 09:23:54.640 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-14 09:23:54.973 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-14 09:23:55.042 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:23:56.569 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:01.381 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:01.382 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-14 09:24:01.389 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:02.896 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:07.540 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:07.541 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-14 09:24:07.550 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:09.051 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:13.695 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:13.695 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-14 09:24:13.705 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:15.270 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:19.990 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:19.990 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-14 09:24:20.952 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:22.723 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:27.822 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:27.822 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-14 09:24:27.837 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:29.429 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:34.412 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:34.412 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-14 09:24:34.421 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:36.037 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:41.014 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:41.014 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-14 09:24:41.022 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:42.608 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:47.567 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:47.567 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-14 09:24:47.576 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:49.193 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:24:54.720 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:24:54.720 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-14 09:24:54.729 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:24:56.362 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:25:01.468 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:25:01.468 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-14 09:25:01.476 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:25:02.993 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:25:08.128 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:25:08.128 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-14 09:25:08.136 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:25:09.754 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:25:14.719 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:25:14.719 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-14 09:25:14.727 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:25:16.347 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:25:21.231 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:25:21.231 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-14 09:25:21.242 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:25:22.859 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:25:27.894 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:25:27.894 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-14 09:25:34.334 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 09:25:34.352 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 09:25:35.737 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:29344, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-14 09:25:36.632 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=413, lastTimeStamp=1752456335745}] - instanceId:[InstanceId{instanceId=*******:29344, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-14 09:25:36.846 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-14 09:25:36.849 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 09:25:36.849 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 09:25:36.850 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-14 09:25:39.895 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-14 09:25:43.647 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6edfb42d[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-14 09:25:43.648 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@147c3b73[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-14 09:25:43.648 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5767053[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-14 09:25:43.648 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@307e1c7b[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-14 09:25:43.696 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2393b885[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-14 09:25:43.696 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@56c1a827[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-14 09:25:43.696 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6083be44[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-14 09:25:49.537 |-ERROR [Thread-325][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 09:25:49.710 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 09:25:49.791 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 09:25:49.830 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 09:25:50.024 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 09:25:50.224 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 161.091 seconds (JVM running for 164.425)
2025-07-14 09:25:50.606 |-INFO  [task-16][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 09:25:50.606 |-INFO  [task-16][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 09:25:50.943 |-INFO  [task-16][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-14 09:25:52.962 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-14 09:25:53.059 |-INFO  [task-9][a0731fe18288d14b] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 09:25:53.061 |-INFO  [task-9][a0731fe18288d14b] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-14 09:25:53.495 |-ERROR [task-11][cac5b9e63915c890] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-14 09:25:53.565 |-INFO  [task-11][cac5b9e63915c890] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 09:25:55.059 |-INFO  [RMI TCP Connection(10)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 09:25:56.163 |-WARN  [RMI TCP Connection(7)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-14 09:36:09.048 |-INFO  [XNIO-1 task-1][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 09:36:09.084 |-INFO  [XNIO-1 task-1][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 09:36:09.247 |-INFO  [XNIO-1 task-1][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 09:36:09.807 |-INFO  [XNIO-1 task-1][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 09:36:10.079 |-INFO  [XNIO-1 task-1][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 09:36:10.175 |-INFO  [XNIO-1 task-1][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 09:36:10.175 |-INFO  [XNIO-1 task-1][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 09:36:10.267 |-INFO  [XNIO-1 task-1][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 09:36:10.297 |-INFO  [XNIO-1 task-2][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 09:36:10.398 |-INFO  [XNIO-1 task-2][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 09:36:11.111 |-INFO  [XNIO-1 task-2][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 09:36:11.197 |-INFO  [XNIO-1 task-2][cb4f78ef70c45d4f] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 09:36:48.767 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 09:36:48.767 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 09:36:48.839 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 09:36:48.880 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 09:36:48.920 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 09:36:48.925 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 09:36:48.925 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 09:36:48.960 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 09:36:48.971 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 09:36:49.040 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 09:36:49.076 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 09:36:49.110 |-INFO  [XNIO-1 task-2][3cbe07fc0b41e984] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 09:39:34.756 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 09:39:34.756 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 09:39:34.905 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 09:39:35.034 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 09:39:35.281 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 09:39:35.401 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 09:39:35.402 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 09:39:35.439 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 09:39:35.448 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 09:39:35.517 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 09:39:35.602 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 09:39:35.639 |-INFO  [XNIO-1 task-2][ae5cd57b378f1ea8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 09:49:28.673 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 09:49:28.673 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 09:49:28.673 |-INFO  [Thread-295][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 09:49:28.673 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 09:49:28.673 |-INFO  [Thread-295][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 09:49:28.673 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.673 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.673 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.673 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.673 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.674 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.674 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.674 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.674 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.674 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 09:49:28.674 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.674 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.674 |-INFO  [Thread-296][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 09:49:28.674 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 09:49:28.675 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 09:49:28.684 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=413, lastTimeStamp=1752457768684}] instanceId:[InstanceId{instanceId=*******:29344, stable=false}] @ namespace:[himall-base].
2025-07-14 09:49:28.718 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 09:50:13.812 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 09:50:13.822 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 09:50:13.831 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 09:50:13.835 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 09:50:13.835 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 09:50:13.835 |-INFO  [Thread-324][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 09:50:13.836 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 09:50:13.863 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 09:50:13.893 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.893 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.893 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.893 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.894 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.894 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.894 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.894 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.894 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.894 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.894 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.895 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.895 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.895 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:13.915 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:50:17.009 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 09:50:17.022 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 09:50:17.041 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 09:50:17.041 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 09:50:17.041 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 09:50:17.046 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 09:50:17.046 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 09:50:17.046 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-14 09:50:41.092 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 09:50:41.255 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 27620 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 09:50:41.255 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 09:50:41.256 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 09:50:41.792 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-14 09:50:41.793 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 09:50:44.685 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-14 09:50:49.242 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 09:50:49.330 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 09:50:49.897 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 09:50:50.607 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 09:50:50.612 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 09:50:50.879 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 09:50:50.879 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 09:50:50.880 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 09:50:50.880 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 09:50:56.801 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-14 09:51:03.024 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-14 09:51:15.373 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 09:51:16.953 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-14 09:51:18.144 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-14 09:51:18.403 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:51:21.288 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:51:30.068 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:51:30.069 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-14 09:51:31.277 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 09:51:32.891 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-14 09:51:33.857 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-07-14 09:51:39.269 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 09:51:39.724 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-14 09:51:40.120 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-14 09:51:40.205 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:51:42.146 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:51:53.118 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:51:53.119 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-14 09:51:53.127 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:51:56.457 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:52:05.652 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:52:05.652 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-14 09:52:05.660 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:52:08.451 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:52:18.840 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:52:18.840 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-14 09:52:18.852 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:52:23.759 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:52:35.910 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:52:35.910 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-14 09:52:37.006 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:52:39.527 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:52:47.453 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:52:47.453 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-14 09:52:47.470 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:52:50.001 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:52:57.879 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:52:57.880 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-14 09:52:57.887 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:53:00.355 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:53:07.856 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:53:07.857 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-14 09:53:07.865 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:53:10.288 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:53:17.980 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:53:17.980 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-14 09:53:17.988 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:53:20.633 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:53:27.275 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:53:27.275 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-14 09:53:27.285 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:53:29.774 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:53:37.159 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:53:37.159 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-14 09:53:37.166 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:53:39.645 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:53:46.163 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:53:46.163 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-14 09:53:46.179 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:53:49.523 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:53:56.739 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:53:56.739 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-14 09:53:56.747 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:53:58.373 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:54:04.256 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:54:04.256 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-14 09:54:04.264 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 09:54:05.846 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 09:54:11.913 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 09:54:11.914 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-14 09:54:17.869 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 09:54:17.884 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 09:54:19.282 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:27620, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-14 09:54:20.142 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=412, lastTimeStamp=1752458059289}] - instanceId:[InstanceId{instanceId=*******:27620, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-14 09:54:20.301 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-14 09:54:20.304 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 09:54:20.304 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 09:54:20.304 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-14 09:54:23.103 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-14 09:54:26.368 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2eea76bc[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-14 09:54:26.368 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1629947a[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-14 09:54:26.368 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2e6e0c7e[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-14 09:54:26.368 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@194910bc[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-14 09:54:26.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@64ebcabf[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-14 09:54:26.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e3b081c[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-14 09:54:26.405 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6068fe59[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-14 09:54:32.219 |-ERROR [Thread-452][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 09:54:32.394 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 09:54:32.480 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 09:54:32.517 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 09:54:32.720 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 09:54:32.944 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 239.931 seconds (JVM running for 243.723)
2025-07-14 09:54:33.329 |-INFO  [task-14][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 09:54:33.329 |-INFO  [task-14][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 09:54:33.670 |-INFO  [task-14][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-14 09:54:35.724 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-14 09:54:35.827 |-INFO  [task-16][23f7a8a1a92d3a74] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 09:54:35.828 |-INFO  [task-16][23f7a8a1a92d3a74] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-14 09:54:36.175 |-ERROR [task-15][0ab62c38419455ef] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-14 09:54:36.205 |-INFO  [task-15][0ab62c38419455ef] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 09:54:36.581 |-INFO  [RMI TCP Connection(11)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 09:54:41.089 |-WARN  [RMI TCP Connection(15)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-14 09:57:15.621 |-INFO  [XNIO-1 task-1][e99271be9ee8e607] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 09:57:15.654 |-INFO  [XNIO-1 task-1][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 09:57:15.804 |-INFO  [XNIO-1 task-1][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 09:57:16.370 |-INFO  [XNIO-1 task-1][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 09:57:16.647 |-INFO  [XNIO-1 task-1][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 09:57:16.739 |-INFO  [XNIO-1 task-1][e99271be9ee8e607] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 09:57:16.740 |-INFO  [XNIO-1 task-1][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 09:57:16.825 |-INFO  [XNIO-1 task-1][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 09:57:16.852 |-INFO  [XNIO-1 task-2][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 09:57:16.937 |-INFO  [XNIO-1 task-2][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 09:57:17.589 |-INFO  [XNIO-1 task-2][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 09:57:17.648 |-INFO  [XNIO-1 task-2][e99271be9ee8e607] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:12:45.807 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 10:12:45.808 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 10:12:45.874 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 10:12:45.998 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 10:12:46.042 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 10:12:46.086 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 10:12:46.086 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 10:12:46.112 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 10:12:46.239 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 10:12:46.500 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:12:46.822 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 10:12:46.854 |-INFO  [XNIO-1 task-2][6dae73cfabacced7] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:13:26.794 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 10:13:26.795 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 10:13:27.080 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 10:13:27.237 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 10:13:27.392 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 10:13:27.395 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 10:13:27.396 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 10:13:27.556 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 10:13:27.561 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 10:13:27.886 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:13:27.936 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 10:13:28.104 |-INFO  [XNIO-1 task-2][924fb85befcdf549] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:14:34.464 |-INFO  [Thread-423][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 10:14:34.464 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 10:14:34.464 |-INFO  [Thread-423][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 10:14:34.464 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 10:14:34.464 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.464 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.465 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 10:14:34.468 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 10:14:34.472 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=412, lastTimeStamp=1752459274472}] instanceId:[InstanceId{instanceId=*******:27620, stable=false}] @ namespace:[himall-base].
2025-07-14 10:14:34.509 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 10:15:16.571 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 10:15:16.580 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 10:15:16.592 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 10:15:16.597 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 10:15:16.597 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 10:15:16.598 |-INFO  [Thread-451][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 10:15:16.598 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 10:15:16.626 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 10:15:16.668 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.668 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.669 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.669 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.669 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.669 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.669 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.669 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.669 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.670 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.670 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.670 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.670 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.670 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:16.704 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:15:50.208 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-14 10:15:50.372 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 24672 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-14 10:15:50.372 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-14 10:15:50.373 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-14 10:15:50.845 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-14 10:15:50.845 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-14 10:15:53.369 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-14 10:15:57.545 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-14 10:15:57.619 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-14 10:15:58.129 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-14 10:15:58.874 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-14 10:15:58.879 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-14 10:15:59.146 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-14 10:15:59.147 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-14 10:15:59.147 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-14 10:15:59.147 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-14 10:16:05.247 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-14 10:16:11.042 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-14 10:16:23.170 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-14 10:16:24.915 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-14 10:16:26.126 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-14 10:16:26.396 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:16:28.857 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:16:38.619 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:16:38.619 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-14 10:16:39.783 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-14 10:16:41.401 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-14 10:16:42.392 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-07-14 10:16:47.845 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-14 10:16:48.256 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-14 10:16:48.676 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-14 10:16:48.761 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:16:55.434 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:17:05.560 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:17:05.561 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-14 10:17:05.569 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:17:08.007 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:17:17.757 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:17:17.758 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-14 10:17:17.768 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:17:20.766 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:17:29.462 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:17:29.462 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-14 10:17:29.470 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:17:34.106 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:17:41.549 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:17:41.549 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-14 10:17:42.812 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:17:45.374 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:17:52.959 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:17:52.959 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-14 10:17:52.979 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:17:55.459 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:18:02.592 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:18:02.592 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-14 10:18:02.600 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:18:04.549 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:18:12.407 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:18:12.407 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-14 10:18:12.418 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:18:14.940 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:18:22.499 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:18:22.499 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-14 10:18:22.509 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:18:25.094 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:18:31.439 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:18:31.439 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-14 10:18:31.454 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:18:33.887 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:18:39.313 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:18:39.313 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-14 10:18:39.321 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:18:41.174 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:18:46.166 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:18:46.166 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-14 10:18:46.174 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:18:47.914 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:18:54.280 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:18:54.281 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-14 10:18:54.290 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:18:55.819 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:19:01.161 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:19:01.161 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-14 10:19:01.169 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-14 10:19:04.075 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-14 10:19:09.914 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 10:19:09.914 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-14 10:19:16.817 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-14 10:19:16.840 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-14 10:19:18.358 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:24672, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-14 10:19:19.297 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=413, lastTimeStamp=1752459558366}] - instanceId:[InstanceId{instanceId=*******:24672, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-14 10:19:19.479 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-14 10:19:19.482 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-14 10:19:19.482 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-14 10:19:19.482 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-14 10:19:22.527 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-14 10:19:26.699 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5af2f934[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-14 10:19:26.699 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@66d8d02e[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-14 10:19:26.699 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@571802f[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-14 10:19:26.700 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@523685f6[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-14 10:19:26.749 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1f40fddd[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-14 10:19:26.750 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3fb917bb[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-14 10:19:26.750 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3640989b[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-14 10:19:33.154 |-ERROR [Thread-452][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 10:19:33.374 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-14 10:19:33.460 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-14 10:19:33.500 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-14 10:19:33.698 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-14 10:19:33.963 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [61] -| Started StartApp in 233.024 seconds (JVM running for 236.983)
2025-07-14 10:19:34.369 |-INFO  [task-9][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-14 10:19:34.370 |-INFO  [task-9][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-14 10:19:34.737 |-INFO  [task-9][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-14 10:19:36.817 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.StartApp [32] -| 服务启动成功！
2025-07-14 10:19:36.924 |-INFO  [task-16][65376dfb1f080f08] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-14 10:19:36.926 |-INFO  [task-16][65376dfb1f080f08] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-14 10:19:37.241 |-ERROR [task-13][4a81f9f64a0f3578] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-14 10:19:37.269 |-INFO  [task-13][4a81f9f64a0f3578] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-14 10:19:38.432 |-INFO  [RMI TCP Connection(8)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 10:19:41.529 |-WARN  [RMI TCP Connection(7)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-14 10:19:52.421 |-INFO  [XNIO-1 task-1][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 10:19:52.458 |-INFO  [XNIO-1 task-1][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 10:19:52.617 |-INFO  [XNIO-1 task-1][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 10:19:53.240 |-INFO  [XNIO-1 task-1][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 10:19:53.536 |-INFO  [XNIO-1 task-1][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 10:19:53.634 |-INFO  [XNIO-1 task-1][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 10:19:53.635 |-INFO  [XNIO-1 task-1][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 10:19:53.722 |-INFO  [XNIO-1 task-1][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 10:19:53.750 |-INFO  [XNIO-1 task-2][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 10:19:53.839 |-INFO  [XNIO-1 task-2][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:19:54.500 |-INFO  [XNIO-1 task-2][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 10:19:54.568 |-INFO  [XNIO-1 task-2][7e2fbacba3b39270] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:31:32.625 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 10:31:32.626 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 10:31:32.688 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 10:31:32.749 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 10:31:32.786 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 10:31:32.793 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 10:31:32.793 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 10:31:32.823 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 10:31:32.839 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 10:31:32.898 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:31:32.927 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 10:31:32.956 |-INFO  [XNIO-1 task-2][32114026cb9217b6] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:37:18.657 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[2394]}
2025-07-14 10:37:18.658 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[2394],"operationUserId":null,"operationShopId":0}
2025-07-14 10:37:18.718 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1749721570000,"updateTime":1749721570000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-14 10:37:18.762 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryShopsByIds 请求入参. request={"shopIds":[162],"operationUserId":null,"operationShopId":0}
2025-07-14 10:37:18.800 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryShopsByIds success. 请求结果. response={"data":[{"id":162,"gradeId":0,"shopName":"官方自营店_1","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":4,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}],"code":0,"message":null}
2025-07-14 10:37:18.804 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.user.account.thrift.impl.ShippingAddressQueryController [39] -| 【收货地址】获取默认收货地址, 请求参数=136662
2025-07-14 10:37:18.804 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getDefaultAddress 请求入参. request={}
2025-07-14 10:37:18.834 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getDefaultAddress success. 请求结果. response={"data":{"defaultAddress":null},"code":0,"message":null}
2025-07-14 10:37:18.838 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryRestrictedRegion 请求入参. request={"freightTemplateId":2394,"operationUserId":null,"operationShopId":0}
2025-07-14 10:37:18.896 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryRestrictedRegion success. 请求结果. response={"data":{"id":2394,"name":"水电费","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"restrictedRegionIds":[],"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 10:37:18.925 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFavoriteProductStatus 请求入参. request={"userId":136662,"productId":16591,"operationUserId":null,"operationShopId":0}
2025-07-14 10:37:18.956 |-INFO  [XNIO-1 task-2][7742c313ee2a9f9e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFavoriteProductStatus success. 请求结果. response={"data":{"userId":136662,"productId":16591,"favoriteStatus":false,"operationUserId":null,"operationShopId":0},"code":0,"message":null}
2025-07-14 11:14:35.588 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-14 11:14:35.590 |-INFO  [Thread-421][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-14 11:14:35.588 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-14 11:14:35.590 |-INFO  [Thread-421][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.590 |-INFO  [Thread-422][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-14 11:14:35.596 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=413, lastTimeStamp=1752462875596}] instanceId:[InstanceId{instanceId=*******:24672, stable=false}] @ namespace:[himall-base].
2025-07-14 11:14:35.624 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 11:14:35.646 |-ERROR [MachineIdGuarder][] -  me.ahoo.cosid.machine.DefaultMachineIdGuarder [104] -| Guard Failed:[java.lang.InterruptedException; nested exception is org.redisson.client.RedisException: java.lang.InterruptedException]!
org.springframework.dao.InvalidDataAccessApiUsageException: java.lang.InterruptedException; nested exception is org.redisson.client.RedisException: java.lang.InterruptedException
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:52) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:204) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:199) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:370) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:736) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1966) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:2123) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at sun.reflect.GeneratedMethodAccessor133.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at com.sun.proxy.$Proxy325.evalSha(Unknown Source) ~[?:?]
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:178) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:345) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133) ~[cosid-spring-redis-1.19.3.jar:?]
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92) ~[cosid-core-1.19.3.jar:?]
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101) ~[cosid-core-1.19.3.jar:?]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_121]
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308) [?:1.8.0_121]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java) [?:1.8.0_121]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [?:1.8.0_121]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: org.redisson.client.RedisException: java.lang.InterruptedException
	at org.redisson.command.CommandAsyncService.get(CommandAsyncService.java:166) ~[redisson-3.27.1.jar:3.27.1]
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:197) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	... 28 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:347) ~[?:1.8.0_121]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1895) ~[?:1.8.0_121]
	at org.redisson.command.CommandAsyncService.get(CommandAsyncService.java:162) ~[redisson-3.27.1.jar:3.27.1]
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:197) ~[redisson-spring-data-27-3.27.1.jar:3.27.1]
	... 28 more
2025-07-14 11:15:20.744 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-14 11:15:20.758 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-14 11:15:20.768 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-14 11:15:20.773 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-14 11:15:20.773 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-14 11:15:20.773 |-INFO  [Thread-451][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-14 11:15:20.774 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-14 11:15:20.806 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-14 11:15:20.858 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.859 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.859 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.859 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.859 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.859 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.859 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.859 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.859 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.860 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.860 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.860 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.860 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.860 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:20.887 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-14 11:15:23.985 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-14 11:15:23.994 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-14 11:15:23.999 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-14 11:15:23.999 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-14 11:15:23.999 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-14 11:15:24.003 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-14 11:15:24.003 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-14 11:15:24.003 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
