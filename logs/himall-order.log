2025-08-01 17:36:06.612 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-08-01 17:36:06.761 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [55] -| Starting OrderApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 8092 (E:\work\himallWork\himall-order\seashop-order-server\target\classes started by Admin in E:\work\himallWork)
2025-08-01 17:36:06.761 |-DEBUG [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-08-01 17:36:06.762 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-08-01 17:36:07.156 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-order.yml, group=1.0.0] success
2025-08-01 17:36:07.156 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-08-01 17:36:11.625 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-08-01 17:36:11.627 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-08-01 17:36:11.709 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-08-01 17:36:13.140 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderCommentExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 17:36:13.141 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 17:36:13.142 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'pendingSettlementOrderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 17:36:13.142 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'platAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PlatAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 17:36:13.142 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'shopAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.ShopAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 17:36:15.205 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 17:36:15.270 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-08-01 17:36:15.663 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-08-01 17:36:16.334 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-08-01 17:36:16.340 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-08-01 17:36:16.663 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-08-01 17:36:16.664 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-08-01 17:36:16.664 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-08-01 17:36:16.664 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 17:36:21.599 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:36:24.220 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:36:34.911 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:36:34.912 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderDelayCheckListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-01 17:36:34.921 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:36:38.189 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:36:43.766 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:36:43.766 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderFailureListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-01 17:36:45.429 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-08-01 17:36:46.119 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-08-01 17:36:47.621 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-08-01 17:36:48.948 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-08-01 17:36:49.867 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:36:53.348 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:37:00.649 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:37:00.649 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-01 17:37:00.657 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:37:02.236 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:37:08.649 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:37:08.649 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-08-01 17:37:09.162 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-08-01 17:37:12.630 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-order) init on namesrv 124.71.221.178:9876
2025-08-01 17:37:17.834 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:37:19.744 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:37:24.954 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:37:24.954 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderPayNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-08-01 17:37:25.500 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:37:27.187 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:37:32.302 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:37:32.302 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-08-01 17:37:32.421 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:37:34.150 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:37:39.318 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:37:39.318 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-08-01 17:37:39.325 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:37:40.987 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:37:46.383 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:37:46.383 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-08-01 17:37:46.549 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:37:48.229 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:37:53.328 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:37:53.328 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-08-01 17:37:53.336 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:37:55.095 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:37:59.739 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:37:59.740 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeForRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-08-01 17:37:59.787 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:38:01.195 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:38:06.494 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:38:06.495 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-08-01 17:38:06.812 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.core.statemachine.config.OrderStateMachineConfiguration [127] -| @startuml
UNDER_PAY --> CLOSED : CANCEL_ORDER
UNDER_PAY --> PAYING : INITIATE_PAY
UNDER_PAY --> CLOSED : CLOSE
UNDER_RECEIVE --> UNDER_RECEIVE : DELAY_RECEIVE
UNDER_RECEIVE --> FINISHED : CONFIRM_RECEIVE
PAYING --> UNDER_SEND : PAY_NOTIFY
PAYING --> UNDER_PAY : CANCEL_PAY
UNDER_SEND --> UNDER_RECEIVE : DELIVERY
@enduml
2025-08-01 17:38:06.911 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:38:08.753 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:38:15.373 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:38:15.373 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-08-01 17:38:15.511 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:38:17.337 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:38:23.998 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:38:23.999 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-08-01 17:38:24.007 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:38:25.975 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:38:31.018 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:38:31.018 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:refundNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-08-01 17:38:33.699 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-08-01 17:38:33.699 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-08-01 17:38:33.789 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:38:35.225 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class java.lang.String
2025-08-01 17:38:40.638 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:38:40.638 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderReverseExceptionListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-08-01 17:38:41.275 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-08-01 17:38:41.370 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:38:43.112 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:38:48.007 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:38:48.008 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositPayStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_16
2025-08-01 17:38:48.049 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:38:49.451 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:38:55.080 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:38:55.080 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositReverseStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_17
2025-08-01 17:39:02.308 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-08-01 17:39:02.326 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-08-01 17:39:03.827 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:8092, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-08-01 17:39:04.704 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=366, lastTimeStamp=1754041143834}] - instanceId:[InstanceId{instanceId=*******:8092, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-08-01 17:39:04.881 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-order.__share__].
2025-08-01 17:39:04.884 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-08-01 17:39:04.884 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-08-01 17:39:04.885 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-order.__share__] jobSize:[0].
2025-08-01 17:39:08.468 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-08-01 17:39:12.403 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshProductCommentEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@43ab3f26[class com.sankuai.shangou.seashop.order.core.task.CommentTask#refreshProductCommentEs]
2025-08-01 17:39:12.403 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCommentTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@22d45306[class com.sankuai.shangou.seashop.order.core.task.CommentTask#autoComment]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:ExecuteMqErrorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2943c93d[class com.sankuai.shangou.seashop.order.core.task.MqErrorDataTask#executeMqErrorDataTask]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildOrderEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@18a59ef8[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildOrderEsDataTask]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildRefundEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@26dc0c5[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildRefundEsDataTask]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCloseTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@221ffed[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoClose]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindWaitDeliveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@351cc4b5[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindWaitDelivery]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@17adeb0[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundStatusTask]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderRefund, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6dfd4db2[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderRefund]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundRecordStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@60ae0977[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundRecordStatusTask]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoFinishTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@33c440bb[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoFinish]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindOrderNotPayTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@641c872d[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindNotPay]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderBill, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@630bd6c1[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderBill]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderItem, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@737e8e66[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderItem]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@622ec92a[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrder]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoConfirmArrival, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@620b0fad[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoConfirmArrival]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundCloseWhenDeliveryExpireTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5f8b60f3[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundCloseWhenDeliveryExpire]
2025-08-01 17:39:12.404 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoAuditTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5881d8da[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoAudit]
2025-08-01 17:39:12.406 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:payReverseStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4cf22d79[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#payReverseStatusQueryTask]
2025-08-01 17:39:12.406 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:unpaidOrderPayStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@76be352d[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#unpaidOrderPayStatusQueryTask]
2025-08-01 17:39:12.434 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@72364ff1[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask$$EnhancerBySpringCGLIB$$24bdd4c5#orderSettlement]
2025-08-01 17:39:12.434 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSplitting, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@24270e65[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask$$EnhancerBySpringCGLIB$$24bdd4c5#orderSplitting]
2025-08-01 17:39:18.119 |-ERROR [Thread-365][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-08-01 17:39:18.277 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-08-01 17:39:18.359 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-08-01 17:39:18.402 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-08-01 17:39:18.616 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-08-01 17:39:18.948 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 17:39:18.949 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 17:39:19.318 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-order *******:8083 register finished
2025-08-01 17:39:20.505 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [61] -| Started OrderApplication in 200.69 seconds (JVM running for 205.601)
2025-08-01 17:39:20.517 |-INFO  [main][] -  adaPayInitConfig [35] -| 汇付支付初始化开始
2025-08-01 17:39:21.922 |-INFO  [main][] -  adaPayInitConfig [38] -| 汇付配置信息:AdaPayConfigModel(izOpen=true, appId=app_39b69a1c-d9ad-4e30-bb29-d80833b86885, apiKey=api_live_54054387-f98b-4f13-ad20-b6ebcbd07da4, apiMockKey=api_test_000fc218-bd56-4ea8-9609-8439687ad2bc, rsaPrivateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIYS3BfoZ2+g0eY21IZwvllVccKEcSwzrJUwLnWJ8vFvSM5mU5aPkMkhO1m5GR2aDJEz16hBuEK1ezzP73R5Sh6Qne2kXHWgCO6vKRWJ/PeAm8xBKddHKO3Xdg9MW1tVQoHA2pPV1ru2KOXT93YWOIKUlWTFpnelpgrIZLjZME2XAgMBAAECgYATy30LWpjK9meHIdlG8CZqch8VpRBAgnCcpjx1xiREWTXao2j79b5es7VbjeSTZkcsuQbCJNHbp4fGdrzX6YBzw/xR2gdnE/A4lRfcgvJ62wFBIbq4MZYevBmBX5Ng2AuxbdEbJ87Kd+zpFFHb6x0aB4aJjUSx5EpQaoiRuhBRIQJBAMWcgi44hvJQNnx9fVZZWs0ZyUDgf9hTVUEcJ+LyaUeNOXcsrP13NJ98PMDtSyhiVEsAmwugZDc2uWRbcbYUyBMCQQCtsE5m+UEH6ALg2lQUVyfuxORWb2OYXSeA3JBJN9RvzBo2WfQnzJO3Vs6BV2qcvqgUcKv+b4DwLPHODPmZyxztAkBvADwL1IrQ4AfLI/5cm7KqlPp8a97EWAMCoNsy2vISVBzceYbulaBEmdfSkzhthdZNjxiIjl7cuOuomMkl+0RrAkEAgEpEbszWmtdlIN5C0k9aAIPPwIRABS9xWT4RGPOy5uzTw6eHrsntpbLpjyGZbrNohMiAUcvcagpYhICS8GTVNQJBAMWPBEHATBX/Ka/5d/WC7SmIZ96d24NDJQDwlo0ufbZAp791HGoujq/w+SAySoQfA3VnVQyIkaqQlKou7Q5MS5A=, rsaProductKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB, deviceId=hishop1905, izDebug=true, prodMode=true)
2025-08-01 17:39:21.922 |-INFO  [main][] -  adaPayInitConfig [39] -| 汇付当前回调地址为:https://himall.cce.35hiw.com/himall-pay/payCallBack/adaPayCallback
2025-08-01 17:39:21.935 |-INFO  [main][] -  adaPayInitConfig [54] -| 汇付支付初始化完成
2025-08-01 17:39:24.263 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-08-01 17:39:24.265 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-order.yml, group=1.0.0
2025-08-01 17:39:24.270 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [37] -| 服务启动成功！
2025-08-01 17:39:24.314 |-INFO  [RMI TCP Connection(28)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 17:39:25.903 |-WARN  [RMI TCP Connection(26)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-08-01 17:53:00.045 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-08-01 17:53:00.045 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-08-01 17:53:00.045 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-08-01 17:53:00.045 |-INFO  [Thread-337][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-08-01 17:53:00.046 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-337][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.046 |-INFO  [Thread-338][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.050 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-08-01 17:53:00.053 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=366, lastTimeStamp=1754041980053}] instanceId:[InstanceId{instanceId=*******:8092, stable=false}] @ namespace:[himall-order].
2025-08-01 17:53:00.079 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
