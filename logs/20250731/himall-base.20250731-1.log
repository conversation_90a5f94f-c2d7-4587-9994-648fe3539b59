2025-07-31 11:21:56.459 |-WARN  [Thread-11][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-31 11:21:56.434 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-31 11:21:56.434 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-31 11:21:56.434 |-INFO  [Thread-450][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-31 11:21:56.542 |-INFO  [Thread-450][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:21:56.543 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:21:56.567 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.568 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.579 |-WARN  [Thread-11][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-31 11:21:56.614 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-31 11:21:56.568 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.620 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.621 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.621 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.621 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.621 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.621 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.689 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.703 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.714 |-INFO  [Thread-451][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:56.782 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=443, lastTimeStamp=1753932116781}] instanceId:[InstanceId{instanceId=2.0.0.1:38484, stable=false}] @ namespace:[himall-base].
2025-07-31 11:21:56.849 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:22:31.810 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-31 11:22:31.825 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 11:22:31.839 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:22:31.845 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-31 11:22:31.845 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-31 11:22:31.845 |-INFO  [Thread-487][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-31 11:22:31.846 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-31 11:22:31.879 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-31 11:22:31.921 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.922 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:31.985 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:35.180 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-31 11:22:35.190 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-31 11:22:35.194 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-31 11:22:35.194 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-31 11:22:35.194 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-31 11:22:35.198 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-31 11:22:35.198 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-31 11:22:35.198 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
