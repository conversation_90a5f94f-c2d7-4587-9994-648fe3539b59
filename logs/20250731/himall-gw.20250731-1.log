2025-07-31 11:21:57.029 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-31 11:21:57.120 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=459, lastTimeStamp=1753932117120}] instanceId:[InstanceId{instanceId=2.0.0.1:18848, stable=false}] @ namespace:[himall-gw].
2025-07-31 11:21:57.029 |-INFO  [Thread-212][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-31 11:21:57.029 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-31 11:21:56.998 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-31 11:21:57.173 |-INFO  [Thread-212][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:21:57.173 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:21:57.173 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.173 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.173 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.173 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.173 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.174 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.174 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.174 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.174 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.174 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.174 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.174 |-INFO  [Thread-213][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:57.208 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-31 11:21:57.246 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:21:57.306 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-31 11:22:03.442 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-31 11:22:03.467 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 11:22:05.295 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:22:05.296 |-INFO  [Thread-238][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-31 11:22:05.953 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='chengpei_himall-gw', registryValue='http://2.0.0.1:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-31 11:22:05.954 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-31 11:22:05.955 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-31 11:22:05.956 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-31 11:22:05.956 |-INFO  [Thread-237][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-31 11:22:05.958 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-31 11:22:06.183 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-31 11:22:07.179 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:07.181 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 11:22:07.181 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
