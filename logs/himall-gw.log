2025-08-01 17:27:02.558 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-08-01 17:27:02.730 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [55] -| Starting GatewayApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 37456 (E:\work\himallWork\himall-gw\seashop-gw-server\target\classes started by Admin in E:\work\himallWork)
2025-08-01 17:27:02.737 |-DEBUG [main][] -  com.sankuai.shangou.seashop.GatewayApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-08-01 17:27:02.737 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-08-01 17:27:03.277 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-gw.yml, group=1.0.0] success
2025-08-01 17:27:03.277 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-08-01 17:27:06.348 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-08-01 17:27:11.036 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 17:27:11.138 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-08-01 17:27:13.197 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-08-01 17:27:15.285 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-08-01 17:27:17.150 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-08-01 17:27:22.711 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-08-01 17:27:28.771 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-gw) init on namesrv 124.71.221.178:9876
2025-08-01 17:27:37.540 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:27:40.085 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:27:47.829 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:27:47.829 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:MExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-01 17:27:47.841 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:27:50.458 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:28:00.206 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:28:00.206 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:sendMessageListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-01 17:28:07.926 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-08-01 17:28:12.196 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:28:15.028 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:28:24.724 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:28:24.725 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:mallExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-01 17:28:28.689 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-08-01 17:28:28.980 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-08-01 17:28:39.855 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-08-01 17:28:39.876 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-08-01 17:28:42.081 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:37456, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-08-01 17:28:43.123 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=460, lastTimeStamp=1754040522090}] - instanceId:[InstanceId{instanceId=*******:37456, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-08-01 17:28:43.306 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-gw.__share__].
2025-08-01 17:28:43.310 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-08-01 17:28:43.310 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-08-01 17:28:43.310 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-gw.__share__] jobSize:[0].
2025-08-01 17:32:48.246 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-08-01 17:32:48.367 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [55] -| Starting GatewayApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 7544 (E:\work\himallWork\himall-gw\seashop-gw-server\target\classes started by Admin in E:\work\himallWork)
2025-08-01 17:32:48.371 |-DEBUG [main][] -  com.sankuai.shangou.seashop.GatewayApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-08-01 17:32:48.372 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-08-01 17:32:48.773 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-gw.yml, group=1.0.0] success
2025-08-01 17:32:48.773 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-08-01 17:32:52.651 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-08-01 17:32:55.878 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 17:32:55.935 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-08-01 17:32:56.976 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-08-01 17:32:58.446 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-08-01 17:33:00.126 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-08-01 17:33:04.029 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-08-01 17:33:07.114 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-gw) init on namesrv 124.71.221.178:9876
2025-08-01 17:33:11.786 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:33:13.188 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:33:17.664 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:33:17.664 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:MExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-01 17:33:17.669 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:33:19.015 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:33:22.890 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:33:22.891 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:sendMessageListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-01 17:33:27.011 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-08-01 17:33:28.603 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-08-01 17:33:29.850 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-08-01 17:33:33.924 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:33:33.924 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:mallExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-01 17:33:36.143 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-08-01 17:33:36.314 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-08-01 17:33:40.799 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-08-01 17:33:40.813 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-08-01 17:33:41.877 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:7544, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-08-01 17:33:42.600 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=461, lastTimeStamp=1754040821884}] - instanceId:[InstanceId{instanceId=*******:7544, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-08-01 17:33:42.734 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-gw.__share__].
2025-08-01 17:33:42.736 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-08-01 17:33:42.736 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-08-01 17:33:42.737 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-gw.__share__] jobSize:[0].
2025-08-01 17:33:47.512 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-08-01 17:33:50.036 |-INFO  [Thread-150][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-08-01 17:33:50.186 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-08-01 17:33:50.255 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-08-01 17:33:50.286 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-08-01 17:33:50.450 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-08-01 17:33:50.632 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [61] -| Started GatewayApplication in 68.551 seconds (JVM running for 72.117)
2025-08-01 17:33:50.648 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [40] -| 服务启动成功！
2025-08-01 17:33:50.885 |-INFO  [task-4][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 17:33:50.885 |-INFO  [task-4][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 17:33:50.982 |-INFO  [task-8][9b0939ae8ca851bd] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-08-01 17:33:50.983 |-INFO  [task-8][9b0939ae8ca851bd] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-gw.yml, group=1.0.0
2025-08-01 17:33:51.334 |-INFO  [RMI TCP Connection(1)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 17:33:51.341 |-INFO  [task-4][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-gw *******:8081 register finished
2025-08-01 17:33:56.161 |-WARN  [RMI TCP Connection(4)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-08-01 17:47:49.920 |-INFO  [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-08-01 17:47:50.153 |-INFO  [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[1],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-08-01 17:47:50.155 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[1],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport"]}
2025-08-01 17:47:50.178 |-INFO  [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=A工厂
2025-08-01 17:47:50.229 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-08-01 17:47:50.230 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 59
2025-08-01 17:47:50.230 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-08-01 17:47:50.230 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-08-01 17:47:50.230 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162,"categoryName":"A工厂"}
2025-08-01 17:47:50.230 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (59-byte body)
2025-08-01 17:47:56.652 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- HTTP/1.1 200 OK (6421ms)
2025-08-01 17:47:56.653 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] connection: keep-alive
2025-08-01 17:47:56.653 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] content-type: application/json
2025-08-01 17:47:56.653 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] date: Fri, 01 Aug 2025 09:47:56 GMT
2025-08-01 17:47:56.653 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] traceid: 4418870aa2cf0c13
2025-08-01 17:47:56.653 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] transfer-encoding: chunked
2025-08-01 17:47:56.654 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Origin
2025-08-01 17:47:56.654 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Method
2025-08-01 17:47:56.654 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Headers
2025-08-01 17:47:56.654 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-08-01 17:47:56.657 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null,"success":true}
2025-08-01 17:47:56.657 |-DEBUG [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END HTTP (1616-byte body)
2025-08-01 17:47:56.673 |-INFO  [XNIO-1 task-1][4418870aa2cf0c13] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null}
2025-08-01 17:48:10.935 |-INFO  [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-08-01 17:48:10.988 |-INFO  [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[1],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-08-01 17:48:10.988 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[1],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport"]}
2025-08-01 17:48:10.988 |-INFO  [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=
2025-08-01 17:48:10.989 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-08-01 17:48:10.989 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 52
2025-08-01 17:48:10.990 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-08-01 17:48:10.990 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-08-01 17:48:10.990 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162,"categoryName":""}
2025-08-01 17:48:10.990 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (52-byte body)
2025-08-01 17:48:17.324 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- HTTP/1.1 200 OK (6333ms)
2025-08-01 17:48:17.324 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] connection: keep-alive
2025-08-01 17:48:17.324 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] content-type: application/json
2025-08-01 17:48:17.326 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] date: Fri, 01 Aug 2025 09:48:17 GMT
2025-08-01 17:48:17.326 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] traceid: 3461a3f813c88ad8
2025-08-01 17:48:17.326 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] transfer-encoding: chunked
2025-08-01 17:48:17.326 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Origin
2025-08-01 17:48:17.326 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Method
2025-08-01 17:48:17.326 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Headers
2025-08-01 17:48:17.327 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-08-01 17:48:17.327 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"},{\"id\":2629,\"parentCategoryId\":0,\"name\":\"测试类目A\",\"fullIds\":[2629],\"children\":[{\"id\":2630,\"parentCategoryId\":2629,\"name\":\"测试类目A-1\",\"fullIds\":[2629,2630],\"children\":[{\"id\":2631,\"parentCategoryId\":2630,\"name\":\"测试类目A-1-1\",\"fullIds\":[2629,2630,2631],\"depth\":3,\"fullCategoryName\":\"测试类目A,测试类目A-1,测试类目A-1-1\"}],\"depth\":2,\"fullCategoryName\":\"测试类目A,测试类目A-1\"}],\"depth\":1,\"fullCategoryName\":\"测试类目A\"}]"},"code":0,"message":null,"success":true}
2025-08-01 17:48:17.328 |-DEBUG [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END HTTP (21611-byte body)
2025-08-01 17:48:17.330 |-INFO  [XNIO-1 task-1][3461a3f813c88ad8] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"},{\"id\":2496,\"parentCategoryId\":0,\"name\":\"B工厂\",\"fullIds\":[2496],\"children\":[{\"id\":2499,\"parentCategoryId\":2496,\"name\":\"冰激凌\",\"fullIds\":[2496,2499],\"children\":[{\"id\":2502,\"parentCategoryId\":2499,\"name\":\"须尽欢\",\"fullIds\":[2496,2499,2502],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,须尽欢\"},{\"id\":2503,\"parentCategoryId\":2499,\"name\":\"巧乐兹\",\"fullIds\":[2496,2499,2503],\"depth\":3,\"fullCategoryName\":\"B工厂,冰激凌,巧乐兹\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,冰激凌\"},{\"id\":2500,\"parentCategoryId\":2496,\"name\":\"饮料\",\"fullIds\":[2496,2500],\"children\":[{\"id\":2510,\"parentCategoryId\":2500,\"name\":\"功能型饮料\",\"fullIds\":[2496,2500,2510],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,功能型饮料\"},{\"id\":2512,\"parentCategoryId\":2500,\"name\":\"果蔬汁\",\"fullIds\":[2496,2500,2512],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,果蔬汁\"},{\"id\":2513,\"parentCategoryId\":2500,\"name\":\"茶饮\",\"fullIds\":[2496,2500,2513],\"depth\":3,\"fullCategoryName\":\"B工厂,饮料,茶饮\"}],\"depth\":2,\"fullCategoryName\":\"B工厂,饮料\"}],\"depth\":1,\"fullCategoryName\":\"B工厂\"},{\"id\":2580,\"parentCategoryId\":0,\"name\":\"C工厂\",\"fullIds\":[2580],\"children\":[{\"id\":2581,\"parentCategoryId\":2580,\"name\":\"女鞋\",\"fullIds\":[2580,2581],\"children\":[{\"id\":2584,\"parentCategoryId\":2581,\"name\":\"厚底\",\"fullIds\":[2580,2581,2584],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,厚底\"},{\"id\":2585,\"parentCategoryId\":2581,\"name\":\"帆布\",\"fullIds\":[2580,2581,2585],\"depth\":3,\"fullCategoryName\":\"C工厂,女鞋,帆布\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,女鞋\"},{\"id\":2582,\"parentCategoryId\":2580,\"name\":\"单鞋\",\"fullIds\":[2580,2582],\"children\":[{\"id\":2586,\"parentCategoryId\":2582,\"name\":\"尖头\",\"fullIds\":[2580,2582,2586],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,尖头\"},{\"id\":2587,\"parentCategoryId\":2582,\"name\":\"高跟\",\"fullIds\":[2580,2582,2587],\"depth\":3,\"fullCategoryName\":\"C工厂,单鞋,高跟\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,单鞋\"},{\"id\":2583,\"parentCategoryId\":2580,\"name\":\"男鞋\",\"fullIds\":[2580,2583],\"children\":[{\"id\":2588,\"parentCategoryId\":2583,\"name\":\"商务皮鞋\",\"fullIds\":[2580,2583,2588],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,商务皮鞋\"},{\"id\":2589,\"parentCategoryId\":2583,\"name\":\"内增高\",\"fullIds\":[2580,2583,2589],\"depth\":3,\"fullCategoryName\":\"C工厂,男鞋,内增高\"}],\"depth\":2,\"fullCategoryName\":\"C工厂,男鞋\"}],\"depth\":1,\"fullCategoryName\":\"C工厂\"},{\"id\":2474,\"parentCategoryId\":0,\"name\":\"母婴用品\",\"fullIds\":[2474],\"children\":[{\"id\":2476,\"parentCategoryId\":2474,\"name\":\"婴儿食品\",\"fullIds\":[2474,2476],\"children\":[{\"id\":2493,\"parentCategoryId\":2476,\"name\":\"零食\",\"fullIds\":[2474,2476,2493],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,零食\"},{\"id\":2494,\"parentCategoryId\":2476,\"name\":\"辅食\",\"fullIds\":[2474,2476,2494],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,辅食\"},{\"id\":2590,\"parentCategoryId\":2476,\"name\":\"宝宝奶粉\",\"fullIds\":[2474,2476,2590],\"depth\":3,\"fullCategoryName\":\"母婴用品,婴儿食品,宝宝奶粉\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,婴儿食品\"},{\"id\":2477,\"parentCategoryId\":2474,\"name\":\"尿不湿\",\"fullIds\":[2474,2477],\"children\":[{\"id\":2478,\"parentCategoryId\":2477,\"name\":\"尿不湿\",\"fullIds\":[2474,2477,2478],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,尿不湿\"},{\"id\":2479,\"parentCategoryId\":2477,\"name\":\"拉拉裤\",\"fullIds\":[2474,2477,2479],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,拉拉裤\"},{\"id\":2491,\"parentCategoryId\":2477,\"name\":\"日用品\",\"fullIds\":[2474,2477,2491],\"depth\":3,\"fullCategoryName\":\"母婴用品,尿不湿,日用品\"}],\"depth\":2,\"fullCategoryName\":\"母婴用品,尿不湿\"}],\"depth\":1,\"fullCategoryName\":\"母婴用品\"},{\"id\":8,\"parentCategoryId\":0,\"name\":\"第二个分类1\",\"fullIds\":[8],\"children\":[{\"id\":9,\"parentCategoryId\":8,\"name\":\"自带父级\",\"fullIds\":[8,9],\"children\":[{\"id\":10,\"parentCategoryId\":9,\"name\":\"第三个\",\"fullIds\":[8,9,10],\"depth\":3,\"fullCategoryName\":\"第二个分类1,自带父级,第三个\"}],\"depth\":2,\"fullCategoryName\":\"第二个分类1,自带父级\"}],\"depth\":1,\"fullCategoryName\":\"第二个分类1\"},{\"id\":1,\"parentCategoryId\":0,\"name\":\"测试\",\"fullIds\":[1],\"children\":[{\"id\":2,\"parentCategoryId\":1,\"name\":\"手机\",\"fullIds\":[1,2],\"children\":[{\"id\":6,\"parentCategoryId\":2,\"name\":\"测试121\",\"fullIds\":[1,2,6],\"depth\":3,\"fullCategoryName\":\"测试,手机,测试121\"}],\"depth\":2,\"fullCategoryName\":\"测试,手机\"},{\"id\":4,\"parentCategoryId\":1,\"name\":\"投影仪\",\"fullIds\":[1,4],\"children\":[{\"id\":5,\"parentCategoryId\":4,\"name\":\"华为\",\"fullIds\":[1,4,5],\"depth\":3,\"fullCategoryName\":\"测试,投影仪,华为\"}],\"depth\":2,\"fullCategoryName\":\"测试,投影仪\"}],\"depth\":1,\"fullCategoryName\":\"测试\"},{\"id\":16,\"parentCategoryId\":0,\"name\":\"美妆个护\",\"fullIds\":[16],\"children\":[{\"id\":17,\"parentCategoryId\":16,\"name\":\"化妆品\",\"fullIds\":[16,17],\"children\":[{\"id\":18,\"parentCategoryId\":17,\"name\":\"芯丝翠\",\"fullIds\":[16,17,18],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,芯丝翠\"},{\"id\":54,\"parentCategoryId\":17,\"name\":\"彩妆\",\"fullIds\":[16,17,54],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,彩妆\"},{\"id\":55,\"parentCategoryId\":17,\"name\":\"清洁护肤\",\"fullIds\":[16,17,55],\"depth\":3,\"fullCategoryName\":\"美妆个护,化妆品,清洁护肤\"}],\"depth\":2,\"fullCategoryName\":\"美妆个护,化妆品\"}],\"depth\":1,\"fullCategoryName\":\"美妆个护\"},{\"id\":45,\"parentCategoryId\":0,\"name\":\"生鲜\",\"fullIds\":[45],\"children\":[{\"id\":47,\"parentCategoryId\":45,\"name\":\"水果\",\"fullIds\":[45,47],\"children\":[{\"id\":51,\"parentCategoryId\":47,\"name\":\"国产水果\",\"fullIds\":[45,47,51],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,国产水果\"},{\"id\":52,\"parentCategoryId\":47,\"name\":\"进口水果\",\"fullIds\":[45,47,52],\"depth\":3,\"fullCategoryName\":\"生鲜,水果,进口水果\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,水果\"},{\"id\":58,\"parentCategoryId\":45,\"name\":\"冷冻食品\",\"fullIds\":[45,58],\"children\":[{\"id\":59,\"parentCategoryId\":58,\"name\":\"r肉类\",\"fullIds\":[45,58,59],\"depth\":3,\"fullCategoryName\":\"生鲜,冷冻食品,r肉类\"}],\"depth\":2,\"fullCategoryName\":\"生鲜,冷冻食品\"}],\"depth\":1,\"fullCategoryName\":\"生鲜\"},{\"id\":32,\"parentCategoryId\":0,\"name\":\"国英一级类目\",\"fullIds\":[32],\"children\":[{\"id\":33,\"parentCategoryId\":32,\"name\":\"国英二级类目\",\"fullIds\":[32,33],\"children\":[{\"id\":34,\"parentCategoryId\":33,\"name\":\"国英三级类目\",\"fullIds\":[32,33,34],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,国英三级类目\"},{\"id\":2436,\"parentCategoryId\":33,\"name\":\"32432434\",\"fullIds\":[32,33,2436],\"depth\":3,\"fullCategoryName\":\"国英一级类目,国英二级类目,32432434\"}],\"depth\":2,\"fullCategoryName\":\"国英一级类目,国英二级类目\"}],\"depth\":1,\"fullCategoryName\":\"国英一级类目\"},{\"id\":60,\"parentCategoryId\":0,\"name\":\"百货食品\",\"fullIds\":[60],\"children\":[{\"id\":61,\"parentCategoryId\":60,\"name\":\"副食品\",\"fullIds\":[60,61],\"children\":[{\"id\":62,\"parentCategoryId\":61,\"name\":\"杂货\",\"fullIds\":[60,61,62],\"depth\":3,\"fullCategoryName\":\"百货食品,副食品,杂货\"}],\"depth\":2,\"fullCategoryName\":\"百货食品,副食品\"}],\"depth\":1,\"fullCategoryName\":\"百货食品\"},{\"id\":75,\"parentCategoryId\":0,\"name\":\"【牵牛花】联调一级类目\",\"fullIds\":[75],\"children\":[{\"id\":76,\"parentCategoryId\":75,\"name\":\"【牵牛花】联调二级类目\",\"fullIds\":[75,76],\"children\":[{\"id\":77,\"parentCategoryId\":76,\"name\":\"【牵牛花】联调三级类目\",\"fullIds\":[75,76,77],\"depth\":3,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目,【牵牛花】联调三级类目\"}],\"depth\":2,\"fullCategoryName\":\"【牵牛花】联调一级类目,【牵牛花】联调二级类目\"}],\"depth\":1,\"fullCategoryName\":\"【牵牛花】联调一级类目\"},{\"id\":2437,\"parentCategoryId\":0,\"name\":\"手机数码\",\"fullIds\":[2437],\"children\":[{\"id\":2441,\"parentCategoryId\":2437,\"name\":\"手机通讯\",\"fullIds\":[2437,2441],\"children\":[{\"id\":2443,\"parentCategoryId\":2441,\"name\":\"游戏手机\",\"fullIds\":[2437,2441,2443],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,游戏手机\"},{\"id\":2444,\"parentCategoryId\":2441,\"name\":\"拍照手机\",\"fullIds\":[2437,2441,2444],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,拍照手机\"},{\"id\":2445,\"parentCategoryId\":2441,\"name\":\"全面屏手机\",\"fullIds\":[2437,2441,2445],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,全面屏手机\"},{\"id\":2446,\"parentCategoryId\":2441,\"name\":\"高端旗舰手机\",\"fullIds\":[2437,2441,2446],\"depth\":3,\"fullCategoryName\":\"手机数码,手机通讯,高端旗舰手机\"}],\"depth\":2,\"fullCategoryName\":\"手机数码,手机通讯\"}],\"depth\":1,\"fullCategoryName\":\"手机数码\"},{\"id\":2438,\"parentCategoryId\":0,\"name\":\"家用电器\",\"fullIds\":[2438],\"children\":[{\"id\":2447,\"parentCategoryId\":2438,\"name\":\"电视\",\"fullIds\":[2438,2447],\"children\":[{\"id\":2450,\"parentCategoryId\":2447,\"name\":\"75寸电视\",\"fullIds\":[2438,2447,2450],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,75寸电视\"},{\"id\":2451,\"parentCategoryId\":2447,\"name\":\"85寸电视\",\"fullIds\":[2438,2447,2451],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,85寸电视\"},{\"id\":2452,\"parentCategoryId\":2447,\"name\":\"95寸电视\",\"fullIds\":[2438,2447,2452],\"depth\":3,\"fullCategoryName\":\"家用电器,电视,95寸电视\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,电视\"},{\"id\":2448,\"parentCategoryId\":2438,\"name\":\"空调\",\"fullIds\":[2438,2448],\"children\":[{\"id\":2453,\"parentCategoryId\":2448,\"name\":\"新风空调\",\"fullIds\":[2438,2448,2453],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,新风空调\"},{\"id\":2454,\"parentCategoryId\":2448,\"name\":\"变频空调\",\"fullIds\":[2438,2448,2454],\"depth\":3,\"fullCategoryName\":\"家用电器,空调,变频空调\"}],\"depth\":2,\"fullCategoryName\":\"家用电器,空调\"}],\"depth\":1,\"fullCategoryName\":\"家用电器\"},{\"id\":2439,\"parentCategoryId\":0,\"name\":\"家居用品\",\"fullIds\":[2439],\"children\":[{\"id\":2455,\"parentCategoryId\":2439,\"name\":\"生活日用\",\"fullIds\":[2439,2455],\"children\":[{\"id\":2457,\"parentCategoryId\":2455,\"name\":\"香薰\",\"fullIds\":[2439,2455,2457],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,香薰\"},{\"id\":2458,\"parentCategoryId\":2455,\"name\":\"化妆镜\",\"fullIds\":[2439,2455,2458],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,化妆镜\"},{\"id\":2459,\"parentCategoryId\":2455,\"name\":\"指甲刀\",\"fullIds\":[2439,2455,2459],\"depth\":3,\"fullCategoryName\":\"家居用品,生活日用,指甲刀\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,生活日用\"},{\"id\":2456,\"parentCategoryId\":2439,\"name\":\"家居饰品\",\"fullIds\":[2439,2456],\"children\":[{\"id\":2460,\"parentCategoryId\":2456,\"name\":\"花瓶\",\"fullIds\":[2439,2456,2460],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,花瓶\"},{\"id\":2461,\"parentCategoryId\":2456,\"name\":\"摆件\",\"fullIds\":[2439,2456,2461],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,摆件\"},{\"id\":2470,\"parentCategoryId\":2456,\"name\":\"灯具\",\"fullIds\":[2439,2456,2470],\"depth\":3,\"fullCategoryName\":\"家居用品,家居饰品,灯具\"}],\"depth\":2,\"fullCategoryName\":\"家居用品,家居饰品\"}],\"depth\":1,\"fullCategoryName\":\"家居用品\"},{\"id\":2440,\"parentCategoryId\":0,\"name\":\"服装\",\"fullIds\":[2440],\"children\":[{\"id\":2462,\"parentCategoryId\":2440,\"name\":\"女装\",\"fullIds\":[2440,2462],\"children\":[{\"id\":2464,\"parentCategoryId\":2462,\"name\":\"时尚套装\",\"fullIds\":[2440,2462,2464],\"depth\":3,\"fullCategoryName\":\"服装,女装,时尚套装\"},{\"id\":2465,\"parentCategoryId\":2462,\"name\":\"T恤\",\"fullIds\":[2440,2462,2465],\"depth\":3,\"fullCategoryName\":\"服装,女装,T恤\"},{\"id\":2466,\"parentCategoryId\":2462,\"name\":\"连衣裙\",\"fullIds\":[2440,2462,2466],\"depth\":3,\"fullCategoryName\":\"服装,女装,连衣裙\"},{\"id\":2578,\"parentCategoryId\":2462,\"name\":\"毛衣\",\"fullIds\":[2440,2462,2578],\"depth\":3,\"fullCategoryName\":\"服装,女装,毛衣\"},{\"id\":2579,\"parentCategoryId\":2462,\"name\":\"外套上衣\",\"fullIds\":[2440,2462,2579],\"depth\":3,\"fullCategoryName\":\"服装,女装,外套上衣\"}],\"depth\":2,\"fullCategoryName\":\"服装,女装\"},{\"id\":2463,\"parentCategoryId\":2440,\"name\":\"男装\",\"fullIds\":[2440,2463],\"children\":[{\"id\":2467,\"parentCategoryId\":2463,\"name\":\"T恤\",\"fullIds\":[2440,2463,2467],\"depth\":3,\"fullCategoryName\":\"服装,男装,T恤\"},{\"id\":2468,\"parentCategoryId\":2463,\"name\":\"牛仔裤\",\"fullIds\":[2440,2463,2468],\"depth\":3,\"fullCategoryName\":\"服装,男装,牛仔裤\"},{\"id\":2469,\"parentCategoryId\":2463,\"name\":\"休闲裤\",\"fullIds\":[2440,2463,2469],\"depth\":3,\"fullCategoryName\":\"服装,男装,休闲裤\"}],\"depth\":2,\"fullCategoryName\":\"服装,男装\"}],\"depth\":1,\"fullCategoryName\":\"服装\"},{\"id\":2471,\"parentCategoryId\":0,\"name\":\"酒\",\"fullIds\":[2471],\"children\":[{\"id\":2472,\"parentCategoryId\":2471,\"name\":\"白酒\",\"fullIds\":[2471,2472],\"children\":[{\"id\":2473,\"parentCategoryId\":2472,\"name\":\"酱香型\",\"fullIds\":[2471,2472,2473],\"depth\":3,\"fullCategoryName\":\"酒,白酒,酱香型\"}],\"depth\":2,\"fullCategoryName\":\"酒,白酒\"}],\"depth\":1,\"fullCategoryName\":\"酒\"},{\"id\":2480,\"parentCategoryId\":0,\"name\":\"测试DD\",\"fullIds\":[2480],\"children\":[{\"id\":2481,\"parentCategoryId\":2480,\"name\":\"测试DD01\",\"fullIds\":[2480,2481],\"children\":[{\"id\":2482,\"parentCategoryId\":2481,\"name\":\"测试DD02\",\"fullIds\":[2480,2481,2482],\"depth\":3,\"fullCategoryName\":\"测试DD,测试DD01,测试DD02\"}],\"depth\":2,\"fullCategoryName\":\"测试DD,测试DD01\"}],\"depth\":1,\"fullCategoryName\":\"测试DD\"},{\"id\":2483,\"parentCategoryId\":0,\"name\":\"酒水饮料\",\"fullIds\":[2483],\"children\":[{\"id\":2484,\"parentCategoryId\":2483,\"name\":\"酒水\",\"fullIds\":[2483,2484],\"children\":[{\"id\":2485,\"parentCategoryId\":2484,\"name\":\"白酒\",\"fullIds\":[2483,2484,2485],\"depth\":3,\"fullCategoryName\":\"酒水饮料,酒水,白酒\"}],\"depth\":2,\"fullCategoryName\":\"酒水饮料,酒水\"}],\"depth\":1,\"fullCategoryName\":\"酒水饮料\"},{\"id\":2486,\"parentCategoryId\":0,\"name\":\"粮油米面\",\"fullIds\":[2486],\"children\":[{\"id\":2487,\"parentCategoryId\":2486,\"name\":\"大米\",\"fullIds\":[2486,2487],\"children\":[{\"id\":2488,\"parentCategoryId\":2487,\"name\":\"粳米\",\"fullIds\":[2486,2487,2488],\"depth\":3,\"fullCategoryName\":\"粮油米面,大米,粳米\"}],\"depth\":2,\"fullCategoryName\":\"粮油米面,大米\"}],\"depth\":1,\"fullCategoryName\":\"粮油米面\"},{\"id\":2509,\"parentCategoryId\":0,\"name\":\"供应商未申请的类目\",\"fullIds\":[2509],\"children\":[{\"id\":2511,\"parentCategoryId\":2509,\"name\":\"供应商未申请的类目-1\",\"fullIds\":[2509,2511],\"children\":[{\"id\":2514,\"parentCategoryId\":2511,\"name\":\"未申请的类目-1-1\",\"fullIds\":[2509,2511,2514],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,未申请的类目-1-1\"},{\"id\":2518,\"parentCategoryId\":2511,\"name\":\"嘻嘻嘻1111\",\"fullIds\":[2509,2511,2518],\"depth\":3,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1,嘻嘻嘻1111\"}],\"depth\":2,\"fullCategoryName\":\"供应商未申请的类目,供应商未申请的类目-1\"}],\"depth\":1,\"fullCategoryName\":\"供应商未申请的类目\"},{\"id\":2515,\"parentCategoryId\":0,\"name\":\"TT平台中心\",\"fullIds\":[2515],\"children\":[{\"id\":2516,\"parentCategoryId\":2515,\"name\":\"TT供平台1\",\"fullIds\":[2515,2516],\"children\":[{\"id\":2517,\"parentCategoryId\":2516,\"name\":\"嘻嘻嘻\",\"fullIds\":[2515,2516,2517],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,嘻嘻嘻\"},{\"id\":2519,\"parentCategoryId\":2516,\"name\":\"哈哈哈\",\"fullIds\":[2515,2516,2519],\"depth\":3,\"fullCategoryName\":\"TT平台中心,TT供平台1,哈哈哈\"}],\"depth\":2,\"fullCategoryName\":\"TT平台中心,TT供平台1\"}],\"depth\":1,\"fullCategoryName\":\"TT平台中心\"},{\"id\":2520,\"parentCategoryId\":0,\"name\":\"生活用品\",\"fullIds\":[2520],\"children\":[{\"id\":2521,\"parentCategoryId\":2520,\"name\":\"厨房用纸\",\"fullIds\":[2520,2521],\"children\":[{\"id\":2522,\"parentCategoryId\":2521,\"name\":\"厨房纸\",\"fullIds\":[2520,2521,2522],\"depth\":3,\"fullCategoryName\":\"生活用品,厨房用纸,厨房纸\"}],\"depth\":2,\"fullCategoryName\":\"生活用品,厨房用纸\"}],\"depth\":1,\"fullCategoryName\":\"生活用品\"},{\"id\":2523,\"parentCategoryId\":0,\"name\":\"TTT测试分类\",\"fullIds\":[2523],\"children\":[{\"id\":2524,\"parentCategoryId\":2523,\"name\":\"嘻嘻嘻1\",\"fullIds\":[2523,2524],\"children\":[{\"id\":2525,\"parentCategoryId\":2524,\"name\":\"噜噜噜噜3\",\"fullIds\":[2523,2524,2525],\"depth\":3,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1,噜噜噜噜3\"}],\"depth\":2,\"fullCategoryName\":\"TTT测试分类,嘻嘻嘻1\"}],\"depth\":1,\"fullCategoryName\":\"TTT测试分类\"},{\"id\":2547,\"parentCategoryId\":0,\"name\":\"TT的表单1128\",\"fullIds\":[2547],\"children\":[{\"id\":2549,\"parentCategoryId\":2547,\"name\":\"二级A\",\"fullIds\":[2547,2549],\"children\":[{\"id\":2550,\"parentCategoryId\":2549,\"name\":\"三级A\",\"fullIds\":[2547,2549,2550],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级A\"},{\"id\":2552,\"parentCategoryId\":2549,\"name\":\"三级B\",\"fullIds\":[2547,2549,2552],\"depth\":3,\"fullCategoryName\":\"TT的表单1128,二级A,三级B\"}],\"depth\":2,\"fullCategoryName\":\"TT的表单1128,二级A\"}],\"depth\":1,\"fullCategoryName\":\"TT的表单1128\"},{\"id\":2592,\"parentCategoryId\":0,\"name\":\"默认分类\",\"fullIds\":[2592],\"children\":[{\"id\":2593,\"parentCategoryId\":2592,\"name\":\"默认分类\",\"fullIds\":[2592,2593],\"children\":[{\"id\":2594,\"parentCategoryId\":2593,\"name\":\"默认分类\",\"fullIds\":[2592,2593,2594],\"depth\":3,\"fullCategoryName\":\"默认分类,默认分类,默认分类\"}],\"depth\":2,\"fullCategoryName\":\"默认分类,默认分类\"}],\"depth\":1,\"fullCategoryName\":\"默认分类\"},{\"id\":2629,\"parentCategoryId\":0,\"name\":\"测试类目A\",\"fullIds\":[2629],\"children\":[{\"id\":2630,\"parentCategoryId\":2629,\"name\":\"测试类目A-1\",\"fullIds\":[2629,2630],\"children\":[{\"id\":2631,\"parentCategoryId\":2630,\"name\":\"测试类目A-1-1\",\"fullIds\":[2629,2630,2631],\"depth\":3,\"fullCategoryName\":\"测试类目A,测试类目A-1,测试类目A-1-1\"}],\"depth\":2,\"fullCategoryName\":\"测试类目A,测试类目A-1\"}],\"depth\":1,\"fullCategoryName\":\"测试类目A\"}]"},"code":0,"message":null}
2025-08-01 17:49:09.380 |-INFO  [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-08-01 17:49:09.436 |-INFO  [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[1],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-08-01 17:49:09.437 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[1],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport"]}
2025-08-01 17:49:09.437 |-INFO  [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=A工厂
2025-08-01 17:49:09.437 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-08-01 17:49:09.439 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 59
2025-08-01 17:49:09.439 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-08-01 17:49:09.439 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-08-01 17:49:09.439 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162,"categoryName":"A工厂"}
2025-08-01 17:49:09.439 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (59-byte body)
2025-08-01 17:49:10.253 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- HTTP/1.1 200 OK (814ms)
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] connection: keep-alive
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] content-type: application/json
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] date: Fri, 01 Aug 2025 09:49:10 GMT
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] traceid: 9b5c39564ab9c9be
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] transfer-encoding: chunked
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Origin
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Method
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Headers
2025-08-01 17:49:10.254 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-08-01 17:49:10.256 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null,"success":true}
2025-08-01 17:49:10.256 |-DEBUG [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END HTTP (1616-byte body)
2025-08-01 17:49:10.257 |-INFO  [XNIO-1 task-1][9b5c39564ab9c9be] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"},{\"id\":2571,\"parentCategoryId\":2568,\"name\":\"X3部门\",\"fullIds\":[2568,2571],\"children\":[{\"id\":2576,\"parentCategoryId\":2571,\"name\":\"太阳镜\",\"fullIds\":[2568,2571,2576],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,太阳镜\"},{\"id\":2577,\"parentCategoryId\":2571,\"name\":\"护目镜\",\"fullIds\":[2568,2571,2577],\"depth\":3,\"fullCategoryName\":\"A工厂,X3部门,护目镜\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X3部门\"},{\"id\":2570,\"parentCategoryId\":2568,\"name\":\"X2部门\",\"fullIds\":[2568,2570],\"children\":[{\"id\":2574,\"parentCategoryId\":2570,\"name\":\"b2提单人\",\"fullIds\":[2568,2570,2574],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b2提单人\"},{\"id\":2575,\"parentCategoryId\":2570,\"name\":\"b3tidanren \",\"fullIds\":[2568,2570,2575],\"depth\":3,\"fullCategoryName\":\"A工厂,X2部门,b3tidanren \"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X2部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null}
2025-08-01 17:49:31.803 |-INFO  [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-08-01 17:49:31.858 |-INFO  [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[1],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-08-01 17:49:31.858 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[1],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport"]}
2025-08-01 17:49:31.858 |-INFO  [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryBusinessCategoryTree 请求入参. request=X1部门
2025-08-01 17:49:31.859 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> POST http://localhost:8082/himall-base/shop/businessCategory/queryBusinessCategoryTree HTTP/1.1
2025-08-01 17:49:31.859 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Length: 60
2025-08-01 17:49:31.859 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] Content-Type: application/json
2025-08-01 17:49:31.859 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-08-01 17:49:31.860 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"operationShopId":0,"shopId":162,"categoryName":"X1部门"}
2025-08-01 17:49:31.860 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] ---> END HTTP (60-byte body)
2025-08-01 17:49:32.618 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- HTTP/1.1 200 OK (757ms)
2025-08-01 17:49:32.618 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] connection: keep-alive
2025-08-01 17:49:32.618 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] content-type: application/json
2025-08-01 17:49:32.618 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] date: Fri, 01 Aug 2025 09:49:32 GMT
2025-08-01 17:49:32.618 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] traceid: e2e1263dabfe2963
2025-08-01 17:49:32.618 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] transfer-encoding: chunked
2025-08-01 17:49:32.619 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Origin
2025-08-01 17:49:32.619 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Method
2025-08-01 17:49:32.619 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] vary: Access-Control-Request-Headers
2025-08-01 17:49:32.619 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] 
2025-08-01 17:49:32.619 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] {"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null,"success":true}
2025-08-01 17:49:32.619 |-DEBUG [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign [72] -| [BusinessCategoryQueryFeign#queryBusinessCategoryTree] <--- END HTTP (674-byte body)
2025-08-01 17:49:32.620 |-INFO  [XNIO-1 task-1][e2e1263dabfe2963] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryBusinessCategoryTree success. 请求结果. response={"data":{"result":"[{\"id\":2568,\"parentCategoryId\":0,\"name\":\"A工厂\",\"fullIds\":[2568],\"children\":[{\"id\":2569,\"parentCategoryId\":2568,\"name\":\"X1部门\",\"fullIds\":[2568,2569],\"children\":[{\"id\":2572,\"parentCategoryId\":2569,\"name\":\"a1提单人\",\"fullIds\":[2568,2569,2572],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a1提单人\"},{\"id\":2573,\"parentCategoryId\":2569,\"name\":\"a2提单人\",\"fullIds\":[2568,2569,2573],\"depth\":3,\"fullCategoryName\":\"A工厂,X1部门,a2提单人\"}],\"depth\":2,\"fullCategoryName\":\"A工厂,X1部门\"}],\"depth\":1,\"fullCategoryName\":\"A工厂\"}]"},"code":0,"message":null}
2025-08-01 17:53:00.293 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-08-01 17:53:00.293 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 17:53:00.294 |-INFO  [Thread-131][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-08-01 17:53:00.294 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-08-01 17:53:00.294 |-INFO  [Thread-131][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-08-01 17:53:00.293 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.294 |-INFO  [Thread-132][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-08-01 17:53:00.297 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-08-01 17:53:00.300 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=461, lastTimeStamp=1754041980300}] instanceId:[InstanceId{instanceId=*******:7544, stable=false}] @ namespace:[himall-gw].
2025-08-01 17:53:00.323 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-08-01 17:53:03.341 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-08-01 17:53:03.349 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 17:53:03.531 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-08-01 17:53:03.532 |-INFO  [Thread-150][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-08-01 17:53:03.911 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='chengpei_himall-gw', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-01 17:53:03.911 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-08-01 17:53:03.912 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-01 17:53:03.912 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-08-01 17:53:03.913 |-INFO  [Thread-149][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-08-01 17:53:03.915 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-08-01 17:53:03.950 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-08-01 17:53:03.979 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:53:03.980 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 17:53:03.980 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
