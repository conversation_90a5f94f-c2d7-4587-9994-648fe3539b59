2025-07-17 10:43:16.343 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-17 10:43:20.018 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 10:43:24.115 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.Insert [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.insert] Has been loaded by XML or SqlProvider or Mybat<PERSON>'s Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-17 10:43:52.105 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-17 10:44:37.487 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 10:44:37.487 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 10:44:37.487 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 10:44:37.491 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 11:11:13.631 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-17 11:11:17.108 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 11:11:20.614 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.Insert [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-17 11:11:45.109 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-17 11:13:30.323 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-17 11:13:49.299 |-ERROR [task-12][8893762577932be4] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-17 11:14:25.558 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:25.567 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:30.254 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=162, gradeId=0, shopName=官方自营店, logo=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/68536990e4b0a866a039149e.jpeg, subDomains=null, theme=null, whetherSelf=true, shopStatus=7, refuseReason=我想拒绝, adapayStatus=10, adapayReason=null, plateStatus=7, createDate=Mon Feb 26 10:35:27 CST 2024, contactsName=婚庆商城, contactsPhone=***********, contactsEmail=<EMAIL>, generalTaxpayerPhot=null, bankAccountName=456, bankAccountNumber=pI8twLEQVIHUS7Be0KG6oA==, bankName=工商银行, bankCode=********, bankRegionId=1100, bankPhoto=, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=500.00, amountFreight=0.00, quantityFreightCondition=3, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=3, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=LDFlL+6R1y9M5OR7NmWfAF5MzkN4BIZ+cObn+DB2z90=, idCardurl=https://himall-obs.35hiw.com/test/rs/himall-base/66bf2abde4b0a866dae64f13.jpg_Q75.jpg_.webp, idCardurl2=https://himall-obs.35hiw.com/test/rs/himall-base/66c414f2e4b0a8667e9e838c.jpg_Q75.jpg_.webp, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=true, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=2, idCardStartDate=Thu Jan 01 08:00:00 CST 1970, idCardEndDate=Thu Jan 01 08:00:00 CST 1970, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"下拉框","type":5,"format":"","options":"一妹,二妹","required":true,"sort":"0","optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":"一妹"},{"name":"文件","type":4,"format":0,"options":"看书,打球,骑行","required":true,"sort":"1","value":""},{"name":"单选","type":6,"format":"","options":"一妹,二妹","required":true,"sort":1,"optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":""},{"name":"复选","type":7,"format":"","options":"一妹,二妹","required":true,"sort":1,"optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":""},{"name":"图片","type":3,"format":0,"options":"男,女","required":true,"sort":"2","value":""},{"name":"多行文本","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":""},{"name":"身份证","type":1,"format":66,"options":"","required":true,"sort":"5","value":""},{"name":"日期","type":1,"format":55,"options":"","required":true,"sort":"6","value":""},{"name":"手机好","type":1,"format":44,"options":"","required":true,"sort":"7","value":""},{"name":"邮箱","type":1,"format":33,"options":"","required":true,"sort":"8","value":""},{"name":"数字","type":1,"format":22,"options":"","required":true,"sort":"9","value":""},{"name":"文本","type":1,"format":11,"options":"","required":true,"sort":"10","value":""}], whetherOpenExclusiveMember=false, createTime=Mon Feb 26 10:35:27 CST 2024, updateTime=Tue Dec 10 00:00:00 CST 2024, orderPayIzSendSms=true, shopType=2, idCardDate=null, serialNumber=7, whetherSupply=true, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.111 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.111 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.112 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 23 13:42:59 CST 2025, contactsName=test001, contactsPhone=***********, contactsEmail=, generalTaxpayerPhot=null, bankAccountName=汪强, bankAccountNumber=0WASNe428Ag80XkXJr/Awrgt2Gm3v7zCOEd2h5kxy3c=, bankName=中国银行, bankCode=********, bankRegionId=1100, bankPhoto=null, bankType=2, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=4, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=hqk6BjynWRhy3JRkSvj+q8lAYNh2ZYEG2Lq/i/TCQ8E=, idCardurl=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6858fc1be4b0a8661c279c66.png, idCardurl2=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6858fc1fe4b0a8661c279c67.png, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"下拉框","type":5,"format":"","options":"一妹,二妹","required":true,"sort":"0","optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":"一妹"},{"name":"文件","type":4,"format":0,"options":"看书,打球,骑行","required":true,"sort":"1","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6858fc5ce4b0a8661c279c69.png"},{"name":"单选","type":6,"format":"","options":"一妹,二妹","required":true,"sort":1,"optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":"一妹"},{"name":"复选","type":7,"format":"","options":"一妹,二妹","required":true,"sort":1,"optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":"二妹"},{"name":"图片","type":3,"format":0,"options":"男,女","required":true,"sort":"2","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6858fc27e4b0a8661c279c68.png"},{"name":"多行文本","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":"sdgdg"},{"name":"身份证","type":1,"format":66,"options":"","required":true,"sort":"5","value":"430722199002285378"},{"name":"日期","type":1,"format":55,"options":"","required":true,"sort":"6","value":"2025-06-26"},{"name":"手机号","type":1,"format":44,"options":"","required":true,"sort":"7","value":"***********"},{"name":"邮箱","type":1,"format":33,"options":"","required":true,"sort":"8","value":"<EMAIL>"},{"name":"数字","type":1,"format":22,"options":"","required":true,"sort":"9","value":"2324"},{"name":"文本","type":1,"format":11,"options":"","required":true,"sort":"10","value":"sdfdf"}], whetherOpenExclusiveMember=false, createTime=Mon Jun 23 13:42:59 CST 2025, updateTime=Mon Jun 23 13:42:59 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.113 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.114 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.114 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=精品小客, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=7, refuseReason=null, adapayStatus=10, adapayReason=null, plateStatus=7, createDate=Mon Jun 30 10:09:53 CST 2025, contactsName=墨阳, contactsPhone=***********, contactsEmail=, generalTaxpayerPhot=null, bankAccountName=蒋丽, bankAccountNumber=pGcAeeN8L19Zi5rGVGw+Hi7CH0KzuZK0qLo5f6BWYrI=, bankName=兴业银行, bankCode=********, bankRegionId=4301, bankPhoto=null, bankType=2, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=100.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=6, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=v+acNB2DUOwQCWFShC/okvAYuWZd15ickBNjZ7xVITM=, idCardurl=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6861f9bce4b0a8661ce6821f.jpg, idCardurl2=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6861f9c0e4b0a8661ce68220.jpg, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"经营地址","type":1,"format":11,"options":"看书,打球,骑行","required":true,"sort":"1","value":"湖南省长沙市望城区砂之船"},{"name":"营业执照","type":4,"format":0,"options":"男,女","required":true,"sort":"2","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6861f9ede4b0a8661ce68221.jpg"},{"name":"经营范围","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":"服装、鞋帽、饰品"},{"name":"日期","type":1,"format":55,"options":"","required":false,"sort":"6","value":""},{"name":"联系方式","type":1,"format":44,"options":"","required":true,"sort":"7","value":"***********"},{"name":"邮箱","type":1,"format":33,"options":"","required":false,"sort":"8","value":""},{"name":"数字","type":1,"format":22,"options":"","required":false,"sort":"9","value":""},{"name":"文本","type":1,"format":11,"options":"","required":false,"sort":"10","value":""}], whetherOpenExclusiveMember=false, createTime=Mon Jun 30 10:09:53 CST 2025, updateTime=Mon Jun 30 10:09:53 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.116 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.116 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.117 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 13:50:43 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 13:50:43 CST 2025, updateTime=Mon Jun 30 13:50:43 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.118 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.118 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.118 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 14:11:33 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=1, senderRegionId=0, productCert=null, otherCert=null, businessType=0, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 14:11:33 CST 2025, updateTime=Mon Jun 30 14:11:33 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.119 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.120 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.120 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 14:15:09 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=1, senderRegionId=0, productCert=null, otherCert=null, businessType=0, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 14:15:09 CST 2025, updateTime=Mon Jun 30 14:15:09 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.121 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.121 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.122 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=精品小店, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=2, refuseReason=null, adapayStatus=10, adapayReason=null, plateStatus=2, createDate=Mon Jun 30 14:15:39 CST 2025, contactsName=Kid, contactsPhone=***********, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=谭智霖, bankAccountNumber=6oCrYanI32g1kVRkWnZ0AGH5tMBb1P576unH2MDZXjk=, bankName=中国银行, bankCode=********, bankRegionId=4301, bankPhoto=null, bankType=2, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=5, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=6I/pphSKIf2iE5ilmUOAvvya+nvyb/3UqkblLIOgAvA=, idCardurl=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/68622bb2e4b0a8661ce68224.jpg, idCardurl2=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/68622bb6e4b0a8661ce68225.jpg, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"经营地址","type":1,"format":11,"options":"看书,打球,骑行","required":true,"sort":"1","value":"湖南省长沙市海商网络科技有限公司"},{"name":"营业执照","type":4,"format":0,"options":"男,女","required":true,"sort":"2","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/68622be1e4b0a8661ce68226.png"},{"name":"经营范围","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":"123213"},{"name":"联系方式","type":1,"format":44,"options":"","required":true,"sort":"7","value":"15575471232"}], whetherOpenExclusiveMember=false, createTime=Mon Jun 30 14:15:39 CST 2025, updateTime=Mon Jun 30 14:15:39 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.123 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.123 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.124 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 15:29:03 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 15:29:03 CST 2025, updateTime=Mon Jun 30 15:29:03 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.125 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.125 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.125 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 15:43:16 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=1, senderRegionId=0, productCert=null, otherCert=null, businessType=0, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 15:43:16 CST 2025, updateTime=Mon Jun 30 15:43:16 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.126 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.126 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.127 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 15:44:35 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=0, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 15:44:35 CST 2025, updateTime=Mon Jun 30 15:44:35 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.128 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.128 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.129 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 15:45:16 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 15:45:16 CST 2025, updateTime=Mon Jun 30 15:45:16 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.130 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.130 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.130 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Tue Jul 01 19:46:49 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Tue Jul 01 19:46:49 CST 2025, updateTime=Tue Jul 01 19:46:49 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:32.131 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:14:32.131 |-WARN  [XNIO-1 task-1][26b5fe93dda6a4dc] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:14:32.132 |-ERROR [XNIO-1 task-1][26b5fe93dda6a4dc] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=精品小店1, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=7, refuseReason=null, adapayStatus=10, adapayReason=null, plateStatus=7, createDate=Wed Jul 02 14:13:57 CST 2025, contactsName=123, contactsPhone=***********, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=谭智霖, bankAccountNumber=c6mRtNEOBTVy3veCElihVlXy5LaoicZO/Q6EzE831k8=, bankName=长沙银行, bankCode=********, bankRegionId=4301, bankPhoto=null, bankType=2, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=6, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=6I/pphSKIf2iE5ilmUOAvvya+nvyb/3UqkblLIOgAvA=, idCardurl=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6864ce33e4b0a8668f0b5a59.jpg, idCardurl2=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6864ce35e4b0a8668f0b5a5a.jpg, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"经营地址","type":1,"format":11,"options":"看书,打球,骑行","required":true,"sort":"1","value":"12321312"},{"name":"营业执照","type":4,"format":0,"options":"男,女","required":true,"sort":"2","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6864ce3fe4b0a8668f0b5a5b.png"},{"name":"经营范围","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":"123213"},{"name":"联系方式","type":1,"format":44,"options":"","required":true,"sort":"7","value":"15575471232"}], whetherOpenExclusiveMember=false, createTime=Wed Jul 02 14:13:57 CST 2025, updateTime=Wed Jul 02 14:13:57 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:14:45.515 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 11:14:45.515 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 11:14:45.515 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 11:14:45.519 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 11:17:06.594 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-17 11:17:10.683 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 11:17:14.507 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.Insert [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-17 11:17:39.197 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-17 11:19:31.209 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-17 11:19:44.031 |-ERROR [task-9][ac258c05ae15c796] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-17 11:21:39.181 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:39.185 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.902 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=162, gradeId=0, shopName=官方自营店, logo=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/68536990e4b0a866a039149e.jpeg, subDomains=null, theme=null, whetherSelf=true, shopStatus=7, refuseReason=我想拒绝, adapayStatus=10, adapayReason=null, plateStatus=7, createDate=Mon Feb 26 10:35:27 CST 2024, contactsName=婚庆商城, contactsPhone=***********, contactsEmail=<EMAIL>, generalTaxpayerPhot=null, bankAccountName=456, bankAccountNumber=pI8twLEQVIHUS7Be0KG6oA==, bankName=工商银行, bankCode=********, bankRegionId=1100, bankPhoto=, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=500.00, amountFreight=0.00, quantityFreightCondition=3, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=3, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=LDFlL+6R1y9M5OR7NmWfAF5MzkN4BIZ+cObn+DB2z90=, idCardurl=https://himall-obs.35hiw.com/test/rs/himall-base/66bf2abde4b0a866dae64f13.jpg_Q75.jpg_.webp, idCardurl2=https://himall-obs.35hiw.com/test/rs/himall-base/66c414f2e4b0a8667e9e838c.jpg_Q75.jpg_.webp, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=true, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=2, idCardStartDate=Thu Jan 01 08:00:00 CST 1970, idCardEndDate=Thu Jan 01 08:00:00 CST 1970, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"下拉框","type":5,"format":"","options":"一妹,二妹","required":true,"sort":"0","optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":"一妹"},{"name":"文件","type":4,"format":0,"options":"看书,打球,骑行","required":true,"sort":"1","value":""},{"name":"单选","type":6,"format":"","options":"一妹,二妹","required":true,"sort":1,"optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":""},{"name":"复选","type":7,"format":"","options":"一妹,二妹","required":true,"sort":1,"optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":""},{"name":"图片","type":3,"format":0,"options":"男,女","required":true,"sort":"2","value":""},{"name":"多行文本","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":""},{"name":"身份证","type":1,"format":66,"options":"","required":true,"sort":"5","value":""},{"name":"日期","type":1,"format":55,"options":"","required":true,"sort":"6","value":""},{"name":"手机好","type":1,"format":44,"options":"","required":true,"sort":"7","value":""},{"name":"邮箱","type":1,"format":33,"options":"","required":true,"sort":"8","value":""},{"name":"数字","type":1,"format":22,"options":"","required":true,"sort":"9","value":""},{"name":"文本","type":1,"format":11,"options":"","required":true,"sort":"10","value":""}], whetherOpenExclusiveMember=false, createTime=Mon Feb 26 10:35:27 CST 2024, updateTime=Tue Dec 10 00:00:00 CST 2024, orderPayIzSendSms=true, shopType=2, idCardDate=null, serialNumber=7, whetherSupply=true, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.912 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.913 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.913 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 23 13:42:59 CST 2025, contactsName=test001, contactsPhone=***********, contactsEmail=, generalTaxpayerPhot=null, bankAccountName=汪强, bankAccountNumber=0WASNe428Ag80XkXJr/Awrgt2Gm3v7zCOEd2h5kxy3c=, bankName=中国银行, bankCode=********, bankRegionId=1100, bankPhoto=null, bankType=2, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=4, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=hqk6BjynWRhy3JRkSvj+q8lAYNh2ZYEG2Lq/i/TCQ8E=, idCardurl=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6858fc1be4b0a8661c279c66.png, idCardurl2=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6858fc1fe4b0a8661c279c67.png, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"下拉框","type":5,"format":"","options":"一妹,二妹","required":true,"sort":"0","optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":"一妹"},{"name":"文件","type":4,"format":0,"options":"看书,打球,骑行","required":true,"sort":"1","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6858fc5ce4b0a8661c279c69.png"},{"name":"单选","type":6,"format":"","options":"一妹,二妹","required":true,"sort":1,"optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":"一妹"},{"name":"复选","type":7,"format":"","options":"一妹,二妹","required":true,"sort":1,"optionList":[{"label":"一妹","value":"一妹"},{"label":"二妹","value":"二妹"}],"value":"二妹"},{"name":"图片","type":3,"format":0,"options":"男,女","required":true,"sort":"2","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6858fc27e4b0a8661c279c68.png"},{"name":"多行文本","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":"sdgdg"},{"name":"身份证","type":1,"format":66,"options":"","required":true,"sort":"5","value":"430722199002285378"},{"name":"日期","type":1,"format":55,"options":"","required":true,"sort":"6","value":"2025-06-26"},{"name":"手机号","type":1,"format":44,"options":"","required":true,"sort":"7","value":"***********"},{"name":"邮箱","type":1,"format":33,"options":"","required":true,"sort":"8","value":"<EMAIL>"},{"name":"数字","type":1,"format":22,"options":"","required":true,"sort":"9","value":"2324"},{"name":"文本","type":1,"format":11,"options":"","required":true,"sort":"10","value":"sdfdf"}], whetherOpenExclusiveMember=false, createTime=Mon Jun 23 13:42:59 CST 2025, updateTime=Mon Jun 23 13:42:59 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.915 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.915 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.916 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=精品小客, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=7, refuseReason=null, adapayStatus=10, adapayReason=null, plateStatus=7, createDate=Mon Jun 30 10:09:53 CST 2025, contactsName=墨阳, contactsPhone=***********, contactsEmail=, generalTaxpayerPhot=null, bankAccountName=蒋丽, bankAccountNumber=pGcAeeN8L19Zi5rGVGw+Hi7CH0KzuZK0qLo5f6BWYrI=, bankName=兴业银行, bankCode=********, bankRegionId=4301, bankPhoto=null, bankType=2, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=100.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=6, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=v+acNB2DUOwQCWFShC/okvAYuWZd15ickBNjZ7xVITM=, idCardurl=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6861f9bce4b0a8661ce6821f.jpg, idCardurl2=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6861f9c0e4b0a8661ce68220.jpg, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"经营地址","type":1,"format":11,"options":"看书,打球,骑行","required":true,"sort":"1","value":"湖南省长沙市望城区砂之船"},{"name":"营业执照","type":4,"format":0,"options":"男,女","required":true,"sort":"2","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6861f9ede4b0a8661ce68221.jpg"},{"name":"经营范围","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":"服装、鞋帽、饰品"},{"name":"日期","type":1,"format":55,"options":"","required":false,"sort":"6","value":""},{"name":"联系方式","type":1,"format":44,"options":"","required":true,"sort":"7","value":"***********"},{"name":"邮箱","type":1,"format":33,"options":"","required":false,"sort":"8","value":""},{"name":"数字","type":1,"format":22,"options":"","required":false,"sort":"9","value":""},{"name":"文本","type":1,"format":11,"options":"","required":false,"sort":"10","value":""}], whetherOpenExclusiveMember=false, createTime=Mon Jun 30 10:09:53 CST 2025, updateTime=Mon Jun 30 10:09:53 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.916 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.917 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.917 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 13:50:43 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 13:50:43 CST 2025, updateTime=Mon Jun 30 13:50:43 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.918 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.918 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.919 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 14:11:33 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=1, senderRegionId=0, productCert=null, otherCert=null, businessType=0, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 14:11:33 CST 2025, updateTime=Mon Jun 30 14:11:33 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.920 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.920 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.920 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 14:15:09 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=1, senderRegionId=0, productCert=null, otherCert=null, businessType=0, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 14:15:09 CST 2025, updateTime=Mon Jun 30 14:15:09 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.921 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.921 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.922 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=精品小店, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=2, refuseReason=null, adapayStatus=10, adapayReason=null, plateStatus=2, createDate=Mon Jun 30 14:15:39 CST 2025, contactsName=Kid, contactsPhone=***********, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=谭智霖, bankAccountNumber=6oCrYanI32g1kVRkWnZ0AGH5tMBb1P576unH2MDZXjk=, bankName=中国银行, bankCode=********, bankRegionId=4301, bankPhoto=null, bankType=2, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=5, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=6I/pphSKIf2iE5ilmUOAvvya+nvyb/3UqkblLIOgAvA=, idCardurl=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/68622bb2e4b0a8661ce68224.jpg, idCardurl2=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/68622bb6e4b0a8661ce68225.jpg, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"经营地址","type":1,"format":11,"options":"看书,打球,骑行","required":true,"sort":"1","value":"湖南省长沙市海商网络科技有限公司"},{"name":"营业执照","type":4,"format":0,"options":"男,女","required":true,"sort":"2","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/68622be1e4b0a8661ce68226.png"},{"name":"经营范围","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":"123213"},{"name":"联系方式","type":1,"format":44,"options":"","required":true,"sort":"7","value":"15575471232"}], whetherOpenExclusiveMember=false, createTime=Mon Jun 30 14:15:39 CST 2025, updateTime=Mon Jun 30 14:15:39 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.924 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.924 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.925 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 15:29:03 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 15:29:03 CST 2025, updateTime=Mon Jun 30 15:29:03 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.926 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.926 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.927 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 15:43:16 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=1, senderRegionId=0, productCert=null, otherCert=null, businessType=0, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 15:43:16 CST 2025, updateTime=Mon Jun 30 15:43:16 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.927 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.928 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.928 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 15:44:35 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=0, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 15:44:35 CST 2025, updateTime=Mon Jun 30 15:44:35 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.929 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.929 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.929 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Mon Jun 30 15:45:16 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Mon Jun 30 15:45:16 CST 2025, updateTime=Mon Jun 30 15:45:16 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.930 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.930 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.931 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=1, refuseReason=null, adapayStatus=8, adapayReason=null, plateStatus=1, createDate=Tue Jul 01 19:46:49 CST 2025, contactsName=null, contactsPhone=null, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=null, bankAccountNumber=null, bankName=null, bankCode=null, bankRegionId=0, bankPhoto=null, bankType=1, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=2, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=null, idCardurl=null, idCardurl2=null, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=null, whetherOpenExclusiveMember=false, createTime=Tue Jul 01 19:46:49 CST 2025, updateTime=Tue Jul 01 19:46:49 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:21:41.932 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer [98] -| No servers available for service: himall-report
2025-07-17 11:21:41.932 |-WARN  [XNIO-1 task-1][7b33a32084f8a56d] -  org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient [147] -| Service instance was not resolved, executing the original request
2025-07-17 11:21:41.932 |-ERROR [XNIO-1 task-1][7b33a32084f8a56d] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [195] -| 同步店铺信息错误：shop:Shop(id=********, gradeId=0, shopName=精品小店1, logo=https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png, subDomains=null, theme=null, whetherSelf=false, shopStatus=7, refuseReason=null, adapayStatus=10, adapayReason=null, plateStatus=7, createDate=Wed Jul 02 14:13:57 CST 2025, contactsName=123, contactsPhone=***********, contactsEmail=null, generalTaxpayerPhot=null, bankAccountName=谭智霖, bankAccountNumber=c6mRtNEOBTVy3veCElihVlXy5LaoicZO/Q6EzE831k8=, bankName=长沙银行, bankCode=********, bankRegionId=4301, bankPhoto=null, bankType=2, taxRegistrationCertificate=null, taxpayerId=null, taxRegistrationCertificatePhoto=null, payPhoto=null, payRemark=null, senderName=null, senderAddress=null, senderPhone=null, freight=0.00, freeFreight=0.00, amountFreightCondition=0.00, amountFreight=0.00, quantityFreightCondition=0, quantityFreight=0.00, moneyOffCondition=0.00, moneyOffFee=0.00, moneyOffOverlay=false, stage=6, senderRegionId=0, productCert=null, otherCert=null, businessType=1, idCard=6I/pphSKIf2iE5ilmUOAvvya+nvyb/3UqkblLIOgAvA=, idCardurl=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6864ce33e4b0a8668f0b5a59.jpg, idCardurl2=https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6864ce35e4b0a8668f0b5a5a.jpg, autoAllotOrder=false, whetherAutoPrint=false, printCount=0, whetherOpenTopImageAd=false, whetherOpenHiChat=false, whetherPayBond=false, whetherAgreement=false, whetherSmsTips=false, autographImg=null, idCardExpireType=1, idCardStartDate=null, idCardEndDate=null, licenceCertAddr=null, currencyType=null, introduct=null, formData=[{"name":"经营地址","type":1,"format":11,"options":"看书,打球,骑行","required":true,"sort":"1","value":"12321312"},{"name":"营业执照","type":4,"format":0,"options":"男,女","required":true,"sort":"2","value":"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6864ce3fe4b0a8668f0b5a5b.png"},{"name":"经营范围","type":2,"format":0,"options":"a,b,c,d","required":true,"sort":"3","value":"123213"},{"name":"联系方式","type":1,"format":44,"options":"","required":true,"sort":"7","value":"15575471232"}], whetherOpenExclusiveMember=false, createTime=Wed Jul 02 14:13:57 CST 2025, updateTime=Wed Jul 02 14:13:57 CST 2025, orderPayIzSendSms=true, shopType=1, idCardDate=null, serialNumber=null, whetherSupply=false, whetherArrear=false, appKey=null, appSecret=null, registed=null, enable=null), feign.RetryableException: himall-report executing POST http://himall-report/himall-report/report/createSourceShop
2025-07-17 11:25:23.101 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 11:25:23.102 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 11:25:23.101 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 11:25:23.106 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 11:29:41.804 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 11:30:04.518 |-ERROR [main][] -  com.zaxxer.hikari.pool.HikariPool [594] -| master - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) [spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585) [spring-context-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.sankuai.shangou.seashop.base.StartApp.main(StartApp.java:31) [classes/:?]
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_121]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_121]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 96 more
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method) ~[?:1.8.0_121]
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188) ~[?:1.8.0_121]
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172) ~[?:1.8.0_121]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_121]
	at java.net.Socket.connect(Socket.java:589) ~[?:1.8.0_121]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	... 96 more
2025-07-17 11:30:04.541 |-WARN  [main][] -  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext [599] -| Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
2025-07-17 11:30:04.620 |-ERROR [main][] -  org.springframework.boot.SpringApplication [818] -| Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.sankuai.shangou.seashop.base.StartApp.main(StartApp.java:31) [classes/:?]
Caused by: java.lang.RuntimeException: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:257) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcMetricsFilter' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/web/servlet/WebMvcMetricsAutoConfiguration.class]: Unsatisfied dependency expressed through method 'webMvcMetricsFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:794) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'simpleMeterRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/export/simple/SimpleMetricsExportAutoConfiguration.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:628) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourcePoolMetadataMeterBinder' defined in class path resource [org/springframework/boot/actuate/autoconfigure/metrics/jdbc/DataSourcePoolMetricsAutoConfiguration$DataSourcePoolMetadataMetricsConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourcePoolMetadataMeterBinder' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:794) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: Invocation of init method failed; nested exception is com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException(HikariPool.java:596) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:582) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_121]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_121]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method) ~[?:1.8.0_121]
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206) ~[?:1.8.0_121]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188) ~[?:1.8.0_121]
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172) ~[?:1.8.0_121]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_121]
	at java.net.Socket.connect(Socket.java:589) ~[?:1.8.0_121]
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81) ~[HikariCP-4.0.3.jar:?]
	at com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator.createDataSource(HikariDataSourceCreator.java:85) ~[dynamic-datasource-creator-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator.createDataSource(DefaultDataSourceCreator.java:97) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider.createDataSourceMap(AbstractDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider.loadDataSources(YmlDynamicDataSourceProvider.java:53) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:229) ~[dynamic-datasource-spring-4.3.0.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1609) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1492) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64) ~[spring-boot-actuator-autoconfigure-2.7.18.jar:2.7.18]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:781) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:532) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:214) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:508) ~[spring-boot-2.7.18.jar:2.7.18]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:187) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:255) ~[undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createManager(UndertowServletWebServerFactory.java:330) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:299) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-2.7.18.jar:2.7.18]
	... 8 more
2025-07-17 11:30:04.626 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 11:30:04.626 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 11:30:04.626 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 11:30:04.627 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 11:32:30.462 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 11:34:42.347 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-17 11:34:51.129 |-ERROR [Thread-16][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-17 11:40:10.482 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 11:40:10.482 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 11:40:10.483 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 11:40:10.486 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 15:00:58.780 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 15:01:28.906 |-WARN  [main][] -  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext [599] -| Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reportTask' defined in file [E:\work\himallWork\himall-base\seashop-base-core\target\classes\com\sankuai\shangou\seashop\base\core\task\ReportTask.class]: Post-processing of merged bean definition failed; nested exception is java.lang.IllegalStateException: @Resource annotation requires a single-arg method: public void com.sankuai.shangou.seashop.base.core.task.ReportTask.reportFavoriteProduct()
2025-07-17 15:01:35.242 |-ERROR [main][] -  org.springframework.boot.SpringApplication [818] -| Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reportTask' defined in file [E:\work\himallWork\himall-base\seashop-base-core\target\classes\com\sankuai\shangou\seashop\base\core\task\ReportTask.class]: Post-processing of merged bean definition failed; nested exception is java.lang.IllegalStateException: @Resource annotation requires a single-arg method: public void com.sankuai.shangou.seashop.base.core.task.ReportTask.reportFavoriteProduct()
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.sankuai.shangou.seashop.base.StartApp.main(StartApp.java:31) [classes/:?]
Caused by: java.lang.IllegalStateException: @Resource annotation requires a single-arg method: public void com.sankuai.shangou.seashop.base.core.task.ReportTask.reportFavoriteProduct()
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.lambda$buildResourceMetadata$1(CommonAnnotationBeanPostProcessor.java:436) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:324) ~[spring-core-5.3.33.jar:5.3.33]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.buildResourceMetadata(CommonAnnotationBeanPostProcessor.java:400) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.findResourceMetadata(CommonAnnotationBeanPostProcessor.java:358) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessMergedBeanDefinition(CommonAnnotationBeanPostProcessor.java:306) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyMergedBeanDefinitionPostProcessors(AbstractAutowireCapableBeanFactory.java:1116) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[spring-beans-5.3.33.jar:5.3.33]
	... 15 more
2025-07-17 15:01:37.134 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 15:01:37.134 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 15:01:37.135 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 15:01:37.138 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 15:03:09.830 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 15:03:39.349 |-WARN  [main][] -  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext [599] -| Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reportTask' defined in file [E:\work\himallWork\himall-base\seashop-base-core\target\classes\com\sankuai\shangou\seashop\base\core\task\ReportTask.class]: Post-processing of merged bean definition failed; nested exception is java.lang.IllegalStateException: @Resource annotation requires a single-arg method: public void com.sankuai.shangou.seashop.base.core.task.ReportTask.reportFavoriteProduct()
2025-07-17 15:03:45.708 |-ERROR [main][] -  org.springframework.boot.SpringApplication [818] -| Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reportTask' defined in file [E:\work\himallWork\himall-base\seashop-base-core\target\classes\com\sankuai\shangou\seashop\base\core\task\ReportTask.class]: Post-processing of merged bean definition failed; nested exception is java.lang.IllegalStateException: @Resource annotation requires a single-arg method: public void com.sankuai.shangou.seashop.base.core.task.ReportTask.reportFavoriteProduct()
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.sankuai.shangou.seashop.base.StartApp.main(StartApp.java:31) [classes/:?]
Caused by: java.lang.IllegalStateException: @Resource annotation requires a single-arg method: public void com.sankuai.shangou.seashop.base.core.task.ReportTask.reportFavoriteProduct()
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.lambda$buildResourceMetadata$1(CommonAnnotationBeanPostProcessor.java:436) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:324) ~[spring-core-5.3.33.jar:5.3.33]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.buildResourceMetadata(CommonAnnotationBeanPostProcessor.java:400) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.findResourceMetadata(CommonAnnotationBeanPostProcessor.java:358) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessMergedBeanDefinition(CommonAnnotationBeanPostProcessor.java:306) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyMergedBeanDefinitionPostProcessors(AbstractAutowireCapableBeanFactory.java:1116) ~[spring-beans-5.3.33.jar:5.3.33]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[spring-beans-5.3.33.jar:5.3.33]
	... 15 more
2025-07-17 15:03:47.601 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 15:03:47.601 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 15:03:47.602 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 15:03:47.606 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 15:05:40.185 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 15:07:57.313 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-17 15:08:05.850 |-ERROR [Thread-16][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-17 15:15:19.370 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 15:15:19.370 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 15:15:19.372 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 15:15:19.373 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 15:16:52.476 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 15:19:08.898 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-17 15:19:17.991 |-ERROR [Thread-16][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-17 15:25:42.166 |-WARN  [slave housekeeper][] -  com.zaxxer.hikari.pool.HikariPool [788] -| slave - Thread starvation or clock leap detected (housekeeper delta=5m47s602ms591µs200ns).
2025-07-17 15:25:42.166 |-WARN  [master housekeeper][] -  com.zaxxer.hikari.pool.HikariPool [788] -| master - Thread starvation or clock leap detected (housekeeper delta=5m47s603ms230µs200ns).
2025-07-17 15:25:42.176 |-ERROR [XNIO-1 task-1][a834f2e97f772148] -  com.sankuai.shangou.seashop.base.core.task.ReportTask [125] -| BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'whether_log_out' in 'field list'
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceUserMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceUserMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_user  ( user_id, nickname, phone,  registration_time,  whether_log_out, create_time, update_time )  VALUES (  ?, ?, ?,  ?,  ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'whether_log_out' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'whether_log_out' in 'field list'
2025-07-17 16:17:16.830 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 16:17:16.830 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 16:17:16.830 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 16:17:16.833 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 16:20:17.647 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 16:21:41.934 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 16:21:41.934 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 16:21:41.935 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 16:21:41.939 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-17 16:23:00.567 |-WARN  [main][] -  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext [599] -| Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'me.ahoo.cosid.spring.boot.starter.segment.CosIdSegmentAutoConfiguration': Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'cosid.segment-me.ahoo.cosid.spring.boot.starter.segment.SegmentIdProperties': Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [me.ahoo.cosid.spring.boot.starter.segment.SegmentIdProperties]: Constructor threw exception; nested exception is java.lang.ExceptionInInitializerError
2025-07-17 16:39:55.514 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 16:42:12.285 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-17 16:42:21.993 |-ERROR [Thread-16][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-17 16:54:31.070 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-17 16:54:31.070 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-17 16:54:31.071 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-17 16:54:31.075 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
