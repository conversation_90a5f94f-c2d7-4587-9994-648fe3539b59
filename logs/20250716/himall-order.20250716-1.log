2025-07-16 14:45:59.589 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-16 14:45:59.741 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 35400 (E:\work\himallWork\himall-order\seashop-order-server\target\classes started by Admin in E:\work\himallWork)
2025-07-16 14:45:59.741 |-DEBUG [main][] -  com.sankuai.shangou.seashop.order.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-16 14:45:59.742 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-16 14:46:00.158 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-order.yml, group=1.0.0] success
2025-07-16 14:46:00.158 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-16 14:46:02.500 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-16 14:46:03.779 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderCommentExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:46:03.779 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:46:03.781 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'pendingSettlementOrderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:46:03.781 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'platAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PlatAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:46:03.781 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'shopAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.ShopAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:46:05.718 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-16 14:46:05.774 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-16 14:46:06.147 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-16 14:46:07.686 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-16 14:46:07.690 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-16 14:46:08.007 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-16 14:46:08.007 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-16 14:46:08.008 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-16 14:46:08.008 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-16 14:46:12.948 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:46:14.955 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:46:22.282 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:46:22.282 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderDelayCheckListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-16 14:46:22.287 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:46:23.987 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:46:28.825 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:46:28.825 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderFailureListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-16 14:46:30.253 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-16 14:46:30.840 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-16 14:46:32.848 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-16 14:46:34.416 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-16 14:46:35.087 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:46:36.819 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:46:41.594 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:46:41.595 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-16 14:46:41.603 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:46:43.081 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:46:47.941 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:46:47.941 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-16 14:46:48.506 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-16 14:46:51.909 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-order) init on namesrv 124.71.221.178:9876
2025-07-16 14:46:57.329 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:46:59.196 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:47:04.409 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:47:04.409 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderPayNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-16 14:47:05.508 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:47:07.487 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:47:12.887 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:47:12.887 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-16 14:47:13.097 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:47:15.175 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:47:20.828 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:47:20.828 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-16 14:47:20.836 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:47:22.566 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:47:28.049 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:47:28.049 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-16 14:47:28.214 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:47:29.754 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:47:34.487 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:47:34.487 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-16 14:47:34.493 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:47:36.065 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:47:40.834 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:47:40.835 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeForRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-16 14:47:40.888 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:47:42.395 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:47:47.013 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:47:47.013 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-16 14:47:47.257 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.core.statemachine.config.OrderStateMachineConfiguration [127] -| @startuml
PAYING --> UNDER_PAY : CANCEL_PAY
PAYING --> UNDER_SEND : PAY_NOTIFY
UNDER_SEND --> UNDER_RECEIVE : DELIVERY
UNDER_RECEIVE --> FINISHED : CONFIRM_RECEIVE
UNDER_RECEIVE --> UNDER_RECEIVE : DELAY_RECEIVE
UNDER_PAY --> PAYING : INITIATE_PAY
UNDER_PAY --> CLOSED : CANCEL_ORDER
UNDER_PAY --> CLOSED : CLOSE
@enduml
2025-07-16 14:47:47.349 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:47:48.889 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:47:53.762 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:47:53.763 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-16 14:47:53.881 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:47:55.371 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:48:00.059 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:48:00.060 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-16 14:48:00.068 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:48:01.603 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:48:06.724 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:48:06.725 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:refundNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-16 14:48:08.282 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:48:09.838 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class java.lang.String
2025-07-16 14:48:14.435 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:48:14.435 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderReverseExceptionListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-16 14:48:14.983 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-16 14:48:15.081 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:48:16.565 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:48:21.516 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:48:21.516 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositPayStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_16
2025-07-16 14:48:21.554 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:48:23.155 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 14:48:28.329 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 14:48:28.329 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositReverseStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_17
2025-07-16 14:48:34.092 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-16 14:48:34.106 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-16 14:48:35.585 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:35400, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-16 14:48:36.494 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=341, lastTimeStamp=1752648515593}] - instanceId:[InstanceId{instanceId=*******:35400, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-16 14:48:36.667 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-order.__share__].
2025-07-16 14:48:36.670 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-16 14:48:36.670 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-16 14:48:36.670 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-order.__share__] jobSize:[0].
2025-07-16 14:48:39.707 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-16 14:48:43.197 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCommentTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@30ad845d[class com.sankuai.shangou.seashop.order.core.task.CommentTask#autoComment]
2025-07-16 14:48:43.197 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshProductCommentEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@295e3370[class com.sankuai.shangou.seashop.order.core.task.CommentTask#refreshProductCommentEs]
2025-07-16 14:48:43.197 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:ExecuteMqErrorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2668c100[class com.sankuai.shangou.seashop.order.core.task.MqErrorDataTask#executeMqErrorDataTask]
2025-07-16 14:48:43.197 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildOrderEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4dae054b[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildOrderEsDataTask]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildRefundEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@117dffcf[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildRefundEsDataTask]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCloseTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@49e09b23[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoClose]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoFinishTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@296023de[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoFinish]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindOrderNotPayTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1459ffe4[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindNotPay]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderBill, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6aa4e927[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderBill]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@27504ed3[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrder]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderItem, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@f06e734[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderItem]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindWaitDeliveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@54fb9fef[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindWaitDelivery]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundRecordStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@19dba986[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundRecordStatusTask]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderRefund, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3c584ab8[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderRefund]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@45f3540[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundStatusTask]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundCloseWhenDeliveryExpireTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@cd3d6c3[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundCloseWhenDeliveryExpire]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoConfirmArrival, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4800c946[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoConfirmArrival]
2025-07-16 14:48:43.198 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoAuditTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@36f35774[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoAudit]
2025-07-16 14:48:43.200 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:payReverseStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4bfb91df[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#payReverseStatusQueryTask]
2025-07-16 14:48:43.200 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:unpaidOrderPayStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@598c23ad[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#unpaidOrderPayStatusQueryTask]
2025-07-16 14:48:43.223 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSplitting, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@17088b23[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask#orderSplitting]
2025-07-16 14:48:43.223 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3bec3641[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask#orderSettlement]
2025-07-16 14:48:48.441 |-ERROR [Thread-16][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-16 14:48:48.615 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-16 14:48:48.692 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-16 14:48:48.734 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-16 14:48:48.929 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-16 14:48:49.238 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 14:48:49.238 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 14:48:49.615 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-order *******:8083 register finished
2025-07-16 14:48:50.992 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [61] -| Started StartApp in 177.961 seconds (JVM running for 181.75)
2025-07-16 14:48:51.006 |-INFO  [main][] -  adaPayInitConfig [32] -| 汇付支付初始化开始
2025-07-16 14:48:52.414 |-INFO  [main][] -  adaPayInitConfig [35] -| 汇付配置信息:AdaPayConfigModel(izOpen=null, appId=app_39b69a1c-d9ad-4e30-bb29-d80833b86885, apiKey=api_live_54054387-f98b-4f13-ad20-b6ebcbd07da4, apiMockKey=api_test_000fc218-bd56-4ea8-9609-8439687ad2bc, rsaPrivateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIYS3BfoZ2+g0eY21IZwvllVccKEcSwzrJUwLnWJ8vFvSM5mU5aPkMkhO1m5GR2aDJEz16hBuEK1ezzP73R5Sh6Qne2kXHWgCO6vKRWJ/PeAm8xBKddHKO3Xdg9MW1tVQoHA2pPV1ru2KOXT93YWOIKUlWTFpnelpgrIZLjZME2XAgMBAAECgYATy30LWpjK9meHIdlG8CZqch8VpRBAgnCcpjx1xiREWTXao2j79b5es7VbjeSTZkcsuQbCJNHbp4fGdrzX6YBzw/xR2gdnE/A4lRfcgvJ62wFBIbq4MZYevBmBX5Ng2AuxbdEbJ87Kd+zpFFHb6x0aB4aJjUSx5EpQaoiRuhBRIQJBAMWcgi44hvJQNnx9fVZZWs0ZyUDgf9hTVUEcJ+LyaUeNOXcsrP13NJ98PMDtSyhiVEsAmwugZDc2uWRbcbYUyBMCQQCtsE5m+UEH6ALg2lQUVyfuxORWb2OYXSeA3JBJN9RvzBo2WfQnzJO3Vs6BV2qcvqgUcKv+b4DwLPHODPmZyxztAkBvADwL1IrQ4AfLI/5cm7KqlPp8a97EWAMCoNsy2vISVBzceYbulaBEmdfSkzhthdZNjxiIjl7cuOuomMkl+0RrAkEAgEpEbszWmtdlIN5C0k9aAIPPwIRABS9xWT4RGPOy5uzTw6eHrsntpbLpjyGZbrNohMiAUcvcagpYhICS8GTVNQJBAMWPBEHATBX/Ka/5d/WC7SmIZ96d24NDJQDwlo0ufbZAp791HGoujq/w+SAySoQfA3VnVQyIkaqQlKou7Q5MS5A=, rsaProductKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB, deviceId=hishop1905, izDebug=true, prodMode=true)
2025-07-16 14:48:52.423 |-INFO  [main][] -  adaPayInitConfig [50] -| 汇付支付初始化完成
2025-07-16 14:48:54.235 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-16 14:48:54.236 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-order.yml, group=1.0.0
2025-07-16 14:48:54.238 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [37] -| 服务启动成功！
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-16 14:55:24.217 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.217 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-16 14:55:24.217 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.219 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 14:55:24.219 |-INFO  [Thread-11][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-16 14:55:24.219 |-INFO  [Thread-11][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-16 14:55:24.222 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-16 14:55:24.222 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-16 14:55:24.226 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=341, lastTimeStamp=1752648924226}] instanceId:[InstanceId{instanceId=*******:35400, stable=false}] @ namespace:[himall-order].
2025-07-16 14:55:24.248 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-16 14:59:35.703 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-16 14:59:35.853 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 19116 (E:\work\himallWork\himall-order\seashop-order-server\target\classes started by Admin in E:\work\himallWork)
2025-07-16 14:59:35.853 |-DEBUG [main][] -  com.sankuai.shangou.seashop.order.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-16 14:59:35.853 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-16 14:59:36.285 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-order.yml, group=1.0.0] success
2025-07-16 14:59:36.285 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-16 14:59:39.290 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-16 14:59:40.700 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderCommentExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:59:40.700 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:59:40.701 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'pendingSettlementOrderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:59:40.703 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'platAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PlatAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:59:40.703 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'shopAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.ShopAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 14:59:42.917 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-16 14:59:42.997 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-16 14:59:43.423 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-16 14:59:44.783 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-16 14:59:44.787 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-16 14:59:45.070 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-16 14:59:45.070 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-16 14:59:45.070 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-16 14:59:45.071 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-16 14:59:50.889 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 14:59:53.120 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:00:00.697 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:00:00.697 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderDelayCheckListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-16 15:00:00.703 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:00:02.337 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:00:07.390 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:00:07.390 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderFailureListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-16 15:00:08.782 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-16 15:00:09.420 |-INFO  [redisson-netty-2-4][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-16 15:00:11.292 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-16 15:00:12.384 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-16 15:00:12.946 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:00:14.493 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:00:19.326 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:00:19.327 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-16 15:00:19.334 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:00:20.979 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:00:25.923 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:00:25.924 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-16 15:00:26.434 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-16 15:00:29.688 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-order) init on namesrv 124.71.221.178:9876
2025-07-16 15:00:34.863 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:00:36.611 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:00:41.943 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:00:41.943 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderPayNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-16 15:00:42.694 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:00:44.449 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:00:49.337 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:00:49.337 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-16 15:00:49.457 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:00:51.051 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:00:56.046 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:00:56.046 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-16 15:00:56.055 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:00:57.622 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:01:02.252 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:01:02.253 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-16 15:01:02.409 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:01:03.965 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:01:08.908 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:01:08.908 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-16 15:01:08.917 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:01:10.460 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:01:15.229 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:01:15.229 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeForRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-16 15:01:15.285 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:01:17.434 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:01:22.779 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:01:22.779 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-16 15:01:23.028 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.core.statemachine.config.OrderStateMachineConfiguration [127] -| @startuml
UNDER_RECEIVE --> FINISHED : CONFIRM_RECEIVE
UNDER_RECEIVE --> UNDER_RECEIVE : DELAY_RECEIVE
UNDER_SEND --> UNDER_RECEIVE : DELIVERY
UNDER_PAY --> CLOSED : CANCEL_ORDER
UNDER_PAY --> CLOSED : CLOSE
UNDER_PAY --> PAYING : INITIATE_PAY
PAYING --> UNDER_PAY : CANCEL_PAY
PAYING --> UNDER_SEND : PAY_NOTIFY
@enduml
2025-07-16 15:01:23.118 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:01:24.673 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:01:29.755 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:01:29.755 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-16 15:01:29.878 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:01:31.621 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:01:37.490 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:01:37.490 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-16 15:01:37.498 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:01:39.386 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:01:45.670 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:01:45.670 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:refundNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-16 15:01:47.361 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:01:49.023 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class java.lang.String
2025-07-16 15:01:54.122 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:01:54.122 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderReverseExceptionListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-16 15:01:54.794 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-16 15:01:54.904 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:01:56.804 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:02:01.846 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:02:01.846 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositPayStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_16
2025-07-16 15:02:01.877 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-16 15:02:03.648 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-16 15:02:10.826 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-16 15:02:10.826 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositReverseStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_17
2025-07-16 15:02:17.552 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-16 15:02:17.572 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-16 15:02:19.648 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:19116, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-16 15:02:20.625 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=341, lastTimeStamp=1752649339664}] - instanceId:[InstanceId{instanceId=*******:19116, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-16 15:02:20.799 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-order.__share__].
2025-07-16 15:02:20.802 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-16 15:02:20.802 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-16 15:02:20.802 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-order.__share__] jobSize:[0].
2025-07-16 15:02:23.749 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-16 15:02:27.857 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCommentTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@620af7f3[class com.sankuai.shangou.seashop.order.core.task.CommentTask#autoComment]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshProductCommentEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4064fbdf[class com.sankuai.shangou.seashop.order.core.task.CommentTask#refreshProductCommentEs]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:ExecuteMqErrorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3feedaec[class com.sankuai.shangou.seashop.order.core.task.MqErrorDataTask#executeMqErrorDataTask]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildOrderEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@19a834b[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildOrderEsDataTask]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildRefundEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@349a59e4[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildRefundEsDataTask]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCloseTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@53c7f6ab[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoClose]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindOrderNotPayTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a377f85[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindNotPay]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoFinishTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4f623753[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoFinish]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderBill, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@29511222[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderBill]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@454fcb0d[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrder]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderItem, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@64a55f76[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderItem]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindWaitDeliveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3a23241e[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindWaitDelivery]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1b42e17a[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundStatusTask]
2025-07-16 15:02:27.858 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundRecordStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@206e46bc[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundRecordStatusTask]
2025-07-16 15:02:27.859 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderRefund, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6e7ee328[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderRefund]
2025-07-16 15:02:27.859 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoConfirmArrival, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4d5e292d[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoConfirmArrival]
2025-07-16 15:02:27.859 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundCloseWhenDeliveryExpireTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@419b48cd[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundCloseWhenDeliveryExpire]
2025-07-16 15:02:27.859 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoAuditTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1312bde4[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoAudit]
2025-07-16 15:02:27.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:unpaidOrderPayStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@a6c6b0d[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#unpaidOrderPayStatusQueryTask]
2025-07-16 15:02:27.861 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:payReverseStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@36472c2c[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#payReverseStatusQueryTask]
2025-07-16 15:02:27.891 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSplitting, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d01c709[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask#orderSplitting]
2025-07-16 15:02:27.891 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2bfaa2d0[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask#orderSettlement]
2025-07-16 15:02:33.054 |-INFO  [Thread-16][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-16 15:02:33.216 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-16 15:02:33.303 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-16 15:02:33.345 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-16 15:02:33.546 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-16 15:02:33.908 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 15:02:33.909 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 15:02:34.202 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-order *******:8083 register finished
2025-07-16 15:02:35.353 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [61] -| Started StartApp in 185.633 seconds (JVM running for 188.69)
2025-07-16 15:02:35.361 |-INFO  [main][] -  adaPayInitConfig [32] -| 汇付支付初始化开始
2025-07-16 15:02:36.758 |-INFO  [main][] -  adaPayInitConfig [35] -| 汇付配置信息:AdaPayConfigModel(izOpen=null, appId=app_39b69a1c-d9ad-4e30-bb29-d80833b86885, apiKey=api_live_54054387-f98b-4f13-ad20-b6ebcbd07da4, apiMockKey=api_test_000fc218-bd56-4ea8-9609-8439687ad2bc, rsaPrivateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIYS3BfoZ2+g0eY21IZwvllVccKEcSwzrJUwLnWJ8vFvSM5mU5aPkMkhO1m5GR2aDJEz16hBuEK1ezzP73R5Sh6Qne2kXHWgCO6vKRWJ/PeAm8xBKddHKO3Xdg9MW1tVQoHA2pPV1ru2KOXT93YWOIKUlWTFpnelpgrIZLjZME2XAgMBAAECgYATy30LWpjK9meHIdlG8CZqch8VpRBAgnCcpjx1xiREWTXao2j79b5es7VbjeSTZkcsuQbCJNHbp4fGdrzX6YBzw/xR2gdnE/A4lRfcgvJ62wFBIbq4MZYevBmBX5Ng2AuxbdEbJ87Kd+zpFFHb6x0aB4aJjUSx5EpQaoiRuhBRIQJBAMWcgi44hvJQNnx9fVZZWs0ZyUDgf9hTVUEcJ+LyaUeNOXcsrP13NJ98PMDtSyhiVEsAmwugZDc2uWRbcbYUyBMCQQCtsE5m+UEH6ALg2lQUVyfuxORWb2OYXSeA3JBJN9RvzBo2WfQnzJO3Vs6BV2qcvqgUcKv+b4DwLPHODPmZyxztAkBvADwL1IrQ4AfLI/5cm7KqlPp8a97EWAMCoNsy2vISVBzceYbulaBEmdfSkzhthdZNjxiIjl7cuOuomMkl+0RrAkEAgEpEbszWmtdlIN5C0k9aAIPPwIRABS9xWT4RGPOy5uzTw6eHrsntpbLpjyGZbrNohMiAUcvcagpYhICS8GTVNQJBAMWPBEHATBX/Ka/5d/WC7SmIZ96d24NDJQDwlo0ufbZAp791HGoujq/w+SAySoQfA3VnVQyIkaqQlKou7Q5MS5A=, rsaProductKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB, deviceId=hishop1905, izDebug=true, prodMode=true)
2025-07-16 15:02:36.768 |-INFO  [main][] -  adaPayInitConfig [50] -| 汇付支付初始化完成
2025-07-16 15:02:38.618 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-16 15:02:38.619 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-order.yml, group=1.0.0
2025-07-16 15:02:38.620 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [37] -| 服务启动成功！
2025-07-16 15:12:20.086 |-INFO  [XNIO-1 task-1][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 15:12:20.762 |-ERROR [XNIO-1 task-1][1c7317816791c4b4] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [84] -| [GLOBAL_EXCEPTION] BusinessException:
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 支付记录不存在
	at com.sankuai.shangou.seashop.pay.core.service.impl.WxShippingBizImpl.getOrder(WxShippingBizImpl.java:148) ~[classes/:?]
	at com.sankuai.shangou.seashop.order.core.thrift.impl.OrderCmdController.queryWxStatus(OrderCmdController.java:393) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:503) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-16 15:44:50.299 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-16 15:44:50.299 |-INFO  [Thread-11][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-16 15:44:50.299 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-16 15:44:50.300 |-INFO  [Thread-11][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-16 15:44:50.299 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-16 15:44:50.300 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-16 15:44:50.300 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.301 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.301 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.301 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.301 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.301 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.301 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.301 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.301 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-16 15:44:50.303 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.303 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.303 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.303 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-16 15:44:50.305 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-16 15:44:50.309 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=341, lastTimeStamp=1752651890308}] instanceId:[InstanceId{instanceId=*******:19116, stable=false}] @ namespace:[himall-order].
2025-07-16 15:44:50.437 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
