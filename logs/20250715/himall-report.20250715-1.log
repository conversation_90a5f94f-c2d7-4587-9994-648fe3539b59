2025-07-15 08:39:47.017 |-DEBUG [XNIO-1 task-3][69d9a8ade16e1fbd] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/visitsEcharts method:POST start
2025-07-15 08:39:47.017 |-DEBUG [XNIO-1 task-2][38376cb5ddc9b30a] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/summary method:POST start
2025-07-15 08:39:47.058 |-INFO  [XNIO-1 task-3][69d9a8ade16e1fbd] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visitsEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-15 08:39:47.058 |-INFO  [XNIO-1 task-2][38376cb5ddc9b30a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visits 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-15 08:39:47.106 |-INFO  [XNIO-1 task-3][69d9a8ade16e1fbd] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visitsEcharts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"被访问商品数","data":[0]},{"name":"动销商品数","data":[0]}]},"code":0,"message":null}
2025-07-15 08:39:47.107 |-DEBUG [XNIO-1 task-3][69d9a8ade16e1fbd] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/visitsEcharts cost:90ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-15 08:39:47.131 |-INFO  [XNIO-1 task-2][38376cb5ddc9b30a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visits success. 请求结果. response={"data":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":null}},"code":0,"message":null}
2025-07-15 08:39:47.134 |-DEBUG [XNIO-1 task-2][38376cb5ddc9b30a] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/summary cost:114ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-15 08:39:47.318 |-DEBUG [XNIO-1 task-2][f55b4deeb0a8387e] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/categoryEcharts method:POST start
2025-07-15 08:39:47.321 |-INFO  [XNIO-1 task-2][f55b4deeb0a8387e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| categoryEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-15 08:39:47.344 |-INFO  [XNIO-1 task-2][f55b4deeb0a8387e] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| categoryEcharts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"类目销量","data":[]},{"name":"类目销售额","data":[]}]},"code":0,"message":null}
2025-07-15 08:39:47.345 |-DEBUG [XNIO-1 task-2][f55b4deeb0a8387e] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/categoryEcharts cost:27ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-15 08:43:03.418 |-DEBUG [XNIO-1 task-3][6e41443ec42cd6dc] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/province/echarts method:POST start
2025-07-15 08:43:03.418 |-DEBUG [XNIO-1 task-2][019ea75b932cc7ac] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/increase/echarts method:POST start
2025-07-15 08:43:03.474 |-DEBUG [XNIO-1 task-3][6e41443ec42cd6dc] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/province/echarts cost:56ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:03.475 |-DEBUG [XNIO-1 task-2][019ea75b932cc7ac] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/increase/echarts cost:57ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:07.533 |-DEBUG [XNIO-1 task-2][328455d6b7f3b552] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:07.597 |-DEBUG [XNIO-1 task-2][328455d6b7f3b552] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:64ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:14.946 |-DEBUG [XNIO-1 task-2][1e50a2624bb15a7f] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:14.987 |-DEBUG [XNIO-1 task-2][1e50a2624bb15a7f] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:42ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-01-01","end":"2023-01-31","day":"2023-01-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:17.888 |-DEBUG [XNIO-1 task-2][07d8c1e4e31dc75c] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:17.930 |-DEBUG [XNIO-1 task-2][07d8c1e4e31dc75c] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:42ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-02-01","end":"2023-02-28","day":"2023-02-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:19.967 |-DEBUG [XNIO-1 task-2][2148bac33a92dd82] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:20.009 |-DEBUG [XNIO-1 task-2][2148bac33a92dd82] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:42ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-03-01","end":"2023-03-31","day":"2023-03-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:22.181 |-DEBUG [XNIO-1 task-2][e20bdbd291aef826] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:22.221 |-DEBUG [XNIO-1 task-2][e20bdbd291aef826] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:40ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2025-07-01","end":"2025-07-31","day":"2025-07-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:22.797 |-DEBUG [XNIO-1 task-2][4759b909ae1a2068] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:22.839 |-DEBUG [XNIO-1 task-2][4759b909ae1a2068] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:41ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2025-07-01","end":"2025-07-31","day":"2025-07-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:26.749 |-DEBUG [XNIO-1 task-2][bf7b0ad99ad0b447] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:26.819 |-DEBUG [XNIO-1 task-2][bf7b0ad99ad0b447] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:70ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-04-01","end":"2023-04-30","day":"2023-04-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:28.983 |-DEBUG [XNIO-1 task-2][fdcd9935362850b9] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:29.030 |-DEBUG [XNIO-1 task-2][fdcd9935362850b9] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:47ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-05-01","end":"2023-05-31","day":"2023-05-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:31.566 |-DEBUG [XNIO-1 task-2][605745ae610d8910] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:31.605 |-DEBUG [XNIO-1 task-2][605745ae610d8910] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:39ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-06-01","end":"2023-06-30","day":"2023-06-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:33.997 |-DEBUG [XNIO-1 task-2][c607e83577d05bd1] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:34.039 |-DEBUG [XNIO-1 task-2][c607e83577d05bd1] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:42ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-07-01","end":"2023-07-31","day":"2023-07-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:36.704 |-DEBUG [XNIO-1 task-2][62113e0e7bc35ccf] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:36.751 |-DEBUG [XNIO-1 task-2][62113e0e7bc35ccf] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:47ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-08-01","end":"2023-08-31","day":"2023-08-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:40.231 |-DEBUG [XNIO-1 task-2][3d079ed54dc9d302] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:40.271 |-DEBUG [XNIO-1 task-2][3d079ed54dc9d302] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:40ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2023-09-01","end":"2023-09-30","day":"2023-09-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:43:54.020 |-DEBUG [XNIO-1 task-2][f1aa9556bbfb631c] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:43:54.065 |-DEBUG [XNIO-1 task-2][f1aa9556bbfb631c] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:44ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"DAY","start":"2022-07-01","end":"2022-08-31","day":"2023-09-01","begin":"2022-07-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:44:07.902 |-DEBUG [XNIO-1 task-2][d1fb3ed5ee9b1363] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:44:07.969 |-DEBUG [XNIO-1 task-2][d1fb3ed5ee9b1363] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:67ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"DAY","start":"2023-01-01","end":"2025-02-28","day":"2023-09-01","begin":"2023-01-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:44:14.175 |-DEBUG [XNIO-1 task-2][57e71268b02236ae] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:44:14.216 |-DEBUG [XNIO-1 task-2][57e71268b02236ae] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:41ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"DAY","start":"2023-01-01","end":"2025-02-28","day":"2023-09-01","begin":"2023-01-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:44:16.268 |-DEBUG [XNIO-1 task-2][1103b37a60c9f8c5] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:44:16.331 |-DEBUG [XNIO-1 task-2][1103b37a60c9f8c5] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:63ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"DAY","start":"2023-01-01","end":"2025-02-28","day":"2023-09-01","begin":"2023-01-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:44:20.756 |-DEBUG [XNIO-1 task-2][25d42424da149cb8] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:44:20.803 |-DEBUG [XNIO-1 task-2][25d42424da149cb8] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:47ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"DAY","start":"2023-01-01","end":"2025-02-28","day":"2023-09-01","begin":"2023-01-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:44:35.282 |-DEBUG [XNIO-1 task-2][d78dd21ae1fad339] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/query method:POST start
2025-07-15 08:44:35.290 |-INFO  [XNIO-1 task-2][d78dd21ae1fad339] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| query 请求入参. request={"shopId":162,"range":"MONTH","start":1751212800000,"end":1753804800000,"categoryName":"","productName":"","sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-15 08:44:35.350 |-INFO  [XNIO-1 task-2][d78dd21ae1fad339] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| query success. 请求结果. response={"data":{"pageNo":1,"pageSize":10,"totalCount":0,"orders":[],"data":[null]},"code":0,"message":null}
2025-07-15 08:44:35.353 |-DEBUG [XNIO-1 task-2][d78dd21ae1fad339] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/query cost:71ms input:
{"operationShopId":0,"pageSize":10,"pageNo":1,"shopId":162,"range":"MONTH","start":"2025-06-30","end":"2025-07-30","categoryName":"","productName":""}
2025-07-15 08:44:36.211 |-DEBUG [XNIO-1 task-2][9bb9f1713a6e6ca3] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/summary method:POST start
2025-07-15 08:44:36.213 |-INFO  [XNIO-1 task-2][9bb9f1713a6e6ca3] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visits 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-15 08:44:36.273 |-INFO  [XNIO-1 task-2][9bb9f1713a6e6ca3] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visits success. 请求结果. response={"data":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":{"visitsProducts":0,"salesProducts":0,"visitsCount":null,"VisitsUsers":null,"cartQuantity":null,"orderQuantity":null,"paymentQuantity":null,"prev":null}},"code":0,"message":null}
2025-07-15 08:44:36.274 |-DEBUG [XNIO-1 task-2][9bb9f1713a6e6ca3] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/summary cost:63ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-15 08:44:36.501 |-DEBUG [XNIO-1 task-2][1613c003e34fd681] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/visitsEcharts method:POST start
2025-07-15 08:44:36.503 |-INFO  [XNIO-1 task-2][1613c003e34fd681] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| visitsEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-15 08:44:36.509 |-DEBUG [XNIO-1 task-3][4c246dd78ed241ef] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/product/categoryEcharts method:POST start
2025-07-15 08:44:36.512 |-INFO  [XNIO-1 task-3][4c246dd78ed241ef] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| categoryEcharts 请求入参. request={"range":"MONTH","shopId":162,"day":null,"start":1751299200000,"end":1753891200000}
2025-07-15 08:44:36.523 |-INFO  [XNIO-1 task-2][1613c003e34fd681] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| visitsEcharts success. 请求结果. response={"data":{"xAxis":{"data":["2025-07-01"]},"series":[{"name":"被访问商品数","data":[0]},{"name":"动销商品数","data":[0]}]},"code":0,"message":null}
2025-07-15 08:44:36.526 |-DEBUG [XNIO-1 task-2][1613c003e34fd681] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/visitsEcharts cost:24ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-15 08:44:36.553 |-INFO  [XNIO-1 task-3][4c246dd78ed241ef] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| categoryEcharts success. 请求结果. response={"data":{"xAxis":{"data":[]},"series":[{"name":"类目销量","data":[]},{"name":"类目销售额","data":[]}]},"code":0,"message":null}
2025-07-15 08:44:36.554 |-DEBUG [XNIO-1 task-3][4c246dd78ed241ef] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/product/categoryEcharts cost:45ms input:
{"range":"MONTH","shopId":162,"start":"2025-07-01","end":"2025-07-31","endDate":"2025-07-31 00:00:00","startDate":"2025-07-01 00:00:00"}
2025-07-15 08:52:20.398 |-DEBUG [XNIO-1 task-3][908d5ca7bd536287] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:52:20.442 |-DEBUG [XNIO-1 task-3][908d5ca7bd536287] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:44ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"DAY","start":"2023-01-01","end":"2025-02-28","day":"2023-09-01","begin":"2023-01-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:53:38.254 |-DEBUG [XNIO-1 task-3][bd23804d2c227979] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:53:38.297 |-DEBUG [XNIO-1 task-3][bd23804d2c227979] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:43ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"DAY","start":"2023-01-01","end":"2025-02-28","day":"2023-09-01","begin":"2023-01-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:55:20.827 |-DEBUG [XNIO-1 task-3][69c71726ece08d20] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 08:59:59.225 |-WARN  [slave housekeeper][] -  com.zaxxer.hikari.pool.HikariPool [788] -| slave - Thread starvation or clock leap detected (housekeeper delta=4m48s787ms839µs100ns).
2025-07-15 08:59:59.222 |-WARN  [master housekeeper][] -  com.zaxxer.hikari.pool.HikariPool [788] -| master - Thread starvation or clock leap detected (housekeeper delta=4m48s788ms919µs300ns).
2025-07-15 08:59:59.244 |-DEBUG [XNIO-1 task-3][69c71726ece08d20] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:278416ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"DAY","start":"2023-01-01","end":"2025-02-28","day":"2023-09-01","begin":"2023-01-01","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 08:59:59.310 |-INFO  [Thread-91][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-15 08:59:59.310 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-15 08:59:59.310 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-15 08:59:59.310 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 08:59:59.310 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-15 08:59:59.310 |-INFO  [Thread-91][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 08:59:59.310 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.310 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.310 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.310 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.310 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.310 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-15 08:59:59.310 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.312 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.312 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-15 08:59:59.312 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.312 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.312 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.312 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.312 |-INFO  [Thread-92][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 08:59:59.313 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=124, lastTimeStamp=1752541199313}] instanceId:[InstanceId{instanceId=*******:26752, stable=false}] @ namespace:[himall-report].
2025-07-15 08:59:59.341 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 08:59:59.345 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-15 08:59:59.351 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-15 08:59:59.436 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 08:59:59.436 |-INFO  [Thread-106][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-15 09:00:00.965 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-15 09:00:00.965 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-15 09:00:00.966 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-15 09:00:00.967 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-15 09:00:00.967 |-INFO  [Thread-105][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-15 09:00:00.967 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-15 09:00:00.996 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-15 09:00:01.021 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-15 09:00:01.030 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-15 09:00:01.034 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-15 09:00:01.034 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-15 09:00:01.034 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-15 09:00:01.038 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-15 09:00:01.039 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-15 09:00:01.039 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-15 10:51:26.429 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-15 10:51:26.653 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 4716 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-15 10:51:26.660 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-15 10:51:26.661 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-15 10:51:27.887 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-15 10:51:27.887 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-15 10:51:35.706 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-15 10:51:35.804 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-15 10:51:36.255 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-15 10:51:37.119 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-15 10:51:37.129 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-15 10:51:37.418 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-15 10:51:37.419 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-15 10:51:37.419 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-15 10:51:37.419 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-15 10:51:40.793 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-15 10:51:42.934 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-15 10:51:44.305 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-15 10:51:45.241 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@f3ae18f'
2025-07-15 10:51:45.718 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-15 10:51:45.756 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-15 10:51:45.929 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-15 10:51:45.972 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-15 10:51:46.020 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-15 10:51:46.066 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-15 10:51:46.107 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-15 10:51:46.148 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-15 10:51:46.194 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-15 10:51:46.269 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-15 10:51:46.294 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-15 10:51:46.321 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-15 10:51:46.357 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-15 10:51:46.385 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-15 10:51:46.411 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-15 10:51:46.445 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-15 10:51:46.500 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-15 10:51:46.537 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-15 10:51:46.542 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-15 10:51:46.549 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-15 10:51:48.696 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-15 10:51:50.502 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-15 10:51:57.731 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-15 10:52:08.022 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-15 10:52:10.485 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-15 10:52:16.304 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-15 10:52:16.326 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-15 10:52:18.334 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:4716, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-15 10:52:19.020 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=125, lastTimeStamp=1752547938340}] - instanceId:[InstanceId{instanceId=*******:4716, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-15 10:52:19.208 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-15 10:52:19.211 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-15 10:52:19.211 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-15 10:52:19.212 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-15 10:52:24.834 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6e9698cf[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-15 10:52:24.834 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@cd2f8c0[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-15 10:52:24.834 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4e23fd4f[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-15 10:52:24.834 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@33b92d63[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-15 10:52:24.834 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@69339b3b[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-15 10:52:24.835 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@723c6a25[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-15 10:52:24.835 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@232438a8[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-15 10:52:24.835 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@590cdb9a[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-15 10:52:24.835 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2c9369c3[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-15 10:52:24.835 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3def5336[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-15 10:52:25.343 |-INFO  [Thread-16][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-15 10:52:25.424 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-15 10:52:25.439 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-15 10:52:25.497 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-15 10:52:25.566 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-15 10:52:25.598 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-15 10:52:25.748 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-15 10:52:26.255 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-15 10:52:26.256 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-15 10:52:26.564 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8085 register finished
2025-07-15 10:52:27.897 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 70.795 seconds (JVM running for 73.642)
2025-07-15 10:52:27.905 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-15 10:52:27.908 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-15 10:52:29.614 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-15 10:52:29.615 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-15 10:52:29.617 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-15 10:53:10.421 |-INFO  [XNIO-1 task-1][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 10:53:10.605 |-DEBUG [XNIO-1 task-1][0ab0790a2f75a43a] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 10:53:15.522 |-DEBUG [XNIO-1 task-1][0ab0790a2f75a43a] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:4930ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 10:59:13.455 |-DEBUG [XNIO-1 task-1][347cdd544c830162] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 10:59:15.627 |-DEBUG [XNIO-1 task-1][347cdd544c830162] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:2172ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 11:00:11.247 |-DEBUG [XNIO-1 task-1][93a7c69be76668f0] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/increase/echarts method:POST start
2025-07-15 11:00:11.339 |-DEBUG [XNIO-1 task-1][93a7c69be76668f0] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/increase/echarts cost:92ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 11:00:11.552 |-DEBUG [XNIO-1 task-1][c6a7f2d5321241d0] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/province/echarts method:POST start
2025-07-15 11:00:11.584 |-DEBUG [XNIO-1 task-1][c6a7f2d5321241d0] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/province/echarts cost:32ms input:
{"range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 11:00:13.173 |-DEBUG [XNIO-1 task-1][d9e73b3e50e43b1f] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 11:00:15.726 |-DEBUG [XNIO-1 task-1][d9e73b3e50e43b1f] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:2553ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 13:59:26.408 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-15 13:59:26.408 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-15 13:59:26.408 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 13:59:26.408 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.408 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.408 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.409 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.408 |-INFO  [Thread-11][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-15 13:59:26.408 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.409 |-INFO  [Thread-11][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.409 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 13:59:26.410 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-15 13:59:26.412 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=125, lastTimeStamp=1752559166412}] instanceId:[InstanceId{instanceId=*******:4716, stable=false}] @ namespace:[himall-report].
2025-07-15 13:59:26.442 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 13:59:26.446 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-15 13:59:26.452 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-15 13:59:26.564 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 13:59:26.565 |-INFO  [Thread-16][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-15 13:59:26.874 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-15 13:59:26.874 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-15 13:59:26.874 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-15 13:59:26.875 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-15 13:59:26.875 |-INFO  [Thread-15][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-15 13:59:26.875 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-15 13:59:26.898 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-15 13:59:29.942 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-15 13:59:29.951 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-15 13:59:29.955 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-15 13:59:29.955 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-15 13:59:29.955 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-15 13:59:29.957 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-15 13:59:29.958 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-15 13:59:29.958 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-15 14:04:59.287 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-15 14:04:59.449 |-INFO  [main][] -  com.hishop.himall.report.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 22540 (E:\work\himallWork\himall-report\himall-report-server\target\classes started by Admin in E:\work\himallWork)
2025-07-15 14:04:59.454 |-DEBUG [main][] -  com.hishop.himall.report.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-15 14:04:59.455 |-INFO  [main][] -  com.hishop.himall.report.StartApp [638] -| The following 1 profile is active: "develop_local"
2025-07-15 14:04:59.931 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-report.yml, group=1.0.0] success
2025-07-15 14:04:59.932 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-15 14:05:04.666 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-15 14:05:04.742 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-15 14:05:05.150 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-15 14:05:06.302 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-15 14:05:06.307 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-15 14:05:06.527 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-15 14:05:06.528 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-15 14:05:06.528 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-15 14:05:06.528 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-15 14:05:09.518 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-15 14:05:11.501 |-INFO  [redisson-netty-1-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-15 14:05:13.264 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-15 14:05:13.972 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@362109d0'
2025-07-15 14:05:14.314 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomMapper.xml]'
2025-07-15 14:05:14.342 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportCustomRecordMapper.xml]'
2025-07-15 14:05:14.464 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportProductTradeMapper.xml]'
2025-07-15 14:05:14.487 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportShopTradeMapper.xml]'
2025-07-15 14:05:14.508 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCartMapper.xml]'
2025-07-15 14:05:14.528 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceCouponMapper.xml]'
2025-07-15 14:05:14.546 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowProductMapper.xml]'
2025-07-15 14:05:14.565 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceFollowShopMapper.xml]'
2025-07-15 14:05:14.585 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderBillMapper.xml]'
2025-07-15 14:05:14.621 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderItemMapper.xml]'
2025-07-15 14:05:14.640 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceOrderMapper.xml]'
2025-07-15 14:05:14.656 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceProductMapper.xml]'
2025-07-15 14:05:14.678 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRefundMapper.xml]'
2025-07-15 14:05:14.696 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceRegionMapper.xml]'
2025-07-15 14:05:14.712 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceShopMapper.xml]'
2025-07-15 14:05:14.732 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceUserMapper.xml]'
2025-07-15 14:05:14.762 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportSourceVisitMapper.xml]'
2025-07-15 14:05:14.783 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportUserTradeMapper.xml]'
2025-07-15 14:05:14.788 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatPortraitMapper.xml]'
2025-07-15 14:05:14.794 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-report\himall-report-dao\target\classes\mapper\ReportWechatVisitMapper.xml]'
2025-07-15 14:05:16.798 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-15 14:05:18.317 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-15 14:05:24.877 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-report) init on namesrv 124.71.221.178:9876
2025-07-15 14:05:34.263 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-15 14:05:35.571 |-DEBUG [main][] -  com.hishop.starter.web.autoconfiguration.ServiceConfig [51] -| [TASK] custom threadGroupName:TASK poolSize:8 maxPoolSize:256 queueCapacity:10240
2025-07-15 14:05:40.412 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-15 14:05:40.425 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-15 14:05:42.273 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:22540, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-15 14:05:42.926 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=125, lastTimeStamp=1752559542279}] - instanceId:[InstanceId{instanceId=*******:22540, stable=false}] - machineBit:[20] @ namespace:[himall-report].
2025-07-15 14:05:43.091 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-report.__share__].
2025-07-15 14:05:43.094 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-15 14:05:43.094 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-report.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-15 14:05:43.095 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-report.__share__] jobSize:[0].
2025-07-15 14:05:48.642 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@bdc5584[class com.hishop.himall.report.core.task.ProductTask#withMonth]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1f6fe574[class com.hishop.himall.report.core.task.ProductTask#withDate]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:productWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4e6b3894[class com.hishop.himall.report.core.task.ProductTask#withWeek]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5b2728db[class com.hishop.himall.report.core.task.ShopTask#withMonth]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@32be264e[class com.hishop.himall.report.core.task.ShopTask#withDate]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:shopWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@521a3128[class com.hishop.himall.report.core.task.ShopTask#withWeek]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@45220c67[class com.hishop.himall.report.core.task.UserTask#withMonth]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@272185e8[class com.hishop.himall.report.core.task.UserTask#withDate]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:userWithWeek, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@64ee4b14[class com.hishop.himall.report.core.task.UserTask#withWeek]
2025-07-15 14:05:48.643 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:WechatTaskSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@67feb5d0[class com.hishop.himall.report.core.task.WechatTask#execute]
2025-07-15 14:05:49.099 |-INFO  [Thread-16][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-15 14:05:49.167 |-DEBUG [main][] -  com.hishop.starter.web.filter.RequestLogFilter [242] -| Filter 'requestLogFilter' configured for use
2025-07-15 14:05:49.180 |-DEBUG [main][] -  org.springframework.web.filter.CorsFilter [242] -| Filter 'corsWebFilter' configured for use
2025-07-15 14:05:49.230 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-15 14:05:49.291 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-15 14:05:49.320 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-15 14:05:49.502 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-15 14:05:49.975 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-15 14:05:49.976 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-15 14:05:50.565 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-report *******:8085 register finished
2025-07-15 14:05:51.808 |-INFO  [main][] -  com.hishop.himall.report.StartApp [61] -| Started StartApp in 59.135 seconds (JVM running for 62.104)
2025-07-15 14:05:51.814 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-15 14:05:51.816 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-15 14:05:53.466 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-report.yml, group=1.0.0
2025-07-15 14:05:53.467 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-15 14:05:53.469 |-INFO  [main][] -  com.hishop.himall.report.StartApp [25] -| 服务启动成功！
2025-07-15 14:07:50.131 |-INFO  [XNIO-1 task-1][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 14:07:50.281 |-DEBUG [XNIO-1 task-1][365ad66541ae03a6] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/shop/query method:POST start
2025-07-15 14:07:51.844 |-DEBUG [XNIO-1 task-1][365ad66541ae03a6] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/shop/query cost:1575ms input:
{"pageNo":1,"pageSize":10,"total":0,"shopName":"","range":"MONTH","start":"2025-07-01","end":"2025-07-31","operatorId":1,"operatorName":"admin","operatorShopId":0}
2025-07-15 14:39:58.933 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.259 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.311 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:378ms input:
{"shopId":162,"shopName":"官方自营店_1","createTime":"2024-02-26 02:35:27","updateTime":"2024-12-09 16:00:00"}
2025-07-15 14:39:59.331 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.385 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.387 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:56ms input:
{"shopId":1000057,"shopName":"","createTime":"2025-06-26 07:41:00","updateTime":"2025-06-26 07:41:00"}
2025-07-15 14:39:59.389 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.443 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.444 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:55ms input:
{"shopId":1000059,"shopName":"","createTime":"2025-06-26 08:19:37","updateTime":"2025-06-26 08:19:37"}
2025-07-15 14:39:59.447 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.501 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.503 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:56ms input:
{"shopId":1000060,"shopName":"","createTime":"2025-06-26 08:22:00","updateTime":"2025-06-26 08:22:00"}
2025-07-15 14:39:59.508 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.560 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.562 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:54ms input:
{"shopId":1000061,"shopName":"","createTime":"2025-06-30 07:26:12","updateTime":"2025-06-30 07:26:12"}
2025-07-15 14:39:59.564 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.617 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.619 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:54ms input:
{"shopId":1000062,"shopName":"","createTime":"2025-07-02 10:36:31","updateTime":"2025-07-02 10:36:31"}
2025-07-15 14:39:59.621 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.678 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.680 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:59ms input:
{"shopId":1000063,"shopName":"","createTime":"2025-07-03 10:59:36","updateTime":"2025-07-03 10:59:36"}
2025-07-15 14:39:59.683 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.738 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.741 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:58ms input:
{"shopId":1000064,"shopName":"","createTime":"2025-07-04 00:37:36","updateTime":"2025-07-04 00:37:36"}
2025-07-15 14:39:59.744 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.796 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.GeneratedMethodAccessor43.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor43.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.799 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:55ms input:
{"shopId":1000065,"shopName":"18300000001","createTime":"2025-07-04 01:32:42","updateTime":"2025-07-04 01:32:42"}
2025-07-15 14:39:59.801 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.854 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.GeneratedMethodAccessor43.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor43.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.858 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:57ms input:
{"shopId":1000066,"shopName":"","createTime":"2025-07-04 01:39:29","updateTime":"2025-07-04 01:39:29"}
2025-07-15 14:39:59.861 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [56] -| [REQ] uri:/himall-report/report/createSourceShop method:POST start
2025-07-15 14:39:59.921 |-ERROR [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.advice.GlobalExceptionHandlerAdvice [116] -| [GLOBAL_EXCEPTION] 服务异常:
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
### The error may exist in com/hishop/himall/report/dao/mapper/ReportSourceShopMapper.java (best guess)
### The error may involve com.hishop.himall.report.dao.mapper.ReportSourceShopMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO report_source_shop  ( shop_id, shop_name,    create_time, update_time )  VALUES (  ?, ?,    ?, ?  )
### Cause: java.sql.SQLException: Field 'province_id' doesn't have a default value
; Field 'province_id' doesn't have a default value; nested exception is java.sql.SQLException: Field 'province_id' doesn't have a default value
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:248) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.33.jar:5.3.33]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy171.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:149) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:90) ~[mybatis-plus-core-3.5.6.jar:3.5.6]
	at com.sun.proxy.$Proxy177.insert(Unknown Source) ~[?:?]
	at com.hishop.himall.report.core.service.impl.ReportServiceImpl.createSourceShop(ReportServiceImpl.java:397) ~[classes/:?]
	at com.hishop.himall.report.core.web.impl.ReportController.createSourceShop(ReportController.java:239) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [seashop-base-boot-1.0.1-20250710.095516-55.jar:1.0.1-SNAPSHOT]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.sql.SQLException: Field 'province_id' doesn't have a default value
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354) ~[mysql-connector-j-8.0.33.jar:8.0.33]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy270.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy268.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.GeneratedMethodAccessor43.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.6.jar:3.5.6]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at sun.reflect.GeneratedMethodAccessor43.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.16.jar:3.5.16]
	at com.sun.proxy.$Proxy267.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.16.jar:3.5.16]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) ~[mybatis-3.5.16.jar:3.5.16]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 91 more
2025-07-15 14:39:59.924 |-DEBUG [XNIO-1 task-1][a781e8e55df87996] -  com.hishop.starter.web.filter.RequestLogFilter [82] -| [RESP] uri:/himall-report/report/createSourceShop cost:63ms input:
{"shopId":1000067,"shopName":"","createTime":"2025-07-07 07:46:41","updateTime":"2025-07-07 07:46:41"}
2025-07-15 14:44:00.980 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-15 14:44:00.980 |-INFO  [Thread-11][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-15 14:44:00.980 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-15 14:44:00.980 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-15 14:44:00.980 |-INFO  [Thread-11][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 14:44:00.980 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 14:44:00.980 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.980 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-15 14:44:00.980 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.981 |-INFO  [Thread-12][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-15 14:44:00.983 |-WARN  [Thread-3][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-15 14:44:00.985 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=125, lastTimeStamp=1752561840985}] instanceId:[InstanceId{instanceId=*******:22540, stable=false}] @ namespace:[himall-report].
2025-07-15 14:44:01.015 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 14:44:01.018 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-15 14:44:01.025 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-15 14:44:01.197 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-15 14:44:01.198 |-INFO  [Thread-16][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-15 14:44:01.488 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='bbc_himall-report', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-15 14:44:01.488 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-15 14:44:01.488 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-15 14:44:01.488 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-15 14:44:01.489 |-INFO  [Thread-15][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-15 14:44:01.489 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-15 14:44:01.598 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-15 14:44:04.646 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-15 14:44:04.654 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-15 14:44:04.656 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-15 14:44:04.656 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-15 14:44:04.656 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-15 14:44:04.657 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-15 14:44:04.657 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-15 14:44:04.657 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
