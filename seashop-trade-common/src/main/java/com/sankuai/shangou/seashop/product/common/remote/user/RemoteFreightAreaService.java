package com.sankuai.shangou.seashop.product.common.remote.user;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.thrift.shop.FreightAreaQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateResp;

/**
 * <AUTHOR>
 * @date 2024/03/04 11:59
 */
@Component
public class RemoteFreightAreaService {

    @Resource
    private FreightAreaQueryFeign freightAreaQueryFeign;

    public List<QueryFreightTemplateDto> queryTplByTemplateIdList(List<Long> templateIdList) {
        if (CollectionUtils.isEmpty(templateIdList)) {
            return Collections.emptyList();
        }

        templateIdList = templateIdList.stream().distinct().collect(Collectors.toList());
        List<List<Long>> subIdsArr = Lists.partition(templateIdList, CommonConstant.QUERY_LIMIT);

        List<QueryFreightTemplateDto> result = new ArrayList<>();
        for (List<Long> subIds : subIdsArr) {
            BaseBatchIdReq req = new BaseBatchIdReq();
            req.setId(subIds);
            QueryFreightTemplateResp resp = ThriftResponseHelper.executeThriftCall(() -> freightAreaQueryFeign.queryTplByTemplateIdList(req));
            if (resp != null && CollectionUtils.isNotEmpty(resp.getResult())) {
                result.addAll(resp.getResult());
            }
        }
        return result;
    }

    public QueryFreightTemplateDto queryTplByTemplateId(Long templateId) {
        List<QueryFreightTemplateDto> templateList = queryTplByTemplateIdList(Arrays.asList(templateId));
        return CollectionUtils.isEmpty(templateList) ? null : templateList.get(0);
    }

    public List<QueryFreightTemplateDto> queryFreightTemplate(Long shopId, List<String> templateNames) {
        if (CollectionUtils.isEmpty(templateNames)) {
            return Collections.EMPTY_LIST;
        }

        QueryFreightTemplateReq req = new QueryFreightTemplateReq();
        req.setShopId(shopId);
        req.setTemplateNames(templateNames);
        QueryFreightTemplateResp resp = ThriftResponseHelper.executeThriftCall(() -> freightAreaQueryFeign.queryMFreightTemplateList(req));
        return resp.getResult();
    }

    public QueryFreightTemplateDto queryFreightTemplate(Long shopId, String templateName) {
        List<QueryFreightTemplateDto> templateList = queryFreightTemplate(shopId, Arrays.asList(templateName));
        return CollectionUtils.isEmpty(templateList) ? null : templateList.get(0);
    }

    public Map<Long, String> getFreightTemplateNameMap(List<Long> templateIdList) {
        List<QueryFreightTemplateDto> templateList = queryTplByTemplateIdList(templateIdList);
        return templateList.stream().collect(Collectors.toMap(QueryFreightTemplateDto::getId, QueryFreightTemplateDto::getName));
    }

}
