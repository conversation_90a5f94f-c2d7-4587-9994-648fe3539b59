package com.sankuai.shangou.seashop.product.common.remote.base;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.ProductSettingResp;
import com.sankuai.shangou.seashop.product.common.remote.base.model.RemoteProductSettingBo;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/18 9:30
 */
@Service
@Slf4j
public class RemoteSiteSettingService {

    @Resource
    private SiteSettingQueryFeign siteSettingQueryFeign;

    /**
     * 获取商品设置
     *
     * @return 商品设置
     */
    public RemoteProductSettingBo getProductSetting() {
        ProductSettingResp setting = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getProductSettings());
        return JsonUtil.copy(setting, RemoteProductSettingBo.class);
    }

    public String getSetting(String key) {
        // todo 后续可以加本地缓存
        return ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey(key));
    }

    public String getSetting(String key, String defaultValue) {
        String value = getSetting(key);
        return StrUtil.isEmpty(value) ? defaultValue : value;
    }

}
