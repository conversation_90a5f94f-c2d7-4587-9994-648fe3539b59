package com.sankuai.shangou.seashop.product.common.remote.user;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.remote.user.model.RemoteShopBo;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.EnoughCashFlagReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopIdsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;

import cn.hutool.core.collection.CollectionUtil;

/**
 * <AUTHOR>
 * @date 2023/12/05 9:59
 */
@Service
public class RemoteShopService {

    @Resource
    private ShopQueryFeign shopQueryFeign;

    /**
     * 根据店铺id列表查询店铺信息
     *
     * @param shopIds 店铺id列表
     * @return 店铺信息列表
     */
    public List<RemoteShopBo> listByShopIds(List<Long> shopIds) {
        if (CollectionUtil.isEmpty(shopIds)) {
            return Collections.EMPTY_LIST;
        }

        try {
            shopIds = shopIds.stream().distinct().collect(Collectors.toList());
            List<RemoteShopBo> shopList = new ArrayList<>();
            List<List<Long>> subShopIdsList = Lists.partition(shopIds, CommonConstant.QUERY_LIMIT);
            subShopIdsList.forEach(subShopIds -> {
                QueryShopPageReq req = new QueryShopPageReq();
                req.setShopIds(subShopIds);
                ShopRespList resp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.getShopList(req));
                if (CollectionUtil.isNotEmpty(resp.getShopRespList())) {
                    shopList.addAll(JsonUtil.copyList(resp.getShopRespList(), RemoteShopBo.class));
                }
            });
            return shopList;
        }
        catch (Exception e) {
            return Collections.EMPTY_LIST;
        }
    }

    /**
     * 根据店铺id查询店铺信息
     *
     * @param shopId 店铺id
     * @return 店铺信息
     */
    public RemoteShopBo getByShopId(Long shopId) {
        if (shopId == null) {
            return null;
        }

        return listByShopIds(Arrays.asList(shopId)).stream().findFirst().orElse(null);
    }

    /**
     * 根据店铺名称查询店铺id的集合
     *
     * @param name 店铺名称
     * @return 店铺id的集合
     */
    public List<Long> getShopIdsByName(String name) {
        if (StringUtils.isBlank(name)) {
            return Collections.EMPTY_LIST;
        }
        QueryShopReq req = new QueryShopReq();
        req.setShopName(name);
        ShopIdsResp resp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.getShopIds(req));
        return resp.getShopIds();
    }

    /**
     * 获取店铺名称map
     *
     * @param shopIds 店铺id集合
     * @return 店铺名称map
     */
    public Map<Long, String> getShopNameMap(List<Long> shopIds) {
        if (CollectionUtil.isEmpty(shopIds)) {
            return Collections.EMPTY_MAP;
        }

        List<RemoteShopBo> shopList = listByShopIds(shopIds);
        return shopList.stream().collect(Collectors.toMap(RemoteShopBo::getId, RemoteShopBo::getShopName, (k1, k2) -> k2));
    }

    /**
     * 获取店铺名称
     *
     * @param shopId 店铺id
     * @return 店铺名称
     */
    public String getShopName(Long shopId) {
        if (shopId == null) {
            return "";
        }

        RemoteShopBo shop = getByShopId(shopId);
        return shop == null ? "" : shop.getShopName();
    }

    /**
     * 根据店铺名称查询店铺信息
     *
     * @param shopNames 店铺名称
     * @return 店铺信息
     */
    public List<ShopSimpleResp> listShopByNames(List<String> shopNames) {
        if (CollectionUtil.isEmpty(shopNames)) {
            return Collections.EMPTY_LIST;
        }
        ShopSimpleQueryReq req = new ShopSimpleQueryReq();
        req.setShopNameList(shopNames);
        ShopSimpleListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(req));
        return resp.getList();
    }

    /**
     * 根据店铺id查询店铺信息
     *
     * @param shopIds 店铺id
     * @return 店铺信息
     */
    public List<ShopSimpleResp> listShopByIds(List<Long> shopIds) {
        if (CollectionUtil.isEmpty(shopIds)) {
            return Collections.EMPTY_LIST;
        }
        ShopSimpleQueryReq req = new ShopSimpleQueryReq();
        req.setShopIdList(shopIds);
        ShopSimpleListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(req));
        return resp.getList();
    }

    ///**
    // * 查询店铺和经营类目ID查询保证金是否足够
    // * @param enoughCashFlagReq
    // * @return
    // */
    //public Boolean enoughCashFlag(EnoughCashFlagReq enoughCashFlagReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.enoughCashFlag(enoughCashFlagReq));
    //}

    public RemoteShopBo checkAndGetShop(Long shopId) {
        RemoteShopBo shop = getByShopId(shopId);
        AssertUtil.throwIfNull(shop, "店铺不存在");
        return shop;
    }
}
