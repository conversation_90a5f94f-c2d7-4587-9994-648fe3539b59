package com.sankuai.shangou.seashop.promotion.common.enums;

/**
 * @author: liu<PERSON><PERSON>
 * @date: 2023/11/7/007
 * @description:
 */

public enum ActiveStatusEnum {

    NOT_START(0, "未开始"),
    START(1, "进行中"),
    END(2, "已结束"),
    ;

    private Integer code;
    private String msg;

    ActiveStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
