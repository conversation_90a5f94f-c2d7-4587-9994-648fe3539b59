package com.sankuai.shangou.seashop.product.common.constant;

import com.sankuai.shangou.seashop.trade.common.util.FunctionUtil;

/**
 * <AUTHOR>
 * @date 2023/11/20 9:49
 */
public class LockConstant {

    /**
     * 锁前缀
     */
    private static final String LOCK_PREFIX = "seashop:product:lock:";

    /**
     * 导入库存锁定时间 60s
     */
    public static final Long IMPORT_STOCK_LOCK_TIME = 60L;

    /**
     * 同步库存超时时间
     */
    public static final Long SYNC_STOCK_TIMEOUT = 10L;

    /**
     * 商品ID 锁
     */
    public static final String PRODUCT_ID_LOCK = LOCK_PREFIX + "product:id:";

    /**
     * SKU ID 锁
     */
    public static final String SKU_ID_LOCK = LOCK_PREFIX + "sku:id:";

    /**
     * 绑定推荐商品锁
     */
    public static final String BIND_RELATION_PRODUCT_LOCK = LOCK_PREFIX + "relation_product:";

    /**
     * 保存品牌信息锁
     */
    public static final String SAVE_BRAND_LOCK = LOCK_PREFIX + "brand:save:";

    /**
     * 品牌申请锁
     */
    public static final String BRAND_APPLY_LOCK = LOCK_PREFIX + "brand:apply:";

    /**
     * 品牌申请审核锁
     */
    public static final String BRAND_APPLY_AUDIT_LOCK = LOCK_PREFIX + "brand:apply_audit:";

    /**
     * 保存类目锁
     */
    public static final String SAVE_CATEGORY_LOCK = LOCK_PREFIX + "category:save:";

    /**
     * 商品审核锁
     */
    public static final String PRODUCT_AUDIT_LOCK = LOCK_PREFIX + "product:audit:";

    /**
     * 店铺分类锁
     */
    public static final String SHOP_CATEGORY_SAVE_LOCK = LOCK_PREFIX + "shop_category:save:";

    /**
     * 规格模板保存锁
     */
    public static final String SPEC_TEMPLATE_SAVE_LOCK = LOCK_PREFIX + "spec_template:save:";
    /**
     * 商品es构建锁
     */
    public static final String LOCK_ES_PRODUCT_UPDATE_PATTERN = LOCK_PREFIX + "es:product:build:{0}";

    /**
     * 商品es构建锁 埋点
     */
    public static final String SCENE_ES_PRODUCT_UPDATE = LOCK_PREFIX + "scene:" + "es:product:build";

    /**
     * 商品审核es构建锁
     */
    public static final String LOCK_ES_PRODUCT_AUDIT_UPDATE_PATTERN = LOCK_PREFIX + "es:product:audit:build:{0}";

    /**
     * 商品审核es构建锁 埋点
     */
    public static final String SCENE_ES_PRODUCT_AUDIT_UPDATE = LOCK_PREFIX + "scene:" + "es:product:audit:build";

    /**
     * 库存同步锁(库存覆盖加锁)
     */
    public static final String STOCK_SYNC_LOCK = LOCK_PREFIX + "stock:sync:";

    /**
     * 库存同步锁 埋点 (库存覆盖加锁)
     */
    public static final String STOCK_SYNC_SCENE = LOCK_PREFIX + "scene:" + "stock:sync:";

    /**
     * 库存变动锁(库存增减加锁)
     */
    public static final String STOCK_CHANGE_LOCK = LOCK_PREFIX + "stock:change:";

    /**
     * 库存变动锁 埋点(库存增减加锁)
     */
    public static final String STOCK_CHANGE_SCENE = LOCK_PREFIX + "scene:" + "stock:change:";

    /**
     * 库存任务锁(库存任务实际执行加锁)
     */
    public static final String STOCK_TASK_LOCK = LOCK_PREFIX + "stock:task:";

    /**
     * 库存任务锁 埋点(库存任务实际执行加锁)
     */
    public static final String STOCK_TASK_SCENE = LOCK_PREFIX + "scene:" + "stock:task:";

    /**
     * 下架店铺下商品锁
     */
    public static final String LOCK_OFF_SHELF_SHOP_PRODUCT_PATTERN = LOCK_PREFIX + "off_shelf_shop_product:{0}";

    /**
     * 下架店铺下商品锁 埋点
     */
    public static final String SCENE_OFF_SHELF_SHOP_PRODUCT = LOCK_PREFIX + "scene:" + "off_shelf_shop_product";

    /**
     * 订单变动事件
     */
    public static final String LOCK_ORDER_CHANGE_EVENT_PATTERN = LOCK_PREFIX + "order:change:event:{0}";

}
