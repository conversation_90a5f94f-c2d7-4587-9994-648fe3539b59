package com.sankuai.shangou.seashop.product.common.remote.user;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryValidBusinessCategoryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ValidBusinessCategoryIdsResp;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/01 10:28
 */
@Service
public class RemoteBusinessCategoryService {

    @Resource
    private BusinessCategoryQueryFeign businessCategoryQueryFeign;

    public List<Long> getValidBusinessCategoryIds(Long shopId) {
        QueryValidBusinessCategoryReq req = new QueryValidBusinessCategoryReq();
        req.setShopId(shopId);
        ValidBusinessCategoryIdsResp resp = ThriftResponseHelper.executeThriftCall(() ->
                businessCategoryQueryFeign.queryValidBusinessCategoryIds(req));
        if (resp != null && CollectionUtils.isNotEmpty(resp.getCategoryIds())) {
            return resp.getCategoryIds();
        }
        return Collections.EMPTY_LIST;
    }

}
