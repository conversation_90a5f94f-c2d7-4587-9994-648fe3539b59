package com.sankuai.shangou.seashop.promotion.common.remote;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberQueryFeign;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberListReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberListResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;

import cn.hutool.core.collection.CollectionUtil;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@Service
public class MemberRemoteService {

    @Resource
    ShopMemberQueryFeign shopMemberQueryFeign;

    /**
     * 查询会员列表
     *
     * @param queryMemberReq
     * @return
     */
    public MemberListResp queryMemberList(QueryMemberListReq queryMemberReq) {
        return ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMemberList(queryMemberReq));
    }

    public List<MemberResp> queryMembers(List<Long> memberIds) {
        if (CollectionUtil.isEmpty(memberIds)) {
            return Collections.emptyList();
        }

        memberIds = memberIds.stream().distinct().collect(Collectors.toList());
        List<List<Long>> subMemberIds = Lists.partition(memberIds, PromotionConstant.MAX_QUERY_LIMIT);
        List<MemberResp> memberList = new ArrayList<>();
        for (List<Long> subList : subMemberIds) {
            QueryMemberListReq req = new QueryMemberListReq();
            req.setMemberIds(subList);
            MemberListResp resp = queryMemberList(req);
            if (resp != null && CollectionUtil.isNotEmpty(resp.getMemberRespList())) {
                memberList.addAll(resp.getMemberRespList());
            }
        }
        return memberList;
    }

    public MemberResp queryMember(Long memberId) {
        List<MemberResp> memberList = queryMembers(Arrays.asList(memberId));
        return CollectionUtils.isEmpty(memberList) ? null : memberList.get(0);
    }
}
