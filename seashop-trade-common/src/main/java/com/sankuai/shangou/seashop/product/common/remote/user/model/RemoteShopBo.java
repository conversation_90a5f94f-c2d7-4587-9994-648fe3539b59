package com.sankuai.shangou.seashop.product.common.remote.user.model;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/05 10:02
 */
@Setter
@Getter
public class RemoteShopBo {

    /**
     * 供应商ID
     */
    private Long id;

    /**
     * 供应商等级
     */
    private Long gradeId;

    /**
     * 供应商名称
     */
    private String shopName;

    /**
     * 是否官方自营店
     */
    private Boolean whetherSelf;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 汇付操作状态
     */
    private Integer adapayStatus;

    /**
     * 平台审核状态
     */
    private Integer plateStatus;

    /**
     * 店铺创建时间
     */
    private Date createDate;

    /**
     * 店铺过期时间
     */
    private Date endDate;

    /**
     * 联系人姓名
     */
    private String contactsName;

    /**
     * 联系人电话
     */
    private String contactsPhone;

    /**
     * 联系人Email
     */
    private String contactsEmail;

    /**
     * 银行开户名
     */
    private String bankAccountName;

    /**
     * 银行账号
     */
    private String bankAccountNumber;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 支行联行号
     */
    private String bankCode;

    /**
     * 开户银行所在地
     */
    private Integer bankRegionId;

    /**
     * 开户银行类型（1对公，2对私）
     */
    private Integer bankType;

    /**
     * 商家发货人名称
     */
    private String senderName;

    /**
     * 商家发货人地址
     */
    private String senderAddress;

    /**
     * 商家发货人电话
     */
    private String senderPhone;


    /**
     * 是否缴纳保证金
     */
    private Boolean whetherPayBond;

}
