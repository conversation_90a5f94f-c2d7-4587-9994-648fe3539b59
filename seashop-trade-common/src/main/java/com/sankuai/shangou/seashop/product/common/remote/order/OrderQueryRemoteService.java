package com.sankuai.shangou.seashop.product.common.remote.order;

import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentSummaryReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentSummaryResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/12 8:20
 */
@Service
public class OrderQueryRemoteService {

    @Resource
    private OrderQueryFeign orderQueryFeign;

    @Resource
    private ProductCommentQueryFeign productCommentQueryFeign;

    public OrderInfoDto queryLastOrderInfo(Long userId) {
        return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryLastOrderInfo(userId));
    }

    public OrderInfoDto getOrder(String orderId) {
        QueryOrderDetailReq req = new QueryOrderDetailReq();
        req.setOrderId(orderId);

        OrderDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryDetail(req));
        return Optional.ofNullable(resp).map(OrderDetailResp::getOrderInfo).orElse(null);
    }

    /**
     * 查询商品评价汇总信息
     *
     * @param productId 商品ID
     * @return 商品评价汇总信息
     */
    public ProductCommentSummaryResp getProductCommentSummary(Long productId) {
        QueryProductCommentSummaryReq request = new QueryProductCommentSummaryReq();
        request.setProductId(productId);
        ProductCommentSummaryResp summaryResp = ThriftResponseHelper.executeThriftCall(() -> productCommentQueryFeign.queryProductCommentSummary(request));
        return summaryResp;
    }
}
