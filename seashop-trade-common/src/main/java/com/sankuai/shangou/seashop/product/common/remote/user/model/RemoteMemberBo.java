package com.sankuai.shangou.seashop.product.common.remote.user.model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/05 10:22
 */
@Setter
@Getter
public class RemoteMemberBo {

    /**
     * 会员id
     */
    private Long id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 省份ID
     */
    private Integer topRegionId;

    /**
     * 省市ID
     */
    private Integer regionId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String cellPhone;

    /**
     * qq
     */
    private String qq;

    /**
     * 地址
     */
    private String address;

    /**
     * 是否禁用
     */
    private Boolean disabled;

    /**
     * 最后登录时间
     */
    private Date lastLoginDate;

    /**
     * 订单数量
     */
    private Integer orderNumber;

    /**
     * 总充值
     */
    private BigDecimal totalAmount;

    /**
     * 总消费金额（不排除退款）
     */
    private BigDecimal expenditure;

    /**
     * 积分
     */
    private Integer points;

    /**
     * 头像
     */
    private String photo;

    /**
     * 商家父账号id
     */
    private Long parentSellerId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 邀请人
     */
    private Long inviteUserId;

    /**
     * 生日
     */
    private Date birthDay;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 净消费
     */
    private BigDecimal netAmount;

    /**
     * 最后消费时间
     */
    private Date lastConsumptionTime;

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 是否不提示入驻为供应商
     */
    private Boolean whetherNoticeJoin;

    /**
     * 是否注销
     */
    private Boolean whetherLogOut;

    /**
     * 注销时间
     */
    private Date logOutTime;

    /**
     * 注册来源 0表示本系统，1表示牵牛花
     */
    private Integer registerSource;

    /**
     * 微信OpenId
     */
    private String openId;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 更新日期
     */
    private Date updateTime;

}
