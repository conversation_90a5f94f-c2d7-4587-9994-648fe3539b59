package com.sankuai.shangou.seashop.product.common.constant;

/**
 * <AUTHOR>
 * @date 2023/11/20 9:35
 */
public class LeafKeyConstant {

    /**
     * 商品id leaf key
     */
    public static final String PRODUCT_ID_LEAF_KEY = "com.sankuai.sgb2b.seashop.product.product.id";

    /**
     * 货号 leaf key
     */
    public static final String PRODUCT_CODE_LEAF_KEY = "PRODUCT_CODE_LEAF_KEY";

    /**
     * 库存同步任务 leaf key
     */
    public static final String STOCK_CHANGE_TASK_LEAF_KEY = "STOCK_CHANGE_TASK_LEAF_KEY";

    /**
     * 库存同步任务明细 leaf key
     */
    public static final String STOCK_CHANGE_TASK_INFO_LEAF_KEY = "STOCK_CHANGE_TASK_INFO_LEAF_KEY";

    /**
     * sku自增id leaf key
     */
    public static final String SKU_AUTO_ID_LEAF_KEY = "com.sankuai.sgb2b.seashop.product.sku.id";

    /**
     * 删除版本号 leaf key
     */
    public static final String DELETE_VERSION_LEAF_KEY = "DELETE_VERSION_LEAF_KEY";

}
