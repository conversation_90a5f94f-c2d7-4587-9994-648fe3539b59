package com.sankuai.shangou.seashop.promotion.common.remote;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;

/**
 * @author: lhx
 * @date: 2023/12/2/002
 * @description:
 */
@Service
public class ShopRemoteService2 {

    @Resource
    private ShopQueryFeign shopQueryFeign;

    public ShopSimpleListResp querySimpleList(ShopSimpleQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(request));
    }

    public List<ShopResp> queryShopsByIds(ShopQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryShopsByIds(request));
    }

    public List<ShopSimpleResp> getShopByIds(List<Long> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return Collections.emptyList();
        }

        shopIds = shopIds.stream().distinct().collect(Collectors.toList());
        List<List<Long>> subShopIds = Lists.partition(shopIds, PromotionConstant.MAX_QUERY_LIMIT);
        List<ShopSimpleResp> shopList = new ArrayList<>();
        subShopIds.forEach(curShopIds -> {
            ShopSimpleQueryReq request = new ShopSimpleQueryReq();
            request.setShopIdList(curShopIds);
            ShopSimpleListResp response = querySimpleList(request);
            if (response != null && CollectionUtils.isNotEmpty(response.getList())) {
                shopList.addAll(response.getList());
            }
        });
        return shopList;
    }

    public Map<Long, ShopSimpleResp> getShopMapByIds(List<Long> shopIds) {
        List<ShopSimpleResp> shopList = getShopByIds(shopIds);
        return shopList.stream().collect(Collectors.toMap(ShopSimpleResp::getId, Function.identity(), (k1, k2) -> k1));
    }
}
