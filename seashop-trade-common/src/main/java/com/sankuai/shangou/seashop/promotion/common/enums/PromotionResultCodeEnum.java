package com.sankuai.shangou.seashop.promotion.common.enums;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
public enum PromotionResultCodeEnum {

    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * </pre>
     */

    DISCOUNT_ACTIVE_NOT_FIND(40050101, "折扣活动不存在"),
    PRODUCT_HAS_OTHER_DISCOUNT_ACTIVE(40050102, "商品已经参加其他折扣活动"),
    COUPON_ACTIVE_NOT_FIND(40050103, "优惠券活动不存在"),
    COUPON_RECEIVE_MAX(40050104, "优惠券领取已达上限"),
    COUPON_RECEIVE_EMPTY(40050105, "优惠券已经抢光了"),
    DISCOUNT_ACTIVE_END(40050106, "折扣活动已经结束"),
    COUPON_ACTIVE_END(40050107, "优惠券活动已经结束"),
    FULL_REDUCTION_NOT_FIND(40050108, "满减活动不存在"),
    FULL_REDUCTION_HAS_ACTIVE(40050109, "时间范围内已存在满减活动"),
    FULL_REDUCTION_HAS_END(40050110, "满减活动已经结束"),
    EXCLUSIVE_PRICE_NOT_FIND(40050111, "专享价活动不存在"),
    EXCLUSIVE_PRICE_PRODUCT_IN_ACTIVE(40050112, "商品已经参加其他专享价活动"),
    EXCLUSIVE_PRICE_END(40050113, "专享价活动已经结束"),
    //限时购分类最多只能添加20个
    FLASH_SALE_CATEGORY_MAX(40050114, "限时购分类最多只能添加%s个"),
    FLASH_SALE_CATEGORY_NOT_EXIST(40050115, "限时购分类不存在"),
    FLASH_SALE_PRODUCT_HAS_ACTIVE(40050116, "商品已经参加其他限时购活动"),
    FLASH_SALE_NOT_EXIST(40050117, "限时购活动不存在"),
    // 限时购活动当前状态不允许修改
    FLASH_SALE_STATUS_NOT_ALLOW_UPDATE(40050118, "限时购活动当前状态不允许修改"),
    // 限时购活动明细数据修改异常
    FLASH_SALE_DETAIL_UPDATE_ERROR(40050119, "限时购活动明细数据修改异常"),
    FLASH_SALE_STATUS_NOT_ALLOW_AUDIT(40050120, "限时购活动当前状态不允许审核"),
    // 限时购活动已经结束
    FLASH_SALE_END(40050121, "限时购活动已经结束"),

    SHOP_NAME_LIKE_QUERY_TOO_MANY(40050122, "店铺名称模糊查询不够精确，导致数据海量，请完善店铺名称"),
    PRODUCT_NAME_LIKE_QUERY_TOO_MANY(40050123, "商品名称模糊查询不够精确，导致数据海量，请完善商品名称"),
    // 部分优惠券已经失效或不存在
    COUPON_PART_NOT_EXIST(40050124, "部分优惠券已经失效或不存在"),
    // 部分优惠券数量不足
    COUPON_PART_NOT_ENOUGH(40050125, "部分优惠券数量不足"),
    // 部分优惠券已经被核销
    COUPON_PART_CONSUMED(40050126, "部分优惠券已经被核销"),
    COUPON_PART_NOT_IN_ACTIVITY(40050127, "部分优惠券不在活动中"),
    COUPON_PART_NOT_IN_PRODUCT(40050128, "部分优惠券商品不在活动中"),
    COUPON_PART_NOT_CONSUMED(40050129, "部分优惠券未被使用"),
    // 部分优惠券不属于当前用户
    COUPON_PART_NOT_BELONG_TO_MEMBER(40050130, "部分优惠券不属于当前用户"),
    FLASH_SALE_CATEGORY_USED(40050131, "限时购分类已经被使用"),
    // 优惠券发放总量小于已领用数量
    COUPON_MAX_RECEIVE_LESS_THAN_RECEIVE(40050132, "优惠券发放总量小于已领用数量"),
    // 优惠券个人最大领用数量小于已领用数量
    COUPON_MAX_RECEIVE_PER_LESS_THAN_RECEIVE(40050133, "优惠券个人最大领用数量小于已领用数量"),
    FLASH_SALE_CATEGORY_NAME_EXIST(40050134, "限时购分类名称已经存在"),
    COUPON_NOT_EXIST(40050135, "优惠券不存在"),
    // 限时购活动未开始或已结束
    FLASH_SALE_NOT_START_OR_END(40050136, "限时购活动未开始或已结束"),
    FLASH_SALE_PRODUCT_LIMIT(40050137, "限时购活动商品数量超过限制"),
    FLASH_SALE_CONSUME_RECORD_NOT_EXIST(40050138, "限时购活动消费记录不存在"),
    // 限时购退还库存大于消费库存
    FLASH_SALE_RETURN_STOCK_GT_CONSUME_STOCK(40050139, "限时购退还库存大于消费库存"),
    // 越权操作
    AUTHORITY_ERROR(40050140, "越权操作"),
    // 限时购商品已经参加其他组合购活动
    FLASH_SALE_PRODUCT_HAS_COLLOCATION(40050141, "商品已经参加其他组合购活动"),
    // 限时购商品已经参加其他阶梯价活动
    FLASH_SALE_PRODUCT_HAS_LADDER_PRICE(40050142, "商品已经开启阶梯价活动"),
    // 限时购商品当前状态不允许修改
    FLASH_SALE_PRODUCT_NOT_ALLOW_UPDATE(40050143, "当前状态不允许修改限时购商品"),
    // 限时购活动当前状态不允许修改开始时间
    FLASH_SALE_BEGIN_DATE_NOT_ALLOW_UPDATE(40050144, "当前状态不允许修改开始时间"),

    // 个人已经达到优惠券的最大领取数量
    COUPON_OVER_MEMBER_MAX_RECEIVED_NUM(40050145, "个人已经达到优惠券的最大领取数量"),

    // 商家已关闭优惠券活动
    RECEIVE_COUPON_ACTIVITY_CLOSED(40050146, "商家已关闭优惠券活动"),

    ;

    private Integer code;
    private String msg;

    PromotionResultCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
