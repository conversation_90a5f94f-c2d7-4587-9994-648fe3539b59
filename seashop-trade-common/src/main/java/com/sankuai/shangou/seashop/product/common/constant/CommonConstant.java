package com.sankuai.shangou.seashop.product.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23 15:43
 */
public class CommonConstant {

    /**
     * 白名单类目 牵牛花的类目 无需权限验证
     */
    public static final List<Long> WHITE_CATEGORY_IDS = Arrays.asList(2424L);

    /**
     * 默认字符编码
     */
    public static final String DEFAULT_CHARSET = "utf-8";

    /**
     * 默认图片
     */
    public static final String DEFAULT_IMAGE = "https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png";

    /**
     * 默认图片key
     */
    public static final String DEFAULT_IMAGE_SETTING_KEY = "defaultImage";


    /**
     * 最大类目登记
     */
    public static final Integer MAX_CATEGORY_LEVEL = 3;

    /**
     * 默认店铺id
     */
    public static final Long DEFAULT_SHOP_ID = 0l;

    /**
     * 默认用户id
     */
    public static final Long DEFAULT_USER_ID = 0l;

    /**
     * 默认表单id
     */
    public static final Long DEFAULT_FORM_ID = 0l;

    /**
     * 商品货号长度
     */
    public static final Integer PRODUCT_CODE_LENGTH = 8;

    /**
     * 默认父类目id
     */
    public static Long DEFAULT_PARENT_ID = 0L;

    /**
     * 申请新品牌id
     */
    public static final Long APPLY_NEW_BRAND_ID = 0L;

    /**
     * ZIP 压缩包后缀
     */
    public static final String ZIP_SUFFIX = ".zip";

    /**
     * 解压缩工作空间
     */
    public static final String ZIP_WORK_SPACE = "/tmp/zip";

    /**
     * 商品压缩包工作空间
     */
    public static final String PRODUCT_WORK_SPACE = ZIP_WORK_SPACE + "/product";

    /**
     * 商品压缩包导入excel名称
     */
    public static final String PRODUCT_EXCEL_NAME = "products.xlsx";

    /**
     * 商品压缩包导入图片文件夹
     */
    public static final String PRODUCT_IMAGE_FOLDER = "products";

    /**
     * 商品更新压缩包工作空间
     */
    public static final String PRODUCT_UPDATE_WORK_SPACE = ZIP_WORK_SPACE + "/productUpdate";

    /**
     * 商品更新压缩包导入excel名称
     */
    public static final String PRODUCT_UPDATE_EXCEL_NAME = "products.xlsx";

    /**
     * 商品更新压缩包导入图片文件夹
     */
    public static final String PRODUCT_UPDATE_IMAGE_FOLDER = "products";

    /**
     * 图片后缀数组
     */
    public static final String[] IMAGE_SUFFIX = {"jpg", "gif", "png"};

    /**
     * 是 常量
     */
    public static final String YES_STR = "是";

    /**
     * 否 常量
     */
    public static final String NO_STR = "否";

    /**
     * 类目path 分割线不带转移
     */
    public static final String CATEGORY_PATH_SPLIT_NO_ESCAPE = "|";

    /**
     * 类目path 分割线
     */
    public static final String CATEGORY_PATH_SPLIT = "\\|";

    /**
     * 类目全名称 分隔符
     */
    public static final String CATEGORY_FULL_NAME_SPLIT = ",";

    /**
     * 每次查询的阈值
     */
    public static final Integer QUERY_LIMIT = 200;

    /**
     * 每次更新的阈值
     */
    public static final Integer UPDATE_LIMIT = 200;

    /**
     * 最大规格等级
     */
    public static final Integer MAX_SPEC_LEVEL = 3;

    /**
     * 默认页码
     */
    public static final Integer DEFAULT_PAGE_NO = 1;

    /**
     * ES 查询最大条数
     */
    public static final Integer ES_QUERY_LIMIT = 1000;

    /**
     * 可以发布商品的类目深度
     */
    public static final Integer CAN_PUBLISH_PRODUCT_CATEGORY_DEPTH = 3;

    /**
     * skuId 分割符
     */
    public static final String SKU_ID_SPLIT = "_";

    /**
     * 默认版式id
     */
    public static final Long DEFAULT_PRODUCT_DESCRIPTION_TEMPLATE_ID = 0L;

    /**
     * SKU_ID 格式化 (单规格)
     */
    public static final String SINGLE_SKU_ID_FORMAT = "%s_0_0_0";

    /**
     * SKU_ID 格式化 (多规格)
     */
    public static final String SKU_ID_FORMAT = "%s_%s_%s_%s";

    /**
     * 推荐品牌数量
     */
    public static Integer RECOMMEND_BRAND_SIZE = 9;

    /**
     * SQL 语句中的 limit
     */
    public static String LIMIT = " limit ";

    /**
     * 规格值最大长度限制
     */
    public static Integer MAX_SPEC_VALUE_LENGTH = 100;

    /**
     * 同步库存更新阈值
     */
    public static Integer SYNC_STOCK_LIMIT = 50;

    /**
     * 规格导入分隔符
     */
    public static String SKU_IMPORT_SPEC_SPLIT = ";";

    /**
     * 规格导入规格值分隔符
     */
    public static String SKU_IMPORT_SPEC_VALUE_SPLIT = ":";

    /**
     * SKU_CODE 分隔符
     */
    public static String SKU_CODE_SPLIT = "-";

    /**
     * 推荐商品限制数量
     */
    public static Integer RECOMMEND_PRODUCT_LIMIT = 8;

    /**
     * 商品事件推送延迟时间 延迟5s
     */
    public static Integer PRODUCT_EVENT_PUSH_DELAY_TIME = 5 * 1000;

    /**
     * 分布式锁最大个数
     */
    public static Integer DISTRIBUTED_LOCK_MAX_SIZE = 50;

    /**
     * zip包导入最大限制 20M, 转换为byte
     */
    public static Long ZIP_IMPORT_MAX_SIZE = 20 * 1024 * 1024L;

    /**
     * excel包导入最大限制 1M
     */
    public static Long EXCEL_IMPORT_MAX_SIZE = 1 * 1024 * 1024L;

    /**
     * 品牌已存在提示语
     */
    public static final String BRAND_NAME_EXIST_TIPS = "该品牌已存在,请选择申请已有品牌!";

    /**
     * 自动审核通过原因
     */
    public static final String AUTO_PASS_AUDIT_REASON = "平台未开启商品审核, 自动通过";
}
