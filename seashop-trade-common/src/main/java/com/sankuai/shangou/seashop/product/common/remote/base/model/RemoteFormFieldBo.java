package com.sankuai.shangou.seashop.product.common.remote.base.model;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/30 16:01
 */
@Getter
@Setter
public class RemoteFormFieldBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 表单id
     */
    private Long formId;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 格式
     */
    private Integer format;

    /**
     * 选项
     */
    private String option;

    /**
     * 是否必填
     */
    private Boolean isRequired;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 添加时间
     */
    private Date addedDate;

}
