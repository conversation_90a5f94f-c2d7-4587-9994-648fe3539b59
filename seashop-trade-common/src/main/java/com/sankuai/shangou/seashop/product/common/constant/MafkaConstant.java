package com.sankuai.shangou.seashop.product.common.constant;

/**
 * <AUTHOR>
 */
public class MafkaConstant {

    /**
     * mafka命名空间，用于区分不同的业务，这里固定为waimai
     */
    public static final String DEFAULT_NAMESPACE = "waimai";

    /**
     * 商品审核表变动消息订阅
     */
    public static final String TOPIC_PRODUCT_AUDIT_UPDATE = "seashop_product_audit_dts_topic";

    /**
     * 商品审核表变动消息订阅消费者组
     */
    public static final String GROUP_ES_PRODUCT_AUDIT_BUILD = "seashop_product_audit_dts_consumer";

    /**
     * 库存表变动消息订阅
     */
    public static final String TOPIC_SKU_STOCK_UPDATE = "seashop_product_sku_stock_dts_topic";

    /**
     * 库存表变动消息订阅消费者组
     */
    public static final String GROUP_ES_SKU_STOCK_BUILD = "seashop_product_sku_stock_dts_consumer";

    /**
     * 商品转移订阅
     */
    public static final String TOPIC_PRODUCT_TRANSFER = "seashop_product_transfer_topic";

    /**
     * 商品转移订阅消费者组
     */
    public static final String GROUP_PRODUCT_TRANSFER = "seashop_product_transfer_consumer";

    /**
     * 转移商品分区key前缀
     */
    public static final String PART_KEY_PRODUCT_TRANSFER_PREFIX = "seashop_product_transfer_part_key_";

    /**
     * 下架店铺下的商品topic
     */
    public static final String TOPIC_OFF_SALE_SHOP_PRODUCT = "seashop_user_shop_change";

    /**
     * 下架店铺下的商品消费者组
     */
    public static final String GROUP_OFF_SALE_SHOP_PRODUCT = "seashop_user_shop_change_product_consumer";

    /**
     * 同步库存topic
     */
    public static final String TOPIC_SYNC_STOCK = "seashop_product_sync_stock_topic";

    /**
     * 同步库存消费者组
     */
    public static final String GROUP_SYNC_STOCK = "seashop_product_sync_stock_consumer";

    /**
     * 同步库存消息分区key前缀
     */
    public static final String PART_KEY_SYNC_STOCK_PREFIX = "seashop_product_sync_stock_part_key_";

    /**
     * 商品变动事件topic
     */
    public static final String TOPIC_PRODUCT_CHANGE = "seashop_product_change_topic";

    public static final String GROUP_PRODUCT_ES_BUILD = "seashop_product_es_build_consumer";

    public static final String GROUP_PRODUCT_WDT_REQUEST = "seashop_product_es_wdt_request";
    /**
     * 类目变动事件topic
     */
    public static final String TOPIC_CATEGORY_CHANGE = "seashop_product_category_change_topic";

    public static final String TOPIC_PRODUCT_MIGRATE_IMAGE = "seashop_product_migrate_image_topic";


    /**
     * 商品收藏topic
     */
    public static final String TOPIC_FAVORITE_PRODUCT = "seashop_favorite_product_topic";
    /**
     * 商品收藏消费者组
     */
    public static final String GROUP_FAVORITE_PRODUCT = "seashop_favorite_product_user_consumer";

    /**
     * 订单变动事件topic
     */
    public static final String TOPIC_ORDER_CHANGE = "seashop_order_change_topic";

    /**
     * 订单变动事件商品消费组
     */
    public static final String GROUP_ORDER_PRODUCT = "seashop_order_change_product_consumer";


}
