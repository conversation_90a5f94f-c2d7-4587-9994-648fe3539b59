package com.sankuai.shangou.seashop.promotion.common.constant;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
public class PromotionConstant {

    /**
     * 全部商品
     */
    public static final Long ALL_PRODUCT = -1L;

    /**
     * 优惠券最大领取数量
     */
    public static final Integer COUPON_MAX_RECEIVE_NUM = 5;


    /**
     * 限时购分类最大数量
     */
    public static final Integer LIMIT_TIME_BUY_CATEGORY_MAX_NUM = 20;

    /**
     * 平台默认Id
     */
    public static final Long PLAT_ID = 0L;

    /**
     * 默认限时购活动开始时间
     */
    public static final Integer DEFAULT_LIMIT_TIME_BUY_PREHEAT = 0;

    /**
     * 查询的初始页面
     */
    public static final Integer DEFAULT_PAGE = 1;

    /**
     * 模糊查询最大值
     */
    public static final Integer MAX_LIKE_QUERY = 200;

    /**
     * 列表最大处理数量
     */
    public static final Integer MAX_LIST_SIZE = 1000;

    /**
     * 优惠券SN码前缀
     */
    public static final String COUPON_SN_PREFIX = "SN";

    /**
     * 最大in的数量
     */
    public static final Integer MAX_IN_SIZE = MAX_LIKE_QUERY;

    /**
     * 优惠券不限制订单金额的默认值
     */
    public static final Long COUPON_DEFAULT_ORDER_AMOUNT = 0L;

    /**
     * 商品已删除
     */
    public static final String PRODUCT_DELETED = "商品已删除";

    /**
     * 最大查询限制
     */
    public static final Integer MAX_QUERY_LIMIT = 200;
}
