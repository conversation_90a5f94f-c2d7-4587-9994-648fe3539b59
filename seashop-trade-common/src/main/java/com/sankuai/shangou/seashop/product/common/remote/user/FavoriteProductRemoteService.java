package com.sankuai.shangou.seashop.product.common.remote.user;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.FavoriteProductCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFavoriteProductReq;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/15 9:34
 */
@Service
public class FavoriteProductRemoteService {

    @Resource
    private FavoriteProductCmdFeign favoriteProductCmdFeign;

    public BaseResp deleteFavoriteProduct(List<Long> productIdList){
        DeleteFavoriteProductReq deleteFavoriteProductReq = new DeleteFavoriteProductReq();
        deleteFavoriteProductReq.setProductIdList(productIdList.stream().map(String::valueOf).collect(Collectors.toList()));
        return ThriftResponseHelper.executeThriftCall(() -> favoriteProductCmdFeign.deleteFavoriteProduct(deleteFavoriteProductReq));
    }
}
