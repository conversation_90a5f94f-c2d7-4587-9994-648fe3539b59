package com.sankuai.shangou.seashop.promotion.common.constant;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
public class LockConstant {

    /**
     * 优惠券保存key
     */
    public static final String COUPON_SAVE_KEY = "promotion:coupon:save:shopId:%s";

    /**
     * 优惠券领取key
     */
    public static final String COUPON_RECEIVE_KEY = "promotion:coupon:receive:%s:%s";

    /**
     * 优惠券发送key（控制整个发送，避免点击）
     */
    public static final String COUPON_SEND_KEY = "promotion:coupon:send";

    /**
     * 优惠券核销key
     */
    public static final String COUPON_CONSUME_KEY = "promotion:coupon:consume:memberId:%s";

    /**
     * 折扣活动保存key
     */
    public static final String DISCOUNT_ACTIVE_SAVE_KEY = "promotion:discount:save:shopId:%s";

    /**
     * 锁定时间
     */
    public static final Long LOCK_TIME = 5L;

    /**
     * 限时购活动保存key
     */
    public static final String LIMIT_TIME_BUY_SAVE_KEY = "promotion:flash:sale:id:%s";

    /**
     * 限时购活动店铺防止重复提交key
     */
    public static final String LIMIT_TIME_BUY_SUBMIT_KEY = "promotion:flash:sale:shopId:%s";

    /**
     * 营销活动的锁（范围店铺ID）
     */
    public static final String PROMOTION_SHOP_LOCK_KEY = "promotion:shopId:%s";

    /**
     * 限时购核销key
     */
    public static final String LIMIT_TIME_BUY_CONSUME_KEY = "promotion:flash:sale:consume:id:%s";

    /**
     * 限时购核销skuId key
     */
    public static final String LIMIT_TIME_BUY_SKU_ID_CONSUME_KEY = "promotion:flash:sale:consume:id:%s:skuId:%s";

    /**
     * 限时购撤销核销orderId key
     */
    public static final String LIMIT_TIME_BUY_ORDER_ID_CONSUME_KEY = "promotion:flash:sale:consume:orderId:%s";

    /**
     * 限时购库存归还 key
     */
    public static final String LIMIT_TIME_BUY_RETURN_STOCK_KEY = "promotion:flash:sale:return:stock:flashSaleId:%s:skuId:%s:orderId:%s:relationId:%s";
}
