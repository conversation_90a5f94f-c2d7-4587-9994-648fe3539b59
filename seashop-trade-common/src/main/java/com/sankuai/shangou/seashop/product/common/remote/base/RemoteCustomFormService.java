package com.sankuai.shangou.seashop.product.common.remote.base;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.CustomFormQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryCustomerFormFieldReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryCustomerFormReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormFieldListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormListRes;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.remote.base.model.RemoteFormBo;
import com.sankuai.shangou.seashop.product.common.remote.base.model.RemoteFormFieldBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:20
 */
@Service
@Slf4j
public class RemoteCustomFormService {

    @Resource
    private CustomFormQueryFeign customFormQueryFeign;


    public List<RemoteFormBo> listCustomerForms(List<Long> formIds) {
        if (CollectionUtils.isEmpty(formIds)) {
            return Collections.EMPTY_LIST;
        }

        List<RemoteFormBo> result = new ArrayList<>();
        List<List<Long>> subIdsArr = Lists.partition(formIds, CommonConstant.QUERY_LIMIT);
        subIdsArr.forEach(subIds -> {
            QueryCustomerFormReq req = new QueryCustomerFormReq();
            req.setFormIds(subIds);
            BaseCustomFormListRes resp =
                    ThriftResponseHelper.executeThriftCall(() -> customFormQueryFeign.queryCustomFormByFormIds(req));
            result.addAll(JsonUtil.copyList(resp.getFormList(), RemoteFormBo.class));
        });
        return result;
    }

    public RemoteFormBo getCustomerForm(Long formId) {
        List<RemoteFormBo> formList = listCustomerForms(Arrays.asList(formId));
        if (CollectionUtils.isEmpty(formList)) {
            return null;
        }
        return formList.get(0);
    }

    public List<RemoteFormFieldBo> getCustomerFormFields(List<Long> fieldIds) {
        if (CollectionUtils.isEmpty(fieldIds)) {
            return Collections.EMPTY_LIST;
        }

        List<RemoteFormFieldBo> result = new ArrayList<>();
        List<List<Long>> subIdsArr = Lists.partition(fieldIds, CommonConstant.QUERY_LIMIT);
        subIdsArr.forEach(subIds -> {
            QueryCustomerFormFieldReq req = new QueryCustomerFormFieldReq();
            req.setFieldIds(subIds);
            BaseCustomFormFieldListRes resp = ThriftResponseHelper.executeThriftCall(() -> customFormQueryFeign.queryCustomFieldByFieldIds(req));
            result.addAll(JsonUtil.copyList(resp.getFieldList(), RemoteFormFieldBo.class));
        });
        return result;
    }

    /**
     * 获取字段名称map
     *
     * @param fieldIds 字段id的集合
     * @return 字段名称map
     */
    public Map<Long, String> getCustomerFieldNameMap(List<Long> fieldIds) {
        if (CollectionUtils.isEmpty(fieldIds)) {
            return Collections.emptyMap();
        }

        List<RemoteFormFieldBo> fields = getCustomerFormFields(fieldIds);
        return fields.stream().collect(Collectors.toMap(RemoteFormFieldBo::getId, RemoteFormFieldBo::getFieldName, (k1, k2) -> k2));
    }

    /**
     * 查询表单名称map
     *
     * @param formIds 表单id的集合
     * @return 表单名称map
     */
    public Map<Long, String> getCustomerFormNameMap(List<Long> formIds) {
        if (CollectionUtils.isEmpty(formIds)) {
            return Collections.emptyMap();
        }

        List<RemoteFormBo> fields = listCustomerForms(formIds);
        return fields.stream().collect(Collectors.toMap(RemoteFormBo::getId, RemoteFormBo::getName, (k1, k2) -> k2));
    }
}
