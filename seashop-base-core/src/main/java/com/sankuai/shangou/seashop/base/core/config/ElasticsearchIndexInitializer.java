package com.sankuai.shangou.seashop.base.core.config;

import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@Component
public class ElasticsearchIndexInitializer implements CommandLineRunner {

    private final RestHighLevelClient client;
    private static final String[] INDEX_NAMES = new String[]{"order_index", "order_item_index", "order_refund_index", "product_audit_index", "trade_product_index", "shop_index","product_comment_index"};

    @Value("${spring.profiles.active}")
    private String indexPrefix;

    @Autowired
    public ElasticsearchIndexInitializer(RestHighLevelClient client) {
        this.client = client;
    }

    @Override
    public void run(String... args) throws Exception {
        if (indexPrefix.contains("local")) {
            return;
        }
        for (String INDEX_NAME : INDEX_NAMES) {
            if (!indexExists(INDEX_NAME)) {
                this.createIndexWithMapping(INDEX_NAME);
            }
        }
    }

    private boolean indexExists(String indexName) throws IOException {
        GetIndexRequest request = new GetIndexRequest(indexPrefix + "." + indexName);
        try {
            return client.indices().exists(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            // 处理异常（如连接失败）
            throw new RuntimeException("Failed to check index existence: " + indexName, e);
        }
    }

    private void createIndexWithMapping(String indexName) throws IOException {
        CreateIndexRequest request = new CreateIndexRequest(indexPrefix + "." + indexName);
        String mappingJson = this.readMappingFromFile("static/ES_index/" + indexName + ".json");
        request.source(mappingJson, XContentType.JSON);
        try {
            client.indices().create(request, RequestOptions.DEFAULT);
            System.out.println("Index created: " + indexName);
        } catch (IOException | RuntimeException e) {
            throw new RuntimeException("Failed to create index: " + indexName, e);
        }
    }

    private String readMappingFromFile(String path) throws IOException {
        ClassPathResource resource = new ClassPathResource(path);
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\n");
            }
        }
        return sb.toString();
    }
}
