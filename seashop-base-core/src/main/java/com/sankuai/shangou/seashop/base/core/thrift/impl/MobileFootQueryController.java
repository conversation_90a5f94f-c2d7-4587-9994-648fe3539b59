package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.MobileFootQueryService;
import com.sankuai.shangou.seashop.base.thrift.core.MobileFootQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryFootMenusResp;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:48
 */
@RestController
@RequestMapping("/mobileFoot")
public class MobileFootQueryController implements MobileFootQueryFeign {

    @Resource
    private MobileFootQueryService mobileFootQueryService;

    @PostMapping(value = "/queryFootMenus", consumes = "application/json")
    @Override
    public ResultDto<QueryFootMenusResp> queryFootMenus(@RequestBody QueryFootMenusReq queryFootMenusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryFootMenus", queryFootMenusReq, req ->
                mobileFootQueryService.queryFootMenus(req));
    }
}
