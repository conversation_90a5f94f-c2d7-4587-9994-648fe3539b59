package com.sankuai.shangou.seashop.base.core.service.impl;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.ExpressSelfQueryService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseExpressCompany;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.repository.ExpressCompanyRepository;
import com.sankuai.shangou.seashop.base.thrift.core.dto.QueryExpressCompanyDto;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ExpressCompanyStatusEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryBatchExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryErpExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyPageReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressSiteSettingResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyPageResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyResp;

import cn.hutool.core.collection.CollectionUtil;

@Service
public class ExpressSelfQueryServiceImpl implements ExpressSelfQueryService {

    @Resource
    private ExpressCompanyRepository expressCompanyRepository;

    @Resource
    private SiteSettingService siteSettingService;

    @Override
    public QueryExpressCompanyResp queryExpressCompanyList(QueryExpressCompanyReq queryExpressCompanyReq) {
        QueryExpressCompanyResp result = new QueryExpressCompanyResp();
        BaseExpressCompany baseExpressCompany = new BaseExpressCompany();
        baseExpressCompany.setId(queryExpressCompanyReq.getId());
        List<BaseExpressCompany> baseExpressCompanyList = expressCompanyRepository.queryExpressCompanyList(baseExpressCompany);
        if (CollectionUtil.isEmpty(baseExpressCompanyList)) {
            return result;
        }
        List<QueryExpressCompanyDto> queryExpressCompanyDtoList =
                baseExpressCompanyList.stream().map(this::convertExpressCompanyDto).collect(Collectors.toList());
        result.setQueryExpressCompanyDtoList(queryExpressCompanyDtoList);
        return result;
    }

    @Override
    public QueryExpressCompanyResp queryBatchExpressCompany(QueryBatchExpressCompanyReq req) {
        QueryExpressCompanyResp result = new QueryExpressCompanyResp();
        List<BaseExpressCompany> baseExpressCompanyList = null;
        if (CollectionUtil.isNotEmpty(req.getCompanyCodes())) {
            baseExpressCompanyList = expressCompanyRepository.queryExpressCompanyByKuaiDiNiaoCompanyCodes(req.getCompanyCodes());
        }
        else if (CollectionUtil.isNotEmpty(req.getCompanyNames())) {
            baseExpressCompanyList = expressCompanyRepository.queryExpressCompanyByCompanyNames(req.getCompanyNames());
        }
        if (CollectionUtil.isEmpty(baseExpressCompanyList)) {
            return result;
        }
        List<QueryExpressCompanyDto> queryExpressCompanyDtoList =
                baseExpressCompanyList.stream().map(this::convertExpressCompanyDto).collect(Collectors.toList());
        result.setQueryExpressCompanyDtoList(queryExpressCompanyDtoList);
        return result;
    }


    private QueryExpressCompanyDto convertExpressCompanyDto(BaseExpressCompany baseExpressCompany) {
        QueryExpressCompanyDto queryExpressCompanyDto = new QueryExpressCompanyDto();
        queryExpressCompanyDto.setId(baseExpressCompany.getId());
        queryExpressCompanyDto.setName(baseExpressCompany.getName());
        queryExpressCompanyDto.setTaobaoCode(baseExpressCompany.getTaobaoCode());
        queryExpressCompanyDto.setKuaidi100Code(baseExpressCompany.getKuaidi100Code());
        queryExpressCompanyDto.setKuaidiniaoCode(baseExpressCompany.getKuaidiniaoCode());
        queryExpressCompanyDto.setWidth(baseExpressCompany.getWidth());
        queryExpressCompanyDto.setHeight(baseExpressCompany.getHeight());
        queryExpressCompanyDto.setLogo(baseExpressCompany.getLogo());
        queryExpressCompanyDto.setBackgroundImage(baseExpressCompany.getBackgroundImage());
        queryExpressCompanyDto.setStatus(baseExpressCompany.getStatus());
        queryExpressCompanyDto.setCreateDate(baseExpressCompany.getCreateDate());
        queryExpressCompanyDto.setWangdiantongCode(baseExpressCompany.getWangdiantongCode());
        queryExpressCompanyDto.setJushuitanCode(baseExpressCompany.getJushuitanCode());
        queryExpressCompanyDto.setBoluopaiCode(baseExpressCompany.getBoluopaiCode());
        queryExpressCompanyDto.setMeituanCode(baseExpressCompany.getMeituanCode());
        return queryExpressCompanyDto;
    }

    @Override
    public QueryExpressCompanyDto queryExpressByThirdCode(QueryErpExpressCompanyReq req) {
        BaseExpressCompany baseExpressCompany = new BaseExpressCompany();
        baseExpressCompany.setJushuitanCode(req.getJushuitanCode());
        baseExpressCompany.setWangdiantongCode(req.getWangdiantongCode());
        baseExpressCompany.setBoluopaiCode(req.getBoluopaiCode());
        baseExpressCompany.setMeituanCode(req.getMeituanCode());
        baseExpressCompany.setKuaidiniaoCode(req.getKuaiDiNiaoCode());
        baseExpressCompany.setStatus(ExpressCompanyStatusEnum.ENABLED.getCode());
        List<BaseExpressCompany> baseExpressCompanyList = expressCompanyRepository.queryExpressCompanyList(baseExpressCompany);
        if (CollectionUtil.isEmpty(baseExpressCompanyList)) {
            return null;
        }
        return convertExpressCompanyDto(baseExpressCompanyList.get(0));
    }

    @Override
    public ExpressSiteSettingResp queryExpressSiteSetting() {
        ExpressSiteSettingResp result = new ExpressSiteSettingResp();
        Field[] fields = result.getClass().getDeclaredFields();
        List<String> settingKeys = new ArrayList<>();
        for (Field field : fields) {
            String name = field.getName();
            settingKeys.add(name);
        }
        //根据keys 获取属性值
        List<BaseSiteSetting> list = siteSettingService.query(settingKeys);
        if (CollectionUtil.isEmpty(list)) {
            return result;
        }
        Map<String, String> map = list.stream().collect(Collectors.toMap(BaseSiteSetting::getKey, BaseSiteSetting::getValue, (o1, o2) -> o2));
        return JsonUtil.copy(map, ExpressSiteSettingResp.class);
    }

    @Override
    public QueryExpressCompanyPageResp queryExpressCompanyPage(QueryExpressCompanyPageReq req) {
        BasePageParam page = new BasePageParam();
        page.setPageNum(req.getPageNo());
        page.setPageSize(req.getPageSize());
        Page<BaseExpressCompany> pageResult = PageHelper.startPage(page);
        BaseExpressCompany baseExpressCompany = new BaseExpressCompany();
        baseExpressCompany.setStatus(ExpressCompanyStatusEnum.ENABLED.getCode());
        expressCompanyRepository.queryExpressCompanyList(baseExpressCompany);
        QueryExpressCompanyPageResp result = new QueryExpressCompanyPageResp();
        result.setPageNo(req.getPageNo());
        result.setPageSize(req.getPageSize());
        if (pageResult == null) {
            result.setTotalCount(0L);
            result.setExpressCompanys(Collections.emptyList());
            return result;
        }
        result.setTotalCount(pageResult.getTotal());
        result.setExpressCompanys(pageResult.stream().map(this::convertExpressCompanyDto).collect(Collectors.toList()));
        return result;
    }
}
