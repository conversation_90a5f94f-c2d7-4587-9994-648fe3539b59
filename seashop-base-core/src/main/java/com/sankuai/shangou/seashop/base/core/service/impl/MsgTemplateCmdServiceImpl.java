package com.sankuai.shangou.seashop.base.core.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.enums.WxTemplatesEnum;
import com.sankuai.shangou.seashop.base.core.service.MsgTemplateCmdService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseWeixinMsgTemplate;
import com.sankuai.shangou.seashop.base.dao.core.domain.WxAppletFormData;
import com.sankuai.shangou.seashop.base.dao.core.repository.MemberOpenIdRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.MsgTemplateRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.WxAppletFormDataRepository;
import com.sankuai.shangou.seashop.base.utils.OkHttpUtil;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:19
 */
@Service
@Slf4j
public class MsgTemplateCmdServiceImpl implements MsgTemplateCmdService {

    @Resource
    private MsgTemplateRepository msgTemplateRepository;

    @Resource
    private SiteSettingService siteSettingService;

    @Resource
    private MemberOpenIdRepository memberOpenIdRepository;

    @Resource
    private WxAppletFormDataRepository wxAppletFormDataRepository;

    @Resource
    private SquirrelUtil squirrelUtil;

    private static final String WEIXINAPPLETID = "weixinAppletId";
    private static final String WEIXINAPPLETSECRET = "weixinAppletSecret";

    public static final Integer ONE = 1;
    public static final Integer TWO = 2;
    public static final Integer THREE = 3;
    public static final Integer FOUE = 4;
    public static final Integer FIVE = 5;
    public static final Integer SIX = 6;
    public static final Integer SEVEN = 7;
    public static final Integer EIGHT = 8;
    public static final Integer NINE = 9;
    public static final Integer TEN = 10;

    @Value("${wechat.getTokenUrl}")
    private String getTokenUrl;

    @Value("${wechat.addTemplate}")
    private String addTemplate;

    @Value("${wechat.removeTemplate}")
    private String removeTemplate;

    @Value("${wechat.sendSubscribeMsg}")
    private String sendSubscribeMsg;

    @Value("${wechat.tokenExpireTime}")
    private Integer tokenExpireTime;

    @Resource
    private BaseLogAssist baseLogAssist;

    @Override
//    @ExaminProcess(processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.INSERT, repository = "msgTemplateRepository", serviceMethod = "saveMsgTemplate", dto = SaveMsgTemplateReq.class, entity = BaseWeixinMsgTemplate.class)
    public void saveMsgTemplate(SaveMsgTemplateReq saveMsgTemplateReq) {
        List<BaseSiteSetting> list = siteSettingService.query(Arrays.asList(WEIXINAPPLETID, WEIXINAPPLETSECRET));
        // 基础配置表的保存
        List<BaseSiteSetting> settings = new ArrayList<>();
        BaseSiteSetting appletId = new BaseSiteSetting();
        appletId.setId(list.stream().filter(v -> WEIXINAPPLETID.equals(v.getKey())).collect(Collectors.toList()).get(0).getId());
        appletId.setKey(WEIXINAPPLETID);
        appletId.setValue(saveMsgTemplateReq.getWeixinAppletId());
        settings.add(appletId);
        BaseSiteSetting appletSecret = new BaseSiteSetting();
        appletSecret.setId(list.stream().filter(v -> WEIXINAPPLETSECRET.equals(v.getKey())).collect(Collectors.toList()).get(0).getId());
        appletSecret.setKey(WEIXINAPPLETSECRET);
        appletSecret.setValue(saveMsgTemplateReq.getWeixinAppletSecret());
        settings.add(appletSecret);
        TransactionHelper.doInTransaction(() -> {
            siteSettingService.saveSettings(settings);
            // 更新模版ID
            saveMsgTemplateReq.getValues().forEach(v -> {
                BaseWeixinMsgTemplate req = new BaseWeixinMsgTemplate();
                req.setId(v.getKey());
                req.setTemplateId(v.getValue());
                msgTemplateRepository.update(req);
            });
        });
    }

    @Override
    public void saveMsgTemplateApplet(MsgTemplateAppletReq request) {
        // 基础配置表的保存

        siteSettingService.saveMsgTemplateApplet(request);
    }

    @Override
    public void saveMsgTemplateData(MsgTemplateDataReq request) {
        BaseWeixinMsgTemplate baseWeixinMsgTemplate = new BaseWeixinMsgTemplate();
        List<BaseWeixinMsgTemplate> list = msgTemplateRepository.selectList(baseWeixinMsgTemplate);
        // 手动写日志
        BaseMsgTemplateLog oldValue = new BaseMsgTemplateLog();
        for (BaseWeixinMsgTemplate baseWeiXin : list) {
            if (WxTemplatesEnum.TemplateNumEnum.TKTZ.getCode().equals(Integer.parseInt(baseWeiXin.getTemplateNum()))) {
                oldValue.setReturnMoneyTemplateId(baseWeiXin.getTemplateId());
            }
            else if (WxTemplatesEnum.TemplateNumEnum.DDFHTZ.getCode().equals(Integer.parseInt(baseWeiXin.getTemplateNum()))) {
                oldValue.setDeliverTemplateId(baseWeiXin.getTemplateId());
            }
        }
        BaseMsgTemplateLog newValue = new BaseMsgTemplateLog();
        List<BaseMsgTemplateReq> items = request.getItems();
        Map<Long, BaseWeixinMsgTemplate> map = list.stream().collect(Collectors.toMap(BaseWeixinMsgTemplate::getId, Function.identity(), (v1, v2) -> v2));
        for (BaseMsgTemplateReq baseWeiXinReq : items) {
            BaseWeixinMsgTemplate msg = map.get(baseWeiXinReq.getKey());
            if (WxTemplatesEnum.TemplateNumEnum.TKTZ.getCode().equals(Integer.parseInt(msg.getTemplateNum()))) {
                newValue.setReturnMoneyTemplateId(baseWeiXinReq.getValue());
            }
            else if (WxTemplatesEnum.TemplateNumEnum.DDFHTZ.getCode().equals(Integer.parseInt(msg.getTemplateNum()))) {
                newValue.setDeliverTemplateId(baseWeiXinReq.getValue());
            }
        }
        baseLogAssist.recordLog(ExaminModelEnum.SYSTEM_SETTING, ExaProEnum.MODIFY, "平台小程序消息配置-保存(模板)",
            request.getOperationUserId(), request.getOperationShopId(),
            oldValue, newValue);
        // 更新模版ID
        items.forEach(v -> {
            BaseWeixinMsgTemplate req = new BaseWeixinMsgTemplate();
            req.setId(v.getKey());
            req.setTemplateId(v.getValue());
            msgTemplateRepository.update(req);
        });
    }

    @Override
    @Async
    public void getAppletSubscribeTmplate() {
        OkHttpClient client = OkHttpUtil.getInstance();
        // 第一步，获取小程序token
        String accesstoken = this.getAppletResetToken();
        BaseWeixinMsgTemplate query = new BaseWeixinMsgTemplate();
        query.setUserInWxApplet(1);
        List<BaseWeixinMsgTemplate> templates = msgTemplateRepository.selectList(query);
        if (CollectionUtils.isEmpty(templates)) {
            return;
        }
        // 过滤出需要的
        List<String> tempNums = Arrays.asList(WxTemplatesEnum.TemplateNumEnum.TKTZ.getCode().toString(), WxTemplatesEnum.TemplateNumEnum.DDFHTZ.getCode().toString());
        List<BaseWeixinMsgTemplate> wxTemplates = templates.stream().filter(v -> tempNums.contains(v.getTemplateNum())).collect(Collectors.toList());
        wxTemplates.forEach(v -> {
            int messageType = WxTemplatesEnum.MessageTypeEnum.FIRST.getCode();
            List<Integer> kidList = new ArrayList<>();
            if (WxTemplatesEnum.TemplateNumEnum.TKTZ.getCode().equals(Integer.parseInt(v.getTemplateNum()))) {
                messageType = WxTemplatesEnum.MessageTypeEnum.TKSHHTZSJ.getCode();
                kidList.addAll(Arrays.asList(SIX, FOUE, FIVE));
            }
            else if (WxTemplatesEnum.TemplateNumEnum.DDFHTZ.getCode().equals(Integer.parseInt(v.getTemplateNum()))) {
                messageType = WxTemplatesEnum.MessageTypeEnum.KDPSDDFHHTZ.getCode();
                kidList.addAll(Arrays.asList(ONE, TWO, SEVEN, FOUE, SIX));
            }
            this.removeTemplate(client, v.getTemplateId(), accesstoken);
            // 再添加新的模板
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tid", v.getTemplateNum());
            jsonObject.put("sceneDesc", v.getDescription());
            jsonObject.put("kidList", kidList);
            String templateId = this.addSubscribeTemplate(1, client, jsonObject, accesstoken);
            // 再保存本地
            BaseWeixinMsgTemplate updateReq = new BaseWeixinMsgTemplate();
            updateReq.setTemplateNum(v.getTemplateNum());
            updateReq.setMessageType(messageType);
            updateReq.setTemplateId(templateId);
            msgTemplateRepository.update(updateReq);
        });
    }

    @Override
    public void sendAppletMessage(SendAppletReq sendAppletReq) {
        // 第一步，根据传入的templateNum获取对应的templateId
        BaseWeixinMsgTemplate baseWeixinMsgTemplate = new BaseWeixinMsgTemplate();
        baseWeixinMsgTemplate.setTemplateNum(String.valueOf(sendAppletReq.getTemplateNum()));
        List<BaseWeixinMsgTemplate> list = msgTemplateRepository.selectList(baseWeixinMsgTemplate);
        AssertUtil.throwIfNull(list, "没有找到对应的模板");
        SendAppletMessageReq request = new SendAppletMessageReq();
        request.setTemplateId(list.get(0).getTemplateId());
        request.setUserId(sendAppletReq.getUserId());
        request.setPage(WxTemplatesEnum.TemplateNumEnum.getPageUrlByCode(sendAppletReq.getTemplateNum()));
        // 这里的data需要根据不同的模板进行组装
        if (WxTemplatesEnum.TemplateNumEnum.TKTZ.getCode().equals(sendAppletReq.getTemplateNum())) {
            // 退款通知
            request.setData(this.dealSendRefundNoticeReq(sendAppletReq.getSendRefundNoticeReq()).toString());
        } else if (WxTemplatesEnum.TemplateNumEnum.DDFHTZ.getCode().equals(sendAppletReq.getTemplateNum())) {
            // 订单发货通知
            request.setData(this.dealSendOrderShippingNoticeReq(sendAppletReq.getSendOrderShippingNoticeReq()).toString());
        }
        this.sendAppletMessageByTemplate(request);
    }

    @Override
    public void sendAppletMessageByTemplate(SendAppletMessageReq req) {
        OkHttpClient client = OkHttpUtil.getInstance();
        // 第一步,获取接收者用户的openId
        String openId = memberOpenIdRepository.getMemberOpenIdInfoByuserIdAndType(req.getUserId(), "WeiXinSmallProg");
        AssertUtil.throwIfNull(openId, "获取接收者用户的openId为空");
        // 第二步，获取小程序token
        String accesstoken = this.getAppletResetToken();
        // 小程序发送通知的微信接口
        String sendUrl = sendSubscribeMsg + "?access_token=" + accesstoken;

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        stringBuffer.append("\"template_id\":\"").append(req.getTemplateId()).append("\",");
        stringBuffer.append("\"page\":\"").append(req.getPage()).append("\",");
        stringBuffer.append("\"touser\":\"").append(openId).append("\",");
        stringBuffer.append("\"miniprogram_state\":\"").append(req.getMiniprogramState()).append("\",");
        stringBuffer.append("\"lang\":\"").append(req.getLang()).append("\",");
        stringBuffer.append("\"data\":").append(req.getData());
        stringBuffer.append("}");
        log.info("小程序发送通知的微信接口请求参数={}", stringBuffer.toString());
        RequestBody body = RequestBody.create(stringBuffer.toString(), MediaType.parse("application/json;charset=utf-8"));
        Request sendRequest = new Request.Builder().url(sendUrl).post(body).build();
        try {
            Response response = client.newCall(sendRequest).execute();
            String bodyStr = response.body().string();
//            JSONObject js = JSON.parseObject(bodyStr, JSONObject.class);
            JsonNode node = JsonUtil.parseObject(bodyStr, JsonNode.class);
            log.info("小程序发送通知的微信接口返回结果={}", node);
            AssertUtil.throwIfTrue(node.get("errcode").asInt() != 0, "小程序发送通知的微信接口调用失败");
        } catch (IOException e) {
            log.error("MsgTemplateCmdServiceImpl sendAppletMessageByTemplate is error e={}", e);
        }
    }

    @Override
    public void insertWxAppletFormData(CmdWxAppletFormDataReq cmdWxAppletFormDataReq) {
        WxAppletFormData req = JsonUtil.copy(cmdWxAppletFormDataReq, WxAppletFormData.class);
        if (req.getEventTime() == null) {
            req.setEventTime(new Date());
        }
        if (req.getExpireTime() == null) {
            // 创建 Calendar 对象
            Calendar calendar = Calendar.getInstance();
            // 将当前时间设置为 Calendar 对象
            calendar.setTimeInMillis(System.currentTimeMillis());
            // 在 Calendar 对象上添加一年
            calendar.add(Calendar.YEAR, 1);
            req.setExpireTime(calendar.getTime());
        }
        wxAppletFormDataRepository.insert(req);
    }


    private JSONObject dealSendRefundNoticeReq(SendRefundNoticeReq req) {
        JSONObject data = new JSONObject();
        // 退款原因
        JSONObject thing6 = new JSONObject();
        thing6.put("value", req.getThing6());
        data.put("thing6", thing6);
        // 退款时间
        JSONObject date4 = new JSONObject();
        thing6.put("value", req.getDate4());
        data.put("date4", date4);
        // 支付金额
        JSONObject amount5 = new JSONObject();
        thing6.put("value", req.getAmount5());
        data.put("amount5", amount5);
        return data;
    }

    private JSONObject dealSendOrderShippingNoticeReq(SendOrderShippingNoticeReq req) {
        JSONObject data = new JSONObject();
        // 商品名称
        JSONObject thing1 = new JSONObject();
        thing1.put("value", req.getThing1());
        data.put("thing1", thing1);
        // 订单号
        JSONObject character_string2 = new JSONObject();
        character_string2.put("value", req.getCharacter_string2());
        data.put("character_string2", character_string2);
        // 订单金额
        JSONObject amount7 = new JSONObject();
        amount7.put("value", req.getAmount7());
        data.put("amount7", amount7);
        // 快递公司
        JSONObject thing4 = new JSONObject();
        thing4.put("value", req.getThing4());
        data.put("thing4", thing4);
        // 备注
        JSONObject thing6 = new JSONObject();
        thing6.put("value", req.getThing6());
        data.put("thing6", thing6);
        return data;
    }

    @Override
    public String getAppletResetToken() {
        OkHttpClient client = OkHttpUtil.getInstance();
        String appId = null; // wx9fbb00b8440454ba
        String secret = null; // 47ae213a213db24b8bce30b5f7bfc91c
        List<BaseSiteSetting> settings = siteSettingService.query(Arrays.asList(WEIXINAPPLETID, WEIXINAPPLETSECRET));
        for (BaseSiteSetting baseSiteSetting : settings) {
            if (WEIXINAPPLETID.equals(baseSiteSetting.getKey())) {
                appId = baseSiteSetting.getValue();
            }
            if (WEIXINAPPLETSECRET.equals(baseSiteSetting.getKey())) {
                secret = baseSiteSetting.getValue();
            }
        }
        String key = appId + "_" + secret;
        Object cacheToken = squirrelUtil.get(key);
        if (!Objects.isNull(cacheToken)) {
            return (String) cacheToken;
        }
        String tokenUrl = getTokenUrl + "?grant_type=client_credential&appid=" + appId + "&secret=" + secret;
        Request tokenRequest = new Request.Builder().url(tokenUrl).build();
        String accesstoken = null;
        try (Response response = client.newCall(tokenRequest).execute()) {
//            JSONObject js = JSON.parseObject(response.body().string(),JSONObject.class);
//            accesstoken = js.get("access_token").toString();
            JsonNode node = JsonUtil.parseObject(response.body().string(), JsonNode.class);
            accesstoken = node.get("access_token").asText();
            // 微信官方的token有效期为7200s,设置在缓存的时间不打满，因为代码从获取到执行到这也是需要时间的。
            squirrelUtil.set(key, accesstoken, tokenExpireTime);
        } catch (IOException e) {
            log.info("MsgTemplateCmdServiceImpl getAppletResetToken is error e={}", e);
        }
        return accesstoken;
    }

    @Override
    public BaseResp bindOpenId(BindOpenIdReq req) {
        return null;
    }

    private String getAppletResetTokenNotFromRedis() {
        OkHttpClient client = OkHttpUtil.getInstance();
        String appId = null; // wx9fbb00b8440454ba
        String secret = null; // 47ae213a213db24b8bce30b5f7bfc91c
        List<BaseSiteSetting> settings = siteSettingService.query(Arrays.asList(WEIXINAPPLETID, WEIXINAPPLETSECRET));
        for (BaseSiteSetting baseSiteSetting : settings) {
            if (WEIXINAPPLETID.equals(baseSiteSetting.getKey())) {
                appId = baseSiteSetting.getValue();
            }
            if (WEIXINAPPLETSECRET.equals(baseSiteSetting.getKey())) {
                secret = baseSiteSetting.getValue();
            }
        }
        String tokenUrl = getTokenUrl + "?grant_type=client_credential&appid=" + appId + "&secret=" + secret;
        Request tokenRequest = new Request.Builder().url(tokenUrl).build();
        String accesstoken = null;
        try (Response response = client.newCall(tokenRequest).execute()) {
//            JSONObject js = JSON.parseObject(response.body().string(), JSONObject.class);
//            accesstoken = js.get("access_token").toString();
            JsonNode node = JsonUtil.parseObject(response.body().string(), JsonNode.class);
            accesstoken = node.get("access_token").asText();
            // 微信官方的token有效期为7200s,设置在缓存的时间不打满，因为代码从获取到执行到这也是需要时间的。
            squirrelUtil.set(appId + "_" + secret, accesstoken, tokenExpireTime);
        } catch (IOException e) {
            log.info("MsgTemplateCmdServiceImpl getAppletResetToken is error e={}", e);
        }
        return accesstoken;
    }

    private void removeTemplate(OkHttpClient client, String templateId, String accesstoken) {
        String deleteUrl = removeTemplate + "?access_token=" + accesstoken;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("priTmplId", templateId);
        RequestBody body = RequestBody.create(jsonObject.toString(), MediaType.parse("application/json;charset=utf-8"));
        Request deleteRequst = new Request.Builder().url(deleteUrl).post(body).build();
        try {
            Response response = client.newCall(deleteRequst).execute();
            String str = response.body().string();
            log.debug(str);
        } catch (IOException e) {
            log.info("MsgTemplateCmdServiceImpl removeTemplate is error e={}", e);
        }
    }

    private String addSubscribeTemplate(int num, OkHttpClient client, JSONObject jsonObject, String accesstoken) {
        String addUrl = addTemplate + "?access_token=" + accesstoken;
        RequestBody body = RequestBody.create(jsonObject.toString(), MediaType.parse("application/json;charset=utf-8"));
        Request addRequst = new Request.Builder().url(addUrl).post(body).build();
        String templateId = null;
        try {
            Response response = client.newCall(addRequst).execute();
//            JSONObject js = JSON.parseObject(response.body().string(), JSONObject.class);
            JsonNode node = JsonUtil.parseObject(response.body().string(), JsonNode.class);
            // 如果是tooken过期，重新获取token再调一次.num限制自己调自己最多调2次
            if (num < 2 && 40001 == node.get("errcode").asInt()) {
                accesstoken = getAppletResetTokenNotFromRedis();
                this.addSubscribeTemplate(num + 1, client, jsonObject, accesstoken);
            }
            templateId = node.get("priTmplId").asText();
        } catch (IOException e) {
            log.info("MsgTemplateCmdServiceImpl addSubscribeTemplate is error e={}", e);
        }
        return templateId;
    }
}
