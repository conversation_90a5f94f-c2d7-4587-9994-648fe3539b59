package com.sankuai.shangou.seashop.base.core.audit.listener.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class ContentAuditResultDTO implements Serializable {

    /**
     * 服务状态码,服务正常返回为 0,服务异常为 500，一般不会出现
     */
    private Integer code;

    /**
     * 对于服务异常的描述信息，业务方一般不用关注
     */
    private String msg;

    /**
     * 需要转成海商的类型，enum
     * 保时洁对用type字段
     */
    private Integer type;

    /**
     * 入参的模板ID参数
     */
    private Integer templateId;


    /**
     * 本次请求的唯一ID，与入参一致
     */
    @JsonProperty("transid")
    private String transId;

    /**
     *内容单元的标识
     */
    private Long bizId;

    /**
     *风控决策码 ,0：通过 1585：拦截
     */
    private Integer riskResultCode;

    /**
     * 提示文案，一般用于提供给前端用户的文案提示
     */
    private String prompt;

    /**
     * 包含风控返回的跟业务方约定的其他信息字段
     */
    private Map<String, Object> extra;

    /**
     * 结果产出时间
     */
    private String datetime;

    /**
     * 业务方透传字段
     */
    private String bizData;

    /**
     * 1、文本，2、图片，3、视频
     */
    private Integer businessType;

    /**
     * 说明输出的结果是首次审核输出，还是内容审核系统经过回扫或修正后，第二次或第N次修正审核的结果。
     * auditFreq = 1，业务送审结果。
     * auditFreq = 2，审核修正结果。
     */
    private Integer auditFreq;

    /**
     * 如果需要了解拦截原因和明细，则可以解析此字段，否则通过riskResultCode字段判断即可
     */
    private List<Map<String,Object>> auditResult;

}
