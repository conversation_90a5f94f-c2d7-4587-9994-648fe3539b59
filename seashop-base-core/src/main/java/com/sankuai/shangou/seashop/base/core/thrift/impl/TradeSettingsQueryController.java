package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.TradeSettingsQueryService;
import com.sankuai.shangou.seashop.base.thrift.core.TradeSettingsQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.TradeSiteSettingsResp;

/**
 * @author： liweisong
 * @create： 2023/11/22 17:25
 */
@RestController
@RequestMapping("/tradeSettings")
public class TradeSettingsQueryController implements TradeSettingsQueryFeign {

    @Resource
    private TradeSettingsQueryService tradeSettingsQueryService;

    @GetMapping(value = "/queryTradeSiteSetting")
    @Override
    public ResultDto<TradeSiteSettingsResp> queryTradeSiteSetting() throws TException {
        return ThriftResponseHelper.responseInvoke("queryTradeSiteSetting", null, req ->
                tradeSettingsQueryService.queryTradeSiteSetting());
    }
}
