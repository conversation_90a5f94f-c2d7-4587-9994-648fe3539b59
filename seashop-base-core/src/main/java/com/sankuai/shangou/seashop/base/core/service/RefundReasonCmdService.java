package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.thrift.core.request.AddRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateRefundReasonReq;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:36
 */
public interface RefundReasonCmdService {

    /**
     * 新增售后原因
     * @param addRefundReasonReq
     */
    void addRefundReason(AddRefundReasonReq addRefundReasonReq);

    /**
     * 修改售后原因
     * @param cmdRefundReasonReq
     */
    void updateRefundReason(UpdateRefundReasonReq cmdRefundReasonReq);

    /**
     * 删除售后原因
     * @param deleteRefundReasonReq
     */
    void deleteRefundReason(DeleteRefundReasonReq deleteRefundReasonReq);
}
