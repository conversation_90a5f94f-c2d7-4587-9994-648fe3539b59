package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.core.service.RefundReasonCmdService;
import com.sankuai.shangou.seashop.base.dao.core.domain.RefundReason;
import com.sankuai.shangou.seashop.base.dao.core.repository.RefundReasonRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateRefundReasonReq;

import cn.hutool.core.collection.CollectionUtil;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:42
 */
@Service
public class RefundReasonCmdServiceImpl implements RefundReasonCmdService {

    @Resource
    private RefundReasonRepository refundReasonRepository;

    private static final String REPEAT_REASON = "售后原因重复，操作失败";
    private static final String REASON_SIZE_MORE20 = "售后原因最多添加20个售后原因，操作失败";

    private List<RefundReason> queryRefundReasonList(RefundReason query){
        return refundReasonRepository.queryRefundReasonList(query);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" , actionName = "新增售后原因",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.INSERT,
            repository = "refundReasonRepository", serviceMethod = "addRefundReason",
            dto = AddRefundReasonReq.class, entity = RefundReason.class)
    public void addRefundReason(AddRefundReasonReq addRefundReasonReq) {
        List<RefundReason> list = this.queryRefundReasonList(new RefundReason());
        if(!CollectionUtils.isEmpty(list)){
            AssertUtil.throwIfTrue(list.size() >= 20, REASON_SIZE_MORE20);
            List<RefundReason> filterList = list.stream().filter(v -> v.getAfterSalesText().equals(addRefundReasonReq.getAfterSalesText())).collect(Collectors.toList());
            AssertUtil.throwIfTrue(!CollectionUtils.isEmpty(filterList), REPEAT_REASON);
        }
        RefundReason refundReason = new RefundReason();
        refundReason.setAfterSalesText(addRefundReasonReq.getAfterSalesText());
        refundReason.setCreateTime(new Date());
        refundReason.setUpdateTime(new Date());
        refundReasonRepository.addRefundReason(refundReason);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" , actionName = "修改售后原因",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "refundReasonRepository", serviceMethod = "updateRefundReason",
            dto = UpdateRefundReasonReq.class, entity = RefundReason.class)
    public void updateRefundReason(UpdateRefundReasonReq updateRefundReasonReq) {
        RefundReason query = new RefundReason();
        query.setAfterSalesText(updateRefundReasonReq.getAfterSalesText());
        List<RefundReason> refundReasons = this.queryRefundReasonList(query);
        boolean isNotEmpty = CollectionUtil.isNotEmpty(refundReasons);
        AssertUtil.throwIfTrue(isNotEmpty && !updateRefundReasonReq.getId().equals(refundReasons.get(0).getId()), REPEAT_REASON);
        RefundReason refundReason = new RefundReason();
        refundReason.setId(updateRefundReasonReq.getId());
        refundReason.setAfterSalesText(updateRefundReasonReq.getAfterSalesText());
        refundReason.setUpdateTime(new Date());
        refundReasonRepository.updateRefundReason(refundReason);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" , actionName = "删除售后原因",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MOVE,
            repository = "refundReasonRepository", serviceMethod = "deleteRefundReason",
            dto = DeleteRefundReasonReq.class, entity = RefundReason.class)
    public void deleteRefundReason(DeleteRefundReasonReq deleteRefundReasonReq) {
        refundReasonRepository.deleteRefundReason(deleteRefundReasonReq.getId());
    }
}
