package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.core.service.model.task.CompleteTaskBo;
import com.sankuai.shangou.seashop.base.core.service.model.task.CreateTaskBo;
import com.sankuai.shangou.seashop.base.core.service.model.task.ExceptionTaskBo;
import com.sankuai.shangou.seashop.base.thrift.core.dto.SellerTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;

/**
 * <AUTHOR>
 */
public interface SellerTaskService {

    /**
     * 创建任务。
     * 任务类型业务系统自己定义，需要唯一，最好具有一定的规则
     * @param createTaskBo 入参
     */
    Long createTask(CreateTaskBo createTaskBo);

    /**
     * 开始任务
     * 开始任务是直接把任务状态修改为进行中
     * @param taskId 任务id
     */
    void startTask(Long taskId);

    /**
     * 完成任务
     * @param completeTaskBo 入参
     */
    void completeTask(CompleteTaskBo completeTaskBo);

    /**
     * 任务异常
     * @param exceptionTaskBo 入参
     */
    void exceptionTask(ExceptionTaskBo exceptionTaskBo);

    /**
     * 分页查询任务列表
     * @param queryReq 入参
     * @return 分页结果
     */
    BasePageResp<SellerTaskDto> pageList(QueryTaskReq queryReq);

    /**
     * 查询任务列表
     * @param queryReq 入参
     * @return 结果
     */
    List<SellerTaskDto> queryList(QueryTaskReq queryReq);

}
