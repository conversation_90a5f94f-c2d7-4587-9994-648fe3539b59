package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory;

public interface ImageCategoryService {
    /**
     * 添加图片分类
     * @param category
     */
    Long create(BasePhotoSpaceCategory category);

    /**
     * 修改图片分类
     * @param category
     * @return
     */
    Boolean update(BasePhotoSpaceCategory category);

    /**
     * 查询所有分类
     * @return
     */
    List<BasePhotoSpaceCategory> query(Long shopId);


    /**
     * 批量删除图片分类
     * @param ids id集合
     * @param shopId 店铺id
     * @return
     */
    Boolean deletes(List<Long> ids, long shopId);
}
