package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.core.service.RefundReasonQueryService;
import com.sankuai.shangou.seashop.base.dao.core.domain.RefundReason;
import com.sankuai.shangou.seashop.base.dao.core.repository.RefundReasonRepository;
import com.sankuai.shangou.seashop.base.thrift.core.dto.QueryRefundReasonRespDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryRefundReasonResp;

import cn.hutool.core.collection.CollectionUtil;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:42
 */
@Service
public class RefundReasonQueryServiceImpl implements RefundReasonQueryService {

    @Resource
    private RefundReasonRepository refundReasonRepository;

    @Override
    public QueryRefundReasonResp queryRefundReasonList() {
        QueryRefundReasonResp result = new QueryRefundReasonResp();
        List<RefundReason> refundReasonList = refundReasonRepository.queryRefundReasonList(new RefundReason());
        if(CollectionUtil.isEmpty(refundReasonList)){
            return result;
        }
        List<QueryRefundReasonRespDto> reasonRespDtos = new ArrayList<>();
        refundReasonList.forEach(v -> {
            QueryRefundReasonRespDto dto = new QueryRefundReasonRespDto();
            dto.setId(v.getId());
            dto.setAfterSalesText(v.getAfterSalesText());
            dto.setSequence(v.getSequence());
            dto.setCreateTime(v.getCreateTime());
            dto.setUpdateTime(v.getUpdateTime());
            reasonRespDtos.add(dto);
        });
        result.setReasonRespDtos(reasonRespDtos);
        return result;
    }


}
