package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.LogQueryService;
import com.sankuai.shangou.seashop.base.thrift.core.LogQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogDetailReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogMQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogSellerQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.LogDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.LogQueryResp;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:12
 */
@RestController
@RequestMapping("/log")
public class LogQueryController implements LogQueryFeign {

    @Resource
    private LogQueryService logQueryService;

    @PostMapping(value = "/pageMBaseLog", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<LogQueryResp>> pageMBaseLog(@RequestBody LogMQueryReq logMQueryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("pageMBaseLog", logMQueryReq, req -> {
            req.checkParameter();
            return logQueryService.pageMBaseLog(req);
        });
    }

    @PostMapping(value = "/pageSellerBaseLog", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<LogQueryResp>> pageSellerBaseLog(@RequestBody LogSellerQueryReq logSellerQueryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("pageSellerBaseLog", logSellerQueryReq, req -> {
            req.checkParameter();
            return logQueryService.pageSellerBaseLog(req);
        });
    }

    @PostMapping(value = "/queryBaseLogDetail", consumes = "application/json")
    @Override
    public ResultDto<LogDetailResp> queryBaseLogDetail(@RequestBody LogDetailReq logDetailReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBaseLogDetail", logDetailReq, req -> {
            req.checkParameter();
            return logQueryService.queryBaseLogDetail(req);
        });
    }
}
