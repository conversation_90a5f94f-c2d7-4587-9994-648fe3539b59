package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseShopReq;

public interface TopicService {

    Long create(BaseTopicReq topic);

    Boolean update(BaseTopicReq topic);

    BaseTopic getById(Long id,Long shopId);

    Boolean delete(BaseShopReq shopReq);

    Page<BaseTopic> queryWithPage(BaseTopicQueryReq query);

    List<BaseTopic> queryRecommend();

    void setHome(BaseShopReq shopReq);
}
