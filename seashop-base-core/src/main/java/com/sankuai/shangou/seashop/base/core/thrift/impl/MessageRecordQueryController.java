package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.SendMessageRecordService;
import com.sankuai.shangou.seashop.base.thrift.core.MessageRecordQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.MessageRecordQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordResp;

/**
 * @description:
 * @author: LXH
 **/
@RestController
@RequestMapping("/messageRecord")
public class MessageRecordQueryController implements MessageRecordQueryFeign {
    @Resource
    private SendMessageRecordService sendMessageRecordService;

    @PostMapping(value = "/queryPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<MessageRecordResp>> queryPage(@RequestBody MessageRecordQueryReq apiSendEmailMsgReq) {
        return ThriftResponseHelper.responseInvoke("queryPage", apiSendEmailMsgReq, req -> sendMessageRecordService.queryPage(apiSendEmailMsgReq));
    }

    @PostMapping(value = "/queryDetail", consumes = "application/json")
    @Override
    public ResultDto<MessageRecordDetailResp> queryDetail(@RequestBody BaseIdReq id) {
        return ThriftResponseHelper.responseInvoke("queryDetail", id, req -> sendMessageRecordService.queryDetail(id));
    }


}
