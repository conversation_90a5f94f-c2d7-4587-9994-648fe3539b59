package com.sankuai.shangou.seashop.base.core.service.impl;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.core.service.ExpressCmdService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseExpressCompany;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.repository.ExpressCompanyRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.CmdExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.ExpressSiteSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateExpressStatusReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateExpressTemplateReq;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ExpressCmdServiceImpl implements ExpressCmdService {

    @Resource
    private ExpressCompanyRepository expressCompanyRepository;

    @Resource
    private SiteSettingService settingService;

    // 快递公司状态（0：正常，1：删除）
    private static final int EXPRESS_STATUS_1 = 1;
    private static final int EXPRESS_STATUS_0 = 0;

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.INSERT,
            repository = "expressCompanyRepository", serviceMethod = "createExpressCompany",
            dto = AddExpressCompanyReq.class, entity = BaseExpressCompany.class, actionName = "平台添加快递公司")
    public void createExpressCompany(AddExpressCompanyReq addExpressCompanyReq) {
        BaseExpressCompany param = new BaseExpressCompany();
        param.setName(addExpressCompanyReq.getName());
        List<BaseExpressCompany> baseExpressCompanyList = expressCompanyRepository.queryExpressCompanyList(param);
        AssertUtil.throwIfTrue(CollectionUtil.isNotEmpty(baseExpressCompanyList), "快递公司名称已存在");
        BaseExpressCompany baseExpressCompany = new BaseExpressCompany();
        baseExpressCompany.setName(addExpressCompanyReq.getName());
        baseExpressCompany.setKuaidiniaoCode(addExpressCompanyReq.getKuaidiniaoCode());
        baseExpressCompany.setStatus(EXPRESS_STATUS_1);
        baseExpressCompany.setCreateDate(new Date());
        baseExpressCompany.setWangdiantongCode(addExpressCompanyReq.getWangdiantongCode());
        baseExpressCompany.setJushuitanCode(addExpressCompanyReq.getJushuitanCode());
        baseExpressCompany.setBoluopaiCode(addExpressCompanyReq.getBoluopaiCode());
        baseExpressCompany.setMeituanCode(addExpressCompanyReq.getMeituanCode());
        // 数据库这两个字段不能为空，那么默认值设置多少呢？？？
        baseExpressCompany.setWidth(10);
        baseExpressCompany.setHeight(10);
        expressCompanyRepository.insert(baseExpressCompany);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "expressCompanyRepository", serviceMethod = "updateExpressCompany",
            dto = CmdExpressCompanyReq.class, entity = BaseExpressCompany.class, actionName = "平台修改快递公司")
    public void updateExpressCompany(CmdExpressCompanyReq cmdExpressCompanyReq) {
        BaseExpressCompany panDuanName = new BaseExpressCompany();
        panDuanName.setName(cmdExpressCompanyReq.getName());
        List<BaseExpressCompany> panDuanNameList = expressCompanyRepository.queryExpressCompanyList(panDuanName);
        boolean isNotEmpty = CollectionUtil.isNotEmpty(panDuanNameList);
        AssertUtil.throwIfTrue(isNotEmpty && !cmdExpressCompanyReq.getId().equals(panDuanNameList.get(0).getId()), "快递公司名称已存在");

        BaseExpressCompany panDuanDelete = new BaseExpressCompany();
        panDuanDelete.setId(cmdExpressCompanyReq.getId());
        List<BaseExpressCompany> panDuanDeleteList = expressCompanyRepository.queryExpressCompanyList(panDuanDelete);
        boolean isDelete = CollectionUtil.isEmpty(panDuanDeleteList);
        AssertUtil.throwIfTrue(isDelete && panDuanDeleteList.get(0).getStatus() == 1, "快递公司不存在或已删除");

        BaseExpressCompany baseExpressCompany = new BaseExpressCompany();
        baseExpressCompany.setId(cmdExpressCompanyReq.getId());
        baseExpressCompany.setName(cmdExpressCompanyReq.getName());
        baseExpressCompany.setKuaidiniaoCode(cmdExpressCompanyReq.getKuaidiniaoCode());
        baseExpressCompany.setWangdiantongCode(cmdExpressCompanyReq.getWangdiantongCode());
        baseExpressCompany.setJushuitanCode(cmdExpressCompanyReq.getJushuitanCode());
        baseExpressCompany.setBoluopaiCode(cmdExpressCompanyReq.getBoluopaiCode());
        baseExpressCompany.setMeituanCode(cmdExpressCompanyReq.getMeituanCode());
        expressCompanyRepository.updateById(baseExpressCompany);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "expressCompanyRepository", serviceMethod = "updateExpressTemplate",
            dto = UpdateExpressTemplateReq.class, entity = BaseExpressCompany.class, actionName = "平台修改快递公司模板")
    public void updateExpressTemplate(UpdateExpressTemplateReq updateExpressTemplateReq) {
        BaseExpressCompany param = new BaseExpressCompany();
        param.setName(updateExpressTemplateReq.getName());
        List<BaseExpressCompany> baseExpressCompanyList = expressCompanyRepository.queryExpressCompanyList(param);
        boolean isNotEmpty = CollectionUtil.isNotEmpty(baseExpressCompanyList);
        AssertUtil.throwIfTrue(isNotEmpty && !updateExpressTemplateReq.getId().equals(baseExpressCompanyList.get(0).getId()), "快递公司名称已存在");
        BaseExpressCompany baseExpressCompany = new BaseExpressCompany();
        baseExpressCompany.setId(updateExpressTemplateReq.getId());
        baseExpressCompany.setName(updateExpressTemplateReq.getName());
        baseExpressCompany.setWidth(updateExpressTemplateReq.getWidth());
        baseExpressCompany.setHeight(updateExpressTemplateReq.getHeight());
        baseExpressCompany.setLogo(updateExpressTemplateReq.getLogo());
        baseExpressCompany.setBackgroundImage(updateExpressTemplateReq.getBackgroundImage());
        expressCompanyRepository.updateById(baseExpressCompany);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "expressCompanyRepository", serviceMethod = "updateExpressStatus",
            dto = UpdateExpressStatusReq.class, entity = BaseExpressCompany.class, actionName = "快递公司管理状态变更")
    public void updateExpressStatus(UpdateExpressStatusReq updateExpressStatusReq) {
        BaseExpressCompany baseExpressCompany = new BaseExpressCompany();
        baseExpressCompany.setId(updateExpressStatusReq.getId());
        baseExpressCompany.setStatus(updateExpressStatusReq.getStatus());
        expressCompanyRepository.updateById(baseExpressCompany);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MOVE,
            repository = "expressCompanyRepository", serviceMethod = "deleteExpressCompany",
            dto = DeleteExpressCompanyReq.class, entity = BaseExpressCompany.class, actionName = "删除快递公司")
    public void deleteExpressCompany(DeleteExpressCompanyReq deleteExpressCompanyReq) {
        expressCompanyRepository.deleteById(deleteExpressCompanyReq.getId());
    }

    @Override
    public void addOrUpdateSiteSetting(ExpressSiteSettingReq expressSiteSettingReq) {
        Field[] fields = expressSiteSettingReq.getClass().getDeclaredFields();
        Map<String, String> settings = new Hashtable<>();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                String name = field.getName();
                Object value = field.get(expressSiteSettingReq);
                if (value != null) {
                    settings.put(name, value.toString());
                }
            } catch (IllegalAccessException e) {
                log.error("ExpressCmdServiceImpl addOrUpdateSiteSetting is error e={}",e);
            }
        }
        AssertUtil.throwIfTrue(settings.size() < 1, "快递鸟保存失败");
        List<BaseSiteSetting> settingList = new ArrayList<>();
        for (Map.Entry<String, String> entry : settings.entrySet()) {
            BaseSiteSetting setting = new BaseSiteSetting();
            setting.setKey(entry.getKey());
            setting.setValue(entry.getValue());
            settingList.add(setting);
        }
        settingService.updateSettingsByKey(settingList);
    }
}
