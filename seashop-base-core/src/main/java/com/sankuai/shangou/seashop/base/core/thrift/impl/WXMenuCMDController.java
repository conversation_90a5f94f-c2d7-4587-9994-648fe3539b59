package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.WXMenuService;
import com.sankuai.shangou.seashop.base.thrift.core.WXMenuCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWXMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveWXAccountReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.WXAccountResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/WXMenu")
public class WXMenuCMDController implements WXMenuCMDFeign {
    @Resource
    private WXMenuService wxMenuService;


    @Override
    @PostMapping(value = "/saveWXAccount", consumes = "application/json")
    public ResultDto<BaseResp> saveWXAccount(@RequestBody SaveWXAccountReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("saveWXAccount", request, req -> {
            wxMenuService.saveWXAccount(req);
            return new BaseResp();
        });
    }

    @Override
    @GetMapping(value = "/getWXAccount", consumes = "application/json")
    public ResultDto<WXAccountResp> getWXAccount() throws TException {
        return ThriftResponseHelper.responseInvoke("saveWXAccount", null, req -> {
            return wxMenuService.getWXAccount();
        });
    }

    @Override
    @PostMapping(value = "/create", consumes = "application/json")
    public ResultDto<Integer> create(@RequestBody BaseWXMenuReq wxMenuReq) throws TException {
        wxMenuReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("createWXMenu", wxMenuReq, req -> {
            Integer id = wxMenuService.create(req);
            return id;
        });
    }

    @Override
    @PostMapping(value = "/update", consumes = "application/json")
    public ResultDto<Boolean> update(@RequestBody BaseWXMenuReq wxMenuReq) throws TException {
        wxMenuReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("updateWXMenu", wxMenuReq, req -> {
            return wxMenuService.update(wxMenuReq);
        });
    }

    @Override
    @PostMapping(value = "/delete", consumes = "application/json")
    public ResultDto<Boolean> delete(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteWXMenu", baseIdReq, req -> {
            return wxMenuService.delete(baseIdReq);
        });
    }

    //菜单同步到微信
    @PostMapping(value = "/syncWXMenu", consumes = "application/json")
    public ResultDto<Boolean> syncWXMenu() throws TException {
        return ThriftResponseHelper.responseInvoke("syncWXMenu", null, req -> {
            return wxMenuService.syncWechatMenu();
        });
    }
}
