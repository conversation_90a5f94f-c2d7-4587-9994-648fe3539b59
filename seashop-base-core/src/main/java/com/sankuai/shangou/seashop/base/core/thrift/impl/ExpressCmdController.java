package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.ExpressCmdService;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.CmdExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.ExpressSiteSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateExpressStatusReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateExpressTemplateReq;

@RestController
@RequestMapping("/express")
public class ExpressCmdController implements ExpressCmdFeign {

    @Resource
    private ExpressCmdService expressCmdService;

    @PostMapping(value = "/createExpressCompany", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> createExpressCompany(@RequestBody AddExpressCompanyReq addExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createExpressCompany", addExpressCompanyReq, req -> {
            req.checkParameter();
            expressCmdService.createExpressCompany(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateExpressCompany", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateExpressCompany(@RequestBody CmdExpressCompanyReq cmdExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateExpressCompany", cmdExpressCompanyReq, req -> {
            req.checkParameter();
            expressCmdService.updateExpressCompany(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateExpressTemplate", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateExpressTemplate(@RequestBody UpdateExpressTemplateReq updateExpressTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateExpressTemplate", updateExpressTemplateReq, req -> {
            req.checkParameter();
            expressCmdService.updateExpressTemplate(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateExpressStatus", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateExpressStatus(@RequestBody UpdateExpressStatusReq updateExpressStatusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateExpressStatus", updateExpressStatusReq, req -> {
            req.checkParameter();
            expressCmdService.updateExpressStatus(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/deleteExpressCompany", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> deleteExpressCompany(@RequestBody DeleteExpressCompanyReq deleteExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteExpressCompany", deleteExpressCompanyReq, req -> {
            req.checkParameter();
            expressCmdService.deleteExpressCompany(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/addOrUpdateSiteSetting", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addOrUpdateSiteSetting(@RequestBody ExpressSiteSettingReq expressSiteSettingReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addOrUpdateSiteSetting", expressSiteSettingReq, req -> {
            req.checkParameter();
            expressCmdService.addOrUpdateSiteSetting(req);
            return new BaseResp();
        });

    }
}
