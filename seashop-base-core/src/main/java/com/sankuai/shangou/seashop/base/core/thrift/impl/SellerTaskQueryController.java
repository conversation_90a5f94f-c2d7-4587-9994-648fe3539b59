package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.SellerTaskService;
import com.sankuai.shangou.seashop.base.thrift.core.SellerTaskQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.dto.SellerTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.task.SellerTaskListResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/sellerTask")
public class SellerTaskQueryController implements SellerTaskQueryFeign {

    @Resource
    private SellerTaskService sellerTaskService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<SellerTaskDto>> pageList(@RequestBody QueryTaskReq queryReq) throws TException {
        log.info("【供应商后台任务】查询分页列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
        return ThriftResponseHelper.responseInvoke("pageList", queryReq, func -> {
            queryReq.checkParameter();
            // 业务逻辑处理
            return sellerTaskService.pageList(queryReq);
        });
    }

    @PostMapping(value = "/queryList", consumes = "application/json")
    @Override
    public ResultDto<SellerTaskListResp> queryList(@RequestBody QueryTaskReq queryReq) throws TException {
        log.info("【供应商后台任务】查询分页列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
        return ThriftResponseHelper.responseInvoke("queryList", queryReq, func -> {
            // 业务逻辑处理
            List<SellerTaskDto> list = sellerTaskService.queryList(queryReq);
            return new SellerTaskListResp(list);
        });
    }
}
