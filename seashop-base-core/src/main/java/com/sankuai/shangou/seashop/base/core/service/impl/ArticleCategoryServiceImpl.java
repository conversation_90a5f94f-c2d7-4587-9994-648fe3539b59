package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.core.service.ArticleCategoryService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategory;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategoryExample;
import com.sankuai.shangou.seashop.base.dao.core.repository.ArticleCategoryRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.ArticleRepository;

@Service
public class ArticleCategoryServiceImpl implements ArticleCategoryService {

    @Resource
    private ArticleCategoryRepository articleCategoryRepository;

    @Resource
    private ArticleRepository articleRepository;

    @Override
    public Long create(BaseArticleCategory category) {
        long articleId = articleCategoryRepository.create(category);
        return articleId;
    }

    @Override
    public Boolean update(BaseArticleCategory category) {
        boolean result = articleCategoryRepository.update(category);
        return result;
    }

    @Override
    public Boolean deletes(List<Long> ids) {

        boolean result = articleCategoryRepository.deletes(ids);
        return result;
    }

    @Override
    public Long getArticleCount(List<Long> ids) {
        return articleRepository.getCountByCateId(ids);
    }

    @Override
    public List<BaseArticleCategory> query() {
        BaseArticleCategoryExample example = new BaseArticleCategoryExample();

        example.setOrderByClause("display_sequence desc,id desc");

        List<BaseArticleCategory> list = query(example);
        return list;
    }

    @Override
    public List<BaseArticleCategory> query(BaseArticleCategoryExample example) {
        List<BaseArticleCategory> list = articleCategoryRepository.query(example);
        return list;
    }

    @Override
    public BaseArticleCategory getById(long id) {
        BaseArticleCategory result = articleCategoryRepository.getById(id);
        return result;
    }


    @Override
    public List<BaseArticleCategory> getChildsById(Long id) {
        List<BaseArticleCategory> result = articleCategoryRepository.getChildsById(id);
        return result;
    }
}
