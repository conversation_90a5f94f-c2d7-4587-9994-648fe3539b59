package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.SendMessageRecordService;
import com.sankuai.shangou.seashop.base.thrift.core.MessageRecordCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.MessageRecordCmdReq;

/**
 * @description:
 * @author: LXH
 **/
@RestController
@RequestMapping("/messageRecord")
public class MessageRecordCmdController implements MessageRecordCmdFeign {
    @Resource
    private SendMessageRecordService sendMessageRecordService;

    @PostMapping(value = "/addRecord", consumes = "application/json")
    @Override
    public ResultDto<Long> addRecord(@RequestBody MessageRecordCmdReq request) {
        return ThriftResponseHelper.responseInvoke("addRecord", request, req -> sendMessageRecordService.addRecord(request));
    }

    @PostMapping(value = "/deleteRecord", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> deleteRecord(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteRecord", request, req -> sendMessageRecordService.deleteRecord(req));
    }
}
