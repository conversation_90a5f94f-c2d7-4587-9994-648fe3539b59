package com.sankuai.shangou.seashop.base.core.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.dto.BaseOperationLogDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.BaseOperationLogService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseOperationLog;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseOperationLogRepository;

@Service
public class BaseOperationLogServiceImpl implements BaseOperationLogService {
    @Resource
    private BaseOperationLogRepository operationLogRepository;

    @Override
    public long insert(BaseOperationLogDto operationLogDto) {
        BaseOperationLog operationLog = JsonUtil.copy(operationLogDto,BaseOperationLog.class);
        return operationLogRepository.insert(operationLog);
    }
}
