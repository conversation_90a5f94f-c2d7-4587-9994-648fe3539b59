package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.core.service.model.BasePhotoSpaceBo;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace;
import com.sankuai.shangou.seashop.base.thrift.core.request.BasePhotoSpaceReq;

public interface ImageService {
    /**
     * 添加图片
     *
     * @param photo
     */
    Long create(BasePhotoSpaceReq photo);

    void batchCreate(List<BasePhotoSpaceReq> photos);
    /**
     * 删除图片
     *
     * @param photo
     * @return
     */
    Boolean delete(BasePhotoSpaceReq photo);

    /**
     * 分页查询图片
     *
     * @param query
     * @return
     */
    Page<BasePhotoSpace> queryWithPage(BasePhotoSpaceBo query);

    Page<BasePhotoSpace> queryAuditWithPage(BasePhotoSpaceBo query);
    /**
     * 移动图片
     *
     * @param id     图片id
     * @param catId  分类id
     * @param shopId 店铺id
     * @return
     */
    Boolean move(List<Long> ids, long catId, long shopId);

    /**
     * 将一个分类下的所有图片移动下另外一个分类
     *
     * @param catId
     * @param targetCatId
     * @param shopId
     * @return
     */
    Boolean moveImages(long catId, long targetCatId, long shopId);


    /**
     * 批量删除图片
     *
     * @param ids    id集合
     * @param shopId 店铺id
     * @return
     */
    Boolean deletes(List<Long> ids, long shopId);


    /** 根据分类id 获取图片数量
     * @param id
     * @param shopId
     * @return
     */
    int getCountByCategoryId(Long id, long shopId);

    /**
     * 修改图片
     * @param photoSpace
     * @return
     */
    Boolean update(BasePhotoSpace photoSpace);
}
