package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.SellerTaskService;
import com.sankuai.shangou.seashop.base.core.service.model.task.CompleteTaskBo;
import com.sankuai.shangou.seashop.base.core.service.model.task.CreateTaskBo;
import com.sankuai.shangou.seashop.base.core.service.model.task.ExceptionTaskBo;
import com.sankuai.shangou.seashop.base.dao.core.domain.SellerTaskInfo;
import com.sankuai.shangou.seashop.base.dao.core.po.task.QueryTaskPo;
import com.sankuai.shangou.seashop.base.dao.core.repository.SellerTaskRepository;
import com.sankuai.shangou.seashop.base.thrift.core.dto.SellerTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TaskStatusEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SellerTaskServiceImpl implements SellerTaskService {

    @Resource
    private SellerTaskRepository sellerTaskRepository;

    @Override
    public Long createTask(CreateTaskBo createTaskBo) {
        SellerTaskInfo task = new SellerTaskInfo();
        task.setBizType(createTaskBo.getBizType());
        task.setTaskType(createTaskBo.getTaskType());
        task.setTaskName(createTaskBo.getTaskName());
        task.setOperatorId(createTaskBo.getOperatorId());
        task.setOperatorAccount(createTaskBo.getOperatorAccount());
        task.setEnv(createTaskBo.getEnv());
        task.setCreateTime(new Date());
        task.setTaskStatus(TaskStatusEnum.READY.getCode());
        task.setExecuteParam(createTaskBo.getExecuteParam());
        sellerTaskRepository.save(task);
        return task.getId();
    }

    @Override
    public void startTask(Long taskId) {
        SellerTaskInfo task = sellerTaskRepository.getById(taskId);
        AssertUtil.throwIfNull(task, "任务不存在");

        SellerTaskInfo updateTask = new SellerTaskInfo();
        updateTask.setTaskStatus(TaskStatusEnum.PROCESSING.getCode());
        updateTask.setBeginTime(new Date());
        // 这里字段定义了默认值0，所以重试次数代表的是执行次数
        int retryTimes = task.getRetryTimes() == null ? 0 : task.getRetryTimes();
        updateTask.setRetryTimes(retryTimes + 1);
        sellerTaskRepository.updateTaskById(taskId, updateTask);
    }

    @Override
    public void completeTask(CompleteTaskBo completeTaskBo) {
        SellerTaskInfo task = sellerTaskRepository.getById(completeTaskBo.getTaskId());
        AssertUtil.throwIfNull(task, "任务不存在");
        Date now = new Date();

        SellerTaskInfo updateTask = new SellerTaskInfo();
        updateTask.setTaskStatus(TaskStatusEnum.SUCCESS.getCode());
        updateTask.setTotalNum(completeTaskBo.getTotalNum());
        updateTask.setSuccessNum(completeTaskBo.getSuccessNum());
        updateTask.setFailedNum(completeTaskBo.getFailedNum());
        updateTask.setExecuteResult(completeTaskBo.getExecuteResult());
        updateTask.setEndTime(now);
        updateTask.setCost((int) (now.getTime() - task.getBeginTime().getTime()));
        updateTask.setFilePath(completeTaskBo.getFilePath());
        sellerTaskRepository.updateTaskById(completeTaskBo.getTaskId(), updateTask);
    }

    @Override
    public void exceptionTask(ExceptionTaskBo exceptionTaskBo) {
        SellerTaskInfo task = sellerTaskRepository.getById(exceptionTaskBo.getTaskId());
        AssertUtil.throwIfNull(task, "任务不存在");
        Date now = new Date();

        SellerTaskInfo updateTask = new SellerTaskInfo();
        updateTask.setTaskStatus(TaskStatusEnum.FAILED.getCode());
        updateTask.setExecuteResult(exceptionTaskBo.getExceptionContent());
        updateTask.setEndTime(now);
        updateTask.setCost((int) (now.getTime() - task.getBeginTime().getTime()));
        sellerTaskRepository.updateTaskById(exceptionTaskBo.getTaskId(), updateTask);
    }

    @Override
    public BasePageResp<SellerTaskDto> pageList(QueryTaskReq queryReq) {
        BasePageParam pageParam = queryReq.buildPage();
        Page<SellerTaskInfo> pageResult = PageHelper.startPage(pageParam);
        QueryTaskPo queryParam = QueryTaskPo.builder()
                .operatorIdEq(queryReq.getOperatorId())
                .envEq(queryReq.getEnv())
                .taskTypeEq(queryReq.getTaskType())
                .bizTypeEq(queryReq.getBizType())
                .build();
        List<SellerTaskInfo> taskList = sellerTaskRepository.getByCondition(queryParam);
        return PageResultHelper.transfer(pageResult, SellerTaskDto.class, dto -> dto.setTaskStatusDesc(TaskStatusEnum.getDesc(dto.getTaskStatus())));
    }

    @Override
    public List<SellerTaskDto> queryList(QueryTaskReq queryReq) {
        QueryTaskPo queryParam = QueryTaskPo.builder()
                .envEq(queryReq.getEnv())
                .taskTypeEq(queryReq.getTaskType())
                .taskStatusListIn(queryReq.getTaskStatusList())
                .build();
        List<SellerTaskInfo> taskList = sellerTaskRepository.getByCondition(queryParam);
        if (CollUtil.isEmpty(taskList)){
            return null;
        }
        return JsonUtil.copyList(taskList, SellerTaskDto.class);
    }
}
