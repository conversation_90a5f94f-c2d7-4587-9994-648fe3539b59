package com.sankuai.shangou.seashop.base.core.service.impl;

import java.io.*;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.common.enums.BaseTemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.common.util.template.TemplateStorageService;
import com.sankuai.shangou.seashop.base.core.service.BaseTemplatePageService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ClientTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.PlateTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWapTopicRes;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.base.core.service.TopicService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicExample;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseTopicRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseShopReq;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TopicServiceImpl implements TopicService {

    @Resource
    private BaseTopicRepository repository;

    @Value("${hishop.storage.domain}")
    private String hostName;
    @Value("${hishop.storage.base-path}")
    private String bucketName;
    @Resource
    private S3plusStorageService s3plusStorageService;
    @Resource
    private TemplateStorageService storageService;

    @Resource
    private BaseTemplatePageService templatePageService;

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "新增专题",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.INSERT,
            repository = "repository", serviceMethod = "create",
            dto = BaseTopicReq.class, entity = BaseTopic.class)
    public Long create(BaseTopicReq topic) {
        BaseTopic dao = JsonUtil.copy(topic, BaseTopic.class);
        dao.setCreateTime(new Date());
        dao.setModifyTime(new Date());
        return repository.create(dao);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "修改专题",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "repository", serviceMethod = "create",
            dto = BaseTopicReq.class, entity = BaseTopic.class)
    public Boolean update(BaseTopicReq topic) {
        BaseTopic dao = JsonUtil.copy(topic, BaseTopic.class);
        dao.setModifyTime(new Date());
        BaseTopic oldModel = getById(topic.getId(), topic.getShopId());
        repository.update(dao);
        if (oldModel.getHome() != null && oldModel.getHome()) {
            BaseShopReq baseShopReq = new BaseShopReq();
            baseShopReq.setId(oldModel.getId());
            baseShopReq.setShopId(oldModel.getShopId());
            setHome(baseShopReq);
        }

        return true;
    }

    @Override
    public BaseTopic getById(Long id, Long shopId) {
        return repository.getById(id, shopId);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "删除专题",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MOVE,
            repository = "repository", serviceMethod = "delete",
            dto = BaseShopReq.class, entity = BaseTopic.class)
    public Boolean delete(BaseShopReq shopReq) {
        return repository.delete(shopReq.getId(), shopReq.getShopId());
    }

    @Override
    public Page<BaseTopic> queryWithPage(BaseTopicQueryReq query) {
        BasePageParam page = new BasePageParam();
        page.setPageNum(query.getPageNo());
        page.setPageSize(query.getPageSize());
        Page<BaseTopic> pageResult = PageHelper.startPage(page);
        BaseTopicExample example = new BaseTopicExample();
        BaseTopicExample.Criteria criteria = example.createCriteria();

        criteria.andShopIdEqualTo(query.getShopId());
        if (!StringUtils.isEmpty(query.getTags())) {
            criteria.andTagsLike("%" + query.getTags() + "%");
        }
        if (!StringUtils.isEmpty(query.getName())) {
            criteria.andNameLike("%" + query.getName() + "%");
        }
        if (query.getPlatForm() != null) {
            criteria.andPlatFormEqualTo(query.getPlatForm());
        }
        example.setOrderByClause("id desc");
        repository.query(example);
        return pageResult;
    }

    @Override
    public List<BaseTopic> queryRecommend() {
        BaseTopicExample example = new BaseTopicExample();
        BaseTopicExample.Criteria criteria = example.createCriteria();
        criteria.andIsRecommendEqualTo(true);
        criteria.andShopIdEqualTo(CommonConstant.PLATFORM_ID);
        List<BaseTopic> result = repository.query(example);
        return result;
    }

    @Override
    @Transactional
    public void setHome(BaseShopReq shopReq) {
        BaseTopic topic = new BaseTopic();
        topic.setShopId(shopReq.getShopId());
        topic.setId(shopReq.getId());
        topic.setHome(true);

        BaseTopic oldModel = getById(shopReq.getId(), shopReq.getShopId());


        BaseTopicExample example1 = new BaseTopicExample();
        BaseTopicExample.Criteria criteria1 = example1.createCriteria();
        criteria1.andShopIdEqualTo(topic.getShopId());
        criteria1.andPlatFormEqualTo(oldModel.getPlatForm());

        BaseTopic topic1 = new BaseTopic();
        topic1.setHome(false);
        repository.updateOnlyModel(topic1, example1);

        BaseTopicExample example = new BaseTopicExample();
        BaseTopicExample.Criteria criteria = example.createCriteria();
        criteria.andIdEqualTo(topic.getId());
        criteria.andShopIdEqualTo(topic.getShopId());
        repository.updateOnlyModel(topic, example);
        BaseTemplateClientTypeEnum clientTypeEnum = BaseTemplateClientTypeEnum.WXSmallProgramSpecial;

        BaseTemplateClientTypeEnum clientIndexTypeEnum = BaseTemplateClientTypeEnum.WXSmallProgramSpecial;
        if (PlateTypeEnum.WXA.getCode().equals(oldModel.getPlatForm())) {
            if (shopReq.getShopId() == 0L) {
                clientTypeEnum = BaseTemplateClientTypeEnum.WXSmallProgramSpecial;
                clientIndexTypeEnum = BaseTemplateClientTypeEnum.WXSmallProgram;
            } else {
                clientTypeEnum = BaseTemplateClientTypeEnum.SellerWxSmallProgramSpecial;
                clientIndexTypeEnum = BaseTemplateClientTypeEnum.WXSmallProgramSellerWapIndex;
            }
        } else if (PlateTypeEnum.Mobile.getCode().equals(oldModel.getPlatForm())) {
            if (shopReq.getShopId() == 0L) {
                clientTypeEnum = BaseTemplateClientTypeEnum.WapSpecial;
                clientIndexTypeEnum = BaseTemplateClientTypeEnum.WapIndex;
            } else {
                clientTypeEnum = BaseTemplateClientTypeEnum.SellerWapSpecial;
                clientIndexTypeEnum = BaseTemplateClientTypeEnum.SellerWapIndex;
            }
        } else if (PlateTypeEnum.PC.getCode().equals(oldModel.getPlatForm())) {
            if (shopReq.getShopId() == 0L) {
                clientTypeEnum = BaseTemplateClientTypeEnum.PCTOPIC;
                clientIndexTypeEnum = BaseTemplateClientTypeEnum.PCIndex;
            } else {
                clientTypeEnum = BaseTemplateClientTypeEnum.PCTOPIC_SELLER;
                clientIndexTypeEnum = BaseTemplateClientTypeEnum.PCIndex_SELLER;
            }
        }

        //设置为首页
        String json = getJson(shopReq.getId().toString(), clientTypeEnum, shopReq.getShopId());
        setIndex(clientTypeEnum, clientIndexTypeEnum, shopReq.getShopId(), json);
    }


    private void setIndex(BaseTemplateClientTypeEnum clientTypeEnum, BaseTemplateClientTypeEnum indexClient, Long shopId, String json) {
        JsonNode node = JsonUtil.parseObject(json, JsonNode.class);
        BaseTemplatePageWithBLOBs templatePageWithBLOBs = new BaseTemplatePageWithBLOBs();
        templatePageWithBLOBs.setjPage(node.get("page") == null ? "{}" : JsonUtil.toJsonString(node.get("page")));
        templatePageWithBLOBs.setlModules(node.get("LModules") == null ? "{}" : JsonUtil.toJsonString(node.get("LModules")));
        templatePageWithBLOBs.setpModules(node.get("PModules") == null ? "{}" : JsonUtil.toJsonString(node.get("PModules")));
        templatePageWithBLOBs.setClient(ClientTypeEnum.Default.getCode());
        templatePageWithBLOBs.setType(indexClient.getCode());
        templatePageWithBLOBs.setShopId(shopId);
        templatePageWithBLOBs.setvShopId(0L);
        templatePageWithBLOBs.setUpdateTime(new Date());
        templatePageService.setShopIndex(templatePageWithBLOBs, indexClient);
    }

    private String getJson(String client, BaseTemplateClientTypeEnum clientTypeEnum, Long shopId) {
        String filePath = storageService.getTemplatePath(client, clientTypeEnum, shopId);
        String dataPath = hostName + bucketName + "/" + filePath + "data/default.json";
        try {
            InputStream stream = s3plusStorageService.readExcelFromOnlineUrl(dataPath);
            InputStreamReader inputStreamReader = new InputStreamReader(stream);
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
                stringBuilder.append(System.lineSeparator());
            }
            String jsonStr = stringBuilder.toString();
            jsonStr = jsonStr.trim().replaceAll("\n$", "");

            return jsonStr;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }
}
