package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.common.enums.BaseTemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageKey;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs;

public interface BaseTemplatePageService {

    Boolean editShopIndex(BaseTemplatePageWithBLOBs baseTemplatePageWithBLOBs);

    Boolean setShopIndex(BaseTemplatePageWithBLOBs baseTemplatePageWithBLOBs, BaseTemplateClientTypeEnum baseTemplateClientTypeEnum);
    BaseTemplatePageWithBLOBs getById(BaseTemplatePageKey key);
}
