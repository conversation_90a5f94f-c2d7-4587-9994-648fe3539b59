package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.dianping.sc.express.dto.ExpressMainDTO;
import com.dianping.sc.express.dto.LoadSubscribedExpressRequest;
import com.dianping.sc.express.dto.SubmitExpressSubscribeRequest;
import com.hishop.starter.logistics.LogisticsClient;
import com.hishop.starter.logistics.model.Logistics;
import com.hishop.starter.logistics.model.LogisticsAccountParam;
import com.hishop.starter.logistics.model.ShipperCodeEnum;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.remote.RemoteExpressCompanyThriftService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressTrackQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.ExpressSubscribeReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LoadSubscribedExpressReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressConfigRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressContactRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressTrackRes;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/expressTrack")
public class ExpressTrackQueryController implements ExpressTrackQueryFeign {

    @Resource
    private RemoteExpressCompanyThriftService expressCompanyThriftService;

    @Resource
    private SiteSettingService settingService;

    @Resource
    private LogisticsClient logisticsClient;

    @Override
    public ResultDto<Boolean> synchroSupportCompany() throws TException {

        int i = 0;
        return ThriftResponseHelper.responseInvoke("synchroSupportCompany", i, req -> {
//            try {
//                List<ExpressCompanyDTO> companys = expressCompanyThriftService.loadBizSupportCompany();
//            } catch (TException e) {
//                throw new SystemException();
//            }

            //todo 这里需要把美团的编码根据快递公司名称与我们自己的物流公司匹配 然后把编码写到我们自己的物流公司里
            return true;
        });

    }

    @PostMapping(value = "/submitExpressSubscribe", consumes = "application/json")
    @Override
    public ResultDto<Long> submitExpressSubscribe(@RequestBody ExpressSubscribeReq request) {
        return ThriftResponseHelper.responseInvoke("submitExpressSubscribe", request, req -> {
            return submitExpressSubscribeRequest(req);
        });
    }

    @PostMapping(value = "/batchSubmitExpressSubscribe", consumes = "application/json")
    @Override
    public ResultDto<Boolean> batchSubmitExpressSubscribe(@RequestBody List<ExpressSubscribeReq> request) {
        return ThriftResponseHelper.responseInvoke("batchSubmitExpressSubscribe", request, list -> {
            for (ExpressSubscribeReq req : list) {
                Long result = submitExpressSubscribeRequest(req);
            }
            return true;
        });
    }

    @PostMapping(value = "/loadSubscribedExpressByCompanyCodeAndExpressNo", consumes = "application/json")
    @Override
    public ResultDto<ExpressTrackRes> loadSubscribedExpressByCompanyCodeAndExpressNo(@RequestBody LoadSubscribedExpressReq request) {
        return ThriftResponseHelper.responseInvoke("loadSubscribedExpressByCompanyCodeAndExpressNo", request, req -> {

            LoadSubscribedExpressRequest query = JsonUtil.copy(req, LoadSubscribedExpressRequest.class);
            ExpressMainDTO expressMainDTO = expressCompanyThriftService.loadSubscribedExpressByCompanyCodeAndExpressNo(query);
            ExpressTrackRes result = JsonUtil.copy(expressMainDTO, ExpressTrackRes.class);
            result.setReceiver(JsonUtil.copy(expressMainDTO.getReceiver(), ExpressContactRes.class));
            result.setSender(JsonUtil.copy(expressMainDTO.getSender(), ExpressContactRes.class));
            return result;
        });
    }


    @PostMapping(value = "/loadSubscribedExpressByHiShopCompanyCodeAndExpressNo", consumes = "application/json")
    @Override
    public ResultDto<ExpressTrackRes> loadSubscribedExpressByHiShopCompanyCodeAndExpressNo(@RequestBody LoadSubscribedExpressReq request) {
        return ThriftResponseHelper.responseInvoke("loadSubscribedExpressByHiShopCompanyCodeAndExpressNo", request, req -> {

            ExpressConfigRes res = settingService.queryExpressConfig();
            if (StringUtils.isEmpty(res.getExpressAppKey()) || StringUtils.isEmpty(res.getExpressSecret())) {
                throw new BusinessException("系统还未配置物流appkey与secret,请配置后重试");
            }
            LogisticsAccountParam account = new LogisticsAccountParam();
            account.setAppKey(res.getExpressAppKey());
            account.setAppSecret(res.getExpressSecret());
            try {
                Logistics logistics = logisticsClient.query(account, ShipperCodeEnum.getShipperCode(req.getCompanyCode()), req.getExpressNo(), req.getReceiveMobile());
                ExpressTrackRes result = JsonUtil.copy(logistics, ExpressTrackRes.class);
                return result;
            } catch (Exception ex) {
                ExpressTrackRes result = new ExpressTrackRes();
                result.setExpressNo(req.getExpressNo());
                result.setCompanyCode(req.getCompanyCode());
                result.setExpressProgressItemDTOList(new ArrayList<>());
                return result;
            }

        });
    }

    @PostMapping(value = "/queryBalance")
    @Override
    public ResultDto<Long> queryBalance() {
        return ThriftResponseHelper.responseInvoke("queryBalance", null, req -> {
            ExpressConfigRes config = this.settingService.queryExpressConfig();
            LogisticsAccountParam account = new LogisticsAccountParam();
            account.setAppKey(config.getExpressAppKey());
            account.setAppSecret(config.getExpressSecret());
            return logisticsClient.queryBalance(account);
        });


    }


    private Long submitExpressSubscribeRequest(ExpressSubscribeReq request) {
        SubmitExpressSubscribeRequest submitExpressSubscribeRequest = new SubmitExpressSubscribeRequest();
        submitExpressSubscribeRequest.setBizId(request.getBizId());
        submitExpressSubscribeRequest.setExpressNo(request.getExpressNo());
        submitExpressSubscribeRequest.setCompanyCode(request.getCompanyCode());
        submitExpressSubscribeRequest.setPlatform(request.getPlatform());
        submitExpressSubscribeRequest.setReceiver(request.getReceiver());
        submitExpressSubscribeRequest.setSender(request.getSender());
        Long result = expressCompanyThriftService.submitExpressSubscribe(submitExpressSubscribeRequest);
        return result;
    }
}
