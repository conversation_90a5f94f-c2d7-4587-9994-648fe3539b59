package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.CustomFormService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField;
import com.sankuai.shangou.seashop.base.thrift.core.CustomFormQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryCustomerFormFieldReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryCustomerFormReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormFieldListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormFieldRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormRes;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:43
 */
@RestController
@RequestMapping("/customForm")
public class CustomFormQueryController implements CustomFormQueryFeign {

    @Resource
    private CustomFormService customFormService;

    @PostMapping(value = "/queryCustomFormByFormIds", consumes = "application/json")
    @Override
    public ResultDto<BaseCustomFormListRes> queryCustomFormByFormIds(@RequestBody QueryCustomerFormReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCustomFormByFormIds", request, req -> {

            List<BaseCustomFormRes> forms = customFormService.queryCustomFormByFormIds(req.getFormIds());
            return BaseCustomFormListRes.builder().formList(forms).build();
        });
    }

    @PostMapping(value = "/queryCustomFieldByFieldIds", consumes = "application/json")
    @Override
    public ResultDto<BaseCustomFormFieldListRes> queryCustomFieldByFieldIds(@RequestBody QueryCustomerFormFieldReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCustomFieldByFieldIds", request, req -> {

            List<BaseCustomFormField> fields = customFormService.queryCustomFieldByFieldIds(req.getFieldIds());
            List<BaseCustomFormFieldRes> formResList = JsonUtil.copyList(fields, BaseCustomFormFieldRes.class);
            return BaseCustomFormFieldListRes.builder().fieldList(formResList).build();
        });
    }
}
