package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.TradeSettingsCmdService;
import com.sankuai.shangou.seashop.base.thrift.core.TradeSettingsCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.TradeSiteSettingsReq;

/**
 * @author： liweisong
 * @create： 2023/11/22 17:22
 */
@RestController
@RequestMapping("/tradeSettings")
public class TradeSettingsCmdController implements TradeSettingsCmdFeign {

    @Resource
    private TradeSettingsCmdService tradeSettingsCmdService;

    @PostMapping(value = "/addOrUpdateSiteSetting", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addOrUpdateSiteSetting(@RequestBody TradeSiteSettingsReq tradeSiteSettingsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addOrUpdateSiteSetting", tradeSiteSettingsReq, req -> {
            tradeSettingsCmdService.addOrUpdateSiteSetting(req);
            return new BaseResp();
        });
    }
}
