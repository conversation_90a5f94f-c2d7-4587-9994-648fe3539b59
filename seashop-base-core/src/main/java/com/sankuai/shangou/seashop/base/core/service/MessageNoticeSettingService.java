package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.thrift.core.request.BaseMessageNoticeSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMessageNoticeSettingRes;

public interface MessageNoticeSettingService {

    /**
     * 根据主键消息类型 获取消息设置
     * @param type
     * @return
     */
    BaseMessageNoticeSettingRes selectById(int type);

    /**
     * 获取所有配置
     * @return
     */
    List<BaseMessageNoticeSettingRes> getSettings();

    /**
     * 设置消息设置
     * @param settingReq
     * @return
     */
    Boolean setSetting(BaseMessageNoticeSettingReq settingReq);
}
