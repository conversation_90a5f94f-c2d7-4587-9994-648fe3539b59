package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.MobileFootCmdService;
import com.sankuai.shangou.seashop.base.thrift.core.MobileFootCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddOrUpdateFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteFootMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveFootMenusReq;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:47
 */
@RestController
@RequestMapping("/mobileFoot")
public class MobileFootCmdController implements MobileFootCmdFeign {

    @Resource
    private MobileFootCmdService mobileFootCmdService;

    @PostMapping(value = "/addOrUpdateFootMenus", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addOrUpdateFootMenus(@RequestBody AddOrUpdateFootMenusReq addFootMenusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addOrUpdateFootMenus", addFootMenusReq, req -> {
            mobileFootCmdService.addOrUpdateFootMenus(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/saveFootMenus", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveFootMenus(@RequestBody SaveFootMenusReq saveFootMenusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addOrUpdateFootMenus", saveFootMenusReq, req -> {
            mobileFootCmdService.saveFootMenus(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/deleteFootMenu", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> deleteFootMenu(@RequestBody DeleteFootMenuReq deleteFootMenuReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteFootMenu", deleteFootMenuReq, req -> {
            req.checkParameter();
            mobileFootCmdService.deleteFootMenu(req);
            return new BaseResp();
        });
    }
}
