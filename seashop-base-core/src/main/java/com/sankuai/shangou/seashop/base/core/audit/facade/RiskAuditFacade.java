package com.sankuai.shangou.seashop.base.core.audit.facade;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.core.audit.facade.dto.ContentAuditDTO;
import com.sankuai.shangou.seashop.base.core.audit.model.RiskTextAuditResult;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * 风控审核
 */
@Slf4j
@Component
public class RiskAuditFacade {
//    @ThriftClientProxy(remoteAppKey = "com.sankuai.canyinrc.r.virbiuspoi", remoteServerPort = 8487)
//    private RiskVirbiusThriftServer.Iface riskThriftService;
    private static final Integer EVENT_ID = 362;
    private static final Integer BUSINESS_LINE = 9;
    private static final int RISK_SUCCESS_CODE = 0;

    /**
     * 同步审核文本内容
     *
     * @param content
     * @return
     */
    public RiskTextAuditResult syncAuditText(ContentAuditDTO content) {
//        try {
//            RiskControlResult result = riskThriftService.riskControl(EVENT_ID, BUSINESS_LINE, JsonUtil.toJsonString(content));
//            Bssert.throwIfNull(result, "风控审核结果未知");
//            Bssert.throwIfTrue(RISK_SUCCESS_CODE != result.getCode(), "风控审核异常:{}", result.getMsg());
//            return convertTextAuditResult(result);
//        } catch (RiskException e) {
//            log.error("同步审核文本异常,", e);
//            throw new BusinessException("同步审核文本异常");
//        } catch (TException e) {
//            log.error("同步审核文本RPC异常,", e);
//            throw new BusinessException("同步审核文本RPC异常");
//        }
        return RiskTextAuditResult.builder()
            .riskResultCode(0)
            .effectiveRuleId(null)
            .extra(StrUtil.EMPTY)
            .prompt(StrUtil.EMPTY)
            .riskCheckId(StrUtil.EMPTY)
            .build();
    }

//    private RiskTextAuditResult convertTextAuditResult(RiskControlResult result) {
//        return RiskTextAuditResult.builder()
//                .riskResultCode(result.getRiskResultCode())
//                .effectiveRuleId(result.getEffectiveRuleId())
//                .extra(result.getExtra())
//                .prompt(result.getPrompt())
//                .riskCheckId(result.getRiskCheckId())
//                .build();
//    }
}

