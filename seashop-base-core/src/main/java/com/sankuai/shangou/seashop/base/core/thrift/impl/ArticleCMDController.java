package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.base.core.service.ArticleCategoryService;
import com.sankuai.shangou.seashop.base.core.service.ArticleService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticle;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategory;
import com.sankuai.shangou.seashop.base.thrift.core.ArticleCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleCategoryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;

@RestController
@RequestMapping("/article")
public class ArticleCMDController implements ArticleCMDFeign {

    @Resource
    private ArticleService articleService;

    @Resource
    private ArticleCategoryService articleCategoryService;

    @PostMapping(value = "/create", consumes = "application/json")
    @Override
    public ResultDto<Long> create(@RequestBody BaseArticleReq articleReq) throws TException {
        articleReq.checkParameter();
        BaseArticleCategory category = articleCategoryService.getById(articleReq.getCategoryId());

        if (category == null) {
            throw new IllegalArgumentException("所属分类不能为空");
        }
        return ThriftResponseHelper.responseInvoke("articleReq", articleReq, req -> {
            //新增时，默认排序数值为0
            req.setDisplaySequence(0L);

            req.setAddDate(new Date());
            Long articleId = articleService.create(req);
            return articleId;
        });

    }

    @PostMapping(value = "/update", consumes = "application/json")
    @Override
    public ResultDto<Boolean> update(@RequestBody BaseArticleReq articleReq) throws TException {
        articleReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("update", articleReq, req -> {
//            BaseArticleWithBLOBs article = JsonUtil.copy(articleReq, BaseArticleWithBLOBs.class);
            BaseArticle article = articleService.getById(req.getId());
            if (article == null) {
                throw new BusinessException("找不到该文章");
            }
            Boolean articleId = articleService.update(req);
            return articleId;
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @Override
    public ResultDto<Boolean> delete(@RequestBody BaseIdsReq idsReq) throws TException {
        idsReq.checkParameter();

        return ThriftResponseHelper.responseInvoke("update", idsReq, req -> {
            Boolean articleId = articleService.deletes(idsReq);
            return articleId;
        });
    }

    @PostMapping(value = "/createCategory", consumes = "application/json")
    @Override
    public ResultDto<Long> createCategory(@RequestBody BaseArticleCategoryReq categoryReq) throws TException {
        categoryReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("createCategory", categoryReq, req -> {
            req.setIsDefault(false);
            BaseArticleCategory category = JsonUtil.copy(req, BaseArticleCategory.class);
            Long articleId = articleCategoryService.create(category);
            return articleId;
        });
    }

    @PostMapping(value = "/updateCategory", consumes = "application/json")
    @Override
    public ResultDto<Boolean> updateCategory(@RequestBody BaseArticleCategoryReq articleReq) throws TException {
        articleReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("createCategory", articleReq, req -> {
            BaseArticleCategory category = JsonUtil.copy(req, BaseArticleCategory.class);
            Boolean result = articleCategoryService.update(category);
            return result;
        });
    }

    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    @Override
    public ResultDto<Boolean> deleteCategory(@RequestBody BaseIdsReq idsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWithPage", idsReq, req -> {
            idsReq.checkParameter();
            boolean hasSystem = idsReq.hasIntersection(Arrays.stream(CommonConstant.SYSTEM_ARTICLE_Category_IDS).collect(Collectors.toList()));
            if (hasSystem) {
                throw new InvalidParamException("系统分类不能删除");
            }

            long articleCount = articleCategoryService.getArticleCount(idsReq.getIds());
            if (articleCount > 0) {
                throw new InvalidParamException("该分类下存在文章，不能删除");
            }

            Boolean result = articleCategoryService.deletes(req.getIds());
            return result;
        });

    }


}
