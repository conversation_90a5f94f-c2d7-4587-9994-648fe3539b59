package com.sankuai.shangou.seashop.base.core.thrift.impl;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.BaseDemoFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.DemoModelReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.DemoWrapper;


@RestController
@RequestMapping("/base")
public class BaseDemoController implements BaseDemoFeign {

    @PostMapping(value = "/createDemo", consumes = "application/json")
    @Override
    public ResultDto<DemoWrapper> createDemo(@RequestBody DemoModelReq req) throws TException {
        DemoWrapper demo = new DemoWrapper();
        demo.setId(req.getId());
        demo.setName(req.getName());

        return new ResultDto<DemoWrapper>(demo);
    }
}
