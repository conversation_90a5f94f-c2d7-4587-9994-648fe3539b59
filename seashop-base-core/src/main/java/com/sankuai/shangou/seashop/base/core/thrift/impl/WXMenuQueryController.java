package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.WXMenuService;
import com.sankuai.shangou.seashop.base.thrift.core.WXMenuQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuRes;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdCreateQRReq;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/WXMenu")
public class WXMenuQueryController implements WXMenuQueryFeign {
    @Resource
    private WXMenuService wxMenuService;
    @Override
    @GetMapping(value = "getWXMenus")
    public ResultDto<List<BaseWXMenuListRes>> queryWXMenus() throws TException {

        return ThriftResponseHelper.responseInvoke("queryWXMenus",null,rep->{
            return wxMenuService.queryWXMenus();
        });
    }

    @Override
    @PostMapping(value = "queryWXMenuById", consumes = "application/json")
    public ResultDto<BaseWXMenuRes> queryWXMenuById(@RequestBody BaseIdReq idReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWXMenuById",idReq,rep->{
            return wxMenuService.queryById(idReq);
        });
    }

    //    createQrCode
    @Override
    @PostMapping(value = "createQR", consumes = "application/json")
    public ResultDto<String> createQR(@RequestBody CmdCreateQRReq cmdCreateQRReq){
        return ThriftResponseHelper.responseInvoke("createQR",cmdCreateQRReq,(rep)->{
            return wxMenuService.createQR(cmdCreateQRReq);
        });
    }
}
