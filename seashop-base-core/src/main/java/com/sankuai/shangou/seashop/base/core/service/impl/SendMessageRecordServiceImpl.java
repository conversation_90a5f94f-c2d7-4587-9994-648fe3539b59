package com.sankuai.shangou.seashop.base.core.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.enums.messageRecord.MessageContentTypeEnum;
import com.sankuai.shangou.seashop.base.common.enums.messageRecord.MessageTypeEnum;
import com.sankuai.shangou.seashop.base.common.enums.messageRecord.SendMessageStateEnum;
import com.sankuai.shangou.seashop.base.core.log.MessageRecordLogBO;
import com.sankuai.shangou.seashop.base.core.service.SendMessageRecordService;
import com.sankuai.shangou.seashop.base.dao.core.domain.SendMessageRecord;
import com.sankuai.shangou.seashop.base.dao.core.repository.SendMessageRecordCouponRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.SendMessageRecordRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.MessageRecordQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.MessageRecordCmdReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordResp;

@Service
public class SendMessageRecordServiceImpl implements SendMessageRecordService {
    @Resource
    private SendMessageRecordRepository sendMessageRecordRepository;
    @Resource
    private SendMessageRecordCouponRepository sendMessageRecordCouponRepository;
    @Resource
    private BaseLogAssist baseLogAssist;


    @Override
    public BasePageResp<MessageRecordResp> queryPage(MessageRecordQueryReq apiSendEmailMsgReq) {
        Page<SendMessageRecord> recordPage = PageHelper.startPage(apiSendEmailMsgReq.getPageNo(), apiSendEmailMsgReq.getPageSize());
        sendMessageRecordRepository.lambdaQuery()
                .eq(apiSendEmailMsgReq.getMessageType() != null, SendMessageRecord::getMessageType, apiSendEmailMsgReq.getMessageType())
                .eq(apiSendEmailMsgReq.getMessageStatus() != null, SendMessageRecord::getSendState, apiSendEmailMsgReq.getMessageStatus())
                .between(apiSendEmailMsgReq.getSendStartTime() != null && apiSendEmailMsgReq.getSendEndTime() != null, SendMessageRecord::getSendTime, apiSendEmailMsgReq.getSendStartTime(), apiSendEmailMsgReq.getSendEndTime())
                .orderByDesc(SendMessageRecord::getSendTime)
                .list();
        return PageResultHelper.transfer(recordPage, MessageRecordResp.class, (record, recordResp) -> {
            recordResp.setMessageTypeDesc(MessageTypeEnum.getDescByCode(record.getMessageType()));
            recordResp.setContentTypeDesc(MessageContentTypeEnum.getDescByCode(record.getContentType()));
            recordResp.setSendStateDesc(SendMessageStateEnum.getDescByCode(record.getSendState()));
        });
    }

    @Override
    public Long addRecord(MessageRecordCmdReq messageRecordCmdReq) {
        SendMessageRecord sendMessageRecord = JsonUtil.copy(messageRecordCmdReq, SendMessageRecord.class);
        sendMessageRecordRepository.save(sendMessageRecord);
        baseLogAssist.recordLog(ExaminModelEnum.SYSTEM_SETTING, ExaProEnum.INSERT, "发送消息",
            messageRecordCmdReq.getOperationUserId(), messageRecordCmdReq.getOperationShopId(),
            new MessageRecordLogBO(), new MessageRecordLogBO(sendMessageRecord));
        return sendMessageRecord.getId();
    }

    @Override
    public MessageRecordDetailResp queryDetail(BaseIdReq baseIdReq) {
        SendMessageRecord sendMessageRecord = sendMessageRecordRepository.getById(baseIdReq.getId());
        return JsonUtil.copy(sendMessageRecord, MessageRecordDetailResp.class);
    }

    @Override
    public BaseResp deleteRecord(BaseIdReq req) {
//        sendMessageRecordRepository.removeById(req.getId());
        sendMessageRecordRepository.lambdaUpdate()
            .set(SendMessageRecord::getSendState, SendMessageStateEnum.SEND_FAIL.getCode())
            .eq(SendMessageRecord::getId, req.getId()).update();
        return BaseResp.of();
    }
}
