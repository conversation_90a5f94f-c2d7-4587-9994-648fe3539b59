package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.core.service.BanksService;
import com.sankuai.shangou.seashop.base.dao.core.domain.Banks;
import com.sankuai.shangou.seashop.base.dao.core.repository.BanksRepository;

@Service
public class BanksServiceImpl implements BanksService {
    @Resource
    private BanksRepository banksRepository;

    @Override
    public List<Banks> queryList() {
        return banksRepository.list();
    }
}
