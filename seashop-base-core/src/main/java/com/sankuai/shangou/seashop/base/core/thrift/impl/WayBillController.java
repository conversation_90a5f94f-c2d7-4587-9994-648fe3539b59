package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.WayBillService;
import com.sankuai.shangou.seashop.base.thrift.core.WayBillFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/wayBill")
public class WayBillController implements WayBillFeign {

    @Resource
    private WayBillService wayBillService;

    @PostMapping(value = "/setRegisterState")
    @Override
    public ResultDto<Boolean> setRegisterState() throws TException {
        return ThriftResponseHelper.responseInvoke("setRegisterState", null, r -> wayBillService.setRegisterState());
    }

    @GetMapping(value = "/goExpressBills")
    @Override
    public ResultDto<String> goExpressBills() throws TException {
        return ThriftResponseHelper.responseInvoke("goExpressBills", null, r -> wayBillService.goExpressBills());
    }

}
