package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.thrift.core.request.MessageRecordQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.MessageRecordCmdReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordResp;

public interface SendMessageRecordService {

    BasePageResp<MessageRecordResp> queryPage(MessageRecordQueryReq apiSendEmailMsgReq);

    Long addRecord(MessageRecordCmdReq messageRecordCmdReq);

    MessageRecordDetailResp queryDetail(BaseIdReq baseIdReq);

    BaseResp deleteRecord(BaseIdReq req);
}
