package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticle;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryTopArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;

public interface ArticleService {


    Long create(BaseArticleReq article);

    Boolean update(BaseArticleReq article);

    Boolean deletes(BaseIdsReq ids);


    Page<BaseArticle> query(BaseArticleQueryReq queryReq);

    List<BaseArticle> queryByCategoryId(QueryTopArticleReq query);

    List<BaseArticle> queryInCategoryId(List<Long> cids);

    BaseArticle getById(long id);
}
