package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.CustomFormService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomForm;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseCustomFormRepository;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormFieldRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormRes;

/**
 * <AUTHOR>
 * @date 2023/12/09 9:53
 */
@Service
public class CustomFormServiceImpl implements CustomFormService {

    @Resource
    private BaseCustomFormRepository baseCustomFormRepository;


    @Override
    public List<BaseCustomFormRes> queryCustomFormByFormIds(List<Long> formIds) {
        List<BaseCustomForm> formList = baseCustomFormRepository.queryCustomFormByFormIds(formIds);
        if (CollectionUtils.isEmpty(formList)) {
            return Collections.EMPTY_LIST;
        }

        List<BaseCustomFormRes> formResList = JsonUtil.copyList(formList, BaseCustomFormRes.class);
        List<BaseCustomFormField> fields = baseCustomFormRepository.queryCustomFieldByFormIds(formIds);
        Map<Long, List<BaseCustomFormField>> fieldMap = fields.stream().collect(Collectors.groupingBy(BaseCustomFormField::getFormId));
        formResList.forEach(formRes -> {
            List<BaseCustomFormField> curFields = fieldMap.get(formRes.getId());
            if (curFields == null) {
                return;
            }

            formRes.setFormFields(JsonUtil.copyList(curFields, BaseCustomFormFieldRes.class));
        });
        return formResList;
    }

    @Override
    public List<BaseCustomFormField> queryCustomFieldByFieldIds(List<Long> fieldIds) {
        return baseCustomFormRepository.queryCustomFieldByFieldIds(fieldIds);
    }
}
