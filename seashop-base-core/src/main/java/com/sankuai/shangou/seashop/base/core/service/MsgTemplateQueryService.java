package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.thrift.core.request.QueryAppletTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryMsgTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryWxAppletFormDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryWxAppletFormDataResp;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:18
 */
public interface MsgTemplateQueryService {

    // 查询消息模版
    QueryMsgTemplateResp queryMsgTemplate(QueryMsgTemplateReq queryMsgTemplateReq);

    List<BaseMsgTemplateResp> queryAppletTemplate(QueryAppletTemplateReq queryAppletTemplateReq);

    List<QueryWxAppletFormDataResp> queryWxAppletFormData(QueryWxAppletFormDataReq queryWxAppletFormDataReq);
}
