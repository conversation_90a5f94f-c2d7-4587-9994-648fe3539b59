package com.sankuai.shangou.seashop.base.core.config;

import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 小程序logo修改工具类
 * @author: LXH
 **/
@Slf4j
public class LogoHelper {

    private static final Logger logger = LoggerFactory.getLogger(LogoHelper.class);

    /**
     * 替换
     *
     * @param in
     * @return
     * @throws Exception
     */
    public static byte[] changLogo(byte[] in, byte[] logo) {
        BufferedImage bdground = null;
        BufferedImage logoimage = null;
        try {
            bdground = ImageIO.read(new ByteArrayInputStream(in));
            logoimage = ImageIO.read(new ByteArrayInputStream(logo));
            //切圆形图片
            logoimage = roundImage(logoimage, 172, 172);
            Graphics2D g = bdground.createGraphics();
            g.drawImage(logoimage, 115, 117, 200, 195, null);
            g.dispose();
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            ImageIO.write(bdground, "jpg", result);
            result.flush();
            return result.toByteArray();
        }
        catch (Exception e) {
            logger.error("重画二维码失败:", e);
        }
        finally {
            if (bdground != null) {
                bdground.getGraphics().dispose();
            }
            if (logoimage != null) {
                logoimage.getGraphics().dispose();
            }
        }
        return in;
    }

    public static byte[] changLogo(byte[] in, String logoStr) {
        byte[] logo = downloadBytes(logoStr);
        if (logo == null) {
            return in;
        }
        return changLogo(in, logo);
    }

    /**
     * 修改图片尺寸
     *
     * @param inputImage 目标图片
     */
    public static BufferedImage changePicSize(BufferedImage inputImage, int newWidth, int newHeight) {
        // 为等比缩放计算输出的图片宽度及高度 保持比例 172*172
        BufferedImage outputImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        // 调整图像大小
        Graphics2D graphics2D = outputImage.createGraphics();
        // 采用双线性插值法调整图像的大小
        graphics2D.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        graphics2D.drawImage(inputImage, 0, 0, newWidth, newHeight, null);
        graphics2D.dispose();
        // 保存缩放后的图像
        return outputImage;
    }

    /***
     * 修改图片尺寸
     * @param image 源图片
     * @param targetSize 文件的边长，单位：像素，最终得到的是一张正方形的图，所以要求targetSize<=源文件的最小边长
     * @param cornerRadius 圆角半径，单位：像素。如果=targetSize那么得到的是圆形图
     * @return 文件的保存路径
     * @throws IOException
     */
    private static BufferedImage roundImage(BufferedImage image, int targetSize, int cornerRadius) {
        image = changePicSize(image, targetSize, targetSize);
        BufferedImage outputImage = new BufferedImage(targetSize, targetSize, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = outputImage.createGraphics();
        g2.setComposite(AlphaComposite.Src);//透明度设置开始
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);//抗锯齿
        g2.setColor(Color.WHITE);//画笔颜色
        g2.fill(new RoundRectangle2D.Float(0, 0, targetSize, targetSize, cornerRadius, cornerRadius));//画圆角矩形
        g2.setComposite(AlphaComposite.SrcAtop);//透明度设置结束
        try {
            g2.drawImage(image, 0, 0, null);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            g2.dispose();
        }
        return outputImage;
    }

    public static boolean exist(String httpFileUrl) {
        try {
            HttpUtil.downloadBytes(httpFileUrl);
            return true;
        }
        catch (Exception e) {
            log.error("图片不存在", e);
            return false;
        }
    }


    public static byte[] downloadBytes(String httpFileUrl) {
        try {
            return HttpUtil.downloadBytes(httpFileUrl);
        }
        catch (Exception e) {
            log.error("图片不存在", e);
            return null;
        }
    }

    public static String getFileName(String path, String logo) {
        String replacedString = path.replaceAll("[^\\w\\s]", "");
        if (StrUtil.isNotBlank(logo)) {
            return replacedString + StrUtil.C_UNDERLINE + logo;
        }
        else {
            return replacedString + ".png";
        }
    }
}