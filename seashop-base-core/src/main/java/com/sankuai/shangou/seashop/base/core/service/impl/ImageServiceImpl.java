package com.sankuai.shangou.seashop.base.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.base.core.service.ImageService;
import com.sankuai.shangou.seashop.base.core.service.model.BasePhotoSpaceBo;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceExample;
import com.sankuai.shangou.seashop.base.dao.core.repository.BasePhotoSpaceRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.BasePhotoSpaceReq;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ImageServiceImpl implements ImageService {

    @Resource
    private BasePhotoSpaceRepository photoSpaceRepository;


    @Override
    @Transactional
//    @ExaminProcess(processModel = ExaminModelEnum.SHOP, processType = ExaProEnum.INSERT,operationUserId = "operationUserId",shopId = "shopId", serviceMethod = "photoSpaceRepository", dto = BasePhotoSpaceReq.class, entity = BasePhotoSpace.class)
    public Long create(BasePhotoSpaceReq photo) {

        BasePhotoSpace daoModel = JsonUtil.copy(photo, BasePhotoSpace.class);
        if (daoModel.getPhotoCategoryId() < CommonConstant.PLATFORM_ID) {
            daoModel.setPhotoCategoryId(0L);
        }
        photoSpaceRepository.create(daoModel);
        photo.setId(daoModel.getId());

        return daoModel.getId();
    }

    @Override
    @Transactional
    public void batchCreate(List<BasePhotoSpaceReq> photos) {
        for (BasePhotoSpaceReq photoSpaceReq : photos) {
            create(photoSpaceReq);
        }
    }


    @Override
    public Boolean delete(BasePhotoSpaceReq photo) {
        photoSpaceRepository.delete(photo.getId(), photo.getShopId());

        return true;
    }

    @Override
    public Page<BasePhotoSpace> queryWithPage(BasePhotoSpaceBo query) {
        BasePageParam page = new BasePageParam();
        page.setPageNum(query.getPageNo());
        page.setPageSize(query.getPageSize());
        Page<BasePhotoSpace> pageResult = PageHelper.startPage(page);

        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        if (query.getShopId() != null) {
            criteria.andShopIdEqualTo(query.getShopId());
        }

        if (query.getPhotoCategoryId() != null) {
            criteria.andPhotoCategoryIdEqualTo(query.getPhotoCategoryId());
        }
        if (!StringUtils.isEmpty(query.getPhotoName())) {
            criteria.andPhotoNameLike("%" + query.getPhotoName() + "%");
        }
        example.setOrderByClause("id desc");
        photoSpaceRepository.query(example);
        return pageResult;
    }

    @Override
    public Page<BasePhotoSpace> queryAuditWithPage(BasePhotoSpaceBo query) {
        BasePageParam page = new BasePageParam();
        page.setPageNum(query.getPageNo());
        page.setPageSize(query.getPageSize());
        Page<BasePhotoSpace> pageResult = PageHelper.startPage(page);

        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        if (query.getShopId() != null) {
            criteria.andShopIdEqualTo(query.getShopId());
        }

        if (query.getPhotoCategoryId() != null) {
            criteria.andPhotoCategoryIdEqualTo(query.getPhotoCategoryId());
        }
        if (!StringUtils.isEmpty(query.getPhotoName())) {
            criteria.andPhotoNameLike("%" + query.getPhotoName() + "%");
        }
        example.setOrderByClause("id desc");
        photoSpaceRepository.query(example);
        return pageResult;
    }

    @Override
    public Boolean move(List<Long> ids, long catId, long shopId) {
        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        criteria.andShopIdEqualTo(shopId);

        BasePhotoSpace photoSpace = new BasePhotoSpace();
        photoSpace.setPhotoCategoryId(catId);
        photoSpaceRepository.update(photoSpace, example);
        return true;
    }

    @Override
    public Boolean moveImages(long catId, long targetCatId, long shopId) {
        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        criteria.andPhotoCategoryIdEqualTo(catId);

        BasePhotoSpace photoSpace = new BasePhotoSpace();
        photoSpace.setPhotoCategoryId(targetCatId);
        photoSpaceRepository.update(photoSpace, example);
        return true;
    }

    @Override
    public Boolean deletes(List<Long> ids, long shopId) {
        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        criteria.andShopIdEqualTo(shopId);
        photoSpaceRepository.deletes(example);
        return true;
    }

    @Override
    public int getCountByCategoryId(Long id, long shopId) {
        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        criteria.andPhotoCategoryIdEqualTo(id);
        criteria.andShopIdEqualTo(shopId);
        int result = photoSpaceRepository.getCountByExample(example);
        return result;
    }

    @Override
    public Boolean update(BasePhotoSpace photoSpace) {
        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        criteria.andShopIdEqualTo(photoSpace.getShopId());
        criteria.andIdEqualTo(photoSpace.getId());
        photoSpaceRepository.update(photoSpace, example);
        return true;
    }
}
