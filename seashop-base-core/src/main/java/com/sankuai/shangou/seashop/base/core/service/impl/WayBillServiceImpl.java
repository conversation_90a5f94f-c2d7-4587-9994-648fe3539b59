package com.sankuai.shangou.seashop.base.core.service.impl;

import com.hishop.starter.util.HishopErpSignUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.core.service.WayBillService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.SortedMap;
import java.util.TreeMap;

@Slf4j
@Service
public class WayBillServiceImpl implements WayBillService {
    //电子面单相关配置begin
    @Value("${himall.waybill.appkey:}")
    private String appKey;
    @Value("${himall.waybill.appsecret:}")
    private String appSecret;
    @Value("${himall.current.domain.url:}")
    private String currentDomainUrl;
    @Value("${himall.erp.url:}")
    private String erpUri;
    @Value("${himall.erp.domain:}")
    private String erpDomain;

    @Override
    public Boolean setRegisterState() {
        if (StringUtils.isBlank(appKey)) {
            throw new BusinessException("未初始开放平台");
        }
        String postData = String.format("appKey=%s&appSecret=%s&routeAddress=%s", appKey, appSecret, currentDomainUrl);
        String rData = this.registeWayBill(erpUri, postData);
        log.info("电子面单注册返回：{}", rData);
        boolean isReged = false;
        if (StringUtils.isNotBlank(rData)) {
            if (rData.replace(" ", "").toLowerCase().contains("\"success\":true")) {
                isReged = true;
            }
        }
        if (!isReged) {
            log.warn("远程注册异常!,原因：{}", rData);
        }
        return isReged;
    }

    @Override
    public String goExpressBills() {
        String goUrl = erpDomain + "/ExpressBill/Allot?app_key=%s&timestamp=%s&sign=%s";
        if (StringUtils.isBlank(appKey)) {
            throw new BusinessException("未开启开放平台");
        }
        SortedMap<String, String> data = new TreeMap<>();
        data.put("app_key", appKey);
        String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        data.put("timestamp", timestamp);
        String sign = HishopErpSignUtil.getSign(data, appSecret);
        goUrl = String.format(goUrl, appKey, timestamp, sign);
        return goUrl;
    }

    private String registeWayBill(String uri, String postData) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(postData, headers);
//        return restTemplate.postForObject(uri, entity, String.class);
        return "\"success\":true";
    }
}
