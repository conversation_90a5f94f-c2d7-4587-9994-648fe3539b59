package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.MessageNoticeSettingService;
import com.sankuai.shangou.seashop.base.thrift.core.MessageNoticeSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMessageNoticeSettingRes;

@RestController
@RequestMapping("/messageNoticeSetting")
public class MessageNoticeSettingQueryController implements MessageNoticeSettingQueryFeign {

    @Resource
    private MessageNoticeSettingService messageNoticeSettingService;


    @GetMapping(value = "/getMessageNoticeSetting")
    @Override
    public ResultDto<List<BaseMessageNoticeSettingRes>> getMessageNoticeSetting() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("getMessageNoticeSetting", i, req -> {
            List<BaseMessageNoticeSettingRes> result = messageNoticeSettingService.getSettings();
            return result;
        });
    }

    @GetMapping(value = "/getMessageNoticeSettingByType")
    @Override
    public ResultDto<BaseMessageNoticeSettingRes> getMessageNoticeSettingByType(@RequestParam Integer type) throws TException {
        return ThriftResponseHelper.responseInvoke("getMessageNoticeSettingByType", type, req -> {
            BaseMessageNoticeSettingRes result = messageNoticeSettingService.selectById(type);
            return result;
        });
    }
}
