package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.MsgTemplateQueryService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseWeixinMsgTemplate;
import com.sankuai.shangou.seashop.base.dao.core.domain.WxAppletFormData;
import com.sankuai.shangou.seashop.base.dao.core.repository.MsgTemplateRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.WxAppletFormDataRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryAppletTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryMsgTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryWxAppletFormDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryWxAppletFormDataResp;


/**
 * @author： liweisong
 * @create： 2023/11/29 17:20
 */
@Service
public class MsgTemplateQueryServiceImpl implements MsgTemplateQueryService {

    @Resource
    private MsgTemplateRepository msgTemplateRepository;

    @Resource
    private WxAppletFormDataRepository wxAppletFormDataRepository;

    @Resource
    private SiteSettingService siteSettingService;

    @Override
    public QueryMsgTemplateResp queryMsgTemplate(QueryMsgTemplateReq queryMsgTemplateReq) {
        QueryMsgTemplateResp result = new QueryMsgTemplateResp();
        BaseWeixinMsgTemplate baseWeixinMsgTemplate = JsonUtil.copy(queryMsgTemplateReq,BaseWeixinMsgTemplate.class);
        List<BaseWeixinMsgTemplate> list = msgTemplateRepository.selectList(baseWeixinMsgTemplate);
        if(CollectionUtils.isEmpty(list)){
            return result;
        }
        //根据keys 获取属性值
        List<String> settingKeys = new ArrayList<>();
        settingKeys.add(queryMsgTemplateReq.getIdKey());
        settingKeys.add(queryMsgTemplateReq.getSecretKey());
        List<BaseSiteSetting> listSite = siteSettingService.query(settingKeys);
        for(BaseSiteSetting baseSiteSetting : listSite){
            if("weixinAppletId".equals(baseSiteSetting.getKey())){
                result.setWeixinAppletId(baseSiteSetting.getValue());
            }
            if("weixinAppletSecret".equals(baseSiteSetting.getKey())){
                result.setWeixinAppletSecret(baseSiteSetting.getValue());
            }
        }
        List<BaseMsgTemplateResp> items = new ArrayList<>();
        list.forEach(v -> {
            BaseMsgTemplateResp resp = new BaseMsgTemplateResp();
            resp.setId(v.getId());
            resp.setTitle(v.getTitle());
            resp.setTemplateNum(v.getTemplateNum());
            resp.setDescription(v.getDescription());
            resp.setTemplateId(v.getTemplateId());
            resp.setMessageType(v.getMessageType());
            items.add(resp);
        });
        result.setItems(items);
        return result;
    }

    @Override
    public List<BaseMsgTemplateResp> queryAppletTemplate(QueryAppletTemplateReq queryAppletTemplateReq) {
        List<BaseWeixinMsgTemplate> list = msgTemplateRepository.selectList(JsonUtil.copy(queryAppletTemplateReq, BaseWeixinMsgTemplate.class));
        return JsonUtil.copyList(list, BaseMsgTemplateResp.class);
    }

    @Override
    public List<QueryWxAppletFormDataResp> queryWxAppletFormData(QueryWxAppletFormDataReq queryWxAppletFormDataReq) {
        WxAppletFormData req = JsonUtil.copy(queryWxAppletFormDataReq, WxAppletFormData.class);
        List<WxAppletFormData> list = wxAppletFormDataRepository.selectList(req);
        return JsonUtil.copyList(list, QueryWxAppletFormDataResp.class);
    }
}
