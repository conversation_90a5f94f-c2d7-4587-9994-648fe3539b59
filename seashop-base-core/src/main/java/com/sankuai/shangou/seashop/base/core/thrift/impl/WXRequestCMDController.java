package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration;
import com.sankuai.shangou.seashop.base.thrift.core.WXRequestCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.JsApiSignatureReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveWXAccountReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JsApiSignatureRes;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/WXRequest")
public class WXRequestCMDController implements WXRequestCMDFeign {

    @Resource
    private WxMpService wxMpService;

    @PostMapping(value = "/getAccessToken", consumes = "application/json")
    @Override
    public ResultDto<String> getAccessToken() throws TException {
        return ThriftResponseHelper.responseInvoke("saveWXAccount", null, req -> {
            String token = null;
            try {
                token = wxMpService.getAccessToken();
            } catch (WxErrorException e) {
                throw new RuntimeException(e);
            }
            return token;
        });
    }

    @PostMapping(value = "/getJsApiSignature", consumes = "application/json")
    public ResultDto<JsApiSignatureRes> getJsApiSignature(@RequestBody JsApiSignatureReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getJsApiSignature", request, req -> {
            String url = request.getUrl();
            String jsapiTicket = null;
            try {
                jsapiTicket = wxMpService.getJsapiTicket(true);
            } catch (WxErrorException e) {
                throw new RuntimeException(e);
            }
            // 计算签名
            WxJsapiSignature signatureResult = null;
            try {
                signatureResult = wxMpService.createJsapiSignature(url);
            } catch (WxErrorException e) {
                throw new RuntimeException(e);
            }

            // 准备返回的数据
            JsApiSignatureRes result = new JsApiSignatureRes();
            result.setAppId(wxMpService.getWxMpConfigStorage().getAppId());
            result.setTimestamp(String.valueOf(signatureResult.getTimestamp()));
            result.setNonceStr(signatureResult.getNonceStr());
            result.setSignature(signatureResult.getSignature());
            return result;
        });
    }
}
