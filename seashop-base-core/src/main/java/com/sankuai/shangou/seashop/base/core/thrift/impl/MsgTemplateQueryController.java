package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.MsgTemplateCmdService;
import com.sankuai.shangou.seashop.base.core.service.MsgTemplateQueryService;
import com.sankuai.shangou.seashop.base.thrift.core.MsgTemplateQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryAppletTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryMsgTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryWxAppletFormDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryWxAppletFormDataResp;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:07
 */
@RestController
@RequestMapping("/msgTemplate")
public class MsgTemplateQueryController implements MsgTemplateQueryFeign {

    @Resource
    private MsgTemplateQueryService msgTemplateQueryService;

    @Resource
    private MsgTemplateCmdService msgTemplateCmdService;

    @PostMapping(value = "/queryMsgTemplate", consumes = "application/json")
    @Override
    public ResultDto<QueryMsgTemplateResp> queryMsgTemplate(@RequestBody QueryMsgTemplateReq queryMsgTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryMsgTemplate", queryMsgTemplateReq, req ->
                msgTemplateQueryService.queryMsgTemplate(req));
    }

    @PostMapping(value = "/queryAppletTemplate", consumes = "application/json")
    @Override
    public ResultDto<List<BaseMsgTemplateResp>> queryAppletTemplate(@RequestBody QueryAppletTemplateReq queryAppletTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryMsgTemplate", queryAppletTemplateReq, req ->
                msgTemplateQueryService.queryAppletTemplate(req));
    }

    @PostMapping(value = "/queryWxAppletFormData", consumes = "application/json")
    @Override
    public ResultDto<List<QueryWxAppletFormDataResp>> queryWxAppletFormData(@RequestBody QueryWxAppletFormDataReq queryWxAppletFormDataReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryMsgTemplate", queryWxAppletFormDataReq, req ->
                msgTemplateQueryService.queryWxAppletFormData(req));
    }

    @GetMapping(value = "/getAppletResetToken")
    @Override
    public ResultDto<String> getAppletResetToken() throws TException {
        return ThriftResponseHelper.responseInvoke("getAppletResetToken", null, req ->
            msgTemplateCmdService.getAppletResetToken());
    }
}
