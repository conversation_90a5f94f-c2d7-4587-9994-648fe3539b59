package com.sankuai.shangou.seashop.base.core.service.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class WechatMenuBo {

    /*
    菜单的响应动作类型，view表示网页类型，click表示点击类型，miniprogram表示小程序类型
     */
    private String type;

    /*
    菜单标题，不超过16个字节，子菜单不超过60个字节
     */
    private String name;

    /*
    网页 链接，用户点击菜单可打开链接，不超过1024字节。 type为miniprogram时，不支持小程序的老版本客户端将打开本url
     */
    private String url;

    /*
   小程序AppId
    */
    private String appid;
    /*
    小程序链接
     */
    private String pagepath;
    private List<WechatMenuBo> sub_button;
}
