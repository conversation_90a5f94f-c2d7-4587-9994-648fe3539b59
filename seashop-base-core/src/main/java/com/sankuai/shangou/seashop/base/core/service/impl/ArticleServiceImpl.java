package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.ArticleService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticle;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleWithBLOBs;
import com.sankuai.shangou.seashop.base.dao.core.repository.ArticleRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryTopArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;

@Service
public class ArticleServiceImpl implements ArticleService {

    @Resource
    private ArticleRepository articleRepository;

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "新增文章",
        processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.INSERT,
        repository = "articleRepository", serviceMethod = "create",
        dto = BaseArticleReq.class, entity = BaseArticleWithBLOBs.class)
    public Long create(BaseArticleReq req) {
        BaseArticleWithBLOBs article = JsonUtil.copy(req, BaseArticleWithBLOBs.class);
        long articleId = articleRepository.create(article);
        return articleId;
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "修改文章",
        processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
        repository = "articleRepository", serviceMethod = "update",
        dto = BaseArticleReq.class, entity = BaseArticleWithBLOBs.class)
    public Boolean update(BaseArticleReq article) {
        BaseArticleWithBLOBs dao = JsonUtil.copy(article, BaseArticleWithBLOBs.class);
        boolean result = articleRepository.update(dao);
        return result;
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "删除文章",
        processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MOVE,
        repository = "articleRepository", serviceMethod = "deletes",
        dto = BaseIdsReq.class, entity = BaseArticleWithBLOBs.class)
    public Boolean deletes(BaseIdsReq ids) {
        boolean result = articleRepository.deletes(ids.getIds());
        return result;
    }

    @Override
    public Page<BaseArticle> query(BaseArticleQueryReq queryReq) {
        BasePageParam page = new BasePageParam();
        page.setPageNum(queryReq.getPageNo());
        page.setPageSize(queryReq.getPageSize());
        Page<BaseArticle> pageResult = PageHelper.startPage(page);

        BaseArticleExample example = new BaseArticleExample();
        BaseArticleExample.Criteria criteria = example.createCriteria();
        if (queryReq.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(queryReq.getCategoryId());
        }
        if (!StringUtils.isEmpty(queryReq.getTitle())) {
            criteria.andTitleLike("%" + queryReq.getTitle() + "%");
        }
        if (queryReq.getIsRelease() != null) {
            criteria.andIsReleaseEqualTo(queryReq.getIsRelease());
        }
        if (queryReq.getCategoryIds() != null && queryReq.getCategoryIds().stream().count() > 0) {
            criteria.andCategoryIdIn(queryReq.getCategoryIds());
        }
        if (queryReq.getSortList() != null && queryReq.getSortList().stream().count() > 0) {

            StringBuilder sb = new StringBuilder();
            for (FieldSortReq sortReq : queryReq.getSortList()) {
                if (sortReq.getSort().equals("add_date") || sortReq.getSort().equals("display_sequence")) {
                    String sortStr = sortReq.getIzAsc().equals(true) ? "asc" : "desc";
                    sb.append(sortReq.getSort() + " " + sortStr + ",");
                } else {
                    throw new IllegalArgumentException("请输入正确的参数");
                }

            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }

            example.setOrderByClause(sb.toString());
        } else {
            example.setOrderByClause("display_sequence desc,id desc");
        }

        articleRepository.query(example);
        return pageResult;
    }

    @Override
    public List<BaseArticle> queryByCategoryId(QueryTopArticleReq query) {
        BasePageParam page = new BasePageParam();
        page.setPageNum(1);
        page.setPageSize(query.getTopNum());
        Page<BaseArticle> pageResult = PageHelper.startPage(page);
        BaseArticleExample example = new BaseArticleExample();
        BaseArticleExample.Criteria criteria = example.createCriteria();
        criteria.andCategoryIdEqualTo(query.getArticleCategoryId());
        articleRepository.query(example);
        return pageResult.getResult();
    }

    @Override
    public List<BaseArticle> queryInCategoryId(List<Long> cids) {

        BaseArticleExample example = new BaseArticleExample();
        BaseArticleExample.Criteria criteria = example.createCriteria();
        criteria.andCategoryIdIn(cids);
        List<BaseArticle> result = articleRepository.query(example);
        return result;
    }

    @Override
    public BaseArticle getById(long id) {
        BaseArticle article = articleRepository.selectById(id);
        return article;
    }
}
