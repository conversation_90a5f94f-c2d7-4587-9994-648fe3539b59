package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.BanksService;
import com.sankuai.shangou.seashop.base.thrift.core.BanksQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.BanksListResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.BanksResp;

@RestController
@RequestMapping("/bank")
public class BanksQueryController implements BanksQueryFeign {

    @Resource
    private BanksService banksService;


    @GetMapping(value = "/queryList")
    @Override
    public ResultDto<BanksListResp> queryList() throws TException {
        return ThriftResponseHelper.responseInvoke("queryList", null,req -> {
            BanksListResp resp = new BanksListResp();
            resp.setBanksList(JsonUtil.copyList(banksService.queryList(), BanksResp.class));
            return resp;
        });
    }
}
