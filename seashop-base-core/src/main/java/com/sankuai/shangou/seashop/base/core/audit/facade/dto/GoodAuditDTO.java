package com.sankuai.shangou.seashop.base.core.audit.facade.dto;

import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class GoodAuditDTO {

    /**
     * vendorId 对应的平台体系 3 闪购
     */
    private Integer vendorSource;
    /**
     * 商品品类体系 40 闪购
     */
    private Integer categorySource;

    /**
     * shopId 对应的平台体系 3 外卖
     */
    private Integer shopSource;

    /**
     * 商品送审类型对应的风控类型
     */
    private Integer type;

    /**
     * 商品信息产生时间
     */
    private String datetime;

    /**
     * 每条送审内容事件的唯一标识，全局唯一
     */
    private String transId;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 业务方透传字段,用于关联业务内容。
     */
    private String bizData;

    /**
     * 供应商Id。
     */
    private Long vendorId;

    /**
     * 商品详情前台URL地址
     */
    private String goodsUrl;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品状态
     * 1、可售,还未上架，但是可进行上架；
     * 2、不可售,还未上架，但是不可进行上架；
     * 3、商品已上架；
     * 4、商品已下架
     */
    private Integer goodsStatus;

    /**
     * 商品品类code,平台分类编码
     */
    private String categoryCode;

    /**
     * 商品品类名称,平台分类名称（一级-二级-三级）
     */
    private String categoryName;

    /**
     * 商品所属品牌
     */
    private String brand;

    /**
     * 商品主图，格式[{ "name":"name1","coverName": "pic1","coverUrl": "url1"},
     * { "name":"name2","coverName": "pic2","coverUrl": "url2" }]
     */
    private List<Map<String, Object>> majorPic;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 商户资质
     * 店铺资质清单
     */
    private List<Map<String, Object>> shopQualification;

    /**
     * 商品其他图片
     */
    private List<Map<String, Object>> otherPic;

    /**
     * 商品小视频.主图视频
     */
    private List<Map<String, Object>> goodsVideo;

    /**
     * 商品参数,商品属性
     */
    private List<Map<String, Object>> goodsParam;

    /**
     * 商品详情,商品描述（图文）,多张图片需要按照顺序传
     */
    private List<Map<String, Object>> goodsDetail;

    /**
     * 供应商资质
     */
    private List<Map<String, Object>> vendorQualification;

    /**
     * 商品自定义标签
     */
    private List<Map<String, Object>> tagList;

    /**
     * 一个商品可以有多个SKU
     */
    private List<Map<String, Object>> goodsSku;

    /**
     * 商品营销信息,广告词
     */
    private List<Map<String, Object>> mktMessage;
}
