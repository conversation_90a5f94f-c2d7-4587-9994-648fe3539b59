package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.thrift.core.request.SmsBodyReq;

import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname MessageCMDThriftService
 * Description //TODO
 * @date 2024/8/9 11:42
 */
public interface MessageCMDThriftService {
    /**
     * 发送短信
     *
     * @param smsBodyReq
     * @return
     */
    boolean sendSms(SmsBodyReq smsBodyReq);

    /**
     * 发送短信
     *
     * @param code
     * @param param
     * @param mobile
     * @return
     */
    boolean sendSms(Long code, String param, String mobile);

    /**
     * 发送短信验证码
     *
     * @param mobile
     * @return
     */
    boolean sendSmsCode(Long code, String mobile);

    /**
     * 发送邮件
     * @param subject
     * @param body
     * @param email
     */
    void sendEmail(String subject, String body, List<String> email);

    /**
     * 发送邮件验证码
     * @param eMail
     */
    void sendEmailCode(String eMail);
}
