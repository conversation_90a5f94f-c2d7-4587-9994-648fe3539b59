package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.core.service.MobileFootQueryService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseMobileFootMenu;
import com.sankuai.shangou.seashop.base.dao.core.repository.MobileFootRepository;
import com.sankuai.shangou.seashop.base.thrift.core.dto.QueryFootMenusRespDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryFootMenusResp;

/**
 * @author： liweisong
 * @create： 2023/11/29 13:45
 */
@Service
public class MobileFootQueryServiceImpl implements MobileFootQueryService {

    @Resource
    private MobileFootRepository mobileFootRepository;

    @Override
    public QueryFootMenusResp queryFootMenus(QueryFootMenusReq queryFootMenusReq) {
        QueryFootMenusResp result = new QueryFootMenusResp();
        BaseMobileFootMenu param = new BaseMobileFootMenu();
        param.setId(queryFootMenusReq.getId());
        param.setType(queryFootMenusReq.getType());
        param.setShopId(queryFootMenusReq.getShopId());
        List<BaseMobileFootMenu> list =  mobileFootRepository.selectList(param);
        if(CollectionUtils.isEmpty(list)){
            return result;
        }
        List<QueryFootMenusRespDto> menusRespDtoList = new ArrayList<>();
        list.forEach(v -> {
            QueryFootMenusRespDto dto = new QueryFootMenusRespDto();
            dto.setId(v.getId());
            dto.setName(v.getName());
            dto.setUrl(v.getUrl());
            dto.setUrlName(v.getUrlName());
            dto.setMenuIcon(v.getMenuIcon());
            dto.setMenuIconSel(v.getMenuIconSel());
            dto.setType(v.getType());
            dto.setShopId(v.getShopId());
            menusRespDtoList.add(dto);
        });
        result.setMenusRespDtoList(menusRespDtoList);
        return result;
    }
}
