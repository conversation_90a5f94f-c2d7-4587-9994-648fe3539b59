package com.sankuai.shangou.seashop.base.core.audit.listener.dto;

import lombok.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class GoodAuditResultDTO implements Serializable {


    /**
     * 服务状态码,服务正常返回为 0,服务异常为 500，一般不会出现
     */
    private Integer code;

    /**
     * 对于服务异常的描述信息，业务方一般不用关注
     */
    private String msg;

    /**
     * 审核结果
     */
    private GoodAuditResult result;

    @Getter
    @Setter
    public static class GoodAuditResult {

        /**
         * "业务方“ID
         */
        private String type;

        /**
         * 业务方透传字段
         */
        private String bizData;

        /**
         * 商品ID
         */
        private Long goodsId;

        /**
         * 每条送审内容事件的唯一标识，全局唯一，业务方负责维护
         */
        private String transId;

        /**
         * 说明输出的结果是首次审核输出，还是商品审核系统经过回扫或修正后，第二次或第N次修正审核的结果
         * auditFreq = 1，业务送审结果。auditFreq = 2，审核修正结果。
         */
        private Integer auditFreq;

        /**
         * 商品一级标签code
         */
        private Integer statusCode;

        /**
         * 商品一级标签名称
         */
        private String verifyDesc;

        /**
         * 商品二级标签code
         */
        private Integer subStatusCode;

        /**
         * 商品二级标签名称
         */
        private String subVerifyDesc;

        /**
         * 商品处置建议
         */
        private Integer advice;

        /**
         * 展示商品具体商品命中的标签
         */
        private List<Map<String, Object>> commodityDetails;

        /**
         * 展示商品具体内容命中的标签
         */
        private List<Map<String, Object>> labelsDetails;
    }
}
