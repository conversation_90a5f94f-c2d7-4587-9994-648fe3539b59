package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.MessageNoticeSettingService;
import com.sankuai.shangou.seashop.base.thrift.core.MessageNoticeSettingCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseMessageNoticeSettingReq;

@RestController
@RequestMapping("/messageNoticeSetting")
public class MessageNoticeSettingCMDController implements MessageNoticeSettingCMDFeign {

    @Resource
    private MessageNoticeSettingService messageNoticeSettingService;

    @PostMapping(value = "/setMessageNoticeSetting", consumes = "application/json")
    @Override
    public ResultDto<Boolean> setMessageNoticeSetting(@RequestBody BaseMessageNoticeSettingReq messageNoticeSettingReq) throws TException {
        return ThriftResponseHelper.responseInvoke("BaseRegionDto", messageNoticeSettingReq, req -> {
            Boolean result = messageNoticeSettingService.setSetting(req);
            return result;
        });
    }
}
