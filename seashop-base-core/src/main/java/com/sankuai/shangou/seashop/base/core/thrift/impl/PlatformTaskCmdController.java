package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.PlatformTaskService;
import com.sankuai.shangou.seashop.base.core.service.model.task.CompleteTaskBo;
import com.sankuai.shangou.seashop.base.core.service.model.task.CreateTaskBo;
import com.sankuai.shangou.seashop.base.core.service.model.task.ExceptionTaskBo;
import com.sankuai.shangou.seashop.base.thrift.core.PlatformTaskCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.CompleteTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.CreateTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.ExceptionTaskReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/platformTask")
public class PlatformTaskCmdController implements PlatformTaskCmdFeign {

    @Resource
    private PlatformTaskService platformTaskService;

    @PostMapping(value = "/createTask", consumes = "application/json")
    @Override
    public ResultDto<Long> createTask(@RequestBody CreateTaskReq createReq) throws TException {
        log.info("【平台后台任务】创建任务, 请求参数={}", createReq);
        return ThriftResponseHelper.responseInvoke("createTask", createReq, func -> {
            CreateTaskBo createTaskBo = JsonUtil.copy(createReq, CreateTaskBo.class);
            // 业务逻辑处理
            return platformTaskService.createTask(createTaskBo);
        });
    }

    @PostMapping(value = "/startTask", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> start(@RequestBody BaseIdReq idReq) throws TException {
        log.info("【平台后台任务】开启任务, 请求参数={}", idReq);
        return ThriftResponseHelper.responseInvoke("start", idReq, func -> {
            // 业务逻辑处理
            platformTaskService.startTask(idReq.getId());
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/completeTask", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> complete(@RequestBody CompleteTaskReq req) throws TException {
        log.info("【平台后台任务】完成任务, 请求参数={}", req);
        return ThriftResponseHelper.responseInvoke("complete", req, func -> {
            CompleteTaskBo completeTaskBo = JsonUtil.copy(req, CompleteTaskBo.class);
            // 业务逻辑处理
            platformTaskService.completeTask(completeTaskBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/exceptionTask", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> exception(@RequestBody ExceptionTaskReq req) throws TException {
        log.info("【平台后台任务】任务异常, 请求参数={}", req);
        return ThriftResponseHelper.responseInvoke("exception", req, func -> {
            ExceptionTaskBo exceptionTaskBo = JsonUtil.copy(req, ExceptionTaskBo.class);
            // 业务逻辑处理
            platformTaskService.exceptionTask(exceptionTaskBo);
            return BaseResp.of();
        });
    }
}
