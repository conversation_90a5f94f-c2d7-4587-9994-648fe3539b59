package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.ImageCategoryService;
import com.sankuai.shangou.seashop.base.core.service.ImageService;
import com.sankuai.shangou.seashop.base.core.service.model.BasePhotoSpaceBo;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory;
import com.sankuai.shangou.seashop.base.thrift.core.ImageQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BasePhotoSpaceQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BasePhotoSpaceCategoryRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BasePhotoSpaceRes;

@RestController
@RequestMapping("/image")
public class ImageQueryController implements ImageQueryFeign {

    @Resource
    private ImageService imageService;

    @Resource
    private ImageCategoryService categoryService;

    @PostMapping(value = "/query", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<BasePhotoSpaceRes>> query(@RequestBody BasePhotoSpaceQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            Page<BasePhotoSpace> photoSpacesResult = imageService.queryWithPage(JsonUtil.copy(query, BasePhotoSpaceBo.class));
            BasePageResp<BasePhotoSpaceRes> result = PageResultHelper.transfer(photoSpacesResult, BasePhotoSpaceRes.class);
            return result;
        });
    }

    @PostMapping(value = "/queryAudit", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<BasePhotoSpaceRes>> queryAudit(@RequestBody BasePhotoSpaceQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryAudit", query, req -> {
            Page<BasePhotoSpace> photoSpacesResult = imageService.queryAuditWithPage(JsonUtil.copy(query, BasePhotoSpaceBo.class));
            BasePageResp<BasePhotoSpaceRes> result = PageResultHelper.transfer(photoSpacesResult, BasePhotoSpaceRes.class);
            return result;
        });
    }

    @GetMapping(value = "/queryCategorys")
    @Override
    public ResultDto<List<BasePhotoSpaceCategoryRes>> queryCategorys(@RequestParam Long shopId) throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("queryCategorys", i, req -> {
            List<BasePhotoSpaceCategory> list = categoryService.query(shopId);
            if (list == null || list.stream().count() < 1) {
                list = new ArrayList<>();
            }

            BasePhotoSpaceCategory category = new BasePhotoSpaceCategory();
            //未分组
            category.setId(0L);
            category.setShopId(shopId);
            category.setPhotoSpaceCatrgoryName("未分组");
            list.add(0, category);

            List<BasePhotoSpaceCategoryRes> result = JsonUtil.copyList(list, BasePhotoSpaceCategoryRes.class);

            for (BasePhotoSpaceCategoryRes categoryRes : result) {
                int imageCount = imageService.getCountByCategoryId(categoryRes.getId(), categoryRes.getShopId());
                categoryRes.setImageCount(imageCount);
            }
            return result;
        });
    }


}
