package com.sankuai.shangou.seashop.base.core.thrift.impl;


import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseAgreement;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomForm;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

@RestController
@RequestMapping("/siteSetting")
public class SiteSettingCMDController implements SiteSettingCMDFeign {

    @Resource
    private SiteSettingService settingService;

    @PostMapping(value = "/saveSettings", consumes = "application/json")
    @Override
    public ResultDto<Boolean> saveSettings(@RequestBody BaseSiteSettingReq settingReq) throws TException {

        settingReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("saveSettings", settingReq, req -> {
            settingService.saveAppSettings(settingReq);
            return true;
        });

    }


    @PostMapping(value = "/saveSettled", consumes = "application/json")
    @Override
    public ResultDto<Boolean> saveSettled(@RequestBody BaseSettledReq settledReq) throws TException {

        return ThriftResponseHelper.responseInvoke("saveSettled", settledReq, req -> {

            boolean result = settingService.saveSettled(req);
            return result;
        });
    }

    @PostMapping(value = "/saveAgreement", consumes = "application/json")
    @Override
    public ResultDto<Boolean> saveAgreement(@RequestBody BaseAgreementReq agreementReq) throws TException {
        agreementReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("saveAgreement", agreementReq, req -> {
            BaseAgreement agreement = JsonUtil.copy(req, BaseAgreement.class);
            boolean result = settingService.saveAgreement(agreement);
            return result;
        });
    }

    @PostMapping(value = "/createCustomFrom", consumes = "application/json")
    @Override
    public ResultDto<Long> createCustomFrom(@RequestBody BaseCustomFormReq baseCustomFormReq) throws TException {
        baseCustomFormReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("createCustomFrom", baseCustomFormReq, req -> {
            req.setUpdateDate(new Date());
            Boolean exist = settingService.existCustomFrom(req.getName());
            if (exist) {
                throw new BusinessException("表单名称不能重复");
            }
            long formId = settingService.createCustomForm(req);
            return formId;

        });
    }

    @PostMapping(value = "/updateCustomFrom", consumes = "application/json")
    @Override
    public ResultDto<Boolean> updateCustomFrom(@RequestBody BaseCustomFormReq baseCustomFormReq) throws TException {
        baseCustomFormReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("updateCustomFrom", baseCustomFormReq, req -> {
            req.checkParameter();
            req.setUpdateDate(new Date());
            BaseCustomForm customFormOld = settingService.getCustomForm(req.getId());
            if (customFormOld == null) {
                throw new BusinessException("找不到该表单");
            }
            boolean result = settingService.updateCustomForm(req);
            return result;
        });
    }

    @PostMapping(value = "/deletesCustomFrom", consumes = "application/json")
    @Override
    public ResultDto<Boolean> deletesCustomFrom(@RequestBody BaseIdsReq baseIdsReq) throws TException {

        return ThriftResponseHelper.responseInvoke("deletesCustomFrom", baseIdsReq, req -> {

            if (req.getIds().stream().count() == 1) {
                BaseCustomForm customFormOld = settingService.getCustomForm(req.getIds().get(0));
                if (customFormOld == null) {
                    throw new IllegalArgumentException("找不到该表单");
                }
            }

            boolean result = settingService.deletesCustomForm(req);
            return result;

        });
    }

    @GetMapping(value = "/existCustomFrom")
    @Override
    public ResultDto<Boolean> existCustomFrom(@RequestParam String name) throws TException {
        if (StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("表单名称不能为空");
        }
        return ThriftResponseHelper.responseInvoke("deletesCustomFrom", name, req -> {
            try {
                boolean result = settingService.existCustomFrom(req);
                return result;
            }
            catch (Exception ex) {
                new TException(ex);
            }
            return true;
        });
    }

    @Override
    public ResultDto<Boolean> saveShopSettings(BaseShopSitSettingReq settingReq) throws TException {
        settingReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("saveShopSettings", settingReq, req -> settingService.saveShopSettings(req));
    }

    @PostMapping(value = "/saveProductSettings", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveProductSettings(@RequestBody SaveProductSettingReq settingReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveProductSettings", settingReq, req -> {
            req.checkParameter();

            settingService.saveProductSettings(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/saveAllAgreement", consumes = "application/json")
    @Override
    public ResultDto<Boolean> saveAllAgreement(@RequestBody BaseAllAgreementReq agreementReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveAllAgreement", agreementReq, req -> {
            req.checkParameter();
            settingService.saveAllAgreement(req);
            return true;
        });
    }

    @PostMapping(value = "/saveSmsSetting", consumes = "application/json")
    public ResultDto<BaseResp> saveSmsSetting(@RequestBody SmsSettingReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("saveSmsSetting", req, fun1 -> settingService.saveSmsSetting(fun1));
    }

    @PostMapping(value = "/saveShopStyle", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveShopStyle(SystemStyleReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("saveShopStyle", req, fun1 -> settingService.saveShopStyle(fun1));
    }

    @PostMapping(value = "/saveExpressConfig", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveExpressConfig(ExpressConfigReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("saveExpressConfig", req, fun1 -> settingService.saveExpressConfig(fun1));
    }


}
