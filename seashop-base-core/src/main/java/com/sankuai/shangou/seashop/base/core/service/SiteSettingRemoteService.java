package com.sankuai.shangou.seashop.base.core.service;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled;
import com.sankuai.shangou.seashop.base.thrift.core.response.AppSitSettingRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseSitSettingRes;

import java.util.Date;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname SiteSettingRemoteService
 * Description //TODO
 * @date 2024/8/9 16:13
 */
public interface SiteSettingRemoteService {

    /**
     * 获取系统配置
     *
     * @return
     */
    BaseSitSettingRes getSetting();

    /**
     * 获取app配置
     * @return
     */
    AppSitSettingRes getAppSetting();

    /**
     * 获取入驻配置
     * @return
     */
    BaseSettled getSettled();

    /**
     * 根据key获取value
     * @param key
     * @return
     */
    String querySettingsValueByKey(String key);
    /**
     * 获取最后续签提醒时间
     * @return
     */
    default Date getLastRenewRemindTime() {
        Date now = new Date();
//        计算到期时间
        return DateUtil.offsetDay(now, 0);
    }
}
