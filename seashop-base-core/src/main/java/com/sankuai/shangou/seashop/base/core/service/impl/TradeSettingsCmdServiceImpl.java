package com.sankuai.shangou.seashop.base.core.service.impl;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.core.service.TradeSettingsCmdService;
import com.sankuai.shangou.seashop.base.core.service.TradeSettingsQueryService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.thrift.core.request.TradeSiteSettingsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.TradeSiteSettingsResp;

import lombok.extern.slf4j.Slf4j;
/**
 * @author： liweisong
 * @create： 2023/11/22 17:30
 */
@Service
@Slf4j
public class TradeSettingsCmdServiceImpl implements TradeSettingsCmdService {

    @Resource
    private SiteSettingService siteSettingService;

    @Resource
    private TradeSettingsQueryService tradeSettingsQueryService;

    @Resource
    private BaseLogAssist baseLogAssist;

    @Override
    public void addOrUpdateSiteSetting(TradeSiteSettingsReq tradeSiteSettingsReq) {
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getUnpaidTimeout())
                || tradeSiteSettingsReq.getUnpaidTimeout() < 1 || tradeSiteSettingsReq.getUnpaidTimeout() > 999999, "保存失败，错误的未付款超时，必须大于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getNoReceivingTimeout())
                || tradeSiteSettingsReq.getNoReceivingTimeout() < 1 || tradeSiteSettingsReq.getNoReceivingTimeout() > 999999, "保存失败，错误的确认收货超时，必须大于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getBeforeReceivingDays())
                || tradeSiteSettingsReq.getBeforeReceivingDays() < 0 || tradeSiteSettingsReq.getBeforeReceivingDays() > 999999, "保存失败，错误的自动收货完成前时间，必须大于或者等于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getNoReceivingDelayDays())
                || tradeSiteSettingsReq.getNoReceivingDelayDays() < 0 || tradeSiteSettingsReq.getNoReceivingDelayDays() > 999999, "保存失败，错误的延迟收货时间，必须大于或者等于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getOrderCommentTimeout())
                || tradeSiteSettingsReq.getOrderCommentTimeout() < 1 || tradeSiteSettingsReq.getOrderCommentTimeout() > 999999, "保存失败，错误的关闭评价通道时限，必须大于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getOrderWaitDeliveryRemindTime())
                || tradeSiteSettingsReq.getOrderWaitDeliveryRemindTime() < 0 || tradeSiteSettingsReq.getOrderWaitDeliveryRemindTime() > 999999, "保存失败，错误的供应商未发货自动短信提醒时限，必须大于或者等于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getCompanyBankOrderAmount())
                || tradeSiteSettingsReq.getCompanyBankOrderAmount().compareTo(BigDecimal.ZERO) < 0, "保存失败，错误的企业网银限制金额，必须大于或者等于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getSalesReturnTimeout())
                || tradeSiteSettingsReq.getSalesReturnTimeout() < 0 || tradeSiteSettingsReq.getSalesReturnTimeout() > 999999, "保存失败，错误的订单退货期限，必须大于或者等于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getShopConfirmTimeout())
                || tradeSiteSettingsReq.getShopConfirmTimeout() < 1 || tradeSiteSettingsReq.getShopConfirmTimeout() > 999999, "保存失败，错误的供应商自动确认售后时限，必须大于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getSendGoodsCloseTimeout())
                || tradeSiteSettingsReq.getSendGoodsCloseTimeout() < 1 || tradeSiteSettingsReq.getSendGoodsCloseTimeout() > 999999, "保存失败，错误的用户发货限时，必须大于0小于等于999999");
        AssertUtil.throwIfTrue(Objects.isNull(tradeSiteSettingsReq.getShopNoReceivingTimeout())
                || tradeSiteSettingsReq.getShopNoReceivingTimeout() < 1 || tradeSiteSettingsReq.getShopNoReceivingTimeout() > 999999, "保存失败，错误的供应商确认到货时限，必须大于0小于等于999999");
        Field[] fields = tradeSiteSettingsReq.getClass().getDeclaredFields();
        Map<String, String> settings = new Hashtable<>();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                String name = field.getName();
                Object value = field.get(tradeSiteSettingsReq);
                if (value != null) {
                    settings.put(name, value.toString());
                }
            } catch (IllegalAccessException e) {
                log.error("TradeSettingsCmdServiceImpl addOrUpdateSiteSetting is error e={}",e);
            }
        }
        AssertUtil.throwIfTrue(settings.size() < 1, "交易参数交易设置保存失败");
        List<BaseSiteSetting> settingList = new ArrayList<>();
        for (Map.Entry<String, String> entry : settings.entrySet()) {
            BaseSiteSetting setting = new BaseSiteSetting();
            setting.setKey(entry.getKey());
            setting.setValue(entry.getValue());
            settingList.add(setting);
        }
        // 查询旧值
        TradeSiteSettingsResp oldValue = tradeSettingsQueryService.queryTradeSiteSetting();
        TradeSiteSettingsResp newValue = JsonUtil.copy(tradeSiteSettingsReq, TradeSiteSettingsResp.class);
        // 手动写日志
        baseLogAssist.recordLog(ExaminModelEnum.SYSTEM_SETTING, ExaProEnum.MODIFY, "交易参数设置",
            tradeSiteSettingsReq.getOperationUserId(), tradeSiteSettingsReq.getOperationShopId(),
            oldValue, newValue);
        siteSettingService.updateSettingsByKey(settingList);
    }
}
