package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.thrift.core.dto.QueryExpressCompanyDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryBatchExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryErpExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyPageReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressSiteSettingResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyPageResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyResp;

public interface ExpressSelfQueryService {

    // 查询所有设置的快递公司
    QueryExpressCompanyResp queryExpressCompanyList(QueryExpressCompanyReq queryExpressCompanyReq);

    /**
     * 批量查询快递公司信息
     *
     * @param req 入参
     * @return 快递公司列表
     */
    QueryExpressCompanyResp queryBatchExpressCompany(QueryBatchExpressCompanyReq req);

    /**
     * 通过erp三方快递编号查询快递公司信息
     *
     * @param req erp三方快递编号
     * @return 快递公司信息
     */
    QueryExpressCompanyDto queryExpressByThirdCode(QueryErpExpressCompanyReq req);

    // 查询物流设置
    ExpressSiteSettingResp queryExpressSiteSetting();


    QueryExpressCompanyPageResp queryExpressCompanyPage(QueryExpressCompanyPageReq req);
}
