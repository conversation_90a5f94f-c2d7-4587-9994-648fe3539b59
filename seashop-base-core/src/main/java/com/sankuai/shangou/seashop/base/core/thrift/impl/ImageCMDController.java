package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.ImageCategoryService;
import com.sankuai.shangou.seashop.base.core.service.ImageService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory;
import com.sankuai.shangou.seashop.base.thrift.core.ImageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/image")
public class ImageCMDController implements ImageCMDFeign {


    @Resource
    private ImageService imageService;

    @Resource
    private ImageCategoryService categoryService;

    @PostMapping(value = "/create", consumes = "application/json")
    @Override
    public ResultDto<Long> create(@RequestBody BasePhotoSpaceReq query) throws TException {
        query.checkParameter();
        return ThriftResponseHelper.responseInvoke("create", query, req -> {
            long result = imageService.create(req);
            return result;
        });
    }

    @PostMapping(value = "/batchCreate", consumes = "application/json")
    @Override
    public ResultDto<Boolean> batchCreate(@RequestBody List<BasePhotoSpaceReq> query) throws TException {
        for (BasePhotoSpaceReq photoSpaceReq : query) {
            photoSpaceReq.checkParameter();
        }
        return ThriftResponseHelper.responseInvoke("batchCreate", query, req -> {

            imageService.batchCreate(req);
            return true;
        });
    }

    @PostMapping(value = "/update", consumes = "application/json")
    @Override
    public ResultDto<Boolean> update(@RequestBody UpdatePhotoSpaceReq query) throws TException {
        query.checkParameter();
        return ThriftResponseHelper.responseInvoke("create", query, req -> {
            BasePhotoSpace photoSpace = new BasePhotoSpace();
            photoSpace.setId(req.getId());
            photoSpace.setShopId(req.getShopId());
            photoSpace.setPhotoName(req.getPhotoName());
            Boolean result = imageService.update(photoSpace);
            return result;
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @Override
    public ResultDto<Boolean> delete(@RequestBody BasePhotoSpaceReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("delete", query, req -> {
            return imageService.delete(query);
        });
    }

    @PostMapping(value = "/moveImageByCateId", consumes = "application/json")
    @Override
    public ResultDto<Boolean> moveImageByCateId(@RequestBody BaseMoveCateImageReq query) throws TException {
        query.checkParameter();
        return ThriftResponseHelper.responseInvoke("moveImageByCateId", query, req -> {
            return imageService.moveImages(req.getSourceId(), req.getTargetId(), req.getShopId());
        });
    }

    @PostMapping(value = "/moveImageById", consumes = "application/json")
    @Override
    public ResultDto<Boolean> moveImageById(@RequestBody BaseMoveImageReq query) throws TException {
        query.checkParameter();
        return ThriftResponseHelper.responseInvoke("moveImageById", query, req -> {
            return imageService.move(req.getIds(), req.getcId(), req.getShopId());
        });
    }

    @PostMapping(value = "/deleteImageByIds", consumes = "application/json")
    @Override
    public ResultDto<Boolean> deleteImageByIds(@RequestBody BaseIdsReq idsReq) throws TException {
        idsReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("deleteImageByIds", idsReq, req -> {
            return imageService.deletes(idsReq.getIds(), idsReq.getShopId());
        });
    }

    @PostMapping(value = "/createCategory", consumes = "application/json")
    @Override
    public ResultDto<Long> createCategory(@RequestBody BasePhotoSpaceCategoryReq categoryReq) throws TException {
        categoryReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("createCategory", categoryReq, req -> {
            //默认排序1
            req.setDisplaysSequence(1L);
            BasePhotoSpaceCategory category = JsonUtil.copy(req, BasePhotoSpaceCategory.class);
            return categoryService.create(category);
        });
    }

    @PostMapping(value = "/updateCategory", consumes = "application/json")
    @Override
    public ResultDto<Boolean> updateCategory(@RequestBody BasePhotoSpaceCategoryReq categoryReq) throws TException {
        categoryReq.checkParameter();
        if (categoryReq.getId().equals(0L)) {
            throw new IllegalArgumentException("未分组不能修改");
        }
        return ThriftResponseHelper.responseInvoke("updateCategory", categoryReq, req -> {
            BasePhotoSpaceCategory category = JsonUtil.copy(req, BasePhotoSpaceCategory.class);
            categoryService.update(category);
            return true;
        });
    }

    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    @Override
    public ResultDto<Boolean> deleteCategory(@RequestBody BaseIdsReq categoryReq) throws TException {
        categoryReq.checkParameter();
        if (categoryReq.getIds().contains(0L)) {
            throw new IllegalArgumentException("未分组不能删除");
        }
        return ThriftResponseHelper.responseInvoke("updateCategory", categoryReq, req -> {
            BasePhotoSpaceCategory category = JsonUtil.copy(req, BasePhotoSpaceCategory.class);
            categoryService.deletes(req.getIds(), req.getShopId());
            return true;
        });
    }
}
