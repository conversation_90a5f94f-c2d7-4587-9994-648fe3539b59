package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogDetailReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogMQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogSellerQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.LogDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.LogQueryResp;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:14
 */
public interface LogQueryService {

    // 操作日志查询
    BasePageResp<LogQueryResp> pageMBaseLog(LogMQueryReq logQueryReq);

    // 操作日志查询
    BasePageResp<LogQueryResp> pageSellerBaseLog(LogSellerQueryReq logQueryReq);

    // 操作日志查看详情
    LogDetailResp queryBaseLogDetail(LogDetailReq logDetailReq);

}
