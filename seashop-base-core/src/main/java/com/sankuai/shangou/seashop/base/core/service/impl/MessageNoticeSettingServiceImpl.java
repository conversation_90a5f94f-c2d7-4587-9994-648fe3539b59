package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.MessageNoticeSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSetting;
import com.sankuai.shangou.seashop.base.dao.core.repository.MessageNoticeSettingRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseMessageNoticeSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMessageNoticeSettingRes;

@Service
public class MessageNoticeSettingServiceImpl implements MessageNoticeSettingService {
    @Resource
    private MessageNoticeSettingRepository messageNoticeSettingRepository;

    @Override
    public BaseMessageNoticeSettingRes selectById(int type) {
        BaseMessageNoticeSetting daoModel = messageNoticeSettingRepository.getSettings(type);
        BaseMessageNoticeSettingRes result = JsonUtil.copy(daoModel, BaseMessageNoticeSettingRes.class);
        return result;
    }

    @Override
    public List<BaseMessageNoticeSettingRes> getSettings() {
        List<BaseMessageNoticeSetting> daoList = messageNoticeSettingRepository.getSettings();
        List<BaseMessageNoticeSettingRes> result = JsonUtil.copyList(daoList, BaseMessageNoticeSettingRes.class);
        return result;
    }

    @Override
    public Boolean setSetting(BaseMessageNoticeSettingReq settingReq) {

        BaseMessageNoticeSetting setting = JsonUtil.copy(settingReq, BaseMessageNoticeSetting.class);
        BaseMessageNoticeSetting oldModel = messageNoticeSettingRepository.getSettings(settingReq.getMessageType());
        if (oldModel==null){
            messageNoticeSettingRepository.addSetting(setting);
        }else {
            messageNoticeSettingRepository.setSetting(setting);
        }

        return true;
    }
}
