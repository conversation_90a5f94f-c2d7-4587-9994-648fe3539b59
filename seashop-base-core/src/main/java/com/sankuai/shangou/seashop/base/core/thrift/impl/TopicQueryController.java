package com.sankuai.shangou.seashop.base.core.thrift.impl;


import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.enums.BaseTemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.common.util.template.TemplateStorageService;
import com.sankuai.shangou.seashop.base.core.service.BaseTemplatePageService;
import com.sankuai.shangou.seashop.base.core.service.TopicService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageKey;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.thrift.core.TopicQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ClientTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWapTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseShopReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseTopicRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWapTopicRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.TemplatePageDto;

@RestController
@RequestMapping("/topic")
public class TopicQueryController implements TopicQueryFeign {

    @Value("${hishop.storage.domain}")
    private String hostName;
    @Value("${hishop.storage.base-path}")
    private String bucketName;
    @Resource
    private TopicService topicService;

    @Resource
    private TemplateStorageService storageService;

    @Resource
    private S3plusStorageService s3plusStorageService;
    @Resource
    private BaseTemplatePageService templatePageService;

    @PostMapping(value = "query", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<BaseTopicRes>> query(@RequestBody BaseTopicQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandForPage", query, req -> {
            Page<BaseTopic> photoSpacesResult = topicService.queryWithPage(query);
            BasePageResp<BaseTopicRes> result = PageResultHelper.transfer(photoSpacesResult, BaseTopicRes.class);
            return result;
        });
    }

    @GetMapping(value = "queryRecommend")
    @Override
    public ResultDto<List<BaseTopicRes>> queryRecommend() throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandForPage", "query", req -> {
            List<BaseTopic> baseTopics = topicService.queryRecommend();
            List<BaseTopicRes> result = JsonUtil.copyList(baseTopics, BaseTopicRes.class);
            return result;
        });
    }

    @PostMapping(value = "getById", consumes = "application/json")
    @Override
    public ResultDto<BaseTopicRes> getById(@RequestBody BaseShopReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("create", query, req -> {
            req.checkParameter();
            if (req.getId() == null) {
                throw new IllegalArgumentException("专题id不能为空");
            }
            BaseTopic topic = topicService.getById(query.getId(), query.getShopId());
            BaseTopicRes topicRes = JsonUtil.copy(topic, BaseTopicRes.class);
            return topicRes;
        });
    }

    @PostMapping(value = "getWapTopicById", consumes = "application/json")
    @Override
    public ResultDto<BaseWapTopicRes> getWapTopicById(@RequestBody BaseWapTopicQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("create", query, req -> {
            String filePath = storageService.getTemplatePath(query.getClient(), BaseTemplateClientTypeEnum.getByCode(query.getType()), query.getShopId());
            String dataPath = hostName + bucketName + "/" + filePath + "data/default.json";
            try {
                InputStream stream = s3plusStorageService.readExcelFromOnlineUrl(dataPath);
                InputStreamReader inputStreamReader = new InputStreamReader(stream);
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                StringBuilder stringBuilder = new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    stringBuilder.append(line);
                    stringBuilder.append(System.lineSeparator());
                }
                BaseWapTopicRes result = new BaseWapTopicRes();
                String jsonStr = stringBuilder.toString();
                jsonStr = jsonStr.trim().replaceAll("\n$", "");
                result.setPage(jsonStr);
                return result;
            } catch (IllegalStateException e) {
                return new BaseWapTopicRes("{}");
            } catch (IOException e) {
                return new BaseWapTopicRes("{}");
            }
        });
    }

    @GetMapping(value = "getPCIndex")
    @Override
    public ResultDto<BaseWapTopicRes> getPCIndex() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("getPCIndex", i, req -> {
            String filePath = storageService.getTemplatePath("index", BaseTemplateClientTypeEnum.PCIndex, 0L);
            String dataPath = hostName + bucketName + "/" + filePath + "data/default.json";
            try {
                InputStream stream = s3plusStorageService.readExcelFromOnlineUrl(dataPath);
                InputStreamReader inputStreamReader = new InputStreamReader(stream);
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                StringBuilder stringBuilder = new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    stringBuilder.append(line);
                    stringBuilder.append(System.lineSeparator());
                }
                BaseWapTopicRes result = new BaseWapTopicRes();
                String jsonStr = stringBuilder.toString();
                result.setPage(jsonStr);
                return result;
            } catch (IOException e) {
                return new BaseWapTopicRes("{}");
            }
        });
    }

    @Override
    public ResultDto<String> getPlatIndex() throws TException {
        return ThriftResponseHelper.responseInvoke("getPCIndex", null, req -> {
            BaseTemplatePageKey key = new BaseTemplatePageKey();
            key.setClient(ClientTypeEnum.Default.getCode());
            key.setType(BaseTemplateClientTypeEnum.PCIndex.getCode());
            key.setShopId(0L);
            key.setvShopId(0L);
            BaseTemplatePageWithBLOBs model = templatePageService.getById(key);
            if (model == null) {
                return "{}";
            } else {
                TemplatePageDto resultObj = new TemplatePageDto();
                resultObj.setPage(JsonUtil.parseObject(model.getjPage(), Object.class));
                resultObj.setLModules(JsonUtil.parseObject(model.getlModules(), List.class));
                resultObj.setPModules(JsonUtil.parseObject(model.getpModules(), List.class));
                String result = JsonUtil.toJsonString(resultObj);
                return result;
            }
        });
    }

    @Override
    public ResultDto<String> getPlatWapIndex() throws TException {
        return ThriftResponseHelper.responseInvoke("getPCIndex", null, req -> {
            BaseTemplatePageKey key = new BaseTemplatePageKey();
            key.setClient(ClientTypeEnum.Default.getCode());
            key.setType(BaseTemplateClientTypeEnum.WXSmallProgram.getCode());
            key.setShopId(0L);
            key.setvShopId(0L);
            BaseTemplatePageWithBLOBs model = templatePageService.getById(key);
            if (model == null) {
                return "{}";
            } else {
                TemplatePageDto resultObj = new TemplatePageDto();
                resultObj.setPage(JsonUtil.parseObject(model.getjPage(), Object.class));
                resultObj.setLModules(JsonUtil.parseObject(model.getlModules(), List.class));
                resultObj.setPModules(JsonUtil.parseObject(model.getpModules(), List.class));
                String result = JsonUtil.toJsonString(resultObj);
                return result;
            }
        });
    }

    @GetMapping(value = "getSellerIndex")
    @Override
    public ResultDto<String> getSellerIndex(@RequestParam Long shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("getPCIndex", shopId, req -> {
            BaseTemplatePageKey key = new BaseTemplatePageKey();
            key.setClient(ClientTypeEnum.Default.getCode());
            key.setType(BaseTemplateClientTypeEnum.WXSmallProgramSellerWapIndex.getCode());
            key.setShopId(req);
            key.setvShopId(0L);
            BaseTemplatePageWithBLOBs model = templatePageService.getById(key);
            if (model == null) {
                return "{}";
            } else {
                TemplatePageDto resultObj = new TemplatePageDto();
                resultObj.setPage(JsonUtil.parseObject(model.getjPage(), Object.class));
                resultObj.setLModules(JsonUtil.parseObject(model.getlModules(), List.class));
                resultObj.setPModules(JsonUtil.parseObject(model.getpModules(), List.class));
                String result = JsonUtil.toJsonString(resultObj);
                return result;
            }

        });
    }


    @GetMapping(value = "getSellerPCIndex")
    @Override
    public ResultDto<String> getSellerPCIndex(@RequestParam Long shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("getPCIndex", shopId, req -> {
            BaseTemplatePageKey key = new BaseTemplatePageKey();
            key.setClient(ClientTypeEnum.Default.getCode());
            key.setType(BaseTemplateClientTypeEnum.PCIndex_SELLER.getCode());
            key.setShopId(req);
            key.setvShopId(0L);
            BaseTemplatePageWithBLOBs model = templatePageService.getById(key);
            if (model == null) {
                return "{}";
            } else {
                TemplatePageDto resultObj = new TemplatePageDto();
                resultObj.setPage(JsonUtil.parseObject(model.getjPage(), Object.class));
                resultObj.setLModules(JsonUtil.parseObject(model.getlModules(), List.class));
                resultObj.setPModules(JsonUtil.parseObject(model.getpModules(), List.class));
                String result = JsonUtil.toJsonString(resultObj);
                return result;
            }

        });
    }
    @GetMapping(value = "getWapIndex")
    @Override
    public ResultDto<String> getWapIndex() throws TException {
        return ThriftResponseHelper.responseInvoke("getPCIndex", null, req -> {
            BaseTemplatePageKey key = new BaseTemplatePageKey();
            key.setClient(ClientTypeEnum.Default.getCode());
            key.setType(BaseTemplateClientTypeEnum.WXSmallProgram.getCode());
            key.setShopId(0L);
            key.setvShopId(0L);
            BaseTemplatePageWithBLOBs model = templatePageService.getById(key);
            if (model == null) {
                return "{}";
            } else {
                TemplatePageDto resultObj = new TemplatePageDto();
                resultObj.setPage(JsonUtil.parseObject(model.getjPage(), Object.class));
                resultObj.setLModules(JsonUtil.parseObject(model.getlModules(), List.class));
                resultObj.setPModules(JsonUtil.parseObject(model.getpModules(), List.class));
                String result = JsonUtil.toJsonString(resultObj);
                return result;
            }

        });
    }
}
