package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.ArticleCategoryService;
import com.sankuai.shangou.seashop.base.core.service.ArticleService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticle;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategory;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategoryExample;
import com.sankuai.shangou.seashop.base.thrift.core.ArticleQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryTopArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.*;
import org.apache.thrift.TException;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/article")
public class ArticleQueryController implements ArticleQueryFeign {

    @Resource
    private ArticleService articleService;

    @Resource
    private ArticleCategoryService articleCategoryService;

    @PostMapping(value = "/queryWithPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<BaseArticleRes>> queryWithPage(@RequestBody BaseArticleQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWithPage", query, req -> {
            Page<BaseArticle> pageList = articleService.query(query);
            BasePageResp<BaseArticleRes> result = PageResultHelper.transfer(pageList, BaseArticleRes.class);
            if (result.getData() != null && result.getData().size() > 0) {
                List<Long> categoryIds = result.getData().stream().map(t -> t.getCategoryId()).distinct().collect(Collectors.toList());
                BaseArticleCategoryExample example = new BaseArticleCategoryExample();
                BaseArticleCategoryExample.Criteria criteria = example.createCriteria();
                criteria.andIdIn(categoryIds);
                List<BaseArticleCategory> categories = articleCategoryService.query(example);
                // 获取分类名称
                for (BaseArticleRes article : result.getData()) {
                    if (article.getCategoryId() != null && article.getCategoryId() == 0) {
                        article.setCategoryName("暂无分类");
                        continue;
                    }
                    if (categories.stream()
                        .filter(t -> t.getId().equals(article.getCategoryId())).findFirst().isPresent()) {
                        BaseArticleCategory category = categories.stream()
                            .filter(t -> t.getId().equals(article.getCategoryId())).findFirst().get();
                        if (StringUtils.isEmpty(article.getCategoryName())) {
                            article.setCategoryName(category.getName());
                        }
                    }
                    else {
                        article.setCategoryName("暂无分类");
                    }

                }
            }

            return result;
        });
    }

    @PostMapping(value = "/queryTopByCategoryId", consumes = "application/json")
    @Override
    public ResultDto<List<BaseArticleRes>> queryTopByCategoryId(@RequestBody QueryTopArticleReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryTopByCategoryId", query, req -> {
            List<BaseArticle> daoList = articleService.queryByCategoryId(req);
            List<BaseArticleRes> result = JsonUtil.copyList(daoList, BaseArticleRes.class);
            return result;
        });
    }

    @GetMapping(value = "/queryAllCategory")
    @Override
    public ResultDto<ArticleCategoryListRes> queryAllCategory() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("queryAllCategory", i, req -> {
            List<BaseArticleCategory> list = articleCategoryService.query();
            List<BaseArticleCategoryRes> resultList = JsonUtil.copyList(list, BaseArticleCategoryRes.class);
            ArticleCategoryListRes result = new ArticleCategoryListRes();
            List<BaseArticleCategoryRes> topCategorys = resultList.stream()
                .filter(t -> t.getParentCategoryId() == 0L).collect(Collectors.toList());

            for (BaseArticleCategoryRes category : topCategorys) {
                List<BaseArticleCategoryChildrenRes> childrens = JsonUtil.copyList(resultList.stream()
                    .filter(t -> t.getParentCategoryId().equals(category.getId())).collect(Collectors.toList()), BaseArticleCategoryChildrenRes.class);
                category.setChildrens(childrens);
            }

            result.setCategorys(topCategorys);
            return result;
        });
    }

    @PostMapping(value = "/getArticleById", consumes = "application/json")
    @Override
    public ResultDto<BaseArticleRes> getArticleById(@RequestBody BaseReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getArticleById", query, req -> {
            BaseArticle article = articleService.getById(query.getId());
            BaseArticleRes result = JsonUtil.copy(article, BaseArticleRes.class);
            return result;
        });
    }

    @PostMapping(value = "/getBaseArticleCategoryById", consumes = "application/json")
    @Override
    public ResultDto<BaseArticleCategoryRes> getBaseArticleCategoryById(@RequestBody BaseReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getArticleById", query, req -> {
            BaseArticleCategory articleCategory = articleCategoryService.getById(query.getId());
            BaseArticleCategoryRes result = JsonUtil.copy(articleCategory, BaseArticleCategoryRes.class);
            return result;
        });
    }

    @PostMapping(value = "/getBaseArticleCategoryByParentId", consumes = "application/json")
    @Override
    public ResultDto<List<BaseArticleCategoryRes>> getBaseArticleCategoryByParentId(@RequestBody BaseReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getArticleById", query, req -> {
            List<BaseArticleCategory> articleCategory = articleCategoryService.getChildsById(query.getId());
            List<BaseArticleCategoryRes> result = JsonUtil.copyList(articleCategory, BaseArticleCategoryRes.class);
            return result;
        });
    }

    @PostMapping(value = "/getArticleCategorysByParentId", consumes = "application/json")
    @Override
    public ResultDto<List<SystemArticleCategoryRes>> getArticleCategorysByParentId(@RequestBody BaseReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getArticleCategorysByParentId", query, req -> {
            List<BaseArticleCategory> articleCategory = articleCategoryService.getChildsById(req.getId());

            List<SystemArticleCategoryRes> result = JsonUtil.copyList(articleCategory, SystemArticleCategoryRes.class);
            if (result != null && result.stream().count() > 0) {
                List<Long> cids = result.stream().map(t -> t.getId()).collect(Collectors.toList());

                List<BaseArticle> articles = articleService.queryInCategoryId(cids);
                for (SystemArticleCategoryRes item : result) {
                    List<BaseArticle> itemArticles = articles.stream().filter(t -> t.getCategoryId().equals(item.getId())).collect(Collectors.toList());
                    List<BaseArticleRes> articleRes = JsonUtil.copyList(itemArticles, BaseArticleRes.class);
                    item.setArticleRes(articleRes);
                }
            }
            return result;
        });
    }
}
