package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.constant.LimitTimeBuyConstant;
import com.sankuai.shangou.seashop.base.common.enums.BaseResultCodeEnum;
import com.sankuai.shangou.seashop.base.core.service.SlideAdService;
import com.sankuai.shangou.seashop.base.dao.core.domain.SlideAd;
import com.sankuai.shangou.seashop.base.dao.core.repository.SlideAdRepository;
import com.sankuai.shangou.seashop.base.thrift.core.enums.MoveTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.SlideAdTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MoveLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.SlideAdResp;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Service
@Slf4j
public class SlideAdServiceImpl implements SlideAdService {

    @Resource
    private SlideAdRepository slideAdRepository;

    @Override
    public List<SlideAdResp> queryLimitTimeBuy() {
        List<SlideAd> slideAds = slideAdRepository.queryByType(SlideAdTypeEnum.PLATFORM_LIMIT_TIME.getType());
        if (CollUtil.isEmpty(slideAds)) {
            return null;
        }
        return JsonUtil.copyList(slideAds, SlideAdResp.class);
    }

    @Override
    public void addLimitTimeBuy(AddLimitTimeBuyReq request) {
        List<SlideAd> slideAds = slideAdRepository.queryByType(SlideAdTypeEnum.PLATFORM_LIMIT_TIME.getType());
        if (CollUtil.isNotEmpty(slideAds) && slideAds.size() >= LimitTimeBuyConstant.LIMIT_TIME_BUY_MAX_COUNT) {
            throw new BusinessException(BaseResultCodeEnum.LIMIT_TIME_BUY_MAX_COUNT.getCode(),
                    String.format(BaseResultCodeEnum.LIMIT_TIME_BUY_MAX_COUNT.getMsg(), LimitTimeBuyConstant.LIMIT_TIME_BUY_MAX_COUNT));
        }
        Long max = 0L;
        if (CollUtil.isNotEmpty(slideAds)) {
            // 获取最大的排序
            max = slideAds.stream().mapToLong(SlideAd::getDisplaySequence).max().orElse(0L);
        }
        SlideAd slideAd = new SlideAd();
        slideAd.setImageUrl(request.getImageUrl());
        slideAd.setUrl(request.getUrl());
        slideAd.setDisplaySequence(max + 1);
        slideAd.setTypeId(SlideAdTypeEnum.PLATFORM_LIMIT_TIME.getType());
        slideAdRepository.save(slideAd);

    }

    @Override
    public void updateLimitTimeBuy(UpdateLimitTimeBuyReq request) {
        Long id = request.getId();
        SlideAd slideAd = slideAdRepository.getById(id);
        if (null == slideAd || slideAd.getDelFlag()) {
            throw new BusinessException(BaseResultCodeEnum.LIMIT_TIME_BUY_DATA_NOT_EXIST.getCode(), BaseResultCodeEnum.LIMIT_TIME_BUY_DATA_NOT_EXIST.getMsg());
        }
        slideAd.setImageUrl(request.getImageUrl());
        slideAd.setUrl(request.getUrl());
        slideAdRepository.updateById(slideAd);
    }

    @Override
    public void moveLimitTimeBuy(MoveLimitTimeBuyReq request) {
        Long id = request.getId();
        SlideAd slideAd = slideAdRepository.getById(id);
        if (null == slideAd) {
            throw new BusinessException(BaseResultCodeEnum.LIMIT_TIME_BUY_DATA_NOT_EXIST.getCode(), BaseResultCodeEnum.LIMIT_TIME_BUY_DATA_NOT_EXIST.getMsg());
        }
        Long displaySequence = slideAd.getDisplaySequence();
        SlideAd updateSlideAd;
        if (request.getType().equals(MoveTypeEnum.UP.getType())) {
            // 上移
            updateSlideAd = slideAdRepository.queryFirstLessThanDisplaySequence(SlideAdTypeEnum.PLATFORM_LIMIT_TIME.getType(), displaySequence);
        } else {
            // 下移
            updateSlideAd = slideAdRepository.queryFirstGreaterThanDisplaySequence(SlideAdTypeEnum.PLATFORM_LIMIT_TIME.getType(), displaySequence);
        }
        if (null == updateSlideAd) {
            // 说明已经到顶了，不能再移动了，直接返回
            return;
        }
        slideAd.setDisplaySequence(updateSlideAd.getDisplaySequence());
        updateSlideAd.setDisplaySequence(displaySequence);

        TransactionHelper.doInTransaction(() -> {
            slideAdRepository.updateById(slideAd);
            slideAdRepository.updateById(updateSlideAd);
        });
    }

    @Override
    public void deleteLimitTimeBuy(BaseIdReq request) {
        Long id = request.getId();
        SlideAd slideAd = slideAdRepository.getById(id);
        if (null == slideAd) {
            throw new BusinessException(BaseResultCodeEnum.LIMIT_TIME_BUY_DATA_NOT_EXIST.getCode(), BaseResultCodeEnum.LIMIT_TIME_BUY_DATA_NOT_EXIST.getMsg());
        }
        slideAd.setDelFlag(Boolean.TRUE);
        slideAdRepository.updateById(slideAd);
    }
}
