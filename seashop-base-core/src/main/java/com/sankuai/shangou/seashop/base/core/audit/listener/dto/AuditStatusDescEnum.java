package com.sankuai.shangou.seashop.base.core.audit.listener.dto;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/05/13
 */
@Getter
public enum AuditStatusDescEnum {
    FORBID_SALE_GOODS(1, "平台禁售商品"),
    ILLEGAL_AD(2,"违规广告信息"),
    ILLEGAL_TERROR(3, "违规暴恐内容"),
    ILLEGAL_POLITICS(4, "违规涉政内容"),
    MORE_INVALID_WORD(5,"较多无效文字信息"),
    ILLEGAL_YELLOW(6,"违规涉黄内容"),
    LOW_INFO(7,"低质量、恶心、盗图等信息"),
    ILLEGAL_AD_LANG(8,"违规广告语"),
    FORBID_TRADE_CONTENT(9, "平台禁止交易内容"),
    PATENT_INFRINGEMENT(10, "专利侵权内容"),
    ILLEGAL_TRADEMARK(11,"违规商标信息"),
    ILLEGAL_GUIDE_INFO(12,"不良导向行为信息"),
    PRICE_EXCEPTION(13,"价格异常"),
    EXCEPTION_ACTION_INFO(14,"异常行为信息"),
    SECRET_INFO(15,"隐私信息"),
    COPYRIGHT_INFRINGEMENT(16, "著作侵权内容"),
    HARMFUL_DISCUSS(17, "不当言论信息"),
    NATIONAL_FORBID_SALE(18, "国家禁售商品"),
    OVER_DESC(19, "过度描述"),
    ABSOLUTIZATION_LANGUAGE(20, "绝对化用语"),
    TRADEMARK_INFRINGEMENT(21,"商标侵权内容"),
    GOODS_ILLEGAL_DESC(22,"商品违规用词"),
    ILLEGAL_COMPETITION_DESC(23,"违规竞争用语"),
    WORD_ILLEGAL_DESC(24,"文字违规信息"),
    SUSPECT_CHEAT_INFO(25,"疑似诈骗信息"),
    CONTENT_ILLEGAL(26, "内容违规"),

    DEFAULT_CONTENT(27,"违规信息"),
    DEFAULT_GOODS(28,"平台禁售商品");

    private int code;
    private String desc;

    AuditStatusDescEnum(int code, String desc){
        this.code = code;
        this.desc = desc;
    }
}
