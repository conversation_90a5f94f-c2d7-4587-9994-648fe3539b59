package com.sankuai.shangou.seashop.base.core.audit.util;


import com.sankuai.shangou.seashop.base.core.audit.listener.dto.AuditStatusDescEnum;
import com.sankuai.shangou.seashop.base.core.audit.listener.dto.AuditStatusVerifyEnum;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditUtils {
    private static final String SUB_STATUS_CODE = "subStatusCode";
    private static final String SUB_VERIFY_DESC = "subVerifyDesc";

    public static void processAuditResult(List<Map<String, Object>> auditResultMapList) {
        if (CollectionUtils.isEmpty(auditResultMapList)) {
            return;
        }
        for (Map<String, Object> contentResultMap : auditResultMapList) {
            Integer subStatusCode = (Integer) contentResultMap.get(SUB_STATUS_CODE);
            if (subStatusCode == null) {
                continue;
            }
            AuditStatusDescEnum auditStatusDescEnum =
                    AuditStatusVerifyEnum.statusVerifyMap.getOrDefault(subStatusCode, AuditStatusDescEnum.DEFAULT_CONTENT);
            contentResultMap.put(SUB_VERIFY_DESC, auditStatusDescEnum.getDesc());
        }
    }
}
