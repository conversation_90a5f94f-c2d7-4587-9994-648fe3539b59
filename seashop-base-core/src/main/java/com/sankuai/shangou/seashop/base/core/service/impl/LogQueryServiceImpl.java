package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.LogQueryService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseOperationLog;
import com.sankuai.shangou.seashop.base.dao.core.model.BaseOperationLogModel;
import com.sankuai.shangou.seashop.base.dao.core.repository.LogQueryRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogDetailReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogMQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogSellerQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.LogDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.LogQueryResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:14
 */
@Service
@Slf4j
public class LogQueryServiceImpl implements LogQueryService {

    @Resource
    private LogQueryRepository logQueryRepository;

    @Override
    public BasePageResp<LogQueryResp> pageMBaseLog(LogMQueryReq logQueryReq) {
        Page<BaseOperationLog> logPage = PageHelper.startPage(logQueryReq.getPageNo(), logQueryReq.getPageSize());
        List<BaseOperationLog> list = logQueryRepository.selectList(JsonUtil.copy(logQueryReq, BaseOperationLogModel.class));
        return PageResultHelper.transfer(logPage, LogQueryResp.class);
    }

    @Override
    public BasePageResp<LogQueryResp> pageSellerBaseLog(LogSellerQueryReq logQueryReq) {
        Page<BaseOperationLog> logPage = PageHelper.startPage(logQueryReq.getPageNo(), logQueryReq.getPageSize());
        List<BaseOperationLog> list = logQueryRepository.selectList(JsonUtil.copy(logQueryReq, BaseOperationLogModel.class));
        return PageResultHelper.transfer(logPage, LogQueryResp.class);
    }

    @Override
    public LogDetailResp queryBaseLogDetail(LogDetailReq logDetailReq) {
        LogDetailResp result = new LogDetailResp();
        BaseOperationLogModel baseOperationLogReq = new BaseOperationLogModel();
        baseOperationLogReq.setId(logDetailReq.getId());
        baseOperationLogReq.setShopId(logDetailReq.getShopId());
        List<BaseOperationLog> list = logQueryRepository.selectList(baseOperationLogReq);
        if(CollectionUtils.isEmpty(list)){
            return result;
        }
        result.setOperationName(list.get(0).getOperationName());
        result.setOperationTime(list.get(0).getOperationTime());
        result.setOperationUserAccount(list.get(0).getOperationUserAccount());
        result.setItems(list.get(0).getOperationContent());
        result.setOperationUserId(list.get(0).getOperationUserId());
//        try {
//            result.setItems(new ArrayList(list.get(0).getOperationContent()));
//        } catch (Exception e) {
//            log.error("操作日志详情查询失败", e);
//        }
        return result;
    }
}
