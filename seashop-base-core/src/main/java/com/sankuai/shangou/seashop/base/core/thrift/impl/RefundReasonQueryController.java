package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.RefundReasonQueryService;
import com.sankuai.shangou.seashop.base.thrift.core.RefundReasonQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryRefundReasonResp;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:35
 */
@RestController
@RequestMapping("/refundReason")
public class RefundReasonQueryController implements RefundReasonQueryFeign {

    @Resource
    private RefundReasonQueryService refundReasonQueryService;

    @GetMapping(value = "/queryRefundReasonList")
    @Override
    public ResultDto<QueryRefundReasonResp> queryRefundReasonList() throws TException {
        return ThriftResponseHelper.responseInvoke("queryRefundReasonList", null, req ->
                refundReasonQueryService.queryRefundReasonList());
    }
}
