package com.sankuai.shangou.seashop.base.core.service.impl;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingRemoteService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.thrift.core.response.AppSitSettingRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseSitSettingRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname SiteSettingRemoteServiceImpl
 * Description //TODO
 * @date 2024/8/9 16:13
 */
@Service
@Slf4j
public class SiteSettingRemoteServiceImpl implements SiteSettingRemoteService {

    @Resource
    private SiteSettingService settingService;

    @Override
    public BaseSitSettingRes getSetting() {
        BaseSitSettingRes res = new BaseSitSettingRes();
        Field[] fields = res.getClass().getDeclaredFields();
        List<String> settingKeys = new ArrayList<>();
        for (Field field : fields) {
            if (field.isSynthetic()) {
                //针对某些情况，编译器会引入一些字段，需要过滤掉
                continue;
            }
            String name = field.getName();
            settingKeys.add(name);
        }
        log.info(JsonUtil.toJsonString(settingKeys));
        //根据keys 获取属性值
        List<BaseSiteSetting> list = settingService.query(settingKeys);
        log.info(JsonUtil.toJsonString(list));
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                String name = field.getName();
                if (list.stream().filter(t -> t.getKey().equals(name)).findFirst().isPresent()) {
                    BaseSiteSetting setting = list.stream().filter(t -> t.getKey().equals(name)).findFirst().get();
                    if (!setting.equals(null)) {
                        field.set(res, setting.getValue());
                    }
                }

            }
        } catch (Exception exception) {
            throw new RuntimeException(exception);
        }
        return res;
    }

    @Override
    public AppSitSettingRes getAppSetting() {
        return settingService.getAppSettings();
    }

    @Override
    public BaseSettled getSettled() {
        BaseSettled settled = settingService.getSettled();
        return settled;
    }

    @Override
    public String querySettingsValueByKey(String key) {
        return settingService.querySettingsValueByKey(key);
    }
}
