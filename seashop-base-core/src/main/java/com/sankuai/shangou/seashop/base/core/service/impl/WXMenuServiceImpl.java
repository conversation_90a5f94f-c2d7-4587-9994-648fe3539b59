package com.sankuai.shangou.seashop.base.core.service.impl;

import com.hishop.starter.storage.client.StorageClient;
import com.hishop.starter.storage.model.StorageFileInfo;
import com.hishop.starter.storage.util.StorageUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.enums.WXMenuLinkTypeEnum;
import com.sankuai.shangou.seashop.base.core.config.LogoHelper;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.core.service.WXMenuService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseWXMenu;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseWXMenuRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWXMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveWXAccountReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.WXAccountResp;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.user.common.constant.CacheConstant;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdCreateQRReq;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WXMenuServiceImpl implements WXMenuService {

    @Value("${weiXin.envVersion:trial}")
    public String envVersion;
    @Resource
    BaseWXMenuRepository baseWXMenuRepository;

    @Resource
    private SiteSettingService siteSettingService;
    @Resource
    private WxMpService wxMpService;
    @Resource
    private StorageClient storageClient;
    @Resource
    private WxMaService wxMaService;

    private static final String WX_APP_ID = "weixinAppletId";
    private static final String WX_MP_APP_ID = "weixinMpAppId";
    private static final String WX_MP_APP_SECRET = "weixinMpAppSecret";
    //小程序无法跳转时的默认地址
    private static final String DEFAULT_URL = "http://mp.weixin.qq.com";
    private static final String DOMAIN_LEY = "domain";

    @Override
    public void saveWXAccount(SaveWXAccountReq saveWXAccountReq) {
        List<BaseSiteSetting> list = siteSettingService.query(Arrays.asList(WX_MP_APP_ID, WX_MP_APP_SECRET));
        List<BaseSiteSetting> settings = new ArrayList<>();
        BaseSiteSetting appletId = new BaseSiteSetting();
        appletId.setId(list.stream().filter(v -> WX_MP_APP_ID.equals(v.getKey())).collect(Collectors.toList()).get(0).getId());
        appletId.setKey(WX_MP_APP_ID);
        appletId.setValue(saveWXAccountReq.getWeixinMpAppId());
        settings.add(appletId);
        BaseSiteSetting appletSecret = new BaseSiteSetting();
        appletSecret.setId(list.stream().filter(v -> WX_MP_APP_SECRET.equals(v.getKey())).collect(Collectors.toList()).get(0).getId());
        appletSecret.setKey(WX_MP_APP_SECRET);
        appletSecret.setValue(saveWXAccountReq.getWeixinMpAppSecret());
        settings.add(appletSecret);
        TransactionHelper.doInTransaction(() -> {
            siteSettingService.saveSettings(settings);
        });
    }

    @Override
    public Integer create(BaseWXMenuReq wxMenu) {
        checkParam(wxMenu);
        BaseWXMenu baseWXMenu = JsonUtil.copy(wxMenu, BaseWXMenu.class);
        baseWXMenu.setCreateTime(new Date());
        baseWXMenu.setUpdateTime(new Date());
        return baseWXMenuRepository.create(baseWXMenu);
    }

    @Override
    public Boolean update(BaseWXMenuReq wxMenu) {
        checkParam(wxMenu);
        BaseWXMenu menu = baseWXMenuRepository.queryWXMenuById(wxMenu.getId());
        AssertUtil.throwIfNull(menu, "菜单不存在");
        BaseWXMenu baseWXMenu = JsonUtil.copy(wxMenu, BaseWXMenu.class);
        return baseWXMenuRepository.update(baseWXMenu);
    }

    @Override
    public Boolean delete(BaseIdReq id) {
        BaseWXMenu menu = baseWXMenuRepository.queryWXMenuById(id.getId());
        AssertUtil.throwIfNull(menu, "菜单不存在");
        return baseWXMenuRepository.delete(id.getId());
    }

    @Override
    public BaseWXMenuRes queryById(BaseIdReq id) {
        BaseWXMenu menu = baseWXMenuRepository.queryWXMenuById(id.getId());
        return JsonUtil.copy(menu, BaseWXMenuRes.class);
    }

    @Override
    public List<BaseWXMenuListRes> queryWXMenus() {
        List<BaseWXMenu> wxMenus = baseWXMenuRepository.queryWXMenus();
        List<BaseWXMenu> topMenus = wxMenus.stream().filter(x -> x.getParentId() == 0).collect(Collectors.toList());
        List<BaseWXMenuListRes> topMenusRes = JsonUtil.copyList(topMenus, BaseWXMenuListRes.class);
        for (BaseWXMenuListRes topMenu : topMenusRes) {
            List<BaseWXMenu> subMenus = wxMenus.stream().filter(x -> x.getParentId() == topMenu.getId()).collect(Collectors.toList());
            List<BaseWXMenuRes> subMenusRes = JsonUtil.copyList(subMenus, BaseWXMenuRes.class);
            topMenu.setSubs(subMenusRes);
        }
        return topMenusRes;
    }

    @Override
    public Boolean syncWechatMenu() {
        try {
            //获取小程序设置 获取公众号设置
            List<BaseSiteSetting> list = siteSettingService.query(Arrays.asList(WX_APP_ID, WX_MP_APP_ID, WX_MP_APP_SECRET, DOMAIN_LEY));
//            list转化未map
            Map<String, String> map = list.stream().collect(Collectors.toMap(BaseSiteSetting::getKey, BaseSiteSetting::getValue));
            String appId = map.get(WX_APP_ID);
            String mpAppId = map.get(WX_MP_APP_ID);
            String mpAppSecret = map.get(WX_MP_APP_SECRET);
            String domain = map.get(DOMAIN_LEY);
//            判断mp配置不能为空
            AssertUtil.throwIfNull(mpAppId, "公众号appId不能为空");
            AssertUtil.throwIfNull(mpAppSecret, "公众号appSecret不能为空");
//            判断和wxMpService的配置是否一致
            if (!mpAppId.equals(wxMpService.getWxMpConfigStorage().getAppId())){
                //刷新配置
                refreshConfig(wxMpService, mpAppId, mpAppSecret);
            }

            //            先删除所有菜单
            wxMpService.getMenuService().menuDelete();
            List<BaseWXMenuListRes> topMenusRes = queryWXMenus();
            List<WxMenuButton> buttons = JsonUtil.copyList(topMenusRes, WxMenuButton.class, (source, target) -> {
                if (source.getSubs() != null && source.getSubs().size() > 0){
                    target.setName(source.getName());
                    target.setSubButtons(JsonUtil.copyList(source.getSubs(), WxMenuButton.class, (source1, target1) -> {
                        setMenu(target1, source1.getLinkType(), source1.getLinkValue(), source1.getName(), appId, domain);
                    }));
                }else {
                    setMenu(target, source.getLinkType(), source.getLinkValue(), source.getName(), appId, domain);
                }
            });
            WxMenu wxMenu = new WxMenu();
            wxMenu.setButtons(buttons);
            wxMpService.getMenuService().menuCreate(wxMenu);
        }catch (Exception e){
            log.error("同步菜单失败",e);
        }
        return true;
    }

    @Override
    public WXAccountResp getWXAccount() {
        List<BaseSiteSetting> list = siteSettingService.query(Arrays.asList(WX_MP_APP_ID, WX_MP_APP_SECRET));
        WXAccountResp wxAccountResp = new WXAccountResp();
        if (list != null && list.size() > 0) {
            wxAccountResp.setWeixinMpAppId(list.stream().filter(v -> WX_MP_APP_ID.equals(v.getKey())).collect(Collectors.toList()).get(0).getValue());
            wxAccountResp.setWeixinMpAppSecret(list.stream().filter(v -> WX_MP_APP_SECRET.equals(v.getKey())).collect(Collectors.toList()).get(0).getValue());
        }
        return wxAccountResp;
    }

    private void setMenu(WxMenuButton target1, Integer linkType, String linkValue, String name, String appId, String domain) {
        if (WXMenuLinkTypeEnum.None.equals(WXMenuLinkTypeEnum.getByCode(linkType))) {
            target1.setType("click");
            target1.setName(name);
            target1.setKey(linkValue);
        } else if (WXMenuLinkTypeEnum.WXH5.equals(WXMenuLinkTypeEnum.getByCode(linkType))) {
            target1.setType("view");
            target1.setName(name);
            target1.setUrl(domain + "/" + linkValue);
        } else {
            target1.setType("miniprogram");
            target1.setUrl(DEFAULT_URL);
            target1.setAppId(appId);
            target1.setName(name);
            target1.setPagePath(linkValue);
        }
    }

    private void checkParam(BaseWXMenuReq wxMenu) {
        AssertUtil.throwIfTrue(StrUtil.isEmpty(wxMenu.getName()), "菜单名称不能为空");
        if (!WXMenuLinkTypeEnum.None.equals(WXMenuLinkTypeEnum.getByCode(wxMenu.getLinkType()))) {
            AssertUtil.throwIfTrue(StrUtil.isEmpty(wxMenu.getLinkValue()), "请选择链接地址");
        }
        if (wxMenu.getParentId() != null && wxMenu.getParentId() > 0) {
            BaseWXMenu parentMenu = baseWXMenuRepository.queryWXMenuById(wxMenu.getParentId());
            AssertUtil.throwIfNull(parentMenu, "父菜单不存在或已被删除");
        }
    }

    public static void refreshConfig(WxMpService wxMpService, String appId, String appSecret) {
        WxMpRedissonConfigImpl configStorage = (WxMpRedissonConfigImpl) wxMpService.getWxMpConfigStorage();
        configStorage.setAppId(appId);
        configStorage.setSecret(appSecret);
        configStorage.expireAccessToken();
    }


    @Override
    public String createQR(CmdCreateQRReq cmdCreateQRReq) {
        return createQR(cmdCreateQRReq, null);
    }

    public String createQR(CmdCreateQRReq cmdCreateQRReq, String logo) {
        try {
            String fileName = LogoHelper.getFileName(cmdCreateQRReq.getPath(), logo);
            String cacheKey = CacheConstant.QR_CODE + fileName;
//            if (squirrelUtil.get(cacheKey) != null) {
//                String httpFileUrl = venusService + squirrelUtil.get(cacheKey).toString();
//                //判断图片是否存在
//                if (LogoHelper.exist(httpFileUrl)) {
//                    log.error("二维码已存在，无需重新生成，直接返回");
//                    return fileName;
//                }
//            }
            //判断图片是否存在
            byte[] file = wxMaService
                .getQrcodeService()
                .createWxaCodeBytes(cmdCreateQRReq.getPath(), envVersion, 430, true, null, false);
            //是否存在logo
            if (StrUtil.isNotEmpty(logo)) {
                file = LogoHelper.changLogo(file, StorageUtil.formatUrl(storageClient.formatHost(), logo));
            }

            StorageFileInfo storageFileInfo = storageClient.putObject(fileName, file, null, null, null);
//            String fileKey = venusService.uploadImage(file, fileName).getFileKey();
//            fileKey = fileKey.replace("/" + bucket + "/", StrUtil.EMPTY);
            return StrUtil.join("/", storageFileInfo.getHost(), storageFileInfo.getKey());
        } catch (WxErrorException e) {
            log.error("创建小程序码失败", e);
            throw new BusinessException(UserResultCodeEnum.CREATE_QR_ERROR);
        }
    }
}
