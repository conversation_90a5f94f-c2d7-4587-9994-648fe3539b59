package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.RefundReasonCmdService;
import com.sankuai.shangou.seashop.base.thrift.core.RefundReasonCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateRefundReasonReq;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:34
 */
@RestController
@RequestMapping("/refundReason")
public class RefundReasonCmdController implements RefundReasonCmdFeign {

    @Resource
    private RefundReasonCmdService refundReasonCmdService;

    @PostMapping(value = "/addRefundReason", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addRefundReason(@RequestBody AddRefundReasonReq addRefundReasonReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addRefundReason", addRefundReasonReq, req -> {
            req.checkParameter();
            refundReasonCmdService.addRefundReason(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateRefundReason", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateRefundReason(@RequestBody UpdateRefundReasonReq cmdRefundReasonReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateRefundReason", cmdRefundReasonReq, req -> {
            req.checkParameter();
            refundReasonCmdService.updateRefundReason(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/deleteRefundReason", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> deleteRefundReason(@RequestBody DeleteRefundReasonReq deleteRefundReasonReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteRefundReason", deleteRefundReasonReq, req -> {
            req.checkParameter();
            refundReasonCmdService.deleteRefundReason(req);
            return new BaseResp();
        });
    }
}
