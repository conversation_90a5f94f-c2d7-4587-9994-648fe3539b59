package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.JobService;
import com.sankuai.shangou.seashop.base.core.task.ReportTask;
import com.sankuai.shangou.seashop.base.dao.core.domain.JobLogInfo;
import com.sankuai.shangou.seashop.base.thrift.core.JobQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JobLogInfoResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/job")
public class JobQueryController implements JobQueryFeign {


    @Resource
    private JobService jobService;

    @PostMapping(value = "/getJobLog", consumes = "application/json")
    @Override
    public ResultDto<JobLogInfoResp> getJobLog(BaseReq query)  {
        return ThriftResponseHelper.responseInvoke("queryTopByCategoryId", query, req -> {
            JobLogInfo jobLog = jobService.getJobLog(req.getId());
            JobLogInfoResp result = JsonUtil.copy(jobLog, JobLogInfoResp.class);
            return result;
        });
    }

    @Resource
    ReportTask reportTask;

    @GetMapping(value = "/reportTask/reportShop")
    public void reportShop() throws TException {
        reportTask.reportShop();
    }

    @GetMapping(value = "/reportTask/reportMember")
    public void reportMember() throws TException {
        reportTask.reportMember();
    }

    @GetMapping(value = "/reportTask/reportFavoriteProduct")
    public void reportFavoriteProduct() throws TException {
        reportTask.reportFavoriteProduct();
    }

}
