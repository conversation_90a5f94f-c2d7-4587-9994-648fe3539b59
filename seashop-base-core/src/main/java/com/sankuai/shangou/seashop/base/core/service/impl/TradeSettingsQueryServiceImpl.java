package com.sankuai.shangou.seashop.base.core.service.impl;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.core.service.TradeSettingsQueryService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.thrift.core.response.TradeSiteSettingsResp;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author： liweisong
 * @create： 2023/11/22 17:30
 */
@Service
@Slf4j
public class TradeSettingsQueryServiceImpl implements TradeSettingsQueryService {

    @Resource
    private SiteSettingService siteSettingService;

    @Override
    public TradeSiteSettingsResp queryTradeSiteSetting() {
        TradeSiteSettingsResp result = new TradeSiteSettingsResp();
        Field[] fields = result.getClass().getDeclaredFields();
        List<String> settingKeys = new ArrayList<>();
        for (Field field : fields) {
            String name = field.getName();
            settingKeys.add(name);
        }
        //根据keys 获取属性值
        List<BaseSiteSetting> list = siteSettingService.query(settingKeys);
        if(CollectionUtil.isEmpty(list)){
            return result;
        }
        Map<String, String> map = list.stream().collect(Collectors.toMap(BaseSiteSetting::getKey, BaseSiteSetting::getValue, (o1, o2) -> o2));

        return JsonUtil.copy(map,TradeSiteSettingsResp.class);
    }
}
