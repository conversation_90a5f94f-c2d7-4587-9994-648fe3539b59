package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.PlatformTaskService;
import com.sankuai.shangou.seashop.base.thrift.core.PlatformTaskQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.dto.PlatformTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.task.PlatformTaskListResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/platformTask")
public class PlatformTaskQueryController implements PlatformTaskQueryFeign {

    @Resource
    private PlatformTaskService platformTaskService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<PlatformTaskDto>> pageList(@RequestBody QueryTaskReq queryReq) throws TException {
        log.info("【平台后台任务】查询分页列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
        return ThriftResponseHelper.responseInvoke("pageList", queryReq, func -> {
            queryReq.checkParameter();
            // 业务逻辑处理
            return platformTaskService.pageList(queryReq);
        });
    }

    @PostMapping(value = "/queryList", consumes = "application/json")
    @Override
    public ResultDto<PlatformTaskListResp> queryList(@RequestBody QueryTaskReq queryReq) throws TException {
        log.info("【平台后台任务】查询任务列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
        return ThriftResponseHelper.responseInvoke("queryList", queryReq, func -> {
            // 业务逻辑处理
            List<PlatformTaskDto> list = platformTaskService.queryList(queryReq);
            return new PlatformTaskListResp(list);
        });
    }
}
