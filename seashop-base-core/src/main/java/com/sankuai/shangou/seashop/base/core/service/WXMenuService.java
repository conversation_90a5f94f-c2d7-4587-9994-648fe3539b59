package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWXMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveWXAccountReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.WXAccountResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdCreateQRReq;

import java.util.List;

public interface WXMenuService {

    void saveWXAccount(SaveWXAccountReq saveWXAccountReq);
    Integer create(BaseWXMenuReq wxMenu);

    Boolean update(BaseWXMenuReq wxMenu);

    Boolean delete(BaseIdReq id);

    List<BaseWXMenuListRes> queryWXMenus();

    BaseWXMenuRes queryById(BaseIdReq id);

    Boolean syncWechatMenu();

    WXAccountResp getWXAccount();

    String createQR(CmdCreateQRReq cmdCreateQRReq);
}
