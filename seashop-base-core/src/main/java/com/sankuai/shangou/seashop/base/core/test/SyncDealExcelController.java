package com.sankuai.shangou.seashop.base.core.test;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 10:15
 */
@Slf4j
@RestController
@RequestMapping("/lws/base")
public class SyncDealExcelController {

    @GetMapping(value = "/data/excel")
    public List<String> dealExcel(@RequestParam Integer sheetNum) throws TException {
        log.info("--------------------------------开始处理excel-------------------------------------");
        SheetNumEnum sheetNumEnum = SheetNumEnum.getSheetNumEnum(sheetNum);
        List<String> result = new ArrayList<>();
        try (FileInputStream file = new FileInputStream("D:/数据迁移/数据迁移的数据表格20240126.xlsx")) {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook(file);
            // 选择工作表（假设是第一个工作表） sheetNum 从0 开始
            Sheet sheet = workbook.getSheetAt(sheetNum);
            List<DataSourceTarget> list = this.getDataSourceTarget(sheet);
            // 过滤掉目标数据源为空的数据
            list = list.stream().filter(dataSourceTarget -> !StringUtils.isEmpty(dataSourceTarget.getTargetTable())).collect(Collectors.toList());
            Map<String, List<DataSourceTarget>> dataMap = list.stream().collect(Collectors.groupingBy(DataSourceTarget::getTargetTable));
            dataMap.entrySet().forEach(entry -> {
                List<DataSourceTarget> value = entry.getValue();
                // 来源表如果没值说明是新增的表，不需要同步数据
                String sourceTable = value.get(0).getSourceTable();
                if(sourceTable == null || sourceTable.trim().length() == 0){
                    return;
                }
                String targetTable = value.get(0).getTargetTable();
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("insert into ").append("`").append(sheetNumEnum.targetSchema).append("`.`").append(targetTable).append("`").append("(");
                for(DataSourceTarget dataSourceTarget : value){
                    String sourceField = dataSourceTarget.getSourceField();
                    String targetField = dataSourceTarget.getTargetField();
                    if(StringUtils.isBlank(sourceField) || StringUtils.isBlank(targetField)){
                        continue;
                    }
                    stringBuffer.append("`").append(targetField).append("`").append(",");
                }
                // 删除最后一个字符逗号
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                stringBuffer.append(") select ");
                for(DataSourceTarget dataSourceTarget : value){
                    String sourceField = dataSourceTarget.getSourceField();
                    String targetField = dataSourceTarget.getTargetField();
                    if(StringUtils.isBlank(sourceField) || StringUtils.isBlank(targetField)){
                        continue;
                    }
                    stringBuffer.append("`").append(sourceField).append("`").append(",");
                }
                // 删除最后一个字符逗号
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                stringBuffer.append(" from ").append("`").append(sheetNumEnum.sourceSchema).append("`.`").append(sourceTable).append("`").append(";");

                result.add(stringBuffer.toString());
            });
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    @GetMapping(value = "/data/count")
    public String dealCount(@RequestParam Integer sheetNum) throws TException {
        log.info("--------------------------------开始处理excel-------------------------------------");
        StringBuffer stringBuffer = new StringBuffer();
        SheetNumEnum sheetNumEnum = SheetNumEnum.getSheetNumEnum(sheetNum);
        try (FileInputStream file = new FileInputStream("D:/数据迁移/数据迁移的数据表格0325.xlsx")) {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook(file);
            // 选择工作表（假设是第一个工作表） sheetNum 从0 开始
            Sheet sheet = workbook.getSheetAt(sheetNum);
            List<DataSourceTarget> list = this.getDataSourceTarget(sheet);
            // 过滤掉目标数据源为空的数据
            list = list.stream().filter(dataSourceTarget -> !StringUtils.isEmpty(dataSourceTarget.getTargetTable())).collect(Collectors.toList());
            Map<String, List<DataSourceTarget>> dataMap = list.stream().collect(Collectors.groupingBy(DataSourceTarget::getTargetTable));
            dataMap.entrySet().forEach(entry -> {
                List<DataSourceTarget> value = entry.getValue();
                // 来源表如果没值说明是新增的表，不需要同步数据
                String sourceTable = value.get(0).getSourceTable();
                if(sourceTable == null || sourceTable.trim().length() == 0){
                    return;
                }

                String targetTable = value.get(0).getTargetTable();
                stringBuffer.append("SELECT ")
                        .append("'").append(sheetNumEnum.model).append("' AS model, ")
                        .append("'").append(targetTable).append("' as targetTable, ")
                        .append("( SELECT count(*) FROM `").append(sheetNumEnum.targetSchema).append("`.`").append(targetTable).append("`) ").append(" AS targetNum, ")
                        .append("'").append(sourceTable).append("' AS sourceTable, ")
                        .append("( SELECT count(*) FROM `").append(sheetNumEnum.sourceSchema).append("`.`").append(sourceTable).append("`) ").append(" AS sourceNum ")
                        .append(" UNION ALL ");
            });
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return stringBuffer.toString();
    }

    private List<DataSourceTarget> getDataSourceTarget(Sheet sheet){
        List<DataSourceTarget> list = new ArrayList<>();
        // 迭代行
        int rowNum = 0;
        for (Row row : sheet) {
            rowNum ++;
            if(rowNum < 3){
                // 前面两行不用处理
                continue;
            }
            // 迭代单元格
            int tableNum = 0;
            DataSourceTarget dataInfo = new DataSourceTarget();
            for (Cell cell : row) {
                tableNum ++;
                if(tableNum == 4){
                    String targetTableName = getMergedCellValue(sheet, cell);
                    log.info("目标表名：{}",targetTableName);
                    dataInfo.setTargetTable(targetTableName);
                }
                if(tableNum == 5){
                    String sourceTableName = getMergedCellValue(sheet, cell);
                    log.info("源表名：{}",sourceTableName);
                    dataInfo.setSourceTable(sourceTableName);
                }
                if(tableNum == 7){
                    String targetTableColumn = getMergedCellValue(sheet, cell);
                    log.info("目标表字段：{}",targetTableColumn);
                    dataInfo.setTargetField(targetTableColumn);
                }
                if(tableNum == 8){
                    String sourceTableColumn = getMergedCellValue(sheet, cell);
                    log.info("源表字段：{}",sourceTableColumn);
                    dataInfo.setSourceField(sourceTableColumn);
                }
            }
            if(!Objects.isNull(dataInfo)){
                list.add(dataInfo);
            }
        }
        return list;
    }

    private List<Map<String,Object>> getTables(List<DataSourceTarget> list){
        Map<String, List<DataSourceTarget>> dataMap = list.stream().collect(Collectors.groupingBy(DataSourceTarget::getTargetTable));
        List<Map<String,Object>> tables = new ArrayList<>();
        dataMap.entrySet().forEach(entry -> {
            Map<String,Object> table = new HashMap<>();
            List<DataSourceTarget> value = entry.getValue();
            Map<String,String> source = new HashMap<>();
            String sourceTable = value.get(0).getSourceTable();
            if(sourceTable == null || sourceTable.trim().length() == 0){
                return;
            }
            source.put("Name",sourceTable);
            source.put("ObjectType", "Table_MYSQL");
            table.put("Source", source);
            Map<String,String> target = new HashMap<>();
            String targetTable = value.get(0).getTargetTable();
            target.put("Name",targetTable);
            target.put("ObjectType", "Table_MYSQL");
            table.put("Target", target);
            table.put("Keys", new ArrayList<>());

            List<Map<String,Object>> fields = new ArrayList<>();
            for(DataSourceTarget dataSourceTarget : value){
                Map<String,Object> field = new HashMap<>();
                Map<String,String> sourceFiled = new HashMap<>();
                sourceFiled.put("Name", dataSourceTarget.getSourceField());
                sourceFiled.put("ObjectType", "TableField_MYSQL");
                field.put("Source", sourceFiled);
                Map<String,String> targetFiled = new HashMap<>();
                targetFiled.put("Name", dataSourceTarget.getTargetField());
                targetFiled.put("ObjectType", "TableField_MYSQL");
                field.put("Target", targetFiled);
                fields.add(field);
            }
            table.put("Fields", fields);
            tables.add(table);
        });
        return tables;
    }

    private String getMergedCellValue(Sheet sheet, Cell cell) {
        // 获取单元格的行索引和列索引
        int rowIndex = cell.getRowIndex();
        int columnIndex = cell.getColumnIndex();

        // 遍历合并单元格区域
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);

            // 检查单元格是否在合并单元格区域内
            if (rowIndex >= mergedRegion.getFirstRow() && rowIndex <= mergedRegion.getLastRow()
                    && columnIndex >= mergedRegion.getFirstColumn() && columnIndex <= mergedRegion.getLastColumn()) {
                // 返回合并单元格的值
                return sheet.getRow(mergedRegion.getFirstRow()).getCell(mergedRegion.getFirstColumn()).getStringCellValue();
            }
        }

        // 如果不在任何合并单元格内，返回单元格的值
        return cell.getStringCellValue();
    }

    private Map<String,Object> dealResponse(SheetNumEnum sheetNumEnum) {
        Map<String,Object> map = new HashMap<>();
        map.put("Version", 1.1);
        Map<String,Object> general = new HashMap<>();
        general.put("ContinueOnError", false);
        general.put("IncludeDelete", true);
        general.put("IncludeInsert", true);
        general.put("IncludeUpdate", true);
        general.put("RunMultipleSQL", false);
        general.put("ShowDetails", true);
        general.put("SourceCatalog", "");
        general.put("SourceProject", "");
        general.put("SourceProjectOwnerNavicatID", "");
        general.put("SourceSchema", sheetNumEnum.getSourceSchema());
        general.put("SourceServer", sheetNumEnum.getSourceServer());
        general.put("SourceServerType", "MYSQL");
        general.put("TargetCatalog", "");
        general.put("TargetProject", "");
        general.put("TargetProjectOwnerNavicatID", "");
        general.put("TargetSchema", sheetNumEnum.getTargetSchema());
        general.put("TargetServer", sheetNumEnum.getTargetServer());
        general.put("TargetServerType", "MYSQL");
        general.put("UseTransaction", false);
        map.put("General",general);
        return map;
    }

    public enum SheetNumEnum {
        PROMOTION(0, "sgb2b", "数据迁移","shangou_sgb2b_seashop_promotion","数据迁移","promotion"),
        BASE(1, "sgb2b", "数据迁移","shangou_sgb2b_seashop_base","数据迁移","base"),
        USER(2, "sgb2b", "数据迁移","shangou_sgb2b_seashop_user","数据迁移","user"),
        PRODUCT(3, "sgb2b", "数据迁移","shangou_sgb2b_seashop_productx","数据迁移","productx"),
        ORDER(4, "sgb2b", "数据迁移","shangou_sgb2b_seashop_order","数据迁移","order"),
        ;

        private final Integer sheetNum;
        private final String sourceSchema;
        private final String sourceServer;
        private final String targetSchema;
        private final String targetServer;
        private final String model;

        SheetNumEnum(Integer sheetNum, String sourceSchema, String sourceServer, String targetSchema, String targetServer, String model) {
            this.sheetNum = sheetNum;
            this.sourceSchema = sourceSchema;
            this.sourceServer = sourceServer;
            this.targetSchema = targetSchema;
            this.targetServer = targetServer;
            this.model = model;
        }

        public Integer getSheetNum() {
            return sheetNum;
        }

        public String getSourceSchema() {
            return sourceSchema;
        }

        public String getSourceServer() {
            return sourceServer;
        }

        public String getTargetSchema() {
            return targetSchema;
        }

        public String getTargetServer() {
            return targetServer;
        }

        public String getModel() {
            return model;
        }

        public static SheetNumEnum getSheetNumEnum(Integer sheetNum) {
            for (SheetNumEnum sheetNumEnum : SheetNumEnum.values()) {
                if (sheetNumEnum.getSheetNum().equals(sheetNum)) {
                    return sheetNumEnum;
                }
            }
            return null;
        }
    }

}
