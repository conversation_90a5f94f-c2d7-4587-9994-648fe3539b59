package com.sankuai.shangou.seashop.base.core.mq.publisher;

import javax.annotation.Resource;

import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.mq.model.CustomFormEvent;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import com.sankuai.shangou.seashop.user.common.constant.MafkaConst;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/12/02 9:45
 */
@Slf4j
@Service
public class CustomFormPublisher {

    @Resource
    private DefaultRocketMq defaultRocketMq;

    public void sendEvent(CustomFormEvent event) {
        if (CollUtil.isEmpty(event.getFormIds())) {
            log.warn("[自定义表单] 变动, formIds 不能为空");
            return;
        }

        // 判断当前是否存在事务，如果存在事务，则在事务提交后发送消息，否则直接发送, 发送消息的方法: actualSendEvent
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    actualSendEvent(event);
                }
            });
        }
        else {
            actualSendEvent(event);
        }
    }

    private void actualSendEvent(CustomFormEvent event) {
        String body = JsonUtil.toJsonString(event);
        log.info("[自定义表单] 发送自定义表单变动事件, body: {}", body);
        try {
            SendResult sendResult = defaultRocketMq.syncSend(MafkaConst.TOPIC_CUSTOM_FORM_CHANGE, body);
            log.info("[自定义表单] 发送自定义表单变动事件, result: {}", sendResult);
            AssertUtil.throwIfTrue(!SendStatus.SEND_OK.equals(sendResult.getSendStatus()), "发送自定义表单变动事件失败");
            log.info("[自定义表单] 发送自定义表单变动事件成功, body: {}", body);
        } catch (Exception e) {
            log.error("[自定义表单] 发送自定义表单变动事件失败, body: {}", body, e);
        }
    }

}
