package com.sankuai.shangou.seashop.base.core.thrift.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.hishop.starter.sms.SmsClient;
import com.hishop.starter.sms.model.SendSmsParam;
import com.hishop.starter.sms.model.SmsAccountParam;
import com.hishop.starter.util.json.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.common.config.HimallConfigProperties;
import com.sankuai.shangou.seashop.base.common.config.SmsTemplate;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseSiteSettingRepository;
import com.sankuai.shangou.seashop.base.thrift.core.MessageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.ContactReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.EmailBodyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SmsBodyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.SmsSettingRes;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

//服务合并，命名冲突，在类前面增加一个old，后续在处理 by chenpeng
@RestController
@RequestMapping("/message")
public class OldMessageCMDController implements MessageCMDFeign {


    @Resource
    private HimallConfigProperties himallConfigProperties;

    @Resource
    private SiteSettingService siteSettingService;

    @Resource
    private BaseSiteSettingRepository siteSettingRepository;
    @Resource
    private SmsClient smsClient;
    //    是否真正发送
    @Value("${himall.sms.real.send:false}")
    private String realSend;

    @PostMapping(value = "/sendEmail", consumes = "application/json")
    @Override
    public ResultDto<Boolean> sendEmail(@RequestBody EmailBodyReq emailBodyReq) throws TException {
//        return ThriftResponseHelper.responseInvoke("sendEmail", emailBodyReq, req -> {
//            EmailBody body = JsonUtil.copy(req, EmailBody.class);
//            messageFacade.sendEmail(body);
//            return true;
//        });
        return ResultDto.newWithData(true);
    }

    @PostMapping(value = "/sendSms", consumes = "application/json")
    @Override
    public ResultDto<Boolean> sendSms(@RequestBody SmsBodyReq smsBodyReq) {
        return ThriftResponseHelper.responseInvoke("sendSms", smsBodyReq, req -> {
            SmsSettingRes settingRes = siteSettingService.querySmsSetting();
            if (settingRes == null) {
                throw new BusinessException("请设置短信秘钥相关");
            }
            // 根据模板
            Pair<Integer, String> smsTemplateByTemplateId = getSmsTemplateByTemplateId(smsBodyReq.getTemplateId(), smsBodyReq.getParam());
            if (smsTemplateByTemplateId == null) {
                throw new BusinessException("没有找到合适的短信模板");
            }
            //查询配置
            SmsAccountParam smsAccountParam = new SmsAccountParam();
            smsAccountParam.setAppKey(settingRes.getSmsAppKey());
            smsAccountParam.setAppSecret(settingRes.getSmsAppSecret());
            SendSmsParam sendSmsParam = new SendSmsParam();
            sendSmsParam.setMobiles(smsBodyReq.getContactList().stream().map(ContactReq::getMobile).collect(Collectors.joining(",")));

            sendSmsParam.setSmsChannel(smsTemplateByTemplateId.getKey());
            sendSmsParam.setText(smsTemplateByTemplateId.getValue());
            //todo 发送短信暂时注释
            if (StrUtil.equalsIgnoreCase(realSend, "false")) {
                return true;
            }
            smsClient.send(smsAccountParam, sendSmsParam);
            return true;
        });
    }

    @GetMapping(value = "/querySmsBalance")
    @Override
    public ResultDto<Long> querySmsBalance() {
        return ThriftResponseHelper.responseInvoke("querySmsBalance", null, req -> {
            SmsSettingRes settingRes = siteSettingService.querySmsSetting();
            if (settingRes == null) {
                return -1L;
            }
            //查询配置
            SmsAccountParam smsAccountParam = new SmsAccountParam();
            smsAccountParam.setAppKey(settingRes.getSmsAppKey());
            smsAccountParam.setAppSecret(settingRes.getSmsAppSecret());
            return smsClient.queryBalance(smsAccountParam);
        });
    }


    private Pair<Integer, String> getSmsTemplateByTemplateId(Long templateId, String param) {
        Map<String, SmsTemplate> templates = himallConfigProperties.getSms().getTemplates();
        if (templateId == null || CollectionUtil.isEmpty(templates)) {
            return null;
        }
        SmsTemplate smsTemplate = templates.get(templateId.toString());
        if (smsTemplate == null) {
            return null;
        }
        Map map = JsonUtil.parseObject(Optional.ofNullable(param).orElse("{}"), Map.class);
        BaseSiteSetting setting = siteSettingRepository.querySettingsValueByKey("siteName");
        map.put("siteName", setting.getValue());
        return Pair.of(smsTemplate.getSmsChannel().getCode(), StrUtil.format(smsTemplate.getContent(), map));
    }
}
