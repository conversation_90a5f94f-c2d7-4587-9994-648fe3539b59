package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:19
 */
public interface MsgTemplateCmdService {

    /**
     * 保存消息配置
     * @param saveMsgTemplateReq
     */
    void saveMsgTemplate(SaveMsgTemplateReq saveMsgTemplateReq);

    /**
     *
     * @param request
     */
    void saveMsgTemplateApplet(MsgTemplateAppletReq request);

    /**
     *
     * @param request
     */
    void saveMsgTemplateData(MsgTemplateDataReq request);

    /**
     * 获取模版ID
     */
    void getAppletSubscribeTmplate();

    /**
     * 小程序发送通知
     * @param req
     */
    void sendAppletMessageByTemplate(SendAppletMessageReq req);

    /**
     *
     * @param cmdWxAppletFormDataReq
     */
    void insertWxAppletFormData(CmdWxAppletFormDataReq cmdWxAppletFormDataReq);

    /**
     * 小程序发送通知再包装
     * @param sendAppletReq
     */
    void sendAppletMessage(SendAppletReq sendAppletReq);

    String getAppletResetToken();

    BaseResp bindOpenId(BindOpenIdReq req);
}
