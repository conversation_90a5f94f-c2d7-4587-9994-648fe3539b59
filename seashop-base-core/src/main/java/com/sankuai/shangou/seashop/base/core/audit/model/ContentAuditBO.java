package com.sankuai.shangou.seashop.base.core.audit.model;

import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class ContentAuditBO {

    /**
     * 对海商送审类型：
     */
    private String type;

    /**
     * 内容的生产来源方(接口传数字枚举值),1 商户 4 运营
     */
    private String dataSource;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商户门店ID.
     */
    private Long shopId;

    /**
     * 内容在平台上产生的时间。
     */
    private String datetime;

    /**
     * 每次送审请求事务的唯一标识，全局唯一，业务方负责维护。
     */
    private String transId;

    /**
     * 内容的标识。
     * 业务方可酌情定义，表示本次送审内容的唯一标识，如：图片ID，评价ID等。
     */
    private Long bizId;

    /**
     * 内容生产者发布内容时，内容生产者的ip.
     */
    private String userIp;

    /**
     * 业务方透传数据，json字符串, 长度不超过200字符，如非必要，请勿滥用
     */
    private String bizData;

    /**
     * 1、文本，2、图片，3、视频
     */
    private Integer businessType;

    /**
     * 文本的送审, ( 不需要审核的不要放进改字段中。)（议送审list的size不要超过20）
     */
    private List<Map<String, Object>> auditInfo;

    /**
     * Json格式，业务定制化策略所需的字段，以kv形式存放在该字段中，不要传""，默认为null就好
     */
    private String extend;

    /**
     * json形式，是外卖风控需要的参数,默认"{}"
     */
    private String riskParams;
}
