package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.enums.BaseTemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.common.util.template.TemplateStorageService;
import com.sankuai.shangou.seashop.base.core.service.BaseTemplatePageService;
import com.sankuai.shangou.seashop.base.core.service.TopicService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.thrift.core.TopicCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ClientTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicJsonFileReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.TemplatePageIndexReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseShopReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;

@RestController
@RequestMapping("/topic")
public class TopicCMDController implements TopicCMDFeign {


    @Resource
    private TopicService topicService;

    @Resource
    private TemplateStorageService storageService;

    @Resource
    private S3plusStorageService s3plusStorageService;

    @Resource
    private BaseTemplatePageService templatePageService;

    @PostMapping(value = "/create", consumes = "application/json")
    @Override
    public ResultDto<Long> create(@RequestBody BaseTopicReq query) throws TException {
        query.checkParameter();
        return ThriftResponseHelper.responseInvoke("create", query, req -> {

            Long id = topicService.create(req);
            return id;
        });
    }

    @PostMapping(value = "/update", consumes = "application/json")
    @Override
    public ResultDto<Boolean> update(@RequestBody BaseTopicReq query) throws TException {
        query.checkParameter();
        return ThriftResponseHelper.responseInvoke("update", query, req -> {

            topicService.update(req);
            return true;
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @Override
    public ResultDto<Boolean> delete(@RequestBody BaseShopReq shopReq) throws TException {
        shopReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("delete", shopReq, req -> {
            topicService.delete(shopReq);
            return true;
        });
    }

    @PostMapping(value = "/setHome", consumes = "application/json")
    @Override
    public ResultDto<Boolean> setHome(@RequestBody BaseShopReq shopReq) throws TException {
        shopReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("delete", shopReq, req -> {
            topicService.setHome(shopReq);
            return true;
        });
    }


    @PostMapping(value = "/uploadTemplate", consumes = "application/json")
    @Override
    public ResultDto<String> uploadTemplate(@RequestBody BaseTopicJsonFileReq jsonFileReq) throws TException {
        jsonFileReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("uploadTemplate", jsonFileReq, req -> {
            String filePath = storageService.getTemplatePath(req.getClient(), BaseTemplateClientTypeEnum.getByCode(req.getType()), req.getShopId());
            String dataPath = filePath + "data/default.json";
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] strToBytes = req.getJsonContent().getBytes();
            try {
                byteArrayOutputStream.write(strToBytes);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            s3plusStorageService.sendFileStream2S3ToFilePath(byteArrayOutputStream, dataPath);
            return dataPath;
        });
    }

    @PostMapping(value = "/editSellerIndex", consumes = "application/json")
    @Override
    public ResultDto<Boolean> editSellerIndex(@RequestBody TemplatePageIndexReq req) throws TException {

        req.checkParameter();
        return ThriftResponseHelper.responseInvoke("uploadTemplate", req, request -> {
            JsonNode node = JsonUtil.parseObject(request.getContent(), JsonNode.class);

            BaseTemplatePageWithBLOBs templatePageWithBLOBs = new BaseTemplatePageWithBLOBs();

            templatePageWithBLOBs.setjPage(node.get("Page") == null ? "{}" : JsonUtil.toJsonString(node.get("Page")));
            templatePageWithBLOBs.setlModules(node.get("LModules") == null ? "{}" : JsonUtil.toJsonString(node.get("LModules")));
            templatePageWithBLOBs.setpModules(node.get("PModules") == null ? "{}" : JsonUtil.toJsonString(node.get("PModules")));
            templatePageWithBLOBs.setClient(ClientTypeEnum.Default.getCode());
            templatePageWithBLOBs.setType(request.getType());
            templatePageWithBLOBs.setShopId(request.getShopId());
            templatePageWithBLOBs.setvShopId(0L);
            templatePageWithBLOBs.setUpdateTime(new Date());
            templatePageService.editShopIndex(templatePageWithBLOBs);
            return true;
        });

    }


}
