package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.core.service.MobileFootCmdService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseMobileFootMenu;
import com.sankuai.shangou.seashop.base.dao.core.repository.MobileFootExtRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.MobileFootRepository;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddOrUpdateFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteFootMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveFootMenusReq;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;

import cn.hutool.core.collection.CollUtil;

/**
 * @author： liweisong
 * @create： 2023/11/29 13:46
 */
@Service
public class MobileFootCmdServiceImpl implements MobileFootCmdService {

    @Resource
    private MobileFootRepository mobileFootRepository;
    @Resource
    private MobileFootExtRepository mobileFootExtRepository;

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" , actionName = "底部导航栏-新增或者编辑",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "mobileFootRepository", serviceMethod = "addOrUpdateFootMenus",
            dto = AddOrUpdateFootMenusReq.class, entity = BaseMobileFootMenu.class)
    public void addOrUpdateFootMenus(AddOrUpdateFootMenusReq addOrUpdateFootMenusReq) {
        AssertUtil.throwIfTrue(StringUtils.isEmpty(addOrUpdateFootMenusReq.getName()) || addOrUpdateFootMenusReq.getName().length() > 4, "标题不能为空，且必须在4个字符以内");
        BaseMobileFootMenu menu = mobileFootRepository.selectByNameAndType(addOrUpdateFootMenusReq.getName(), addOrUpdateFootMenusReq.getType());
        AssertUtil.throwIfTrue(menu != null && !menu.getId().equals(addOrUpdateFootMenusReq.getId()), "标题已存在");
        BaseMobileFootMenu param = new BaseMobileFootMenu();
        param.setShopId(addOrUpdateFootMenusReq.getShopId());
        param.setType(addOrUpdateFootMenusReq.getType());
        List<BaseMobileFootMenu> result = mobileFootRepository.selectList(param);
        AssertUtil.throwIfTrue(result.size() > 5, "底部菜单最多5个");
        BaseMobileFootMenu baseMobileFootMenu = new BaseMobileFootMenu();
        baseMobileFootMenu.setId(addOrUpdateFootMenusReq.getId());
        baseMobileFootMenu.setName(addOrUpdateFootMenusReq.getName());
        baseMobileFootMenu.setUrl(addOrUpdateFootMenusReq.getUrl());
        baseMobileFootMenu.setMenuIcon(addOrUpdateFootMenusReq.getMenuIcon());
        baseMobileFootMenu.setMenuIconSel(addOrUpdateFootMenusReq.getMenuIconSel());
        baseMobileFootMenu.setType(addOrUpdateFootMenusReq.getType());
        baseMobileFootMenu.setShopId(addOrUpdateFootMenusReq.getShopId());
        baseMobileFootMenu.setUpdateTime(new Date());
        if(addOrUpdateFootMenusReq.getId() != null){
            // 更新
            baseMobileFootMenu.setCreateTime(addOrUpdateFootMenusReq.getCreateTime());
            mobileFootRepository.update(baseMobileFootMenu);
        } else {
            // 新增
            baseMobileFootMenu.setCreateTime(new Date());
            mobileFootRepository.insert(baseMobileFootMenu);
        }
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" , actionName = "底部导航栏删除接口",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MOVE, repository = "mobileFootRepository",
            serviceMethod = "deleteFootMenu", dto = DeleteFootMenuReq.class, entity = BaseMobileFootMenu.class)
    public void deleteFootMenu(DeleteFootMenuReq deleteFootMenuReq) {
        mobileFootRepository.deleteById(deleteFootMenuReq.getId());
    }

    @Override
    public void saveFootMenus(SaveFootMenusReq saveFootMenusReq) {
        List<BaseMobileFootMenu> menuList = saveFootMenusReq.getFootMenuList()
                .stream().map(this::convertToBaseMobileFootMenu)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(menuList)) {
            return;
        }

        TransactionHelper.doInTransaction(() -> {
            mobileFootExtRepository.remove(null);
            mobileFootExtRepository.saveBatch(menuList);
        });
    }

    private BaseMobileFootMenu convertToBaseMobileFootMenu(AddOrUpdateFootMenusReq addOrUpdateFootMenusReq) {
        if (addOrUpdateFootMenusReq == null) {
            return null;
        }

        BaseMobileFootMenu baseMobileFootMenu = new BaseMobileFootMenu();
        baseMobileFootMenu.setName(addOrUpdateFootMenusReq.getName());
        baseMobileFootMenu.setUrl(addOrUpdateFootMenusReq.getUrl());
        baseMobileFootMenu.setUrlName(addOrUpdateFootMenusReq.getUrlName());
        baseMobileFootMenu.setMenuIcon(addOrUpdateFootMenusReq.getMenuIcon());
        baseMobileFootMenu.setMenuIconSel(addOrUpdateFootMenusReq.getMenuIconSel());
        baseMobileFootMenu.setType(addOrUpdateFootMenusReq.getType());
        baseMobileFootMenu.setShopId(addOrUpdateFootMenusReq.getShopId());
        return baseMobileFootMenu;
    }
}
