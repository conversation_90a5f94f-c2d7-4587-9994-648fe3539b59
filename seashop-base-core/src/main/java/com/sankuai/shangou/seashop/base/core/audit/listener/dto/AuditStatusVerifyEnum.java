package com.sankuai.shangou.seashop.base.core.audit.listener.dto;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/05/13
 */
@Getter
public enum AuditStatusVerifyEnum {
    //351,352,353,354,355,357,358,359,360,361,362,363,365,366,367,368,370,371,372,373,375,376,377,378,379,159,87,155,156,183
    FORBID_SALE_GOODS_351(351, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_352(352, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_353(353, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_354(354, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_355(355, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_357(357, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_358(358, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_359(359, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_360(360, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_361(361, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_362(362, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_363(363, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_365(365, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_366(366, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_367(368, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_370(370, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_371(371, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_372(372, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_373(373, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_375(375, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_376(376, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_377(377, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_378(378, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_379(379, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_159(159, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_87(87, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_155(155, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_156(156, AuditStatusDescEnum.FORBID_SALE_GOODS),
    FORBID_SALE_GOODS_183(183, AuditStatusDescEnum.FORBID_SALE_GOODS),

    //42,55,56,184,88,89,90,91
    ILLEGAL_AD_42(42, AuditStatusDescEnum.ILLEGAL_AD),
    ILLEGAL_AD_55(55, AuditStatusDescEnum.ILLEGAL_AD),
    ILLEGAL_AD_56(56, AuditStatusDescEnum.ILLEGAL_AD),
    ILLEGAL_AD_184(184, AuditStatusDescEnum.ILLEGAL_AD),
    ILLEGAL_AD_89(89, AuditStatusDescEnum.ILLEGAL_AD),
    ILLEGAL_AD_90(90, AuditStatusDescEnum.ILLEGAL_AD),
    ILLEGAL_AD_91(91, AuditStatusDescEnum.ILLEGAL_AD),

    //36,38,39,40,181,84,85,86
    ILLEGAL_TERROR_36(36, AuditStatusDescEnum.ILLEGAL_TERROR),
    ILLEGAL_TERROR_38(38, AuditStatusDescEnum.ILLEGAL_TERROR),
    ILLEGAL_TERROR_39(39, AuditStatusDescEnum.ILLEGAL_TERROR),
    ILLEGAL_TERROR_40(40, AuditStatusDescEnum.ILLEGAL_TERROR),
    ILLEGAL_TERROR_181(181, AuditStatusDescEnum.ILLEGAL_TERROR),
    ILLEGAL_TERROR_84(84, AuditStatusDescEnum.ILLEGAL_TERROR),
    ILLEGAL_TERROR_85(85, AuditStatusDescEnum.ILLEGAL_TERROR),
    ILLEGAL_TERROR_86(86, AuditStatusDescEnum.ILLEGAL_TERROR),

    // 57,58,59,136,180,82,83,60
    ILLEGAL_POLITICS_57(57, AuditStatusDescEnum.ILLEGAL_POLITICS),
    ILLEGAL_POLITICS_58(58, AuditStatusDescEnum.ILLEGAL_POLITICS),
    ILLEGAL_POLITICS_59(59, AuditStatusDescEnum.ILLEGAL_POLITICS),
    ILLEGAL_POLITICS_136(136, AuditStatusDescEnum.ILLEGAL_POLITICS),
    ILLEGAL_POLITICS_180(180, AuditStatusDescEnum.ILLEGAL_POLITICS),
    ILLEGAL_POLITICS_82(82, AuditStatusDescEnum.ILLEGAL_POLITICS),
    ILLEGAL_POLITICS_83(83, AuditStatusDescEnum.ILLEGAL_POLITICS),
    ILLEGAL_POLITICS_60(60, AuditStatusDescEnum.ILLEGAL_POLITICS),

    //92,93,76,71,94,122
    MORE_INVALID_WORD_92(92, AuditStatusDescEnum.MORE_INVALID_WORD),
    MORE_INVALID_WORD_93(93, AuditStatusDescEnum.MORE_INVALID_WORD),
    MORE_INVALID_WORD_76(76, AuditStatusDescEnum.MORE_INVALID_WORD),
    MORE_INVALID_WORD_71(71, AuditStatusDescEnum.MORE_INVALID_WORD),
    MORE_INVALID_WORD_94(94, AuditStatusDescEnum.MORE_INVALID_WORD),
    MORE_INVALID_WORD_122(122, AuditStatusDescEnum.MORE_INVALID_WORD),

    //75,73,33,51,45,182
    ILLEGAL_YELLOW_75(75, AuditStatusDescEnum.ILLEGAL_YELLOW),
    ILLEGAL_YELLOW_73(73, AuditStatusDescEnum.ILLEGAL_YELLOW),
    ILLEGAL_YELLOW_33(33, AuditStatusDescEnum.ILLEGAL_YELLOW),
    ILLEGAL_YELLOW_51(51, AuditStatusDescEnum.ILLEGAL_YELLOW),
    ILLEGAL_YELLOW_45(45, AuditStatusDescEnum.ILLEGAL_YELLOW),
    ILLEGAL_YELLOW_182(182, AuditStatusDescEnum.ILLEGAL_YELLOW),

    //53,54,44,135,35
    LOW_INFO_53(54, AuditStatusDescEnum.LOW_INFO),
    LOW_INFO_44(44, AuditStatusDescEnum.LOW_INFO),
    LOW_INFO_135(135, AuditStatusDescEnum.LOW_INFO),
    LOW_INFO_54(54, AuditStatusDescEnum.LOW_INFO),
    LOW_INFO_35(35, AuditStatusDescEnum.LOW_INFO),

    //385,386,387,388,389,336,323,324,325,326
    ILLEGAL_AD_LANG_385(385, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_386(386, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_387(387, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_388(388, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_389(389, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_336(336, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_323(323, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_324(324, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_325(325, AuditStatusDescEnum.ILLEGAL_AD_LANG),
    ILLEGAL_AD_LANG_326(326, AuditStatusDescEnum.ILLEGAL_AD_LANG),

    //97,98,123,124
    FORBID_TRADE_CONTENT_97(97, AuditStatusDescEnum.FORBID_TRADE_CONTENT),
    FORBID_TRADE_CONTENT_98(98, AuditStatusDescEnum.FORBID_TRADE_CONTENT),
    FORBID_TRADE_CONTENT_123(123, AuditStatusDescEnum.FORBID_TRADE_CONTENT),
    FORBID_TRADE_CONTENT_124(124, AuditStatusDescEnum.FORBID_TRADE_CONTENT),

    //393
    PATENT_INFRINGEMENT_393(393, AuditStatusDescEnum.PATENT_INFRINGEMENT),

    //328
    CONTENT_ILLEGAL_328(328, AuditStatusDescEnum.CONTENT_ILLEGAL),

    //122,123,124
    ILLEGAL_TRADEMARK_122(122, AuditStatusDescEnum.ILLEGAL_TRADEMARK),
    ILLEGAL_TRADEMARK_123(123, AuditStatusDescEnum.ILLEGAL_TRADEMARK),
    ILLEGAL_TRADEMARK_124(124, AuditStatusDescEnum.ILLEGAL_TRADEMARK),

    //191,192
    ILLEGAL_GUIDE_INFO_191(191, AuditStatusDescEnum.ILLEGAL_GUIDE_INFO),
    ILLEGAL_GUIDE_INFO_192(192, AuditStatusDescEnum.ILLEGAL_GUIDE_INFO),

    //90000001,90000002
    PRICE_EXCEPTION_90000001(90000001, AuditStatusDescEnum.PRICE_EXCEPTION),
    PRICE_EXCEPTION_90000002(90000002, AuditStatusDescEnum.PRICE_EXCEPTION),

    //95,96
    EXCEPTION_ACTION_INFO_95(95, AuditStatusDescEnum.EXCEPTION_ACTION_INFO),
    EXCEPTION_ACTION_INFO_96(96, AuditStatusDescEnum.EXCEPTION_ACTION_INFO),

    //157,158
    SECRET_INFO_157(157, AuditStatusDescEnum.SECRET_INFO),
    SECRET_INFO_158(158, AuditStatusDescEnum.SECRET_INFO),


    COPYRIGHT_INFRINGEMENT_392(392, AuditStatusDescEnum.COPYRIGHT_INFRINGEMENT),

    HARMFUL_DISCUSS_74(74, AuditStatusDescEnum.HARMFUL_DISCUSS),

    NATIONAL_FORBID_SALE_37(37, AuditStatusDescEnum.NATIONAL_FORBID_SALE),

    OVER_DESC_381(381, AuditStatusDescEnum.OVER_DESC),

    ABSOLUTIZATION_LANGUAGE_382(382, AuditStatusDescEnum.ABSOLUTIZATION_LANGUAGE),

    TRADEMARK_INFRINGEMENT_391(391, AuditStatusDescEnum.TRADEMARK_INFRINGEMENT),

    GOODS_ILLEGAL_DESC_154(154, AuditStatusDescEnum.GOODS_ILLEGAL_DESC),

    ILLEGAL_COMPETITION_DESC_383(383, AuditStatusDescEnum.ILLEGAL_COMPETITION_DESC),

    WORD_ILLEGAL_DESC_52(52, AuditStatusDescEnum.WORD_ILLEGAL_DESC),

    SUSPECT_CHEAT_INFO_137(137, AuditStatusDescEnum.SUSPECT_CHEAT_INFO);

    private int code;

    private AuditStatusDescEnum descEnum;

    public static Map<Integer, AuditStatusDescEnum> statusVerifyMap = new HashMap<>();

    static {
        for (AuditStatusVerifyEnum auditStatusVerifyEnum : AuditStatusVerifyEnum.values()){
            statusVerifyMap.put(auditStatusVerifyEnum.getCode(), auditStatusVerifyEnum.getDescEnum());
        }
    }

    AuditStatusVerifyEnum(Integer code, AuditStatusDescEnum descEnum) {
        this.code = code;
        this.descEnum = descEnum;
    }

}
