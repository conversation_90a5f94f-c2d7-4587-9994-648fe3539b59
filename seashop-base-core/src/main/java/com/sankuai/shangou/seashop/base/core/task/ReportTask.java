package com.sankuai.shangou.seashop.base.core.task;

import com.hishop.himall.report.api.request.ReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.ReportSourceRegionReq;
import com.hishop.himall.report.api.request.ReportSourceShopReq;
import com.hishop.himall.report.api.request.ReportSourceUserReq;
import com.hishop.himall.report.api.service.ReportFeign;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionProvinceCity;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseRegionRepository;
import com.sankuai.shangou.seashop.base.thrift.core.JobQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JobLogInfoResp;
import com.sankuai.shangou.seashop.user.dao.account.domain.Favorite;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopExt;
import com.sankuai.shangou.seashop.user.dao.shop.repository.FavoriteProductRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopExtRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description:
 */
@Slf4j
@Component
public class ReportTask {

    @Resource
    private FavoriteProductRepository favoriteProductRepository;
    @Resource
    private MemberRepository memberRepository;
    @Resource
    private BaseRegionRepository baseRegionRepository;
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private ReportFeign reportFeign;
    @Resource
    private JobQueryFeign jobQueryThriftService;
    @Resource
    private ShopExtRepository shopExtRepository;

    @XxlJob("reportFavoriteProduct")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步收藏的商品")
    public void reportFavoriteProduct() {
        log.info("【定时任务】【同步购物车报表】...start...");
        long jobId = XxlJobHelper.getJobId();
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryThriftService.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<Favorite> carts = favoriteProductRepository.getByUpdateTime(triggerTime);

        for (Favorite favorite : carts) {
            ReportSourceFollowProductReq followProductReq = new ReportSourceFollowProductReq();
            followProductReq.setId(favorite.getId());
            followProductReq.setProductId(favorite.getProductId());
            followProductReq.setFollowTime(favorite.getDate());
            followProductReq.setUserId(favorite.getUserId());
            BaseResp res = ThriftResponseHelper.executeThriftCall(() -> reportFeign.createSourceFollowProduct(followProductReq));
        }

    }

    @XxlJob("reportMember")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步会员报表")
    public void reportMember() {
        log.info("【定时任务】【同步会员报表】...start...");
        long jobId = XxlJobHelper.getJobId();
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryThriftService.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<Member> members = memberRepository.getByUpdateTime(triggerTime);

        for (Member member : members) {
            ReportSourceUserReq userReq = new ReportSourceUserReq();
            userReq.setUserId(member.getId());
            userReq.setNickname(member.getNick());
            if (member.getProvinceId() != null) {
                userReq.setProvinceId(member.getProvinceId().intValue());
            } else {
                log.info("同步会员，会员无收货地址省份id,memberId:" + member.getId());
            }

            userReq.setRegistrationTime(member.getCreateTime());
            userReq.setFirstPaymentTime(member.getFirstConsumptionTime());
            userReq.setPhone(member.getCellPhone());
            userReq.setWhetherLogOut(member.getWhetherLogOut());
            userReq.setCreateTime(LocalDateTime.ofInstant(member.getCreateTime().toInstant(), ZoneId.systemDefault()));
            userReq.setUpdateTime(LocalDateTime.ofInstant(member.getUpdateTime().toInstant(), ZoneId.systemDefault()));
            try {
                BaseResp res = ThriftResponseHelper.executeThriftCall(() -> reportFeign.createSourceUser(userReq));
            } catch (Exception ex) {
                log.error(ex.getMessage());
                continue;
            }


        }

    }

    @XxlJob("reportRegion")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步地区至报表服务")
    public void reportRegion() {
        log.info("【定时任务】【同步地区】...start...");
        long jobId = XxlJobHelper.getJobId();
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryThriftService.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<BaseRegion> regions = baseRegionRepository.getByUpdateTime(triggerTime);

        for (BaseRegion region : regions) {
            ReportSourceRegionReq regionReq = new ReportSourceRegionReq();
            regionReq.setId(region.getId());
            regionReq.setName(region.getName());
            regionReq.setLevel(region.getRegionLevel());
            regionReq.setParentId(region.getParentId());
            if (region.getCreateTime() != null) {
                regionReq.setCreateTime(LocalDateTime.ofInstant(region.getCreateTime().toInstant(), ZoneId.systemDefault()));
            }
            if (region.getUpdateTime() != null) {
                regionReq.setUpdateTime(LocalDateTime.ofInstant(region.getUpdateTime().toInstant(), ZoneId.systemDefault()));
            }
            BaseResp res = ThriftResponseHelper.executeThriftCall(() -> reportFeign.createSourceRegion(regionReq));
        }


    }

    @XxlJob("reportShop")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步店铺数据")
    public void reportShop() {
        log.info("【定时任务】【同步店铺】...start...");
        long jobId = XxlJobHelper.getJobId();
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryThriftService.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<Shop> shops = shopRepository.listByUpdateTime(triggerTime);
        log.info("【定时任务】【同步店铺】...start...shop size:{}", shops.size());
        for (Shop shop : shops) {
            try {
                ReportSourceShopReq req = new ReportSourceShopReq();
                req.setShopId(shop.getId());
                req.setShopName(shop.getShopName());
                req.setOpeningTime(shop.getCreateTime());
                //TODO 省市id，开业时间待同步
                ShopExt shopExt = shopExtRepository.getByShopId(shop.getId());
                if(shopExt != null && shopExt.getCompanyRegionId() != null) {
                    BaseRegionProvinceCity brpt = baseRegionRepository.getProvinceCity(Long.parseLong(shopExt.getCompanyRegionId() + ""));
                    if(brpt != null) {
                        req.setProvinceId(brpt.getProvinceId());
                        req.setCity(brpt.getCityId() != null ? brpt.getCityId() : 0);
                    } else {
                        req.setProvinceId(0);
                        req.setCity(0);
                    }
                } else {
                    req.setProvinceId(0);
                    req.setCity(0);
                }
                req.setCreateTime(shop.getCreateTime());
                req.setUpdateTime(shop.getUpdateTime());
                reportFeign.createSourceShop(req);
            } catch (Exception e) {
                log.error("同步店铺信息错误：shop:{}, {}", shop, e);
            }
        }
    }
}
