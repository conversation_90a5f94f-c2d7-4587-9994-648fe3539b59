package com.sankuai.shangou.seashop.base.core.service.impl;

import com.sankuai.shangou.seashop.base.core.service.JobService;
import com.sankuai.shangou.seashop.base.dao.core.domain.JobInfo;
import com.sankuai.shangou.seashop.base.dao.core.domain.JobLogInfo;
import com.sankuai.shangou.seashop.base.dao.core.repository.JobInfoRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.JobLogInfoRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class JobServiceImpl implements JobService {


    @Resource
    private JobLogInfoRepository jobLogInfoRepository;

    @Resource
    private JobInfoRepository jobInfoRepository;

    @Override
    public JobLogInfo getJobLog(Long jobId) {

        JobInfo info = jobInfoRepository.getById(jobId);
        if (info == null) {
            return null;
        }
        JobLogInfo result = jobLogInfoRepository.getLastSuccessInfoByJobId(jobId);
        if (result == null) {
            result = new JobLogInfo();
        }
        result.setJobId(info.getId());
        return result;
    }
}
