package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.ExpressSelfQueryService;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.dto.QueryExpressCompanyDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryBatchExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryErpExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyPageReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressSiteSettingResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyPageResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyResp;

@RestController
@RequestMapping("/express")
public class ExpressQueryController implements ExpressQueryFeign {

    @Resource
    private ExpressSelfQueryService expressSelfQueryService;

    @PostMapping(value = "/queryExpressCompanyList", consumes = "application/json")
    @Override
    public ResultDto<QueryExpressCompanyResp> queryExpressCompanyList(@RequestBody QueryExpressCompanyReq queryExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryExpressCompanyList", queryExpressCompanyReq, req ->
            expressSelfQueryService.queryExpressCompanyList(req));
    }

    @PostMapping(value = "/queryBatchExpressCompany", consumes = "application/json")
    @Override
    public ResultDto<QueryExpressCompanyResp> queryBatchExpressCompany(@RequestBody QueryBatchExpressCompanyReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBatchExpressCompany", req,
                funReq -> expressSelfQueryService.queryBatchExpressCompany(req));
    }

    @PostMapping(value = "/queryExpressByThirdCode", consumes = "application/json")
    @Override
    public ResultDto<QueryExpressCompanyDto> queryExpressByThirdCode(@RequestBody QueryErpExpressCompanyReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("queryExpressByThirdCode", req,
                input -> expressSelfQueryService.queryExpressByThirdCode(input));
    }

    @GetMapping(value = "/queryExpressSiteSetting")
    @Override
    public ResultDto<ExpressSiteSettingResp> queryExpressSiteSetting() throws TException {
        return ThriftResponseHelper.responseInvoke("queryExpressSiteSetting", null, req ->
            expressSelfQueryService.queryExpressSiteSetting());
    }

    @PostMapping(value = "/queryExpressCompanyPage", consumes = "application/json")
    @Override
    public ResultDto<QueryExpressCompanyPageResp> queryExpressCompanyPage(@RequestBody QueryExpressCompanyPageReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("queryExpressCompanyPage", req, request ->
                expressSelfQueryService.queryExpressCompanyPage(request));
    }
}
