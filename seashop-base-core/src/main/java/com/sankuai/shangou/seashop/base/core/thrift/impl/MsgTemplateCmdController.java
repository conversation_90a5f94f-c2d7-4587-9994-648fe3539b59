package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.MsgTemplateCmdService;
import com.sankuai.shangou.seashop.base.thrift.core.MsgTemplateCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BindOpenIdReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.CmdWxAppletFormDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MsgTemplateAppletReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MsgTemplateDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveMsgTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SendAppletMessageReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SendAppletReq;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:06
 */
@RestController
@RequestMapping("/msgTemplate")
public class MsgTemplateCmdController implements MsgTemplateCmdFeign {

    @Resource
    private MsgTemplateCmdService msgTemplateCmdService;

    @PostMapping(value = "/saveMsgTemplate", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveMsgTemplate(@RequestBody SaveMsgTemplateReq saveMsgTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveMsgTemplate", saveMsgTemplateReq, req -> {
            msgTemplateCmdService.saveMsgTemplate(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/saveMsgTemplateApplet", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveMsgTemplateApplet(@RequestBody MsgTemplateAppletReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("saveMsgTemplateApplet", request, req -> {
            msgTemplateCmdService.saveMsgTemplateApplet(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/saveMsgTemplateData", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveMsgTemplateData(@RequestBody MsgTemplateDataReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("saveMsgTemplateData", request, req -> {
            msgTemplateCmdService.saveMsgTemplateData(req);
            return new BaseResp();
        });

    }

    @GetMapping(value = "/getAppletSubscribeTmplate")
    @Override
    public ResultDto<BaseResp> getAppletSubscribeTmplate() throws TException {
        return ThriftResponseHelper.responseInvoke("getAppletSubscribeTmplate", null, req -> {
            msgTemplateCmdService.getAppletSubscribeTmplate();
            return new BaseResp();
        });
    }

    @PostMapping(value = "/sendAppletMessageByTemplate", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> sendAppletMessageByTemplate(@RequestBody SendAppletMessageReq sendAppletMessageReq) throws TException {
        return ThriftResponseHelper.responseInvoke("sendAppletMessageByTemplate", sendAppletMessageReq, req -> {
            msgTemplateCmdService.sendAppletMessageByTemplate(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/sendAppletMessage", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> sendAppletMessage(@RequestBody SendAppletReq sendAppletReq) throws TException {
        return ThriftResponseHelper.responseInvoke("sendAppletMessageByTemplate", sendAppletReq, req -> {
            req.checkParameter();
            msgTemplateCmdService.sendAppletMessage(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/insertWxAppletFormData", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> insertWxAppletFormData(@RequestBody CmdWxAppletFormDataReq cmdWxAppletFormDataReq) throws TException {
        return ThriftResponseHelper.responseInvoke("sendAppletMessageByTemplate", cmdWxAppletFormDataReq, req -> {
            req.checkParameter();
            msgTemplateCmdService.insertWxAppletFormData(req);
            return new BaseResp();
        });
    }

    @Override
    public ResultDto<BaseResp> bindOpenId(BindOpenIdReq bindOpenIdReq) throws TException {
        //TODO 迁移
        return null;
    }

}
