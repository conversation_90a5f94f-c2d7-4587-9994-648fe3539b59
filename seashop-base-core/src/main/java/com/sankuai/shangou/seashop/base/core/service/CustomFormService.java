package com.sankuai.shangou.seashop.base.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormRes;

/**
 * <AUTHOR>
 * @date 2023/12/09 9:53
 */
public interface CustomFormService {

    /**
     * 根据表单id查询表单
     *
     * @param formIds 表单id的集合
     * @return 表单
     */
    List<BaseCustomFormRes> queryCustomFormByFormIds(List<Long> formIds);

    /**
     * 根据表单id查询表单字段
     *
     * @param fieldIds 字段id的集合
     * @return 表单字段
     */
    List<BaseCustomFormField> queryCustomFieldByFieldIds(List<Long> fieldIds);
}
