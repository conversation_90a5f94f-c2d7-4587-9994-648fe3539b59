package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.thrift.core.request.AddOrUpdateFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteFootMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveFootMenusReq;

/**
 * @author： liweisong
 * @create： 2023/11/29 13:45
 */
public interface MobileFootCmdService {

    /**
     * 新增或者编辑底部导航栏
     * @param addFootMenusReq
     */
    void addOrUpdateFootMenus(AddOrUpdateFootMenusReq addFootMenusReq);

    /**
     * 删除底部导航栏
     * @param deleteFootMenuReq
     */
    void deleteFootMenu(DeleteFootMenuReq deleteFootMenuReq);

    /**
     * 保存底部导航栏
     *
     * @param saveFootMenusReq
     */
    void saveFootMenus(SaveFootMenusReq saveFootMenusReq);
}
