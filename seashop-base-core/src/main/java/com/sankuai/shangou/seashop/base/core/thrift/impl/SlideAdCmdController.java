package com.sankuai.shangou.seashop.base.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.common.constant.LockConstant;
import com.sankuai.shangou.seashop.base.core.service.SlideAdService;
import com.sankuai.shangou.seashop.base.thrift.core.SlideAdCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MoveLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.utils.LockHelper;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@RestController
@RequestMapping("/slideAd")
public class SlideAdCmdController implements SlideAdCmdFeign {

    @Resource
    private SlideAdService slideAdService;

    @PostMapping(value = "/addLimitTimeBuy", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addLimitTimeBuy(@RequestBody AddLimitTimeBuyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("addLimitTimeBuy", request, req -> {
            req.checkParameter();
            LockHelper.lock(LockConstant.LIMIT_TIME_BUY_LOCK, () -> {
                slideAdService.addLimitTimeBuy(req);
            });
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateLimitTimeBuy", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateLimitTimeBuy(@RequestBody UpdateLimitTimeBuyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateLimitTimeBuy", request, req -> {
            req.checkParameter();
            slideAdService.updateLimitTimeBuy(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/moveLimitTimeBuy", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> moveLimitTimeBuy(@RequestBody MoveLimitTimeBuyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("moveLimitTimeBuy", request, req -> {
            req.checkParameter();
            LockHelper.lock(LockConstant.LIMIT_TIME_BUY_LOCK, () -> {
                slideAdService.moveLimitTimeBuy(req);
            });
            return new BaseResp();
        });
    }

    @PostMapping(value = "/deleteLimitTimeBuy", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> deleteLimitTimeBuy(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteLimitTimeBuy", request, req -> {
            req.checkParameter();
            LockHelper.lock(LockConstant.LIMIT_TIME_BUY_LOCK, () -> {
                slideAdService.deleteLimitTimeBuy(req);
            });
            return new BaseResp();
        });
    }
}
