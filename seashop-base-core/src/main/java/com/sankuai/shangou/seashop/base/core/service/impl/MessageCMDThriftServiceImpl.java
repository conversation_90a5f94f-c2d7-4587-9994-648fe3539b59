package com.sankuai.shangou.seashop.base.core.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hishop.starter.sms.SmsClient;
import com.hishop.starter.sms.model.SendSmsParam;
import com.hishop.starter.sms.model.SmsAccountParam;
import com.hishop.starter.util.json.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.common.config.HimallConfigProperties;
import com.sankuai.shangou.seashop.base.common.config.SmsTemplate;
import com.sankuai.shangou.seashop.base.core.service.MessageCMDThriftService;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.thrift.core.request.ContactReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SmsBodyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.SmsSettingRes;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.user.common.constant.LeafConstant;
import com.sankuai.shangou.seashop.user.common.constant.SmsConstant;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname MessageCMDThriftServiceImpl
 * Description //TODO
 * @date 2024/8/9 11:43
 */
@Service
public class MessageCMDThriftServiceImpl implements MessageCMDThriftService {
    @Resource
    private HimallConfigProperties himallConfigProperties;

    @Resource
    private SiteSettingService siteSettingService;

    @Resource
    private SmsClient smsClient;
    //    是否真正发送
    @Value("${himall.sms.real.send:false}")
    private String realSend;

    @Resource
    private LeafService leafService;
    @Resource
    private SquirrelUtil squirrelUtil;

    @Override
    public boolean sendSms(SmsBodyReq smsBodyReq) {
        SmsSettingRes settingRes = siteSettingService.querySmsSetting();
        if (settingRes == null) {
            throw new BusinessException("请设置短信秘钥相关");
        }
        // 根据模板
        Pair<Integer, String> smsTemplateByTemplateId = getSmsTemplateByTemplateId(smsBodyReq.getTemplateId(), smsBodyReq.getParam());
        if (smsTemplateByTemplateId == null) {
            throw new BusinessException("没有找到合适的短信模板");
        }
        //查询配置
        SmsAccountParam smsAccountParam = new SmsAccountParam();
        smsAccountParam.setAppKey(settingRes.getSmsAppKey());
        smsAccountParam.setAppSecret(settingRes.getSmsAppSecret());
        SendSmsParam sendSmsParam = new SendSmsParam();
        sendSmsParam.setMobiles(smsBodyReq.getContactList().stream().map(ContactReq::getMobile).collect(Collectors.joining(",")));

        sendSmsParam.setSmsChannel(smsTemplateByTemplateId.getKey());
        sendSmsParam.setText(smsTemplateByTemplateId.getValue());
        //todo 发送短信暂时注释
        if (StrUtil.equalsIgnoreCase(realSend, "false")) {
            return true;
        }
        smsClient.send(smsAccountParam, sendSmsParam);
        return true;
    }

    @Override
    public boolean sendSms(Long code, String param, String mobile) {
        // 发送短信
        SmsBodyReq smsBodyReq = new SmsBodyReq();
        smsBodyReq.setTemplateId(code);
        smsBodyReq.setParam(param);
        smsBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
        // 设置收信人
        ContactReq contactReq = new ContactReq();
        contactReq.setMobile(mobile);
        smsBodyReq.setContactList(Collections.singletonList(contactReq));
        return this.sendSms(smsBodyReq);
    }

    @Override
    public boolean sendSmsCode(Long code, String mobile) {
        //生成随机6位数字验证码
        String randCode = RandomUtil.randomNumbers(6);
        Map<String, String> params = new HashMap<>();
        params.put(SmsConstant.BASE_SMS_CODE_KEY, randCode);
        String paramStr = JSONUtil.toJsonStr(params);
        //缓存验证码
//        判断验证码是否存在
        if (squirrelUtil.get(SmsConstant.SMS_CODE_KEY + mobile) != null) {
            throw new BusinessException(UserResultCodeEnum.SMS_CODE_EXIST);
        }
        squirrelUtil.set(SmsConstant.SMS_CODE_KEY + mobile, randCode, SmsConstant.SMS_CODE_EXPIRE_TIME);
        this.sendSms(code, paramStr, mobile);
        return false;
    }

    @Override
    public void sendEmail(String subject, String body, List<String> email) {
        //TODO 待实现
//        EmailBodyReq emailBodyReq = new EmailBodyReq();
//        emailBodyReq.setBody(body);
//        emailBodyReq.setSendFrom("test?");
//        emailBodyReq.setSubject(subject);
//        emailBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
//        // 设置收信人
//        List<ContactReq> contactReqList = email.stream().map(e -> {
//            ContactReq contactReq = new ContactReq();
//            contactReq.setEmail(e);
//            return contactReq;
//        }).collect(Collectors.toList());
//        emailBodyReq.setContactList(contactReqList);
        throw new UnsupportedOperationException("功能待实现");
    }

    @Override
    public void sendEmailCode(String eMail) {
        //生成随机6位数字验证码
        String code = RandomUtil.randomNumbers(6);
        String message = StrUtil.format(SmsConstant.EMAIL_CODE_CONTENT, code);
        //缓存验证码
        if (squirrelUtil.get(SmsConstant.EMAIL_CODE_KEY + eMail) != null) {
            throw new BusinessException(UserResultCodeEnum.SMS_CODE_EXIST);
        }
        squirrelUtil.set(SmsConstant.EMAIL_CODE_KEY + eMail, code, SmsConstant.SMS_CODE_EXPIRE_TIME);
        this.sendEmail(SmsConstant.EMAIL_CODE_SUBJECT, message, Collections.singletonList(eMail));
    }

    private Pair<Integer, String> getSmsTemplateByTemplateId(Long templateId, String param) {
        Map<String, SmsTemplate> templates = himallConfigProperties.getSms().getTemplates();
        if (templateId == null || CollectionUtil.isEmpty(templates)) {
            return null;
        }
        SmsTemplate smsTemplate = templates.get(templateId.toString());
        if (smsTemplate == null) {
            return null;
        }
        Map map = JsonUtil.parseObject(Optional.ofNullable(param).orElse("{}"), Map.class);

        return Pair.of(smsTemplate.getSmsChannel().getCode(), StrUtil.format(smsTemplate.getContent(), map));
    }
}
