package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategory;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategoryExample;

import java.util.List;

public interface ArticleCategoryService {

    Long create(BaseArticleCategory category);

    Boolean update(BaseArticleCategory category);

    Boolean deletes(List<Long> ids);

    Long getArticleCount(List<Long> ids);

    List<BaseArticleCategory> query();

    List<BaseArticleCategory> query(BaseArticleCategoryExample example);

    BaseArticleCategory getById(long id);


    List<BaseArticleCategory> getChildsById(Long id);
}
