package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.core.service.SlideAdService;
import com.sankuai.shangou.seashop.base.thrift.core.SlideAdQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.SlideAdListResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.SlideAdResp;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@RestController
@RequestMapping("/slideAd")
public class SlideAdQueryController implements SlideAdQueryFeign {

    @Resource
    private SlideAdService slideAdService;

    @GetMapping(value = "/queryLimitTimeBuy")
    @Override
    public ResultDto<SlideAdListResp> queryLimitTimeBuy() throws TException {
        return ThriftResponseHelper.responseInvoke("queryLimitTimeBuy", null, req -> {
            List<SlideAdResp> slideAdList = slideAdService.queryLimitTimeBuy();
            SlideAdListResp slideAdListResp = new SlideAdListResp();
            slideAdListResp.setList(slideAdList);
            return slideAdListResp;
        });
    }
}
