package com.sankuai.shangou.seashop.base.core.thrift.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.RegionService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.dto.TreeRegionDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseRegionRes;

import cn.hutool.core.util.StrUtil;


@RestController
@RequestMapping("/region")
public class RegionQueryController implements RegionQueryFeign {

    @Resource
    private RegionService regionService;

    @GetMapping(value = "/getRegionByParentId")
    @Override
    public ResultDto<List<BaseRegionRes>> getRegionByParentId(@RequestParam("parentId") long parentId) throws TException {
        return ThriftResponseHelper.responseInvoke("BaseRegionDto", parentId, req -> {
            List<BaseRegion> regions = regionService.getRegionByParentId(req);
            return JsonUtil.copyList(regions, BaseRegionRes.class);
        });
    }


    @GetMapping(value = "/getTreeRegions")
    @Override
    public ResultDto<String> getTreeRegions() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("BaseRegionDto", i, req -> {
            //获取三级地址
            List<BaseRegion> regions = regionService.getAllRegions(3);
            List<TreeRegionDto> treeRegions = listToTree(regions);

            return JsonUtil.toJsonString(treeRegions);
        });
    }

    @GetMapping(value = "/getAllRegions")
    @Override
    public ResultDto<List<BaseRegionRes>> getAllRegions() throws TException {

        int i = 0;
        return ThriftResponseHelper.responseInvoke("BaseRegionDto", i, req -> {
            //获取三级地址
            List<BaseRegion> regions = regionService.getAllRegions(3);
            List<BaseRegionRes> result = JsonUtil.copyList(regions, BaseRegionRes.class);

            return result;
        });
    }

    @GetMapping(value = "/getParentRegions")
    @Override
    public ResultDto<List<BaseRegionRes>> getParentRegions(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getParentRegions", id, req -> {
            List<BaseRegion> regions = regionService.getParentRegions(req);
            List<BaseRegionRes> result = JsonUtil.copyList(regions, BaseRegionRes.class);
            return result;
        });
    }

    @GetMapping(value = "/getParentRegionsByCode")
    @Override
    public ResultDto<List<BaseRegionRes>> getParentRegionsByCode(@RequestParam String code) throws TException {
        if (StrUtil.isBlank(code)) {
            return ResultDto.<List<BaseRegionRes>>newWithData(null)
                    .fail(-1, "code不能为空");
        }

        return ThriftResponseHelper.responseInvoke("getParentRegionsByCode", code, req -> {
            List<BaseRegion> regions = regionService.getParentRegionsByCode(req);
            return JsonUtil.copyList(regions, BaseRegionRes.class);
        });
    }

    @GetMapping(value = "/getSubRegions")
    @Override
    public ResultDto<List<BaseRegionRes>> getSubRegions(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getSubRegions", id, req -> {
            List<BaseRegion> regions = regionService.getSubRegions(req);
            List<BaseRegionRes> result = JsonUtil.copyList(regions, BaseRegionRes.class);
            return result;
        });
    }

    @GetMapping(value = "/getTrackRegionsById")
    @Override
    public ResultDto<List<BaseRegionRes>> getTrackRegionsById(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getTrackRegionsById", id, req -> {
            List<BaseRegion> regions = regionService.getTrackRegionsById(req);
            List<BaseRegionRes> result = JsonUtil.copyList(regions, BaseRegionRes.class);
            return result;
        });
    }

    @PostMapping(value = "/getAllPathRegions", consumes = "application/json")
    @Override
    public ResultDto<Map<String, AllPathRegionResp>> getAllPathRegions(@RequestBody RegionIdsReq regionIdsReq) throws TException {
        regionIdsReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("getAllPathRegions", regionIdsReq, req -> regionService.getAllPathRegions(req));
    }

    /**
     * 使用递归方法建树
     *
     * @param list 要转成树的集合
     * @return List
     */
    public static List<TreeRegionDto> listToTree(List<BaseRegion> list) {
        List<TreeRegionDto> trees = new ArrayList<>();
        for (BaseRegion entity : list) {
            TreeRegionDto dto = JsonUtil.copy(entity, TreeRegionDto.class);
            long parentId = dto.getParentId();
            if (parentId == 0) {
                // 是父级
                trees.add(findChildren(dto, list));
            }
        }
        return trees;
    }

    /**
     * 递归查找子节点
     *
     * @param entity 对象
     * @param list   子节点
     * @return MenuTree
     */
    private static TreeRegionDto findChildren(TreeRegionDto entity, List<BaseRegion> list) {
        for (BaseRegion info : list) {
            if (entity.getId().equals(info.getParentId())) {
                if (entity.getSub() == null) {
                    entity.setSub(new ArrayList<>());
                }
                TreeRegionDto dto = JsonUtil.copy(info, TreeRegionDto.class);
                entity.getSub().add(findChildren(dto, list));
            }
        }
        return entity;
    }

}
