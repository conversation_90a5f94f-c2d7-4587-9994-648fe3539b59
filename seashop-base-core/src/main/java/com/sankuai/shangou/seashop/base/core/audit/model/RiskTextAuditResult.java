package com.sankuai.shangou.seashop.base.core.audit.model;

import lombok.*;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class RiskTextAuditResult {
    /**
     * 风控决策码
     * 正常 0, 拦截 XXXX
     */
    private Integer riskResultCode;

    /**
     * 风控拦截的提示文案
     */
    private String prompt;

    /**
     * 本次请求的唯一标识，用于问题排查
     */
    private String riskCheckId;

    /**
     * 风控根据业务需求返回的额外信息,至少包含bizData和statusNames两个字段
     */
    private String extra;
    /**
     * 命中风控规则ID，业务方一般不用关注
     */
    private Long effectiveRuleId;
}
