package com.sankuai.shangou.seashop.base.core.service;

import com.sankuai.shangou.seashop.base.thrift.core.request.AddExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.CmdExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.ExpressSiteSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateExpressStatusReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateExpressTemplateReq;

public interface ExpressCmdService {

    /**
     * 新增
     * @param addExpressCompanyReq
     */
    void createExpressCompany(AddExpressCompanyReq addExpressCompanyReq);

    /**
     * 编辑
     * @param cmdExpressCompanyReq
     * @return
     */
    void updateExpressCompany(CmdExpressCompanyReq cmdExpressCompanyReq);

    /**
     * 模版设置
     * @param updateExpressTemplateReq
     * @return
     */
    void updateExpressTemplate(UpdateExpressTemplateReq updateExpressTemplateReq);

    /**
     * 状态变更
     * @param updateExpressStatusReq
     * @return
     */
    void updateExpressStatus(UpdateExpressStatusReq updateExpressStatusReq);

    /**
     * 删除
     * @param deleteExpressCompanyReq
     * @return
     */
    void deleteExpressCompany(DeleteExpressCompanyReq deleteExpressCompanyReq);

    /**
     * 美团物流设置
     * @param expressSiteSettingReq
     * @return
     */
    void addOrUpdateSiteSetting(ExpressSiteSettingReq expressSiteSettingReq);
}
