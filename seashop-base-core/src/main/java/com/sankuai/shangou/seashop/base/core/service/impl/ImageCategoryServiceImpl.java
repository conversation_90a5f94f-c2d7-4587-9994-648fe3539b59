package com.sankuai.shangou.seashop.base.core.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.core.service.ImageCategoryService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory;
import com.sankuai.shangou.seashop.base.dao.core.repository.BasePhotoSpaceCategoryRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.BasePhotoSpaceRepository;

@Service
public class ImageCategoryServiceImpl implements ImageCategoryService {

    @Resource
    private BasePhotoSpaceCategoryRepository categoryRepository;

    @Resource
    private BasePhotoSpaceRepository photoSpaceRepository;

    @Override
    public Long create(BasePhotoSpaceCategory category) {
        categoryRepository.create(category);
        return category.getId();
    }

    @Override
    public Boolean update(BasePhotoSpaceCategory category) {
        categoryRepository.update(category);
        return true;
    }

    @Override
    public List<BasePhotoSpaceCategory> query(Long shopId) {
        List<BasePhotoSpaceCategory> result = categoryRepository.getList(shopId);
        return result;
    }

    @Override
    public Boolean deletes(List<Long> ids, long shopId) {
        categoryRepository.delete(ids, shopId);
        photoSpaceRepository.setNoGroup(ids, shopId);
        return true;
    }
}
