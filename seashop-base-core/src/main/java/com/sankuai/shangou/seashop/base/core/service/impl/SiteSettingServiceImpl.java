package com.sankuai.shangou.seashop.base.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.base.common.constant.LockConstant;
import com.sankuai.shangou.seashop.base.common.constant.SiteSettingConstant;
import com.sankuai.shangou.seashop.base.core.mq.model.CustomFormEvent;
import com.sankuai.shangou.seashop.base.core.mq.publisher.CustomFormPublisher;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.*;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseAgreementRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseCustomFormRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseSettledRepository;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseSiteSettingRepository;
import com.sankuai.shangou.seashop.base.thrift.core.enums.AgreementStatus;
import com.sankuai.shangou.seashop.base.thrift.core.enums.AgreementTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.*;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.user.common.enums.CustomFormChangeEnum;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SiteSettingServiceImpl implements SiteSettingService {

    private static final String WEIXINAPPLETID = "weixinAppletId";
    private static final String WEIXINAPPLETSECRET = "weixinAppletSecret";
    @Resource
    private BaseSiteSettingRepository siteSettingRepository;

    @Resource
    private BaseSettledRepository baseSettledRepository;

    @Resource
    private BaseAgreementRepository agreementRepository;

    @Resource
    private BaseCustomFormRepository customFormRepository;

    @Resource
    private BaseLogAssist baseLogAssist;
    @Resource
    private CustomFormPublisher customFormPublisher;

    @Override
    @Transactional
    public Boolean saveSettings(List<BaseSiteSetting> settings) {
        for (BaseSiteSetting setting : settings) {
            boolean isExist = siteSettingRepository.exist(setting);
            if (isExist) {
                siteSettingRepository.update(setting);
            } else {
                siteSettingRepository.create(setting);
            }
        }
        return true;
    }

    @Override
    @Transactional
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "修改站点设置",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "siteSettingRepository", serviceMethod = "saveAppSettings",
            dto = BaseSiteSettingReq.class, entity = BaseSiteSetting.class)
    public Boolean saveAppSettings(BaseSiteSettingReq req) {
        Field[] fields = req.getClass().getDeclaredFields();
        Map<String, String> settings = new Hashtable<>();
        for (Field field : fields) {
            try {
                if (field.isSynthetic()) {
                    //针对某些情况，编译器会引入一些字段，需要过滤掉
                    continue;
                }
                field.setAccessible(true);
                String name = field.getName();
                Object value = field.get(req);
                if (value != null) {
                    settings.put(name, value.toString());
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }

        if (settings.size() < 1) {
            return true;
        }

        List<BaseSiteSetting> settingList = new ArrayList<>();
        for (Map.Entry<String, String> entry : settings.entrySet()) {
            BaseSiteSetting setting = new BaseSiteSetting();
            setting.setKey(entry.getKey());
            setting.setValue(entry.getValue());
            settingList.add(setting);
        }
        for (BaseSiteSetting setting : settingList) {
            boolean isExist = siteSettingRepository.exist(setting);
            if (isExist) {
                siteSettingRepository.update(setting);
            } else {
                siteSettingRepository.create(setting);
            }
        }
        return true;
    }

    @Override
    /*@ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "修改小程序appKey",
        processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
        repository = "siteSettingRepository", serviceMethod = "saveMsgTemplateApplet", getDaoMethod = "selectMsgTemplateAppletById",
        dto = MsgTemplateAppletReq.class, entity = MsgTemplateApplet.class)*/
    public Boolean saveMsgTemplateApplet(MsgTemplateAppletReq req) {
        List<BaseSiteSetting> settings = new ArrayList<>();
        BaseSiteSetting appletId = new BaseSiteSetting();
        appletId.setKey(WEIXINAPPLETID);
        appletId.setValue(req.getWeixinAppletId());
        settings.add(appletId);
        BaseSiteSetting appletSecret = new BaseSiteSetting();
        appletSecret.setKey(WEIXINAPPLETSECRET);
        appletSecret.setValue(req.getWeixinAppletSecret());
        settings.add(appletSecret);
        return saveSettings(settings);

    }

    @Override
    @Transactional
    public Boolean updateSettingsByKey(List<BaseSiteSetting> settings) {
        for (BaseSiteSetting setting : settings) {
            boolean isExist = siteSettingRepository.exist(setting);
            if (isExist) {
                siteSettingRepository.updateSettingsByKey(setting);
            } else {
                siteSettingRepository.create(setting);
            }
        }
        return true;
    }

    @Override
    public List<BaseSiteSetting> query(List<String> keys) {
        BaseSiteSettingExample example = new BaseSiteSettingExample();
        BaseSiteSettingExample.Criteria criteria = example.createCriteria();
        criteria.andKeyIn(keys);
        return siteSettingRepository.query(example);
    }


    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "修改入驻配置",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "baseSettledRepository", serviceMethod = "saveSettled", getDaoMethod = "get",
            dto = BaseSettledReq.class, entity = BaseSettled.class)
    public Boolean saveSettled(BaseSettledReq settled) {
        BaseSettledWithBLOBs req = JsonUtil.copy(settled, BaseSettledWithBLOBs.class);
        return baseSettledRepository.save(req);
    }

    @Override
    public BaseSettled getSettled() {
        return baseSettledRepository.get();
    }

    @Override
    public BaseAgreement getAgreement(int type) {
        return agreementRepository.get(type);
    }

    @Override
    public BaseAllAgreementRes getAgreements() {
        List<BaseAgreement> agreements = agreementRepository.getAll();

        BaseAllAgreementRes allAgreementRes = new BaseAllAgreementRes();
        boolean hasBuyer = agreements.stream().anyMatch(t -> t.getAgreementType().equals(AgreementTypeEnum.Buyers.getCode()));
        if (hasBuyer) {
            BaseAgreement buyer = agreements.stream().filter(t -> t.getAgreementType().equals(AgreementTypeEnum.Buyers.getCode())).findFirst().get();
            allAgreementRes.setBuyerAgreementContent(buyer.getAgreementContent());
        }
        boolean hasSeller = agreements.stream().anyMatch(t -> t.getAgreementType().equals(AgreementTypeEnum.Seller.getCode()));
        if (hasSeller) {
            BaseAgreement seller = agreements.stream().filter(t -> t.getAgreementType().equals(AgreementTypeEnum.Seller.getCode())).findFirst().get();
            allAgreementRes.setSellerAgreementContent(seller.getAgreementContent());
        }

        boolean hasPrivacyPolicy = agreements.stream().anyMatch(t -> t.getAgreementType().equals(AgreementTypeEnum.PrivacyPolicy.getCode()));
        if (hasPrivacyPolicy) {
            BaseAgreement privacyPolicy = agreements.stream().filter(t -> t.getAgreementType().equals(AgreementTypeEnum.PrivacyPolicy.getCode())).findFirst().get();
            allAgreementRes.setPrivacyAgreementContent(privacyPolicy.getAgreementContent());
        }
        return allAgreementRes;
    }

    @Override
    public Boolean saveAgreement(BaseAgreement agreement) {
        Boolean result = agreementRepository.save(agreement);
        return result;
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "新增自定义表单",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.INSERT,
            repository = "customFormRepository", serviceMethod = "createCustomForm", getDaoMethod = "get",
            dto = BaseCustomFormReq.class, entity = BaseCustomForm.class)
    public Long createCustomForm(BaseCustomFormReq req) {
        BaseCustomForm customForm = JsonUtil.copy(req, BaseCustomForm.class);
        customForm.setCreateDate(new Date());
        long formId = customFormRepository.create(customForm);

        CustomFormEvent event = CustomFormEvent.create(Collections.singletonList(formId), CustomFormChangeEnum.ADD.getCode());
        customFormPublisher.sendEvent(event);
        return formId;
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "编辑自定义表单",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MODIFY,
            repository = "customFormRepository", serviceMethod = "createCustomForm", getDaoMethod = "get",
            dto = BaseCustomFormReq.class, entity = BaseCustomForm.class)
    public Boolean updateCustomForm(BaseCustomFormReq req) {
        BaseCustomForm customForm = JsonUtil.copy(req, BaseCustomForm.class);
        customForm.setUpdateDate(new Date());
        boolean result = customFormRepository.update(customForm);

        CustomFormEvent event = CustomFormEvent.create(Collections.singletonList(customForm.getId()), CustomFormChangeEnum.EDIT.getCode());
        customFormPublisher.sendEvent(event);
        return result;
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId", actionName = "删除自定义表单",
            processModel = ExaminModelEnum.SYSTEM_SETTING, processType = ExaProEnum.MOVE,
            repository = "customFormRepository", serviceMethod = "deletes",
            dto = BaseIdsReq.class, entity = BaseCustomForm.class)
    public Boolean deletesCustomForm(BaseIdsReq baseIdsReq) {

        boolean result = customFormRepository.deletes(baseIdsReq.getIds());

        CustomFormEvent event = CustomFormEvent.create(baseIdsReq.getIds(), CustomFormChangeEnum.DELETE.getCode());
        customFormPublisher.sendEvent(event);
        return result;
    }

    @Override
    public Boolean existCustomFrom(String name) {
        Boolean result = customFormRepository.existCustomFrom(name);
        return result;
    }

    @Override
    public Page<BaseCustomForm> queryCustomFormWithPage(BaseCustFormQueryReq queryReq) {
        BasePageParam page = new BasePageParam();
        page.setPageNum(queryReq.getPageNo());
        page.setPageSize(queryReq.getPageSize());
        Page<BaseCustomForm> pageResult = PageHelper.startPage(page);
        BaseCustomFormExample example = new BaseCustomFormExample();
        BaseCustomFormExample.Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(queryReq.getName())) {
            criteria.andNameLike("%" + queryReq.getName() + "%");
        }
        example.setOrderByClause("update_date desc");
        customFormRepository.query(example);
        return pageResult;
    }

    @Override
    public BaseCustomForm getCustomForm(long formId) {
        return customFormRepository.get(formId);
    }

    @Override
    public Boolean saveShopSettings(BaseShopSitSettingReq settingReq) {
        Field[] fields = BaseShopSitSettingReq.class.getDeclaredFields();
        //List<BaseSiteLogBO> oldValue = new ArrayList<>();
        //List<BaseSiteLogBO> newValue = new ArrayList<>();
        for (Field field : fields) {
            BaseSiteSetting setting = new BaseSiteSetting();
            setting.setKey(field.getName());
            Object value = null;
            try {
                field.setAccessible(true);
                value = field.get(settingReq);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
            setting.setValue(StrUtil.toString(value));
            BaseSiteSetting setting1 = getOne(setting.getKey());
            //oldValue.add(new BaseSiteLogBO(setting1));
            if (setting1 != null) {
                setting1.setValue(setting.getValue());
                siteSettingRepository.updateByPrimaryKey(setting1);
                //newValue.add(new BaseSiteLogBO(setting1));
            } else {
                siteSettingRepository.create(setting);
                //newValue.add(new BaseSiteLogBO(setting));
            }
        }
//        baseLogAssist.recordLog(ExaminModelEnum.SYSTEM_SETTING, ExaProEnum.MODIFY, "修改店铺提醒设置",
//            settingReq.getOperationUserId(), settingReq.getOperationShopId(),
//            oldValue, newValue);
        return true;
    }

    public BaseSiteSetting getOne(String key) {
        BaseSiteSettingExample example = new BaseSiteSettingExample();
        BaseSiteSettingExample.Criteria criteria = example.createCriteria();
        criteria.andKeyEqualTo(key);
        example.setOrderByClause("id desc");
        List<BaseSiteSetting> settings = siteSettingRepository.query(example);
        if (settings.size() > 0) {
            return settings.get(0);
        }
        return null;
    }

    @Override
    public BaseShopSitSettingRes getShopSettings() {
        Field[] fields = BaseShopSitSettingRes.class.getDeclaredFields();
        BaseShopSitSettingRes settingRes = new BaseShopSitSettingRes();
        setSettingValue(fields, settingRes);
        return settingRes;
    }

    @Override
    public AppSitSettingRes getAppSettings() {
        Field[] fields = AppSitSettingRes.class.getDeclaredFields();
        AppSitSettingRes settingRes = new AppSitSettingRes();
        setSettingValue(fields, settingRes);
        return settingRes;
    }

    @Override
    public void saveProductSettings(SaveProductSettingReq req) {
        List<BaseSiteSetting> settings = new ArrayList<>();
        BaseSiteSetting auditOnOff = new BaseSiteSetting();
        auditOnOff.setKey(SiteSettingConstant.PRODUCT_AUDIT_ON_OFF);
        auditOnOff.setValue(String.valueOf(req.getProductAuditOnOff()));
        settings.add(auditOnOff);

        BaseSiteSetting saleCountOnOff = new BaseSiteSetting();
        saleCountOnOff.setKey(SiteSettingConstant.PRODUCT_SALE_COUNT_ON_OFF);
        saleCountOnOff.setValue(String.valueOf(req.getProductSaleCountOnOff()));
        settings.add(saleCountOnOff);

        LockHelper.lock(LockConstant.SAVE_PRODUCT_SETTING_LOCK, () -> {
            saveSettings(settings);
        });
    }

    @Override
    public ProductSettingResp getProductSettings() {
        List<BaseSiteSetting> setting = query(Arrays.asList(SiteSettingConstant.PRODUCT_AUDIT_ON_OFF, SiteSettingConstant.PRODUCT_SALE_COUNT_ON_OFF));
        Map<String, String> settingMap = setting.stream().collect(Collectors.toMap(BaseSiteSetting::getKey, BaseSiteSetting::getValue, (k1, k2) -> k1));
        ProductSettingResp resp = new ProductSettingResp();
        resp.setProductAuditOnOff(Boolean.parseBoolean(settingMap.getOrDefault(SiteSettingConstant.PRODUCT_AUDIT_ON_OFF, CommonConstant.FALSE_STR)));
        resp.setProductSaleCountOnOff(Boolean.parseBoolean(settingMap.getOrDefault(SiteSettingConstant.PRODUCT_SALE_COUNT_ON_OFF, CommonConstant.FALSE_STR)));
        return resp;
    }

    @Override
    @Transactional
    public Boolean saveAllAgreement(BaseAllAgreementReq req) {
        Date now = new Date();
        BaseAgreement buyer = new BaseAgreement();
        buyer.setAgreementType(AgreementTypeEnum.Buyers.getCode());
        buyer.setAgreementContent(req.getBuyerAgreementContent());
        buyer.setStatus(AgreementStatus.Normal.getCode());
        buyer.setLastUpdateTime(now);
        agreementRepository.save(buyer);

        BaseAgreement seller = new BaseAgreement();
        seller.setAgreementType(AgreementTypeEnum.Seller.getCode());
        seller.setAgreementContent(req.getSellerAgreementContent());
        seller.setStatus(AgreementStatus.Normal.getCode());
        seller.setLastUpdateTime(now);
        agreementRepository.save(seller);

        BaseAgreement privacy = new BaseAgreement();
        privacy.setAgreementType(AgreementTypeEnum.PrivacyPolicy.getCode());
        privacy.setAgreementContent(req.getPrivacyAgreementContent());
        privacy.setStatus(AgreementStatus.Normal.getCode());
        privacy.setLastUpdateTime(now);
        agreementRepository.save(privacy);

        return true;
    }

    @Override
    public String querySettingsValueByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        BaseSiteSetting baseSiteSetting = siteSettingRepository.querySettingsValueByKey(key);
        return baseSiteSetting.getValue();
    }


    @Override
    public SmsSettingRes querySmsSetting() {
        Field[] fields = SmsSettingRes.class.getDeclaredFields();
        SmsSettingRes settingRes = new SmsSettingRes();
        setSettingValue(fields, settingRes);
        return settingRes;
    }

    @Override
    public BaseResp saveSmsSetting(SmsSettingReq req) {
        List<BaseSiteSetting> settings = new ArrayList<>();

        BaseSiteSetting appKey = new BaseSiteSetting();
        appKey.setKey("smsAppKey");
        appKey.setValue(req.getSmsAppKey());
        settings.add(appKey);

        BaseSiteSetting appSecret = new BaseSiteSetting();
        appSecret.setKey("smsAppSecret");
        appSecret.setValue(req.getSmsAppSecret());
        settings.add(appSecret);

        LockHelper.lock(LockConstant.SAVE_SMS_SETTING_LOCK, () -> {
            saveSettings(settings);
        });
        return BaseResp.of();
    }

    @Override
    public BaseResp saveShopStyle(SystemStyleReq fun1) {
        BaseSiteSetting setting = new BaseSiteSetting();
        setting.setKey(fun1.getSystemStyleEnum().getKey());
        setting.setValue(fun1.getValue());
        boolean isExist = siteSettingRepository.exist(setting);
        if (isExist) {
            siteSettingRepository.update(setting);
        } else {
            siteSettingRepository.create(setting);
        }
        return BaseResp.of();
    }

    @Override
    public String queryShopStyle(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        BaseSiteSetting baseSiteSetting = siteSettingRepository.querySettingsValueByKey(key);
        if (baseSiteSetting == null) {
            return "{}";
        }
        return baseSiteSetting.getValue();
    }

    @Override
    public BaseResp saveExpressConfig(ExpressConfigReq fun1) {
        List<BaseSiteSetting> settings = new ArrayList<>();
        BaseSiteSetting key = new BaseSiteSetting();
        key.setKey("expressAppKey");
        key.setValue(fun1.getExpressAppKey());
        settings.add(key);

        BaseSiteSetting secret = new BaseSiteSetting();
        secret.setKey("expressSecret");
        secret.setValue(fun1.getExpressSecret());
        settings.add(secret);

        for (BaseSiteSetting setting : settings) {
            boolean isExist = siteSettingRepository.exist(setting);
            if (isExist) {
                siteSettingRepository.update(setting);
            } else {
                siteSettingRepository.create(setting);
            }
        }
        return BaseResp.of();
    }

    @Override
    public ExpressConfigRes queryExpressConfig() {
        Field[] fields = ExpressConfigRes.class.getDeclaredFields();
        ExpressConfigRes settingRes = new ExpressConfigRes();
        setSettingValue(fields, settingRes);
        return settingRes;
    }

    @Override
    public String queryOfficeMark(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        BaseSiteSetting baseSiteSetting = siteSettingRepository.querySettingsValueByKey(key);
        if (baseSiteSetting == null) {
            return "{}";
        }
        return baseSiteSetting.getValue();
    }

    private void setSettingValue(Field[] fields, BaseParamReq settingRes) {
        //获取所有字段名
        List<String> keys = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
        //查询所有的设置
        BaseSiteSettingExample example = new BaseSiteSettingExample();
        BaseSiteSettingExample.Criteria criteria = example.createCriteria();
        criteria.andKeyIn(keys);
        List<BaseSiteSetting> settings = siteSettingRepository.query(example);
        //settings转化为Map
        Map<String, BaseSiteSetting> settingMap = settings.stream().collect(Collectors.toMap(BaseSiteSetting::getKey, setting -> setting));
        //遍历所有的设置
        for (Field field : fields) {
            //设置值
            try {
                if (field.isSynthetic()) {
                    //针对某些情况，编译器会引入一些字段，需要过滤掉
                    continue;
                }
                field.setAccessible(true);
                String name = field.getName();
                BaseSiteSetting setting = settingMap.get(name);
                if (setting != null) {
                    field.set(settingRes, setting.getValue());
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
    }


}
