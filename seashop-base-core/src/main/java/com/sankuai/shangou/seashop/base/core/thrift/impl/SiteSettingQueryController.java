package com.sankuai.shangou.seashop.base.core.thrift.impl;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseAgreement;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomForm;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.SystemStyleEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseCustFormQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/siteSetting")
public class SiteSettingQueryController implements SiteSettingQueryFeign {
    @Resource
    private SiteSettingService settingService;


    @GetMapping(value = "/getSetting")
    @Override
    public ResultDto<BaseSitSettingRes> getSetting() throws TException {
        BaseSitSettingRes res = new BaseSitSettingRes();
        return ThriftResponseHelper.responseInvoke("getSetting", res, req -> {
            Field[] fields = res.getClass().getDeclaredFields();
            List<String> settingKeys = new ArrayList<>();

            for (Field field : fields) {
                if (field.isSynthetic()) {
                    //针对某些情况，编译器会引入一些字段，需要过滤掉
                    continue;
                }
                String name = field.getName();
                settingKeys.add(name);
            }
            log.info(JsonUtil.toJsonString(settingKeys));
            //根据keys 获取属性值
            List<BaseSiteSetting> list = settingService.query(settingKeys);
            log.info(JsonUtil.toJsonString(list));
            try {
                for (Field field : fields) {
                    field.setAccessible(true);
                    String name = field.getName();
                    if (list.stream().filter(t -> t.getKey().equals(name)).findFirst().isPresent()) {
                        BaseSiteSetting setting = list.stream().filter(t -> t.getKey().equals(name)).findFirst().get();
                        if (!setting.equals(null)) {
                            field.set(res, setting.getValue());
                        }
                    }

                }
            } catch (Exception exception) {
                throw new RuntimeException(exception);
            }
            return res;
        });
    }

    @GetMapping(value = "/getSettled")
    @Override
    public ResultDto<BaseSettledRes> getSettled() throws TException {
        long index = 0L;
        return ThriftResponseHelper.responseInvoke("getSettled", index, req -> {
            BaseSettled settled = settingService.getSettled();
            BaseSettledRes result = JsonUtil.copy(settled, BaseSettledRes.class);
            return result;
        });
    }

    @GetMapping(value = "/getAgreement")
    @Override
    public ResultDto<BaseAgreementRes> getAgreement(@RequestParam int agreementType) throws TException {
        return ThriftResponseHelper.responseInvoke("getSettled", agreementType, req -> {
            BaseAgreement agreement = settingService.getAgreement(agreementType);
            BaseAgreementRes result = JsonUtil.copy(agreement, BaseAgreementRes.class);
            return result;
        });

    }

    @GetMapping(value = "/getAllAgreement")
    @Override
    public ResultDto<BaseAllAgreementRes> getAllAgreement() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("getAllAgreement", i, req -> {
            BaseAllAgreementRes agreement = settingService.getAgreements();
            return agreement;
        });
    }

    @Override
    public ResultDto<BaseShopSitSettingRes> getShopSettings() throws TException {
        return ThriftResponseHelper.responseInvoke("getShopSettings", null, req -> settingService.getShopSettings());
    }

    @GetMapping(value = "/getCustomForm")
    @Override
    public ResultDto<BaseCustomFormRes> getCustomForm(@RequestParam long formId) throws TException {
        return ThriftResponseHelper.responseInvoke("getCustomForm", formId, req -> {
            BaseCustomForm customForm = settingService.getCustomForm(formId);
            AssertUtil.throwIfNull(customForm, "已删除，请刷新列表");
            BaseCustomFormRes result = JsonUtil.copy(customForm, BaseCustomFormRes.class);
            return result;
        });
    }

    @PostMapping(value = "/queryCustomFormWithPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<BaseCustomFormRes>> queryCustomFormWithPage(@RequestBody BaseCustFormQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCustomFormWithPage", query, req -> {
            Page<BaseCustomForm> customForms = settingService.queryCustomFormWithPage(query);
            BasePageResp<BaseCustomFormRes> result = PageResultHelper.transfer(customForms, BaseCustomFormRes.class);
            return result;
        });
    }

    @GetMapping(value = "/getAppSettings")
    @Override
    public ResultDto<AppSitSettingRes> getAppSettings() {
        return ThriftResponseHelper.responseInvoke("getAppSettings", null, req -> settingService.getAppSettings());
    }

    @GetMapping(value = "/getProductSettings")
    @Override
    public ResultDto<ProductSettingResp> getProductSettings() throws TException {
        return ThriftResponseHelper.responseInvoke("getProductSettings", null, req -> settingService.getProductSettings());
    }

    @GetMapping(value = "/querySettingsValueByKey")
    @Override
    public ResultDto<String> querySettingsValueByKey(@RequestParam String key) throws TException {
        return ThriftResponseHelper.responseInvoke("querySettingsValueByKey", key, req -> settingService.querySettingsValueByKey(req));
    }

    @GetMapping(value = "/querySmsSetting")
    public ResultDto<SmsSettingRes> querySmsSetting() throws TException {
        return ThriftResponseHelper.responseInvoke("querySmsSetting", null, req -> settingService.querySmsSetting());
    }

    @GetMapping(value = "/queryShopStyle")
    @Override
    public ResultDto<String> queryShopStyle(@RequestParam SystemStyleEnum styleEnum) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopStyle", null, req -> settingService.queryShopStyle(styleEnum.getKey()));
    }

    @GetMapping(value = "/queryExpressConfig")
    @Override
    public ResultDto<ExpressConfigRes> queryExpressConfig() throws TException {
        return ThriftResponseHelper.responseInvoke("queryExpressConfig", null, req -> settingService.queryExpressConfig());
    }

    @Override
    public ResultDto<String> queryOfficeMark(SystemStyleEnum systemStyleEnum) {
        return ThriftResponseHelper.responseInvoke("queryOfficeMark", null, req -> settingService.queryOfficeMark(systemStyleEnum.getKey()));
    }

}
