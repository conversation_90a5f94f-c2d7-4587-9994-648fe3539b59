package com.sankuai.shangou.seashop.base.core.config;

import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname WxaConfiguration
 * Description //TODO
 * @date 2023/3/21 16:27
 */
@Configuration
@Slf4j
public class WxMpConfiguration {
    private static final String WEIXINAPPID = "weixinMpAppId";
    private static final String WEIXINAPPSECRET = "weixinMpAppSecret";
    @Resource
    private SiteSettingService siteSettingService;
    @Resource
    private RedissonClient redissonClient;

    @Bean("wxMpService")
    public WxMpService wxMpService() {
        List<BaseSiteSetting> list = siteSettingService.query(Arrays.asList(WEIXINAPPID, WEIXINAPPSECRET));
        String wxAppKey = list.stream().filter(setting -> setting.getKey().equals(WEIXINAPPID)).findFirst().orElseGet(BaseSiteSetting::new).getValue();
        String wxAppSecret = list.stream().filter(setting -> setting.getKey().equals(WEIXINAPPSECRET)).findFirst().orElseGet(BaseSiteSetting::new).getValue();
        log.info("init wxa config, wxAppKey:{},wxAppSecret:{}", wxAppKey, wxAppSecret);
        WxMpService service = new WxMpServiceImpl();
        WxMpRedissonConfigImpl configStorage = new WxMpRedissonConfigImpl(redissonClient, "WXA");
        configStorage.setAppId(wxAppKey);
        configStorage.setSecret(wxAppSecret);
        configStorage.useStableAccessToken(true);
        service.setWxMpConfigStorage(configStorage);
        return service;
    }

}
