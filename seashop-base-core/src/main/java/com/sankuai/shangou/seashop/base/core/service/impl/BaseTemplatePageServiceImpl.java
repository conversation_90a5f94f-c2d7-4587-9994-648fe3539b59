package com.sankuai.shangou.seashop.base.core.service.impl;

import com.sankuai.shangou.seashop.base.common.enums.BaseTemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.core.service.BaseTemplatePageService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageKey;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs;
import com.sankuai.shangou.seashop.base.dao.core.repository.BaseTemplatePageRepository;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ClientTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class BaseTemplatePageServiceImpl implements BaseTemplatePageService {

    @Resource
    private BaseTemplatePageRepository templatePageRepository;

    @Override
    public Boolean editShopIndex(BaseTemplatePageWithBLOBs templatePageWithBLOBs) {
        BaseTemplatePageKey key =new BaseTemplatePageKey();
        key.setClient(ClientTypeEnum.Default.getCode());
        key.setType(BaseTemplateClientTypeEnum.WXSmallProgramSellerWapIndex.getCode());
        key.setShopId(templatePageWithBLOBs.getShopId());
        key.setvShopId(0L);
        BaseTemplatePageWithBLOBs model =getById(key);
        if (model ==null){
            templatePageWithBLOBs.setAddTime(new Date());
            templatePageWithBLOBs.setUpdateTime(new Date());
            templatePageRepository.create(templatePageWithBLOBs);
        }else {
            templatePageWithBLOBs.setUpdateTime(new Date());
            templatePageRepository.update(templatePageWithBLOBs);
        }
        return true ;
    }

    @Override
    public Boolean setShopIndex(BaseTemplatePageWithBLOBs templatePageWithBLOBs,BaseTemplateClientTypeEnum baseTemplateClientTypeEnum) {
        BaseTemplatePageKey key =new BaseTemplatePageKey();
        key.setClient(ClientTypeEnum.Default.getCode());
        key.setType(baseTemplateClientTypeEnum.getCode());
        key.setShopId(templatePageWithBLOBs.getShopId());
        key.setvShopId(0L);
        BaseTemplatePageWithBLOBs model =getById(key);
        if (model ==null){
            templatePageWithBLOBs.setAddTime(new Date());
            templatePageWithBLOBs.setUpdateTime(new Date());
            templatePageRepository.create(templatePageWithBLOBs);
        }else {


            templatePageWithBLOBs.setUpdateTime(new Date());
            templatePageRepository.update(templatePageWithBLOBs);
        }
        return true ;
    }
    @Override
    public BaseTemplatePageWithBLOBs getById(BaseTemplatePageKey key) {
        BaseTemplatePageWithBLOBs model =templatePageRepository.getById(key);
        return model;
    }
}
