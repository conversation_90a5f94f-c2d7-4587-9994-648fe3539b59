package com.sankuai.shangou.seashop.base.core.service;


import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MoveLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.SlideAdResp;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
public interface SlideAdService {

    /**
     * 查询限时购轮播图
     *
     * @return
     */
    List<SlideAdResp> queryLimitTimeBuy();

    /**
     * 新增限时购轮播图
     *
     * @param request
     * @return
     */
    void addLimitTimeBuy(AddLimitTimeBuyReq request);

    /**
     * 修改限时购轮播图
     *
     * @param request
     * @return
     */
    void updateLimitTimeBuy(UpdateLimitTimeBuyReq request);

    /**
     * 移动限时购轮播图
     *
     * @param request
     * @return
     */
    void moveLimitTimeBuy(MoveLimitTimeBuyReq request);

    /**
     * 删除限时购轮播图
     *
     * @param request
     * @return
     */
    void deleteLimitTimeBuy(BaseIdReq request);
}
