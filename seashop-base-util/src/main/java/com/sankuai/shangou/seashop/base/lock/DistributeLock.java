package com.sankuai.shangou.seashop.base.lock;


import com.sankuai.shangou.seashop.base.lock.model.DistributeLockEngineEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
/**
 * 分布式锁
 * <AUTHOR>
 */
public @interface DistributeLock {
    /**
     * 锁底层引擎
     *
     * @return
     */
    DistributeLockEngineEnum engine() default DistributeLockEngineEnum.SQUIRREL;
    /**
     * 锁pattern
     *
     * @return
     */
    String keyPattern();
    /**
     * 业务场景
     *
     * @return
     */
    String scenes() default "default";
    /**
     * 填充锁key的值,{0}.属性名称.属性名称，0表示参数的序号，从0开始
     *
     * @return
     */
    String[] keyValues() default {};

    /**
     * 是否等待获取锁
     *
     * @return
     */
    boolean waitLock() default false;

}