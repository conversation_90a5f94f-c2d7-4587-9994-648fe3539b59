package com.sankuai.shangou.seashop.base.export.exec;


import com.sankuai.shangou.seashop.base.export.handler.ExportTask;

import java.util.List;

/**
 * excel导出执行接口
 * <AUTHOR>
 * @date 2023/4/6
 */
public interface ExcelExportExec {

    /**
     * 获取任务列表
     * <AUTHOR>
     * @date 2023/4/13
     */
    List<ExportTask> getTaskList();

    /**
     * 开启任务时的操作，比如修改状态
     * <AUTHOR>
     * @date 2023/4/13
     */
    void start(ExportTask task);

    /**
     * 完成任务时的操作，比如修改状态
     * <AUTHOR>
     * @date 2023/4/13
     */
    void complete(ExportTask task);

    /**
     * 执行异常时的操作
     * <AUTHOR>
     * @date 2023/4/13
     */
    void exception(ExportTask task);

    /**
     * 获取重试任务列表：失败的和超时的任务
     * <AUTHOR>
     */
    List<ExportTask> getRedoTaskList();

}
