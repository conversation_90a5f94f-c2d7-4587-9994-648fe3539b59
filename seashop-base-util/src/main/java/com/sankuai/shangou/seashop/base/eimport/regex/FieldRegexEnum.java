package com.sankuai.shangou.seashop.base.eimport.regex;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/11/22 17:24
 */
public enum FieldRegexEnum {

    NONE(StringUtils.EMPTY, StringUtils.EMPTY),
    POSITIVE_INTEGER_REGEX("^[1-9]\\d*$", "请输入正整数"),
    NON_NEGATIVE_INTEGER_REGEX("^\\d+$", "请输入非负整数"),
    AMOUNT_REGEX("^(([1-9]\\d{0,9})|0)(\\.\\d{1,2})?$", "请输入正确的金额"),
    YES_OR_NO_REGEX("^(是|否)$", "请输入是或否"),
    ;

    @Getter
    private String regex;
    @Getter
    private String regexMsg;

    FieldRegexEnum(String regex, String regexMsg) {
        this.regex = regex;
        this.regexMsg = regexMsg;
    }

}
