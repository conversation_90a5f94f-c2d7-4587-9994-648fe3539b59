package com.sankuai.shangou.seashop.base.export.head;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 表头构建抽象实现类。多动态表头+多份导出字段，即每个sheet一个表头，一份独立的导出字段
 * abstr = abstract
 * S = single
 * D = dynamic
 * M = multiple
 * <AUTHOR>
 * @date 2023/4/24
 */
@Slf4j
public abstract class AbstrMDHeadMSelectBuilder implements MDHeadMSelectBuilder {

    /**
     * 最外层的List是多个sheet，内层的List<String>是sheet的表头对象字段，指定的顺序是excel表头的顺序
     * 字段是英文的，会基于ExcelProperty注解解析中文
     * 这里定义的是excel可能的全部字段，主要是为了指定顺序
     * <AUTHOR>
     * @date 2023/4/21
     */
    public abstract List<List<String>> sheetHeadFieldList();

    @Override
    public HeadFieldHolder dynamicHead(List<List<Object>> dataList, List<List<String>> selectedField) {
        log.info("当前需要导出的字段为: {}", JSONUtil.toJsonStr(selectedField));
        // 从子类获取定义的Excel表头，List<List<String>>格式
        // 最外层的List是多个sheet，内层的List<String>是sheet的表头对象字段
        List<List<String>> sheetFieldList = sheetHeadFieldList();
        // 是否所有sheet都没有自定义导出字段
        boolean nonSelected = CollUtil.isEmpty(selectedField);
        int size = 10;
        if (!nonSelected) {
            size = selectedField.size();
        }
        // 定义返回的每个sheet的head容器
        List<List<List<String>>> headField = new ArrayList<>(size);
        // 定义返回的每个sheet的需要导出的字段额 容器
        List<List<String>> sorted = new ArrayList<>(size);
        // 相当于遍历每个sheet
        for (int sheetIndex = 0; sheetIndex < sheetFieldList.size(); sheetIndex++) {
            // 当前sheet的所有表头
            List<String> allOrderField = sheetFieldList.get(sheetIndex);
            // 当前sheet用户选择需要导出的表头
            List<String> select = selectedField.get(sheetIndex);
            // 根据规则，获取当前sheet的表头
            List<List<String>> curHead = HeadConverter.convert(allOrderField, select, getObjectFieldNameMap(dataList.get(sheetIndex)));
            headField.add(curHead);
            // 如果用户没有选择字段，则导出所有字段
            if (nonSelected || CollUtil.isEmpty(select)) {
                sorted.add(allOrderField);
            } else {
                List<String> curSorted = HeadConverter.singleSortSelected(select, allOrderField);
                sorted.add(curSorted);
            }
        }
        return HeadFieldHolder.builder()
                .multiHeadList(headField)
                .multiSortedSelect(sorted)
                .build();
    }

    private Map<String, Field> getObjectFieldNameMap(List<Object> data) {
        Object obj = data.get(0);
        return ReflectUtil.getFieldMap(obj.getClass());
    }

}
