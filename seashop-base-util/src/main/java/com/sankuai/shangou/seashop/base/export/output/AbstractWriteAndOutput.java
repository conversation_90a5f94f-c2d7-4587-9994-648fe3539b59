package com.sankuai.shangou.seashop.base.export.output;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import com.sankuai.shangou.seashop.base.export.writter.ExcelWriteDelegator;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Slf4j
public abstract class AbstractWriteAndOutput<P extends BasePageReq> implements FileWriteAndOutputWay<P> {

    // excel数据写入帮助器
    private final static ExcelWriteDelegator EXCEL_WRITE_ASSIST = new ExcelWriteDelegator();

    @Override
    public String writeAndOutput(ExportDataGetter exportService, String taskId, Date taskDate, P param) {
        ByteArrayOutputStream outputStream = EXCEL_WRITE_ASSIST.writeExcel(exportService, param);
        log.info("【数据导出】任务={}，数据写入Excel完成", taskId);
        // 自定义的文件名称，由于唯一性考虑，最终的文件名称由 文件名称+taskId 组合
        String fileName = exportService.getFileName();
        log.info("【数据导出】任务={}，文件名称={}", taskId, fileName);
        // 转成字节数组进行上产
        byte[] data = outputStream.toByteArray();
        String curTimeStr = DateUtil.format(taskDate, "yyyy-MM-dd_HH_mm");
        log.info("【数据导出】任务={}，文件名称={}，时间={}", taskId, fileName, curTimeStr);
        return outputFile(data, fileName, taskId, curTimeStr);
    }

    protected abstract String outputFile(byte[] data, String fileName, String taskId, String timeStr);

}
