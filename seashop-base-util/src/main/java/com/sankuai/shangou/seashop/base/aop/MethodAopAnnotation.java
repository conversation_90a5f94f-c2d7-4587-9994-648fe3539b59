package com.sankuai.shangou.seashop.base.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @author: lhx
 * @date: 2024/3/9/009
 * @description:
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MethodAopAnnotation {

    // 默认几秒
    int timeOut() default 2;

    // 默认参数名
    String paramName();
}
