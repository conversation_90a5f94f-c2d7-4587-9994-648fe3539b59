package com.sankuai.shangou.seashop.base.export.handler;

import lombok.Data;

import java.util.Date;

/**   
 * 数据导出任务对象
 */

@Data
public class ExportTask {
    
    /**
     * 主键id
     */
	private String taskId;
    /**
     * 任务类型
     */
	private Integer taskType;
    /**
     * 任务参数内容
     */
	private String executeParam;
	/**
	 * 执行耗时
	 */
	private int cost;
	/**
	 * 执行异常信息
	 */
	private String exception;
	/**
	 * 记录数
	 */
	private int count;
	/**
	 * 上传到nfs的文件路径，不包括域名
	 */
	private String filePath;
	/**
	 * 任务开始时间
	 */
	private Date taskDate;

}
