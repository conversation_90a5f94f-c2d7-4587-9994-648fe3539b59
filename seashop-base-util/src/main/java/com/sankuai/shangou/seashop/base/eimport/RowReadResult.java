package com.sankuai.shangou.seashop.base.eimport;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
public class RowReadResult {

    /**
     * 当前行号索引
     */
    @ExcelIgnore
    private Integer rowNum;
    /**
     * 当前行的错误提示
     */
    @ExcelProperty(value = "错误原因")
    private String errMsg;

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public Integer getRowNum() {
        return rowNum;
    }

    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }
}
