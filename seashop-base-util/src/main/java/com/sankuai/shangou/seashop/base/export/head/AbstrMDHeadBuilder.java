package com.sankuai.shangou.seashop.base.export.head;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.utils.ReflectUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 表头构建抽象实现类。多动态表头
 * abstr = abstract
 * S = single
 * D = dynamic
 * M = multiple
 * <AUTHOR>
 * @date 2023/4/20
 */
@Slf4j
public abstract class AbstrMDHeadBuilder implements DHeadSSelectBuilder {

    /**
     * 最外层的List是多个sheet，内层的List<String>是sheet的表头对象字段，指定的顺序是excel表头的顺序
     * 字段是英文的，会基于ExcelProperty注解解析中文
     * 这里定义的是excel可能的全部字段，主要是为了指定顺序
     * <AUTHOR>
     * @date 2023/4/21
     */
    public abstract List<List<String>> sheetHeadFieldList();

    @Override
    public HeadFieldHolder dynamicHead(List<String> selectedField) {
        // 根据规则，响应对象是实现的第一个接口的第一个泛型
        Class responseClz = ReflectUtil.getInterfaceGenericClass(this, 0, 0);
        // 获取响应对象，即EasyExcel模板的字段属性
        Field[] fieldArr = responseClz.getDeclaredFields();
        // 将响应对象的字段转成Map格式，方便取值判断
        Map<String, Field> fieldMap = Arrays.stream(fieldArr).collect(Collectors.toMap(Field::getName, Function.identity()));
        // 从子类获取定义的Excel表头，List<List<String>>格式
        // 最外层的List是多个sheet，内层的List<String>是sheet的表头对象字段
        List<List<String>> sheetFieldList = sheetHeadFieldList();
        log.info("当前需要导出的字段为: {}", JSONUtil.toJsonStr(selectedField));
        List<List<List<String>>> headField = sheetFieldList.stream()
                // Head转换，基于需要导出的字段，从类的字段属性中获取ExcelProperty定义的表头名称
                .map(HeadConverter.convertFunction(selectedField, fieldMap))
                .collect(Collectors.toList());
        List<List<String>> sorted = HeadConverter.sortSelected(selectedField, sheetFieldList);
        return HeadFieldHolder.builder()
                .multiHeadList(headField)
                .multiSortedSelect(sorted)
                .build();
    }

}
