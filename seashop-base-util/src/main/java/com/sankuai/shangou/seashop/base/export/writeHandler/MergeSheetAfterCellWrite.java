package com.sankuai.shangou.seashop.base.export.writeHandler;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTMergeCell;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTMergeCells;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTWorksheet;

import com.alibaba.excel.context.WriteContext;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.export.context.SheetMergeRegionHolder;
import com.sankuai.shangou.seashop.base.export.model.RowMergeRegion;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 单元格合并处理器
 * <pre>
 *     POI底层在合并单元格的时候，通常是用sheet.addMergedRegionUnsafe()方法，这个方法虽然不会做任何校验，但底层会锁住sheet，导致并发性能下降；
 *     参考底层逻辑代码，把锁去掉，然后自己实现合并单元格的逻辑，这样就可以实现并发合并单元格；
 *     基本思路是通过一个标识来判断，当前sheet是否已经有过合并了，如果已经有过合并了，则直接获取合并的单元格集合：ctWorksheet.getMergeCells()；
 *     否则，新建一个合并单元格集合：ctWorksheet.addNewMergeCells()，然后把合并单元格集合放到sheet的合并单元格集合中：CTMergeCell ctMergeCell = ctMergeCells.addNewMergeCell()；
 *     本代码的实现逻辑，是遍历每个sheet以及需要合并的单元格区域，放到线程池执行，所以获取到第一个要合并的区域时，直接新增合并，后面的，就可以都获取到合并单元格集合，然后往里面添加即可；
 * </pre>
 * <AUTHOR>
 */
@Slf4j
public class MergeSheetAfterCellWrite {

    // 定义合并单元格使用的线程池
    private final static ThreadPoolExecutor THREAD_POOL = generateThreadPool();

    /**
     * ThreadPool 线程池支持链路追踪
     * <AUTHOR>
     */
    private static ThreadPoolExecutor generateThreadPool() {
        int num = Runtime.getRuntime().availableProcessors();

        return new ThreadPoolExecutor(
            num, num * 5, 30, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100000),
            r -> {
                Thread thread = new Thread(r);
                thread.setName("excel-merge-task-");
                return thread;
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
        );

//        DefaultThreadPoolProperties.Setter setter = DefaultThreadPoolProperties.Setter()
//                .withCoreSize(num)
//                .withMaxSize(num * 5)
//                .withKeepAliveTimeMinutes(30)
//                .withKeepAliveTimeUnit(TimeUnit.SECONDS)
//                .withBlockingQueue(new LinkedBlockingQueue<>())
//                // 对于几十万数据的情况，需要合并的单元格会很多，所以阻塞队列设置的比较大，内存需要考虑
//                .withMaxQueueSize(100000)
//                .withThreadFactory(new ThreadFactoryBuilder().setNameFormat("excel-merge-task-").build())
//                .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        return Rhino.newThreadPool(ThreadPoolConst.RHINO_KEY_EXPORT_TASK_MERGE_CELL, setter);
    }

    /**
     * 执行单元格合并
     * <p>需要合并的单元格是在单元格写入时，根据业务数据以及配置，保存起来的；
     * 此时的sheet数据已经全部写入完成了，需要合并的区域也都已经确定了，所以在执行单元格合并的时候，可以将每个需要合并的单元格区域放到线程池处理</p>
     * <AUTHOR>
     * @date 2023/8/31
     */
    public static void doMerge(WriteContext context, String sheetName) {
        Map<Integer/*列索引*/, List<RowMergeRegion>/*每列需要合并的多个行区域*/> rowMergeRegion = SheetMergeRegionHolder.getSheetMergeRegion(sheetName);
        if (rowMergeRegion == null || rowMergeRegion.isEmpty()) {
            return;
        }
        // 获取工作sheet
        CTWorksheet ctWorksheet = getWorkSheet(context, sheetName);
        // 初始化sheet的merge
        initSheetMergeAndRemoveThis(ctWorksheet, rowMergeRegion);

        // 遍历每个sheet，处理单元格合并，第一个初始化的区域在初始化的时候已经从集合中删除掉了
        iterateMultiThreadMerge(ctWorksheet, rowMergeRegion);
    }


    /**
     * 初始化sheet的单元格合并，为了提高性能，每个sheet只创建一个合并对象
     * 需要注意，这个方式，舍弃了其他很多校验等处理，单纯为了提高性能
     * <AUTHOR>
     */
    private static void initSheetMergeAndRemoveThis(CTWorksheet ctWorksheet,
                                                    Map<Integer/*列索引*/, List<RowMergeRegion>/*每列需要合并的多个行区域*/> rowMergeRegion) {
        // 因为是map，不好取某一个值，所以也进行遍历，初始化合并后直接退出
        for (Map.Entry<Integer, List<RowMergeRegion>> et : rowMergeRegion.entrySet()) {
            if (CollUtil.isEmpty(et.getValue())) {
                continue;
            }
            // 列索引
            int curColIndex = et.getKey();
            // 当前列需要合并的区域信息
            List<RowMergeRegion> goalRegionList = et.getValue();
            // 如果当前sheet还没有初始化merge，则进行初始化，这个目的是减少合并时的一些判断，一个sheet只初始化一次
            RowMergeRegion firstRegion = goalRegionList.get(0);
            log.info("【合并单元格】第一个合并的单元格为, curColIndex={}, region: {}", curColIndex, JSONUtil.toJsonStr(firstRegion));

            // 第一个需要合并的单元格，单独拿出来处理，新增合并集合
            CTMergeCells ctMergeCells = ctWorksheet.addNewMergeCells();
            CTMergeCell ctMergeCell = ctMergeCells.addNewMergeCell();

            // 需要合并的区域
            CellRangeAddress firstRangeAddress = new CellRangeAddress(firstRegion.getFromRowIndex(), firstRegion.getToRowIndex(), curColIndex, curColIndex);
            log.info("【合并单元格】第一个合并的单元格区域为, curColIndex={}, region: {}", curColIndex, firstRangeAddress.formatAsString());
            ctMergeCell.setRef(firstRangeAddress.formatAsString());

            // 把第一个已经合并的单元格从目标区域删除掉，后续的遍历不用再处理这个区域了
            goalRegionList.remove(0);
            break;
        }
    }

    /**
     * 遍历所有需要合并的区域，多线程设置合并，此时所有需要合并的区域已经确定了，sheet也写完了，所以可以多线程
     * <AUTHOR>
     * @date 2023/9/5
     */
    private static void iterateMultiThreadMerge(CTWorksheet ctWorksheet,
                                                Map<Integer/*列索引*/, List<RowMergeRegion>/*每列需要合并的多个行区域*/> rowMergeRegion) {

        int totalMergeCount = rowMergeRegion.values().stream().mapToInt(list -> CollUtil.isEmpty(list) ? 0 : list.size()).sum();
        log.info("【合并单元格】当前需要合并的区域个数为: {}", totalMergeCount);
        if (totalMergeCount <= 0) {
            return;
        }
        CountDownLatch countDownLatch = new CountDownLatch(totalMergeCount);
        // 遍历每个sheet，处理单元格合并
        for (Map.Entry<Integer, List<RowMergeRegion>> et : rowMergeRegion.entrySet()) {
            // 列索引
            int curColIndex = et.getKey();
            // 当前列需要合并的区域信息
            List<RowMergeRegion> goalRegionList = et.getValue();
            log.debug("【合并单元格】当前列需要合并的单元格区域为, curColIndex={}, regionList: {}", curColIndex, JSONUtil.toJsonStr(goalRegionList));
            if (CollUtil.isEmpty(goalRegionList)) {
                continue;
            }
            // 遍历当前sheet所有需要合并的区域
            for (RowMergeRegion region : goalRegionList) {
                THREAD_POOL.execute(new MergeTask(countDownLatch, ctWorksheet, region, curColIndex));
            }
        }
        try {
            countDownLatch.await();
            log.info("【合并单元格】合并完毕");
        } catch (InterruptedException e) {
            log.error("等待合并单元格结束异常", e);
            throw new BusinessException("等待合并单元格结束异常");
        }
    }

    private static CTWorksheet getWorkSheet(WriteContext context, String sheetName) {
        // 获取到当前sheet的工作簿，目前只支持07版的excel(xlsx)
        SXSSFWorkbook workbook = (SXSSFWorkbook)(context.writeWorkbookHolder().getWorkbook());
        XSSFWorkbook xssfWorkbook = workbook.getXSSFWorkbook();
        XSSFSheet sheet = xssfWorkbook.getSheet(sheetName);
        return sheet.getCTWorksheet();
    }


    @Slf4j
    static class MergeTask implements Runnable {

        // 多线程同步计数器，用于确认所有的需要合并的区域都处理完成
        private final CountDownLatch countDownLatch;
        // 当前工作sheet
        private final CTWorksheet ctWorksheet;
        // 需要合并的行区域
        private final RowMergeRegion region;
        // 当前处理的列
        private final int curColIndex;

        public MergeTask(CountDownLatch countDownLatch, CTWorksheet ctWorksheet, RowMergeRegion region, int curColIndex) {
            this.countDownLatch = countDownLatch;
            this.ctWorksheet = ctWorksheet;
            this.region = region;
            this.curColIndex = curColIndex;
        }

        @Override
        public void run() {
            CellRangeAddress cellRangeAddress = new CellRangeAddress(region.getFromRowIndex(), region.getToRowIndex(), curColIndex, curColIndex);
            log.debug("【合并单元格】当前合并的单元格为: {}", JSONUtil.toJsonStr(cellRangeAddress.formatAsString()));

            CTMergeCells ctMergeCells =  ctWorksheet.getMergeCells();
            CTMergeCell ctMergeCell = ctMergeCells.addNewMergeCell();

            ctMergeCell.setRef(cellRangeAddress.formatAsString());

            countDownLatch.countDown();
        }
    }

}
