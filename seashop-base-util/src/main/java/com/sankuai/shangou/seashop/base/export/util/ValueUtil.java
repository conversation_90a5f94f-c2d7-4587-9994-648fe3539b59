package com.sankuai.shangou.seashop.base.export.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.DateUtil;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Slf4j
public class ValueUtil {

    public static String getCellValueString(Cell cell) {
        switch (cell.getCellType()) {
            case BOOLEAN:
                log.debug("【获取单元格数据-Boolean】value={}", cell.getBooleanCellValue());
                return CellValue.valueOf(cell.getBooleanCellValue()).formatAsString();
            case ERROR:
                log.debug("【获取单元格数据-ERROR】value={}", cell.getErrorCellValue());
                return CellValue.getError(cell.getErrorCellValue()).formatAsString();
            case NUMERIC:
                double numericCellValue = cell.getNumericCellValue();
                log.debug("【获取单元格数据-NUMERIC】value={}", numericCellValue);
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cn.hutool.core.date.DateUtil.formatLocalDateTime(DateUtil.getLocalDateTime(numericCellValue));
                } else {
                    return String.valueOf(numericCellValue);
                }
            case STRING:
                log.debug("【获取单元格数据-STRING】value={}", cell.getRichStringCellValue());
                return cell.getRichStringCellValue().getString().replaceAll("\\^l", "");
            case BLANK:
                log.debug("【获取单元格数据-BLANK】");
                return "";
            default:
                throw new RuntimeException("无法识别的单元格类型");
        }
    }

}
