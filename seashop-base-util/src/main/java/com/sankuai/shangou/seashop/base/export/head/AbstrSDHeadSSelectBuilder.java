package com.sankuai.shangou.seashop.base.export.head;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.utils.ReflectUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 表头构建抽象实现类。单动态表头+单导出字段
 * abstr = abstract
 * S = single
 * D = dynamic
 * <AUTHOR>
 * @date 2023/4/21
 */
@Slf4j
public abstract class AbstrSDHeadSSelectBuilder implements DHeadSSelectBuilder {

    /**
     * List<String>是sheet的表头对象字段，指定的顺序是excel表头的顺序
     * 字段是英文的，会基于ExcelProperty注解解析中文
     * <AUTHOR>
     * @date 2023/4/21
     */
    public abstract List<String> sheetHeadFieldList();

    @Override
    public HeadFieldHolder dynamicHead(List<String> selectedField) {
        log.info("当前需要导出的字段为: {}", JSONUtil.toJsonStr(selectedField));
        // 根据规则，响应对象是实现的第一个接口的第一个泛型
        Class responseClz = ReflectUtil.getInterfaceGenericClass(this, 0, 0);
        // 获取响应对象，即EasyExcel模板的字段属性
        Field[] fieldArr = cn.hutool.core.util.ReflectUtil.getFields(responseClz);
        // 将响应对象的字段转成Map格式，方便取值判断
        Map<String, Field> fieldMap = Arrays.stream(fieldArr).collect(Collectors.toMap(Field::getName, Function.identity()));
        // 从子类获取定义的Excel表头，List<String>格式
        // List<String>是sheet的表头对象字段
        List<String> sheetFieldList = sheetHeadFieldList();
        // Head转换，从字段属性中获取ExcelProperty定义的表头名称
        List<List<String>> headList = HeadConverter.convert(sheetFieldList, selectedField, fieldMap);
        // 如果没有选择导出的字段，则默认用业务定义的所有字段导出，因为实际不会既要导出又不选择字段
        if (CollUtil.isEmpty(selectedField)) {
            return HeadFieldHolder.builder()
                    .singleHeadList(headList)
                    .singleSortedSelect(sheetFieldList)
                    .build();
        }
        // 根据字段顺序，对选择的字段进行处理，变成顺序与字段顺序一致
        List<String> sortedSelect = HeadConverter.singleSortSelected(selectedField, sheetFieldList);
        return HeadFieldHolder.builder()
                .singleHeadList(headList)
                .singleSortedSelect(sortedSelect)
                .build();
    }
}
