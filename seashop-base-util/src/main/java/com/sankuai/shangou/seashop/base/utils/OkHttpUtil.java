package com.sankuai.shangou.seashop.base.utils;

import okhttp3.OkHttpClient;

import java.util.concurrent.TimeUnit;

/**
 * @author： liweisong
 * @create： 2023/12/1 18:02
 */
public class OkHttpUtil {

    private static volatile OkHttpClient singleton;
    public static final Integer  DEFAULT_TIME_OUT= 7;

    private OkHttpUtil() {
    }

    public static OkHttpClient getInstance() {
        if (singleton == null) {
            synchronized (OkHttpUtil.class) {
                if (singleton == null) {
                    singleton = new OkHttpClient()
                            .newBuilder()
                            .connectTimeout(DEFAULT_TIME_OUT, TimeUnit.SECONDS)
                            .readTimeout(DEFAULT_TIME_OUT, TimeUnit.SECONDS)
                            .writeTimeout(DEFAULT_TIME_OUT, TimeUnit.SECONDS)
                            .build();
                }
            }
        }
        return singleton;
    }

}
