package com.sankuai.shangou.seashop.base.eimport;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import lombok.Data;

/**
 * Excel读取结果
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
@Data
public class ReadResult<D extends RowReadResult> {

    /**
     * excel的数据是否有错误，针对能直接从excel数据判断的，比如缺失，重复等
     */
    private boolean hasErr = false;
    /**
     * 包含有原始数据和每行错误信息的数据列表
     */
    private List<D> dataList;

    public List<D> getSuccessDataList() {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.EMPTY_LIST;
        }
        return dataList.stream().filter(item -> StringUtils.isEmpty(item.getErrMsg())).collect(Collectors.toList());
    }

    public List<D> getErrDataList() {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.EMPTY_LIST;
        }
        return dataList.stream().filter(item -> StringUtils.isNotEmpty(item.getErrMsg())).collect(Collectors.toList());
    }

}
