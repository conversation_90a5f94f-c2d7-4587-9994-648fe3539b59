package com.sankuai.shangou.seashop.base.export.writeHandler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.sankuai.shangou.seashop.base.export.util.ValueUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 单元格数据格式化处理器，比如设置数值千分位
 * <p>当前处理器只支持对象添加了@ExcelProperty属性的</p>
 * <p>当前一个属性只支持一种格式化</p>
 * <AUTHOR>
 * @date 2023/8/25
 */
@Slf4j
public class ValueFormatWriteHandler implements CellWriteHandler {

    /**
     * 根据构造函数传入的数据生成的需要进行格式化的列和其map
     */
    private final Map<Integer, String> formatColumnMap;
    /**
     * 普通字段的数量，即添加了@ExcelProperty注解的字段数量，用于处理Map类型的字段时知道从哪里开始
     */
    private int commonFieldCount;
    /**
     * 如果数据量大的时候，poi不支持生成超过一定数量的样式，
     * 比如异常：The maximum number of Cell Styles was exceeded. You can define up to 64000 style in a .xlsx Workbook，
     * 所以需要缓存样式，key为同格式化的格式，value为样式
     */
    private Map<String, CellStyle> CELL_STYLE_CACHE = new HashMap<>();

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> ValueFormatWriteHandler(Class<R> excelClz, List<String> selectedFieldList) {
        formatColumnMap = new HashMap<>(16);
        Field[] fieldArr = ReflectUtil.getFields(excelClz);
        Field[] exportFieldArr = null;
        if (CollUtil.isEmpty(selectedFieldList)) {
            exportFieldArr = fieldArr;
        } else {
            exportFieldArr = Arrays.stream(fieldArr).filter(field -> selectedFieldList.contains(field.getName())).toArray(Field[]::new);
        }
        // 筛选出添加了@ExcelProperty注解的字段，这样才好排除用不到的列，否则列索引会可能对应不上
        Field[] annoArr = Arrays.stream(exportFieldArr)
                .filter(field -> field.getAnnotation(ExcelProperty.class) != null).toArray(Field[]::new);
        // 遍历当前最终的表头
        for (int i = 0; i < annoArr.length; i++) {
            Field field = annoArr[i];
            // 如果添加@NumberFormat，说明需要进行数值类型的格式化
            NumberFormat numberFormat = field.getAnnotation(NumberFormat.class);
            if (numberFormat != null) {
                formatColumnMap.put(i, numberFormat.value());
            }
        }
        commonFieldCount = annoArr.length;
        log.info("【单元格格式-初始化格式策略】mergerColIndexMap={}", JSONUtil.toJsonStr(formatColumnMap));
    }

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * @param firstRowObj 导出的对象的第一行数据，用于获取map类型的字段，注意，必须是LinkedHashMap类型的字段
     * @param customFormatField 自定义的格式化字段，key是map字段名称，同一个导出对象中不同格式化类型的map要分开，value是格式化类型，比如：#0.00%
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> ValueFormatWriteHandler(Class<R> excelClz, List<String> selectedFieldList, R firstRowObj, Map<String, String> customFormatField) {
        this(excelClz, selectedFieldList);
        if (firstRowObj == null) {
            return;
        }
        // 索引是从0开始，所以下一列的索引就是当前普通字段的数量
        int colIndex = commonFieldCount;
        // 遍历当前对象的所有字段，找到LinkedHashMap类型的字段，并解析出对应的key作为列设置格式化
        for (Field field : ReflectUtil.getFields(excelClz)) {
            if (field.getType().equals(LinkedHashMap.class)) {
                field.setAccessible(true);
                try {
                    LinkedHashMap linkedHashMap = (LinkedHashMap) field.get(firstRowObj);
                    // 约定的map类型，key是字段名称，value是字段值，遍历map，每个key就是一列，设置格式化类型
                    // 即使当前map不需要格式化，也要遍历，将列索引累加
                    boolean needFormat = customFormatField.containsKey(field.getName());
                    for (Object key : linkedHashMap.keySet()) {
                        if (needFormat) {
                            formatColumnMap.put(colIndex, customFormatField.get(field.getName()));
                        }
                        colIndex++;
                    }
                } catch (IllegalAccessException e) {
                    log.error("【单元格格式-初始化格式策略】解析LinkedHashMap类型字段异常", e);
                }
            }
        }
        log.info("【单元格格式-初始化格式策略】需要格式化的单元格包括: {}", JSONUtil.toJsonStr(formatColumnMap));
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        if (context.getHead()) {
            return;
        }
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        Workbook workbook = sheet.getWorkbook();
        Cell cell = context.getCell();
        int colIndex = cell.getColumnIndex();
        int rowIndex = context.getRowIndex();
        boolean needFormat = formatColumnMap.containsKey(colIndex);
        String cellValue = ValueUtil.getCellValueString(cell);
        log.debug("【单元格格式-处理单元格】rowIndex={}, colIndex={},needFormat={}, cellValue={}",
                rowIndex, colIndex, needFormat, ValueUtil.getCellValueString(cell));
        // 没数据设置格式化没意义，而且这样可以让设置格式化在获取合并单元格之后执行，可以少设置一些
        if (needFormat && StrUtil.isNotBlank(cellValue)) {
            String pattern = formatColumnMap.get(colIndex);
            log.debug("【单元格格式-处理单元格】pattern={}", pattern);
            if (StrUtil.isBlank(pattern)) {
                return;
            }
            if (CELL_STYLE_CACHE.containsKey(pattern)) {
                log.debug("【单元格格式-处理单元格】从缓存中获取样式pattern={}", pattern);
                cell.setCellStyle(CELL_STYLE_CACHE.get(pattern));
            } else {
                CellStyle style = workbook.createCellStyle();
                DataFormat dataFormat = workbook.createDataFormat();
                style.setDataFormat(dataFormat.getFormat(pattern));
                cell.setCellStyle(style);
                CELL_STYLE_CACHE.put(pattern, style);
            }
        }
        log.debug("【单元格格式-处理单元格】设置完格式后rowIndex={}, colIndex={},cellValue={}",
                rowIndex, colIndex, ValueUtil.getCellValueString(cell));
    }

}
