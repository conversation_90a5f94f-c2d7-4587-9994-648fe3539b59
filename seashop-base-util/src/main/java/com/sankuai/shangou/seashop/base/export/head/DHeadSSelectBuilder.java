package com.sankuai.shangou.seashop.base.export.head;

import java.util.List;

/**
 * 单份导出字段的动态表头构造
 * abstr = abstract
 * M = multiple
 * D = dynamic
 * S = single
 * <AUTHOR>
 * @date 2023/4/20
 */
public interface DHeadSSelectBuilder {

    /**
     * 根据需要动态生成表头
     * <b>Important: 目前的规则是，指定需要导出的列时，必须是param对象里数据类型为字符串数组，且字段名称为 selectedFieldList，否则无法识别 </b>
     * @param selectedField 动态表头时，用户选择的当前需要导出的字段
     * <AUTHOR>
     * @date 2023/4/20
     */
    HeadFieldHolder dynamicHead(List<String> selectedField);

}
