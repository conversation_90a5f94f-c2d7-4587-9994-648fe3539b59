package com.sankuai.shangou.seashop.base.export.writter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterTableBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.sankuai.shangou.seashop.base.export.context.ExportTaskContextHolder;
import com.sankuai.shangou.seashop.base.export.handler.AbstrCustomCycleTableGetter;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import com.sankuai.shangou.seashop.base.export.model.CustomTableWrapper;
import com.sankuai.shangou.seashop.base.export.model.CycleTableContext;
import com.sankuai.shangou.seashop.base.export.model.TableWrapper;
import com.sankuai.shangou.seashop.base.export.util.SheetNameUtil;
import com.sankuai.shangou.seashop.base.export.writeHandler.ColumnRowMergeWriteHandler;
import com.sankuai.shangou.seashop.base.export.writeHandler.DefaultHeadStyle;
import com.sankuai.shangou.seashop.base.export.writeHandler.MergeSheetAfterCellWrite;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Slf4j
public class CustomCycleTableWriteStgy <P extends BasePageReq> extends SSheetCycleTableWriteStgy<P> {

    @Override
    public ByteArrayOutputStream writeToOutputStream(ExportDataGetter exportService, P param) {
        log.info("【数据导出】定制-单sheet+循环表格数据导出");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        // 基于EasyExcel将获取到的数据写到excel，并将excel上传到文件服务器，后续用户从页面通过文件服务器的地址获取文件
        AbstrCustomCycleTableGetter currentGetter = (AbstrCustomCycleTableGetter)exportService;
        CycleTableContext tableContext = currentGetter.selectData(param);
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream).registerWriteHandler(DefaultHeadStyle.getInstance()).build()) {
            // 定义当前的sheet，首先指定不需要表头，如果需要表头，WriteTable单独指定
            String sheetName = SheetNameUtil.getSheetName(tableContext.getSheetName(), 0);
            WriteSheet writeSheet = EasyExcel.writerSheet()
                    .sheetName(sheetName)
                    .needHead(false)
                    .build();
            // 获取当前需要输出的所有表格，每个表格可以认为就是一个小的sheet的内容，可以单独自定义格式，表头等
            List<TableWrapper> tableList = tableContext.getTableList();
            if (CollUtil.isEmpty(tableList)) {
                return outputStream;
            }
            int mergeFromRow = 0;
            for (int i = 0; i < tableList.size(); i++) {
                TableWrapper tableWrapper = tableList.get(i);
                // 构造当前的数据表格
                ExcelWriterTableBuilder tableBuilder = EasyExcel.writerTable(i);
                // 如果需要表头，则指定表头类型，基于数据类型反射获取
                if (tableWrapper.needHead()) {
                    tableBuilder.needHead(true)
                            .head(ClassUtil.getTypeArgument(tableWrapper.getClass()));
                } else {
                    tableBuilder.needHead(false);
                }
                // 注册数据输出处理器，作用是单元格样式等处理
                List<WriteHandler> writeHandlerList = tableWrapper.getWriteHandlerList();
                if (CollUtil.isNotEmpty(writeHandlerList)) {
                    writeHandlerList.forEach(x -> tableBuilder.registerWriteHandler(x));
                }
                // 如果是定制的表格，需要合并单元格
                if (tableWrapper instanceof CustomTableWrapper) {
                    CustomTableWrapper ctw = (CustomTableWrapper)tableList.get(i);
                    Boolean shouldMerge = ctw.shouldMerge();
                    int lastColumnIndex = ctw.getLastColumnIndex();
                    if (shouldMerge) {
                        tableBuilder.registerWriteHandler(new ColumnRowMergeWriteHandler(mergeFromRow, mergeFromRow, 0, lastColumnIndex));
                    }
                }
                List<?> dataList = tableWrapper.getDataList(param);
                if (dataList == null) {
                    dataList = Collections.emptyList();
                }
                ExportTaskContextHolder.updateTotalCount(dataList.size());
                // 单元格行数累加
                mergeFromRow = mergeFromRow + dataList.size();
                // 表头需要额外加一行
                if (tableWrapper.needHead()) {
                    mergeFromRow = mergeFromRow + 1;
                }
                WriteTable writeTable = tableBuilder.build();
                // 将当前的table数据写到sheet
                excelWriter.write(dataList, writeSheet, writeTable);
            }
            MergeSheetAfterCellWrite.doMerge(excelWriter.writeContext(), sheetName);
        }
        return outputStream;
    }

}
