package com.sankuai.shangou.seashop.base.export.handler;

import com.sankuai.shangou.seashop.base.export.template.TemplateDataWrapper;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

/**
 * 单数据列表的基于模板处理的数据获取器
 * <AUTHOR>
 * @date 2023/5/6
 */
public interface SingleTempDataGetter<R, P extends BasePageReq> extends SingleSheetDataGetter {

    TemplateDataWrapper<R> buildTemplateData(P param);

}
