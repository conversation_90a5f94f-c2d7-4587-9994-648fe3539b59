package com.sankuai.shangou.seashop.base.export.head;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.util.DynamicBeanUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/19
 */
@Slf4j
public abstract class AbstrBaseHeadBuilder implements BaseHeadBuilder {

    private final static Object VALUE_OBJ = new Object();

    /**
     * 解析表头
     * <p>导出类型中，ExcelProperty的顺序为表头的顺序，用户可以根据代码逻辑和页面选择需要导出的字段，字段名称需要与对象的属性名称保持一致</p>
     * @param currentDataClz 当前需要导出的数据类
     * @param dataObj 之所以需要对象，是业务上可能对象里面通过map传入需要导出的字段名，此时需要从对象获取map的数据
     * @param selectedFieldList 用户选择的需要导出的字段，包括根据业务逻辑生成的字段
     * <AUTHOR>
     * @date 2023/8/7
     */
    @Override
    public HeadFieldHolder buildHead(Object dataObj, Class<?> currentDataClz, List<String> selectedFieldList) {
        // 获取对象的所有属性，并过滤掉对象属性中的合成字段
        // @link https://www.eclemma.org/jacoco/trunk/doc/faq.html My code uses reflection. Why does it fail when I execute it with JaCoCo?
        Field[] fieldArr = ReflectUtil.getFields(currentDataClz, field -> !field.isSynthetic());
        List<List<String>> headList = CollUtil.newArrayList();

        List<String> headField = CollUtil.newArrayList();
        Map<String, Object> selectedFieldMap = getSelectedOrAllFieldMap(selectedFieldList, fieldArr);
        int offset = -1;
        // 遍历导出对象的所有字段，字段顺序默认为表头的顺序 TODO 后续可以支持ExcelProperty的index属性
        for (int i = 0; i < fieldArr.length; i++) {
            Field field = fieldArr[i];
            if (!selectedFieldMap.containsKey(field.getName())) {
                continue;
            }
            if (field.getType().isAssignableFrom(Map.class) || field.getType().isAssignableFrom(LinkedHashMap.class)) {
                // 如果是Map类型，则map的key作为表头和表头列，需要是 linkedHashMap 类型，保证顺序
                LinkedHashMap mapValue = (LinkedHashMap) ReflectUtil.getFieldValue(dataObj, field);
                if (CollUtil.isNotEmpty(mapValue)) {
                    List<String> mapKeyList = CollUtil.newArrayList(mapValue.keySet());
                    mapKeyList.stream().forEach(x -> {
                        headList.add(Collections.singletonList(x));
                        headField.add(x);
                    });
                }
                // 如果是map类型，且是第一项，则设置以下offset，用于后面调整字段顺序
                // 因为子类继承父类的话，获取子类的字段时，索引的第一项会是子类的字段
                if (i == 0) {
                    offset = 0;
                }
            } else {
                // 标明了忽略的则不需要设置到头
                ExcelIgnore excelIgnore = field.getDeclaredAnnotation(ExcelIgnore.class);
                if (excelIgnore != null) {
                    continue;
                }
                // 从注解获取表头名称，如果没有，则默认为字段名称
                ExcelProperty excelProperty = field.getDeclaredAnnotation(ExcelProperty.class);
                List<String> headName = null;
                if (excelProperty != null) {
                    //headName = Collections.singletonList(excelProperty.value()[0]);
                    headName = Arrays.stream(excelProperty.value()).map(x -> x).collect(Collectors.toList());
                } else {
                    headName = Collections.singletonList(field.getName());
                }
                // 如果offset大于等于0，将非map的类型放到最前面
                if (offset >= 0) {
                    headList.add(offset, headName);
                    headField.add(offset, field.getName());
                    offset = offset + 1;
                } else {
                    headList.add(headName);
                    headField.add(field.getName());
                }
            }
        }
        return HeadFieldHolder.builder()
                .singleHeadList(headList)
                .singleSortedSelect(headField)
                .build();
    }

    public <R> void writeToSheet(ExcelWriter excelWriter, WriteSheet writeSheet,
                                 List<String> headField,
                                 List<R> list, Class responseClz) {
        // 重新构建数据
        long start = System.currentTimeMillis();
        List<List<Object>> newList = regenerateDataOrder(list, responseClz, headField);
        log.debug("重建完数据, cost={}, data: {}", System.currentTimeMillis() - start, JSONUtil.toJsonStr(newList));
        excelWriter.write(newList, writeSheet);
    }

    public <R> void writeToSheet(ExcelWriter excelWriter, String sheetName,
                                        List<List<String>> head, List<String> headField,
                                        List<R> list, Class responseClz,
                                        List<WriteHandler> writeHandlerList) {
        ExcelWriterSheetBuilder builder = EasyExcel.writerSheet(sheetName)
                // 根据表头属性列指定当前导出的数据列
                .includeColumnFieldNames(headField)
                // 指定表头
                .head(head);
        if (CollUtil.isNotEmpty(writeHandlerList)) {
            writeHandlerList.stream().forEach(x -> builder.registerWriteHandler(x));
        }
        WriteSheet writeSheet = builder.build();
        // 重新构建数据
        List<List<Object>> newList = regenerateDataOrder(list, responseClz, headField);
        log.info("重建完数据");
        excelWriter.write(newList, writeSheet);
    }

    public static <R> List<List<Object>> regenerateDataOrder(List<R> list, Class<R> responseClz, List<String> headField) {
        List<List<Object>> dataList = new ArrayList<>();
        if (CollUtil.isEmpty(list)) {
            return dataList;
        }
        LinkedHashMap<String, Class> propertyMap = resolveProperty(list.get(0), responseClz);
        for (int i = 0; i < list.size(); i++) {
            Object row = list.get(i);
            Object newRowData = DynamicBeanUtil.dynamicClass(row, propertyMap);
            Map<String, Object> map = JsonUtil.parseObject(JsonUtil.toJsonString(newRowData), Map.class);
            List<Object> fieldValueList = new ArrayList<>(map.size());
            for (String head : headField) {
                // 如果头字段不包含，尝试调整字段名称再次获取
                if (!map.containsKey(head)) {
                    head = DynamicBeanUtil.buildExtFieldKey(head);
                }
                fieldValueList.add(map.get(head));
            }
            dataList.add(fieldValueList);
        }
        return dataList;
    }

    /**
     * 从数据和数据类中解析需要导出的属性列和对应的数据类型
     * <AUTHOR>
     * @date 2023/5/19
     */
    private static LinkedHashMap<String, Class> resolveProperty(Object data, Class responseClz) {
        // 原返回对象的所有字段，包括 map 类型
        Field[] fieldArr = ReflectUtil.getFields(responseClz);
        // 字段的名称和类型对象
        List<IndexKey> indexKeyList = new ArrayList<>();
        int offset = -1;
        // 遍历所有字段，如果是map类型，则对其进行平铺，这里需要注意顺序
        for (int i = 0; i < fieldArr.length; i++) {
            Field field = fieldArr[i];
            if (field.getType().isAssignableFrom(Map.class) || field.getType().isAssignableFrom(LinkedHashMap.class)) {
                LinkedHashMap mapValue = (LinkedHashMap) ReflectUtil.getFieldValue(data, field);
                if (CollUtil.isNotEmpty(mapValue)) {
                    List<String> mapKeyList = CollUtil.newArrayList(mapValue.keySet());
                    for (String x : mapKeyList) {
                        indexKeyList.add(new IndexKey(DynamicBeanUtil.buildExtFieldKey(x), mapValue.get(x).getClass()));
                    }
                }
                if (i == 0) {
                    offset = 0;
                }
            } else {
                if (offset >= 0) {
                    indexKeyList.add(offset, new IndexKey(field.getName(), field.getType()));
                    offset = offset + 1;
                } else {
                    indexKeyList.add(new IndexKey(field.getName(), field.getType()));
                }

            }
        }
        // 将顺序的导出字段转成map格式，动态创建bean时需要
        LinkedHashMap<String, Class> propertyMap = new LinkedHashMap<>(indexKeyList.size());
        for (IndexKey ik : indexKeyList) {
            propertyMap.put(ik.getKey(), ik.getClz());
        }
        return propertyMap;
    }

    /**
     * 获取当前需要导出的字段Map
     * 如果指定了要导出的字段，则只导出指定的字段，否则导出所有字段
     * <AUTHOR>
     * @date 2023/8/9
     */
    private Map<String, Object> getSelectedOrAllFieldMap(List<String> selectedFieldList, Field[] fieldArr) {
        if (CollUtil.isEmpty(selectedFieldList)) {
            selectedFieldList = Arrays.stream(fieldArr).map(Field::getName).collect(Collectors.toList());
        }
        return selectedFieldList.stream().collect(Collectors.toMap(x -> x, x -> VALUE_OBJ));
    }


    @Data
    static class IndexKey  {
        private String key;
        private Class clz;

        public IndexKey(String key, Class clz) {
            this.key = key;
            this.clz = clz;
        }
    }

}
