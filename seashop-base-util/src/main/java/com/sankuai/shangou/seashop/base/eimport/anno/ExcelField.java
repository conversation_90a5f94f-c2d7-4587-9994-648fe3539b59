package com.sankuai.shangou.seashop.base.eimport.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.sankuai.shangou.seashop.base.eimport.regex.FieldRegexEnum;

/**
 * 导入时字段类型校验
 *
 * <AUTHOR>
 * @date 2023/11/22 15:44
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelField {

    boolean required() default false;

    FieldRegexEnum regexEnum() default FieldRegexEnum.NONE;

    String regex() default "";

    String regexMsg() default "";

}
