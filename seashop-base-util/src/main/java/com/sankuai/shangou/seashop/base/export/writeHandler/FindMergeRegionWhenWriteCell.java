package com.sankuai.shangou.seashop.base.export.writeHandler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.sankuai.shangou.seashop.base.export.anno.RowKey;
import com.sankuai.shangou.seashop.base.export.anno.ShouldMerge;
import com.sankuai.shangou.seashop.base.export.context.SheetMergeRegionHolder;
import com.sankuai.shangou.seashop.base.export.enums.MergeStyle;
import com.sankuai.shangou.seashop.base.export.model.RowMergeRegion;
import com.sankuai.shangou.seashop.base.export.util.ValueUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2023/8/31
 */
@Slf4j
public class FindMergeRegionWhenWriteCell implements CellWriteHandler {

    // 根据导出对象的@ShouldMerge解析出来的，需要合并单元格的列索引，以及对应的合并格式，默认是排除空数据
    private Map<Integer, MergeStyle> mergerColIndexMap;
    // 最大行索引，数据列大小，因为表头占了一行，索引0已被使用
    private int maxRowIndex = 0;
    // 最大列索引，当前需要导出的表头的数量-1
    private int maxMergeColIndex = 0;
    // 数据行唯一标识需要用到的列，如果没有，则代表只需要判断当前单元格的值是否相等
    private final List<Pair<Integer, String>> rowKeyIndexList = new ArrayList<>(5);
    // 一行可能会有多个字段需要合并，每行的唯一标识只解析一次，没新开启一行，都把上一行数据清空，节约内存
    private final Map<Integer, String> rowKeyValueMap = new HashMap<>(2);
    private int opRowIndex = 0;
    private AtomicInteger ignoreRowNum = null;
    private AtomicReference<List<?>> dataSupplier = null;

    // 需要清空的行+列，key是行，value是列，如果不清空，会导致excel底部的计数有问题
    private List<Pair<Integer, Integer>> clearRowIndexList = new ArrayList<>(5);
    private boolean ignoreHeadNum = false;




    private FindMergeRegionWhenWriteCell() {}

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * @param dataList 导出数据的
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> FindMergeRegionWhenWriteCell(Class<R> excelClz, List<String> selectedFieldList, List<?> dataList) {
        this(excelClz, selectedFieldList, new AtomicReference<>(dataList), new AtomicInteger(0));
    }

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * @param dataSupplier 导出数据的
     * @param ignoreRowNum 忽略的行数，一种情况，比如表格前面几行输出其他东西，然后在输出列表数据，则ignoreRowNum代表前面几行的行数
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> FindMergeRegionWhenWriteCell(Class<R> excelClz, List<String> selectedFieldList,
                                            AtomicReference<List<?>> dataSupplier, AtomicInteger ignoreRowNum) {
        this(excelClz, selectedFieldList, null, dataSupplier, ignoreRowNum);
    }

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * @param dataSupplier 导出数据的
     * @param ignoreRowNum 忽略的行数，一种情况，比如表格前面几行输出其他东西，然后在输出列表数据，则ignoreRowNum代表前面几行的行数
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> FindMergeRegionWhenWriteCell(Class<R> excelClz, List<String> selectedFieldList,
                                            AtomicReference<List<?>> dataSupplier, AtomicInteger ignoreRowNum, boolean ignoreHeadNum) {
        this(excelClz, selectedFieldList, null, dataSupplier, ignoreRowNum, ignoreHeadNum);
    }

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * @param specialMergeField 不根据导出对象识别，而单独定义的需要合并的字段
     * @param dataSupplier 导出数据的
     * @param ignoreRowNum 忽略的行数，一种情况，比如表格前面几行输出其他东西，然后在输出列表数据，则ignoreRowNum代表前面几行的行数
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> FindMergeRegionWhenWriteCell(Class<R> excelClz, List<String> selectedFieldList, Map<String, MergeStyle> specialMergeField,
                                            AtomicReference<List<?>> dataSupplier, AtomicInteger ignoreRowNum) {
        this(excelClz, selectedFieldList, specialMergeField, dataSupplier, ignoreRowNum, false);
    }

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * @param specialMergeField 不根据导出对象识别，而单独定义的需要合并的字段
     * @param dataSupplier 导出数据的
     * @param ignoreRowNum 忽略的行数，一种情况，比如表格前面几行输出其他东西，然后在输出列表数据，则ignoreRowNum代表前面几行的行数
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> FindMergeRegionWhenWriteCell(Class<R> excelClz, List<String> selectedFieldList, Map<String, MergeStyle> specialMergeField,
                                            AtomicReference<List<?>> dataSupplier, AtomicInteger ignoreRowNum, boolean ignoreHeadNum) {
        this.ignoreRowNum = ignoreRowNum;
        this.maxRowIndex = ignoreRowNum.get();
        this.dataSupplier = dataSupplier;
        this.ignoreHeadNum = ignoreHeadNum;
        mergerColIndexMap = new HashMap<>(16);
        Field[] fieldArr = ReflectUtil.getFields(excelClz);
        Field[] exportFieldArr = null;
        if (CollUtil.isEmpty(selectedFieldList)) {
            exportFieldArr = fieldArr;
        } else {
            exportFieldArr = Arrays.stream(fieldArr).filter(field -> selectedFieldList.contains(field.getName())).toArray(Field[]::new);
        }
        // 遍历当前最终的表头
        for (int i = 0; i < exportFieldArr.length; i++) {
            Field field = exportFieldArr[i];
            // 如果添加了@RowKey注解，说明是行唯一标识，把索引记录下来，后续判断数据是否相等时使用
            if (field.getAnnotation(RowKey.class) != null) {
                rowKeyIndexList.add(new Pair<>(i, field.getName()));
            }
            // 添加要合并的列
            addMergeColIndex(i, field, specialMergeField);
        }
        log.info("【合并单元格-初始化合并策略】maxRowIndex={}, maxColIndex={}, rowKeyIndexList={}, mergerColIndexMap={}",
                maxRowIndex, maxMergeColIndex, JSONUtil.toJsonStr(rowKeyIndexList), JSONUtil.toJsonStr(mergerColIndexMap));
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        Cell cell = context.getCell();
        // 当前行的行号索引
        int curRowIndex = cell.getRowIndex();
        // 当前列的列号索引
        int curColIndex = cell.getColumnIndex();
        boolean needMerge = needMerge(context);
        log.debug("【合并单元格】curRowIndex={}, curColIndex={}, 是否需要合并={}", curRowIndex, curColIndex, needMerge);
        if (!needMerge) {
            // 如果当前行与操作行不一样说明是新行，新开启一行，判断与上一行的RowKey是否一致，不一致就可以清除了
            // 从第二行数据行开始判断
            int extraIgnoreNum = ignoreHeadNum ? 0 : 1;
            int ignoreRowIndex = ignoreRowNum.get() == 0 ? 0 : ignoreRowNum.get() + extraIgnoreNum;
            if (curRowIndex > ignoreRowIndex + 1 && curRowIndex != opRowIndex && !rowKeyEquals(curRowIndex)) {
                clearMergedCell(sheet, curRowIndex);
            }
            opRowIndex = curRowIndex;
            return;
        }
        opRowIndex = curRowIndex;

        Pair<Integer, Integer> pair = new Pair<>(curRowIndex, curColIndex);
        log.debug("【合并单元格】curRowIndex={}, curColIndex={}, 当前单元格的行列唯一标识为: {}", curRowIndex, curColIndex, pair);
        clearRowIndexList.add(pair);

        // 此时说明当前单元格与上一行单元格的 行唯一标识+数据 相等，需要记录合并
        String sheetName = context.getWriteSheetHolder().getWriteSheet().getSheetName();
        List<RowMergeRegion> columnMergeRegionList = SheetMergeRegionHolder.getSheetMergeRegion(sheetName, curColIndex);
        RowMergeRegion lastMergeRegion = CollUtil.getLast(columnMergeRegionList);
        log.debug("【合并单元格】curRowIndex={}, curColIndex={}, 当前最后的合并区域为: {}", curRowIndex, curColIndex, JSONUtil.toJsonStr(lastMergeRegion));
        if (lastMergeRegion == null || !(lastMergeRegion.getToRowIndex() == curRowIndex - 1)) {
            RowMergeRegion newMergeRegion = new RowMergeRegion(curRowIndex - 1, curRowIndex);
            columnMergeRegionList.add(newMergeRegion);
        } else {
            lastMergeRegion.setToRowIndex(lastMergeRegion.getToRowIndex() + 1);
        }
        log.debug("【合并单元格】curRowIndex={}, curColIndex={}, 最新的需要合并单元格的范围为: {}", curRowIndex, curColIndex, JSONUtil.toJsonStr(lastMergeRegion));
        // 已经达到最大行数了，则清除
        int curMaxRowIndex = dataSupplier.get().size() + ignoreRowNum.get();
        if (curRowIndex == curMaxRowIndex && curColIndex == maxMergeColIndex) {
            clearMergedCell(sheet, curRowIndex);
        }
        // 已经达到最大行数了，则清除
        if (curRowIndex == maxRowIndex && curColIndex == maxMergeColIndex) {
            clearMergedCell(sheet, curRowIndex);
        }
    }

    /**
     * 是否需要合并的判断，会基于前后两行的唯一标识，即类上的@RowKey注解的字段，如果导出的没有，则当前无法合并
     * <p>导出逻辑中，在 BaseWriteStgy 中会根据导出字段重新生成对象，所以如果不导出唯一标识，则无法获取进行判断</p>
     * <AUTHOR>
     * @date 2023/9/14
     */
    private boolean needMerge(CellWriteHandlerContext context) {
        Integer relativeRowIndex = context.getRelativeRowIndex();
        // 如果是表头，或者空行，或者第一行数据(第一行不需要判断合并)，直接返回
        if (context.getHead() || relativeRowIndex == null || relativeRowIndex == 0) {
            return false;
        }
        Cell cell = context.getCell();
        // 当前行的行号索引
        int curRowIndex = cell.getRowIndex();
        // 当前列的列号索引
        int curColIndex = cell.getColumnIndex();
        // 如果不在合并列的索引中，或者当前单元格数据为空，且合并格式是忽略空数据，则不合并
        if (mergerColIndexMap == null || !mergerColIndexMap.containsKey(curColIndex)) {
            log.debug("【合并单元格】curColIndex={} 不需要合并", curColIndex);
            return false;
        }
        // 至少从第二行数据行开始判断
        // 如果当前行与上一行的 行唯一标识 相等，则需要判断是否需要合并，合并的依据是 行唯一标识+单元格数据 与 上一行的 行唯一标识+单元格数据 是否相等
        boolean rowKeyEquals = rowKeyEquals(curRowIndex);
        if (!rowKeyEquals) {
            return false;
        }
        // 当前单元格的数据
        String curData = ValueUtil.getCellValueString(cell);
        // 当前单元格所在列+上一行对应的数据，即需要比对是否相等的两个单元格
        Cell preCell = cell.getSheet().getRow(curRowIndex - 1).getCell(curColIndex);
        String preData = ValueUtil.getCellValueString(preCell);
        return dataEquals(curRowIndex, curData, preData);
    }


    private boolean rowKeyEquals(int curRowIndex) {
        // 基于规则获取的当前行的唯一标识数据
        String curRowKeyValue = getRowKeyValue(curRowIndex);
        // 如果唯一标识为空，则不合并
        if (StrUtil.isBlank(curRowKeyValue)) {
            return false;
        }
        String preRowKeyValue = getRowKeyValue(curRowIndex - 1);
        return curRowKeyValue.equals(preRowKeyValue);
    }

    private boolean dataEquals(int curRowIndex, String curData, String preData) {
        // 基于规则获取的当前行的唯一标识数据
        String curRowKeyValue = getRowKeyValue(curRowIndex);
        String preRowKeyValue = getRowKeyValue(curRowIndex - 1);
        // 唯一标识+当前单元格的数据
        String finalCurData = curRowKeyValue + curData;
        String finalPreData = preRowKeyValue + preData;
        log.debug("【合并单元格】curRowIndex={}, curRowKeyValue={}, preRowKeyValue={}, finalCurData={}, finalPreData={}",
                curRowIndex, curRowKeyValue, preRowKeyValue, finalCurData, finalPreData);
        // 返回是否相等
        return finalCurData.equals(finalPreData);
    }

    private String getRowKeyValue(int curRowIndex) {
        if (CollUtil.isEmpty(rowKeyIndexList)) {
            return "";
        }
        if (rowKeyValueMap.containsKey(curRowIndex)) {
            return rowKeyValueMap.get(curRowIndex);
        }
        StringBuilder rowKey = new StringBuilder();
        // 获取行关键字数据时，由于当前类是写excel单元格时触发的，所以对于在当前单元格之后写入数据的单元格是获取不到的，只能通过反射从对象获取
        for (Pair<Integer, String> rowIndex : rowKeyIndexList) {
            // 减一是因为数据和行号是从0开始的，但行号有表头，所以会比数据索引多一
            Object data = dataSupplier.get().get(curRowIndex - ignoreRowNum.get() - 1);
            String rowKeyCellValue = String.valueOf(ReflectUtil.getFieldValue(data, rowIndex.getValue()));
            rowKey.append(rowKeyCellValue).append("^_^");
        }
        // 每一行的唯一数据进行缓存
        rowKeyValueMap.put(curRowIndex, rowKey.toString());
        return rowKey.toString();
    }


    /**
     * 全部单元格处理完后，统一清除需要清空的数据，如果每次循环都清除，会导致最多只能合并两行，因为前面数据清空了
     * <AUTHOR>
     * @date 2023/8/10
     */
    private void clearMergedCell(Sheet sheet, int curRowIndex) {
        if (CollUtil.isEmpty(clearRowIndexList)) {
            return;
        }
        log.debug("【合并单元格】清除数据, curRowIndex={}, clearRowIndexList={}", curRowIndex, clearRowIndexList);
        for (Pair<Integer, Integer> rowIndex : clearRowIndexList) {
            Row row = sheet.getRow(rowIndex.getKey());
            if (row == null) {
                log.debug("row=null, rowIndex={}", rowIndex);
                continue;
            }
            Cell clearCell = sheet.getRow(rowIndex.getKey()).getCell(rowIndex.getValue());
            clearCell.setBlank();
        }
        clearRowIndexList = new ArrayList<>(5);
    }


    /**
     * 添加需要合并的列
     * <p>基本前提是导出的对象定义了 @ShouldMerge 注解；支持特殊指定部分要合并的列，比如多个导出操作公用一个对象，但有不同合并需求的场景</p>
     * <AUTHOR>
     * @date 2023/9/1
     */
    private void addMergeColIndex(int colIndex, Field field, Map<String, MergeStyle> specialMergeField) {
        // 如果添加@ShouldMerge注解，说明需要合并
        if (specialMergeField == null || specialMergeField.isEmpty()) {
            ShouldMerge shouldMerge = field.getAnnotation(ShouldMerge.class);
            if (shouldMerge != null) {
                mergerColIndexMap.put(colIndex, shouldMerge.style());
                maxMergeColIndex = colIndex;
            }
            return;
        }
        // 如果有指定的合并列，那就只合并这些特殊指定的
        MergeStyle mergeStyle = null;
        String fieldName = field.getName();
        if (specialMergeField.containsKey(fieldName)) {
            mergeStyle = specialMergeField.get(fieldName);
            if (mergeStyle == null) {
                mergeStyle = MergeStyle.ALL;
            }
            mergerColIndexMap.put(colIndex, mergeStyle);
            maxMergeColIndex = colIndex;
        }
    }


}
