package com.sankuai.shangou.seashop.base.export.writter;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCustomWriteStrategyFetcher<P extends BasePageReq> implements ExcelWriteStrategy<P> {


    @Override
    public ByteArrayOutputStream writeToOutputStream(ExportDataGetter exportService, P param) {
        return writeToOutputStream(param);
    }

    protected abstract ByteArrayOutputStream writeToOutputStream(P param);
}
