package com.sankuai.shangou.seashop.base.export.model;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
public interface BaseExportWrapper<T, P extends BasePageReq> {

    /**
     * 数据列表，每个对象是一行数据
     * <AUTHOR>
     * @date 2023/5/19
     */
    List<T> getDataList(P param);

    /**
     * 当前数据块内容输出的处理器，比如可以设置单元格样式，合并单元格等
     * <AUTHOR>
     * @date 2023/5/16
     */
    default List<WriteHandler> getWriteHandlerList() {return null;}

    /**
     * 当前数据块的sheet名称，如果不设置，则使用默认的sheet名称，即Sheet1,Sheet2等等
     * <AUTHOR>
     * @date 2023/5/16
     */
    default String sheetName() {
        return null;
    }

    /**
     * 是否有map类型的表头表头，组件会将map的key作为数据列，此时需要查询一次数据获取数据对象来构造表头
     * <AUTHOR>
     */
    default boolean haveMapHeader() {
        return false;
    }

    /**
     * 分页导出时，每个sheet可以自定义的每页查询数据，null默认使用全局配置
     */
    default Integer getBatchCount() {
        return null;
    }

    /**
     * 分页导出时，每个sheet可以自定义的最大页数，null默认使用全局配置
     */
    default Integer getMaxBatch() {
        return null;
    }

}
