package com.sankuai.shangou.seashop.base.export.writter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterTableBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.AbstrSSheetCycleTableDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import com.sankuai.shangou.seashop.base.export.model.CycleTableContext;
import com.sankuai.shangou.seashop.base.export.model.TableWrapper;
import com.sankuai.shangou.seashop.base.export.util.SheetNameUtil;
import com.sankuai.shangou.seashop.base.export.writeHandler.DefaultHeadStyle;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Slf4j
public class SSheetCycleTableWriteStgy<P extends BasePageReq> implements ExcelWriteStrategy<P> {


    @Override
    public ByteArrayOutputStream writeToOutputStream(ExportDataGetter exportService, P param) {
        log.info("【数据导出】单sheet+循环表格数据导出");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        // 基于EasyExcel将获取到的数据写到excel，并将excel上传到文件服务器，后续用户从页面通过文件服务器的地址获取文件
        AbstrSSheetCycleTableDataGetter currentGetter = (AbstrSSheetCycleTableDataGetter)exportService;
        CycleTableContext tableContext = currentGetter.selectData(param);
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream).build()) {
            // 定义当前的sheet，首先指定不需要表头，如果需要表头，WriteTable单独指定
            String sheetName = SheetNameUtil.getSheetName(tableContext.getSheetName(), 0);
            WriteSheet writeSheet = EasyExcel.writerSheet()
                    .sheetName(sheetName)
                    .needHead(false)
                    .build();
            // 获取当前需要输出的所有表格，每个表格可以认为就是一个小的sheet的内容，可以单独自定义格式，表头等
            List<TableWrapper> tableList = tableContext.getTableList();
            for (int i = 0; i < tableList.size(); i++) {
                TableWrapper tableWrapper = tableList.get(i);
                // 构造当前的数据表格
                ExcelWriterTableBuilder tableBuilder = EasyExcel.writerTable(i).registerWriteHandler(DefaultHeadStyle.getInstance());
                if (tableWrapper.needHead()) {
                    tableBuilder.needHead(true)
                            .head(ClassUtil.getTypeArgument(tableWrapper.getDataList(param).getClass()));
                } else {
                    tableBuilder.needHead(false);
                }
                List<WriteHandler> writeHandlerList = tableWrapper.getWriteHandlerList();
                if (CollUtil.isNotEmpty(writeHandlerList)) {
                    writeHandlerList.forEach(x -> tableBuilder.registerWriteHandler(x));
                }
                WriteTable writeTable = tableBuilder.build();
                // 将当前的table数据写到sheet
                excelWriter.write(tableWrapper.getDataList(param), writeSheet, writeTable);
            }
        }
        return outputStream;
    }
}
