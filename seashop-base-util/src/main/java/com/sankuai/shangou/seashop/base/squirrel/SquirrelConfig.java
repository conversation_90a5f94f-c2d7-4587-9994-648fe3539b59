package com.sankuai.shangou.seashop.base.squirrel;

import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SquirrelConfig {

    @ConditionalOnMissingBean
    @Bean
    public SquirrelUtil squirrelUtil() {
        return new SquirrelUtil();
    }
}
