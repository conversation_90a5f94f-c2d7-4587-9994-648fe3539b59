package com.sankuai.shangou.seashop.base.export.writeHandler;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

/**
 * <AUTHOR>
 * @date 2023/8/5
 */
@Slf4j
public class DefaultHeadStyle implements CellWriteHandler {

    private final static DefaultHeadStyle INSTANCE = new DefaultHeadStyle();

    public static DefaultHeadStyle getInstance() {
        return INSTANCE;
    }

    private CellStyle cellStyle;

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        log.trace("【设置头样式】rowIndex={}, columnIndex={}, relativeRowIndex={}",
                context.getRowIndex(), context.getColumnIndex(), context.getRelativeRowIndex());
        WriteSheetHolder writeSheetHolder = context.getWriteSheetHolder();
        Cell cell = context.getCell();
        if (BooleanUtils.isNotTrue(context.getHead())) {
            return;
        }
        int width = 20;
        width = width * 256;
        writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), width);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        } else {
            // 拿到poi的workbook
            Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
            // 这里千万记住 想办法能复用的地方把他缓存起来 一个表格最多创建6W个样式
            // 不同单元格尽量传同一个 cellStyle
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font font = workbook.createFont();
            font.setFontName("宋体");
            font.setColor(IndexedColors.BLACK.getIndex());
            font.setFontHeightInPoints((short) 10);
            font.setBold(true);
            cellStyle.setFont(font);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            cell.setCellStyle(cellStyle);
            this.cellStyle = cellStyle;
        }



        // 由于这里没有指定dataformat 最后展示的数据 格式可能会不太正确

        // 这里要把 WriteCellData的样式清空， 不然后面还有一个拦截器 FillStyleCellWriteHandler 默认会将 WriteCellStyle 设置到
        // cell里面去 会导致自己设置的不一样
        context.getFirstCellData().setWriteCellStyle(null);
    }

}
