package com.sankuai.shangou.seashop.base.export.head;

import java.util.List;

/**
 * 多动态表头+多份导出字段的表头构造器
 * abstr = abstract
 * M = multiple
 * D = dynamic
 * S = single
 * <AUTHOR>
 * @date 2023/4/20
 */
public interface MDHeadMSelectBuilder {

    /**
     * 根据需要动态生成表头
     * <b>Important: 目前的规则是，指定需要导出的列时，必须是param对象里数据类型为字符串数组，且字段名称为 selectedFieldList，否则无法识别 </b>
     * @param dataList 当前需要导出的多份数据，因为每份数据可能都不一样，这个List是要获取数据的类型，构造excel的字段时匹配获取描述
     * @param selectedField 用户选择的每个sheet需要导出的字段
     * <AUTHOR>
     * @date 2023/4/20
     */
    HeadFieldHolder dynamicHead(List<List<Object>> dataList, List<List<String>> selectedField);

}
