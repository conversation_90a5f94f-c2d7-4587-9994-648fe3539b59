package com.sankuai.shangou.seashop.base.leaf;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.segment.SegmentId;
import me.ahoo.cosid.snowflake.SnowflakeId;

/**
 * <AUTHOR>
 * @CreateTime: 2023-11-09  10:08
 * 统一使用leaf生成id
 */
@Slf4j
@Service
public class LeafService {


    @Qualifier("__share__SnowflakeId")
    @Lazy
    @Autowired
    private SnowflakeId snowflakeId;

    @Qualifier("__share__SegmentId")
    @Lazy
    @Autowired
    private SegmentId segmentId;

    /**
     * 默认的号段模式的分布式ID生成器
     *
     * @param key 业务key，需要到leaf申请权限 <a href="https://leaf.mws-test.sankuai.com/"/>
     *            java.lang.Long
     * <AUTHOR>
     */
    @Retryable(value = BusinessException.class, maxAttempts = 3, backoff = @Backoff(delay = 500, multiplier = 1))
    public Long generateNo(String key) {
        try {
            long generate = segmentId.generate();
            log.info("generateNo key:{},res:{}", key, generate);
            return generate;
        }
        catch (Exception e) {
            log.error("invoke leaf generate id failed", e);
            throw new BusinessException("系统异常，请稍后再试");
        }
    }

    /**
     * 批量生成默认的号段模式的分布式ID生成器
     * <AUTHOR>
     * @param key 业务key，需要到leaf申请权限 <a href="https://leaf.mws-test.sankuai.com/"/>
     * java.lang.Long
     */
    @Retryable(value = BusinessException.class, maxAttempts = 3, backoff = @Backoff(delay = 500, multiplier = 1))
    public List<Long> batchGenerateNo(String key, int size) {
        try {
            List<Long> idLists = IntStream.range(1, size + 1).mapToObj(i -> segmentId.generate()).collect(Collectors.toList());
            log.info("batchGenerateNo key:{},size:{},resList:{}", key, size, idLists);
            return idLists;
        }
        catch (Exception e) {
            log.error("invoke leaf generate id failed", e);
            throw new BusinessException("系统异常，请稍后再试");
        }
    }

    /**
     * 生成雪花算法id（长度19位）
     *
     * @param key（需要雪花算法申请权限）
     * @return
     */
    @Retryable(value = BusinessException.class, maxAttempts = 3, backoff = @Backoff(delay = 500, multiplier = 1))
    public Long generateNoBySnowFlake(String key) {
        try {
            long generate = snowflakeId.generate();
            log.info("generateNoBySnowFlake key:{},res:{}", key, generate);
            return generate;
        }
        catch (Exception e) {
            log.error("invoke leaf generate id failed", e);
            throw new BusinessException("系统异常，请稍后再试");
        }
    }

    /**
     * 批量生成雪花算法id（长度19位）
     *
     * @param key（需要雪花算法申请权限）
     * @return 批量ID
     */
    @Retryable(value = BusinessException.class, maxAttempts = 3, backoff = @Backoff(delay = 500, multiplier = 1))
    public List<Long> batchGenerateNoBySnowFlake(String key, int size) {
        try {
            List<Long> idList = IntStream.range(1, size + 1).mapToObj(i -> snowflakeId.generate()).collect(Collectors.toList());
            log.info("batchGenerateNoBySnowFlake key:{},size:{},resList:{}", key, size, idList);
            return idList;
        }
        catch (Exception e) {
            log.error("invoke leaf generate id failed", e);
            throw new BusinessException("系统异常，请稍后再试");
        }
    }
}
