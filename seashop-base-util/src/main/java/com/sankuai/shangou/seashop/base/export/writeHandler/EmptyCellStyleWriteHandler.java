package com.sankuai.shangou.seashop.base.export.writeHandler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Row;

/**
 * 空行样式，默认高度1000
 * <AUTHOR>
 * @date 2023/5/16
 */
public class EmptyCellStyleWriteHandler implements RowWriteHandler {

    private short height;

    public EmptyCellStyleWriteHandler(short height) {
        this.height = height;
    }

    @Override
    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
                               Integer relativeRowIndex, Boolean isHead) {
        if (relativeRowIndex == 0) {
            row.setHeight(height);
        }
    }

    private final static EmptyCellStyleWriteHandler DEFAULT_1000_HEIGHT = new EmptyCellStyleWriteHandler((short)1000);

    public static EmptyCellStyleWriteHandler getInstance() {
        return DEFAULT_1000_HEIGHT;
    }

}
