package com.sankuai.shangou.seashop.base.export.handler;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.config.ExportTaskProps;
import com.sankuai.shangou.seashop.base.export.output.FileWriteAndOutputWay;
import com.sankuai.shangou.seashop.base.utils.ReflectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 导出任务处理器
 * <AUTHOR>
 * @date 2023/3/31
 */
@Slf4j
public class ExportTaskHandler implements InitializingBean, ApplicationContextAware {

    // 缓存需要导出的业务数据获取器
    private final static Map<Integer, ExportDataGetter> BIZ_EXPORTER = new HashMap<>();

    private ApplicationContext applicationContext;

    private final static Integer DEFAULT_BATCH_COUNT = 5000;

    /**
     * 文件写出方式，目前支持写到服务器目录以及cos
     * <AUTHOR>
     * @date 2023/5/25
     */
    private FileWriteAndOutputWay fileWriteAndOutputWay;

    public ExportTaskHandler(FileWriteAndOutputWay fileWriteAndOutputWay) {
        this.fileWriteAndOutputWay = fileWriteAndOutputWay;
    }

    @Resource
    private ExportTaskProps exportTaskProps;

    /**
     * 执行导出
     * <p>写到文件服务器的路径为: export/excel/{fileName}{taskId}.xlsx</p>
     * <AUTHOR>
     * @date 2023/3/31
     */
    public ExecuteResult execute(ExportTask task) {
        // 不同的业务有不同的模块类型
        Integer module = task.getTaskType();
        // 通过业务类型获取对应的数据获取器
        ExportDataGetter exportService = BIZ_EXPORTER.get(module);
        if (exportService == null) {
            return ExecuteResult.error();
        }
        return executeForDataGetter(exportService, task);
    }

    public <P extends BasePageReq> ExecuteResult executeForDataGetter(ExportDataGetter exportService, ExportTask task) {
        Class paramClz = ReflectUtil.getInterfaceGenericClass(exportService, 0, 0);
        // 通过请求参数获取对应需要导出的数据，由具体的业务获取器从对应的业务服务获取数据
        String paramStr = task.getExecuteParam();
        Object paramObj = JSONUtil.toBean(paramStr, paramClz);
        P param = (P) paramObj;
        // 将数据写到excel并上传到文件服务器
        String filePath = fileWriteAndOutputWay.writeAndOutput(exportService, task.getTaskId(), task.getTaskDate(), param);
        // 至此导出成功，构建返回结果
        ExecuteResult result = ExecuteResult.success(filePath);
        // 任务导出结束后，支持进行额外的数据处理，后置处理不影响主体导出业务
        try {
            // 获取后置处理器，如果后置处理器不为空，则回调处理
            PostProcessor processor = exportService.postProcessor();
            if (processor != null) {
                processor.process(task, result);
            }
        } catch (Exception e) {
            log.error("【数据导出】执行后置处理异常", e);
        }
        return result;
    }


    private final static String DEFAULT_PROTOCOL = "https://";
    /**
     * 提供通用的方法给业务层获取完整的文件路径
     * <AUTHOR>
     * @date 2023/5/25
     */
    public String buildFullFilePath(String domain, String filePath) {
        if (domain.startsWith("http://") || domain.startsWith("https://")) {
            return domain + filePath;
        }
        return DEFAULT_PROTOCOL + domain + filePath;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, ExportDataGetter> beanMap = this.applicationContext.getBeansOfType(ExportDataGetter.class);
        if (CollectionUtils.isEmpty(beanMap)) {
            return;
        }
        beanMap.values().stream().forEach(serv -> BIZ_EXPORTER.put(serv.getModule(), serv));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private ExportTaskProps getExportTaskProps() {
        if (exportTaskProps == null) {
            exportTaskProps = new ExportTaskProps();
            exportTaskProps.setBatchCount(5000);
            exportTaskProps.setMaxBatch(100);
            exportTaskProps.setNfsPathPrefix("export/excel");
        }
        return exportTaskProps;
    }

}
