package com.sankuai.shangou.seashop.base.export.config;

import com.sankuai.shangou.seashop.base.export.handler.ExportTaskHandler;
import com.sankuai.shangou.seashop.base.export.output.FileWriteAndOutputWay;
import com.sankuai.shangou.seashop.base.export.output.UploadOutputWay;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/4/6
 */
@Configuration
public class ExportAutoConfiguration {

    @Bean
    public FileWriteAndOutputWay fileOutputWay() {
        return new UploadOutputWay();
    }

    @Bean
    @ConditionalOnProperty(prefix = "task.export", name = "enable", havingValue = "true")
    public ExportTaskHandler exportTaskHandler(FileWriteAndOutputWay fileWriteAndOutputWay) {
        return new ExportTaskHandler(fileWriteAndOutputWay);
    }



}
