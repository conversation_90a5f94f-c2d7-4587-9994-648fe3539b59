package com.sankuai.shangou.seashop.base.lock;


import java.util.List;
import java.util.concurrent.TimeUnit;

import com.sankuai.shangou.seashop.base.lock.model.DistributeLockEngineEnum;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;

/**
 * 分布式锁服务
 * @Author: wbhz_chenpeng1
 * @CreateTime: 2023-11-09  10:08
 */
public interface DistributedLockService {
    /**
     * 阻塞等待
     *
     * @param lockKey  锁key
     * @param runnable 任务
     */
    void lock(LockKey lockKey, TaskRunnable runnable);

    /**
     * 阻塞等待
     *
     * @param lockKey  锁key
     * @param callable 任务
     * @return 返回
     */
    <R> R lock(LockKey lockKey, TaskCallable<R> callable) throws Throwable;

    /**
     * 获取失败立马返回
     *
     * @param lockKey  锁key
     * @param runnable 任务
     */
    void tryLock(LockKey lockKey, TaskRunnable runnable);

    /**
     * 获取失败立马返回
     *
     * @param lockKey  锁key
     * @param callable 任务
     * @return 返回
     * @throws Throwable
     */
    <R> R tryLock(Lock<PERSON>ey lockKey, TaskCallable<R> callable) throws Throwable;

    /**
     * 获取失败，等待指定时间
     *
     * @param lockKey
     * @param runnable
     * @param time
     * @param timeUnit
     */
    void tryLock(LockKey lockKey, TaskRunnable runnable, Long time, TimeUnit timeUnit);

    /**
     * 获取失败，等待指定时间
     *
     * @param lockKey
     * @param callable
     * @param time
     * @param timeUnit
     * @param <R>
     * @return
     * @throws Throwable
     */
    <R> R tryLock(LockKey lockKey, TaskCallable<R> callable, Long time, TimeUnit timeUnit) throws Throwable;

    /**
     * 锁实现类型
     *
     * @return
     */
    default DistributeLockEngineEnum engineType() {
        return DistributeLockEngineEnum.SQUIRREL;
    }

    /**
     * 批量获取锁
     *
     * @param lockKeys
     * @param runnable
     * @return
     * @throws Throwable
     */
    void tryMultiReentrantLock(List<LockKey> lockKeys, TaskRunnable runnable);


    /**
     * 批量获取锁
     *
     * @param lockKeys
     * @param callable
     * @param <R>
     * @return
     * @throws Throwable
     */
    <R> R tryMultiReentrantLock(List<LockKey> lockKeys, TaskCallable<R> callable) throws Throwable;


    /**
     * 批量获取锁
     *
     * @param lockKeys
     * @param callable
     * @param <R>
     * @return
     * @throws Throwable
     */
    <R> R tryMultiLock(List<String> lockKeys, TaskCallable<R> callable) throws Throwable;
}