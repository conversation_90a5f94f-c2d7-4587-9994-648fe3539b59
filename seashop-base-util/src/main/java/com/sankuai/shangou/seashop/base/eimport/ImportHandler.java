package com.sankuai.shangou.seashop.base.eimport;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public abstract class ImportHandler<D extends RowReadResult> {

    public abstract BizType bizType();

    public abstract void checkExistsAndSetValue(ReadResult<D> importResult);

    public abstract void saveImportData(List<D> successList);

    public abstract DataWrapper<D> wrapData(List<D> errList);

}
