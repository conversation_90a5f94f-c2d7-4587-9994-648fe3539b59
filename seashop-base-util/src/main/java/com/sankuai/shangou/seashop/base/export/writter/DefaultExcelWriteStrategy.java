package com.sankuai.shangou.seashop.base.export.writter;

import com.alibaba.excel.EasyExcel;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.BaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import com.sankuai.shangou.seashop.base.export.util.SheetNameUtil;
import com.sankuai.shangou.seashop.base.export.writeHandler.DefaultHeadStyle;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 默认按单sheet+固定表头处理
 * <AUTHOR>
 */
@Slf4j
public class DefaultExcelWriteStrategy<P extends BasePageReq> implements ExcelWriteStrategy<P> {

    @Override
    public ByteArrayOutputStream writeToOutputStream(ExportDataGetter exportService, P param) {
        log.info("【数据导出】默认单sheet+固定表头导出");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        if (!(exportService instanceof BaseDataGetter)) {
            return outputStream;
        }
        BaseDataGetter currentGetter = (BaseDataGetter) exportService;
        String sheetName = currentGetter.sheetName();
        List<Object> exportList = currentGetter.getDataList();
        sheetName = SheetNameUtil.getSheetName(sheetName, 0);
        EasyExcel.write(outputStream, currentGetter.getResponseClz())
                .registerWriteHandler(DefaultHeadStyle.getInstance())
                .sheet(sheetName)
                .doWrite(exportList);
        return outputStream;
    }
}
