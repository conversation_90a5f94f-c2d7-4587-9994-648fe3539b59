package com.sankuai.shangou.seashop.base.export.context;

import com.sankuai.shangou.seashop.base.export.model.RowMergeRegion;
import org.springframework.core.NamedThreadLocal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/31
 */
public class SheetMergeRegionHolder {

    /**
     * ThreadLocal<Map<String(sheetName), Map<Integer(列索引), List<RowMergeRegion>(每列需要合并的多个行区域)>>>
     */
    private static final ThreadLocal<Map<String, Map<Integer, List<RowMergeRegion>>>> mergeRegionHolder = new NamedThreadLocal<>("sheet-merge-region");

    public static Map<Integer, List<RowMergeRegion>> getSheetMergeRegion(String sheetName) {
        Map<String, Map<Integer, List<RowMergeRegion>>> regionHolder = mergeRegionHolder.get();
        if (regionHolder == null) {
            regionHolder = new HashMap<>(4);
            mergeRegionHolder.set(regionHolder);
        }
        return regionHolder.computeIfAbsent(sheetName, k -> new HashMap<>(32));
    }

    public static List<RowMergeRegion> getSheetMergeRegion(String sheetName, int colIndex) {
        Map<Integer, List<RowMergeRegion>> colRegion = getSheetMergeRegion(sheetName);
        return colRegion.computeIfAbsent(colIndex, k -> new ArrayList<>(4));
    }

    public static void remove() {
        mergeRegionHolder.remove();
    }


}
