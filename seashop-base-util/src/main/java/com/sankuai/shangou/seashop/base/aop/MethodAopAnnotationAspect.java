package com.sankuai.shangou.seashop.base.aop;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;

import cn.hutool.core.bean.BeanUtil;

/**
 * @author: lhx
 * @date: 2024/3/9/009
 * @description:
 */

@Aspect
@Component
public class MethodAopAnnotationAspect {

    @Resource
    @Lazy
    private SquirrelUtil squirrelUtil;

    @Around("@annotation(methodAopAnnotation)")
    public Object handleCustomRequestParam(ProceedingJoinPoint joinPoint, MethodAopAnnotation methodAopAnnotation) throws Throwable {
        // 获取方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();

        String paramName = methodAopAnnotation.paramName();

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        String[] parameterNames = methodSignature.getParameterNames();

        Object param = null;

        // 遍历参数和注解，找到带有@CustomRequestParam注解的参数
        for (int i = 0; i < parameterNames.length; i++) {
            String parameterName = parameterNames[i];
            if (parameterName.equals(paramName)) {
                param = args[i];
                break;
            }
        }
        if (null != param) {
            Map<String, Object> map = new TreeMap<>(BeanUtil.beanToMap(param, true, true));
            String jsonString = JsonUtil.toJsonString(map);
            Object o = squirrelUtil.get(jsonString);
            if (null != o) {
//                throw new BusinessException("重复提交");
                return new ResultDto<>(411, "点太快了~");
            }
            squirrelUtil.set(jsonString, 1, methodAopAnnotation.timeOut());
        }

        // 如果没有找到带有@CustomRequestParam注解的参数，继续执行原方法
        return joinPoint.proceed();
    }
}
