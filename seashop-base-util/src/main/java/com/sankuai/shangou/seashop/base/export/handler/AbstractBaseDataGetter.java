package com.sankuai.shangou.seashop.base.export.handler;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.head.AbstrBaseHeadBuilder;
import com.sankuai.shangou.seashop.base.export.writter.BaseDataWriteStrategy;
import com.sankuai.shangou.seashop.base.export.writter.ExcelWriteStrategy;
import com.sankuai.shangou.seashop.base.export.writter.WriteStrategyFetcher;

/**
 * <AUTHOR>
 */
public abstract class AbstractBaseDataGetter<P extends BasePageReq>
        extends AbstrBaseHeadBuilder
        implements SingleWrapperDataGetter<P>, WriteStrategyFetcher {

    public AbstractBaseDataGetter() {
    }

    @Override
    public ExcelWriteStrategy<P> getStrategy() {
        return new BaseDataWriteStrategy<>();
    }
}
