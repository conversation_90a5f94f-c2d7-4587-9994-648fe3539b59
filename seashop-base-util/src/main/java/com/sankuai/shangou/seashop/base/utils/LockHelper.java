package com.sankuai.shangou.seashop.base.utils;

import java.time.Duration;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.api.options.CommonOptions;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/20 17:24
 */
@Slf4j
@SuppressWarnings("all")
@Component
public class LockHelper implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * 默认锁定时间 5s
     */
    private static final Long DEFAULT_LOCK_TIME = 5L;

    public static void lock(String lockName, VoidSupplier supplier) {
        lock(lockName, DEFAULT_LOCK_TIME, supplier);
    }

    public static void lock(String lockName, long tryTime, VoidSupplier supplier) {
        RLock lock = getRedissonClient().getLock(lockName);
        try {
            boolean flag = lock.tryLock(tryTime, TimeUnit.SECONDS);
            AssertUtil.throwIfTrue(!flag, "服务繁忙, 请稍后再试~");
            supplier.apply();
        }
        catch (InterruptedException ie) {
            log.error("获取分布式锁失败, error: {}", ie);
            throw new BusinessException("服务繁忙, 请稍后再试~");
        }
        catch (Exception e) {
            log.error("系统异常: {}", e);
            throw new BusinessException(e.getMessage());
        }
        finally {
            lock.unlock();
        }
    }

    public static <T> T lock(String lockName, long tryTime, Supplier<T> supplier) {
        RLock lock = getRedissonClient().getLock(CommonOptions.name(lockName).timeout(Duration.ofSeconds(tryTime)));
        try {
            boolean flag = lock.tryLock(tryTime, TimeUnit.SECONDS);
            AssertUtil.throwIfTrue(!flag, "服务繁忙, 请稍后再试~");
            return supplier.get();
        }
        catch (InterruptedException ie) {
            log.error("获取分布式锁失败, error: {}", ie);
            throw new BusinessException("服务繁忙, 请稍后再试~");
        }
        catch (Exception e) {
            log.error("系统异常: {}", e);
            throw new BusinessException(e.getMessage());
        }
        finally {
            lock.unlock();
        }
    }

    public static void lock(String[] lockNames, VoidSupplier supplier) {
        lock(lockNames, DEFAULT_LOCK_TIME, supplier);
    }

    public static void lock(String[] lockNames, long tryTime, VoidSupplier supplier) {
        RedissonClient redissonClient = getRedissonClient();
        RLock lock = redissonClient.getMultiLock(Arrays.stream(lockNames).map(redissonClient::getLock).toArray(RLock[]::new));
        try {
            boolean flag = lock.tryLock(tryTime, TimeUnit.SECONDS);
            AssertUtil.throwIfTrue(!flag, "服务繁忙, 请稍后再试~");
            supplier.apply();
        }
        catch (InterruptedException ie) {
            log.error("获取分布式锁失败, error: {}", ie);
            throw new BusinessException("服务繁忙, 请稍后再试~");
        }
        catch (Exception e) {
            log.error("系统异常: {}", e);
            throw new BusinessException(e.getMessage());
        }
        finally {
            lock.unlock();
        }
    }

    private static RedissonClient getRedissonClient() {
        return applicationContext.getBean(RedissonClient.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

}
