package com.sankuai.shangou.seashop.base.export.enums;

/**
 * 后台任务类型接口
 * <AUTHOR>
 */
public interface TaskType {

    /**
     * 任务类型，具有一定规则的等长数字
     * <pre>
     *     类型规则：101150001 => 1 01 15 0001
     *     1. 第一位数字:发起外观。1-平台；2：供应商；3：商家
     *     2. 第二、三位数字：业务类型。01-导出任务
     *     3. 第四、五位数字：业务系统。10-基础服务；11-交易服务；12-营销服务；13-用户服务；14-商品服务；15-订单服务
     *     4，最后四位：具体业务
     * </pre>
     */
    Integer getType();

    /**
     * 任务名称
     */
    String getName();

}
