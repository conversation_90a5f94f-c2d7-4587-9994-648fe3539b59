package com.sankuai.shangou.seashop.base.export.writter;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.sankuai.shangou.seashop.base.export.handler.AbstrSSheetSimpTplDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import com.sankuai.shangou.seashop.base.export.template.TemplateDataWrapper;
import com.sankuai.shangou.seashop.base.export.writeHandler.DefaultHeadStyle;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 简单的基于模板的单sheet单数据导出
 * 模板只能是 简单的对象+数据列表
 * <AUTHOR>
 * @date 2023/5/6
 */
@Slf4j
public class SSheetSimpTplExcelWriteStgy<P extends BasePageReq> implements ExcelWriteStrategy<P> {

    @Override
    public ByteArrayOutputStream writeToOutputStream(ExportDataGetter exportService, P param) {
        log.info("【数据导出】简单模板导出");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        AbstrSSheetSimpTplDataGetter currentGetter = (AbstrSSheetSimpTplDataGetter)exportService;
        TemplateDataWrapper templateWrapper = currentGetter.getTemplateDataWrapper();
        // try会关闭流
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream).withTemplate(templateWrapper.getTemplateStream()).registerWriteHandler(DefaultHeadStyle.getInstance()).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // 直接写入数据
            excelWriter.fill(new FillWrapper(templateWrapper.getDataListName(), templateWrapper.getDataList()), writeSheet);
            // 写入list之前的数据
            excelWriter.fill(templateWrapper.getTemplateValue(), writeSheet);
        }
        return outputStream;
    }
}
