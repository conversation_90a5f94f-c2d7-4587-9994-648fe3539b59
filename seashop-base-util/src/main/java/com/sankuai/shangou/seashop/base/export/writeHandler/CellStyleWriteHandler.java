package com.sankuai.shangou.seashop.base.export.writeHandler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Slf4j
public class CellStyleWriteHandler implements CellWriteHandler {

    private HorizontalAlignment horizontalAlignment;
    private VerticalAlignment verticalAlignment;
    private List<Integer> relativeRowIndexList;
    private Font headFont;
    private Font rowFont;

    public CellStyleWriteHandler(HorizontalAlignment horizontalAlignment, VerticalAlignment verticalAlignment, List<Integer> relativeRowIndexList,
                                 Font headFont, Font rowFont) {
        this.horizontalAlignment = horizontalAlignment;
        this.verticalAlignment = verticalAlignment;
        this.relativeRowIndexList = relativeRowIndexList;
        this.headFont = headFont;
        this.rowFont = rowFont;
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell,
                                Head head, Integer relativeRowIndex, Boolean isHead) {
        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        cellStyle.setVerticalAlignment(verticalAlignment);
        if (relativeRowIndexList.contains(relativeRowIndex)) {
            font.setBold(headFont.getBold());
            font.setFontHeightInPoints(headFont.getFontHeightInPoints());
            cellStyle.setFont(font);
            cellStyle.setAlignment(horizontalAlignment);
        } else {
            font.setBold(rowFont.getBold());
            font.setFontHeightInPoints(rowFont.getFontHeightInPoints());
            cellStyle.setFont(font);
        }
        //设置左边框
        cellStyle.setBorderLeft(BorderStyle.THIN);
        //设置右边框
        cellStyle.setBorderRight(BorderStyle.THIN);
        //设置上边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        //设置下边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cell.setCellStyle(cellStyle);
    }

}
