package com.sankuai.shangou.seashop.base.export.writter;

import com.alibaba.excel.EasyExcel;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.AbstractListDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleSheetDataGetter;
import com.sankuai.shangou.seashop.base.export.util.SheetNameUtil;
import com.sankuai.shangou.seashop.base.export.writeHandler.DefaultHeadStyle;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 其他类型都按单sheet+固定表头处理
 * <AUTHOR>
 * @date 2023/4/21
 */
@Slf4j
public class ListDataExcelWriteStrategy<R, P extends BasePageReq> implements ExcelWriteStrategy<P> {

    @Override
    public ByteArrayOutputStream writeToOutputStream(ExportDataGetter exportService, P param) {
        log.info("【数据导出】指定list数据的导出");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        String sheetName = null;
        if (exportService instanceof SingleSheetDataGetter) {
            sheetName = ((SingleSheetDataGetter) exportService).sheetName();
        }
        AbstractListDataGetter currentGetter = (AbstractListDataGetter)exportService;
        List<R> dataList = currentGetter.getDataList();
        sheetName = SheetNameUtil.getSheetName(sheetName, 0);
        EasyExcel.write(outputStream, currentGetter.getDataClz())
                .registerWriteHandler(DefaultHeadStyle.getInstance())
                .sheet(sheetName)
                .doWrite(dataList);
        return outputStream;
    }
}
