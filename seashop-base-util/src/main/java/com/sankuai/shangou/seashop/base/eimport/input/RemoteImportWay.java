package com.sankuai.shangou.seashop.base.eimport.input;

import java.io.File;
import java.io.FileInputStream;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DefaultReadListener;
import com.sankuai.shangou.seashop.base.eimport.ImportResult;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/21 19:44
 */
@Component
@Slf4j
public abstract class RemoteImportWay<D extends RowReadResult> extends ImportWay<D, String> {

    @Override
    public ImportResult importData(BizType bizType, String file, Class<D> dataClz, DefaultReadListener<D> listener) {
        return importData(bizType, file, dataClz, listener, DEFAULT_EXCEL_IMPORT_MAX_SIZE);
    }

    @Override
    public ImportResult importData(BizType bizType, String file, Class<D> dataClz, DefaultReadListener<D> listener, Long maxSize) {
        File localFile = null;
        FileInputStream fis = null;
        try {
            localFile = getFile(file);
            checkFileSize(localFile, maxSize);
            fis = new FileInputStream(localFile);
            return importData(bizType, fis, dataClz, listener);
        }
        catch (Exception e) {
            log.error("read remote file fail, file: {}, error: {}", file, e);
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }

            throw new BusinessException("导入失败");
        }
        finally {
            IoUtil.close(fis);
            FileUtil.del(localFile);
        }
    }

    public abstract File getFile(String file);


}
