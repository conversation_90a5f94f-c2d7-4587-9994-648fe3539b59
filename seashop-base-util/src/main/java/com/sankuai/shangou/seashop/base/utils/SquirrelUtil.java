package com.sankuai.shangou.seashop.base.utils;

import com.hishop.starter.util.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 *
 * category需要在平台集群申请才可使用
 *
 * <AUTHOR>
 * @CreateTime: 2023-11-09  10:08
 */
@Slf4j
public class SquirrelUtil implements InitializingBean {

    /**
     * 从平台申请的category
     */
    @Value("${spring.application.name:'himall'}")
    private String category;


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 设置没有过期时间的缓存
     *
     * @param key
     * @param value
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(buildStoreKey(key), value);
    }

    /**
     * 设置含有过期时间的缓存
     *
     * @param key
     * @param value
     * @param expire 单位秒
     */
    public void set(String key, Object value, int expire) {
        redisTemplate.opsForValue().set(buildStoreKey(key), value, expire, TimeUnit.SECONDS);
    }

    public Object get(String key) {
        return redisTemplate.opsForValue().get(buildStoreKey(key));
    }

    public void deleteKey(String key) {
        redisTemplate.delete(buildStoreKey(key));
    }

    /**
     * 设置过期时间（单位秒）
     *
     * @param key
     * @param expire 单位秒
     */
    public void expire(String key, int expire) {
        redisTemplate.expire(buildStoreKey(key), expire, TimeUnit.SECONDS);
    }

    public boolean setnx(String key, Object value) {
        return redisTemplate.execute((RedisCallback<Boolean>) (connection) -> connection.setNX(buildStoreKey(key).getBytes(), JsonUtil.toJSONBytes(value)));

    }

    /**
     * 添加 Key 对应的值为 Value，只有当 Key 不存在时才添加，如果 Key 已经存在，不改变现有的值
     * { RedisStoreClient#add(StoreKey, Object, int)}
     *
     * @param key             要添加的  Key
     * @param value           要添加的 Value
     * @param expireInSeconds 过期时间
     * @return 如果 Key 不存在且添加成功，返回 true<br>
     * 如果 Key 已经存在，返回 false
     * @throws RuntimeException 异常都是 StoreException 的子类且是 RuntimeException，可以根据需要捕获相应异常。
     *                          如：如果需要捕获超时异常，可以捕获 StoreTimeoutException
     */
    public Boolean setnx(String key, Object value, int expireInSeconds) {
        return redisTemplate.opsForValue().setIfAbsent(buildStoreKey(key), value, expireInSeconds, TimeUnit.SECONDS);
    }

    /**
     * 如果当前key 存在并且 value 与 expect 相同,则删除该key.
     * 该操作为原子操作
     *
     * @param key    要添加的  Key
     * @param expect 期望的value
     * @return {@code true} 成功， {@code false} 失败（当前value 与 expect不相同）
     */
    public Boolean compareAndDelete(String key, Object expect) {
        String result = String.valueOf(redisTemplate.opsForValue().get(buildStoreKey(key)));
        if (JsonUtil.toJSONString(expect).equals(result)) {
            redisTemplate.delete(buildStoreKey(key));
            return true;
        }
        return false;
    }

    public Boolean incr(String key) {
        redisTemplate.opsForValue().increment(buildStoreKey(key), 1);
        return true;
    }


    public String buildStoreKey(String key) {
        return category + ":" + key;
    }


    @Override
    public void afterPropertiesSet() {
        log.info("缓存组件服务启动完成");
    }
}