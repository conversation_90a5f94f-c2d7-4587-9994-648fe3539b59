package com.sankuai.shangou.seashop.base.export.writter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.config.ExportProps;
import com.sankuai.shangou.seashop.base.export.config.ExportTaskProps;
import com.sankuai.shangou.seashop.base.export.context.ExportTaskContextHolder;
import com.sankuai.shangou.seashop.base.export.context.SheetMergeRegionHolder;
import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import com.sankuai.shangou.seashop.base.export.head.ChangeParam;
import com.sankuai.shangou.seashop.base.export.head.HeadFieldHolder;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.base.export.util.SheetNameUtil;
import com.sankuai.shangou.seashop.base.export.writeHandler.DefaultHeadStyle;
import com.sankuai.shangou.seashop.base.export.writeHandler.MergeSheetAfterCellWrite;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 数据对象包含map的导出策略
 * <AUTHOR>
 * @date 2023/5/18
 */
@Slf4j
public class BaseDataWriteStrategy<P extends BasePageReq> implements ExcelWriteStrategy<P> {

    private ExportTaskProps exportTaskProps = SpringUtil.getBean(ExportTaskProps.class);
    private final ExportProps props = SpringUtil.getBean(ExportProps.class);

    @Override
    public ByteArrayOutputStream writeToOutputStream(ExportDataGetter exportService, P param) {
        log.info("【数据导出】基础数据导出");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        AbstractBaseDataGetter currentGetter = (AbstractBaseDataGetter)exportService;
        log.info("【数据导出】当前查询参数: {}", JSONUtil.toJsonStr(param));
        DataContext dataContext = currentGetter.selectData(param);
        List<BaseExportWrapper> sheetDataList = dataContext.getSheetDataList();
        // 约定数据类型，基于数据对象的属性名称和map的key构建动态表头
        // try会关闭流
        long startTime = System.currentTimeMillis();
        try(ExcelWriter excelWriter = EasyExcel.write(outputStream).registerWriteHandler(DefaultHeadStyle.getInstance()).build()) {
            // 因为是多sheet，所以循环创建sheet
            for (int i = 0; i < sheetDataList.size(); i++) {
                long t1 = System.currentTimeMillis();
                // 当前sheet的数据
                BaseExportWrapper exportWrapper = sheetDataList.get(i);
                // 当前sheet需要导出的数据类型
                Class<?> currentDataClz = ClassUtil.getTypeArgument(exportWrapper.getClass());
                // 构造表头的时候，需要传入数据对象，是因为可能需要根据map类型传入的数据构造表头
                Object fieldObj = null;
                if (Boolean.TRUE.equals(exportWrapper.haveMapHeader())) {
                    // 首先获取一条数据，目的时为了获取导出对象，从而解析出表头等信息
                    param.setPageNo(1);
                    param.setPageSize(1);
                    List dataList = exportWrapper.getDataList(param);
                    fieldObj = dataList.get(0);
                } else {
                    fieldObj = ReflectUtil.newInstanceIfPossible(currentDataClz);
                }
                // 根据规则获取sheetName
                String sheetName = SheetNameUtil.getSheetName(exportWrapper.sheetName(), i);
                // 业务上可能用户指定了导出字段，也可能需要根据业务生成导出字段
                List<String> selectedFieldList = selectedSheetFieldList(exportService, param);
                // 解析数据，获取表头相关
                HeadFieldHolder headHolder = currentGetter.buildHead(fieldObj, currentDataClz, selectedFieldList);
                // 获取当前sheet的表头
                List<List<String>> head = headHolder.getSingleHeadList();
                // 获取当前sheet的表头属性列
                List<String> headField = headHolder.getSingleSortedSelect();

                long t2 = System.currentTimeMillis();
                log.info("【数据导出】当前sheet构建完毕，sheetName={}, cost={}", sheetName, t2 - t1);
                // 基础工作做好后，分页获取并写入数据
                pageWrite(exportWrapper, param, currentGetter, excelWriter, sheetName, head, headField, currentDataClz);
                long t3 = System.currentTimeMillis();
                log.info("【数据导出】当前sheet写入完毕，sheetName={}, cost={}", sheetName, t3 - t2);
                // 合并单元格处理
                MergeSheetAfterCellWrite.doMerge(excelWriter.writeContext(), sheetName);
                long t4 = System.currentTimeMillis();
                log.info("【数据导出】当前sheet合并单元格完毕, sheetName={}, cost={}", sheetName, t4 - t3);
            }
            log.info("【数据导出】excel数据写入完毕，耗时={}ms", System.currentTimeMillis() - startTime);
            // threadLocal 清除，防止内存溢出
            SheetMergeRegionHolder.remove();
        }
        return outputStream;
    }

    public List<String> selectedSheetFieldList(ExportDataGetter exportService, P param) {
        P changedParam = param;
        if (exportService instanceof ChangeParam) {
            ChangeParam<P> changeParam = (ChangeParam<P>)exportService;
            changedParam = changeParam.changeParam(param);
        }
        List<String> selected = (List<String>) ReflectUtil.getFieldValue(changedParam, "selectedFieldList");
        return selected;
    }

    private void pageWrite(BaseExportWrapper exportWrapper, P param, AbstractBaseDataGetter currentGetter, ExcelWriter excelWriter,
                           String sheetName, List<List<String>> head, List<String> headField, Class<?> currentDataClz) {

        ExcelWriterSheetBuilder builder = EasyExcel.writerSheet(sheetName)
                // 根据表头属性列指定当前导出的数据列
                .includeColumnFieldNames(headField)
                // 指定表头
                .head(head);
        log.info("【数据导出】headField={}, head={}", JSONUtil.toJsonStr(headField), JSONUtil.toJsonStr(head));
        List<WriteHandler> writeHandlerList = exportWrapper.getWriteHandlerList();
        if (CollUtil.isNotEmpty(writeHandlerList)) {
            writeHandlerList.stream().forEach(x -> builder.registerWriteHandler(x));
        }
        WriteSheet writeSheet = builder.build();

        Map<Integer, ExportProps.BizConfig> bizConfigMap = props.getBizConfigMap();
        ExportProps.BizConfig bizConfig = null;
        // 导出的分页配置，默认取全局配置，即使没有参数化配置，get方法设置了默认值
        int batchCount = exportTaskProps.getBatchCount();
        int maxBatch = exportTaskProps.getMaxBatch();
        // 如果有业务单独的配置，取业务配置
        if (MapUtil.isNotEmpty(bizConfigMap) && (bizConfig = bizConfigMap.get(currentGetter.getModule())) != null) {
            if (bizConfig.getBatchCount() != null) {
                batchCount = bizConfig.getBatchCount();
            }
            if (bizConfig.getMatchBatch() != null) {
                maxBatch = bizConfig.getMatchBatch();
            }
        }
        // 业务配置是针对整个sheet的，还能基于sheet单独配置，优先级最高
        if (exportWrapper.getBatchCount() != null) {
            batchCount = exportWrapper.getBatchCount();
        }
        if (exportWrapper.getMaxBatch() != null) {
            maxBatch = exportWrapper.getMaxBatch();
        }
        log.info("【数据导出】分页获取导出数据, 当前分页配置, batchCount={}, maxBatch={}", batchCount, maxBatch);
        for (int i = 1; i <= maxBatch; i++) {
            long beginTime = System.currentTimeMillis();
            param.setPageNo(i);
            param.setPageSize(batchCount);
            log.info("【数据导出】分页导出, 当前页码={}, 当前每页数据大小={}", i, batchCount);
            // 当前sheet数据为空，则不写入
            long t1 = System.currentTimeMillis();
            List pageList = exportWrapper.getDataList(param);
            long t2 = System.currentTimeMillis();
            long queryCost = t2 - t1;
            log.info("【数据导出】分页导出, 当前页码={}, 查询cost={}", i, queryCost);
            if (pageList == null) {
                pageList = Collections.emptyList();
            }
            // 修改上下文
            ExportTaskContextHolder.updateTotalCount(pageList.size());
            long startTime = System.currentTimeMillis();
            // 写入sheet
            log.info("【数据导出】分页导出, sheetName={}, 当前页码={}, 当前页数据大小: {}", sheetName, i, pageList.size());
            currentGetter.writeToSheet(excelWriter, writeSheet, headField, pageList, currentDataClz);
            long endTime = System.currentTimeMillis();
            log.info("【数据导出】分页导出, 分页写入sheet完毕，sheetName={}, 当前页码={}, 数据量大小={}, 查询cost={}, 写cost={}, 总cost={}",
                    sheetName, i, pageList.size(), queryCost, endTime - startTime, endTime - beginTime);
            // 如果当前页的数量已经小于每页大小，说明已经没有数据了，提前终止。如果恰好是边界值，则只能多查一次
            if (pageList.size() < batchCount) {
                break;
            }
        }
    }
}
