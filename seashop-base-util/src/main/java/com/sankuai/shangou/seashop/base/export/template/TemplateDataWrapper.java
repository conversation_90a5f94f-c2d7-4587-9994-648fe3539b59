package com.sankuai.shangou.seashop.base.export.template;

import lombok.Data;

import java.io.File;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/6
 */
@Data
public class TemplateDataWrapper<R> {

    /**
     * 模板文件的输入流，必填
     */
    private InputStream templateStream;
    /**
     * 模板文件，可以为空
     */
    private File templateFile;
    /**
     * 普通的填充到模板的数据对象，对象的属性名需要与模板中定义的一致
     */
    private Object templateValue;
    /**
     * 填充到模板中的数据列表
     */
    private List<R> dataList;
    /**
     * 列表数据填充时，会根据该字段的名称，从dataList中获取数据，比如 dataListName.userName
     * 如果设置，组件默认认为该值为 data1
     */
    private String dataListName = "data1";

}
