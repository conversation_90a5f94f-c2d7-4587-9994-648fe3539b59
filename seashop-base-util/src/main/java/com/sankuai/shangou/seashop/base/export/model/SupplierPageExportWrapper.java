package com.sankuai.shangou.seashop.base.export.model;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Slf4j
public abstract class SupplierPageExportWrapper<T, P extends BasePageReq> extends PageExportWrapper<T, P> {

    private final Supplier<List<T>> dataSupplier;

    public SupplierPageExportWrapper(Supplier<List<T>> dataSupplier) {
        this.dataSupplier = dataSupplier;
    }

    @Override
    public List<T> getDataList(P param) {
        return dataSupplier.get();
    }

    @Override
    public List<T> getPageList(BasePageReq param) {
        return null;
    }

}
