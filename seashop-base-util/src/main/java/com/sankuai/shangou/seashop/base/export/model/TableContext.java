package com.sankuai.shangou.seashop.base.export.model;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * table类型的数据上下文，主要是为了在当前导出过程中共享和传输一些数据状态
 * <AUTHOR>
 * @date 2023/9/9
 */
public class TableContext {

    /**
     * 当前已经累计写入Excel中的行数，多个table汇总的
     * 采用对象的原因是，期望实现类似于延迟获取的效果，accumulateRowNum 的改变是在获取数据的时候(getDataList)，
     * 但传入wrapper是在构造函数中，所以需要一个对象来传递
     */
    private final AtomicInteger accumulateRowNum;

    public TableContext() {
        accumulateRowNum = new AtomicInteger(0);
    }

    public void accumulateRowNum(int num) {
        accumulateRowNum.getAndAdd(num);
    }

    public int getAccumulateRowNum() {
        return accumulateRowNum.get();
    }

}
