package com.sankuai.shangou.seashop.base.export.handler;

import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

/**
 * 数据表多sheet数据获取器，每个sheet可以有自己的数据类型和样式处理器
 * <AUTHOR>
 * @date 2023/5/19
 */
public interface SingleWrapperDataGetter<P extends BasePageReq> extends SingleSheetDataGetter {

    /**
     * 获取数据，这里是单个sheet的数据
     * <AUTHOR>
     * @date 2023/5/19
     */
    DataContext selectData(P param);

}
