package com.sankuai.shangou.seashop.base.utils;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

//import static org.reflections.Reflections.log;

/**
 * @Description: 数据校验帮助类
 * @Date: 
 */
@Slf4j
public class ValidatorHelper {
 
    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 实体校验
     *
     * @param obj
     * @throws BusinessException
     */
    public static <T> void validate(T obj) throws BusinessException {
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(obj, new Class[0]);
        if (constraintViolations.size() > 0) {
            ConstraintViolation<T> validateInfo = (ConstraintViolation<T>) constraintViolations.iterator().next();
            // validateInfo.getMessage() 校验不通过时的信息，即message对应的值
            log.error(validateInfo.getMessage());
            throw new InvalidParamException(validateInfo.getMessage());
        }
    }

    /**
     * 分组实体校验
     *
     * @param obj
     * @throws BusinessException
     */
    public static <T> void validate(T obj, Class<?>... groups) throws BusinessException {
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(obj, groups);
        if (constraintViolations.size() > 0) {
            ConstraintViolation<T> validateInfo = (ConstraintViolation<T>) constraintViolations.iterator().next();
            // validateInfo.getMessage() 校验不通过时的信息，即message对应的值
            log.error(validateInfo.getMessage());
            throw new InvalidParamException(validateInfo.getMessage());
        }
    }

    /**
     * 对比两个组数是否存在交集
     * @param array1
     * @param array2
     * @return
     */
    public static boolean hasIntersection(long[] array1, long[] array2) {
        Set<Long> set1 = Arrays.stream(array1).boxed().collect(Collectors.toSet());
        Set<Long> set2 = Arrays.stream(array2).boxed().collect(Collectors.toSet());
        set1.retainAll(set2);
        return !set1.isEmpty();
    }
}