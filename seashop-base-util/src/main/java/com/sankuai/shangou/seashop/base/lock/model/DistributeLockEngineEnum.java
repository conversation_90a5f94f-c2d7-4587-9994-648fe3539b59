package com.sankuai.shangou.seashop.base.lock.model;


import lombok.Getter;

/**
 * 分布式锁引擎枚举
 * @Author: wbhz_chenpeng1
 * @CreateTime: 2023-11-09  10:08
 */
@Getter
public enum DistributeLockEngineEnum {
    SQUIRREL(0, "redis"),
    ZOOKEEPER(1, "zk"),
    ;
    public int code;
    public String msg;

    public String getMsg() {
        return msg;
    }
    DistributeLockEngineEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}