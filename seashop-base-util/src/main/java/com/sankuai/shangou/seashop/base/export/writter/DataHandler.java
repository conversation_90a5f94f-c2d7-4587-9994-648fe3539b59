package com.sankuai.shangou.seashop.base.export.writter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.sankuai.shangou.seashop.base.export.writeHandler.DefaultHeadStyle;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/26
 */
public class DataHandler {

    /**
     * 由于EasyExcel存在缓存，即使修改了 ExcelProperty 的index属性，也只有第一次会生效
     * 所以，基于反射，重新构造数据，按照动态表头的字段顺序构造，这样导出的数据就是按照表头的顺序导出
     * <AUTHOR>
     * @date 2023/4/21
     */
    public static List<List<Object>> regenerateDataOrder(List<Object> list, List<String> selectedField, Class responseClz) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<List<Object>> dataList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            for (int j = 0; j <selectedField.size() ; j++) {
                dataList.add(new ArrayList<>());
                Field f = null;
                try {
                    f = ReflectUtil.getField(responseClz, selectedField.get(j));
                    f.setAccessible(true);
                    dataList.get(i).add(f.get(list.get(i)));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return dataList;
    }

    /**
     * 将数据写入sheet
     * <AUTHOR>
     * @date 2023/4/26
     */
    public static void writeToSheet(ExcelWriter excelWriter, String sheetName,
                                    List<List<String>> head, List<String> headField,
                                    List<Object> list, Class responseClz) {
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName)
                .registerWriteHandler(DefaultHeadStyle.getInstance())
                // 根据表头属性列指定当前导出的数据列
                .includeColumnFieldNames(headField)
                // 指定表头
                .head(head)
                .build();
        // 重新构建数据，因为EasyExcel的缓存会导致即使动态设置index也会只有第一次生效
        List<List<Object>> newList = regenerateDataOrder(list, headField, responseClz);
        excelWriter.write(newList, writeSheet);
    }

}
