package com.sankuai.shangou.seashop.base.s3plus;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.security.SecureRandom;
import java.util.Optional;

import javax.annotation.Resource;

import org.joda.time.DateTime;

import com.hishop.starter.storage.client.StorageClient;
import com.hishop.starter.storage.model.StorageFileInfo;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * S3文件上传、下载方法
 *
 * <AUTHOR>
 * @CreateTime: 2023-11-13  10:08
 */
@Slf4j
public class S3plusStorageService {

    private static final SecureRandom random = new SecureRandom();

    @Resource
    private StorageClient storageClient;

    /**
     * 流式上传,从指定url地址获取文件流上传到s3
     *
     * @param objectName
     * @param url
     */
    public void uploadImage(String objectName, String url) throws Exception {
        InputStream inputStream = null;
        try {
            log.info("开始上传，objectName：{}", objectName);
            //bucketName指定上传文件所在的桶名（服务秘钥要用桶别名）
            //objectName指定上传的文件名
            //theStreamContentLengh指定上传文件流的content-length
            URLConnection connection = new URL(url).openConnection();

//            ObjectMetadata metadata = new ObjectMetadata();
//            //content-type可以替换
//            metadata.setContentType("image/png");
//            metadata.setContentLength(connection.getContentLength());

            inputStream = connection.getInputStream();
//            PutObjectResult putObjectResult = s3client.putObject(bucketName, objectName, inputStream, metadata);

            StorageFileInfo storageFileInfo = storageClient.putObject(objectName, inputStream, "image/png", null, null);

            log.info("上传图片完成，保存结果：{}", storageFileInfo);

        }
        catch (FileNotFoundException e) {
            log.error("图片地址找不到 {}", url);
            throw e;
        }
        catch (Exception e) {
            log.error("图片上传exception,", e);
            throw e;
        }
        finally {
            Optional.ofNullable(inputStream).ifPresent(in -> {
                try {
                    in.close();
                }
                catch (Exception e) {
                    log.error("stream close error", e);
                }
            });

        }
    }

    /**
     * 大文件流式下载
     *
     * @param objectName 对象名称
     */
    public void getObjectByStream(String objectName) {
        StorageFileInfo storageFileInfo = storageClient.downloadStream(objectName, null);
        try (InputStream content = storageFileInfo.getContent()) {
            BufferedReader reader = new BufferedReader(new InputStreamReader(content));
            while (true) {
                String line = reader.readLine();
                if (line == null) {
                    break;
                }
                //业务处理
            }
        }
        catch (Exception e) {
            //存储服务端处理异常
            log.error("download object by stream error,", e);
            throw new BusinessException("download object by stream error");
        }
    }

    public InputStream getObjectByInputStream(String objectName) {
        StorageFileInfo storageFileInfo = storageClient.downloadStream(objectName, null);
        try (InputStream content = storageFileInfo.getContent()) {
            return content;
        }
        catch (Exception e) {
            //存储服务端处理异常
            log.error("download object by stream error,", e);
            throw new BusinessException("download object by stream error");
        }
    }

    /**
     * 读取文件流
     *
     * @param fileUrl s3plus文件地址
     * @return
     * @throws IOException
     */
    public InputStream readExcelFromOnlineUrl(String fileUrl) throws IOException {
        URL url = new URL(MtUrlUtils.translate2IntranetUrl(fileUrl));
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setConnectTimeout(10 * 1000);

        if (conn.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new IllegalStateException("下载Excel文件失败");
        }

        return conn.getInputStream();
    }

    /**
     * 文件上传s3
     *
     * @param tempFile
     * @return
     */
    public String sendFile2S3(File tempFile) {
        AssertUtil.throwIfNull(tempFile, "文件不能为空");

//            s3client.putObject(bucketName, tempFile.getName(), tempFile);
        storageClient.putObject(tempFile.getName(), tempFile, null, null);
        return tempFile.getName();

    }

    /**
     * 文件上传s3
     *
     * @param tempFile
     * @param path     文件路径
     * @return
     */
    public String sendFile2S3(File tempFile, String path) {
        AssertUtil.throwIfNull(tempFile, "文件不能为空");

//            s3client.putObject(bucketName, path + "/" + tempFile.getName(), tempFile);
        storageClient.putObject(path + "/" + tempFile.getName(), tempFile, null, null);
        return tempFile.getName();


    }

    /**
     * 临时文件上传s3
     * (临时文件上传s3后会删除临时文件)
     *
     * @param tempFile
     * @return
     */
    public String sendTempFile2S3(File tempFile) {
        AssertUtil.throwIfNull(tempFile, "文件不能为空");
        try {
//            s3client.putObject(bucketName, tempFile.getName(), tempFile);
            storageClient.putObject(tempFile.getName(), tempFile, null, null);
            return tempFile.getName();
        }
        finally {
            //删除临时文件
            tempFile.delete();
        }
    }

    /**
     * 临时文件上传s3
     * (临时文件上传s3后会删除临时文件)
     *
     * @param tempFile
     * @param path     文件路径
     * @return
     */
    public String sendTempFile2S3(File tempFile, String path) {
        AssertUtil.throwIfNull(tempFile, "文件不能为空");
        try {
//            s3client.putObject(bucketName, path + "/" + tempFile.getName(), tempFile);
            storageClient.putObject(path + "/" + tempFile.getName(), tempFile, null, null);
            return tempFile.getName();
        }
        finally {
            //删除临时文件
            tempFile.delete();
        }
    }

    /**
     * 获取下载路径
     */
    public String getDownloadUrl(String fileName) {
        DateTime now = new DateTime();
        now.plusDays(7);
        URL url = storageClient.generatePresignedUrl(fileName, now.toDate());
        return url.toString();
    }

    /**
     * 内存流上传
     *
     * @param outputStream
     * @param filePre
     * @return
     */
    public String sendFileStream2S3(ByteArrayOutputStream outputStream, String filePre) {
        AssertUtil.throwIfNull(outputStream, "文件流不能为空");
        ByteArrayInputStream inputStream = null;
        try {
            String fileName = createFileName(filePre);
            inputStream = new ByteArrayInputStream(outputStream.toByteArray());
//            s3client.putObject(bucketName, fileName, inputStream, null);
            storageClient.putObject(fileName, inputStream, null, null, null);
            return fileName;
        }
        finally {
            try {
                outputStream.close();
                if (inputStream != null) {
                    inputStream.close();
                }
            }
            catch (IOException e) {
                log.error("关闭流异常，", e);
            }
        }
    }

    /**
     * 字节上传
     *
     * @param bytes
     * @param filePath 文件完整路径（含文件名）
     * @return
     */
    public String sendByte2S3(byte[] bytes, String filePath) {
        AssertUtil.throwIfNull(bytes, "文件字节不能为空");

//            s3client.putObject(bucketName, filePath, new ByteArrayInputStream(bytes), null);
        storageClient.putObject(filePath, bytes, null, null, null);
        return filePath;

    }

    public String sendFileStream2S3ToFilePath(ByteArrayOutputStream outputStream, String fileName) {
        AssertUtil.throwIfNull(outputStream, "文件流不能为空");
        ByteArrayInputStream inputStream = null;
        try {

            inputStream = new ByteArrayInputStream(outputStream.toByteArray());
//            s3client.putObject(bucketName, fileName, inputStream, null);
            storageClient.putObject(fileName, inputStream, null, null, null);
            return fileName;
        }
        finally {
            try {
                outputStream.close();
                if (inputStream != null) {
                    inputStream.close();
                }
            }
            catch (IOException e) {
                log.error("关闭流异常，", e);
            }
        }


    }

    private String createFileName(String tempFilePre) {
        long n = random.nextLong();
        if (n == Long.MIN_VALUE) {
            n = 0;
        }
        else {
            n = Math.abs(n);
        }

        return tempFilePre + "-" + System.currentTimeMillis() + "-" + n + ".xlsx";
    }

    /**
     * 当前使用的文件载体。
     * <p>file不为空，则使用文件；outputStream不为空，则使用内存</p>
     */
    @Getter
    private static class FileCarrier {
        private File file;
        private ByteArrayOutputStream outputStream;

        FileCarrier(String tempFilePre, boolean uploadUseFileBuffer) throws IOException {
            if (uploadUseFileBuffer) {
                String fileName = tempFilePre + "-" + System.currentTimeMillis() + "-";
                this.file = File.createTempFile(fileName, ".xlsx");
            }
            else {
                outputStream = new ByteArrayOutputStream();
            }
        }
    }
}
