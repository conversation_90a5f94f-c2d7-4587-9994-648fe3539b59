package com.sankuai.shangou.seashop.base.export.handler;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface BaseDataGetter<D> extends ExportDataGetter {

    /**
     * 当前sheet的名称，默认为 sheet0
     */
    default String sheetName() {
        return null;
    }

    /**
     * 当前sheet需要导出的数据，列表的每一项对应sheet的一行数据
     * <AUTHOR>
     */
    List<D> getDataList();

    default Class<D> getResponseClz() {
        // 获取当前类的泛型父类或接口
        Type genericSuperclass = getClass().getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            // 强转为ParameterizedType
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            // 获取泛型的实际类型参数
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            // 假设我们只有一个泛型参数，获取第一个
            Type type = actualTypeArguments[0];
            if (type instanceof Class) {
                // 强转为Class类型
                return (Class<D>) type;
            }
        }
        throw new RuntimeException("无法获取泛型类型");
    }

}
