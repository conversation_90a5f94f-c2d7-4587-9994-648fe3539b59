package com.sankuai.shangou.seashop.base.export.writeHandler;

import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFFont;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
public class DefaultCenterCellStyleWriteHandler extends CellStyleWriteHandler {

    private final static HorizontalAlignment horizontalAlignment = HorizontalAlignment.CENTER;
    private final static VerticalAlignment verticalAlignment = VerticalAlignment.CENTER;
    private final static List<Integer> relativeRowIndexList = Collections.singletonList(0);
    private final static Font HEAD_FONT = new XSSFFont();
    private final static Font ROW_FONT = new XSSFFont();

    static {
        HEAD_FONT.setBold(true);
        HEAD_FONT.setFontHeightInPoints((short)14);

        ROW_FONT.setFontHeightInPoints((short)10);
    }

    public DefaultCenterCellStyleWriteHandler() {
        super(horizontalAlignment, verticalAlignment, relativeRowIndexList, HEAD_FONT, ROW_FONT);
    }

    private final static DefaultCenterCellStyleWriteHandler DEFAULT_INSTANCE = new DefaultCenterCellStyleWriteHandler();
    public static DefaultCenterCellStyleWriteHandler getInstance() {
        return DEFAULT_INSTANCE;
    }
}
