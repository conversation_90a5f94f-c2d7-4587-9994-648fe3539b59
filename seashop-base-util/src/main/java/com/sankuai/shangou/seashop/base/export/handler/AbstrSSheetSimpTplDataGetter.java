package com.sankuai.shangou.seashop.base.export.handler;


import com.sankuai.shangou.seashop.base.export.template.TemplateDataWrapper;
import com.sankuai.shangou.seashop.base.export.writter.ExcelWriteStrategy;
import com.sankuai.shangou.seashop.base.export.writter.SSheetSimpTplExcelWriteStgy;
import com.sankuai.shangou.seashop.base.export.writter.WriteStrategyFetcher;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

/**
 * <AUTHOR>
 * @date 2023/5/6
 */
public abstract class AbstrSSheetSimpTplDataGetter<R, P extends BasePageReq>
        implements SingleTempDataGetter<R, P>, WriteStrategyFetcher {

    private TemplateDataWrapper<R> templateDataWrapper;

    @Override
    public ExcelWriteStrategy getStrategy() {
        return new SSheetSimpTplExcelWriteStgy();
    }

    public TemplateDataWrapper<R> getTemplateDataWrapper() {
        return templateDataWrapper;
    }

    public void setTemplateDataWrapper(TemplateDataWrapper<R> templateDataWrapper) {
        this.templateDataWrapper = templateDataWrapper;
    }
}
