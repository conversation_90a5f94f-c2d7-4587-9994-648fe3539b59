package com.sankuai.shangou.seashop.base.export.writeHandler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Slf4j
public class ColumnRowMergeWriteHandler extends AbstractMergeStrategy {

    private int mergeFromRow;
    private int mergeToRow;
    private int firstColumnIndex;
    private int lastColumnIndex;

    public ColumnRowMergeWriteHandler(int mergeFromRow, int mergeToRow, int firstColumnIndex, int lastColumnIndex) {
        this.mergeFromRow = mergeFromRow;
        this.mergeToRow = mergeToRow;
        this.firstColumnIndex = firstColumnIndex;
        this.lastColumnIndex = lastColumnIndex;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        log.info("【合并单元格】开始合并的行号：{}，合并到第{}行，开始合并的列：{}，合并到第{}列， 当前行相对索引：{}",
                mergeFromRow, mergeToRow, firstColumnIndex, lastColumnIndex, relativeRowIndex);
        CellRangeAddress cellRangeAddress = new CellRangeAddress(mergeFromRow, mergeToRow, firstColumnIndex, lastColumnIndex);
        sheet.addMergedRegionUnsafe(cellRangeAddress);
        mergeFromRow = mergeFromRow + 1;
        mergeToRow = mergeToRow + 1;
    }

}
