package com.sankuai.shangou.seashop.base.eimport.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.sankuai.shangou.seashop.base.eimport.input.ImportWay;
import com.sankuai.shangou.seashop.base.eimport.input.S3ImportWay;

/**
 * <AUTHOR>
 * @date 2023/4/6
 */
@Configuration
public class ImportAutoConfiguration {

    @Bean
    @ConditionalOnProperty(name = "s3plus.hostName")
    public ImportWay importWay() {
        return new S3ImportWay();
    }

}
