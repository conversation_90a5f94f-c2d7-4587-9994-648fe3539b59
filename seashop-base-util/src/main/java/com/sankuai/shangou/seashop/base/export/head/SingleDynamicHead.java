package com.sankuai.shangou.seashop.base.export.head;

import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
public interface SingleDynamicHead<P> {

    /**
     * 一个sheet能导出的可能有10个字段，但当前可能只需要导出5个字段，selectedSheetFieldList 定义每个sheet需要导出的字段
     * sheetHeadFieldList 定义所有字段以及对应顺序
     * <AUTHOR>
     * @date 2023/4/24
     */
    List<String> selectedSheetFieldList(ExportDataGetter exportService, P param);

}
