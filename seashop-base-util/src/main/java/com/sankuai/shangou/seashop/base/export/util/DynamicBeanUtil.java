package com.sankuai.shangou.seashop.base.export.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.hash.MurmurHash;
import cn.hutool.crypto.digest.MD5;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanGenerator;
import org.springframework.cglib.beans.BeanMap;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/5/19
 */
@Slf4j
public final class DynamicBeanUtil {

    /**
     * @param object    旧的对象带值
     * @param propertyMap    动态需要添加的属性和属性类型
     * @return 新的对象
     * @throws
     */
    public static Object dynamicClass(Object object, Map<String, Class> propertyMap) {
        // 返回的字段名称和字段值
        LinkedHashMap<String, Object> returnMap = new LinkedHashMap<>();
        // 动态创建的Bean的名称和类型，基于这个创建对象。
        LinkedHashMap<String, Class> typeMap = new LinkedHashMap<>();

        try {
            Class<?> type = object.getClass();
            // 在 Java Bean上进行内省,了解其所有属性、公开的方法和事件
            BeanInfo beanInfo = Introspector.getBeanInfo(type);
            // 获得 beans PropertyDescriptor
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (int i = 0; i < propertyDescriptors.length; i++) {
                PropertyDescriptor descriptor = propertyDescriptors[i];
                // 获得所有对象属性值得名字
                String propertyName = descriptor.getName();
                if ("class".equals(propertyName)) {
                    continue;
                }
                // 设置应用于读取属性值的方法
                Method readMethod = descriptor.getReadMethod();
                // 处理代理实例上的方法调用并返回结果
                Object result = readMethod.invoke(object);
                if (descriptor.getPropertyType().isAssignableFrom(Map.class) || descriptor.getPropertyType().isAssignableFrom(LinkedHashMap.class)) {
                    LinkedHashMap mapValue = (LinkedHashMap) result;
                    if (CollUtil.isNotEmpty(mapValue)) {
                        List<String> mapKeyList = CollUtil.newArrayList(mapValue.keySet());
                        mapKeyList.stream().forEach(x -> {
                            returnMap.put(buildExtFieldKey(x), mapValue.get(x));
                        });
                    }
                } else {
                    returnMap.put(propertyName, result);
                }
            }

            typeMap.putAll(propertyMap);
            // map转换成实体对象
            DynamicBean bean = new DynamicBean(typeMap);
            // 赋值
            Set<String> keys = typeMap.keySet();
            for (Iterator it = keys.iterator(); it.hasNext(); ) {
                String key = (String) it.next();
                bean.setValue(key, returnMap.get(key));
            }
            return bean.getObject();
        } catch (Exception e) {
            log.warn("动态添加参数错误：{}", e);
            return object;
        }
    }

    /**
     * 生成map扩展属性对应的字段名称。因为导出时可能时日期这种数字表头，而Java字段名称只支持 _、数字和英文符号，所以需要处理下
     * <p>由于map的字段可能是中文和括号等字符，所以采用将原始的名称通过hash算法生成属性名称的方式来实现。MurmurHash3是性能和冲突率都优秀的hash算法
     * ，表格的字段也不会特别多，hash实现问题不大，如果后续出现冲突了在做考虑</p>
     * <AUTHOR>
     * @date 2023/5/19
     */
    public static String buildExtFieldKey(String originName) {
        long hash = MurmurHash.hash64(originName);
        long nameHash = 0;
        if (hash < 0) {
            nameHash = hash * -1;
        } else {
            nameHash = hash;
        }
        String md5 = MD5.create().digestHex(originName).replaceAll("-", "");
        return "_" + nameHash + "_" + md5;
    }

    private static class DynamicBean {
        // 动态生成的类
        private Object object = null;
        // 存放属性名称以及属性的类型
        private BeanMap beanMap = null;

        public DynamicBean() {
            super();
        }

        public DynamicBean(LinkedHashMap<String, Class> propertyMap) {
            this.object = generateBean(propertyMap);
            this.beanMap = BeanMap.create(this.object);
        }

        /**
         * 生成Bean
         *
         * @param propertyMap
         * @return
         */
        private Object generateBean(LinkedHashMap<String, Class> propertyMap) {
            BeanGenerator generator = new BeanGenerator();
            for (Map.Entry<String, Class> et : propertyMap.entrySet()) {
                String key = et.getKey();
                generator.addProperty(key, propertyMap.get(key));
            }
            return generator.create();
        }

        /**
         * 给bean属性赋值
         *
         * @param property 属性名
         * @param value    值
         */
        public void setValue(Object property, Object value) {
            beanMap.put(property, value);
        }

        /**
         * 通过属性名得到属性值
         *
         * @param property 属性名
         * @return 值
         */
        public Object getValue(String property) {
            return beanMap.get(property);
        }

        /**
         * 得到该实体bean对象
         *
         * @return
         */
        public Object getObject() {
            return this.object;
        }
    }
}
