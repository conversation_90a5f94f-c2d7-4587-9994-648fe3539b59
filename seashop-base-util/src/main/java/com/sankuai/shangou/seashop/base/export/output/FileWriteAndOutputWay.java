package com.sankuai.shangou.seashop.base.export.output;


import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
public interface FileWriteAndOutputWay<P extends BasePageReq> {

    String writeAndOutput(ExportDataGetter exportService, String taskId, Date taskDate, P param);

}
