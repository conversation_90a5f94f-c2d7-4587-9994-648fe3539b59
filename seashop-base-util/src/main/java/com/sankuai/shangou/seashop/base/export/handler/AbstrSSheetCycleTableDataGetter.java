package com.sankuai.shangou.seashop.base.export.handler;


import com.sankuai.shangou.seashop.base.export.writter.ExcelWriteStrategy;
import com.sankuai.shangou.seashop.base.export.writter.SSheetCycleTableWriteStgy;
import com.sankuai.shangou.seashop.base.export.writter.WriteStrategyFetcher;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
public abstract class AbstrSSheetCycleTableDataGetter<R, P extends BasePageReq>
        implements SSheetCycleTableDataGetter<R, P>, WriteStrategyFetcher {

    @Override
    public ExcelWriteStrategy getStrategy() {
        return new SSheetCycleTableWriteStgy();
    }
}
