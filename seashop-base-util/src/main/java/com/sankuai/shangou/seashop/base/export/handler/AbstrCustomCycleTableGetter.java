package com.sankuai.shangou.seashop.base.export.handler;


import com.sankuai.shangou.seashop.base.export.writter.CustomCycleTableWriteStgy;
import com.sankuai.shangou.seashop.base.export.writter.ExcelWriteStrategy;
import com.sankuai.shangou.seashop.base.export.writter.WriteStrategyFetcher;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

/**
 * 定制化的表格数据获取器，专门针对 配送-待配货订单明细 导出
 * 构造数据时，团长名称需要合并单元格的，对象需要使用 CustomTableWrapper
 * <p>团长名称+自提点名称+时间+地址+联系电话 构成一个数据块， 即一个 TableWrapper，这里是 CustomTableWrapper，
 * 定义一个类，包含一个string字段，5个字段，每个字段显示完整的文字内容</p>
 * <p>包含表头的订单+商品数据，也构成一个数据块， 即一个 TableWrapper</p>
 * <p>中间需要留白的区域也是一个数据块</p>
 * <p>所以整体就是多个数据块一次输出，形成excel</p>
 * <AUTHOR>
 * @date 2023/5/16
 */
public abstract class AbstrCustomCycleTableGetter<R, P extends BasePageReq>
        implements SSheetCycleTableDataGetter<R, P>, WriteStrategyFetcher {

    @Override
    public ExcelWriteStrategy getStrategy() {
        return new CustomCycleTableWriteStgy();
    }

}
