package com.sankuai.shangou.seashop.base.export.writter;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * @date 2023/4/21
 */
public interface ExcelWriteStrategy<P extends BasePageReq> {

    /**
     * 将数据list写入到excel，并返回字节数组
     * <AUTHOR>
     * @date 2023/4/21
     */
    ByteArrayOutputStream writeToOutputStream(ExportDataGetter exportService, P param);

}
