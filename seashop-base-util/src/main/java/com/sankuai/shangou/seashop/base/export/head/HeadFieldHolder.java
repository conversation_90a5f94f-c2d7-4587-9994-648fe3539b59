package com.sankuai.shangou.seashop.base.export.head;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
@Data
@Builder
public class HeadFieldHolder {

    /**
     * 单个表头内容
     */
    private List<List<String>> singleHeadList;
    /**
     * 单份根据业务需要排序后的表头字段
     */
    private List<String> singleSortedSelect;

    /**
     * 多个表头的内容
     */
    private List<List<List<String>>> multiHeadList;
    /**
     * 多份根据业务需要排序后的表头字段
     */
    private List<List<String>> multiSortedSelect;

}
