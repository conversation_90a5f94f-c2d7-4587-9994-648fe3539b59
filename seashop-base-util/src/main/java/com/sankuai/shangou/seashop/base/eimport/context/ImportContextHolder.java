package com.sankuai.shangou.seashop.base.eimport.context;

import org.springframework.core.NamedInheritableThreadLocal;

/**
 * <AUTHOR>
 * @date 2023/12/05 17:43
 */
public class ImportContextHolder {

    private static final ThreadLocal<ImportContext> IMPORT_CONTEXT_THREAD_LOCAL = new NamedInheritableThreadLocal<>("export-context");

    public static void remove() {
        IMPORT_CONTEXT_THREAD_LOCAL.remove();
    }

    public static void set(ImportContext context) {
        IMPORT_CONTEXT_THREAD_LOCAL.set(context);
    }

    public static <T> T get() {
        ImportContext context = IMPORT_CONTEXT_THREAD_LOCAL.get();
        if (context == null) {
            return null;
        }
        return (T) IMPORT_CONTEXT_THREAD_LOCAL.get();
    }

}
