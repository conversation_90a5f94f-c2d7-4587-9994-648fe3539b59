package com.sankuai.shangou.seashop.base.eimport;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/10
 */
@Slf4j
public abstract class ValidHeaderReadListener<D extends RowReadResult> extends DefaultReadListener<D> {


    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析表头:headMap={},context={}", headMap, context);
        List<String> curHeaderList = headMap.values().stream().map(CellData::getStringValue).collect(Collectors.toList());
        String curHeaderStr = Optional.ofNullable(curHeaderList).orElse(Collections.emptyList()).stream().collect(Collectors.joining(","));
        String expectedHeaderStr = getExpectedHeads().stream().collect(Collectors.joining(","));
        log.info("当前表头:{},期望表头:{}", curHeaderStr, expectedHeaderStr);
        if (!curHeaderStr.equals(expectedHeaderStr)) {
            throw new BusinessException("导入模板不正确,请下载最新模板!");
        }
    }

    /**
     * 预期导入的表头
     *
     * @return 预期导入的表头
     */
    protected abstract List<String> getExpectedHeads();

}
