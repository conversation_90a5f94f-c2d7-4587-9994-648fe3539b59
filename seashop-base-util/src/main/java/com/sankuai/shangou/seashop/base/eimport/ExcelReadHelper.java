package com.sankuai.shangou.seashop.base.eimport;

import java.io.InputStream;
import java.util.List;
import java.util.function.Consumer;

import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

/**
 * 基于EasyExcel的Excel读取工具类，用工具类的目的是屏蔽业务系统对第三方的直接依赖
 * <AUTHOR>
 * @date 2023/7/10
 */
@Slf4j
public class ExcelReadHelper {

    /**
     * 读取Excel内容
     * <AUTHOR>
     * @date 2023/7/10
     */
    public static <D extends RowReadResult> ReadResult<D> read(MultipartFile file, Class<D> clazz) {
        return read(file, clazz, null, null, null);
    }

    /**
     * 读取Excel内容
     * <AUTHOR>
     * @date 2023/7/10
     */
    public static <D extends RowReadResult> ReadResult<D> read(MultipartFile file, Class<D> clazz, Integer maxCount) {
        return read(file, clazz, maxCount, null, null);
    }

    /**
     * 读取Excel内容
     * <AUTHOR>
     * @date 2023/7/10
     */
    public static <D extends RowReadResult> ReadResult<D> read(MultipartFile file, Class<D> clazz, Integer batchCount, Consumer<List<D>> batchConsumer) {
        return read(file, clazz, null, batchCount, batchConsumer);
    }

    /**
     * 读取Excel内容
     * <AUTHOR>
     * @date 2023/7/10
     */
    public static <D extends RowReadResult> ReadResult<D> read(MultipartFile file, Class<D> clazz, Integer maxCount, Integer batchCount, Consumer<List<D>> batchConsumer) {
        return read(file, clazz, new DefaultReadListener<>(maxCount, batchCount, batchConsumer));
    }

    /**
     * 读取Excel内容
     * <AUTHOR>
     * @date 2023/7/10
     */
    public static <D extends RowReadResult> ReadResult<D> read(MultipartFile file, Class<D> clazz, DefaultReadListener<D> listener) {
        String fileName = null;
        try {
            fileName = file.getOriginalFilename();
            ReadResult<D> result = read(file.getInputStream(), clazz, listener);
            log.info("读取导入Excel内容成功, fileName={}, size={}", fileName, result.getDataList().size());
            return result;
        } catch (Exception e) {
            log.error(String.format("读取导入Excel内容异常, fileName=%s", fileName), e);
            throw new BusinessException("读取导入Excel内容异常");
        }
    }

    /**
     * 读取Excel内容
     * <AUTHOR>
     * @date 2023/7/10
     */
    public static <D extends RowReadResult> ReadResult<D> read(InputStream inputStream, Class<D> clazz, DefaultReadListener<D> listener) {
        try {
            EasyExcel.read(inputStream, clazz, listener).sheet().doRead();
            List<D> dataList = listener.getDataList();
            ReadResult<D> result = new ReadResult<>();
            result.setDataList(dataList);
            result.setHasErr(listener.isHasErr());
            return result;
        } catch (Exception e) {
            log.error("读取导入Excel内容异常", e);
            throw new BusinessException("读取导入Excel内容异常");
        }
    }

}
