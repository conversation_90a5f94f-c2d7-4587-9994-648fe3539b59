package com.sankuai.shangou.seashop.base.eimport;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * 导入结果对象
 *
 * <AUTHOR>
 * @date 2023/11/21 21:41
 */
@Builder
@Setter
@Getter
public class ImportResult {

    /**
     * 导出成功条数
     */
    private int successCount;

    /**
     * 导入失败条数
     */
    private int errCount;

    /**
     * 失败文件下载路径
     */
    private String filePath;

}
