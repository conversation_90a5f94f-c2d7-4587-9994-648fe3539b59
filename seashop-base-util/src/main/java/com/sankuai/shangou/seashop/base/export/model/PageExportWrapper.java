package com.sankuai.shangou.seashop.base.export.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.extern.slf4j.Slf4j;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Slf4j
public abstract class PageExportWrapper<T, P extends BasePageReq> implements BaseExportWrapper<T, P> {

    private final AtomicInteger total = new AtomicInteger(0);
    private final AtomicInteger prevNum = new AtomicInteger(0);
    private final AtomicReference<List<?>> dataSupplier = new AtomicReference<>(Collections.emptyList());

    public PageExportWrapper() {

    }

    public PageExportWrapper(int ignoreNum) {
        total.getAndAdd(ignoreNum);
        prevNum.getAndAdd(ignoreNum);
    }

    @Override
    public List<T> getDataList(P param) {
        log.info("【数据导出】分页导出, total={}, prevNum={}, 查询参数: {}", total.get(), prevNum.get(), JSONUtil.toJsonStr(param));
        List<T> dataList = getPageList(param);
        // 排除掉 pageSize=1是因为 BaseWriteStgy 处理的时候，会首先查一条数据来构造表头这些，是特殊的，不在分页查询范围内，所以排除
        if (param.getPageSize() != 1 && CollUtil.isNotEmpty(dataList)) {
            total.getAndAdd(dataList.size());
            log.info("【数据导出】分页导出, 处理前置忽略行前, total={}, prevNum={}, 数据大小={}", total.get(), prevNum.get(), dataList.size());
            prevNum.getAndAdd(total.get() - prevNum.get() - dataList.size());
            log.info("【数据导出】分页导出, 处理前置忽略行后, total={}, prevNum={}, 数据大小={}", total.get(), prevNum.get(), dataList.size());
            dataSupplier.getAndSet(dataList);
        }
        return dataList;
    }

    public abstract List<T> getPageList(P param);

    protected AtomicInteger getPrevNum() {
        return prevNum;
    }

    protected AtomicReference<List<?>> getDataSupplier() {
        return dataSupplier;
    }

}
