package com.sankuai.shangou.seashop.base.export.anno;


import com.sankuai.shangou.seashop.base.export.enums.MergeStyle;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于指定某个数据属性是否需要进行合并
 * <AUTHOR>
 * @date 2023/8/10
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ShouldMerge {

    MergeStyle style() default MergeStyle.ALL;

}
