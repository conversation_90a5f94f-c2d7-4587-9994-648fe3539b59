package com.sankuai.shangou.seashop.base.lock.config;

import com.sankuai.shangou.seashop.base.lock.SquirrelDistributedLockServiceImpl;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: wbhz_chenpeng1
 * @CreateTime: 2023-11-09  10:08
 * @Description: 分布式锁配置
 * @Version: 1.0
 */
@Configuration
public class DistributeLockConfiguration {

    @ConditionalOnMissingBean
    @Bean
    public SquirrelDistributedLockServiceImpl squirrelDistributedLockService(RedissonClient redissonClient) {
        return new SquirrelDistributedLockServiceImpl(redissonClient);
    }
}