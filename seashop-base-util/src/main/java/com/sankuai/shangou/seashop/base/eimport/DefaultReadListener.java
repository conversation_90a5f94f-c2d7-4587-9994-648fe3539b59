package com.sankuai.shangou.seashop.base.eimport;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.sankuai.shangou.seashop.base.boot.utils.ValidationUtil;
import com.sankuai.shangou.seashop.base.eimport.anno.ExcelField;
import com.sankuai.shangou.seashop.base.eimport.regex.FieldRegexEnum;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/10
 */
@Slf4j
public class DefaultReadListener<D extends RowReadResult> implements ReadListener<D> {

    private Integer batchCount;
    private Integer maxCount;
    private Consumer<List<D>> batchConsumer;
    private Function<D, String> uniqueKeyFunc;
    private Map<String, D> dataMap;
    private boolean hasErr = false;


    public DefaultReadListener() {
    }

    public DefaultReadListener(Integer maxCount) {
        this.maxCount = maxCount;
    }

    public DefaultReadListener(Integer batchCount, Consumer<List<D>> batchConsumer) {
        this.batchCount = batchCount;
        this.batchConsumer = batchConsumer;
    }

    public DefaultReadListener(Integer maxCount, Integer batchCount, Consumer<List<D>> batchConsumer) {
        this.batchCount = batchCount;
        this.maxCount = maxCount;
        this.batchConsumer = batchConsumer;
    }

    private List<D> dataList = new ArrayList<>(1000);

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(D data, AnalysisContext context) {
        Integer rowNum = context.readRowHolder().getRowIndex();
        log.debug("解析到一条数据, row={}, data:{}", rowNum, JSONUtil.toJsonStr(data));
        // 先判断是否超过限制
        int size = dataList.size();
        if (maxCount != null && size >= maxCount) {
            throw new RuntimeException("最大支持导入数量：" + maxCount);
        }
        // 设置行号
        data.setRowNum(rowNum);
        // 校验行数据
        validRowData(data);
        dataList.add(data);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (batchCount != null && size >= batchCount) {
            batchConsumer.accept(dataList);
            // 存储完成清理 list
            dataList = new ArrayList<>(batchCount);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        log.info("所有数据解析完成！");
    }

    public List<D> getDataList() {
        return dataList;
    }

    public void setUniqueKeyFunc(Function<D, String> uniqueKeyFunc) {
        this.uniqueKeyFunc = uniqueKeyFunc;
        dataMap = new HashMap<>(32);
    }

    private void validRowData(D data) {
        // 目前字段校验未通过 不会继续校验数据
        String errMsg = validField(data);
        errMsg = errMsg + checkData(data);
        if (uniqueKeyFunc != null) {
            String uniqueKey = uniqueKeyFunc.apply(data);
            if (dataMap.containsKey(uniqueKey)) {
                if (errMsg == null) {
                    errMsg = "";
                }
                errMsg += String.format("与表格中的第%d行数据重复", dataMap.get(uniqueKey).getRowNum());
            }
            else {
                dataMap.put(uniqueKey, data);
            }
        }
        if (StrUtil.isBlank(errMsg)) {
            return;
        }
        hasErr = true;
        data.setErrMsg(errMsg);
    }

    protected String checkData(D data) {
        return StringUtils.EMPTY;
    }

    public boolean isHasErr() {
        return hasErr;
    }

    /**
     * 校验字段, 提取字段上的ExcelField 注解，校验字段值
     *
     * @param data 行数据
     * @return 错误信息
     */
    private String validField(D data) {
        StringBuilder errBuilder = new StringBuilder();
        Class<? extends RowReadResult> clazz = data.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            ExcelField excelField = field.getAnnotation(ExcelField.class);
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelField == null || excelProperty == null) {
                continue;
            }

            try {
                Object fieldVal = FieldUtils.readField(field, data);
                boolean fieldEmpty = ObjectUtils.isEmpty(fieldVal);

                if (excelField.required() && fieldEmpty) {
                    errBuilder.append(String.format("【%s】为必填项;", excelProperty.value()[0]));
                }

                String regex = excelField.regex();
                String regexMsg = excelField.regexMsg();
                if (excelField.regexEnum() != null && !excelField.regexEnum().equals(FieldRegexEnum.NONE)) {
                    regex = excelField.regexEnum().getRegex();
                    regexMsg = excelField.regexEnum().getRegexMsg();
                }
                if (!fieldEmpty && StringUtils.isNotEmpty(regex)) {
                    if (!ValidationUtil.isMatchRegex(Pattern.compile(regex), String.valueOf(fieldVal))) {
                        errBuilder.append(String.format("【%s】%s;", excelProperty.value()[0], regexMsg));
                    }
                }
            }
            catch (Exception e) {
                log.error("读取字段值异常", e);
            }
        }
        return errBuilder.toString();
    }
}
