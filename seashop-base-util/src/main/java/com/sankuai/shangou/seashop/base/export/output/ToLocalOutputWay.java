package com.sankuai.shangou.seashop.base.export.output;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Slf4j
public class ToLocalOutputWay<P extends BasePageReq> extends AbstractWriteAndOutput<P> {

    private final static String FILE_PATH = "E:\\";

    @Override
    protected String outputFile(byte[] data, String fileName, String taskId, String curTimeStr) {
        String dir = FILE_PATH + File.separator + taskId;
        File dirFile = new File(dir);
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }
        String fileUrl = dir + File.separator + fileName + curTimeStr + ".xlsx";
        try (FileOutputStream out = new FileOutputStream(fileUrl)){
            out.write(data);
        } catch (Exception e) {
            log.info("写入文件失败：", e);
            throw new RuntimeException(e);
        }
        return fileUrl;
    }
}
