package com.sankuai.shangou.seashop.base.export.exec;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter;
import com.sankuai.shangou.seashop.base.export.config.ExportTaskProps;
import com.sankuai.shangou.seashop.base.export.context.ExportTaskContextHolder;
import com.sankuai.shangou.seashop.base.export.handler.ExecuteResult;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.base.export.handler.ExportTaskHandler;
import com.sankuai.shangou.seashop.base.export.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractExcelExportExec implements ExcelExportExec {

    private final ExportTaskHandler exportTaskHandler;
    //    private final ThreadPool threadPool;
    private final int MAX_TASK_COUNT;
    private final int FULL_LIMIT;

    private final ThreadPoolExecutor threadPool;

    public AbstractExcelExportExec(ExportTaskHandler exportTaskHandler, ExportTaskProps taskProps) {
        this.exportTaskHandler = exportTaskHandler;

//        DefaultThreadPoolProperties.Setter setter = DefaultThreadPoolProperties.Setter()
//                .withCoreSize(taskProps.getExecPoolCoreSize())
//                .withMaxSize(taskProps.getExecPoolMaxSize())
//                .withKeepAliveTimeMinutes(taskProps.getExecPoolKeepAliveSeconds())
//                .withKeepAliveTimeUnit(TimeUnit.SECONDS)
//                .withBlockingQueue(new LinkedBlockingQueue<>())
//                .withMaxQueueSize(taskProps.getExecPoolQueueSize())
//                .withThreadFactory(new ThreadFactoryBuilder().setNameFormat(taskProps.getExecPoolThreadName()).build());
//        threadPool = Rhino.newThreadPool(ThreadPoolConst.RHINO_KEY_EXPORT_TASK_EXEC, setter);
        threadPool = new ThreadPoolExecutor(taskProps.getExecPoolCoreSize(), taskProps.getExecPoolMaxSize(),
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                r -> {
                    Thread thread = new Thread(r);
                    thread.setName(taskProps.getExecPoolThreadName());
                    return thread;
                });

        FULL_LIMIT = taskProps.getExecPoolCoreSize();
        MAX_TASK_COUNT = taskProps.getExecPoolMaxSize();
    }


    private final static Map<String, Future<?>> TASK_FUTURE_MAP = new ConcurrentHashMap<>();


    private final static AtomicInteger FULL_COUNT = new AtomicInteger(0);

    public void checkAndRedo() {
        List<ExportTask> list = getRedoTaskList();
        if (list == null || list.isEmpty()) {
            return;
        }
        log.info("当前需要执行的任务数量：{}", list.size());
        for (ExportTask task : list) {
            executeTaskAsync(task);
        }
    }

    public void executeTaskAsync(ExportTask task) {
        // 超过处理能力暂不处理，等待定时任务调度补偿
        int activeCount = threadPool.getActiveCount();

        log.info("当前线程池活跃线程数：{}, 满处理能力：{}", activeCount, MAX_TASK_COUNT);
        // 如果溢出次数超过阈值，则尝试取消部分任务，防止老任务无法结束导致新任务不执行，老的任务等待定时任务恢复
        if (activeCount >= MAX_TASK_COUNT) {
            log.info("当前线程池溢出处理次数：{}", FULL_COUNT.get());
            /*FULL_COUNT.addAndGet(1);
            if (FULL_COUNT.get() <= FULL_LIMIT) {
                return;
            }
            //cancelTask();
            FULL_COUNT.set(0);*/
            return;
        }
        Future<?> taskFuture = threadPool.submit(() -> executeTask(task));
        TASK_FUTURE_MAP.put(task.getTaskId(), taskFuture);
    }

    public void executeTask(ExportTask task) {
        String traceId = getTraceId();
        if (!StringUtils.hasLength(traceId)) {
            traceId = UUID.randomUUID().toString().replace("-", "");
            MDC.put(TraceIdFilter.MDC_TRACE_ID, traceId);
        }
        long begin = System.currentTimeMillis();
        log.info("执行导出任务, 线程ID={}, 开始时间戳={}, 任务内容: {}", Thread.currentThread().getName(), begin, JSONUtil.toJsonStr(task));
        String errMsg = null;
        try {
            // 设置导出任务上下文
            ExportTaskContextHolder.addContext(task.getTaskId(), begin);
            // 修改状态为进行中
            start(task);
            log.info("任务id={}，修改状态为进行中成功", task.getTaskId());
            ExecuteResult result = exportTaskHandler.execute(task);
            // 设置导出后的文件路径
            task.setFilePath(result.getFilePath());
            if (!result.isSuccess()) {
                errMsg = "未找到对应的数据获取器";
            }
        } catch (Exception e) {
            log.error("执行导出任务异常", e);
            String msg = e.getMessage();
            if (msg == null) {
                try {
                    errMsg = e + e.getStackTrace()[0].toString();
                } catch (Exception exception) {
                    errMsg = "执行导出任务异常";
                }
            } else if (msg.length() > 500) {
                errMsg = msg.substring(0, 500);
            } else {
                errMsg = msg;
            }
        } finally {
            long end = System.currentTimeMillis();
            int cost = (int) (end - begin);
            log.info("任务id={}执行结束, 线程ID={}, 开始时间戳={}, 结束时间戳={}, 耗时={}ms",
                    Thread.currentThread().getName(), begin, end, task.getTaskId(), cost);
            // 修改任务记录
            TaskContext context = ExportTaskContextHolder.get();
            task.setCount(context.getTotal());
            task.setCost(cost);
            try {
                if (errMsg != null) {
                    String msg = "traceId=" + traceId + ", " + errMsg;
                    if (msg.length() > 500) {
                        msg = msg.substring(0, 500);
                    }
                    task.setException(msg);
                    exception(task);
                } else {
                    complete(task);
                    log.info("任务id={}，执行结束", task.getTaskId());
                }
            } catch (Exception e) {
                log.error("完成或保存异常信息报错", e);
            }
            // 执行完任务，从缓存里面清除
            //TASK_FUTURE_MAP.remove(task.getTaskId());
            ExportTaskContextHolder.remove();
        }
    }

    /*public void cancelTask(List<ExportTask> taskList) {
        log.info("当前线程池活跃线程数量已经达到上限，尝试取消之前的任务重新开始");
        if (taskList == null || taskList.isEmpty()) {
            return;
        }
        taskList.forEach(this::cancel);
    }

    public void cancel(ExportTask task) {
        Future<?> taskFuture = TASK_FUTURE_MAP.get(task.getTaskId());
        if (taskFuture != null) {
            taskFuture.cancel(true);
            task.setException("资源紧张，执行超时，暂时取消等待重新执行");
            exception(task);
            TASK_FUTURE_MAP.remove(task.getTaskId());
        }
    }*/

    public String getTraceId() {
//        Span serverSpan = Tracer.getServerTracer().getSpan();
//        if (serverSpan != null) {
//            log.info("server traceId:{}", serverSpan.getTraceId());
//            return serverSpan.getTraceId();
//        }
//        Span clientSpan = Tracer.getClientTracer().getSpan();
//        if (clientSpan != null) {
//            log.info("client traceId:{}", clientSpan.getTraceId());
//            return clientSpan.getTraceId();
//        }
        String traceId = MDC.get("X-B3-TraceId");
        log.info("X-B3-TraceId:{}", traceId);
        if (traceId == null) {
            traceId = MDC.get(TraceIdFilter.MDC_TRACE_ID);
        }
        return traceId;
    }

}
