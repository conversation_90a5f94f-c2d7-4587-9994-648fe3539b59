package com.sankuai.shangou.seashop.base.export.writter;


import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;

import java.io.ByteArrayOutputStream;

/**
 * excel数据写入代理，默认是直接把数据写入到第一个sheet，如果有特殊需求，需要单独实现WriteStrategyFetcher接口
 * <AUTHOR>
 * @date 2023/4/21
 */
public class ExcelWriteDelegator {

    private final static ExcelWriteStrategy DEFAULT_STRATEGY = new DefaultExcelWriteStrategy();

    public <P extends BasePageReq> ByteArrayOutputStream writeExcel(ExportDataGetter exportService, P param) {
        ByteArrayOutputStream out = null;
        if (exportService instanceof WriteStrategyFetcher) {
            out = ((WriteStrategyFetcher)exportService).getStrategy().writeToOutputStream(exportService, param);
        } else if (exportService instanceof ExcelWriteStrategy) {
            out = ((ExcelWriteStrategy) exportService).writeToOutputStream(exportService, param);
        } else {
            out = DEFAULT_STRATEGY.writeToOutputStream(exportService, param);
        }
        return out;
    }

}
