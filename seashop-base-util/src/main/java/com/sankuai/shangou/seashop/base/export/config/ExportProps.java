package com.sankuai.shangou.seashop.base.export.config;

import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Component
@Getter
@Setter
public class ExportProps {

    /**
     * 导出任务中业务配置对象，根据module区分
     */
    @Value("#{${seashop.export.bizConfig:}?:{}}")
    private Map<Integer, BizConfig> bizConfigMap;

    public static class BizConfig {

        /**
         * 分页导出时，每页查询数据量，有全局的默认配置 ExportTaskProps.batchCount=5000
         */
        private Integer batchCount;
        /**
         * 分页导出时，最多查询次数，有全局的默认配置 ExportTaskProps.maxBatch=100
         */
        private Integer matchBatch;
        /**
         * 基于scroll查询时的数据过期时间，单位分钟。没有默认值，具体业务自行设置默认值
         */
        private Long scrollTimeMinute;

        public Integer getBatchCount() {
            return batchCount;
        }

        public void setBatchCount(Integer batchCount) {
            this.batchCount = batchCount;
        }

        public Integer getMatchBatch() {
            return matchBatch;
        }

        public void setMatchBatch(Integer matchBatch) {
            this.matchBatch = matchBatch;
        }

        public Long getScrollTimeMinute() {
            return scrollTimeMinute;
        }

        public void setScrollTimeMinute(Long scrollTimeMinute) {
            this.scrollTimeMinute = scrollTimeMinute;
        }
    }

}
