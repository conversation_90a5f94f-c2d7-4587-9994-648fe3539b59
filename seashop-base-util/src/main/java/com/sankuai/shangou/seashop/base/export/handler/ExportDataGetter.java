package com.sankuai.shangou.seashop.base.export.handler;


/**
 * 导出数据获取器，目前仅支持返回对象是同一个的情况
 * <p>Notice: 直接实现该接口的话，实现的是基本的根据R模板固定表头，且是单Excel的sheet进行导出，
 * 如果有其他需求，请继承或者实现子类</p>
 * <p>目前组件支持：动态表头导出、动态表头+同一份数据多模板多sheet导出(模板与sheet一一对应)</p>
 * <AUTHOR>
 * @date 2023/3/31
 */
public interface ExportDataGetter {

    /**
     * 导出任务业务类型
     * <AUTHOR>
     * @date 2023/3/31
     */
    Integer getModule();

    /**
     * 生成的excel文件名称，最终的文件名称由当前方法+任务的ID组成
     * <p>实际文件名称在 module 枚举已经定义，但是接口是组件提供的，不知道业务数据，
     * 所以需要业务层提供，建议返回 moduleEnum.getDesc() </p>
     * <AUTHOR>
     * @date 2023/3/31
     */
    String getFileName();

    /**
     * 单个导出任务执行结束后，支持业务进行额外处理
     * <AUTHOR>
     * @date 2023/4/21
     */
    default PostProcessor postProcessor(){return null;};

}
