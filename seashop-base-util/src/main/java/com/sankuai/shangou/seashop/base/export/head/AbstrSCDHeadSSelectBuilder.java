package com.sankuai.shangou.seashop.base.export.head;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.utils.ReflectUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 单自定义动态表头 abstr = abstract S = single D = dynamic C=custom
 * 所谓的自定义是指，需要根据一定的代码逻辑才能确定表头的，不是固定的也不是页面用户选择的
 * 目前这个只支持表头的计算和数据的获取是分开的
 * 因为是根据代码逻辑计算的，所以无法指定所有的表头，所写excel时，表头的顺序根据生成的自定义表头确定
 * <AUTHOR>
 * @date 2023/5/11
 */
@Slf4j
public abstract class AbstrSCDHeadSSelectBuilder<P extends BasePageReq> implements DHeadSSelectBuilder {

    /**
     * 一个sheet能导出的可能有10个字段，但当前可能只需要导出5个字段，customSheetFieldList 定义每个sheet需要导出的字段
     * 是根据业务实际情况，基于请求入参指定的
     * sheetHeadFieldList 定义所有字段以及对应顺序
     * <AUTHOR>
     * @date 2023/4/24
     */
    public abstract List<String> customSheetFieldList(P param);

    @Override
    public HeadFieldHolder dynamicHead(List<String> selectedField) {
        log.info("当前需要导出的字段为: {}", JSONUtil.toJsonStr(selectedField));
        // 根据规则，响应对象是实现的第一个接口的第一个泛型
        Class responseClz = ReflectUtil.getInterfaceGenericClass(this, 0, 0);
        // 获取响应对象，即EasyExcel模板的字段属性
        Field[] fieldArr = responseClz.getDeclaredFields();
        List<String> sheetFieldList = Arrays.stream(fieldArr).map(Field::getName).collect(Collectors.toList());
        // 根据字段顺序，对选择的字段进行处理，变成顺序与字段顺序一致
        List<String> sortedFieldList = HeadConverter.singleSortSelected(selectedField, sheetFieldList);
        // 将响应对象的字段转成Map格式，方便取值判断
        Map<String, Field> fieldMap = Arrays.stream(fieldArr).collect(Collectors.toMap(Field::getName, Function.identity()));
        // Head转换，从字段属性中获取ExcelProperty定义的表头名称
        List<List<String>> headList = HeadConverter.convert(selectedField, fieldMap);
        // 如果没有选择导出的字段，则默认用业务定义的所有字段导出，因为实际不会既要导出又不选择字段
        if (CollUtil.isEmpty(selectedField)) {
            return HeadFieldHolder.builder()
                    .singleHeadList(headList)
                    .singleSortedSelect(sortedFieldList)
                    .build();
        }
        // 根据字段顺序，对选择的字段进行处理，变成顺序与字段顺序一致
        return HeadFieldHolder.builder()
                .singleHeadList(headList)
                .singleSortedSelect(sortedFieldList)
                .build();
    }

}
