package com.sankuai.shangou.seashop.base.export.writeHandler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.sankuai.shangou.seashop.base.export.anno.RowKey;
import com.sankuai.shangou.seashop.base.export.anno.ShouldMerge;
import com.sankuai.shangou.seashop.base.export.enums.MergeStyle;
import com.sankuai.shangou.seashop.base.export.util.ValueUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 默认公共的合并单元格策略，执行的实际是行遍历的时候，也就是执行时，是某一个列，然后循环行进行处理
 * <p>改策略实现的是，对于每一列，会判断相邻之间的多行，是否满足合并条件，如果满足，则会把临近的多行进行合并</p>
 * <p>因为每一种业务需要合并的列不一定，其行数据判断唯一的标准也不一致，所以每种业务都需要单独实例化该对象</p>
 * <p>需要合并的列和每行数据的唯一标识，需要自己指定，构造当前处理器时，需要指定导出类型，指定需要导出的列和导出数据大小</p>
 * @see ShouldMerge
 * @see RowKey
 * <AUTHOR>
 * @date 2023/8/10
 */
@Slf4j
public class DefaultRowMergeWriteHandler extends AbstractMergeStrategy {

    // 根据导出对象的@ShouldMerge解析出来的，需要合并单元格的列索引，以及对应的合并格式，默认是排除空数据
    private Map<Integer, MergeStyle> mergerColIndexMap;
    // 需要清空的行+列，key是行，value是列，如果不清空，会导致excel底部的计数有问题
    private List<Pair<Integer, Integer>> clearRowIndexList = new ArrayList<>(5);
    // 最大行索引，数据列大小，因为表头占了一行，索引0已被使用
    private int maxRowIndex = 0;
    // 最大列索引，当前需要导出的表头的数量-1
    private int maxColIndex = 0;
    // 数据行唯一标识需要用到的列，如果没有，则代表只需要判断当前单元格的值是否相等
    private final List<Pair<Integer, String>> rowKeyIndexList = new ArrayList<>(5);
    // 一行可能会有多个字段需要合并，每行的唯一标识只解析一次，没新开启一行，都把上一行数据清空，节约内存
    private final Map<Integer, String> rowKeyValueMap = new HashMap<>(2);
    // 当前需要处理的数据列表
    //private List<?> dataList = new ArrayList<>();
    private int opRowIndex = 0;
    private AtomicInteger ignoreRowNum = null;

    private AtomicReference<List<?>> dataSupplier = null;

    private int mergedCount;
    private Map<Integer, CellRangeAddress> mergedRowColMap = new HashMap<>(512);

    private DefaultRowMergeWriteHandler() {
    }

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * @param dataList 导出数据的
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> DefaultRowMergeWriteHandler(Class<R> excelClz, List<String> selectedFieldList, List<?> dataList) {
        this(excelClz, selectedFieldList, new AtomicReference<>(dataList), new AtomicInteger(0));
    }

    /**
     * @param excelClz 导出的对象类型
     * @param selectedFieldList 指定的要导出的列，可以为空，如果为空则导出导出对象的所有字段
     * //@param dataList 导出数据的
     * @param ignoreRowNum 忽略的行数，一种情况，比如表格前面几行输出其他东西，然后在输出列表数据，则ignoreRowNum代表前面几行的行数
     * <AUTHOR>
     * @date 2023/8/10
     */
    public <R> DefaultRowMergeWriteHandler(Class<R> excelClz, List<String> selectedFieldList, AtomicReference<List<?>> dataSupplier, AtomicInteger ignoreRowNum) {
        this.ignoreRowNum = ignoreRowNum;
        this.maxRowIndex = ignoreRowNum.get();
        this.dataSupplier = dataSupplier;
        mergerColIndexMap = new HashMap<>(16);
        Field[] fieldArr = ReflectUtil.getFields(excelClz);
        Field[] exportFieldArr = null;
        if (CollUtil.isEmpty(selectedFieldList)) {
            exportFieldArr = fieldArr;
        } else {
            exportFieldArr = Arrays.stream(fieldArr).filter(field -> selectedFieldList.contains(field.getName())).toArray(Field[]::new);
        }
        this.maxColIndex = exportFieldArr.length - 1;
        // 遍历当前最终的表头
        for (int i = 0; i < exportFieldArr.length; i++) {
            Field field = exportFieldArr[i];
            // 如果添加了@RowKey注解，说明是行唯一标识，把索引记录下来，后续判断数据是否相等时使用
            if (field.getAnnotation(RowKey.class) != null) {
                rowKeyIndexList.add(new Pair<>(i, field.getName()));
            }
            // 如果添加@ShouldMerge注解，说明需要合并
            if (field.getAnnotation(ShouldMerge.class) != null) {
                mergerColIndexMap.put(i, MergeStyle.ALL);
            }
        }
        log.info("【合并单元格-初始化合并策略】maxRowIndex={}, maxColIndex={}, rowKeyIndexList={}, mergerColIndexMap={}",
                maxRowIndex, maxColIndex, JSONUtil.toJsonStr(rowKeyIndexList), JSONUtil.toJsonStr(mergerColIndexMap));
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        StopWatch stopWatch = new StopWatch();
        // 当前行的行号索引
        int curRowIndex = cell.getRowIndex();
        // 当前列的列号索引
        int curColIndex = cell.getColumnIndex();
        log.debug("【合并单元格】curRowIndex={}, columnIndex={}, curRelativeRowIndex={}, head={}",
                curRowIndex, curColIndex, relativeRowIndex, JSONUtil.toJsonStr(head));
        // 清除上一行的行唯一标识
        stopWatch.start("清除上一行行标识");
        clearRowKeyValue(curRowIndex);
        stopWatch.stop();
        stopWatch.start("判断是否需要合并");
        boolean maybeMerge = maybeMerge(cell, curRowIndex, curColIndex, relativeRowIndex);
        stopWatch.stop();
        // 不需要合并，可能是当前行的当前单元格不需要合并，也可能是新开始了一行，与上一行不一样需要合并
        if (!maybeMerge) {
            stopWatch.start("清除之前合并过后的单元格样式");
            // 如果当前行与操作行不一样说明是新行，新开启一行，判断与上一行的RowKey是否一致，不一致就可以清除了
            // 从第二行数据行开始判断
            int ignoreRowIndex = ignoreRowNum.get() == 0 ? 0 : ignoreRowNum.get() + 1;
            if (curRowIndex > ignoreRowIndex + 1 && curRowIndex != opRowIndex && !rowKeyEquals(curRowIndex)) {
                clearMergedCell(sheet, curRowIndex);
            }
            opRowIndex = curRowIndex;
            stopWatch.stop();
            return;
        }
        opRowIndex = curRowIndex;
        // 上一行的行号索引
        int preRowIndex = curRowIndex - 1;
        stopWatch.start("处理单元格合并");
        int key = curColIndex;
        if (!mergedRowColMap.containsKey(key)) {
            CellRangeAddress cellRangeAddress = new CellRangeAddress(preRowIndex, curRowIndex, curColIndex, curColIndex);
            mergedRowColMap.put(key, cellRangeAddress);
        } else {
            CellRangeAddress oldRegion = mergedRowColMap.get(key);
            if (oldRegion.getFirstRow() <= preRowIndex && oldRegion.getLastRow() >= preRowIndex) {
                CellRangeAddress cellRangeAddress = new CellRangeAddress(oldRegion.getFirstRow(), curRowIndex, oldRegion.getFirstColumn(), curColIndex);
                mergedRowColMap.put(key, cellRangeAddress);
                if (sheet.getLastRowNum() == curRowIndex) {
                    sheet.addMergedRegionUnsafe(mergedRowColMap.get(key));
                }
            } else {
                sheet.addMergedRegionUnsafe(oldRegion);
                CellRangeAddress cellRangeAddress = new CellRangeAddress(preRowIndex, curRowIndex, curColIndex, curColIndex);
                mergedRowColMap.put(key, cellRangeAddress);
            }
        }
        clearRowIndexList.add(new Pair<>(curRowIndex, curColIndex));
        stopWatch.stop();
        log.debug("【合并单元格】结束：{}", stopWatch.prettyPrint());
        // 已经达到最大行数了，则清除
        int curMaxRowIndex = dataSupplier.get().size() + ignoreRowNum.get();
        if (curRowIndex == curMaxRowIndex && curColIndex == maxColIndex) {
            clearMergedCell(sheet, curRowIndex);
        }
        // 已经达到最大行数了，则清除
        if (curRowIndex == maxRowIndex && curColIndex == maxColIndex) {
            clearMergedCell(sheet, curRowIndex);
        }
    }


    /**
     * 当前单元格的数据是否满足合并要求
     * 认为一定有表头
     * <AUTHOR>
     * @date 2023/8/10
     */
    private boolean maybeMerge(Cell cell, int curRowIndex, int curColIndex, int relativeRowIndex) {
        // 如果行索引是1，因为认为一定有表头，所以不需要合并
        // ignoreRowNum 是数量，索引从1开始，所以要额外加1
        if (ignoreRowNum.get() == 0 && curRowIndex == 1) {
            return false;
        } else if (ignoreRowNum.get() > 0 && curRowIndex < ignoreRowNum.get() + 1 + 1) {
            return false;
        }
        // 当前单元格的数据
        String curData = ValueUtil.getCellValueString(cell);
        // 当前单元格所在列+上一行对应的数据，即需要比对是否相等的两个单元格
        Cell preCell = cell.getSheet().getRow(curRowIndex - 1).getCell(curColIndex);
        String preData = ValueUtil.getCellValueString(preCell);
        log.debug("【合并单元格】curRowIndex={}, preRowIndex={}, columnIndex={}, preRelativeRowIndex={}, curData={}, preData={}",
                curRowIndex, curRowIndex - 1, curColIndex, relativeRowIndex, curData, preData);
        // 如果不在合并列的索引中，或者当前单元格数据为空，且合并格式是忽略空数据，则不合并
        if (mergerColIndexMap == null || !mergerColIndexMap.containsKey(curColIndex)) {
            log.debug("【合并单元格】curColIndex={} 不需要合并", curColIndex);
            return false;
        }
        if (StrUtil.isBlank(curData) && MergeStyle.IGNORE_NULL.equals(mergerColIndexMap.get(curColIndex))) {
            log.debug("【合并单元格】curRowIndex={}, curColIndex={} 数据为空, 不需要合并", curRowIndex, curColIndex);
            return false;
        }
        // 将当前单元格数据与上一个单元格数据比较
        return dataEquals(cell, curRowIndex, curData, preData);
    }

    /**
     * 全部单元格处理完后，统一清除需要清空的数据，如果每次循环都清除，会导致最多只能合并两行，因为前面数据清空了
     * <AUTHOR>
     * @date 2023/8/10
     */
    private void clearMergedCell(Sheet sheet, int curRowIndex) {
        if (CollUtil.isEmpty(clearRowIndexList)) {
            return;
        }
        log.debug("【合并单元格】清除数据, curRowIndex={}, clearRowIndexList={}", curRowIndex, clearRowIndexList);
        clearRowIndexList.forEach(rowIndex -> {
            Row row = sheet.getRow(rowIndex.getKey());
            if (row == null) {
                log.info("row=null, rowIndex={}", rowIndex);
            }
            Cell clearCell = sheet.getRow(rowIndex.getKey()).getCell(rowIndex.getValue());
            clearCell.setBlank();
        });
        clearRowIndexList = null;
        clearRowIndexList = new ArrayList<>(5);
    }

    /**
     * 认为数据一定会有表头，所以如果上一行的索引是0，则直接返回false
     * <AUTHOR>
     * @date 2023/8/14
     */
    private boolean dataEquals(Cell cell, int curRowIndex, String curData, String preData) {
        // 基于规则获取的当前行的唯一标识数据
        String curRowKeyValue = getRowKeyValue(curRowIndex);
        String preRowKeyValue = getRowKeyValue(curRowIndex - 1);
        // 唯一标识+当前单元格的数据
        String finalCurData = curRowKeyValue + curData;
        String finalPreData = preRowKeyValue + preData;
        log.debug("【合并单元格】curRowIndex={}, curRowKeyValue={}, preRowKeyValue={}, finalCurData={}, finalPreData={}",
                curRowIndex, curRowKeyValue, preRowKeyValue, finalCurData, finalPreData);
        // 返回是否相等
        return finalCurData.equals(finalPreData);
    }

    private String getRowKeyValue(int curRowIndex) {
        if (CollUtil.isEmpty(rowKeyIndexList)) {
            return "";
        }
        if (rowKeyValueMap.containsKey(curRowIndex)) {
            return rowKeyValueMap.get(curRowIndex);
        }
        StringBuilder rowKey = new StringBuilder();
        // 获取行关键字数据时，由于当前类是写excel单元格时触发的，所以对于在当前单元格之后写入数据的单元格是获取不到的，只能通过反射从对象获取
        for (Pair<Integer, String> rowIndex : rowKeyIndexList) {
            // 减一是因为数据和行号是从0开始的，但行号有表头，所以会比数据索引多一
            Object data = dataSupplier.get().get(curRowIndex - ignoreRowNum.get() - 1);
            String rowKeyCellValue = String.valueOf(ReflectUtil.getFieldValue(data, rowIndex.getValue()));
            rowKey.append(rowKeyCellValue).append("^_^");
        }
        // 每一行的唯一数据进行缓存
        rowKeyValueMap.put(curRowIndex, rowKey.toString());
        return rowKey.toString();
    }

    private boolean rowKeyEquals(int curRowIndex) {
        // 基于规则获取的当前行的唯一标识数据
        String curRowKeyValue = getRowKeyValue(curRowIndex);
        String preRowKeyValue = getRowKeyValue(curRowIndex - 1);
        return curRowKeyValue.equals(preRowKeyValue);
    }

    private void clearRowKeyValue(int curRowIndex) {
        if (rowKeyValueMap.isEmpty()) {
            return;
        }
        rowKeyValueMap.remove(curRowIndex - 1);
    }

}
