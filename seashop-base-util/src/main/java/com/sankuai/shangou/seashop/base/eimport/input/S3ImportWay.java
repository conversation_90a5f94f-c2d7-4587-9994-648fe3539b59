package com.sankuai.shangou.seashop.base.eimport.input;

import java.io.File;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/21 21:48
 */
@Slf4j
public class S3ImportWay<D extends RowReadResult> extends RemoteImportWay<D> {

    @Resource
    private S3plusStorageService s3plusStorageService;


    @Override
    public File getFile(String fileUrl) {
        try {
            File file = new File(IdUtil.fastSimpleUUID() + "." + FileUtil.getSuffix(fileUrl));
            return HttpUtil.downloadFileFromUrl(fileUrl, file, 20000);
        } catch (Exception e) {
            log.error("read s3 file fail, file: {}, error: {}", fileUrl, e);
            throw new BusinessException("解析文件失败");
        }
    }
}
