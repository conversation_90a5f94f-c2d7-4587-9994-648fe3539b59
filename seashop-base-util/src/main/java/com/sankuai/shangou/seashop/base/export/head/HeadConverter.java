package com.sankuai.shangou.seashop.base.export.head;

import com.alibaba.excel.annotation.ExcelProperty;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Excel头转换函数
 * 从对象的字段Map中过滤掉没有ExcelProperty注解的，并从ExcelProperty获取设置的表头名称
 * <AUTHOR>
 * @date 2023/4/21
 */
public class HeadConverter {

    public static Function<List<String>, List<List<String>>> convertFunction(List<String> currentSelected, Map<String, Field> fieldMap) {
        return fieldList -> convert(fieldList, currentSelected, fieldMap);
    }

    public static List<List<String>> convert(List<String> currentSelected, Map<String, Field> fieldMap) {
        return convert(currentSelected, currentSelected, fieldMap);
    }

    public static List<List<String>> convert(List<String> fieldList, List<String> currentSelected, Map<String, Field> fieldMap) {
        final Map<String, String> selectMap = new HashMap<>();
        if (currentSelected != null && currentSelected.size() > 0) {
            for (String str : currentSelected) {
                selectMap.put(str, str);
            }
        } else {
            selectMap.put("-1", "-1");
        }
        return fieldList.stream()
                .filter(x -> {
                    // 如果没有选择导出字段，则默认导出全部字段
                    if (selectMap == null || selectMap.containsKey("-1")) {
                        return true;
                    }
                    return selectMap.containsKey(x);
                })
                .filter(x -> {
                    Field field = fieldMap.get(x);
                    ExcelProperty excelProperty = field.getDeclaredAnnotation(ExcelProperty.class);
                    return excelProperty != null;
                })
                .map(x -> {
                    Field field = fieldMap.get(x);
                    ExcelProperty excelProperty = field.getDeclaredAnnotation(ExcelProperty.class);
                    return Arrays.asList(excelProperty.value()[0]);
                }).collect(Collectors.toList());
    }

    public static List<List<String>> sortSelected(List<String> selected, List<List<String>> sheetFieldList) {
        if (selected == null || selected.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        return sheetFieldList.stream()
                .map(x -> singleSortSelected(selected, x))
                .collect(Collectors.toList());
    }

    public static List<String> singleSortSelected(List<String> selected, List<String> sheetFieldList) {
        if (selected == null || selected.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        Map<String, String> map = selected.stream().collect(Collectors.toMap(k -> k, v -> v));
        return sheetFieldList.stream()
                .filter(x -> map.get(x) != null)
                .collect(Collectors.toList());
    }
}
