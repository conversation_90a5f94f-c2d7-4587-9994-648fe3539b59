package com.sankuai.shangou.seashop.base.eimport;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Component
@Slf4j
public class ImportHandlerContainer implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private Map<BizType, ImportHandler> importHandlerMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, ImportHandler> beanMap = applicationContext.getBeansOfType(ImportHandler.class);
        beanMap.forEach((k, v) -> {
            importHandlerMap.put(v.bizType(), v);
        });
        log.info("ImportHandlerContainer init success");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public ImportHandler getImportHandler(BizType bizType) {
        return importHandlerMap.get(bizType);
    }
}
