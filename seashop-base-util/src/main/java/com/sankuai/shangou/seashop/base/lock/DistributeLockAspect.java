package com.sankuai.shangou.seashop.base.lock;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StopWatch;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: wbhz_chenpeng1
 * @CreateTime: 2023-11-09  10:08
 * @Description: 分布式锁切面
 * @Version: 1.0
 */
@Aspect
@Component
@Order
@Slf4j
public class DistributeLockAspect {
    @Autowired(required = false)
    private DistributedLockService lockService;
//    @ConfigValue(key = "lock.wait.time.config", defaultValue = "{}")
@Value("#{${lock.wait.time.config:}?:{}}")
    private Map<String, Long> waitTimeMap;
    /**
     * 对象反射缓存
     * 类class-><参数名称，field>
     */
    private static final Map<Class<?>, Map<String, Optional<Field>>> CACHE_FIELD_MAP = Maps.newConcurrentMap();

    @Around(value = "@annotation(com.sankuai.shangou.seashop.base.lock.DistributeLock)")
    public Object aroundInvoke(ProceedingJoinPoint point) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) point
                .getSignature();
        Method method = methodSignature.getMethod();
        DistributeLock distributeLock = AnnotationUtils.findAnnotation(method, DistributeLock.class);
        if (distributeLock == null) {
            return point.proceed();
        }
        String lockKey = resolveLockKey(point.getArgs(), distributeLock);
        LockKey lock = LockKey.builder().catTag(distributeLock.scenes()).lockName(lockKey).build();

        TaskCallable<Object> callable = buildCallable(point);

        boolean waitLock = distributeLock.waitLock();
        if (waitLock) {
            //等待锁,默认等待1秒
//            Long waitTime = waitTimeMap.getOrDefault("lock.wait.time." + distributeLock.scenes(), 2000L);
            Long waitTime = 2000L;
            return lockService.tryLock(lock, callable, waitTime, TimeUnit.MILLISECONDS);
        }
        return lockService.tryLock(lock, callable);
    }

    private TaskCallable<Object> buildCallable(ProceedingJoinPoint point) {
        return () -> {
            String className = point.getTarget().getClass().getName();
            String methodName = point.getSignature().getName();
            String timeCostKey = Joiner.on(".").skipNulls().join(className, methodName);
            StopWatch stopWatch = new StopWatch(timeCostKey);
            stopWatch.start();
            try {
                return point.proceed();
            } finally {
                stopWatch.stop();
                //打印业务处理耗时
//                MetricHelper.build().name("execute.cost.time.in.lock").tag("method", timeCostKey)
//                        .duration(stopWatch.getTotalTimeMillis());
            }
        };
    }

    private String resolveLockKey(Object[] args, DistributeLock distributeLock) {
        String keyPattern = distributeLock.keyPattern();
        if (ArrayUtils.isEmpty(args)) {
            //没有参数
            return keyPattern;
        }
        //解析参数
        String[] keyValues = distributeLock.keyValues();
        if (ArrayUtils.isEmpty(keyValues)) {
            //没有设置填充值
            return keyPattern;
        }
        String[] result = new String[keyValues.length];
        for (int i = 0; i < keyValues.length; i++) {
            String paramDesc = keyValues[i];
            String[] subParams = paramDesc.split("\\.");
            AssertUtil.throwIfTrue(ArrayUtils.isEmpty(subParams), "lock value param desc list is null");
            String paramNumStr = subParams[0];
            String paramNum = paramNumStr.substring(1, paramNumStr.length() - 1);
            int paramSeq = Integer.parseInt(paramNum);
            AssertUtil.throwIfTrue(paramSeq >= args.length, "lock key param seq is over method args!");
            Object param = args[paramSeq];
            if (subParams.length == 1) {
                //只有一个
                result[i] = param.toString();
                continue;
            }
            Object obj = param;
            for (int j = 1; j < subParams.length; j++) {
                String name = subParams[j];
                obj = resolveAttr(obj, name);
                AssertUtil.throwIfNull(obj, "lock param value is null," + name);
            }
            result[i] = obj.toString();
        }
        return MessageFormat.format(keyPattern, result);
    }

    private Object resolveAttr(Object obj, String paramName) {
        Class<?> clz = obj.getClass();
        Map<String, Optional<Field>> methodMap = CACHE_FIELD_MAP.computeIfAbsent(clz, f -> Maps.newConcurrentMap());
        Optional<Field> paramFieldOp = methodMap.computeIfAbsent(paramName, name -> {
            Field f = ReflectionUtils.findField(clz, name);
            if (f == null) {
                return Optional.empty();
            }
            f.setAccessible(true);
            return Optional.of(f);
        });
        AssertUtil.throwIfTrue(!paramFieldOp.isPresent(), "param name not fund," + paramName);
        Field paramField = paramFieldOp.get();
        try {
            return paramField.get(obj);
        } catch (IllegalAccessException e) {
            log.error("get value by param name {}", paramName, e);
            throw new IllegalArgumentException(e);
        }
    }


}