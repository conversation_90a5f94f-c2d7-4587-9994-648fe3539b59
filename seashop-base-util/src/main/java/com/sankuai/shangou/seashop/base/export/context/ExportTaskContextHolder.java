package com.sankuai.shangou.seashop.base.export.context;

import com.sankuai.shangou.seashop.base.export.model.TaskContext;
import org.springframework.core.NamedInheritableThreadLocal;

/**
 * <AUTHOR>
 * @date 2023/9/5
 */
public class ExportTaskContextHolder {

    private static final ThreadLocal<TaskContext> TASK_CONTEXT = new NamedInheritableThreadLocal<>("export-task-context");

    public static void remove() {
        TASK_CONTEXT.remove();
    }

    public static void addContext(String taskId, long begin) {
        TaskContext context = TASK_CONTEXT.get();
        if (context == null) {
            context = new TaskContext();
            context.setTaskId(taskId);
            context.setBegin(begin);
            TASK_CONTEXT.set(context);
        }
    }

    public static void updateTotalCount(int count) {
        TaskContext context = TASK_CONTEXT.get();
        if (context == null) {
            return;
        }
        context.setTotal(context.getTotal() + count);
    }

    public static TaskContext get() {
        return TASK_CONTEXT.get();
    }

}
