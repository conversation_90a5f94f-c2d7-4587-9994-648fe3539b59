package com.sankuai.shangou.seashop.base.export.model;


import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

/**
 * 定制化的表格数据包装器，目前针对的是 配送-待配货订单明细 导出，扩展是否需要合并单元格
 * 由组件根据业务情况处理了单元格的合并
 * <AUTHOR>
 * @date 2023/5/16
 */
public interface CustomTableWrapper<T, P extends BasePageReq> extends TableWrapper<T, P> {

    /**
     * 当前数据是否需要合并单元格，使用的是默认的合并策略 ColumnRowMergeWriteHandler
     * <AUTHOR>
     * @date 2023/5/16
     */
    Boolean shouldMerge();

    /**
     * 获取当前数据块的最后一列的索引，用于合并单元格
     * @return 最后一列的索引
     */
    int getLastColumnIndex();

}
