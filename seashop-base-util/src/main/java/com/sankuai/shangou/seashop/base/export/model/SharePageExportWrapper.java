package com.sankuai.shangou.seashop.base.export.model;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.extern.slf4j.Slf4j;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Slf4j
public abstract class SharePageExportWrapper<T, P extends BasePageReq> extends PageExportWrapper<T, P> {

    private List<T> dataList;

    @Override
    public List<T> getDataList(P param) {
        dataList = super.getDataList(param);
        return dataList;
    }

    public List<T> getDataList() {
        return dataList == null ? Collections.emptyList() : dataList;
    }
}
