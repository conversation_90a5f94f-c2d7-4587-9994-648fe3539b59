package com.sankuai.shangou.seashop.base.lock;

import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import com.sankuai.shangou.seashop.base.boot.exception.LockFailException;
import com.sankuai.shangou.seashop.base.lock.model.DistributeLockEngineEnum;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: wbhz_chenpeng1
 * @CreateTime: 2023-11-09  10:08
 * @Description: 分布式锁-squirrel
 * @Version: 1.0
 */
@Slf4j
public class SquirrelDistributedLockServiceImpl implements DistributedLockService {

    private final RedissonClient redissonClient;

    public SquirrelDistributedLockServiceImpl(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Override

    public void lock(LockKey lockKey, TaskRunnable runnable) {
        String lockName = lockKey.getLockName();
        //重入锁
        RLock lock = redissonClient.getLock(lockName);
        String threadName = Thread.currentThread().getName();
        log.info("thread:{},lockName:{},block acquire lock begin", threadName, lockName);
        //阻塞等待
        lock.lock();
        try {
            // 处理任务
            runnable.run();
        }
        finally {
            // 释放锁
            lock.unlock();
            log.info("thread:{},lockName:{},unlocked", threadName, lockName);
        }
    }

    @Override

    public <R> R lock(LockKey lockKey, TaskCallable<R> callable) throws Throwable {
        String lockName = lockKey.getLockName();
        //重入锁
        RLock lock = redissonClient.getLock(lockName);
        String threadName = Thread.currentThread().getName();
        log.info("thread:{},lockName:{},block acquire lock begin", threadName, lockName);
        //阻塞等待
        lock.lock();
        try {
            // 处理任务
            return callable.call();
        }
        finally {
            // 释放锁
            lock.unlock();
            log.info("thread:{},lockName:{},unlocked", threadName, lockName);
        }
    }

    @Override

    public void tryLock(LockKey lockKey, TaskRunnable runnable) {
        String lockName = lockKey.getLockName();
        //重入锁
        RLock lock = redissonClient.getLock(lockName);
        String threadName = Thread.currentThread().getName();
        log.info("thread:{},lockName:{},,non block try acquire lock begin", threadName, lockName);
        boolean acquire = lock.tryLock();
        //失败打点
        validateAcquireSuccess(acquire, lockKey);
        try {
            // 处理任务
            runnable.run();
        }
        finally {
            // 释放锁
            lock.unlock();
            log.info("thread:{},lockName:{},unlocked", threadName, lockName);
        }
    }

    @Override

    public <R> R tryLock(LockKey lockKey, TaskCallable<R> callable) throws Throwable {
        String lockName = lockKey.getLockName();
        //重入锁
        RLock lock = redissonClient.getLock(lockName);
        String threadName = Thread.currentThread().getName();
        log.info("thread:{},lockName:{},non block try acquire lock begin", threadName, lockName);
        boolean acquire = lock.tryLock();
        //失败打点
        validateAcquireSuccess(acquire, lockKey);
        try {
            // 处理任务
            return callable.call();
        }
        finally {
            // 释放锁
            lock.unlock();
            log.info("thread:{},lockName:{},unlocked", threadName, lockName);
        }
    }

    @Override
    public void tryLock(LockKey lockKey, TaskRunnable runnable, Long time, TimeUnit timeUnit) {
        String lockName = lockKey.getLockName();
        //重入锁
        RLock lock = redissonClient.getLock(lockName);
        String threadName = Thread.currentThread().getName();
        boolean acquire;
        try {
            log.info("thread:{},lockName:{},non block with time,try acquire lock begin", threadName, lockName);
            acquire = lock.tryLock(time, timeUnit);
        }
        catch (InterruptedException e) {
            log.error("thread interrupt error", e);
            Thread.currentThread().interrupt();
            throw new LockFailException("when acquire lock,but thread interrupt!");
        }
        //失败打点
        validateAcquireSuccess(acquire, lockKey);
        try {
            runnable.run();
        }
        finally {
            lock.unlock();
            log.info("thread:{},lockName:{},unlocked", threadName, lockName);
        }
    }

    @Override
    public <R> R tryLock(LockKey lockKey, TaskCallable<R> callable, Long time, TimeUnit timeUnit) throws Throwable {
        String lockName = lockKey.getLockName();
        //重入锁
        RLock lock = redissonClient.getLock(lockName);
        String threadName = Thread.currentThread().getName();
        log.info("thread:{},lockName:{},non block with time,try acquire lock begin", threadName, lockName);
        boolean acquire;
        try {
            acquire = lock.tryLock(time, timeUnit);
        }
        catch (InterruptedException e) {
            log.error("thread interrupt error", e);
            Thread.currentThread().interrupt();
            throw new LockFailException("when acquire lock,but thread interrupt!");
        }
        //失败打点
        validateAcquireSuccess(acquire, lockKey);
        try {
            return callable.call();
        }
        finally {
            lock.unlock();
            log.info("thread:{},lockName:{},unlocked", threadName, lockName);
        }
    }

    @Override
    public DistributeLockEngineEnum engineType() {
        return DistributeLockEngineEnum.SQUIRREL;
    }

    @Override
    public void tryMultiReentrantLock(List<LockKey> lockKeys, TaskRunnable runnable) {
        if (CollectionUtils.isEmpty(lockKeys)) {
            return;
        }
        RLock lock =
                redissonClient.getMultiLock(lockKeys.stream().map(lockKey -> redissonClient.getLock(lockKey.getLockName())).toArray(RLock[]::new));

        boolean acquire = lock.tryLock();
        if (!acquire) {
            throw new LockFailException("try acquire lock failed");
        }

        try {
            runnable.run();
        }
        finally {
            lock.unlock();
        }
    }

    @Override
    public <R> R tryMultiReentrantLock(List<LockKey> lockKeys, TaskCallable<R> callable) throws Throwable {
        if (CollectionUtils.isEmpty(lockKeys)) {
            return null;
        }

        RLock lock =
                redissonClient.getMultiLock(lockKeys.stream().map(lockKey -> redissonClient.getLock(lockKey.getLockName())).toArray(RLock[]::new));

        boolean acquire = lock.tryLock();
        if (!acquire) {
            throw new LockFailException("try acquire lock failed");
        }

        try {
            return callable.call();
        }
        finally {
            lock.unlock();
        }
    }


    @Override
    public <R> R tryMultiLock(List<String> lockKeys, TaskCallable<R> callable) throws Throwable {
        if (CollectionUtils.isEmpty(lockKeys)) {
            return null;
        }

        RLock lock =
                redissonClient.getMultiLock(lockKeys.stream().map(redissonClient::getLock).toArray(RLock[]::new));

        boolean acquire = lock.tryLock();
        if (!acquire) {
            throw new LockFailException("try acquire lock failed");
        }

        try {
            return callable.call();
        }
        finally {
            lock.unlock();
        }
    }

    private void validateAcquireSuccess(boolean acquire, LockKey lockKey) {
        log.info("lock key:{},try acquire lock result:{}", lockKey.getLockName(), acquire);
        if (acquire) {
            return;
        }
        //打点
        throw new LockFailException("try acquire lock failed," + lockKey.getLockName());
    }

}