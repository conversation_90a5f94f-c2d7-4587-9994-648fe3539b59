package com.sankuai.shangou.seashop.base.export.handler;


import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
public interface SingleDataGetter<R, P extends BasePageReq> extends SingleSheetDataGetter {

    /**
     * 通过参数P查询获取数据
     * <p>此处支持分页获取数据，防止数据太多占用数据库资源太长时间</p>
     * <p>Notice：组件会设置默认的分页参数获取数据，目前最多支持获取 500000 数据，超出这个范围的将忽略</p>
     * <p>对于数据量不大的，不需要分页的，可以业务层直接提供获取全量list的数据，或者重置分页参数，将分页参数的 pageSize设置大一点，或者设置 pageSize=-1，此时会获取全量数据</p>
     * <AUTHOR>
     * @date 2023/3/31
     */
    List<R> selectData(P p);

}
