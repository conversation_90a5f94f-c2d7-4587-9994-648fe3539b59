package com.sankuai.shangou.seashop.base.export.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 导出任务配置，部分有默认值
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "seashop.export")
public class ExportTaskProps {

    /**
     * 批次数量
     */
    private Integer batchCount;
    /**
     * 最多数据获取批次
     */
    private Integer maxBatch;
    /**
     * 导出文件存储路径，比如：export/excel
     */
    private String nfsPathPrefix;

    /**
     * 导出任务线程池配置
     */
    private Integer execPoolCoreSize;
    /**
     * 导出任务线程池配置
     */
    private Integer execPoolMaxSize;
    /**
     * 导出任务线程池配置
     */
    private Integer execPoolQueueSize;
    /**
     * 导出任务线程池配置
     */
    private Integer execPoolKeepAliveSeconds;
    /**
     * 导出任务线程池配置
     */
    private String execPoolThreadName;

    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes;
    /**
     * 任务执行超时时间，单位毫秒，超过该时间的任务将被重试
     */
    private Long processTimeoutMillis;
    /**
     * 待执行任务超时时间，单位毫秒，超过该时间的任务将被定时任务补偿执行
     */
    private Long readyTimeoutMillis;

    public Integer getBatchCount() {
        if (batchCount == null) {
            return 5000;
        }
        return batchCount;
    }

    public void setBatchCount(Integer batchCount) {
        this.batchCount = batchCount;
    }

    public Integer getMaxBatch() {
        if (maxBatch == null) {
            return 100;
        }
        return maxBatch;
    }

    public void setMaxBatch(Integer maxBatch) {
        this.maxBatch = maxBatch;
    }

    public String getNfsPathPrefix() {
        return nfsPathPrefix;
    }

    public void setNfsPathPrefix(String nfsPathPrefix) {
        this.nfsPathPrefix = nfsPathPrefix;
    }

    public Integer getExecPoolCoreSize() {
        if (execPoolCoreSize == null) {
            return 5;
        }
        return execPoolCoreSize;
    }

    public void setExecPoolCoreSize(Integer execPoolCoreSize) {
        this.execPoolCoreSize = execPoolCoreSize;
    }

    public Integer getExecPoolMaxSize() {
        if (execPoolMaxSize == null) {
            return 10;
        }
        return execPoolMaxSize;
    }

    public void setExecPoolMaxSize(Integer execPoolMaxSize) {
        this.execPoolMaxSize = execPoolMaxSize;
    }

    public Integer getExecPoolQueueSize() {
        if (execPoolQueueSize == null) {
            return 200;
        }
        return execPoolQueueSize;
    }

    public void setExecPoolQueueSize(Integer execPoolQueueSize) {
        this.execPoolQueueSize = execPoolQueueSize;
    }

    public Integer getExecPoolKeepAliveSeconds() {
        if (execPoolKeepAliveSeconds == null) {
            return 30;
        }
        return execPoolKeepAliveSeconds;
    }

    public void setExecPoolKeepAliveSeconds(Integer execPoolKeepAliveSeconds) {
        this.execPoolKeepAliveSeconds = execPoolKeepAliveSeconds;
    }

    public String getExecPoolThreadName() {
        if (execPoolThreadName == null || execPoolThreadName.isEmpty()) {
            return "export-task-";
        }
        return execPoolThreadName;
    }

    public void setExecPoolThreadName(String execPoolThreadName) {
        this.execPoolThreadName = execPoolThreadName;
    }

    public Integer getMaxRetryTimes() {
        if (maxRetryTimes == null) {
            return 3;
        }
        return maxRetryTimes;
    }

    public void setMaxRetryTimes(Integer maxRetryTimes) {
        this.maxRetryTimes = maxRetryTimes;
    }

    public Long getProcessTimeoutMillis() {
        if (processTimeoutMillis == null) {
            // 默认30分钟
            return 1_800_000L;
        }
        return processTimeoutMillis;
    }

    public void setProcessTimeoutMillis(Long processTimeoutMillis) {
        this.processTimeoutMillis = processTimeoutMillis;
    }

    public Long getReadyTimeoutMillis() {
        if (readyTimeoutMillis == null) {
            // 默认1分钟
            return 60_000L;
        }
        return readyTimeoutMillis;
    }

    public void setReadyTimeoutMillis(Long readyTimeoutMillis) {
        this.readyTimeoutMillis = readyTimeoutMillis;
    }
}
