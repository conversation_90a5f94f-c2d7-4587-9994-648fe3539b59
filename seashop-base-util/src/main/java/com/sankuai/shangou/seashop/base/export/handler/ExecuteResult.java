package com.sankuai.shangou.seashop.base.export.handler;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/17
 */
@Data
public class ExecuteResult {

    private boolean success;
    private String filePath;

    public static ExecuteResult error() {
        ExecuteResult result = new ExecuteResult();
        result.setSuccess(false);
        return result;
    }

    public static ExecuteResult empty() {
        ExecuteResult result = new ExecuteResult();
        result.setSuccess(true);
        return result;
    }

    public static ExecuteResult success(String filePath) {
        ExecuteResult result = new ExecuteResult();
        result.setSuccess(true);
        result.setFilePath(filePath);
        return result;
    }

}
