package com.sankuai.shangou.seashop.base.export.handler;

import com.sankuai.shangou.seashop.base.export.writter.ExcelWriteStrategy;
import com.sankuai.shangou.seashop.base.export.writter.ListDataExcelWriteStrategy;
import com.sankuai.shangou.seashop.base.export.writter.WriteStrategyFetcher;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AbstractListDataGetter<E>
        implements SingleSheetDataGetter, WriteStrategyFetcher {

    private final List<E> dataList;
    private final String fileName;
    private final Class<E> dataClz;

    public AbstractListDataGetter(String fileName, List<E> dataList, Class<E> dataClz) {
        this.fileName = fileName;
        this.dataList = dataList;
        this.dataClz = dataClz;
    }

    @Override
    public Integer getModule() {
        return null;
    }

    @Override
    public String sheetName() {
        return "";
    }

    public List<E> getDataList() {
        return dataList;
    }

    @Override
    public String getFileName() {
        return fileName;
    }

    public Class<E> getDataClz() {
        return dataClz;
    }

    @Override
    public ExcelWriteStrategy getStrategy() {
        return new ListDataExcelWriteStrategy<>();
    }
}
