package com.sankuai.shangou.seashop.base.export.writeHandler;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

/**
 * 定制开发的单元格样式，表头 大小10，居中，白色背景，内容 大小10，水平居中，自动换行
 * <AUTHOR>
 * @date 2023/5/16
 */
public class SpecialCellWriteHandler extends HorizontalCellStyleStrategy {

    public SpecialCellWriteHandler() {
        super(headStyle(), contentStyle());
    }

    public static WriteCellStyle headStyle() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE1.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short)10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        //设置左边框
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        //设置右边框
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        //设置上边框
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        //设置下边框
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        return headWriteCellStyle;
    }

    public static WriteCellStyle contentStyle() {
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        //contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short)10);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置自动换行
        contentWriteCellStyle.setWrapped(true);
        //设置左边框
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        //设置右边框
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        //设置上边框
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        //设置下边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        return contentWriteCellStyle;
    }

    private final static SpecialCellWriteHandler DEFAULT_CELL = new SpecialCellWriteHandler();

    public static SpecialCellWriteHandler getInstance() {
        return DEFAULT_CELL;
    }

}
