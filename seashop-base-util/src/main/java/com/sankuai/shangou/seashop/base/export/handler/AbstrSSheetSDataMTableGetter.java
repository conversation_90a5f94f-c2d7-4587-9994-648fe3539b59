package com.sankuai.shangou.seashop.base.export.handler;


import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.writter.ExcelWriteStrategy;
import com.sankuai.shangou.seashop.base.export.writter.SSheetSimpTplExcelWriteStgy;
import com.sankuai.shangou.seashop.base.export.writter.WriteStrategyFetcher;

/**
 * <AUTHOR>
 * @date 2023/5/6
 */
public abstract class AbstrSSheetSDataMTableGetter<R, P extends BasePageReq>
        implements TableDataGetter<P>, WriteStrategyFetcher {

    @Override
    public ExcelWriteStrategy getStrategy() {
        return new SSheetSimpTplExcelWriteStgy();
    }

}
