package com.sankuai.shangou.seashop.base.utils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2023/4/13
 */
public class ReflectUtil {

    /**
     * 获取当前对象实现接口的泛型类类型
     * @param interfaceObj 实现了接口的对象
     * @param interfaceIndex 当前对象实现的接口的索引，类可以实现多个接口，索引从左至右从0开始
     * @param genericClassIndex 需要获取的泛型类索引，泛型也可以有多个，索引从左至右从0开始
     * <AUTHOR>
     * @date 2023/4/13
     */
    public static Class getInterfaceGenericClass(Object interfaceObj, int interfaceIndex, int genericClassIndex) {
        // 拿到当前实现的接口
        Type[] interfaceTypeArr = interfaceObj.getClass().getGenericInterfaces();
        Type interfaceType = interfaceTypeArr[interfaceIndex];
        ParameterizedType parameterizedType = (ParameterizedType) interfaceType;
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        return (Class) actualTypeArguments[genericClassIndex];
    }

}
