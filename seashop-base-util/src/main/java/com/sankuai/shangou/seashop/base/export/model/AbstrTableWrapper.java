package com.sankuai.shangou.seashop.base.export.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * table的列表数据，当前不支持分页
 * <p>
 *     AbstrTableWrapper 是在构造导出上下文，也即每一个table的时候初始化的，TableContext 是一开始就初始化的，但TableContext初始化的时候，accumulateRowNum是0，
 *     table类型的导出逻辑是，首先构造导出上下文，然后获取数据，然后写入数据，循环上述过程。
 *     在获取数据的时候，会更新 accumulateRowNum 的值，所以下一次获取数据的时候，accumulateRowNum 已经是前面已经写入的行数了
 * </p>
 * <AUTHOR>
 * @date 2023/8/29
 */
@Slf4j
public abstract class AbstrTableWrapper<T, P extends BasePageReq> implements TableWrapper<T, P> {

    // 可以延迟获取的 当前数据列表 前面需要忽略的行数，比如前面已经写了一个table了，
    // 用于在数据列表要做一些合并单元格的判断时，要计算索引从数据列表获取值
    private final AtomicInteger prevNum = new AtomicInteger(0);
    private final AtomicReference<List<?>> dataSupplier = new AtomicReference<>(Collections.emptyList());
    private final TableContext tableContext;

    public AbstrTableWrapper(TableContext tableContext) {
        this.tableContext = tableContext;
    }

    @Override
    public List<T> getDataList(P param) {
        log.info("【数据导出】分页导出, prevNum={}, 查询参数: {}", prevNum.get(), JSONUtil.toJsonStr(param));
        List<T> dataList = getCustomDataList(param);
        int size = 0;
        if (CollUtil.isNotEmpty(dataList)) {
            size = dataList.size();
        }
        prevNum.set(tableContext.getAccumulateRowNum());
        log.info("【数据导出】分页导出, 处理前置忽略行后, prevNum={}, 数据大小={}", prevNum.get(), size);
        dataSupplier.getAndSet(dataList);
        // 这个wrapper有表头，多加一行
        tableContext.accumulateRowNum(size + 1);
        return dataList;
    }

    public abstract List<T> getCustomDataList(P param);

    protected AtomicInteger getPrevNum() {
        return prevNum;
    }

    protected AtomicReference<List<?>> getDataSupplier() {
        return dataSupplier;
    }
}
