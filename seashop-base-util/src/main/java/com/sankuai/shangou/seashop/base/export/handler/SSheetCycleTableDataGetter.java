package com.sankuai.shangou.seashop.base.export.handler;

import com.sankuai.shangou.seashop.base.export.model.CycleTableContext;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
public interface SSheetCycleTableDataGetter<R, P extends BasePageReq> extends SingleSheetDataGetter {

    /**
     * 根据查询参数获取需要导出的数据，对象包含sheet名称和数据块列表
     * <AUTHOR>
     * @date 2023/5/16
     */
    CycleTableContext selectData(P p);

}
