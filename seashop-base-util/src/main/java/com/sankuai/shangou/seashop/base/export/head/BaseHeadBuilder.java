package com.sankuai.shangou.seashop.base.export.head;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/19
 */
public interface BaseHeadBuilder {

    /**
     * 构造解析包含Map数据的表头，对象中的普通属性不变，map属性扩展为多个属性，动态添加到对象
     * @param currentDataClz 当前需要导出的数据类
     * @param dataObj 之所以需要对象，是业务上可能对象里面通过map传入需要导出的字段名，此时需要从对象获取map的数据
     * @param selectedFieldList 用户选择的需要导出的字段，包括根据业务逻辑生成的字段
     * <AUTHOR>
     * @date 2023/5/19
     */
    HeadFieldHolder buildHead(Object dataObj, Class<?> currentDataClz, List<String> selectedFieldList);

}
