package com.sankuai.shangou.seashop.base.eimport.input;

import java.io.File;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.DefaultReadListener;
import com.sankuai.shangou.seashop.base.eimport.ExcelReadHelper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer;
import com.sankuai.shangou.seashop.base.eimport.ImportResult;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.base.eimport.context.ImportContextHolder;
import com.sankuai.shangou.seashop.base.export.output.FileWriteAndOutputWay;

import cn.hutool.core.io.IoUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/21 20:09
 */
@Slf4j
public abstract class ImportWay<D extends RowReadResult, T> {

    @Resource
    private ImportHandlerContainer importHandlerContainer;
    @Resource
    @Lazy
    private FileWriteAndOutputWay fileWriteAndOutputWay;

    /**
     * excel包导入最大限制 1M
     */
    protected static Long DEFAULT_EXCEL_IMPORT_MAX_SIZE = 1 * 1024 * 1024L;

    public ImportResult importData(BizType bizType, InputStream inputStream, Class<D> dataClz, DefaultReadListener<D> listener) {
        log.info("获取导入处理器: bizType={}", bizType);
        ImportHandler<D> importHandler = importHandlerContainer.getImportHandler(bizType);
        AssertUtil.throwIfNull(importHandler, "导入处理器不存在");
        try {

            ReadResult<D> importResult = ExcelReadHelper.read(inputStream, dataClz, listener);
            AssertUtil.throwIfTrue(CollectionUtils.isEmpty(importResult.getDataList()), "待导入的数据为空");

            importHandler.checkExistsAndSetValue(importResult);

            List<D> successDataList = importResult.getSuccessDataList();
            if (!CollectionUtils.isEmpty(successDataList)) {
                importHandler.saveImportData(successDataList);
            }
            successDataList = importResult.getSuccessDataList();
            List<D> errList = importResult.getErrDataList();
            int successCount = successDataList.size();
            int errCount = errList.size();
            String filePath = "";
            if (!CollectionUtils.isEmpty(errList)) {
                DataWrapper<D> dataWrapper = importHandler.wrapData(errList);

                filePath = fileWriteAndOutputWay.writeAndOutput(dataWrapper, UUID.randomUUID().toString(), new Date(), null);
                errCount = errList.size();
            }
            ImportResult result = ImportResult.builder().successCount(successCount).errCount(errCount).filePath(filePath).build();
            log.info("导入成功: {}", JsonUtil.toJsonString(result));
            return result;
        }
        catch (Exception e) {
            log.error("导入失败: {}", e);
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }

            throw new BusinessException("导入失败");
        }
        finally {
            IoUtil.close(inputStream);
            ImportContextHolder.remove();
        }
    }

    public abstract ImportResult importData(BizType bizType, T file, Class<D> dataClz, DefaultReadListener<D> listener);

    public abstract ImportResult importData(BizType bizType, T file, Class<D> dataClz, DefaultReadListener<D> listener, Long maxSize);

    @SneakyThrows
    protected void checkFileSize(File file, Long maxSize) {
        if (maxSize == null) {
            return;
        }

        if (file.length() > maxSize) {
            throw new BusinessException(String.format("【EXCEL】不能超过%s", FileUtils.byteCountToDisplaySize(maxSize)));
        }
    }

}
