package com.sankuai.shangou.seashop.base.utils;

import java.util.function.Supplier;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

/**
 * <AUTHOR>
 * @date 2023/11/18 9:41
 */
@Component
public class TransactionHelper implements ApplicationContextAware {

    private static ApplicationContext applicationContext;
    private static volatile DataSourceTransactionManager dataSourceTransactionManager;

    public static void doInTransaction(VoidSupplier supplier, int propagationBehavior) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehavior(propagationBehavior);
        DataSourceTransactionManager transactionManager = getDataSourceTransactionManager();
        TransactionStatus transactionStatus = transactionManager.getTransaction(definition);
        try {
            supplier.apply();
            transactionManager.commit(transactionStatus);
        }
        catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw e;
        }
    }

    public static void doInTransaction(VoidSupplier supplier) {
        doInTransaction(supplier, TransactionDefinition.PROPAGATION_REQUIRED);
    }

    public static <T> T doInTransaction(Supplier<T> supplier, int propagationBehavior) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehavior(propagationBehavior);
        DataSourceTransactionManager transactionManager = getDataSourceTransactionManager();
        TransactionStatus transactionStatus = transactionManager.getTransaction(definition);
        T data;
        try {
            data = supplier.get();
            transactionManager.commit(transactionStatus);
        }
        catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw e;
        }
        return data;
    }

    public static <T> T doInTransaction(Supplier<T> supplier) {
        return doInTransaction(supplier, TransactionDefinition.PROPAGATION_REQUIRED);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private static DataSourceTransactionManager getDataSourceTransactionManager() {
        if (dataSourceTransactionManager == null) {
            synchronized (TransactionHelper.class) {
                if (dataSourceTransactionManager == null) {
                    dataSourceTransactionManager = applicationContext.getBean(DataSourceTransactionManager.class);
                }
            }
        }
        return dataSourceTransactionManager;
    }
}
