package com.sankuai.shangou.seashop.base.export.output;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.config.ExportTaskProps;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Slf4j
public class UploadOutputWay<P extends BasePageReq> extends AbstractWriteAndOutput<P> {

    /**
     * 导出成excel后，excel文件上传到文件服务器的路径前缀
     */
    private final static String NFS_PATH_PREFIX = "export/excel";

    @Resource
    @Lazy
    private S3plusStorageService s3plusStorageService;
    @Resource
    private ExportTaskProps exportTaskProps;

    @Override
    protected String outputFile(byte[] data, String fileName, String taskId, String curTimeStr) {
        return uploadFile(taskId, fileName, data, curTimeStr);
    }

    private String uploadFile(String taskId, String fileName, byte[] data, String curTimeStr) {
        // 构建上传后的路径
        String filePath = buildFilePath(fileName, taskId, curTimeStr);
        log.info("【数据导出】任务={}到路径={}", taskId, filePath);
        // 上传前，先删除原文件，目前的测试结果是重复的路径+名称，文件不会覆盖
        // TODO 目前即使删除，同名的文件还是历史内容
        // 上传
        s3plusStorageService.sendByte2S3(data, filePath);
        return filePath;
    }

    public String buildFilePath(String fileName, String taskId, String curTimeStr) {
        return getFilePathPrefix() + "/" + taskId + "/" + fileName + curTimeStr + ".xlsx";
    }

    public String getFilePathPrefix() {
        String configPrefix = exportTaskProps.getNfsPathPrefix();
        if (configPrefix != null && !"".equals(configPrefix.trim())) {
            return configPrefix;
        }
        return NFS_PATH_PREFIX;
    }
}
