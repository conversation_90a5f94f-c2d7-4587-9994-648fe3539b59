package com.sankuai.shangou.seashop.base.s3plus;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@ConditionalOnProperty(name = "hishop.storage.endpoint")
public class S3plusConfig {

    @Bean
    public S3plusStorageService s3plusStorageService() {
        return new S3plusStorageService();
    }
}
