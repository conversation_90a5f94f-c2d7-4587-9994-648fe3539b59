package com.sankuai.shangou.seashop.base.export.writter;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.AbstractCustomDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.CustomDataGetter;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 */
public class TestCustomGetter extends AbstractCustomDataGetter<BasePageReq>
        implements CustomDataGetter<BasePageReq> {


    @Override
    public Integer getModule() {
        return null;
    }

    @Override
    public String getFileName() {
        return null;
    }

    @Override
    protected ByteArrayOutputStream writeToOutputStream(BasePageReq param) {
        return null;
    }
}
