package com.sankuai.shangou.seashop.base.export.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.base.export.anno.RowKey;
import com.sankuai.shangou.seashop.base.export.anno.ShouldMerge;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 */
@Data
public class XOrderExcelVO extends RowReadResult {

    @ExcelProperty(value = "订单号")
    private String orderId;
    @ExcelProperty(value = "下单日期")
    private String orderDate;
    @ExcelProperty(value = "店铺ID")
    @ShouldMerge
    @RowKey
    private Long shopId;
    @ExcelProperty(value = "店铺名称")
    @ShouldMerge
    private String shopName;
    @ExcelProperty(value = "订单金额")
    private BigDecimal orderAmount;
    @ExcelProperty(value = "用户ID")
    private String userId;



    public static List<XOrderExcelVO> pageList(int from, int to) {
        if (from >= to) {
            return null;
        }
        List<XOrderExcelVO> list = randomList();
        if (to > list.size()) {
            to = list.size();
        }
        if (from >= to) {
            return null;
        }
        return list.subList(from, to);
    }

    private static List<XOrderExcelVO> randomList() {
        // 生成10个订单，包含3个店铺，4个用户
        List<XOrderExcelVO> list = new ArrayList<>();

        // 随机生成3个店铺ID
        List<Long> shopIds = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            shopIds.add(new Random().nextLong());
        }

        // 随机生成4个用户ID
        List<String> userIds = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            userIds.add(String.valueOf(new Random().nextLong()));
        }

        // 随机生成订单日期
        LocalDate now = LocalDate.now();
        for (int i = 0; i < 10; i++) {
            XOrderExcelVO vo = new XOrderExcelVO();
            vo.setOrderId(String.valueOf(i));
            vo.setOrderDate(now.plusDays(i).toString());
            vo.setShopId(shopIds.get(i % 3));
            vo.setShopName("店铺名称" + i);
            vo.setOrderAmount(new BigDecimal(i * 100));
            vo.setUserId(userIds.get(i % 4));
            list.add(vo);
        }

        return list;
    }

}
