package com.sankuai.shangou.seashop.base.export.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.export.anno.RowKey;
import com.sankuai.shangou.seashop.base.export.anno.ShouldMerge;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试类-订单商品Excel对象
 * <AUTHOR>
 * @date 2023/6/14
 */
@Data
public class XOrderProductExcelVO {


    @ExcelProperty(value = "店铺ID")
    @ShouldMerge
    private Long shopId;
    @ExcelProperty(value = "店铺名称")
    @ShouldMerge
    private String shopName;
    @ExcelProperty(value = "订单号")
    @ShouldMerge
    @RowKey
    private String orderNo;
    @ExcelProperty(value = "用户ID")
    @ShouldMerge
    private String userId;
    @ExcelProperty(value = "用户名称")
    @ShouldMerge
    private String userName;
    @ExcelProperty(value = "用户手机号码")
    @ShouldMerge
    private String userPhone;
    @ExcelProperty(value = "订单状态")
    private String orderStatus;
    @ExcelProperty(value = "商品编码")
    private String productCode;
    @ExcelProperty(value = "商品名称")
    private String productName;
    @ExcelProperty(value = "购买价格")
    private BigDecimal salePrice;
    @ExcelProperty(value = "购买数量")
    private Long quantity;



    public static List<XOrderProductExcelVO> randomList() {
        List<XOrderProductExcelVO> list = new ArrayList<>(10);
        XOrderProductExcelVO vo1 = new XOrderProductExcelVO();
        vo1.setShopId(1L);
        vo1.setShopName("伊利");
        vo1.setOrderNo("SO1001");
        vo1.setUserId("U1001");
        vo1.setUserName("张三");
        vo1.setUserPhone("13412340001");
        vo1.setOrderStatus("已支付");
        vo1.setProductCode("1001");
        vo1.setProductName("安慕希原味酸奶");
        vo1.setSalePrice(new BigDecimal("5.60"));
        vo1.setQuantity(1L);
        list.add(vo1);

        XOrderProductExcelVO vo2 = new XOrderProductExcelVO();
        vo2.setShopId(1L);
        vo2.setShopName("伊利");
        vo2.setOrderNo("SO1001");
        vo2.setUserId("U1001");
        vo2.setUserName("张三");
        vo2.setUserPhone("13412340001");
        vo2.setOrderStatus("已支付");
        vo2.setProductCode("1002");
        vo2.setProductName("芝士波波球");
        vo2.setSalePrice(new BigDecimal("5.60"));
        vo2.setQuantity(2L);
        list.add(vo2);

        XOrderProductExcelVO vo3 = new XOrderProductExcelVO();
        vo3.setShopId(1L);
        vo3.setShopName("伊利");
        vo3.setOrderNo("SO1003");
        vo3.setUserId("U1001");
        vo3.setUserName("张三");
        vo3.setUserPhone("13412340001");
        vo3.setOrderStatus("已支付");
        vo3.setProductCode("1002");
        vo3.setProductName("芝士波波球");
        vo3.setSalePrice(new BigDecimal("5.60"));
        vo3.setQuantity(2L);
        list.add(vo3);

        XOrderProductExcelVO vo4 = new XOrderProductExcelVO();
        vo4.setShopId(2L);
        vo4.setShopName("美团");
        vo4.setOrderNo("SO1004");
        vo4.setUserId("U1001");
        vo4.setUserName("张三");
        vo4.setUserPhone("13412340001");
        vo4.setOrderStatus("待付款");
        vo4.setProductCode("1002");
        vo4.setProductName("芝士波波球");
        vo4.setSalePrice(new BigDecimal("5.60"));
        vo4.setQuantity(3L);
        list.add(vo4);

        XOrderProductExcelVO vo5 = new XOrderProductExcelVO();
        vo5.setShopId(2L);
        vo5.setShopName("美团");
        vo5.setOrderNo("SO1004");
        vo5.setUserId("U1001");
        vo5.setUserName("张三");
        vo5.setUserPhone("13412340001");
        vo5.setOrderStatus("待付款");
        vo5.setProductCode("3003");
        vo5.setProductName("伊利纯牛奶");
        vo5.setSalePrice(new BigDecimal("5.60"));
        vo5.setQuantity(3L);
        list.add(vo5);

        XOrderProductExcelVO vo6 = new XOrderProductExcelVO();
        vo6.setShopId(2L);
        vo6.setShopName("美团");
        vo6.setOrderNo("SO1004");
        vo6.setUserId("U1001");
        vo6.setUserName("张三");
        vo6.setUserPhone("13412340001");
        vo6.setOrderStatus("待付款");
        vo6.setProductCode("3004");
        vo6.setProductName("拉比克");
        vo6.setSalePrice(new BigDecimal("5.60"));
        vo6.setQuantity(3L);
        list.add(vo6);


        return list;
    }

    public static List<XOrderProductExcelVO> pageList(int from, int to) {
        if (from >= to) {
            return null;
        }
        List<XOrderProductExcelVO> list = randomList();
        if (to > list.size()) {
            to = list.size();
        }
        if (from >= to) {
            return null;
        }
        return list.subList(from, to);
    }

}
