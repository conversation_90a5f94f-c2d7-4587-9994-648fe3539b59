package com.sankuai.shangou.seashop.base.export.getter;

import com.alibaba.excel.EasyExcel;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.AbstractCustomDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.CustomDataGetter;
import com.sankuai.shangou.seashop.base.export.model.XOrderProductExcelVO;
import com.sankuai.shangou.seashop.base.export.writeHandler.DefaultHeadStyle;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CustomFetcherGetter extends AbstractCustomDataGetter<BasePageReq>
        implements CustomDataGetter<BasePageReq> {


    @Override
    public Integer getModule() {
        return -999908;
    }

    @Override
    public String getFileName() {
        return "custom fetcher";
    }

    @Override
    protected ByteArrayOutputStream writeToOutputStream(BasePageReq param) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<XOrderProductExcelVO> exportList = XOrderProductExcelVO.randomList();
        EasyExcel.write(outputStream, XOrderProductExcelVO.class)
                .registerWriteHandler(DefaultHeadStyle.getInstance())
                .sheet("sheetNameA")
                .doWrite(exportList);
        return outputStream;
    }
}
