package com.sankuai.shangou.seashop.base.utils

import spock.lang.Shared
import spock.lang.Specification
/**
 * <AUTHOR>
 * @date 2024/03/02 15:09
 */
class CompressUtilSpec extends Specification {

    @Shared
    def static testDirPath = "build/test_resources"

    def setup() {
        // 在每个测试方法执行前准备测试文件和文件夹
        prepareTestFiles()
    }

    def cleanup() {
        // 在每个测试方法执行后清理测试文件和文件夹
        cleanupTestFiles(new File(testDirPath))
    }

    def "compress should work for multiple files"() {
        given:
        File[] sourceFiles = [
                new File("$testDirPath/testfile1.txt"),
                new File("$testDirPath/testfile2.txt"),
                new File("$testDirPath/testfile3.txt")
        ]
        def zipFilePath = "build/multiplefiles.zip"

        when:
        CompressUtil.compress(sourceFiles, zipFilePath)
        def decompressedFolder = CompressUtil.decompress(zipFilePath, "$testDirPath/decompressed_multiple")

        then:
        decompressedFolder.listFiles().size() == sourceFiles.size()
        decompressedFolder.listFiles().every { it.isFile() }
    }

    def "decompress should work for a single file"() {
        given:
        File[] sourceFiles = [
                new File("$testDirPath/testfile1.txt")
        ]
        def zipFilePath = "build/singlefile.zip"

        when:
        CompressUtil.compress(sourceFiles, zipFilePath)
        def decompressedFolder = CompressUtil.decompress(zipFilePath, "$testDirPath/decompressed_single")

        then:
        decompressedFolder.listFiles().size() == 1
        decompressedFolder.listFiles()[0].name == sourceFiles[0].name
    }

    def "decompress should work for multiple files"() {
        given:
        File[] sourceFiles = [
                new File("$testDirPath/testfile1.txt"),
                new File("$testDirPath/testfile2.txt"),
                new File("$testDirPath/testfile3.txt")
        ]
        def zipFilePath = "build/multiplefiles.zip"

        when:
        CompressUtil.compress(sourceFiles, zipFilePath)
        def decompressedFolder = CompressUtil.decompress(zipFilePath, "$testDirPath/decompressed_multiple")

        then:
        decompressedFolder.listFiles().size() == sourceFiles.size()
        decompressedFolder.listFiles().every { it.isFile() }
    }

    def "delFolder should delete the specified directory and its contents"() {
        given:
        def dirPath = "build/testDir"

        when:
        CompressUtil.delFolder(dirPath)

        then:
        !new File(dirPath).exists()
    }

    def "getFiles should return a list of file paths in the given directory and its subdirectories"() {
        given:
        def dirPath = "$testDirPath/subfolder"
        def expectedFileNames = ["subfile1.txt", "subfile2.txt"]
        def expectedFilePaths = expectedFileNames.collect { "$dirPath/$it" }

        when:
        new File(dirPath).mkdirs()
        expectedFilePaths.each { new File(it).text = "Test content" }
        def actualFilePaths = CompressUtil.getFiles(dirPath)
        def actualFiles = actualFilePaths.collect { new File(it) }

        then:
        actualFiles.collect { it.canonicalPath } == expectedFilePaths.collect { new File(it).canonicalPath }
    }

    static void prepareTestFiles() {
        // 创建一个测试文件夹
        File testDir = new File(testDirPath)
        testDir.mkdirs()

        // 创建待压缩的文件
        File file1 = new File(testDir, "testfile1.txt")
        File file2 = new File(testDir, "testfile2.txt")
        File file3 = new File(testDir, "testfile3.txt")

        // 写入一些测试内容到文件中
        file1.text = "Content of testfile1"
        file2.text = "Content of testfile2"
        file3.text = "Content of testfile3"
    }

    static void cleanupTestFiles(File testDir) {
        // 删除测试文件夹及其内容
        if (testDir.exists()) {
            CompressUtil.delFolder(testDir.toString())
        }
    }
}
