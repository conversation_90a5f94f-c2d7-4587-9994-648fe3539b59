package com.sankuai.shangou.seashop.base;

import java.nio.charset.StandardCharsets;
import java.util.Locale;

import cn.hutool.crypto.digest.MD5;

/**
 * <AUTHOR>
 */
public class DemoTest {

    public static void main(String[] args) {
        String signStr = "{\"sendType\":0,\"fromAddress\":\"金水路226号楷林国际\",\"fromUsernote\":\"A座4层\",\"fromLat\":\"34.768959\"," +
            "\"fromLng\":\"113.724459\",\"toAddress\":\"金水路226号楷林国际\",\"toUsernote\":\"A座4层\",\"toLat\":\"34.768959\",\"toLng\":\"113.724459\",\"cityName\":\"郑州市\",\"countyName\":\"金水区\",\"goodsType\":\"美食\",\"goodsWeight\":1}2815a7a1f8e3405d81fd6263683ec4e71434600000";

        System.out.println(MD5.create().digestHex(signStr, StandardCharsets.UTF_8).toUpperCase(Locale.ROOT));
    }
}
