package com.sankuai.shangou.seashop.base.export;

import com.sankuai.shangou.seashop.base.export.getter.SingleSheetGetter;
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.base.export.handler.ExportTaskHandler;
import com.sankuai.shangou.seashop.base.export.output.ToLocalOutputWay;

import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class SingleSheetExportMainTest {

    public static void main(String[] args) {
        ExportDataGetter dataGetter = new SingleSheetGetter();
        ExportTask task = new ExportTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setExecuteParam("{}");
        task.setTaskDate(new Date());
        new ExportTaskHandler(new ToLocalOutputWay()).executeForDataGetter(dataGetter, task);
    }

}
