package com.sankuai.shangou.seashop.base.export.getter;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.base.export.model.XOrderProductExcelVO;
import com.sankuai.shangou.seashop.base.export.wrapper.OrderProductDataWrapper;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
public class SingleSheetGetter
        extends AbstractBaseDataGetter<BasePageReq>
        implements SingleWrapperDataGetter<BasePageReq> {

    private XOrderProductExcelVO repository = new XOrderProductExcelVO();

    @Override
    public Integer getModule() {
        return -999905;
    }

    @Override
    public String getFileName() {
        return "分页获取数据";
    }

    @Override
    public DataContext selectData(BasePageReq param) {
        DataContext context = new DataContext();
        BaseExportWrapper<XOrderProductExcelVO, BasePageReq> wrapper1 = new OrderProductDataWrapper(repository);
        //BaseExportWrapper<XOrderProductExcelVO, QueryBasePO> wrapper2 = new PageDataWrapper2(() -> ((PageDataWrapper)wrapper1).getExistsList());

        context.setSheetDataList(Arrays.asList(wrapper1));
        return context;
    }
}
