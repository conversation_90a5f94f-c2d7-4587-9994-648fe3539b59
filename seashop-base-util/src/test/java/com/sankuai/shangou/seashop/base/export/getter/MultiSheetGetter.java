package com.sankuai.shangou.seashop.base.export.getter;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.base.export.model.XOrderExcelVO;
import com.sankuai.shangou.seashop.base.export.model.XOrderProductExcelVO;
import com.sankuai.shangou.seashop.base.export.wrapper.OrderDataWrapper;
import com.sankuai.shangou.seashop.base.export.wrapper.OrderProductDataWrapper;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
public class MultiSheetGetter
        extends AbstractBaseDataGetter<BasePageReq>
        implements SingleWrapperDataGetter<BasePageReq> {

    private XOrderProductExcelVO repository = new XOrderProductExcelVO();
    private XOrderExcelVO orderRepository = new XOrderExcelVO();

    @Override
    public Integer getModule() {
        return -999906;
    }

    @Override
    public String getFileName() {
        return "分页获取数据";
    }

    @Override
    public DataContext selectData(BasePageReq param) {
        DataContext context = new DataContext();

        BaseExportWrapper<XOrderExcelVO, BasePageReq> wrapper1 = new OrderDataWrapper(orderRepository);
        BaseExportWrapper<XOrderProductExcelVO, BasePageReq> wrapper2 = new OrderProductDataWrapper(repository);

        context.setSheetDataList(Arrays.asList(wrapper1, wrapper2));
        return context;
    }
}
