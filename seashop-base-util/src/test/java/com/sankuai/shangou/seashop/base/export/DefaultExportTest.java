package com.sankuai.shangou.seashop.base.export;

import cn.hutool.core.io.FileUtil;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.export.model.XOrderExcelVO;
import com.sankuai.shangou.seashop.base.export.writter.DefaultExcelWriteStrategy;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DefaultExportTest {

    public static void main(String[] args){
        List<XOrderExcelVO> list = XOrderExcelVO.pageList(0, 10);
        DataWrapper<XOrderExcelVO> dataWrapper = new DataWrapper<XOrderExcelVO>() {
            @Override
            public List<XOrderExcelVO> getDataList() {
                return list;
            }

            @Override
            public Integer getModule() {
                return 1;
            }

            @Override
            public String getFileName() {
                return "a";
            }
        };
        ByteArrayOutputStream stream = new DefaultExcelWriteStrategy<>().writeToOutputStream(dataWrapper, null);
        FileUtil.writeBytes(stream.toByteArray(), "E:/test.xlsx");
    }

}
