package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CouponProductMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponProductParamDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@Repository
@Slf4j
public class CouponProductRepository extends ServiceImpl<CouponProductMapper, CouponProduct> {

    @Resource
    private CouponProductMapper couponProductMapper;

    public void deleteByCouponId(Long couponId) {
        log.info("deleteByCouponId-couponId:{}", couponId);
        LambdaQueryWrapper<CouponProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CouponProduct::getCouponId, couponId);
        couponProductMapper.delete(queryWrapper);
    }

    public long countByCouponId(CouponProductParamDto paramDto) {
        LambdaQueryWrapper<CouponProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(null != paramDto.getCouponId(), CouponProduct::getCouponId, paramDto.getCouponId());
        queryWrapper.eq(CouponProduct::getDelFlag, Boolean.FALSE);
        return couponProductMapper.selectCount(queryWrapper);
    }

    public List<CouponProduct> listByCouponId(Long couponId) {
        LambdaQueryWrapper<CouponProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CouponProduct::getCouponId, couponId);
        queryWrapper.eq(CouponProduct::getDelFlag, Boolean.FALSE);
        queryWrapper.orderByAsc(CouponProduct::getId);
        return couponProductMapper.selectList(queryWrapper);
    }

    /**
     * 通过优惠券ID列表查询
     */
    public List<CouponProduct> listByCouponIds(List<Long> couponIds) {
        LambdaQueryWrapper<CouponProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CouponProduct::getCouponId, couponIds);
        queryWrapper.eq(CouponProduct::getDelFlag, Boolean.FALSE);
        return couponProductMapper.selectList(queryWrapper);
    }
}
