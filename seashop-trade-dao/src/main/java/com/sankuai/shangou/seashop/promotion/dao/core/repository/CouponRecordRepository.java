package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponRecord;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CouponRecordExtMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CouponRecordMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordConsumeDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordSimpleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@Repository
@Slf4j
public class CouponRecordRepository extends ServiceImpl<CouponRecordMapper, CouponRecord> {

    @Resource
    private CouponRecordMapper couponRecordMapper;
    @Resource
    private CouponRecordExtMapper couponRecordExtMapper;

    public List<CouponRecord> listByCouponId(Long couponId) {
        LambdaQueryWrapper<CouponRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CouponRecord::getCouponId, couponId);
        queryWrapper.orderByAsc(CouponRecord::getId);
        return couponRecordMapper.selectList(queryWrapper);
    }

    public long countByCouponAndUserId(CouponRecordParamDto paramDto) {
        LambdaQueryWrapper<CouponRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(null != paramDto.getCouponId(), CouponRecord::getCouponId, paramDto.getCouponId());
        queryWrapper.eq(null != paramDto.getUserId(), CouponRecord::getUserId, paramDto.getUserId());
        return couponRecordMapper.selectCount(queryWrapper);
    }

    public BasePageResp<CouponRecordSimpleDto> pageList(BasePageParam paramPage, CouponRecordParamDto paramDto) {
        Page page = PageHelper.startPage(paramPage);
        couponRecordExtMapper.pageList(paramDto);
        return PageResultHelper.transfer(page, CouponRecordSimpleDto.class);
    }

    public Integer queryAvailableCouponCountByUser(Long userId) {
        return couponRecordMapper.queryAvailableCouponCountByUser(userId);
    }

    public List<CouponRecord> queryListByIdAndUserId(CouponRecordConsumeDto model) {
        return couponRecordExtMapper.queryListByIdAndUserId(model.getMemberId(), model.getConsumeList());
    }

    public int cancelConsume(List<Long> ids, Integer couponStatus) {
        UpdateWrapper<CouponRecord> updateWrapper = new UpdateWrapper<>();
        // 外层已经处理
        updateWrapper.in("id", ids);
        updateWrapper.set("coupon_status", couponStatus);
        updateWrapper.set("order_id", null);
        updateWrapper.set("used_time", null);
        return this.baseMapper.update(null, updateWrapper);
    }

    public List<CouponRecord> getByIdOrSn(List<Long> idList, List<String> snList) {
        LambdaQueryWrapper<CouponRecord> queryWrapper = new LambdaQueryWrapper<>();
        // 入口已经校验处理
        queryWrapper.in(null != idList, CouponRecord::getId, idList);
        // 入口已经校验处理
        queryWrapper.in(null != snList, CouponRecord::getCouponSn, snList);
        return this.baseMapper.selectList(queryWrapper);
    }

    /**
     * 通过订单ID查询优惠券记录
     *
     * @param orderIdList
     * @return
     */
    public List<CouponRecord> queryListByOrderId(List<String> orderIdList) {
        LambdaQueryWrapper<CouponRecord> queryWrapper = new LambdaQueryWrapper<>();
        // 外层已经处理
        queryWrapper.in(CouponRecord::getOrderId, orderIdList);
        return this.baseMapper.selectList(queryWrapper);
    }

    public List<CouponRecord> listByCouponIdsAndUserId(List<Long> couponIds, Long userId) {
        if (CollectionUtils.isEmpty(couponIds) || userId == null) {
            return Collections.emptyList();
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<CouponRecord>().in(CouponRecord::getCouponId, ids).eq(CouponRecord::getUserId, userId)), couponIds);
    }

    /**
     * 通过用户ID和店铺ID查询当前时间可用的优惠券记录
     *
     * @param userId
     * @param shopIdList
     * @param couponStatus
     */
    public List<CouponRecordSimpleDto> queryListByUserIdAndShopId(Long userId, List<Long> shopIdList, Integer couponStatus) {
        CouponRecordParamDto queryBo = new CouponRecordParamDto();
        queryBo.setUserId(userId);
        queryBo.setShopIds(shopIdList);
        queryBo.setStatus(couponStatus);
        List<CouponRecordSimpleDto> couponRecordSimpleModels = couponRecordExtMapper.pageList(queryBo);
        return couponRecordSimpleModels;
    }
    public List<CouponRecord> getByUpdateTime(Date updateTime) {
        LambdaQueryWrapper<CouponRecord> queryWrapper = new LambdaQueryWrapper<>();
        if (updateTime != null) {
            queryWrapper.ge(CouponRecord::getUpdateTime, updateTime);
        }

        return baseMapper.selectList(queryWrapper);
    }


}
