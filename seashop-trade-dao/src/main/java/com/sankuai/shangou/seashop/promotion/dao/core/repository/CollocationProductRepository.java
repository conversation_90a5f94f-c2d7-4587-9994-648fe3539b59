package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationSku;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CollocationProductMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CollocationSkuMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 16:26
 */
@Repository
@Slf4j
public class CollocationProductRepository {

    @Resource
    private CollocationProductMapper collocationProductMapper;

    @Resource
    private CollocationSkuMapper collocationSkuMapper;

    public CollocationProduct selectById(Long id){
        return collocationProductMapper.selectById(id);
    }

    public List<CollocationProduct> selectByColloIds(List<Long> colloIds){
        QueryWrapper<CollocationProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(CollocationProduct::getColloId, colloIds);
        return collocationProductMapper.selectList(queryWrapper);
    }

    public int getSkuReqListSize(Long colloProductId){
        LambdaQueryWrapper<CollocationSku> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CollocationSku::getColloProductId, colloProductId);
        return Math.toIntExact(collocationSkuMapper.selectCount(queryWrapper));
    }

    public Long insertCollocationProduct(CollocationProduct collocationProduct){
        collocationProductMapper.insert(collocationProduct);
        return collocationProduct.getId();
    }

    public Long updateCollocationProductById(CollocationProduct collocationProduct){
        collocationProductMapper.updateById(collocationProduct);
        return collocationProduct.getId();
    }

    public int deleteCollocationProductByColloId(Long colloId){
        QueryWrapper<CollocationProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollocationProduct::getColloId, colloId);
        return collocationProductMapper.delete(queryWrapper);
    }

    public List<CollocationProduct> queryCollocationProductList(CollocationProduct request){
        QueryWrapper<CollocationProduct> queryWrapper = new QueryWrapper<>();
        if(request.getId() != null){
            queryWrapper.lambda().eq(CollocationProduct::getId, request.getId());
        }
        if(request.getColloId() != null){
            queryWrapper.lambda().eq(CollocationProduct::getColloId, request.getColloId());
        }
        if(request.getProductId() != null){
            queryWrapper.lambda().eq(CollocationProduct::getProductId, request.getProductId());
        }
        return collocationProductMapper.selectList(queryWrapper);
    }

    /**
     * 查询所有有效的组合购商品信息
     * @param shopId
     * @return
     */
    public List<CollocationProduct> listEffectiveByShopId(Long shopId){
        return collocationProductMapper.listEffectiveByShopId(shopId);
    }
}
