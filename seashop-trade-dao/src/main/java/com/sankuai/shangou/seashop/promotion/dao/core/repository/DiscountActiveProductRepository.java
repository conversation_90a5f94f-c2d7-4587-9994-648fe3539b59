package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActiveProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.DiscountActiveProductMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveProductParamDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@Repository
@Slf4j
public class DiscountActiveProductRepository extends ServiceImpl<DiscountActiveProductMapper, DiscountActiveProduct> {

    @Resource
    private DiscountActiveProductMapper discountActiveProductMapper;

    public long countByExample(DiscountActiveProductParamDto paramDto) {
        LambdaQueryWrapper<DiscountActiveProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(null != paramDto.getActiveId(), DiscountActiveProduct::getActiveId, paramDto.getActiveId());
        queryWrapper.eq(DiscountActiveProduct::getDelFlag, Boolean.FALSE);
        return discountActiveProductMapper.selectCount(queryWrapper);
    }

    public List<DiscountActiveProduct> listByActiveId(Long activeId) {
        LambdaQueryWrapper<DiscountActiveProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DiscountActiveProduct::getActiveId, activeId);
        queryWrapper.eq(DiscountActiveProduct::getDelFlag, Boolean.FALSE);
        queryWrapper.orderByAsc(DiscountActiveProduct::getId);
        return discountActiveProductMapper.selectList(queryWrapper);
    }
}
