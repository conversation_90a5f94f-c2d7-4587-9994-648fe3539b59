package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleConfig;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.FlashSaleConfigMapper;
import org.springframework.stereotype.Repository;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Repository
public class FlashSaleConfigRepository extends ServiceImpl<FlashSaleConfigMapper, FlashSaleConfig> {

    /**
     * 通过shopId查询限时购配置
     *
     * @param shopId
     * @return
     */
    public FlashSaleConfig selectByShopId(Long shopId) {
        LambdaQueryWrapper<FlashSaleConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSaleConfig::getShopId, shopId);
        return this.getOne(queryWrapper);
    }
}
