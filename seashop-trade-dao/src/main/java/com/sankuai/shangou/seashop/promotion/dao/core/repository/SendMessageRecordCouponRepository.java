package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.SendMessageRecordCoupon;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.SendMessageRecordCouponMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@Repository
@Slf4j
public class SendMessageRecordCouponRepository extends ServiceImpl<SendMessageRecordCouponMapper, SendMessageRecordCoupon> {



    public List<SendMessageRecordCoupon> getByMessageId(Long id) {
        return lambdaQuery().eq(SendMessageRecordCoupon::getMessageId, id).list();
    }
}
