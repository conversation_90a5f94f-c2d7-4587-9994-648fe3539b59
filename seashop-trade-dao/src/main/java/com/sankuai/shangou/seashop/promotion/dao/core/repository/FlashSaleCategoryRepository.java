package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleCategory;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.FlashSaleCategoryMapper;
import org.springframework.stereotype.Repository;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Repository
public class FlashSaleCategoryRepository extends ServiceImpl<FlashSaleCategoryMapper, FlashSaleCategory> {

    /**
     * 查询限时购分类数量
     *
     * @return
     */
    public long count() {
        LambdaQueryWrapper<FlashSaleCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSaleCategory::getDelFlag, Boolean.FALSE);
        return this.count(queryWrapper);
    }

    /**
     * 通过名字查询数量，用于判断是否重复
     *
     * @param categoryName
     * @return
     */
    public int countByName(String categoryName) {
        LambdaQueryWrapper<FlashSaleCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSaleCategory::getDelFlag, Boolean.FALSE);
        queryWrapper.eq(FlashSaleCategory::getCategoryName, categoryName);
        return Math.toIntExact(this.count(queryWrapper));
    }

    public Page<FlashSaleCategory> pageList(BasePageParam paramPage) {
        Page<FlashSaleCategory> page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<FlashSaleCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSaleCategory::getDelFlag, Boolean.FALSE);
        queryWrapper.orderByDesc(FlashSaleCategory::getId);
        page.doSelectPage(() -> this.list(queryWrapper));
        return page;
    }
}
