package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.Collocation;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CollocationMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CollocationProductMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 13:50
 */
@Repository
@Slf4j
public class CollocationRepository extends ServiceImpl<CollocationMapper, Collocation> {

    @Resource
    private CollocationMapper collocationMapper;

    @Resource
    private CollocationProductMapper collocationProductMapper;

    public Collocation selectById(Long id){
        return collocationMapper.selectById(id);
    }

    public List<Collocation> selectByIds(List<Long> ids){
        if(CollectionUtil.isEmpty(ids)){
            return null;
        }
        QueryWrapper<Collocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        return collocationMapper.selectList(queryWrapper);
    }

    public int getProductReqListSize(Long colloId){
        LambdaQueryWrapper<CollocationProduct> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CollocationProduct::getColloId, colloId);
        return Math.toIntExact(collocationProductMapper.selectCount(queryWrapper));
    }

    public Page<PageCollocationDto> pageSellerCollocation(PageSellerCollocationDto request, Integer pageNo, Integer pageSize){
        Page<PageCollocationDto> result = PageHelper.startPage(pageNo, pageSize);
        collocationMapper.pageSellerCollocation(request);
        return result;
    }

    public Page<PageCollocationDto> pageMCollocation(PageMCollocationDto request, Integer pageNo, Integer pageSize){
        Page<PageCollocationDto> result = PageHelper.startPage(pageNo, pageSize);
        collocationMapper.pageMCollocation(request);
        return result;
    }

    public Long insertCollocation(Collocation collocation){
        collocationMapper.insert(collocation);
        return collocation.getId();
    }

    public Long updateCollocationById(Collocation collocation){
        collocationMapper.updateById(collocation);
        return collocation.getId();
    }

    public List<Collocation> queryCollocationList(CollocationDto request){
        QueryWrapper<Collocation> queryWrapper = new QueryWrapper<>();
        if(request.getId() != null){
            queryWrapper.lambda().eq(Collocation::getId, request.getId());
        }
        if(!CollectionUtil.isEmpty(request.getColloIdList())){
            queryWrapper.lambda().in(Collocation::getId, request.getColloIdList());
        }
        if(request.getShopId() != null){
            queryWrapper.lambda().eq(Collocation::getShopId, request.getShopId());
        }
        return collocationMapper.selectList(queryWrapper);
    }

    public List<CollocationActivityRespDto> queryCollocationByProductIdsAndStatus(List<String> productIds, Integer flag){
        return collocationMapper.queryCollocationByProductIdsAndStatus(productIds, flag);
    }

}
