package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationSku;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CollocationSkuMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CollocationSkuDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 16:27
 */
@Repository
@Slf4j
public class CollocationSkuRepository {

    @Resource
    private CollocationSkuMapper collocationSkuMapper;

    public CollocationSku selectById(Long id){
        return collocationSkuMapper.selectById(id);
    }

    public Long insertCollocationSku(CollocationSku collocationSku){
        collocationSkuMapper.insert(collocationSku);
        return collocationSku.getId();
    }

    public Long updateCollocationSkuById(CollocationSku collocationSku){
        collocationSkuMapper.updateById(collocationSku);
        return collocationSku.getId();
    }

    public int deleteCollocationSkuByColloProductIds(List<Long> colloProductIds){
        QueryWrapper<CollocationSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(CollocationSku::getColloProductId, colloProductIds);
        return collocationSkuMapper.delete(queryWrapper);
    }

    public List<CollocationSku> queryCollocationSkuList(CollocationSkuDto request){
        QueryWrapper<CollocationSku> queryWrapper = new QueryWrapper<>();
        if(request.getId() != null){
            queryWrapper.lambda().eq(CollocationSku::getId, request.getId());
        }
        if(request.getColloProductId() != null){
            queryWrapper.lambda().eq(CollocationSku::getColloProductId, request.getColloProductId());
        }
        if(!CollectionUtil.isEmpty(request.getColloProductIdList())){
            queryWrapper.lambda().in(CollocationSku::getColloProductId, request.getColloProductIdList());
        }
        return collocationSkuMapper.selectList(queryWrapper);
    }
}
