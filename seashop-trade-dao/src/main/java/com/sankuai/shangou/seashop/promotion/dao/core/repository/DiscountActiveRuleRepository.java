package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActiveRule;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.DiscountActiveRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@Repository
@Slf4j
public class DiscountActiveRuleRepository extends ServiceImpl<DiscountActiveRuleMapper, DiscountActiveRule> {

    @Resource
    private DiscountActiveRuleMapper discountActiveRuleMapper;

    public DiscountActiveRule selectById(Long id) {
        return discountActiveRuleMapper.selectById(id);
    }

    public void deleteByActiveId(Long activeId) {
        log.info("deleteByActiveId-activeId:{}", activeId);
        LambdaQueryWrapper<DiscountActiveRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DiscountActiveRule::getActiveId, activeId);
        discountActiveRuleMapper.delete(queryWrapper);
    }

    public List<DiscountActiveRule> listByActiveId(Long activeId) {
        LambdaQueryWrapper<DiscountActiveRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DiscountActiveRule::getActiveId, activeId);
        queryWrapper.eq(DiscountActiveRule::getDelFlag, Boolean.FALSE);
        queryWrapper.orderByAsc(DiscountActiveRule::getId);
        return discountActiveRuleMapper.selectList(queryWrapper);
    }

        public List<DiscountActiveRule> listByActiveIds(Collection<Long> activeIds) {
        LambdaQueryWrapper<DiscountActiveRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DiscountActiveRule::getActiveId, activeIds);
        queryWrapper.eq(DiscountActiveRule::getDelFlag, Boolean.FALSE);
        queryWrapper.orderByAsc(DiscountActiveRule::getId);
        return discountActiveRuleMapper.selectList(queryWrapper);
    }
}
