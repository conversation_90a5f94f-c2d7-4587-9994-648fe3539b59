package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleStockReturn;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.FlashSaleStockReturnMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/17/017
 * @description:
 */
@Repository
public class FlashSaleStockReturnRepository extends ServiceImpl<FlashSaleStockReturnMapper, FlashSaleStockReturn> {

    /**
     * 通过relationId查询数据
     */
    public FlashSaleStockReturn selectByRelationId(Long relationId) {
        return new LambdaQueryChainWrapper<>(this.getBaseMapper()).eq(FlashSaleStockReturn::getRelationId, relationId).one();
    }

    /**
     * 通过skuId、flashSaleId、orderId查询数据
     */
    public List<FlashSaleStockReturn> selectBySkuIdAndFlashSaleId(String skuId, Long flashSaleId, String orderId) {
        return new LambdaQueryChainWrapper<>(this.getBaseMapper())
                .eq(FlashSaleStockReturn::getSkuId, skuId)
                .eq(FlashSaleStockReturn::getFlashSaleId, flashSaleId)
                .eq(FlashSaleStockReturn::getOrderId, orderId).list();
    }
}
