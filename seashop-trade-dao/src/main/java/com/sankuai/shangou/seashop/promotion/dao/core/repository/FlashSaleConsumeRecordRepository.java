package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleConsumeRecord;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.FlashSaleConsumeRecordMapper;
import org.springframework.stereotype.Repository;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
@Repository
public class FlashSaleConsumeRecordRepository extends ServiceImpl<FlashSaleConsumeRecordMapper, FlashSaleConsumeRecord> {

    /**
     * 根据用户ID和活动ID查询核销数量
     *
     * @param memberId
     * @param flashSaleId
     */
    public Integer countByMemberId(Long memberId, Long flashSaleId) {
        return Math.toIntExact(lambdaQuery()
            .eq(FlashSaleConsumeRecord::getMemberId, memberId)
            .eq(FlashSaleConsumeRecord::getFlashSaleId, flashSaleId)
            .eq(FlashSaleConsumeRecord::getConsumeFlag, Boolean.TRUE)
            .count());
    }

    /**
     * 根据用户ID和skuId查询核销数量
     *
     * @param memberId
     * @param flashSaleId
     * @param skuId
     */
    public Integer countByMemberIdAndSku(Long memberId, Long flashSaleId, String skuId) {
        return Math.toIntExact(lambdaQuery()
            .eq(FlashSaleConsumeRecord::getMemberId, memberId)
            .eq(FlashSaleConsumeRecord::getFlashSaleId, flashSaleId)
            .eq(FlashSaleConsumeRecord::getSkuId, skuId)
            .eq(FlashSaleConsumeRecord::getConsumeFlag, Boolean.TRUE)
            .count());
    }

    /**
     * 通过订单ID查询核销记录
     */
    public FlashSaleConsumeRecord getByOrderId(String orderId) {
        return lambdaQuery()
                .eq(FlashSaleConsumeRecord::getOrderId, orderId)
                .eq(FlashSaleConsumeRecord::getConsumeFlag, Boolean.TRUE)
                .one();
    }
}
