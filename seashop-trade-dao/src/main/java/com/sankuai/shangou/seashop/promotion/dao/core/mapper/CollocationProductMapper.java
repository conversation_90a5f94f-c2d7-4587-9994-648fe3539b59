package com.sankuai.shangou.seashop.promotion.dao.core.mapper;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationProduct;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 组合购商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
public interface CollocationProductMapper extends EnhancedMapper<CollocationProduct> {

    /**
     * 根据shopId查询有效的组合购商品
     *
     * @param shopId
     * @return
     */
    List<CollocationProduct> listEffectiveByShopId(@Param("shopId") Long shopId);
}
