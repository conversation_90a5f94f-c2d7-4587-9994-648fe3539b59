package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePriceProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ExclusivePriceProductMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceProductParamDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@Repository
@Slf4j
public class ExclusivePriceProductRepository extends ServiceImpl<ExclusivePriceProductMapper, ExclusivePriceProduct> {

    public void deleteByActiveId(Long activeId) {
        log.info("deleteByActiveId-activeId:{}", activeId);
        LambdaQueryWrapper<ExclusivePriceProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExclusivePriceProduct::getActiveId, activeId);
        baseMapper.delete(queryWrapper);
    }

    /**
     * 通过活动id查询专享价商品
     *
     * @param activeId
     * @return
     */
    public List<ExclusivePriceProduct> listByActiveId(Long activeId) {
        LambdaQueryWrapper<ExclusivePriceProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExclusivePriceProduct::getActiveId, activeId);
        queryWrapper.eq(ExclusivePriceProduct::getDelFlag,Boolean.FALSE);
        return this.list(queryWrapper);
    }

    public List<ExclusivePriceProduct> listByActiveIds(List<Long> activeIds) {
        if (CollectionUtils.isEmpty(activeIds)) {
            return Collections.emptyList();
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<ExclusivePriceProduct>()
                .in(ExclusivePriceProduct::getActiveId, ids).eq(ExclusivePriceProduct::getDelFlag, false)), activeIds);
    }

    public List<ExclusivePriceProduct> listByActiveOrMemberId(ExclusivePriceProductParamDto paramDto) {
        LambdaQueryWrapper<ExclusivePriceProduct> queryWrapper = new LambdaQueryWrapper<>();
        if (null != paramDto.getMemberId()) {
            queryWrapper.eq(ExclusivePriceProduct::getMemberId, paramDto.getMemberId());
        }
        queryWrapper.eq(null != paramDto.getActiveId(), ExclusivePriceProduct::getActiveId, paramDto.getActiveId());
        queryWrapper.eq(null != paramDto.getProductId(), ExclusivePriceProduct::getProductId, paramDto.getProductId());
        queryWrapper.eq(null != paramDto.getMemberId(), ExclusivePriceProduct::getMemberId, paramDto.getMemberId());
        queryWrapper.eq(ExclusivePriceProduct::getDelFlag,Boolean.FALSE);
        queryWrapper.orderByAsc(ExclusivePriceProduct::getId);
        if (paramDto.getQueryLimit() != null) {
            queryWrapper.last("limit " + paramDto.getQueryLimit());
        }
        return baseMapper.selectList(queryWrapper);
    }

    public Page<ExclusivePriceProduct> pageByActiveId(BasePageParam paramPage, ExclusivePriceProductParamDto paramDto) {
        Page<ExclusivePriceProduct> page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<ExclusivePriceProduct> queryWrapper = new LambdaQueryWrapper<>();
        if (null != paramDto.getMemberId()) {
            queryWrapper.eq(ExclusivePriceProduct::getMemberId, paramDto.getMemberId());
        }
        queryWrapper.eq(null != paramDto.getActiveId(), ExclusivePriceProduct::getActiveId, paramDto.getActiveId());
        queryWrapper.eq(ExclusivePriceProduct::getDelFlag,Boolean.FALSE);
        queryWrapper.orderByAsc(ExclusivePriceProduct::getId);

        page.doSelectPage(() -> this.list(queryWrapper));
        return page;
    }

}
