package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleDetail;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.FlashSaleDetailMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.FlashSaleDetailExtMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FlashSaleDetailCountDto;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@Repository
public class FlashSaleDetailRepository extends ServiceImpl<FlashSaleDetailMapper, FlashSaleDetail> {

    @Resource
    private FlashSaleDetailExtMapper flashSaleDetailExtMapper;

    /**
     * 根据活动ID查询活动明细
     *
     * @param flashSaleId
     * @return
     */
    public List<FlashSaleDetail> getByFlashSaleId(Long flashSaleId) {
        LambdaQueryWrapper<FlashSaleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSaleDetail::getFlashSaleId, flashSaleId);
        queryWrapper.eq(FlashSaleDetail::getDelFlag, Boolean.FALSE);
        return this.list(queryWrapper);
    }

    /**
     * 根据活动ID和skuId查询活动明细
     *
     * @param flashSaleId
     * @param skuId
     * @return
     */
    public FlashSaleDetail getByFlashSaleAndSkuId(Long flashSaleId, String skuId) {
        LambdaQueryWrapper<FlashSaleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSaleDetail::getFlashSaleId, flashSaleId);
        queryWrapper.eq(FlashSaleDetail::getSkuId, skuId);
        queryWrapper.eq(FlashSaleDetail::getDelFlag, Boolean.FALSE);
        return this.getOne(queryWrapper);
    }

    public FlashSaleDetail getByFlashSaleAndSkuIdForceMaster(Long flashSaleId, String skuId) {
        return getByFlashSaleAndSkuId(flashSaleId, skuId);
    }

    /**
     * 通过ID扣减库存
     */
    public int reduceStockByFlashSaleIdAndSkuId(Long id, Integer number) {
        return flashSaleDetailExtMapper.reduceStockByFlashSaleIdAndSkuId(id, number);
    }

    /**
     * 通过活动ID统计库存
     *
     * @param flashSaleIdList
     * @return
     */
    public List<FlashSaleDetailCountDto> countByFlashSaleId(List<Long> flashSaleIdList) {
        return flashSaleDetailExtMapper.countByFlashSaleId(flashSaleIdList);
    }
}
