package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.SendMessageRecordCouponSn;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.SendMessageRecordCouponSnMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.SendMessageRecordCouponSnExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@Repository
@Slf4j
public class SendMessageRecordCouponSnRepository extends ServiceImpl<SendMessageRecordCouponSnMapper, SendMessageRecordCouponSn> {
    @Resource
    private SendMessageRecordCouponSnExtMapper sendMessageRecordCouponSnExtMapper;

    public Double calculateUsageRate(Long msgId){
        return sendMessageRecordCouponSnExtMapper.calculateUsageRate(msgId);
    }

}
