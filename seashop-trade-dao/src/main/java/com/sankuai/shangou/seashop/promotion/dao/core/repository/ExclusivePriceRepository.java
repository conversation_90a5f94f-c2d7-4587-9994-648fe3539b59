package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.promotion.common.enums.ActiveStatusEnum;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePrice;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ExclusivePriceMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.ExclusivePriceExtMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceExtDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceSimpleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@Repository
@Slf4j
public class ExclusivePriceRepository extends ServiceImpl<ExclusivePriceMapper, ExclusivePrice> {

    @Resource
    private ExclusivePriceExtMapper exclusivePriceExtMapper;

    public List<ExclusivePrice> listByParams(ExclusivePriceParamDto paramDto) {
        return exclusivePriceExtMapper.listByParams(paramDto);
    }

    public List<ExclusivePriceExtDto> listExtByParams(ExclusivePriceParamDto paramDto) {
        return exclusivePriceExtMapper.listExtByParams(paramDto);
    }

    public ExclusivePrice selectById(Long id) {
        return this.getById(id);
    }

    public BasePageResp<ExclusivePriceSimpleDto> pageList(BasePageParam paramPage, ExclusivePriceParamDto paramDto) {
        Page page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<ExclusivePrice> queryWrapper = new LambdaQueryWrapper<>();
        if (null != paramDto.getId()) {
            queryWrapper.eq(ExclusivePrice::getId, paramDto.getId());
        }
        if (null != paramDto.getShopId()) {
            queryWrapper.eq(ExclusivePrice::getShopId, paramDto.getShopId());
        }
        if (CollectionUtils.isNotEmpty(paramDto.getShopIdList())) {
            // 外围已经控制了数量，不会超过200个，这里可以不处理
            queryWrapper.in(ExclusivePrice::getShopId, paramDto.getShopIdList());
        }
        if (StringUtils.isNotBlank(paramDto.getName())) {
            queryWrapper.like(ExclusivePrice::getName, paramDto.getName());
        }
        if (null != paramDto.getStartTime()) {
            queryWrapper.ge(ExclusivePrice::getStartTime, paramDto.getStartTime());
        }
        if (null != paramDto.getEndTime()) {
            queryWrapper.le(ExclusivePrice::getEndTime, paramDto.getEndTime());
        }
        if (null != paramDto.getStatus()) {
            Date now = new Date();
            if (paramDto.getStatus().equals(ActiveStatusEnum.NOT_START.getCode())) {
                queryWrapper.ge(ExclusivePrice::getStartTime, now);
                queryWrapper.ge(ExclusivePrice::getEndTime, now);
            } else if (paramDto.getStatus().equals(ActiveStatusEnum.START.getCode())) {
                queryWrapper.le(ExclusivePrice::getStartTime, now);
                queryWrapper.ge(ExclusivePrice::getEndTime, now);
            } else if (paramDto.getStatus().equals(ActiveStatusEnum.END.getCode())) {
                queryWrapper.le(ExclusivePrice::getStartTime, now);
                queryWrapper.le(ExclusivePrice::getEndTime, now);
            }
        }
        queryWrapper.orderByDesc(ExclusivePrice::getId);
        page.doSelectPage(() -> baseMapper.selectList(queryWrapper));
        return PageResultHelper.transfer(page, ExclusivePriceSimpleDto.class);
    }

    /**
     * 查询店铺下的正在进行或者未开始的专享价活动
     */
    public List<ExclusivePrice> listEffectiveByShopId(Long shopId) {
        return exclusivePriceExtMapper.queryAllActiveExclusivePrice(shopId);
    }

    /**
     * 查询店铺下的正在进行或者未开始的专享价活动
     */
    public List<ExclusivePrice> getValidByShopId(Long shopId) {
        LambdaQueryWrapper<ExclusivePrice> queryWrapper = new LambdaQueryWrapper<>();
        Date now = new Date();
        queryWrapper.eq(ExclusivePrice::getShopId, shopId)
                .le(ExclusivePrice::getStartTime, now)
                .ge(ExclusivePrice::getEndTime, now);
        return super.list(queryWrapper);
    }
}
