package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.promotion.common.constant.SortFliedConstant;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSale;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleDetail;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.FlashSaleMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.FlashSaleExtMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FlashSaleParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.MallFlashSaleDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.MallFlashSaleParamDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@Repository
public class FlashSaleRepository extends ServiceImpl<FlashSaleMapper, FlashSale> {


    @Resource
    private FlashSaleExtMapper flashSaleExtMapper;

    public FlashSale selectById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询时间范围内商品是否已经有活动
     */
    public List<FlashSale> listByProductIdAndTime(FlashSaleParamDto paramModel) {
        LambdaQueryWrapper<FlashSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSale::getProductId, paramModel.getProductId());
        // 状态
        queryWrapper.in(FlashSale::getStatus, paramModel.getStatusList());
        // 结束时间大于当前时间
        queryWrapper.gt(FlashSale::getEndDate, new Date());
//        // 开始时间小于结束时间
//        queryWrapper.lt(FlashSale::getBeginDate, paramModel.getEndDate());
//        // 结束时间大于开始时间
//        queryWrapper.gt(FlashSale::getEndDate, paramModel.getBeginDate());
        // 排除的活动ID
        if (paramModel.getExcludeId() != null) {
            queryWrapper.ne(FlashSale::getId, paramModel.getExcludeId());
        }
        return this.list(queryWrapper);
    }

    /**
     * 列表分页查询
     *
     * @param paramPage
     * @param paramDto
     * @return
     */
    public Page<FlashSale> pageList(BasePageParam paramPage, FlashSaleParamDto paramDto, List<FieldSortReq> sortList) {

        Page<FlashSale> page = PageHelper.startPage(paramPage);

        LambdaQueryWrapper<FlashSale> queryWrapper = new LambdaQueryWrapper<>();

        // 商品ID
        if (paramDto.getProductId() != null) {
            queryWrapper.eq(FlashSale::getProductId, paramDto.getProductId());
        }
        if (CollUtil.isNotEmpty(paramDto.getProductIdList())) {
            queryWrapper.in(FlashSale::getProductId, paramDto.getProductIdList());
        }

        // 店铺ID
        if (paramDto.getShopId() != null) {
            queryWrapper.eq(FlashSale::getShopId, paramDto.getShopId());
        }
        if (CollUtil.isNotEmpty(paramDto.getShopIdList())) {
            queryWrapper.in(FlashSale::getShopId, paramDto.getShopIdList());
        }

        // 活动ID
        if (paramDto.getId() != null) {
            queryWrapper.eq(FlashSale::getId, paramDto.getId());
        }

        // 活动名称
        if (StrUtil.isNotBlank(paramDto.getTitle())) {
            queryWrapper.like(FlashSale::getTitle, paramDto.getTitle());
        }

        // 开始时间处理
        if (paramDto.getStartTime() != null && paramDto.getEndTime() != null) {
            queryWrapper.le(FlashSale::getBeginDate, paramDto.getStartTime());
            queryWrapper.ge(FlashSale::getEndDate, paramDto.getEndTime());
        }
        if (paramDto.getStartTime() != null && paramDto.getEndTime() == null) {
            queryWrapper.le(FlashSale::getBeginDate, paramDto.getStartTime());
        }
        if (paramDto.getStartTime() == null && paramDto.getEndTime() != null) {
            queryWrapper.ge(FlashSale::getEndDate, paramDto.getEndTime());
        }

        // 状态处理
        if (paramDto.getStatus() != null) {
            queryWrapper.eq(FlashSale::getStatus, paramDto.getStatus());
        }
        if (CollUtil.isNotEmpty(paramDto.getStatusList())) {
            queryWrapper.in(FlashSale::getStatus, paramDto.getStatusList());
        }

        // 特殊处理了beginDate和endDate
        if (paramDto.getBeginDate() != null && paramDto.getEndDate() != null) {
            // lt<
            // gt>
            queryWrapper.lt(FlashSale::getBeginDate, paramDto.getBeginDate());
            queryWrapper.gt(FlashSale::getEndDate, paramDto.getEndDate());
        }
        if (paramDto.getBeginDate() == null && paramDto.getEndDate() != null) {
            queryWrapper.lt(FlashSale::getEndDate, paramDto.getEndDate());
        }
        if (paramDto.getBeginDate() != null && paramDto.getEndDate() == null) {
            queryWrapper.gt(FlashSale::getBeginDate, paramDto.getBeginDate());
            queryWrapper.gt(FlashSale::getEndDate, paramDto.getBeginDate());
        }
        if (paramDto.getNormalFlag() != null && paramDto.getNormalFlag()) {
            queryWrapper.gt(FlashSale::getEndDate, new Date());
        }
        if (paramDto.getFrontFlag() != null) {
            queryWrapper.eq(FlashSale::getFrontFlag, paramDto.getFrontFlag());
        }

        // 处理排序
        if (CollUtil.isNotEmpty(sortList)) {
            queryWrapper.last("order by " + MybatisUtil.getOrderSql(sortList));
        } else {
            // 默认排序
            queryWrapper.orderByDesc(FlashSale::getId);
            queryWrapper.orderByDesc(FlashSale::getBeginDate);
        }
        if (CollUtil.isNotEmpty(paramDto.getFlashSaleIds())) {
            queryWrapper.in(FlashSale::getId, paramDto.getFlashSaleIds());
        }

        page.doSelectPage(() -> this.list(queryWrapper));

        return page;
    }

    public List<FlashSale> pageListByProductId(FlashSaleParamDto paramDto) {
        LambdaQueryWrapper<FlashSale> queryWrapper = new LambdaQueryWrapper<>();

        // 商品ID
        if (CollUtil.isNotEmpty(paramDto.getProductIdList())) {
            queryWrapper.in(FlashSale::getProductId, paramDto.getProductIdList());
        }

        // 开始时间处理
        if (paramDto.getStartTime() != null && paramDto.getEndTime() != null) {
            queryWrapper.between(FlashSale::getBeginDate, paramDto.getStartTime(), paramDto.getEndTime());
        }
        if (paramDto.getStartTime() != null && paramDto.getEndTime() == null) {
            queryWrapper.ge(FlashSale::getBeginDate, paramDto.getStartTime());
        }
        if (paramDto.getStartTime() == null && paramDto.getEndTime() != null) {
            queryWrapper.le(FlashSale::getBeginDate, paramDto.getEndTime());
        }

        // 状态处理
        if (paramDto.getStatus() != null) {
            queryWrapper.eq(FlashSale::getStatus, paramDto.getStatus());
        }
        if (CollUtil.isNotEmpty(paramDto.getStatusList())) {
            queryWrapper.in(FlashSale::getStatus, paramDto.getStatusList());
        }

        // 特殊处理了beginDate和endDate
        if (paramDto.getBeginDate() != null && paramDto.getEndDate() != null) {
            // lt<
            // gt>
            queryWrapper.lt(FlashSale::getBeginDate, paramDto.getBeginDate());
            queryWrapper.gt(FlashSale::getEndDate, paramDto.getEndDate());
        }
        if (paramDto.getBeginDate() == null && paramDto.getEndDate() != null) {
            queryWrapper.lt(FlashSale::getEndDate, paramDto.getEndDate());
        }
        if (paramDto.getBeginDate() != null && paramDto.getEndDate() == null) {
            queryWrapper.gt(FlashSale::getBeginDate, paramDto.getBeginDate());
            queryWrapper.gt(FlashSale::getEndDate, paramDto.getBeginDate());
        }
        if (paramDto.getNormalFlag() != null && paramDto.getNormalFlag()) {
            queryWrapper.gt(FlashSale::getEndDate, new Date());
        }
        if (paramDto.getFrontFlag() != null) {
            queryWrapper.eq(FlashSale::getFrontFlag, paramDto.getFrontFlag());
        }

        // 默认排序
        queryWrapper.orderByDesc(FlashSale::getId);
        queryWrapper.orderByDesc(FlashSale::getBeginDate);

        return this.list(queryWrapper);
    }

    /**
     * 通过商品ID列表分页查询
     *
     * @param paramPage
     * @param paramDto
     * @return
     */
    public Page<FlashSale> pageListByProductId(BasePageParam paramPage, FlashSaleParamDto paramDto) {

        Page<FlashSale> page = PageHelper.startPage(paramPage);

        List<FlashSale> flashSaleList = MybatisUtil.queryBatch(productIds -> {
            LambdaQueryWrapper<FlashSale> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(FlashSale::getProductId, productIds);
            // 状态处理
            if (paramDto.getStatus() != null) {
                queryWrapper.eq(FlashSale::getStatus, paramDto.getStatus());
            }
            // 特殊处理了beginDate和endDate
            if (paramDto.getBeginDate() != null && paramDto.getEndDate() != null) {
                // lt<
                // gt>
                queryWrapper.lt(FlashSale::getBeginDate, paramDto.getBeginDate());
                queryWrapper.gt(FlashSale::getEndDate, paramDto.getEndDate());
            }
            if (paramDto.getBeginDate() != null && paramDto.getEndDate() == null) {
                queryWrapper.gt(FlashSale::getBeginDate, paramDto.getBeginDate());
                queryWrapper.gt(FlashSale::getEndDate, paramDto.getBeginDate());
            }
            if (paramDto.getBeginDate() == null && paramDto.getEndDate() != null) {
                queryWrapper.lt(FlashSale::getEndDate, paramDto.getEndDate());
            }
            if (paramDto.getNormalFlag() != null && paramDto.getNormalFlag()) {
                queryWrapper.gt(FlashSale::getEndDate, new Date());
            }
            queryWrapper.orderByDesc(FlashSale::getBeginDate);
            return this.list(queryWrapper);
        }, paramDto.getProductIdList());
        if (CollUtil.isEmpty(flashSaleList)) {
            return page;
        }

        // 分割flashSaleList
        List<List<FlashSale>> flashSaleListArr = CollUtil.split(flashSaleList, paramPage.getPageSize());
        int size = flashSaleListArr.size();
        page.setPages(size);
        page.setTotal(flashSaleList.size());
        if (paramPage.getPageNum() < size) {
            page.addAll(flashSaleListArr.get(paramPage.getPageNum() - 1));
        }
        return page;
    }

    /**
     * 商城限时购活动分页查询
     *
     * @param paramPage
     * @param paramDto
     * @return
     */
    public Page<MallFlashSaleDto> mallPageList(BasePageParam paramPage, MallFlashSaleParamDto paramDto) {
        Page<MallFlashSaleDto> page = PageHelper.startPage(paramPage);

        List<FieldSortReq> sortList = paramDto.getSortList();
        if (CollUtil.isNotEmpty(sortList)) {
            for (FieldSortReq sortReq : sortList) {
                if (StrUtil.isNotBlank(sortReq.getSort())) {
                    if (StrUtil.equals(sortReq.getSort(), SortFliedConstant.SALE_COUNT) && !sortReq.getIzAsc()) {
                        paramDto.setSaleCountSort(true);
                    }
                }
            }
        }

        // 商城默认只展示被平台设置展示的
        paramDto.setFrontFlag(Boolean.TRUE);

        page.doSelectPage(() -> flashSaleExtMapper.queryMallFlashSaleList(paramDto));
        return page;
    }

    public FlashSale getByNowTimeAndShopAndProductId(Long shopId, Long productId, Integer status, Boolean frontFlag) {
        Date now = new Date();
        LambdaQueryWrapper<FlashSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSale::getShopId, shopId);
        queryWrapper.eq(FlashSale::getProductId, productId);
        queryWrapper.eq(FlashSale::getStatus, status);
        queryWrapper.le(FlashSale::getBeginDate, now);
        queryWrapper.ge(FlashSale::getEndDate, now);
        queryWrapper.eq(FlashSale::getFrontFlag, frontFlag);
        return this.getOne(queryWrapper);
    }

    /**
     * 查询最近即将开始的限时购活动
     */

    public FlashSale getNearlyBeginByShopId(Long shopId, Long productId, Integer status, Boolean frontFlag) {
        LambdaQueryWrapper<FlashSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlashSale::getShopId, shopId);
        queryWrapper.eq(FlashSale::getProductId, productId);
        queryWrapper.eq(FlashSale::getStatus, status);
        queryWrapper.ge(FlashSale::getBeginDate, new Date());
        queryWrapper.eq(FlashSale::getFrontFlag, frontFlag);
        queryWrapper.orderByAsc(FlashSale::getBeginDate);
        return this.getOne(queryWrapper);
    }


    /**
     * 查询店铺下的正在进行或者未开始的限时购活动
     */
    public List<FlashSale> listEffectiveByShopId(Long shopId) {
        return flashSaleExtMapper.queryAllActiveFlashSale(shopId, null);
    }

    /**
     * 查询某个类型的正在进行或者未开始的限时购活动
     */
    public List<FlashSale> listEffectiveByCategoryId(Long categoryId) {
        return flashSaleExtMapper.queryAllActiveFlashSale(null, categoryId);
    }

    /**
     * 通过明细查询当前正在进行的活动
     */
    public FlashSale getActiveByDetailParam(FlashSaleDetail detail) {
        return flashSaleExtMapper.queryActiveByDetailParam(detail);
    }

    /**
     * 通过活动ID增加销量
     *
     * @param id
     * @param number
     */
    public int updateSaleCountById(Long id, Integer number) {
        return flashSaleExtMapper.updateSaleCountById(id, number);
    }

    public List<FlashSale> getByIds(List<Long> flashSaleIds) {
        if (CollectionUtils.isEmpty(flashSaleIds)) {
            return Collections.emptyList();
        }

        flashSaleIds = flashSaleIds.stream().distinct().collect(Collectors.toList());
        return MybatisUtil.queryBatch(ids -> listByIds(ids), flashSaleIds);
    }

    public FlashSale getByIdForceMaster(Long id) {
        return this.getById(id);
    }
}
