package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponSetting;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CouponSettingMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponSettingParamDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@Repository
@Slf4j
public class CouponSettingRepository extends ServiceImpl<CouponSettingMapper, CouponSetting> {

    @Resource
    private CouponSettingMapper couponSettingMapper;

    public void deleteByCouponId(Long couponId) {
        log.info("deleteByCouponId-couponId:{}", couponId);
        LambdaQueryWrapper<CouponSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CouponSetting::getCouponId, couponId);
        couponSettingMapper.delete(queryWrapper);
    }

    public List<CouponSetting> listByExample(CouponSettingParamDto paramDto) {
        LambdaQueryWrapper<CouponSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(null != paramDto.getCouponId(), CouponSetting::getCouponId, paramDto.getCouponId());
        queryWrapper.orderByAsc(CouponSetting::getId);
        return couponSettingMapper.selectList(queryWrapper);
    }
}
