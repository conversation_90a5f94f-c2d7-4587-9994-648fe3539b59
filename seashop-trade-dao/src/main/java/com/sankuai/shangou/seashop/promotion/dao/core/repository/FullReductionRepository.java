package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.SortFliedConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.ActiveStatusEnum;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FullReduction;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.FullReductionMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.FullReductionExtMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionSimpleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@Repository
@Slf4j
public class FullReductionRepository extends ServiceImpl<FullReductionMapper, FullReduction> {

    @Resource
    private FullReductionExtMapper fullReductionExtMapper;

    public FullReduction selectById(Long id) {
        return this.getById(id);
    }

    public int countByTime(FullReductionParamDto paramDto) {
        LambdaQueryWrapper<FullReduction> queryWrapper = new LambdaQueryWrapper<>();
        if (null != paramDto.getShopId()) {
            queryWrapper.eq(FullReduction::getShopId, paramDto.getShopId());
        }
        if (null != paramDto.getNotEqId()) {
            queryWrapper.ne(FullReduction::getId, paramDto.getNotEqId());
        }
        if (null != paramDto.getStartTime() && null != paramDto.getEndTime()) {
            queryWrapper.le(FullReduction::getStartTime, paramDto.getEndTime());
            queryWrapper.ge(FullReduction::getEndTime, paramDto.getStartTime());
        }
        // 排除掉已经结束的活动
        queryWrapper.ge(FullReduction::getEndTime, new Date());
        return Math.toIntExact(this.count(queryWrapper));
    }

    /**
     * 判断时间段内是否有活动
     *
     * @param paramDto
     * @return
     */
    public int hasActive(FullReductionParamDto paramDto) {
        LambdaQueryWrapper<FullReduction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FullReduction::getShopId, paramDto.getShopId());
        if (null != paramDto.getNotEqId()) {
            queryWrapper.ne(FullReduction::getId, paramDto.getNotEqId());
        }
        queryWrapper.le(FullReduction::getStartTime, paramDto.getEndTime());
        queryWrapper.ge(FullReduction::getEndTime, paramDto.getStartTime());
        return Math.toIntExact(this.count(queryWrapper));
    }

    public List<FullReduction> listByTimeAndShopIdList(FullReductionParamDto paramDto) {
        LambdaQueryWrapper<FullReduction> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(paramDto.getShopIdList())) {
            // 外面已经做了200数量的校验
            queryWrapper.in(FullReduction::getShopId, paramDto.getShopIdList());
        }
        if (null != paramDto.getBetweenTime()) {
            queryWrapper.lt(FullReduction::getStartTime, paramDto.getBetweenTime());
            queryWrapper.gt(FullReduction::getEndTime, paramDto.getBetweenTime());
        }
        return this.list(queryWrapper);
    }

    public BasePageResp<FullReductionSimpleDto> pageList(BasePageParam paramPage, FullReductionParamDto paramDto) {
        Page page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<FullReduction> queryWrapper = new LambdaQueryWrapper<>();
        if (null != paramDto.getId()) {
            queryWrapper.eq(FullReduction::getId, paramDto.getId());
        }
        if (null != paramDto.getShopId()) {
            queryWrapper.eq(FullReduction::getShopId, paramDto.getShopId());
        }
        if (StringUtils.isNotBlank(paramDto.getShopName())) {
            queryWrapper.like(FullReduction::getShopName, paramDto.getShopName());
        }
        if (StringUtils.isNotBlank(paramDto.getActiveName())) {
            queryWrapper.like(FullReduction::getActiveName, paramDto.getActiveName());
        }
        if (null != paramDto.getStartTime()) {
            queryWrapper.ge(FullReduction::getStartTime, DateUtil.beginOfDay(paramDto.getStartTime()));
        }
        if (null != paramDto.getEndTime()) {
            queryWrapper.le(FullReduction::getEndTime, DateUtil.endOfDay(paramDto.getEndTime()));
        }
        if (null != paramDto.getStatus()) {
            Date now = new Date();
            if (paramDto.getStatus().equals(ActiveStatusEnum.NOT_START.getCode())) {
                queryWrapper.ge(FullReduction::getStartTime, now);
                queryWrapper.ge(FullReduction::getEndTime, now);
            } else if (paramDto.getStatus().equals(ActiveStatusEnum.START.getCode())) {
                queryWrapper.le(FullReduction::getStartTime, now);
                queryWrapper.ge(FullReduction::getEndTime, now);
            } else if (paramDto.getStatus().equals(ActiveStatusEnum.END.getCode())) {
                queryWrapper.le(FullReduction::getStartTime, now);
                queryWrapper.le(FullReduction::getEndTime, now);
            }
        }
        List<FieldSortReq> sortList = paramDto.getSortList();
        if (CollectionUtils.isNotEmpty(sortList)) {
            for (FieldSortReq fieldSortReq : sortList) {
                if (SortFliedConstant.START_TIME.equalsIgnoreCase(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), FullReduction::getStartTime);
                }
                if (SortFliedConstant.END_TIME.equalsIgnoreCase(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), FullReduction::getEndTime);
                }
            }
        } else {
            queryWrapper.orderByDesc(FullReduction::getEndTime, FullReduction::getStartTime);
        }
        queryWrapper.orderByDesc(FullReduction::getId);
        page.doSelectPage(() -> baseMapper.selectList(queryWrapper));
        return PageResultHelper.transfer(page, FullReductionSimpleDto.class);
    }

    public FullReduction getByNowAndShopId(Long shopId) {
        Date now = new Date();
        LambdaQueryWrapper<FullReduction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FullReduction::getShopId, shopId);
        queryWrapper.le(FullReduction::getStartTime, now);
        queryWrapper.ge(FullReduction::getEndTime, now);
        return this.getOne(queryWrapper);
    }

    /**
     * 查询店铺下的正在进行或者未开始的满减活动
     */
    public List<FullReduction> listEffectiveByShopId(Long shopId) {
        return fullReductionExtMapper.queryAllActiveFullReduction(shopId);
    }

    /**
     * 获取当前达到门槛的营销
     *
     * @return
     */
    public FullReduction currentEnableFullReduction() {
        return fullReductionExtMapper.currentEnableFullReduction();
    }
}
