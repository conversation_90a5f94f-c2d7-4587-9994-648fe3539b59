package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.SortFliedConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.ActiveStatusEnum;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActive;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActiveProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.DiscountActiveMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.DiscountActiveExtMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveDetailDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveSimpleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@Repository
@Slf4j
public class DiscountActiveRepository extends ServiceImpl<DiscountActiveMapper, DiscountActive> {

    @Resource
    private DiscountActiveMapper discountActiveMapper;
    @Resource
    private DiscountActiveExtMapper discountActiveExtMapper;

    public List<DiscountActive> selectProductInActive(DiscountActiveParamDto paramDto) {
        return discountActiveExtMapper.selectProductInActive(paramDto);
    }

    public List<DiscountActiveDetailDto> selectProductInActiveDetail(DiscountActiveParamDto paramDto) {
        return discountActiveExtMapper.selectProductInActiveDetail(paramDto);
    }

    /**
     * 通过ID查询单条数据
     */
    public DiscountActive selectById(Long id) {
        return discountActiveMapper.selectById(id);
    }

    public BasePageResp<DiscountActiveSimpleDto> pageList(BasePageParam paramPage, DiscountActiveParamDto paramDto) {
        Page page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<DiscountActive> queryWrapper = new LambdaQueryWrapper<>();
        if (null != paramDto.getId()) {
            queryWrapper.eq(DiscountActive::getId, paramDto.getId());
        }
        if (null != paramDto.getShopId()) {
            queryWrapper.eq(DiscountActive::getShopId, paramDto.getShopId());
        }
        if (CollectionUtils.isNotEmpty(paramDto.getShopIdList())) {
            queryWrapper.in(DiscountActive::getShopId, paramDto.getShopIdList());
        }
        if (StringUtils.isNotBlank(paramDto.getActiveName())) {
            queryWrapper.like(DiscountActive::getActiveName, paramDto.getActiveName());
        }
        if (null != paramDto.getStartTime()) {
            queryWrapper.ge(DiscountActive::getStartTime, paramDto.getStartTime());
        }
        if (null != paramDto.getEndTime()) {
            queryWrapper.le(DiscountActive::getEndTime, paramDto.getEndTime());
        }
        if (null != paramDto.getStatus()) {
            Date now = new Date();
            if (paramDto.getStatus().equals(ActiveStatusEnum.NOT_START.getCode())) {
                queryWrapper.ge(DiscountActive::getStartTime, now);
                queryWrapper.ge(DiscountActive::getEndTime, now);
            } else if (paramDto.getStatus().equals(ActiveStatusEnum.START.getCode())) {
                queryWrapper.le(DiscountActive::getStartTime, now);
                queryWrapper.ge(DiscountActive::getEndTime, now);
            } else if (paramDto.getStatus().equals(ActiveStatusEnum.END.getCode())) {
                queryWrapper.le(DiscountActive::getStartTime, now);
                queryWrapper.le(DiscountActive::getEndTime, now);
            }
        }
        List<FieldSortReq> sortList = paramDto.getSortList();
        if (CollectionUtils.isNotEmpty(sortList)) {
            for (FieldSortReq fieldSortReq : sortList) {
                if (SortFliedConstant.START_TIME.equalsIgnoreCase(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), DiscountActive::getStartTime);
                }
                if (SortFliedConstant.END_TIME.equalsIgnoreCase(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), DiscountActive::getEndTime);
                }
            }
        }
        queryWrapper.orderByDesc(DiscountActive::getId);
        page.doSelectPage(() -> discountActiveMapper.selectList(queryWrapper));
        return PageResultHelper.transfer(page, DiscountActiveSimpleDto.class);
    }

    public List<DiscountActive> listByExample(DiscountActiveParamDto paramDto) {
        LambdaQueryWrapper<DiscountActive> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(paramDto.getShopIdList())) {
            // 入口做了数量限制
            queryWrapper.in(DiscountActive::getShopId, paramDto.getShopIdList());
        }
        if (null != paramDto.getBetweenTime()) {
            queryWrapper.lt(DiscountActive::getStartTime, paramDto.getBetweenTime());
            queryWrapper.gt(DiscountActive::getEndTime, paramDto.getBetweenTime());
        }
        return this.list(queryWrapper);
    }

    /**
     * 查询店铺下的正在进行或者未开始的折扣活动
     */
    public List<DiscountActive> listEffectiveByShopId(Long shopId) {
        return discountActiveExtMapper.queryAllActiveDiscountActive(shopId);
    }

        public DiscountActive selectCurrentEnableIzAllProduct() {
        return discountActiveExtMapper.selectCurrentEnableIzAllProduct();
    }

    public List<DiscountActiveProduct> selectCurrentByProductId(Collection<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        return discountActiveExtMapper.selectCurrentByProductId(productIds);
    }
}
