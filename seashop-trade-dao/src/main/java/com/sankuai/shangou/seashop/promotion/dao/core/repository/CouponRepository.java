package com.sankuai.shangou.seashop.promotion.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.promotion.common.constant.SortFliedConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.ActiveStatusEnum;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.Coupon;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.CouponMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.CouponExtMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponExtDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponSimpleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@Repository
@Slf4j
public class CouponRepository extends ServiceImpl<CouponMapper, Coupon> {

    @Resource
    private CouponMapper couponMapper;
    @Resource
    private CouponExtMapper couponExtMapper;

    public Coupon findById(Long id) {
        return couponMapper.selectById(id);
    }

    /**
     * 通过ID查询优惠券活动信息（切面使用）
     *
     * @param id
     * @return
     */
    public Coupon selectById(Long id) {
        return couponMapper.selectById(id);
    }

    public BasePageResp<CouponSimpleDto> pageList(BasePageParam paramPage, CouponParamDto paramDto) {
        Page page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<Coupon> queryWrapper = new LambdaQueryWrapper<>();
        if (null != paramDto.getCouponId()) {
            queryWrapper.eq(Coupon::getId, paramDto.getCouponId());
        }
        if (null != paramDto.getShopId()) {
            queryWrapper.eq(Coupon::getShopId, paramDto.getShopId());
        }
        if (null != paramDto.getShopIdList()) {
            queryWrapper.in(Coupon::getShopId, paramDto.getShopIdList());
        }
        if (StringUtils.isNotBlank(paramDto.getShopName())) {
            queryWrapper.like(Coupon::getShopName, paramDto.getShopName());
        }
        if (StringUtils.isNotBlank(paramDto.getCouponName())) {
            queryWrapper.like(Coupon::getCouponName, paramDto.getCouponName());
        }
        if (null != paramDto.getStatus()) {
            Date now = new Date();
            if (paramDto.getStatus().equals(ActiveStatusEnum.NOT_START.getCode())) {
                queryWrapper.ge(Coupon::getStartTime, now);
                queryWrapper.ge(Coupon::getEndTime, now);
            }
            else if (paramDto.getStatus().equals(ActiveStatusEnum.START.getCode())) {
                queryWrapper.le(Coupon::getStartTime, now);
                queryWrapper.ge(Coupon::getEndTime, now);
            }
            else if (paramDto.getStatus().equals(ActiveStatusEnum.END.getCode())) {
                queryWrapper.le(Coupon::getStartTime, now);
                queryWrapper.le(Coupon::getEndTime, now);
            }
        }
        if (null != paramDto.getClaimable() && paramDto.getClaimable()) {
            // 包含未开始和进行中的数据
            Date now = new Date();
            queryWrapper.ge(Coupon::getEndTime, now);
            queryWrapper.apply(" num > receive_num ");
        }
        if (paramDto.getHasStock() != null && paramDto.getHasStock()) {
            queryWrapper.apply(" num > receive_num ");
        }
        if (CollectionUtils.isNotEmpty(paramDto.getReceiveTypeList())) {
            queryWrapper.in(Coupon::getReceiveType, paramDto.getReceiveTypeList());
        }

        List<FieldSortReq> sortList = paramDto.getSortList();
        if (CollectionUtils.isNotEmpty(sortList)) {
            for (FieldSortReq fieldSortReq : sortList) {
                if (SortFliedConstant.START_TIME.equalsIgnoreCase(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), Coupon::getStartTime);
                }
                if (SortFliedConstant.END_TIME.equalsIgnoreCase(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), Coupon::getEndTime);
                }
                if (SortFliedConstant.PRICE.equals(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), Coupon::getPrice);
                }
                if (SortFliedConstant.CREATED_TIME.equalsIgnoreCase(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), Coupon::getCreateTime);
                }
            }
        }
        queryWrapper.orderByDesc(Coupon::getId);
        page.doSelectPage(() -> couponMapper.selectList(queryWrapper));
        return PageResultHelper.transfer(page, CouponSimpleDto.class);
    }

    /**
     * 减少库存
     */
    public int reduceStock(Long id) {
        return couponExtMapper.reduceStock(id);
    }

    public int receiveCoupon(Long id, Integer receiveNum) {
        return couponExtMapper.receiveCoupon(id, receiveNum);
    }

    /**
     * 通过ID列表查询当前有效优惠券活动信息（在当前时间范围内）
     *
     * @param idList
     * @return
     */
    public List<Coupon> queryEffectiveByIdList(List<Long> idList) {
        Date now = new Date();
        return MybatisUtil.queryBatch(ids -> {
            LambdaQueryWrapper<Coupon> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Coupon::getId, ids);
            // 时间在当前时间范围内
            queryWrapper.le(Coupon::getStartTime, now);
            queryWrapper.ge(Coupon::getEndTime, now);
            return this.list(queryWrapper);
        }, idList);
    }

    /**
     * 根据商品id查询优惠券
     *
     * @param productId
     * @param shopId
     * @return
     */
    public List<Coupon> selectByProductId(Long productId, Long shopId) {
        return couponExtMapper.selectByProductId(productId, shopId);
    }

    /**
     * 查询店铺下的可用优惠券（正在进行的）
     *
     * @param shopIds
     * @return
     */
    public List<CouponExtDto> selectAvailableList(List<Long> shopIds, List<Long> productIds) {
        return couponExtMapper.selectAvailableList(shopIds, productIds);
    }

    /**
     * 查询店铺下的正在进行或者未开始的优惠券活动
     */
    public List<Coupon> listEffectiveByShopId(Long shopId) {
        return couponExtMapper.queryAllActiveCoupon(shopId);
    }

    /**
     * 查询优惠券信息(强制走主)
     *
     * @param ids 优惠券id列表
     * @return 优惠券列表
     */
    public List<Coupon> listByIdForceMaster(List<Long> ids) {
        return couponMapper.selectBatchIds(ids);
    }
}
