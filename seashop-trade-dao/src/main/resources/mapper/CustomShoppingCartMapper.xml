<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.trade.dao.core.mapper.CustomShoppingCartMapper">

  <update id="increaseSkuQuantity">
    update trade_shopping_cart set quantity = quantity + #{quantity} where id = #{id}
  </update>

  <update id="increaseSkuQuantityAndOverSelect">
    update trade_shopping_cart
    <set>
      quantity = quantity + #{quantity}
      <if test="selected != null">
            ,whether_select = #{selected}
      </if>
    </set>
    where id = #{id}
  </update>

  <update id="updateSkuQuantity">
    update trade_shopping_cart set quantity = #{quantity} where id = #{id}
  </update>

  <update id="updateWhetherSelect">
    update trade_shopping_cart set whether_select=#{selected} where id = #{id}
  </update>
</mapper>