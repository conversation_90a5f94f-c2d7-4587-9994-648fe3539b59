<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.FlashSaleDetailExtMapper">

    <update id="reduceStockByFlashSaleIdAndSkuId">
        update flash_sale_detail set total_count = total_count - #{number} where id = #{id} and total_count >= #{number}
    </update>

    <select id="countByFlashSaleId"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.FlashSaleDetailCountDto">
        SELECT
            flash_sale_id as flashSaleId,
            sum( total_count ) as totalCount
        FROM
            flash_sale_detail
        WHERE
            flash_sale_id in
            <foreach collection="flashSaleIdList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        GROUP BY
            flash_sale_id
    </select>

</mapper>
