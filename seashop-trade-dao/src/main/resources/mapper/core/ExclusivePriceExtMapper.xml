<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.ExclusivePriceExtMapper">


    <select id="listByParams"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePrice">
        SELECT DISTINCT
            t1.id,
            t1.`name`,
            t1.shop_id,
            t1.start_time,
            t1.end_time,
            t1.member_count,
            t1.product_count
        FROM
            exclusive_price t1
                INNER JOIN exclusive_price_product t2 ON t1.id = t2.active_id
        WHERE
            t1.end_time > now()
            and t1.start_time <![CDATA[<]]> t1.end_time
            <if test = " shopId != null ">
                and t1.shop_id = #{shopId}
            </if>
            <if test = " shopIdList != null and shopIdList.size() > 0 ">
                and t1.shop_id in
                <foreach collection="shopIdList" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test = " betweenTime != null ">
                and t1.start_time <![CDATA[<]]> #{betweenTime}
                and t1.end_time <![CDATA[>]]> #{betweenTime}
            </if>
            <if test = " notEqId != null ">
                and t1.id <![CDATA[<>]]> #{notEqId}
            </if>
            <if test = " productIds != null and productIds.size() > 0 ">
                and t2.product_id in
                <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                    #{productId}
                </foreach>
            </if>
            <if test = " memberId != null ">
                and t2.member_id = #{memberId}
            </if>
            /*取交集的算法*/
            <if test = " endTime != null">
                and t1.start_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test = "startTime != null">
                and t1.end_time <![CDATA[>=]]> #{startTime}
            </if>
    </select>

    <select id="queryAllActiveExclusivePrice"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePrice"
            parameterType="java.lang.Long">
        SELECT
         *
        FROM
        exclusive_price
        WHERE
        start_time <![CDATA[<]]> end_time AND end_time <![CDATA[>]]> NOW()
        <if test=" shopId != null ">
            AND shop_id = #{shopId}
        </if>
    </select>

    <select id="listExtByParams"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceExtDto">
        SELECT DISTINCT
        t1.id,
        t1.`name`,
        t1.shop_id,
        t1.start_time,
        t1.end_time,
        t1.member_count,
        t1.product_count,
        t2.product_id
        FROM
            exclusive_price t1
            INNER JOIN exclusive_price_product t2 ON t1.id = t2.active_id
        WHERE
            t1.end_time > now()
            and t1.start_time <![CDATA[<]]> t1.end_time
            <if test = " shopId != null ">
                and t1.shop_id = #{shopId}
            </if>
            <if test = " shopIdList != null and shopIdList.size() > 0 ">
                and t1.shop_id in
                <foreach collection="shopIdList" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test = " betweenTime != null ">
                and t1.start_time <![CDATA[<]]> #{betweenTime}
                and t1.end_time <![CDATA[>]]> #{betweenTime}
            </if>
            <if test = " notEqId != null ">
                and t1.id <![CDATA[<>]]> #{notEqId}
            </if>
            <if test = " productIds != null and productIds.size() > 0 ">
                and t2.product_id in
                <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                    #{productId}
                </foreach>
            </if>
            <if test = " memberId != null ">
                and t2.member_id = #{memberId}
            </if>
            /*取交集的算法*/
            <if test = " endTime != null">
                and t1.start_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test = "startTime != null">
                and t1.end_time <![CDATA[>=]]> #{startTime}
            </if>
    </select>
</mapper>
