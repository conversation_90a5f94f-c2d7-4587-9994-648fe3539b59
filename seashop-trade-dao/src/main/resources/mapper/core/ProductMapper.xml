<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.product.dao.core.mapper.ProductMapper">

    <update id="addSaleCount">
        UPDATE product
        SET sale_counts = sale_counts + #{param.addSaleCount}
        WHERE product_id = #{param.productId}
    </update>

    <select id="getShopSaleCounts"
            resultType="com.sankuai.shangou.seashop.product.dao.core.model.ShopSaleCountsDto">
        SELECT
            #{shopId} `shopId`,
            IFNULL( SUM( sale_counts ), 0 ) `saleCounts`,
            IFNULL( SUM( virtual_sale_counts ), 0 ) `virtualSaleCounts`
        FROM
            product
        WHERE
            shop_id = #{shopId}
        AND whether_delete = 0
    </select>

    <select id="countBySpecNameId" resultType="integer">
        SELECT count(*) FROM product WHERE FIND_IN_SET(spec_name_ids, #{nameId}) AND whether_delete = 0
    </select>

</mapper>
