<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.product.dao.core.mapper.FirstPageMapper">

    <select id="queryProductsCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT count( 1 ) AS productsCount FROM product where 1=1
        <if test="shopId != null">
            AND shop_id = #{shopId}
        </if>
        AND whether_delete = 0
    </select>

    <select id="queryProductsInDraft" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT count( 1 ) AS productsInDraft FROM product WHERE shop_id = #{shopId} AND sale_status = 3 AND whether_delete = 0
    </select>

    <select id="queryOnSaleProducts" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT
        count( 1 ) AS onSaleProducts
        FROM
        product
        WHERE
        1=1
        <if test="shopId != null">
            AND shop_id = #{shopId}
        </if>
        AND sale_status = 1 AND audit_status = 2
        AND whether_delete = 0
    </select>

    <select id="queryInfractionSaleOffProducts" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT count( 1 ) AS infractionSaleOffProducts FROM product WHERE shop_id = #{shopId} AND audit_status = 4 AND whether_delete = 0
    </select>

    <select id="queryInStockProducts" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT count( 1 ) AS inStockProducts FROM product WHERE shop_id = #{shopId} AND sale_status = 2 AND whether_delete = 0
    </select>

    <select id="queryWaitForAuditingProducts" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT
        count( 1 ) AS waitForAuditingProducts
        FROM
        product_audit
        WHERE 1=1
        <if test="shopId != null">
            AND shop_id = #{shopId}
        </if>
        AND whether_delete = 0
        AND audit_status = 1
    </select>

    <select id="queryAuditFailureProducts" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT count( 1 ) AS auditFailureProducts FROM product_audit WHERE shop_id = #{shopId} AND whether_delete = 0 AND audit_status = 3
    </select>

    <select id="queryOverSafeStockProducts" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT
            count( 1 ) AS overSafeStockProducts
        FROM
            product pi
                INNER JOIN sku_stock si ON si.product_id = pi.id
                AND si.Safe_Stock >= si.Stock
        WHERE
            pi.shop_id = #{shopId}
          AND pi.whether_delete = 0
          AND pi.sale_status = 2
          AND pi.audit_status = 2
    </select>

    <select id="queryProductsBrands" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select
        count(1) as productsBrands
        from brand_apply
        where 1=1
        <if test="shopId != null">
            AND shop_id = #{shopId}
        </if>
        and audit_status = 0
    </select>

</mapper>
