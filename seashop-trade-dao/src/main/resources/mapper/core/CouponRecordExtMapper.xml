<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.CouponRecordExtMapper">


    <select id="pageList" resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordSimpleDto"
            parameterType="com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordParamDto">

        SELECT
            t1.id,
            t1.coupon_id,
            t2.coupon_name,
            t1.coupon_sn,
            t1.coupon_status,
            t1.coupon_time,
            t1.used_time,
            t1.order_id,
            t1.shop_id,
            t1.shop_name,
            t1.user_id,
            t1.user_name,
            t2.create_time,
            t2.start_time,
            t2.end_time,
            t2.price,
            t2.order_amount,
            t2.remark,
            t2.use_area
        FROM
            coupon_record t1
                INNER JOIN coupon t2 on t1.coupon_id = t2.id
        WHERE
            1 = 1
            <if test = " param.couponId != null ">
                AND t1.coupon_id = #{param.couponId}
            </if>
            <if test = " param.userId != null ">
                AND t1.user_id = #{param.userId}
            </if>
            <if test = " param.status != null">
                <if test = " param.status == 0 ">
                    AND t1.coupon_status = 0
--                     AND t2.start_time <![CDATA[<]]> now()
                    AND t2.end_time <![CDATA[>]]> now()
                </if>
                <if test = " param.status == 1 ">
                    AND t1.coupon_status = 1
                </if>
                <if test = " param.status == 2 ">
                    AND t1.coupon_status = 0
                    AND t2.end_time <![CDATA[<]]> now()
                </if>
                <if test = " param.status == 10 ">
                    AND t1.coupon_status = 0
                    AND t2.start_time <![CDATA[<]]> now()
                    AND t2.end_time <![CDATA[>]]> now()
                </if>
            </if>
            <if test = " param.shopId != null ">
                AND t2.shop_id = #{param.shopId}
            </if>
            <if test=" param.shopIds != null and param.shopIds.size() > 0">
                AND t2.shop_id in
                <foreach collection="param.shopIds" item="shopId" open="(" close=")" separator=",">
                    #{shopId}
                </foreach>
            </if>
            <if test = " param.shopName != null and param.shopName != '' ">
                AND t2.shop_name like concat('%',#{param.shopName},'%')
            </if>
            <if test = " param.couponName != null and param.couponName != '' ">
                AND t2.coupon_name like concat('%',#{param.couponName},'%')
            </if>
            <if test = " param.orderId != null and param.orderId != '' ">
                AND t1.order_id like concat('%',#{param.orderId},'%')
            </if>
            <if test = " param.userName != null and param.userName != '' ">
                AND t1.user_name like concat('%',#{param.userName},'%')
            </if>
            <if test = " param.startReceiveTime != null and param.endReceiveTime != null">
                AND t1.coupon_time between #{param.startReceiveTime} and #{param.endReceiveTime}
            </if>
            <if test=" param.startReceiveTime != null and param.endReceiveTime == null">
                AND t1.coupon_time <![CDATA[>=]]> #{param.startReceiveTime}
            </if>
            <if test=" param.startReceiveTime == null and param.endReceiveTime != null">
                AND t1.coupon_time <![CDATA[<=]]> #{param.endReceiveTime}
            </if>
            <if test = " param.startUseTime != null and param.endUseTime != null">
                AND t1.used_time between #{param.startUseTime} and #{param.endUseTime}
            </if>
            <if test=" param.startUseTime != null and param.endUseTime == null">
                AND t1.used_time <![CDATA[>=]]> #{param.startUseTime}
            </if>
            <if test=" param.startUseTime == null and param.endUseTime != null">
                AND t1.used_time <![CDATA[<=]]> #{param.endUseTime}
            </if>
            <if test = " param.sortSql != null and param.sortSql != ''">
                order by ${param.sortSql}
            </if>
    </select>
    <select id="queryListByIdAndUserId"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponRecord">
        SELECT
            t1.*
        FROM
            coupon_record t1
                INNER JOIN coupon t2 ON t1.coupon_id = t2.id
        where
            t1.user_id = #{userId}
            and
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=" or ">
                (t1.id = #{item.couponRecordId} and t2.shop_id = #{item.shopId})
            </foreach>

    </select>
</mapper>