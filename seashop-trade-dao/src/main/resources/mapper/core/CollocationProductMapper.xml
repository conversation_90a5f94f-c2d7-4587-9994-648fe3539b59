<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.CollocationProductMapper">

    <select id="listEffectiveByShopId"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationProduct">
        select t1.* from collocation_product t1
        INNER JOIN collocation t2 on t1.collo_id = t2.id
        where t2.start_time <![CDATA[<]]> t2.end_time and t2.end_time <![CDATA[>]]> now()
        <if test = " shopId != null ">
            AND shop_id = #{shopId}
        </if>
    </select>

</mapper>
