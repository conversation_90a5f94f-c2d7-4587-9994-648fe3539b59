<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.product.dao.core.mapper.ext.CategoryExtMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        c1.id, c1.name, c1.icon, c1.display_sequence, c1.parent_category_id, c1.depth,  c1.has_children, c1.commission_rate,  c1.whether_delete, c1.whether_show, c1.custom_form_id, c1.default_status, c1.create_time, c1.update_time, c1.create_user, c1.update_user
    </sql>
    <select id="selectAllCategoryPath"
            resultType="com.sankuai.shangou.seashop.product.dao.core.domain.Category">
        SELECT
        <include refid="Base_Column_List"/>,CONCAT(c3.name,'>',c2.name,'>',c1.name) as path
        FROM category c1
        left join category c2 on c1.parent_category_id = c2.id
        left join category c3 on c2.parent_category_id = c3.id
        WHERE c1.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>
