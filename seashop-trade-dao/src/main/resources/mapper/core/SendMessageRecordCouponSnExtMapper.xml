<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.SendMessageRecordCouponSnExtMapper">

    <select id="calculateUsageRate" resultType="java.lang.Double">
        select (sum(if(cr.coupon_status = 1,1,0))/count(1))*100
        from send_message_record_coupon_sn smrcs
                 left join coupon_record cr on smrcs.coupon_sn = cr.coupon_sn
        where smrcs.message_id = #{msgId}
    </select>

</mapper>
