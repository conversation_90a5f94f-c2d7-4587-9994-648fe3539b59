<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.product.dao.core.mapper.SpecValueMapper">
    <insert id="insertBatchSomeColumn" parameterType="java.util.List">
        INSERT INTO spec_value (shop_id,name_id, value)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.shopId},#{item.nameId}, #{item.value})
        </foreach>
    </insert>
</mapper>
