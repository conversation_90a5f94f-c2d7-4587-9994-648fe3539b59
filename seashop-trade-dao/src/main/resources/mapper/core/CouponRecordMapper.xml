<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.CouponRecordMapper">

    <select id="queryAvailableCouponCountByUser" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            coupon_record cri
                INNER JOIN coupon ci ON cri.coupon_Id = ci.Id
                AND ci.end_time > NOW()
        WHERE
            cri.user_id = #{userId}
          AND cri.coupon_status = '0'
    </select>
</mapper>
