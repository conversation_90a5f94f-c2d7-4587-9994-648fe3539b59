<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.FullReductionExtMapper">


    <select id="queryAllActiveFullReduction"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.FullReduction"
            parameterType="java.lang.Long">
        SELECT
        *
        FROM
        full_reduction
        WHERE
        start_time <![CDATA[<]]> end_time AND end_time <![CDATA[>]]> NOW()
        <if test=" shopId != null ">
            AND shop_id = #{shopId}
        </if>

    </select>

    <select id="currentEnableFullReduction"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.FullReduction">
        SELECT *
        FROM full_reduction
        WHERE
          -- 当前是启用状态
            active_status = 0
          -- 当前正在进行中的活动
          and start_time <![CDATA[<=]]> NOW()
          AND end_time <![CDATA[>=]]> NOW()
        order by update_time desc limit 1
    </select>
</mapper>
