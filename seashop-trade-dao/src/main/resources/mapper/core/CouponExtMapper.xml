<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.CouponExtMapper">

    <update id="reduceStock" parameterType="java.lang.Long">
        update coupon
        set
            receive_num = receive_num + 1
        where
            id = #{couponId}
            and num - receive_num > 0
    </update>

    <update id="receiveCoupon">
        update coupon
        set
            receive_num = receive_num + #{receiveNum}
        where
            id = #{couponId}
            and num - receive_num >= #{receiveNum}
    </update>
    <select id="selectByProductId" resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.Coupon">
        SELECT
            DISTINCT t1.*
        FROM
            coupon t1
                LEFT JOIN coupon_product t2 ON t1.id = t2.coupon_id
        WHERE
            t1.shop_id = #{shopId}
          AND NOW() BETWEEN t1.start_time
            AND t1.end_time
          AND (
                    t1.use_area = 0
                OR ( t1.use_area = 1 AND t2.product_id = #{productId} )
            )
        order by t1.price desc
    </select>

    <select id="queryAllActiveCoupon" resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.Coupon"
            parameterType="java.lang.Long">
        SELECT
            *
        FROM
        coupon
        WHERE
        start_time <![CDATA[<]]> end_time AND end_time <![CDATA[>]]> NOW()
        <if test=" shopId != null ">
            AND shop_id = #{shopId}
        </if>

    </select>
    <select id="selectAvailableList" resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.CouponExtDto">
        SELECT
            DISTINCT t1.*,t2.product_id
        FROM
            coupon t1
        LEFT JOIN coupon_product t2 ON t1.id = t2.coupon_id
        <where>
            <if test="shopIds != null and shopIds.size() > 0">
                AND t1.shop_id in
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            AND NOW() BETWEEN t1.start_time
            AND t1.end_time
            <if test="productIds != null and productIds.size() > 0">
                AND
                (
                    t1.use_area = 0
                    OR (
                        t1.use_area = 1 AND t2.product_id in
                        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                            #{productId}
                        </foreach>
                        )
                )
            </if>
        </where>
    </select>

</mapper>