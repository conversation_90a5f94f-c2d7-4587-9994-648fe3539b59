<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.product.dao.core.mapper.ProductDescriptionMapper">

    <select id="listRelateProductCount" resultType="com.sankuai.shangou.seashop.product.dao.core.model.DescriptionRelateProductDto">
        SELECT
            description_prefix_id `templateId`,
            COUNT(product_id) `relateProductCount`
        FROM
            product_description
        WHERE
            deleted = 0
        <if test="templateIds != null and templateIds.size() > 0">
            AND description_prefix_id IN
            <foreach collection="templateIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
            description_prefix_id
        UNION
        SELECT
            description_suffix_id `templateId`,
            COUNT(product_id) `relateProductCount`
        FROM
            product_description
        WHERE
            deleted = 0
        <if test="templateIds != null and templateIds.size() > 0">
            AND description_suffix_id IN
            <foreach collection="templateIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
            description_suffix_id
    </select>

</mapper>
