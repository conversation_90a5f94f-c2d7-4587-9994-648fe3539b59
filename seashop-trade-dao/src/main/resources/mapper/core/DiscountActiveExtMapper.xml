<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.DiscountActiveExtMapper">

    <select id="selectProductInActive"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActive">
        select
            t1.id,
            t1.active_name,
            t1.shop_id,
            t1.start_time,
            t1.end_time,
            t1.iz_all_product
        from
            discount_active t1
        left join discount_active_product t2 on t1.id = t2.active_id
        where
            t1.end_time > now()
            and t1.start_time <![CDATA[<]]>  now()
            and t2.del_flag = 0
            <if test=" shopId != null ">
                and t1.shop_id = #{shopId}
            </if>
            <if test = " notEqId != null ">
                and t1.id <![CDATA[<>]]> #{notEqId}
            </if>
            AND ((t1.iz_all_product = 0
            <if test=" productIds != null and productIds.size() > 0 ">and t2.product_id in
                <foreach
                        collection="productIds" item="productId" open="(" separator="," close=")">
                    #{productId}
                </foreach>
            </if>
            ) or t1.iz_all_product = 1)
            /*取交集的算法*/
            <if test="endTime != null">
                and t1.start_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="startTime != null">
                and t1.end_time <![CDATA[>=]]> #{startTime}
            </if>
    </select>

    <select id="selectProductInActiveDetail"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveDetailDto">
        select
            t1.id,
            t1.active_name,
            t1.shop_id,
            t1.start_time,
            t1.end_time,
            t1.iz_all_product,
            t2.product_id
        from
            discount_active t1
        left join discount_active_product t2 on t1.id = t2.active_id
        where
            t1.end_time > now()
            and t1.start_time <![CDATA[<]]> t1.end_time
            and t2.del_flag = 0
            <if test=" shopId != null ">
                and t1.shop_id = #{shopId}
            </if>
            <if test = " notEqId != null ">
                and t1.id <![CDATA[<>]]> #{notEqId}
            </if>
            AND ((t1.iz_all_product = 0
            <if test=" productIds != null and productIds.size() > 0 ">and t2.product_id in
                <foreach
                        collection="productIds" item="productId" open="(" separator="," close=")">
                    #{productId}
                </foreach>
            </if>
            ) or t1.iz_all_product = 1)
            /*取交集的算法*/
            <if test="endTime != null">
                and t1.start_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="startTime != null">
                and t1.end_time <![CDATA[>=]]> #{startTime}
            </if>
    </select>

    <select id="queryAllActiveDiscountActive"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActive"
            parameterType="java.lang.Long">
        SELECT
            *
        FROM
        discount_active
        WHERE
        start_time <![CDATA[<]]> end_time AND end_time <![CDATA[>]]> NOW()
        <if test=" shopId != null ">
            AND shop_id = #{shopId}
        </if>
    </select>

    <select id="selectCurrentEnableIzAllProduct"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActive">
        select *
        from discount_active
        where active_status = 0
          and iz_all_product = 1
          and start_time <![CDATA[<=]]> NOW()
          and end_time <![CDATA[>=]]> NOW()
        order by update_time desc limit 1;
    </select>

    <select id="selectCurrentByProductId"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActiveProduct">
        select DISTINCT dap.*
        from discount_active da
        join discount_active_product dap on da.id=dap.active_id
        where da.active_status=0 and da.iz_all_product=0
        and start_time <![CDATA[<=]]> NOW()
        and end_time <![CDATA[>=]]> NOW()
        and dap.del_flag=0
        and dap.product_id in
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>
</mapper>