<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.product.dao.core.mapper.ShopBrandMapper">

    <resultMap id="shopBrandExtResultMap"
               type="com.sankuai.shangou.seashop.product.dao.core.model.ShopBrandExtDto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="brand_id" jdbcType="BIGINT" property="brandId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" javaType="java.util.Date" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" javaType="java.util.Date" />
        <result column="brand_id" jdbcType="BIGINT" property="brandId" />
        <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
        <result column="display_sequence" jdbcType="BIGINT" property="displaySequence" />
        <result column="logo" jdbcType="VARCHAR" property="logo" />
        <result column="description" jdbcType="VARCHAR" property="description" />
    </resultMap>

    <select id="listShopBrandExt" resultMap="shopBrandExtResultMap">
        SELECT
        sb.id,
        sb.shop_id,
        sb.create_time,
        sb.update_time,
        b.id `brand_id`,
        b.`name` `brand_name`,
        b.display_sequence,
        b.logo,
        b.description
        FROM
        shop_brand sb
        INNER JOIN brand b ON sb.brand_id = b.id
        <where>
            <if test="param.shopId != null and param.shopId > 0">
                AND sb.shop_id = #{param.shopId}
            </if>
            <if test="param.brandName != null and param.brandName != ''">
                AND b.`name` LIKE CONCAT('%', #{param.brandName}, '%')
            </if>
            AND b.whether_delete = 0
        </where>
    </select>

</mapper>
