<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.CollocationMapper">

    <select id="pageSellerCollocation" parameterType="com.sankuai.shangou.seashop.promotion.dao.core.model.PageSellerCollocationDto" resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.PageCollocationDto">
        SELECT
            a.id as activityId,
            a.title as activityName,
            a.shop_id as shopId,
            a.start_time as startTime,
            a.end_time as endTime,
        any_value(b.product_id) as mainProductId
        FROM
            collocation a, collocation_product b, collocation_product c
        where
            a.id = b.collo_id and b.main_flag = 1
          and a.id = c.collo_id
        <if test="req.title != null and req.title != ''">
            and a.title like CONCAT('%',#{req.title},'%')
        </if>
        <if test="req.shopId != null and req.shopId != ''">
            and a.shop_id = #{req.shopId}
        </if>
        <if test="req.status != null">
            <if test="req.status == 0">
                and a.start_time &gt;= NOW() and a.end_time &gt;= NOW()
            </if>
            <if test="req.status == 1">
                and a.start_time &lt;= NOW() and a.end_time &gt;= Now()
            </if>
            <if test="req.status == 2">
                and a.end_time &lt;= NOW()
            </if>
        </if>
        <if test="req.activityId != null and req.activityId != ''">
            and a.id like CONCAT('%',#{req.activityId},'%')
        </if>
        <if test="req.productId != null and req.productId != ''">
            and c.main_flag = 1
            and c.product_id like CONCAT('%',#{req.productId},'%')
        </if>
        <if test="req.mainProductIds != null and req.mainProductIds.size()>0">
            and c.main_flag = 1
            and c.product_id in
            <foreach collection="req.mainProductIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY a.id
        order by a.create_time DESC
    </select>

    <select id="pageMCollocation" parameterType="com.sankuai.shangou.seashop.promotion.dao.core.model.PageMCollocationDto" resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.PageCollocationDto">
        SELECT
        a.id as activityId,
        a.title as activityName,
        a.shop_id as shopId,
        a.start_time as startTime,
        a.end_time as endTime,
        any_value(b.product_id) as mainProductId
        FROM
        collocation a, collocation_product b, collocation_product c
        where
        a.id = b.collo_id and b.main_flag = 1
        and a.id = c.collo_id
        <if test="req.shopIdList != null and req.shopIdList.size()>0">
            and a.shop_id in
            <foreach collection="req.shopIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.shopId != null and req.shopId != ''">
            and a.shop_id like CONCAT('%',#{req.shopId},'%')
        </if>
        <if test="req.activityId != null and req.activityId != ''">
            and a.id like CONCAT('%',#{req.activityId},'%')
        </if>
        <if test="req.title != null and req.title != ''">
            and a.title like CONCAT('%',#{req.title},'%')
        </if>
        <if test="req.mainproductId != null and req.mainproductId != ''">
            and c.main_flag = 1
            and c.product_id like CONCAT('%',#{req.mainproductId},'%')
        </if>
        <if test="req.productIdList != null and req.productIdList.size()>0">
            and c.main_flag = 1
            and c.product_id in
            <foreach collection="req.productIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.startTime != null">
            and a.start_time &gt;= #{req.startTime}
        </if>
        <if test="req.endTime != null">
            and a.end_time &lt;= #{req.endTime}
        </if>
        GROUP BY a.id
        order by a.create_time DESC
    </select>

    <select id="queryCollocationByProductIdsAndStatus" parameterType="java.lang.String" resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.CollocationActivityRespDto">
        SELECT
            a.id as id,
            a.title as title,
            a.start_time as startTime,
            a.end_time as endTime,
            b.product_id as productId,
            b.main_flag as mainFlag,
            (case when a.start_time &gt;= NOW() and a.end_time &gt;= NOW() then '未开始'
            when a.start_time &lt;= NOW() and a.end_time &gt;= NOW() then '进行中'
            when a.end_time &lt;= NOW() then '已结束' else '-1' END)  `statusName`
        FROM
            collocation a,
            collocation_product b
        WHERE
            a.id = b.collo_id
          AND b.product_id in
        <foreach collection="productIds" item="item" open="(" separator="," close=")">
             #{item}
        </foreach>
        <if test="flag != null and flag == 1">
            and a.start_time &lt; a.end_time
            and a.end_time &gt; NOW()
        </if>
    </select>
</mapper>
