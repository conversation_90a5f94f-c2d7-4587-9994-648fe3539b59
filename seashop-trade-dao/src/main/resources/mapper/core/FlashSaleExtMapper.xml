<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext.FlashSaleExtMapper">

    <update id="updateSaleCountById">
        update flash_sale set sale_count = sale_count + #{number} where id = #{id}
    </update>

    <select id="queryMallFlashSaleList"
            resultType="com.sankuai.shangou.seashop.promotion.dao.core.model.MallFlashSaleDto"
            parameterType="com.sankuai.shangou.seashop.promotion.dao.core.model.MallFlashSaleParamDto">
        SELECT
            t1.id,
            t1.title,
            t1.shop_id,
            t1.product_id,
            t1.url_path,
            t1.begin_date,
            t1.end_date,
            t1.min_price,
            t1.sale_count,
            t2.preheat,
            t2.normal_purchase_flag
        FROM
            flash_sale t1
        LEFT JOIN flash_sale_config t2 ON t1.shop_id = t2.shop_id
        WHERE
        t1.`status` = 2
        <if test = " frontFlag != null">
            AND t1.front_flag = #{frontFlag}
        </if>
        AND (
                (
                -- 	  情况1：当前时间已经在活动时间范围内
                t1.begin_date <![CDATA[<]]> now()
                AND t1.end_date <![CDATA[>]]> now()
                )

            OR
                (
                -- 	    情况2：当时时间小于活动时间，但在配置的时间预热时间范围内
                t1.begin_date <![CDATA[>]]> now()
                AND t2.preheat IS NOT NULL
                AND t2.preheat != 0
                AND t1.begin_date <![CDATA[<]]> DATE_ADD(NOW(),INTERVAL t2.preheat HOUR)
                )
        )
        <if test = " productIds != null and productIds.size() > 0 ">
            AND t1.product_id IN
            <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                #{productId}
            </foreach>
        </if>
        <if test = " categoryId != null and categoryId > 0 ">
            AND t1.category_id = #{categoryId}
        </if>
        order by
        <if test = " saleCountSort != null and saleCountSort">
            t1.sale_count desc,
        </if>
            t1.begin_date asc

    </select>
    <select id="queryAllActiveFlashSale" resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSale"
            parameterType="java.lang.Long">
        SELECT
        *
        FROM
        flash_sale
        WHERE
        begin_date <![CDATA[<]]> end_date AND end_date <![CDATA[>]]> now()
        AND `status` BETWEEN 1 AND 2
        <if test = " shopId != null ">
            AND shop_id = #{shopId}
        </if>
        <if test = "categoryId != null">
            AND category_id = #{categoryId}
        </if>
    </select>

    <select id="queryActiveByDetailParam" resultType="com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSale"
            parameterType="com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleDetail">
        SELECT
            t1.*
        FROM
            flash_sale t1
        INNER JOIN flash_sale_detail t2 ON t1.id = t2.flash_sale_id
        where t1.begin_date <![CDATA[<]]> t1.end_date AND t1.end_date <![CDATA[>]]> now()
        AND t1.status BETWEEN 1 AND 2
        AND t1.front_flag = 1
        <if test = " flashSaleId != null ">
            AND t2.flash_sale_id = #{flashSaleId}
        </if>
        <if test = " productId != null ">
            AND t2.product_id = #{productId}
        </if>
        <if test = " skuId != null and skuId != '' ">
            AND t2.sku_id = #{skuId}
        </if>
    </select>
</mapper>