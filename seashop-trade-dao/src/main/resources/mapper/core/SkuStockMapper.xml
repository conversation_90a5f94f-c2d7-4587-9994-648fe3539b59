<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.product.dao.core.mapper.SkuStockMapper">

    <select id="listBySkuIdsForUpdate" resultType="com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock">
        SELECT
            *
        FROM
            sku_stock
        WHERE
            sku_id IN
        <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        FOR UPDATE
    </select>

    <update id="updateBatchBySkuId">
        <foreach collection="skuList" item="sku" separator=";">
            UPDATE sku_stock SET
            <trim suffixOverrides=",">
                <if test="sku.stock != null">
                    stock = #{sku.stock},
                </if>
                <if test="sku.safeStock != null">
                    safe_stock = #{sku.safeStock},
                </if>
            </trim>
            WHERE sku_id = #{sku.skuId}
        </foreach>
    </update>

</mapper>
