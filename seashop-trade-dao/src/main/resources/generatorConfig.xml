<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
  <context id="MybatisGenerator" targetRuntime="MyBatis3">

    <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin" />

    <commentGenerator>
        <property name="suppressDate" value="true"/>
        <property name="suppressAllComments" value="true"/>
        <property name="javaFileEncoding" value="UTF-8"/>
    </commentGenerator>

    <!--替换成自己数据库的jdbcRef -->
    <zebra jdbcRef="waimaistoremanagement_shangou_sgb2b_seashop_order_test"/>

    <javaModelGenerator targetPackage="com.sankuai.shangou.seashop.trade.dao.core.domain" targetProject="src/main/java">
        <property name="enableSubPackages" value="true" />
        <property name="trimStrings" value="true" />
    </javaModelGenerator>

    <sqlMapGenerator targetPackage="com.sankuai.shangou.seashop.trade.dao.core.mapper" targetProject="src/main/resources">
        <property name="enableSubPackages" value="true" />
    </sqlMapGenerator>

    <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.shangou.seashop.trade.dao.core.mapper"  targetProject="src/main/java">
        <property name="enableSubPackages" value="true" />
    </javaClientGenerator>


     <table tableName="trade_shopping_cart" domainObjectName="ShoppingCart">
        <generatedKey column="id" sqlStatement="MySql" identity="true"/>
     </table>

  </context>
</generatorConfiguration>
