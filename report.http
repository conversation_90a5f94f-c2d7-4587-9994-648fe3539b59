
### 普通测试
POST http://localhost:8080/himall-report/testuser/query
Content-Type: application/json

{
"id": 1,
"name": "张三",
"nowTime": "2024-03-08 13:22:00"
}

### 测试 id为空
POST http://localhost:8080/himall-report/testuser/query
Content-Type: application/json

{
"name": "张三",
"nowTime": "2024-03-08 13:22:00"
}

### 测试业务异常
POST http://localhost:8080/himall-report/testuser/query
Content-Type: application/json

{
"id": 0,
"name": "张三",
"nowTime": "2024-03-08 13:22:00"
}

### 分页
POST http://localhost:8080/himall-report/testuser/page
Content-Type: application/json

{"pageNo": 1, "pageSize": 10, "id": 10, "name": "张三"}


### 用户新增趋势
POST http://localhost:8080/himall-report/report/user/increase/echarts
Content-Type: application/json

{
"range": "DAY",
"start": "2021-01-01 00:00:00",
"end": "2023-02-02 00:00:00"
}

### 商品概览
POST http://localhost:8080/himall-report/report/product/summary
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 商品走势图
POST http://localhost:8080/himall-report/report/product/visitsEcharts
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 分类饼图
POST http://localhost:8080/himall-report/report/product/categoryEcharts
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 商品分析列表
POST http://localhost:8080/himall-report/report/product/query
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 商品分析列表
POST http://localhost:8080/himall-report/report/custom/query
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 商品分析列表
GET http://localhost:8080/himall-report/report/custom/1
Content-Type: application/json

### 报表删除
DELETE http://localhost:8080/himall-report/report/custom/1

### 店铺新增趋势
POST http://localhost:8080/himall-report/report/shop/increase/echarts
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 店铺省份分布
POST http://localhost:8080/himall-report/report/shop/province/echarts
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 店铺分析报表
POST http://localhost:8080/himall-report/report/shop/query
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 交易概览
POST http://localhost:8080/himall-report/report/trade/summary
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 交易趋势
POST http://localhost:8080/himall-report/report/trade/echarts
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 交易省份分布
POST http://localhost:8080/himall-report/report/trade/province
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}

### 交易省份分布
POST http://localhost:8080/himall-report/report/trade/province
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}
### 交易分析报表
POST http://localhost:8080/himall-report/report/trade/query
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}
### 统计测试接口
POST http://localhost:8080/himall-report/report/test/users
Content-Type: application/json

{
"range": "DAY",
"start": "2024-04-01 00:00:00",
"end": "2024-04-30 00:00:00"
}





### 自定义报表保存
POST http://localhost:8080/himall-report/report/custom/save
Content-Type: application/json
{
"id": 2,
"name": "自定义报表11",
"platform": "ALL",
"dimension": "PLATFORM",
"groups": [1,2,3,4,6],
"fields": [1,2,3,4],
"range": "DAY",
"automatic": false,
"times": 7,
"startDate": "2024-01-01 00:00:00",
"endDate": "2024-04-01 00:00:00"
}