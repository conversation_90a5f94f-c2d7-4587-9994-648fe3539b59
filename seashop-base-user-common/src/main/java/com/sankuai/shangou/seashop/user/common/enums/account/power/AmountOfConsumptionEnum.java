package com.sankuai.shangou.seashop.user.common.enums.account.power;

/**
 * 消费金额  枚举 0.0-500 1.500-1000 2.1000-3000 3.3000+
 */
public enum AmountOfConsumptionEnum {
    /**
     * 0-500
     */
    AmountOne(0, "0-500"),

    /**
     * 500-1000
     */
    AmountTwo(1, "500-1000"),

    /**
     * 1000-3000
     */
    AmountThree(2, "1000-3000"),

    /**
     * 3000+
     */
    AmountFour(3, "3000+");

    private Integer code;
    private String desc;

    AmountOfConsumptionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AmountOfConsumptionEnum getByCode(Integer code) {
        for (AmountOfConsumptionEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
//    {
//        /// <summary>
//        /// 0-500
//        /// </summary>
//        [Description("0-500")]
//        AmountOne = 0,
//
//        /// <summary>
//        /// 500-1000
//        /// </summary>
//        [Description("500-1000")]
//        AmountTwo = 1,
//
//        /// <summary>
//        /// 1000-3000
//        /// </summary>
//        [Description("1000-3000")]
//        AmountThree = 2,
//
//        /// <summary>
//        /// 3000+
//        /// </summary>
//        [Description("3000+")]
//        AmountFour = 3,
//
//        ///// <summary>
//        ///// 200-300
//        ///// </summary>
//        //[Description("3000+")]
//        //AmountFive = 4,
//    }