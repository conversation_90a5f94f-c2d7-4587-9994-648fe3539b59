package com.sankuai.shangou.seashop.user.common.enums.shop;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/25 16:23
 */
public enum StockStatusEmun {

    SOLD_OUT(0, "已售罄"),
    DELISTED(1, "已下架"),
    APPROVED_SPOT_GOODS(2, "现货"),
    NONE(3, "异常库存")
    ;

    private final Integer code;
    private final String name;

    StockStatusEmun(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static StockStatusEmun getEmunBySaleStatusAndAuditStatus(Integer auditStatus, Integer saleStatus) {
        if (auditStatus == 4 || saleStatus == 2){
            return DELISTED;
        }else if(auditStatus == 2 || saleStatus == 1){
            return APPROVED_SPOT_GOODS;
        }else {
            return NONE;
        }
    }
}
