package com.sankuai.shangou.seashop.user.common.util;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.layout.font.FontProvider;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PdfFileUtils implements CommandLineRunner {

    /**
     * 字体为方正兰亭黑系列  公司购买的永久使用权，文档： https://km.sankuai.com/page/**********，该类有缓存 第一次加载时会较慢
     */
    @Value("${font.path:}")
    private String FONT_PATH_CU;
    @Value("${font.cu_path:}")
    private String FONT_PATH;

    public FontProvider fontProvider;

    public void html2pdf(OutputStream out, String html) {
        try {
            ConverterProperties converterProperties = new ConverterProperties();
            converterProperties.setCharset("UTF-8");
            converterProperties.setFontProvider(fontProvider);
            HtmlConverter.convertToPdf(html, out, converterProperties);
        } catch (IOException e) {
            log.error("html转PDF出错", e);
            throw new BusinessException("html转PDF出错");
        }
    }

    @Override
    public void run(String... args) throws Exception {
        fontProvider = new FontProvider();
        fontProvider.addStandardPdfFonts();
        try {
            PdfFont microsoft = PdfFontFactory.createFont(FONT_PATH, PdfEncodings.UTF8, false);
            fontProvider.addFont(microsoft.getFontProgram(), PdfEncodings.IDENTITY_H);
            PdfFont microsoft_cu = PdfFontFactory.createFont(FONT_PATH_CU, PdfEncodings.UTF8, false);
            fontProvider.addFont(microsoft_cu.getFontProgram(), PdfEncodings.IDENTITY_H);
        } catch (Exception e) {
            log.error("获取字体文件出错", e);
            throw new BusinessException("获取字体文件出错");
        }
    }

//    public void htmlToPdf() throws Exception {
//        String path = resourcesDir + "/template/template.html";
//        String destPath = resourcesDir + "/template/template.pdf";
//        ConverterProperties converterProperties = new ConverterProperties();
//        FontProvider dfp = new DefaultFontProvider();
//        //添加字体库
//        dfp.addDirectory("C:/Windows/Fonts");
//        converterProperties.setFontProvider(dfp);
//        try (InputStream in = new FileInputStream(new File(path)); OutputStream out = new FileOutputStream(new File(destPath))){
//            HtmlConverter.convertToPdf(in, out, converterProperties);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
}
