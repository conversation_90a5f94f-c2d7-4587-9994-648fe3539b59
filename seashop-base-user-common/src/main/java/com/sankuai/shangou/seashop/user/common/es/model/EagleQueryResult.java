package com.sankuai.shangou.seashop.user.common.es.model;

import com.google.common.collect.Lists;
import lombok.*;
import org.elasticsearch.search.aggregations.Aggregations;

import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class EagleQueryResult {
    /**
     * 命中总数
     */
    private Long totalHit;
    /**
     * score
     */
    private float maxScore;
    /**
     * 游标ID
     */
    private String scrollId;
    /**
     * 文档内容，json格式
     */
    private List<String> hits;
    /**
     * 聚合
     */
    private Aggregations aggregations;


    public static EagleQueryResult defaultEagleQueryResult() {
        return EagleQueryResult.builder()
                .totalHit(0L)
                .maxScore(0f)
                .hits(Lists.newArrayList())
                .build();
    }
}
