package com.sankuai.shangou.seashop.user.common.constant;


import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/30 11:44
 */
public class CommonConstant {

    /**
     * 申请经营类目的深度
     */
    public static final Integer APPLY_BUSINESS_CATEGORY_DEPTH = 3;

    /**
     * 每次查询条数
     */
    public static final Integer BATCH_QUERY_SIZE = 200;

    /**
     * 类目path 分割线
     */
    public static final String CATEGORY_PATH_SPLIT = "\\|";


    public static final Integer DEFAULT_PAGE_NO = 1;

    public static final String SMS = "Himall.Plugin.Message.SMS";
    public static final String EMAIL = "Himall.Plugin.Message.Email";
    //银行类型 1-对公 2-对私
    public static final String BANK_TYPE_PUBLIC = "1";
    public static final String BANK_TYPE_PRIVATE = "2";
    //默认long id
    public static final Long DEFAULT_LONG_ID = 0L;
    //默认int id
    public static final Integer DEFAULT_INT_ID = 0;
    //默认int id
    public static final Integer DEFAULT_INT_ONE = 1;
    //汇付审核状态成功
    public static final String AUDIT_STATE_SUCCESS = "E";
    //汇付审核状态成功
    public static final String AUDIT_STATE_CREATE_SUCCESS = "D";
    //汇付更新企业账号审核状态成功
    public static final String UPDATE_STATE_SUCCESS = "S";
    //默认角色名称
    public static final String DEFAULT_ROLE_NAME = "系统管理员";

    /**
     * ES 查询最大条数
     */
    public static final Integer ES_QUERY_LIMIT = 1000;

    public static final String BANK_PHOTO_SUFFIX = "bankPhoto.png";

    public static final String ID_CARD_URL_SUFFIX = "idCardUrl.png";

    public static final String ID_CARD_URL2_SUFFIX = "idCardUrl2.png";

    public static final String BUSINESS_LICENCE_NUMBER_PHOTO_SUFFIX = "businessLicenceNumberPhoto.png";

    public static final String CERTIFICATES_SUFFIX = "certificates.zip";

    public static final String SUCCESS = "success";

    //修改手机号类型
    public static final Integer CHANGE_MOBILE_TYPE = 2;

    public static final String SPLITTER_FREIGHT_FREE_GROUP = "\\$";
    public static final String MESSAGE_PATTERN_SHIPPING_ADDRESS_FORBIDDEN = "商品%s在该地区暂不支持销售，请切换收货地址或选择其他产品";

    //默认评分 5
    public static final BigDecimal DEFAULT_SCORE = BigDecimal.valueOf(5);
    //默认评分 5
    public static final String CORP_MEMBER_UPDATE = "corp_member_update";
    /**
     * 店铺提醒缓存redis key
     */
    public static final String SHOP_REMIND_CACHE_KEY = "shop:remind:";
    //    缓存过期时间
    public static final Integer CACHE_EXPIRE_TIME = 3600 * 24;
    public static final Long DEFAULT_PARENT_ID = 0L;
    public static final Long SUPER_ADMIN_ROLE_ID = 0L;

    /**
     * excel包导入最大限制 1M
     */
    public static Long EXCEL_IMPORT_MAX_SIZE = 1 * 1024 * 1024L;

    // 区域不存在时，默认设置的区域ID，只是为了防止空指针
    public static final Integer DEFAULT_REGION_ID = -1;

    //afs认证成功错误码
    public static final Integer AFS_SUCCESS_CODE = 100;

    /**
     * 默认图片
     */
    public static final String DEFAULT_IMAGE = "https://historeforjava.obs.cn-south-1.myhuaweicloud.com/test/rs/histore-base/default.png";

    //汇付结算账号创建失败
    public static final String HFP_SETTLE_ACCOUNT_CREATE_FAIL = "银行卡校验未通过，请修改之后重新提交";
}
