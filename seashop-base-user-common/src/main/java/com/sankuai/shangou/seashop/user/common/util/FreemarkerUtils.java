package com.sankuai.shangou.seashop.user.common.util;

import com.google.common.base.Preconditions;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.StringWriter;

/**
 * <AUTHOR> on 2022/1/11
 * <p>
 * 加载类路径下文件
 **/
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FreemarkerUtils {
    public static String loadFtlHtml(String path, String fileName, Object properties) {
        Preconditions.checkNotNull(path, "classpath not null");
        Preconditions.checkNotNull(fileName, "filename not null");
        Preconditions.checkNotNull(properties, "template value not null");

        ClassTemplateLoader classTemplateLoader = new ClassTemplateLoader(FreemarkerUtils.class, path);
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_23);
        try {
            cfg.setTemplateLoader(classTemplateLoader);
            cfg.setDefaultEncoding("UTF-8");
            cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
            cfg.setClassicCompatible(true);
            Template template = cfg.getTemplate(fileName);
            StringWriter stringWriter = new StringWriter();
            template.process(properties, stringWriter);
            return stringWriter.toString();

        } catch (IOException | TemplateException e) {
            log.error("使用模版生成html文件错误", e);
            throw new BusinessException("load fail file");
        }
    }
}
