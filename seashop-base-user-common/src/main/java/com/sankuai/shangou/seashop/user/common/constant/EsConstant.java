package com.sankuai.shangou.seashop.user.common.constant;

/**
 * <AUTHOR>
 */
public class EsConstant {

    /**
     * 店铺es索引
     */
    public static String INDEX_SHOP = "shop_index";

    public static class Shop {

        public static final String SEARCH_FIELD_SHOP_ID = "shopId";
        public static final String SEARCH_FIELD_SHOP_NAME = "shopName.keyword";
        public static final String SEARCH_FIELD_SHOP_STATUS = "shopStatus";
        public static final String SEARCH_FIELD_EXCLUSIVE = "whetherOpenExclusiveMember";
        public static final String SEARCH_FIELD_SERIAL_NUMBER = "serialNumber";
        public static final String SEARCH_FIELD_CATEGORY_IDS = "categoryIdList";
        public static final String SEARCH_FIELD_FIRST_CATEGORY_IDS = "firstCategoryIdList";
        public static final String SEARCH_FIELD_BRAND_IDS = "brandIdList";
        public static final String SEARCH_FIELD_EXCLUSIVE_MEMBER_IDS = "exclusiveMemberIdList";

        /*
         * 聚合相关熟悉
         */
        public static final String AGG_FIELD_BRANDS = "brands";

        public static final String AGG_FIELD_LIST_BRAND_ID = "brandIdList";
        public static final String AGG_FIELD_CATEGORYS = "categorys";
        public static final String AGG_FIELD_LIST_CATEGORY_ID = "categoryIdList";
        public static final String AGG_FIELD_FIRST_CATEGORY_ID = "firstCategoryIdList";

        // 默认搜索的店铺数量
        public static final int DEFAULT_SEARCH_SIZE = 2000;

    }

}
