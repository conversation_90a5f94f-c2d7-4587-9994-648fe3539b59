package com.sankuai.shangou.seashop.user.common.enums;

/**
 * @description: 0菜单 1按钮
 * @author: LXH
 **/
public enum PowerTypeEnum {
    MENU(0, "菜单"),
    SUB_PAGE(1, "子页面"),
    BUTTON(2, "按钮"),
    MENU_PAGE(3, "菜单子界面"),
    AUTHORITY_SUB_PAGE(4, "权限子页面");
    private final Integer value;
    private final String name;

    PowerTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }


    public String getName() {
        return name;
    }
}
