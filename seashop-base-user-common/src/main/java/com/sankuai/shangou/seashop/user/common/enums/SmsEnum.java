package com.sankuai.shangou.seashop.user.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
public class SmsEnum {

    @Getter
    public enum Template {
        // 保证金提醒
        DEPOSIT_REMIND(2007554L, "保证金提醒"),
        // 签署合同提醒
        RE_SIGN_CONTRACT(2007555L, "签署合同提醒"),
        // 签署合同和保证金提醒
        SIGN_CONTRACT_AND_DEPOSIT_REMIND(2009179L, "签署合同和保证金提醒"),
        // 续签合同提醒
        RENEWAL_CONTRACT(2007556L, "续签合同提醒"),
        // 验证码
        VERIFICATION_CODE(2007552L, "验证码"),
        // 审核失败提醒
        REVIEW_FAILURE_REMINDER(2007587L, "审核失败提醒"),
        // 审核通过提醒
        REVIEW_APPROVED_REMINDER(2007553L, "审核通过提醒"),
        // 注册成功通知
        REGISTER_SUCCESS_NOTICE(2007968L, "注册成功通知"),
        // 迁移账号通知
        MIGRATION_ACCOUNT_NOTICE(2008630L, "迁移账号通知"),
        ;

        private final Long code;
        private final String desc;

        Template(Long code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
