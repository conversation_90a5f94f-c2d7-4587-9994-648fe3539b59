package com.sankuai.shangou.seashop.user.common.remote.order;

import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: LXH
 **/
@Service
public class CashDepositRemoteService {

//    @Resource
//    private CashDepositQueryThriftService cashDepositQueryThriftService;
//
////    queryOneByShopId
//    public CashDepositResp queryOneByShopId(Long shopId) {
//        ShopIdReq shopIdReq = new ShopIdReq();
//        shopIdReq.setShopId(shopId);
////        return new CashDepositResp();
//        //远程调用获取结果
//        return ThriftResponseHelper.executeThriftCall(() -> cashDepositQueryThriftService.queryOneByShopId(shopIdReq));
//    }
}

