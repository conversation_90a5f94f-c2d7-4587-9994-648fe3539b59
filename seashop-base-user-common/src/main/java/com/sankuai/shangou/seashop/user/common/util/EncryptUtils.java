package com.sankuai.shangou.seashop.user.common.util;

import org.finance.keykaiser.KeyczarConvertReader;
import org.finance.keykaiser.UnversionedAndSignedCrypter;
import org.finance.keykaiser.UnversionedAndSignedEncrypter;
import org.finance.keykaiser.exceptions.KeyczarException;
import org.finance.keykaiser.util.Base64Coder;
import org.slf4j.Logger;

/**
 * <AUTHOR> on 2022/1/20
 **/
public class EncryptUtils {
    static Logger logger = org.slf4j.LoggerFactory.getLogger(EncryptUtils.class);

    private static final String type = "AES";

    private static final String mode = "GCM";

    private static final String purpose = "DECRYPT_AND_ENCRYPT";

    private static final String aesKey = "DvfCfVzo0HMbVFOp_q1jvw";

    private EncryptUtils() {}

    public static String encrypt(String plaintext){
        try {
            byte[] key_bytes = Base64Coder.decodeWebSafe(aesKey);
            UnversionedAndSignedEncrypter unversionedAndSignedEncrypter = new UnversionedAndSignedEncrypter(new KeyczarConvertReader(type, purpose, mode, key_bytes));
            return unversionedAndSignedEncrypter.encrypt(plaintext);
        }catch (KeyczarException keyczarException ){
            logger.error("加密失败", keyczarException);
            return plaintext;
        }

    }

    public static String decrypt(String ciphertext) {
        try {
            byte[] key_bytes = Base64Coder.decodeWebSafe(aesKey);
            UnversionedAndSignedCrypter unversionedAndSignedCrypter = new UnversionedAndSignedCrypter(new KeyczarConvertReader(type, purpose, mode, key_bytes));
            return unversionedAndSignedCrypter.decrypt(ciphertext);
        } catch (KeyczarException keyczarException) {
            logger.error("解密失败", keyczarException);
            return ciphertext;
        }
    }

}
