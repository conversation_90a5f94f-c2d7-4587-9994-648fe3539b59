package com.sankuai.shangou.seashop.user.common.remote.order;

import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: LXH
 **/
@Service
public class OrderCommentRemoteService {

    //@Resource
    //private OrderCommentQueryThriftService orderCommentQueryThriftService;
    //
    //public List<OrderCommentResp> queryOrderCommentForPlatform(Long shopId) {
    //    QueryOrderCommentReq queryOrderCommentReq = new QueryOrderCommentReq();
    //    queryOrderCommentReq.setShopId(shopId);
    //    //设置不分页
    //    queryOrderCommentReq.setPageNo(1);
    //    queryOrderCommentReq.setPageSize(-1);
    //    //远程调用获取结果
    //    BasePageResp<OrderCommentResp> basePageResp = ThriftResponseHelper.executeThriftCall(() -> orderCommentQueryThriftService.queryOrderCommentForPlatform(queryOrderCommentReq));
    //    //判空
    //    if (basePageResp == null || basePageResp.getData() == null) {
    //        return Collections.emptyList();
    //    }
    //    return basePageResp.getData();
    //}
    //
    ///**
    // * 查询店铺评分
    // *
    // * @param shopId
    // * @return
    // */
    //public ShopMarkResp queryShopMarkByShopId(Long shopId) {
    //    ShopIdReq shopIdReq = new ShopIdReq();
    //    shopIdReq.setShopId(shopId);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderCommentQueryThriftService.queryShopMarkByShopId(shopIdReq));
    //}
}
