package com.sankuai.shangou.seashop.user.common.enums.account.power;


public enum MemberStatisticsType {
    //一个月活跃商家
    ACTIVE_MERCHANTS_FOR_ONE_MONTH(0, "一个月活跃商家"),
    //三个月活跃商家
    ACTIVE_MERCHANTS_FOR_THREE_MONTHS(1, "三个月活跃商家"),
    //六个月活跃商家
    ACTIVE_MERCHANTS_FOR_SIX_MONTHS(2, "六个月活跃商家"),
    //三个月沉睡商家
    SleepingThree(10, "三个月沉睡商家"),
    /// 六个月沉睡商家
    SleepingSix(11, "六个月沉睡商家"),
    /// 九个月沉睡商家
    SleepingNine(12, "九个月沉睡商家"),
    /// 十二个月沉睡商家
    SleepingTwelve(13, "十二个月沉睡商家"),
    /// 二十四个月沉睡商家
    SleepingTwentyFour(14, "二十四个月沉睡商家"),
    /// 今日生日商家
    BirthdayToday(15, "今日生日商家"),
    /// 今月生日商家
    BirthdayThisMonth(16, "今月生日商家"),
    /// 下月生日商家
    BirthdayNextMonth(17, "下月生日商家"),
    /// 注册商家
    Register(18, "注册商家"),
    ;


    private final Integer code;
    private final String desc;

    MemberStatisticsType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MemberStatisticsType getByCode(Integer purchases) {
        for (MemberStatisticsType value : values()) {
            if (value.getCode().equals(purchases)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}