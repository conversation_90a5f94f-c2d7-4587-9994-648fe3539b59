package com.sankuai.shangou.seashop.user.common.enums;

/**
 * @description:
 * @author: LXH
 **/

public class EpAccountEnum {

    public enum PlatformType {
        WEB(2, "WEB/H5"),
        WeiXinSmallProg(6, "微信小程序"),
        ;

        private Integer code;
        private String desc;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        PlatformType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public enum OperateType {
        UNKNOWN(0, "未知"),
        MODIFY_PASSWORD(1, "修改密码"),
        MODIFY_ACCOUNT_NAME(2, "修改账号名"),
        FIND_ACCOUNT_AND_PASSWORD(3, "找回账号和密码"),
        FIND_PASSWORD(4, "找回密码"),
        CHANGE_PHONE(5, "换绑手机号"),
        REGISTER_ACCOUNT(6, "注册账号"),
        CREATE_ACCOUNT(7, "创建账号"),
        ENABLE_DISABLE_ACCOUNT(8, "启用禁用账号"),
        ;

        private Integer code;
        private String desc;

        OperateType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

            public static OperateType getByCode(Integer code) {
                for (OperateType operateType : OperateType.values()) {
                    if (operateType.getCode().equals(code)) {
                        return operateType;
                    }
                }
                return UNKNOWN;
            }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
