package com.sankuai.shangou.seashop.user.common.es.model.shop;

import lombok.*;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EsBusinessCategoryModel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目全名(包含上级)
     */
    private String fullCategoryName;

    /**
     * 是否冻结
     */
    private Boolean whetherFrozen;
}
