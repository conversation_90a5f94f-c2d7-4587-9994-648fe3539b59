package com.sankuai.shangou.seashop.user.common.util;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Component;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/12 15:22
 */
@Slf4j
@Component
public class ThreadPoolUtil {

    public static ThreadPoolExecutor getUserThreadPool() {
//        DefaultThreadPoolProperties.Setter setter = DefaultThreadPoolProperties.Setter()
//                .withCoreSize(10)
//                .withMaxSize(20)
//                .withKeepAliveTimeMinutes(10)
//                .withKeepAliveTimeUnit(TimeUnit.SECONDS)
//                .withBlockingQueue(new ArrayBlockingQueue<>(200000))
//                .withMaxQueueSize(200000)
//                .withThreadFactory(new ThreadFactoryBuilder().setNameFormat("USER").build());
//        return Rhino.newThreadPool("QUERY_USER_CENTER_HOME", setter);
        return new ThreadPoolExecutor(10, 20, 10, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(200000),
            new ThreadFactoryBuilder().setNameFormat("USER").build()
        );
    }
}
