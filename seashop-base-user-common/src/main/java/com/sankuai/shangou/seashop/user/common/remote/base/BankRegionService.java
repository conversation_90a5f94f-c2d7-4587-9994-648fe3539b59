package com.sankuai.shangou.seashop.user.common.remote.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.user.common.model.BankRegionBO;
import com.sankuai.shangou.seashop.user.common.model.TreeRegionBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author： liweisong
 * @create： 2023/12/16 14:02
 */
@Service
@Slf4j
public class BankRegionService implements CommandLineRunner {

    private List<TreeRegionBO> treeRegionBOS;


    public String getTreeRegions(){
        return JSONUtil.toJsonStr(treeRegionBOS);
    }

    public List<TreeRegionBO> getRegionsById(BaseIdReq Id){
        //        遍历treeRegionBOS;
        return getFullList(Id.getId().intValue());
    }

    public List<TreeRegionBO> getRegionByParentId(BaseIdReq parentId){
        //        遍历treeRegionBOS;
        List<TreeRegionBO> result = new ArrayList<>();
        for (TreeRegionBO treeRegionBO : treeRegionBOS) {
            if (parentId.getId() == 0) {
                result.add(treeRegionBO);
            }else {
                if (NumberUtil.equals(parentId.getId().longValue(),treeRegionBO.getId().longValue())) {
                    result = treeRegionBO.getSub();
                }
            }
        }
        return result;
    }

//    public Map<String, AllPathRegionResp> getAllPathRegions(RegionIdsReq regionIdsReq){
//        return new HashMap<>();
//    }

    /**
     * 根据区域id获取区域全名
     * @param regionId 区域id
     * @return 区域全名
     */
    public String getRegionFullName(Integer regionId){
//        遍历treeRegionBOS获取全名
        for (TreeRegionBO treeRegionBO : treeRegionBOS) {
            if (regionId.equals(treeRegionBO.getId()) || CollUtil.isEmpty(treeRegionBO.getSub())) {
                continue;
            }
            for (TreeRegionBO secondTreeRegionBO : treeRegionBO.getSub()) {
                if (regionId.equals(secondTreeRegionBO.getId())) {
                    return treeRegionBO.getName() + secondTreeRegionBO.getName();
                }
            }
        }
        return "";
    }

    /**
     * 根据区域id获取区域列表
     * @param code 区域id
     * @return 区域全名
     */
    public List<TreeRegionBO> getFullList(Integer code){
//        遍历treeRegionBOS;
        List<TreeRegionBO> result = new ArrayList<>();
        for (TreeRegionBO treeRegionBO : treeRegionBOS) {
            if (code.equals(treeRegionBO.getId())||CollUtil.isEmpty(treeRegionBO.getSub())) {
                continue;
            }
            for (TreeRegionBO secondTreeRegionBO : treeRegionBO.getSub()){
                if (code.equals(secondTreeRegionBO.getId())) {
                    TreeRegionBO newTreeRegionBO = new TreeRegionBO();
                    newTreeRegionBO.setId(treeRegionBO.getId());
                    newTreeRegionBO.setCode(treeRegionBO.getCode());
                    newTreeRegionBO.setRegionLevel(treeRegionBO.getRegionLevel());
                    newTreeRegionBO.setName(treeRegionBO.getName());
                    newTreeRegionBO.setParentId(treeRegionBO.getParentId());
                    result.add(newTreeRegionBO);
                    result.add(secondTreeRegionBO);
                    return result;
                }
            }
        }
        return result;
    }

    @Override
    public void run(String... args) throws Exception {
        try {
            String read = new String(HttpUtil.downloadBytes("https://himall-storage-**********.cos.ap-nanjing.myqcloud.com/web/Storage/regionBank.json"));
            List<BankRegionBO> bankRegionDTOS = JSONUtil.toList(read, BankRegionBO.class);
            treeRegionBOS = bankRegionDTOS.stream().map(bankRegionBO -> {
                TreeRegionBO treeRegionBO = new TreeRegionBO();
                treeRegionBO.setId(Integer.parseInt(bankRegionBO.getValue()));
                treeRegionBO.setCode(bankRegionBO.getValue());
                treeRegionBO.setRegionLevel(1);
                treeRegionBO.setParentId(0);
                treeRegionBO.setName(bankRegionBO.getTitle());
                List<BankRegionBO> cities = bankRegionBO.getCities();
                if (CollUtil.isNotEmpty(cities)) {
                    List<TreeRegionBO> treeRegionBOS1 = cities.stream().map(bankRegionBO1 -> {
                        TreeRegionBO treeRegionBO1 = new TreeRegionBO();
                        treeRegionBO1.setId(Integer.parseInt(bankRegionBO1.getValue()));
                        treeRegionBO1.setCode(bankRegionBO1.getValue());
                        treeRegionBO1.setRegionLevel(2);
                        treeRegionBO1.setParentId(treeRegionBO.getId());
                        treeRegionBO1.setName(bankRegionBO1.getTitle());
                        return treeRegionBO1;
                    }).collect(Collectors.toList());
                    treeRegionBO.setSub(treeRegionBOS1);
                }
                return treeRegionBO;
            }).collect(Collectors.toList());
        }
        catch (Exception e) {
            log.error("sync bankRegion error ", e);
        }
    }
}
