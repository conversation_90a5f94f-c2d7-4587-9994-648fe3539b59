package com.sankuai.shangou.seashop.user.common.enums;

/**
 * @description: 0平台，1供应商，2商家
 * @author: LXH
 **/
public enum PlatformTypeEnum {

    Platform_TYPE_0(0, "0平台"),
    Platform_TYPE_1(1, "1供应商"),
    Platform_TYPE_2(2, "2商家");
    private final Integer value;
    private final String name;

    PlatformTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }


    public String getName() {
        return name;
    }
}
