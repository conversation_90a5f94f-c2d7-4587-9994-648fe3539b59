package com.sankuai.shangou.seashop.user.common.constant;

/**
 * @author: lhx
 * @date: 2024/1/4/004
 * @description:
 */
public class MafkaDtsConstant extends MafkaConst{

    /**
     * 店铺表变更topic
     * seashop_user_shop_dts_topic
     */
    public static final String TOPIC_USER_SHOP_DTS = "seashop_user_shop_dts_topic";

    /**
     * 店铺表变更消费者
     * seashop_user_shop_dts_consumer
     */
    public static final String CONSUMER_USER_SHOP_DTS ="seashop_user_shop_dts_consumer";

    /**
     * 店铺品牌变更topic
     * seashop_shop_brand_dts_topic
     */
    public static final String TOPIC_SHOP_BRAND_DTS = "seashop_shop_brand_dts_topic";

    /**
     * 店铺品牌变更消费者
     * seashop_shop_brand_dts_consumer
     */
    public static final String CONSUMER_SHOP_BRAND_DTS ="seashop_shop_brand_dts_consumer";

    /**
     * 店铺类目变更topic
     * seashop_user_business_category_dts_topic
     */
    public static final String TOPIC_USER_BUSINESS_CATEGORY_DTS = "seashop_user_business_category_dts_topic";
    /**
     * 店铺类目变更消费者
     * seashop_user_business_category_dts_consumer
     */
    public static final String CONSUMER_USER_BUSINESS_CATEGORY_DTS ="seashop_user_business_category_dts_consumer";

    /**
     * 订单评论变更topic
     * seashop_order_comment_dts_topic
     */
    public static final String TOPIC_ORDER_COMMENT_DTS = "seashop_order_comment_dts_topic";
    /**
     * 订单评论变更消费者
     * seashop_order_comment_dts_consumer
     */
    public static final String CONSUMER_ORDER_COMMENT_DTS ="seashop_order_comment_dts_consumer";

    /**
     * 订单状态变更topic
     * seashop_order_change_topic
     */
    public static final String TOPIC_ORDER_CHANGE = "seashop_order_change_topic";
    /**
     * 订单状态变更消费者
     * seashop_order_change_shop_consumer
     */
    public static final String CONSUMER_ORDER_CHANGE ="seashop_order_change_shop_consumer";

    /**
     * 专属商家变更topic
     * seashop_user_exclusive_member_dts_topic
     */
    public static final String TOPIC_USER_EXCLUSIVE_MEMBER_DTS = "seashop_user_exclusive_member_dts_topic";
    /**
     * 专属商家变更消费者
     * seashop_user_exclusive_member_dts_consumer
     */
    public static final String CONSUMER_USER_EXCLUSIVE_MEMBER_DTS ="seashop_user_exclusive_member_dts_consumer";

    /**
     * 类目变动事件topic
     */
    public static final String TOPIC_CATEGORY_CHANGE = "seashop_product_category_change_topic";

    /**
     * 类目变更消费者
     */
    public static final String GROUP_CATEGORY_CHANGE = "seashop_product_category_change_user_consumer";

    /**
     * 商品变动事件topic
     */
    public static final String TOPIC_PRODUCT_DTS = "seashop_product_dts_topic";

    /**
     * 商品变动消费者
     */
    public static final String CONSUMER_SHOP_PRODUCT_DTS = "seashop_shop_product_dts_consumer";
    /**
     * 自定义表单变动topic
     */
    public static final String CUSTOM_FORM_FIELD_DTS_TOPIC = "seashop_base_custom_form_field_dts_topic";

    /**
     * 商品变动消费者
     */
    public static final String custom_form_field_dts_user_consumer = "seashop_base_custom_form_field_dts_user_consumer";
}
