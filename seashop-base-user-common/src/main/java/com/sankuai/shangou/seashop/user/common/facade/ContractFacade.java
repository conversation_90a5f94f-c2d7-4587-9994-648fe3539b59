package com.sankuai.shangou.seashop.user.common.facade;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.user.common.model.ContractBO;
import com.sankuai.shangou.seashop.user.common.model.SignContractBO;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO 认证
 * <AUTHOR>
 * 合同服务
 */
@Slf4j
@Service
public class ContractFacade {
//    @Resource
//    private EsignThriftClient esignThriftClient;
    /**
     * 成功码
     */
    private static final int SUCCESS_CODE = 1000;

    private static final int SIGN_OFFSET_Y = -100;
    /**
     * 合同有效时间，单位年
     */
    public final static int CONTRACT_EXPIRATION_TIME = 1;

    public final static String CONTRACT_TITLE = "美团商家服务市场合作协议";

    public final static String CONTRACT_DESCRIPTION = "美团商家服务市场合作协议";

    /**
     * 创建合同，需要先进行认证拿到客户ID
     *
     * @param contract
     * @return 合同ID
     */
    public String createContract(ContractBO contract) {
//        ContractRequest contractRequest = new ContractRequest();
//        contractRequest.setDescription(CONTRACT_DESCRIPTION);
//        contractRequest.setTitle(CONTRACT_TITLE);
//        contractRequest.setCreatorCustomerId(contract.getCustomerId());
//        contractRequest.setContractDatas(contract.getContractData());
//        contractRequest.setExpireAt(contract.getExpireAt());
//        CreateContractResult createContractResult = esignThriftClient.createContract(contractRequest);
//        if (SUCCESS_CODE != createContractResult.getCode()) {
//            log.error("创建合同错误，code:{}, msg:{}", createContractResult.getCode(), createContractResult.getMessage());
//            throw new BusinessException(createContractResult.getMessage());
//
//        }
//        return createContractResult.getContractId();
        return StrUtil.EMPTY;
    }

    /**
     * 签署合同
     *
     * @param signContractDto
     * @return
     */
    public String signContract(SignContractBO signContractDto) {
//        SignContractRequest signContractRequest = new SignContractRequest();
//        signContractRequest.setContractId(signContractDto.getContractId());
//        signContractRequest.setCustomerId(signContractDto.getCustomerId());
//        signContractRequest.setKeyWord(signContractDto.getKeyWord());
//        signContractRequest.setCustomerIdCode(signContractDto.getCustomerIdCode());
//        signContractRequest.setCustomerName(signContractDto.getCustomerName());
//        signContractRequest.setOffsetY(SIGN_OFFSET_Y);
//        SignContractResult result = esignThriftClient.signContract(signContractRequest);
//        if (SUCCESS_CODE != result.getCode()) {
//            log.error("签署合同错误，code:{}, msg:{}", result.getCode(), result.getMessage());
//            throw new BusinessException(result.getMessage());
//        }
//        return result.getContractId();
        return StrUtil.EMPTY;
    }

    /**
     * 归档合同
     *
     * @param contractId
     */
    public void archiveContract(String contractId) {
//        ArchiveContractResult result = esignThriftClient.archiveContract(contractId);
//        if (SUCCESS_CODE != result.getCode()) {
//            log.error("归档合同错误，code:{}, msg:{}", result.getCode(), result.getMessage());
//            throw new BusinessException(result.getMessage());
//        }
    }

    /**
     * 获取预览url
     *
     * @param contractId
     * @param customerId
     * @return
     */
    public String previewContract(String contractId, String customerId) {
//        GenPreviewUrlRequest genPreviewUrlRequest = new GenPreviewUrlRequest();
//        genPreviewUrlRequest.setContractId(contractId);
//        genPreviewUrlRequest.setCustomerId(customerId);
//        genPreviewUrlRequest.setExpireAt(LocalDateTime.now().plusYears(CONTRACT_EXPIRATION_TIME)
//                .toInstant(ZoneOffset.of("+8")).toEpochMilli());
//        genPreviewUrlRequest.setNeedVerify(false);
//        GetUrlResult result = esignThriftClient.getContractPreviewUrl(genPreviewUrlRequest);
//        if (SUCCESS_CODE != result.getCode()) {
//            log.error("上传签名，code:{}, msg:{}", result.getCode(), result.getMessage());
//            throw new BusinessException(result.getMessage());
//        }
//        return result.getUrl();
        return StrUtil.EMPTY;
    }

    /**
     * 下载合同
     *
     * @param contractId
     * @return
     */
    public byte[] downloadContract(String contractId) {
//        GetContractResult result = esignThriftClient.downloadContract(contractId);
//        if (SUCCESS_CODE != result.getCode()) {
//            log.error("下载合同错误，code:{}, msg:{}", result.getCode(), result.getMessage());
//            throw new BusinessException(result.getMessage());
//        }
//        return result.getContract();
        return new byte[0];
    }
}
