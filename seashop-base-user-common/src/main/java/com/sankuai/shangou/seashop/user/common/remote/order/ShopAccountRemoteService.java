package com.sankuai.shangou.seashop.user.common.remote.order;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author： liweisong
 * @create： 2023/12/13 10:44
 */
@Service
@Slf4j
public class ShopAccountRemoteService {

    //@Resource
    //private ShopAccountCmdThriftService shopAccountCmdThriftService;
    //@Resource
    //private ShopAccountQueryThriftService shopAccountQueryThriftService;
    //
    ////    创建店铺结算信息
    //public BaseResp createShopAccount(CreateAccountReq accountReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopAccountCmdThriftService.createAccount(accountReq));
    //}
    //
    //public String getShopAccountByShopId(Long shopId) {
    //    return ThriftResponseHelper
    //        .executeThriftCall(() -> shopAccountQueryThriftService.getShopAccountByShopId(shopId));
    //}
}
