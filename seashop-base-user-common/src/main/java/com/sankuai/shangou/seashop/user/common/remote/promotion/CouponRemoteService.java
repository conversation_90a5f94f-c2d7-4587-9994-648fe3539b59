package com.sankuai.shangou.seashop.user.common.remote.promotion;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author： liweisong
 * @create： 2023/12/13 9:21
 */
@Service
@Slf4j
public class CouponRemoteService {

    //@Resource
    //private CouponQueryThriftService couponQueryThriftService;
    //
    //public Integer queryAvailableCouponCountByUser(Long userId){
    //    return ThriftResponseHelper.executeThriftCall(()->couponQueryThriftService.queryAvailableCouponCountByUser(userId));
    //}
}
