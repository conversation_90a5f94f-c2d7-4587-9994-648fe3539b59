package com.sankuai.shangou.seashop.user.common.facade;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.user.common.model.CompanyCertBO;
import com.sankuai.shangou.seashop.user.common.model.PersonCertBO;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO 认证
 *
 * <AUTHOR>
 * 认证服务
 */
@Slf4j
@Service
public class CertFacade {
//    @Resource
//    private EsignThriftClient esignThriftClient;
    /**
     * 成功码
     */
    private static final int SUCCESS_CODE = 1000;

    /**
     * 企业认证
     *
     * @param companyCert
     * @return
     */
    public String applyCompanyCert(CompanyCertBO companyCert) {
//        CompanyCertigierRequest companyCertifierRequest = new CompanyCertigierRequest();
//        companyCertifierRequest.setCompanyType(CompanyType.BUSINESS_LICENSE);
//        companyCertifierRequest.setCompanyName(companyCert.getCompanyName());
//        companyCertifierRequest.setRegCode(companyCert.getRegCode());
//        companyCertifierRequest.setOrgCode(companyCert.getOrgCode());
//        companyCertifierRequest.setTaxCode(companyCert.getTaxCode());
//        companyCertifierRequest.setCertigierMobile(companyCert.getCertifierMobile());
//        companyCertifierRequest.setCertigierIdentityType(IdentityType.ID_CARD);
//        companyCertifierRequest.setCertigierIdentity(companyCert.getCertifierIdentity());
//        companyCertifierRequest.setCertigierName(companyCert.getCertifierName());
//        companyCertifierRequest.setContactorMobile(companyCert.getContactorMobile());
//
//        ApplyCertResult certResult = esignThriftClient.applyCompanyCertByCertigier(companyCertifierRequest);
//        if (SUCCESS_CODE != certResult.getCode()) {
//            log.error("认证错误，code:{}, msg:{}", certResult.getCode(), certResult.getMessage());
//            throw new BusinessException(certResult.getMessage());
//        }
//        return certResult.getCustomerId();
        return StrUtil.EMPTY;
    }

    /**
     * 企业法人认证
     *
     * @param cert
     * @return
     */
    public String applyCompanyCertByLegalPerson(CompanyCertBO cert) {
//        CompanyLegalRequest personRequest = new CompanyLegalRequest();
//        personRequest.setCompanyType(CompanyType.BUSINESS_LICENSE);
//        personRequest.setCompanyName(cert.getCompanyName());
//        personRequest.setRegCode(cert.getRegCode());
//        personRequest.setOrgCode(cert.getOrgCode());
//        personRequest.setTaxCode(cert.getTaxCode());
//        personRequest.setLegalPersonMobile(cert.getCertifierMobile());
//        personRequest.setLegalPersonIdentityType(IdentityType.ID_CARD);
//        personRequest.setLegalPersonIdentity(cert.getCertifierIdentity());
//        personRequest.setLegalPersonName(cert.getCertifierName());
//        personRequest.setContactorMobile(cert.getContactorMobile());
//
//        ApplyCertResult certResult = esignThriftClient.applyCompanyCertByLegalPerson(personRequest);
//        if (SUCCESS_CODE != certResult.getCode()) {
//            log.error("认证错误，code:{}, msg:{}", certResult.getCode(), certResult.getMessage());
//            throw new BusinessException(certResult.getMessage());
//        }
//        return certResult.getCustomerId();
        return StrUtil.EMPTY;
    }

    /**
     * 个人认证
     *
     * @param cert
     * @return
     */
    public String applyPersonCert(PersonCertBO cert) {
//        PersonRequest personRequest = new PersonRequest();
//        personRequest.setPersonName(cert.getName());
//        personRequest.setIdentity(cert.getIdentity());
//        personRequest.setContactMobile(cert.getMobile());
//        personRequest.setIdentityType(IdentityType.ID_CARD);
//        personRequest.setContactEmail(cert.getEmail());
//        ApplyCertResult certResult = esignThriftClient.applyPersonCertV2(personRequest);
//
//        if (SUCCESS_CODE != certResult.getCode()) {
//            log.error("认证错误，code:{}, msg:{}", certResult.getCode(), certResult.getMessage());
//            throw new BusinessException(certResult.getMessage());
//        }
//        return certResult.getCustomerId();
        return StrUtil.EMPTY;
    }
}
