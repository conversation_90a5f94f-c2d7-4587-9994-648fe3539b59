package com.sankuai.shangou.seashop.user.common.remote.product.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/14 9:20
 */
@Setter
@Getter
public class CategoryTreeNodeBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 上级类目id
     */
    private Long parentCategoryId;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 完整的类目id路径
     */
    private List<Long> fullIds;

    /**
     * 子类目
     */
    private List<CategoryTreeNodeBo> children;

    /**
     * 类目的深度
     */
    private Integer depth;

    /**
     * 规格模板id
     */
    private Long specTemplateId;

    /**
     * 完整的类目名称
     */
    private String fullCategoryName;


}
