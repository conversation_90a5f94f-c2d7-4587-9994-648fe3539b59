package com.sankuai.shangou.seashop.user.common.remote.product;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class Product2BaseRemoteService {


//    @Resource
//    private ProductQueryThriftService productQueryThriftService;
//    @Resource
//    private ProductCmdThriftService productCmdThriftService;
//
//    public BaseResp offSaleAllProduct(BaseIdReq shopId) {
//        return ThriftResponseHelper.executeThriftCall(() -> productCmdThriftService.offSaleAllProduct(shopId));
//    }
//
//    public Boolean queryProductByTemplateId(ProductTemplateReq req) {
//        return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductByTemplateId(req));
//    }
//
//    public RecommendProductsResp queryRecommendProducts(Long userId) {
//        RecommendProductsReq req = new RecommendProductsReq();
//        req.setUserId(userId);
////        req.setShopId(shopId);
//        return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryRecommendProducts(req));
//    }
//
//    public List<ProductPageResp> queryProduct(QueryProductReq request) {
//        BasePageResp<ProductPageResp> basePageResp = ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProduct(request));
//        return basePageResp.getData();
//    }
//
//    public List<ProductPageResp> queryProduct(List<Long> productId) {
//        QueryProductReq queryProductReq = new QueryProductReq();
//        queryProductReq.setProductIds(productId);
//        //查询所有数据
//        queryProductReq.setPageSize(productId.size());
//        queryProductReq.setPageSize(1);
//        BasePageResp<ProductPageResp> pageResp = ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProduct(queryProductReq));
//        return pageResp.getData();
//    }
//
//    public ProductDetailResp queryProductDetail(Long baseIdReq) {
//        ProductQueryDetailReq productQueryDetailReq = new ProductQueryDetailReq();
//        productQueryDetailReq.setProductId(baseIdReq);
//        return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductDetail(productQueryDetailReq));
//    }
//
//    public List<CountProductTemplateResp> queryProductCountByTemplateId(CountProductTemplateReq template) {
//        return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductCountByTemplateId(template));
//    }
//
//    public ShopSaleCountsResp queryShopSaleCounts(Long shopId) {
//        QueryShopSaleCountsReq request = new QueryShopSaleCountsReq();
//        request.setShopId(shopId);
//        return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryShopSaleCounts(request));
//    }
}
