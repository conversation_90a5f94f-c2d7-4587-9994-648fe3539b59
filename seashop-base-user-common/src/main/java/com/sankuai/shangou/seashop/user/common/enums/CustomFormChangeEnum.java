package com.sankuai.shangou.seashop.user.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/02 11:00
 */
@Getter
public enum CustomFormChangeEnum {

    // 0-新增 1-编辑 2-删除
    ADD(0, "新增"),
    EDIT(1, "编辑"),
    DELETE(2, "删除");

    private final Integer code;
    private final String name;

    CustomFormChangeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
