package com.sankuai.shangou.seashop.user.common.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class SignServiceConfig {
//    @ThriftClientProxy(remoteAppKey = "com.sankuai.it.bsi.esign", timeout = 10000)
//    private EsignThriftService esignThriftService;
//
//    @Bean
//    public EsignThriftClient createSignService(@Value("${esign.appId}") String appId,
//                                               @Value("$KMS{esign.app.secret}") String appSecret) {
//        EsignThriftClient client = new EsignThriftClient();
//        client.setAppId(appId);
//        client.setAppSecret(appSecret);
//        client.setEsignService(esignThriftService);
//        return client;
//    }
}