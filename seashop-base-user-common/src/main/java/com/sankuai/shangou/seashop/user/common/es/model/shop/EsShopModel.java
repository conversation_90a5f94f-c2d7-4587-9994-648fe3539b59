package com.sankuai.shangou.seashop.user.common.es.model.shop;

import java.math.BigDecimal;
import java.util.List;

import com.sankuai.shangou.seashop.user.common.es.model.EsBase;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Getter
@Setter
public class EsShopModel extends EsBase<Long> {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺logo
     */
    private String logo;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 是否开启专属商家
     */
    private Boolean whetherOpenExclusiveMember;

    /**
     * 专属商家对象的用户信息
     */
    // private List<EsExclusiveMemberModel> exclusiveMemberList;
    private List<Long> exclusiveMemberIdList;

    /**
     * 订单量（已完成）
     */
    private Long orderSaleCount;

    /**
     * 商品销量（实际销量+虚拟销量）
     */
    private Long productSaleCount;

    /**
     * 商品实际销量
     */
    private Long productRealSaleCount;

    /**
     * 商品虚拟销量
     */
    private Long productVirtualSaleCount;

    /**
     * 品牌列表
     */
    // private List<EsShopBrandModel> shopBrandList;
    private List<Long> brandIdList;

    /**
     * 类目列表(三级)
     */
    // private List<EsBusinessCategoryModel> businessCategoryList;
    private List<Long> categoryIdList;

    /**
     * 一级类目列表
     */
    private List<Long> firstCategoryIdList;

    /**
     * 评分数量
     */
    private Long markCount;

    /**
     * 包装评分
     */
    private BigDecimal packMark;

    /**
     * 服务评分
     */
    private BigDecimal serviceMark;

    /**
     * 综合评分
     */
    private BigDecimal comprehensiveMark;

    /**
     * 排序号
     */
    private Integer serialNumber;

    @Override
    public Long getId() {
        return this.shopId;
    }
}
