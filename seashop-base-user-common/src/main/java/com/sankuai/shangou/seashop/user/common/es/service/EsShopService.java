package com.sankuai.shangou.seashop.user.common.es.service;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.user.common.constant.EsConstant;
import com.sankuai.shangou.seashop.user.common.es.model.shop.EsShopModel;
import com.sankuai.shangou.seashop.user.common.es.model.shop.EsShopParam;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Service
@Slf4j
public class EsShopService extends AbsEsService<EsShopParam, EsShopModel> {
    @Override
    public String getLogPrefix() {
        return "店铺搜索";
    }

    @Override
    public String getIndexName() {
        return EsConstant.INDEX_SHOP;
    }

    @Override
    public BoolQueryBuilder buildProductSearchCondition(EsShopParam queryBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (queryBo.getShopId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_SHOP_ID, queryBo.getShopId()));
        }
        if (StrUtil.isNotBlank(queryBo.getShopName())) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery(EsConstant.Shop.SEARCH_FIELD_SHOP_NAME, "*" + queryBo.getShopName() + "*"));
        }
        if (queryBo.getCategoryId() != null) {
            // 如果是检索最后一级类目
            if (queryBo.getSearchLastCategory() != null && queryBo.getSearchLastCategory()) {
                boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_CATEGORY_IDS, queryBo.getCategoryId()));
            }
            // 默认检索一级的
            else {
                boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_FIRST_CATEGORY_IDS, queryBo.getCategoryId()));
            }
        }
        if (CollectionUtils.isNotEmpty(queryBo.getCategoryIdList())) {
            // 如果是检索最后一级类目
            if (queryBo.getSearchLastCategory() != null && queryBo.getSearchLastCategory()) {
                boolQueryBuilder.must(QueryBuilders.termsQuery(EsConstant.Shop.SEARCH_FIELD_CATEGORY_IDS, queryBo.getCategoryIdList()));
            }
            // 默认检索一级的
            else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(EsConstant.Shop.SEARCH_FIELD_FIRST_CATEGORY_IDS, queryBo.getCategoryIdList()));
            }
        }
        if (queryBo.getBrandId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_BRAND_IDS, queryBo.getBrandId()));
        }
        if (queryBo.getShopStatus() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_SHOP_STATUS, queryBo.getShopStatus()));
        }
        if (CollUtil.isNotEmpty(queryBo.getShopStatusList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(EsConstant.Shop.SEARCH_FIELD_SHOP_STATUS, queryBo.getShopStatusList()));
        }
        // 专属商家 查询处理
        BoolQueryBuilder orCondition = QueryBuilders.boolQuery();
        orCondition.should(QueryBuilders.boolQuery().must(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_EXCLUSIVE, Boolean.FALSE)));
        if (queryBo.getUserId() != null) {
            orCondition.should(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_EXCLUSIVE, Boolean.TRUE))
                    .must(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_EXCLUSIVE_MEMBER_IDS, queryBo.getUserId())));
        }
        boolQueryBuilder.must(orCondition);
        return boolQueryBuilder;
    }

    @Override
    public List<FieldSortReq> defaultSortList() {
        FieldSortReq sort = new FieldSortReq();
        sort.setSort(EsConstant.Shop.SEARCH_FIELD_SERIAL_NUMBER);
        sort.setIzAsc(Boolean.FALSE);
        return Arrays.asList(sort);
    }
}
