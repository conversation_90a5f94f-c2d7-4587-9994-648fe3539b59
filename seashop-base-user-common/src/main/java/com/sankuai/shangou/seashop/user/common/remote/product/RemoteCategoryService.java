package com.sankuai.shangou.seashop.user.common.remote.product;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ShowStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryByIdsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryListReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryTreeReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryFirstCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryTreeResp;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.common.remote.product.model.CategoryTreeNodeBo;
import com.sankuai.shangou.seashop.user.common.remote.product.model.RemoteCategoryBo;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class RemoteCategoryService {


    @Resource
    private CategoryQueryFeign categoryQueryFeign;


    //public CategoryListResp queryCategoryList(QueryCategoryListReq queryCategoryListReq) {
    //    return ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryList(queryCategoryListReq));
    //}
    //
    //
    //public List<CategoryResp> querySimpleCategoryList(List<Long> categoryIdList) {
    //    QueryCategoryListReq queryCategoryListReq = new QueryCategoryListReq();
    //    queryCategoryListReq.setIds(categoryIdList);
    //    return queryCategoryList(queryCategoryListReq).getCategoryRespList();
    //}

    /**
     * 根据类目名称查询类目id
     *
     * @param name 类目名称
     * @return 类目id的集合
     */
    public List<Long> getCategoryIdByName(String name) {
        if (StringUtils.isBlank(name)) {
            return Collections.EMPTY_LIST;
        }

        QueryCategoryListReq req = new QueryCategoryListReq();
        req.setNameLike(name);
        CategoryListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryList(req));
        if (CollectionUtils.isEmpty(resp.getCategoryRespList())) {
            return Collections.EMPTY_LIST;
        }
        return resp.getCategoryRespList().stream().map(CategoryResp::getId).collect(Collectors.toList());
    }

    /**
     * 查询最后一级类目
     * 根据当前类目id 查询最后一级的类目列表
     *
     * @param categoryId 类目id
     * @return 类目列表
     */
    public List<RemoteCategoryBo> queryLastCategoryList(Long categoryId) {
        BaseIdReq req = new BaseIdReq();
        req.setId(categoryId);
        CategoryListResp categoryListResp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryLastCategoryList(req));
        List<RemoteCategoryBo> result = JsonUtil.copyList(categoryListResp.getCategoryRespList(), RemoteCategoryBo.class);
        // 提取类目全路径
        initCategoryIds(result);
        return result;
    }

    /**
     * 查询第一级类目
     * 根据当前类目id的集合 查询第一级类目
     *
     * @param categoryIds 类目id的集合
     * @return 类目列表
     */
    public List<RemoteCategoryBo> queryFirstCategoryList(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.EMPTY_LIST;
        }

        QueryFirstCategoryReq req = new QueryFirstCategoryReq();
        req.setCategoryIds(categoryIds);
        CategoryListResp categoryListResp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryFirstCategoryList(req));
        List<RemoteCategoryBo> result = JsonUtil.copyList(categoryListResp.getCategoryRespList(), RemoteCategoryBo.class);
        // 提取类目全路径
        initCategoryIds(result);
        return result;
    }

    /**
     * 根据类目id查询类目信息
     *
     * @param categoryIds 类目id
     * @return 类目列表
     */
    public List<RemoteCategoryBo> queryCategoryList(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.EMPTY_LIST;
        }

        categoryIds = categoryIds.stream().distinct().collect(Collectors.toList());
        List<RemoteCategoryBo> result = new ArrayList<>();
        List<List<Long>> subIdsArr = Lists.partition(categoryIds, CommonConstant.BATCH_QUERY_SIZE);
        subIdsArr.forEach(subIds -> {
            QueryCategoryListReq req = new QueryCategoryListReq();
            req.setIds(subIds);
            CategoryListResp categoryListResp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryList(req));
            result.addAll(JsonUtil.copyList(categoryListResp.getCategoryRespList(), RemoteCategoryBo.class));
        });
        // 提取类目全路径
        initCategoryIds(result);
        return result;
    }

    public List<CategoryResp> queryCategoryByTreeId(List<Long> categoryIds) {
        QueryCategoryTreeReq queryCategoryTreeReq = new QueryCategoryTreeReq();
        queryCategoryTreeReq.setIds(categoryIds);
        CategoryListResp categoryListResp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.getAllCategoryPath(queryCategoryTreeReq));
        return categoryListResp.getCategoryRespList();
    }

    /**
     * 计算类目全路径
     * 拆分path 为类目id的集合
     *
     * @param categoryList 类目列表
     */
    private void initCategoryIds(List<RemoteCategoryBo> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }
        categoryList.forEach(category -> {
            if (StringUtils.isEmpty(category.getPath())) {
                category.setCategoryIds(Collections.EMPTY_LIST);
                return;
            }

            String[] arr = category.getPath().split(CommonConstant.CATEGORY_PATH_SPLIT);
            category.setCategoryIds(Arrays.asList(arr).stream().map(Long::valueOf).collect(Collectors.toList()));
        });
    }

    /**
     * 查询类目树
     *
     * @param categoryIds 类目id的集合
     * @return 类目树
     */
    public List<CategoryTreeNodeBo> queryCategoryTreeWithParent(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.EMPTY_LIST;
        }

        QueryCategoryTreeReq req = new QueryCategoryTreeReq();
        req.setIds(categoryIds);
        CategoryTreeResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTreeWithParent(req));
        return JsonUtil.parseArray(resp.getResult(), CategoryTreeNodeBo.class);
    }

    public CategoryResp queryCategoryById(Long categoryId) {
        BaseIdReq req = new BaseIdReq();
        req.setId(categoryId);
        CategoryListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryLastCategoryList(req));
        if (CollectionUtils.isEmpty(resp.getCategoryRespList())) {
            return null;
        }
        return resp.getCategoryRespList().get(0);
    }

    public List<CategoryTreeNodeBo> queryCategoryTree(ShowStatusEnum showStatusEnum, Boolean filterNoChildren) {
        QueryCategoryReq req = new QueryCategoryReq();
        if (showStatusEnum != null) {
            req.setShowStatus(showStatusEnum.getValue());
        }
        req.setFilterNoChildren(filterNoChildren);
        CategoryTreeResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTree(req));
        return JsonUtil.parseArray(resp.getResult(), CategoryTreeNodeBo.class);
    }

    public List<Long> getLastCategoryIds(Long categoryId) {
        if (categoryId == null) {
            return Collections.emptyList();
        }

        BaseIdReq req = new BaseIdReq();
        req.setId(categoryId);
        CategoryListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryLastCategoryList(req));
        if (resp == null || CollectionUtils.isEmpty(resp.getCategoryRespList())) {
            return Collections.emptyList();
        }
        return resp.getCategoryRespList().stream().map(CategoryResp::getId).collect(Collectors.toList());
    }

    public List<CategoryResp> getFirstCategoryList(List<Long> categoryIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }

        List<CategoryResp> categoryList = new ArrayList<>();

        categoryIds = categoryIds.stream().distinct().collect(Collectors.toList());
        List<List<Long>> subCategoryIds = Lists.partition(categoryIds, CommonConstant.BATCH_QUERY_SIZE);
        subCategoryIds.forEach(subIds -> {
            QueryFirstCategoryReq remoteReq = new QueryFirstCategoryReq();
            remoteReq.setCategoryIds(subIds);
            CategoryListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryFirstCategoryList(remoteReq));
            categoryList.addAll(resp.getCategoryRespList());
        });

        // 根据id 去重
        Set<Long> existCategoryIds = new HashSet<>();
        return categoryList.stream().filter(category -> existCategoryIds.add(category.getId())).collect(Collectors.toList());
    }

    public List<RemoteCategoryBo> queryCacheCategoryList(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.EMPTY_LIST;
        }

        categoryIds = categoryIds.stream().distinct().collect(Collectors.toList());
        QueryCategoryByIdsReq req = new QueryCategoryByIdsReq();
        req.setCategoryIds(categoryIds);
        req.setUseCache(true);
        CategoryListResp categoryListResp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.getCategoryList(req));
        List<RemoteCategoryBo> result = JsonUtil.copyList(categoryListResp.getCategoryRespList(), RemoteCategoryBo.class);
        // 提取类目全路径
        initCategoryIds(result);
        return result;
    }

    /**
     * 获取递归子分类ID列表（包含自身）
     * 通过查询所有分类，然后根据path进行过滤来实现
     *
     * @param parentId 父分类ID
     * @return 子分类ID列表
     */
    public List<Long> getRecursiveChildIds(Long parentId) {
        if (parentId == null) {
            return Collections.EMPTY_LIST;
        }

        // 先获取父分类信息
        List<RemoteCategoryBo> parentCategories = queryCategoryList(Arrays.asList(parentId));
        if (CollectionUtils.isEmpty(parentCategories)) {
            return Collections.EMPTY_LIST;
        }

        RemoteCategoryBo parentCategory = parentCategories.get(0);
        String parentPath = parentCategory.getPath();

        // 构建查询条件：查询所有分类，然后过滤出子分类
        QueryCategoryListReq req = new QueryCategoryListReq();
        // 不设置具体条件，查询所有分类
        CategoryListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryList(req));

        if (resp == null || CollectionUtils.isEmpty(resp.getCategoryRespList())) {
            return Arrays.asList(parentId); // 至少返回自身
        }

        Set<Long> childIds = new HashSet<>();
        childIds.add(parentId); // 包含自身

        // 遍历所有分类，找出子分类
        for (CategoryResp category : resp.getCategoryRespList()) {
            if (category.getPath() != null && category.getPath().startsWith(parentPath + "|")) {
                childIds.add(category.getId());
            }
        }

        return new ArrayList<>(childIds);
    }

    public List<Long> queryAlreadyRemoveCategoryIds() {
        return ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryAlreadyRemoveCategoryIds());
    }
}
