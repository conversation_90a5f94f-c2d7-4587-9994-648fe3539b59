package com.sankuai.shangou.seashop.user.common.es.model.shop;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EsShopParam {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 店铺状态
     */
    private List<Integer> shopStatusList;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 排序字段
     */
    private List<FieldSortReq> sortList;

    /**
     * 类目ID列表
     */
    private List<Long> categoryIdList;

    /**
     * 是否是筛选最后一级类目
     */
    private Boolean searchLastCategory;
}
