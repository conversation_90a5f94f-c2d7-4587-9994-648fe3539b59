package com.sankuai.shangou.seashop.user.common.remote.trade;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/11 14:19
 */
@Service
@Slf4j
public class TradeProductRemoteService {

    //@Resource
    //private TradeProductQueryThriftService tradeProductQueryThriftService;
    //
    //public List<QueryProductByIdListResp> queryProductByIdList(ProductIdsReq request) {
    //    return ThriftResponseHelper.executeThriftCall(()->tradeProductQueryThriftService.queryProductByIdList(request));
    //}
}
