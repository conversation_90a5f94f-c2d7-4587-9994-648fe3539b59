package com.sankuai.shangou.seashop.user.common.enums.account.power;

/**
 * 最近花费时间 枚举 0.1周内 1.2周内 2.1个月内 3.1个月前 4.2个月前 5.3个月前 6.6个月前
 */
public enum RecentlySpentTimeEnum {
    OneWeek(0, "1周内"),
    TwoWeek(1, "2周内"),
    OneMonthWithin(2, "1个月内"),
    OneMonth(3, "1个月前"),
    <PERSON><PERSON><PERSON><PERSON>(4, "2个月前"),
    <PERSON><PERSON><PERSON><PERSON>(5, "3个月前"),
    <PERSON><PERSON><PERSON><PERSON>(6, "6个月前");

    private final Integer code;
    private final String desc;

    RecentlySpentTimeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RecentlySpentTimeEnum getByCode(Integer code) {
        for (RecentlySpentTimeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}