package com.sankuai.shangou.seashop.user.common.constant;

/**
 * <AUTHOR>
 * @date 2023/12/09 11:25
 */
public class LockConstant {


    /**
     * 锁前缀
     */
    private static final String LOCK_PREFIX = "seashop:user:lock:";

    /**
     * 经营类目申请审核锁
     */
    public static final String BUSINESS_CATEGORY_APPLY_AUDIT_LOCK = LOCK_PREFIX + "business_category_apply:audit:";

    /**
     * 经营类目申请审核(埋点)
     */
    public static final String BUSINESS_CATEGORY_APPLY_AUDIT_SCENE = LOCK_PREFIX + "scene:" + "business_category_apply:audit";

    /**
     * 编辑店铺银行信息锁
     */
    public static final String EDIT_SHOP_BANK_INFO_LOCK = LOCK_PREFIX + "modify_shop_bank:modify:";
    /**
     * 店铺申请审核锁
     */
    public static final String BUSINESS_SHOP_APPLY_AUDIT_LOCK = LOCK_PREFIX + "shop_apply:audit:";

    /**
     * 店铺申请审核(埋点)
     */
    public static final String BUSINESS_SHOP_APPLY_AUDIT_SCENE = LOCK_PREFIX + "scene:" + "shop_apply:audit";


    /**
     * 防止重复授权自动注册时（埋点）
     */
    public static final String SCENE_USER_WX_LOGIN_SCENE = LOCK_PREFIX + "scene:" + "wxMini:login";

    /**
     * 防止重复授权自动注册时，创建多条用户信息
     */
    public static final String LOCK_USER_WX_LOGIN_LOCK = LOCK_PREFIX +"login:wx_mini_phone:login";

}
