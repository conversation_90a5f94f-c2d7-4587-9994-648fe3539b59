package com.sankuai.shangou.seashop.user.common.enums.account.power;

/**
 * 商家购买次数 枚举 0.0次 1.1次 2.2次 3.3次 4.4次
 */
public enum PurchasesEnum {
    ZeroTimes(0, "0次"),
    OneTimes(1, "1次+"),
    TwoTimes(2, "2次+"),
    ThreeTimes(3, "3次+"),
    FourTimes(4, "4次+");

    private final Integer code;
    private final String desc;

    PurchasesEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PurchasesEnum getByCode(Integer purchases) {
        for (PurchasesEnum value : values()) {
            if (value.getCode().equals(purchases)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
///// <summary>
//    /// 商家购买次数
//    /// </summary>
//    public enum Purchases : int
//    {
//        /// <summary>
//        /// 0次
//        /// </summary>
//        [Description("0次")]
//        ZeroTimes = 0,
//
//        /// <summary>
//        /// 1次
//        /// </summary>
//        [Description("1次+")]
//        OneTimes = 1,
//
//        /// <summary>
//        /// 2次
//        /// </summary>
//        [Description("2次+")]
//        TwoTimes = 2,
//
//        /// <summary>
//        /// 3次
//        /// </summary>
//        [Description("3次+")]
//        ThreeTimes = 3,
//
//        /// <summary>
//        /// 4次
//        /// </summary>
//        [Description("4次+")]
//        FourTimes = 4,
//    }