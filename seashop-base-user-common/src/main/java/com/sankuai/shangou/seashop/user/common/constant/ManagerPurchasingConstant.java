package com.sankuai.shangou.seashop.user.common.constant;

import java.math.BigDecimal;

/**
 * @description: 供应商hi管理员常量类
 * @author: LXH
 **/
public class ManagerPurchasingConstant {
    //0.0-500 1.500-1000 2.1000-3000 3.3000+
    // 500
    public static final BigDecimal AMOUNT_OF_CONSUMPTION_500 = new BigDecimal(500);
    // 1000
    public static final BigDecimal AMOUNT_OF_CONSUMPTION_1000 = new BigDecimal(1000);
    // 3000
    public static final BigDecimal AMOUNT_OF_CONSUMPTION_3000 = new BigDecimal(3000);
    // MAX
    public static final BigDecimal AMOUNT_OF_CONSUMPTION_MAX = new BigDecimal(999999999);
}
