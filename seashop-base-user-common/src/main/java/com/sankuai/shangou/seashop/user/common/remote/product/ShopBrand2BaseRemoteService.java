package com.sankuai.shangou.seashop.user.common.remote.product;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Service
@Slf4j
public class ShopBrand2BaseRemoteService {

    //@Resource
    //private ShopBrandQueryThriftService shopBrandQueryThriftService;
    //
    //public ShopBrandListResp queryShopBrandForList(QueryShopBrandReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> shopBrandQueryThriftService.queryShopBrandForList(request));
    //}
}
