package com.sankuai.shangou.seashop.user.common.enums;

/**
 * <AUTHOR>
 */
public class FreightEnum {

    public enum TemplateFreeType {

        SELF_DEFINE(1, "自定义模板"),
        FREE(2, "卖家承担运费(包邮)"),

        ;

        private final Integer code;
        private final String desc;

        TemplateFreeType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum FreeGroupConditionType {

        BUY_COUNT(1, "购买件数"),
        AMOUNT(2, "金额"),
        BOTH_COUNT_AND_AMOUNT(3, "件数+金额"),

        ;

        private final Integer code;
        private final String desc;

        FreeGroupConditionType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static FreeGroupConditionType getByCode(Integer code) {
            for (FreeGroupConditionType value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum TemplateValuationMethod {

        WEIGHT(1, "重量"),
        VOLUME(2, "体积"),
        QUANTITY(0, "件数"),

        ;

        private final Integer code;
        private final String desc;

        TemplateValuationMethod(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
