package com.sankuai.shangou.seashop.user.common.service;


import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.user.common.enums.CertifierTypeEnum;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.common.facade.CertFacade;
import com.sankuai.shangou.seashop.user.common.facade.ContractFacade;
import com.sankuai.shangou.seashop.user.common.facade.MtCompany;
import com.sankuai.shangou.seashop.user.common.model.*;
import com.sankuai.shangou.seashop.user.common.util.ContractTemplateUtils;
import com.sankuai.shangou.seashop.user.common.util.EncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZoneOffset;

/**
 * <AUTHOR>
 * 合同服务
 */
@Service
@Slf4j
public class ContractService {
    /**
     * 合同
     */
    @Resource
    private ContractFacade contractFacade;
    /**
     * 认证服务
     */
    @Resource
    private CertFacade certFacade;
    @Resource
    private ContractTemplateUtils contractTemplateUtils;

    public static final String FIRST_SIGNER_KEYWORD = "甲方盖章处";
    public static final String SECOND_SIGNER_KEYWORD = "乙方盖章处";

    /**
     * 创建企业合同
     *
     * @param companyContract
     * @return
     */
    public String createCompanyContract(CompanyContractBO companyContract, String customerId) {
        AssertUtil.throwIfNull(companyContract, "合同不能为空");
        AssertUtil.throwIfNull(companyContract.getCertifierTypeEnum(), "认证类型不能为空");
        AssertUtil.throwIfTrue(CertifierTypeEnum.PERSON.equals(companyContract.getCertifierTypeEnum()), "认证类型错误");
        //创建合同
        ContractBO contract = buildCompanyContractDto(companyContract, customerId);
        return contractFacade.createContract(contract);
    }

    public String getCompanyCustomerId(CompanyContractBO companyContract) {
        //先认证拿到客户ID
        return CertifierTypeEnum.COMPANY_LEGAL_PERSON.equals(companyContract.getCertifierTypeEnum()) ?
                certFacade.applyCompanyCertByLegalPerson(buildCompanyCertDto(companyContract)) :
                certFacade.applyCompanyCert(buildCompanyCertDto(companyContract));
    }

    public String getPersonCustomerId(PersonContractBO personContract) {
        //先认证拿到客户ID
        return certFacade.applyPersonCert(buildPersonCertDto(personContract));
    }

    /**
     * 创建个人合同
     *
     * @param personContract
     * @return
     */
    public String createPersonContract(PersonContractBO personContract ,String customerId) {
        //创建合同
        ContractBO contract = buildPersonContractDto(personContract, customerId);
        return contractFacade.createContract(contract);
    }

    /**
     * 签署合同
     *
     * @param sign
     * @return
     */
    public Boolean signContract(ContractSignBO sign) {
        //解密合同ID
        String contractId = sign.getContractId();
        //供应商签署合同
        SignContractBO signerSignContractDto = SignContractBO.builder()
                .contractId(contractId)
                .customerId(sign.getCustomerId())
                .customerIdCode(sign.getCustomerIdCode())
                .customerName(sign.getCustomerName())
                .keyWord(FIRST_SIGNER_KEYWORD)
                .build();
        OwnerCompany mtCompany = MtCompany.getCreateCompany();
        SignContractBO mtCompanySignContractDto = SignContractBO.builder()
                .contractId(contractId)
                .customerId(mtCompany.getCustomId())
                .customerIdCode(mtCompany.getRegCode())
                .customerName(mtCompany.getCompanyName())
                .keyWord(SECOND_SIGNER_KEYWORD)
                .build();
        try {
            contractFacade.signContract(signerSignContractDto);
            //美团方签署
            contractFacade.signContract(mtCompanySignContractDto);
            //归档
            contractFacade.archiveContract(contractId);
        }catch (Exception e){
            log.error("sign contract error,",e);
            throw new BusinessException(UserResultCodeEnum.SIGN_CONTRACT_ERROR);
        }

        return true;
    }

    private String decrypt(String sourceId) {
        try {
            String contractId = EncryptUtils.decrypt(sourceId);
            AssertUtil.throwIfNull(contractId, "合同ID不存在");
            return contractId;
        } catch (Exception e) {
            log.error("decrypt contract id error,", e);
            throw new BusinessException("解密合同ID失败");
        }
    }

    /**
     * 预览合同
     *
     * @param preview
     * @return
     */
    public String previewContract(ContractPreviewBO preview) {
        //解密合同ID
        String contractId = preview.getContractId();
        String customerId = preview.getCustomerId();
        return contractFacade.previewContract(contractId, customerId);
    }

    /**
     * 下载合同
     *
     * @param preview
     * @return
     */
    public byte[] downloadContract(ContractPreviewBO preview) {
        //解密合同ID
        String contractId = preview.getContractId();
        String customerId = preview.getCustomerId();
        return contractFacade.downloadContract(contractId);
    }

    private ContractBO buildPersonContractDto(PersonContractBO personContract, String customerId) {
        return ContractBO.builder()
                .contractData(contractTemplateUtils.createContractContent(personContract))
                .customerId(customerId)
                .expireAt(personContract.getExpireAt().toInstant(ZoneOffset.of("+8")).toEpochMilli())
                .build();
    }

    private PersonCertBO buildPersonCertDto(PersonContractBO personContract) {
        return PersonCertBO.builder()
                .name(personContract.getName())
                .mobile(personContract.getMobile())
                .identity(personContract.getIdentity())
                .email(personContract.getEmail())
                .build();
    }


    private ContractBO buildCompanyContractDto(CompanyContractBO companyContract, String customerId) {
        return ContractBO.builder()
                .contractData(contractTemplateUtils.createContractContent(companyContract))
                .customerId(customerId)
                .expireAt(companyContract.getExpireAt().toInstant(ZoneOffset.of("+8")).toEpochMilli())
                .build();
    }


    private CompanyCertBO buildCompanyCertDto(CompanyContractBO companyContract) {
        return CompanyCertBO.builder()
                .companyName(companyContract.getName())
                .orgCode(companyContract.getOrgCode())
                .regCode(companyContract.getRegCode())
                .taxCode(companyContract.getTaxCode())
                .certifierMobile(companyContract.getCertifierMobile())
                .certifierName(companyContract.getCertifierName())
                .certifierIdentity(companyContract.getCertifierIdentity())
                .contactorMobile(companyContract.getContactPerson().getMobile())
                .build();
    }
}
