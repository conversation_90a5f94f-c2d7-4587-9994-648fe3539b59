package com.sankuai.shangou.seashop.user.common.service;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * @description:
 * @author: LXH
 **/
@Service
@Slf4j
public class AfsService {
    @Value("${afs.regionId:cn-hangzhou}")
    private String regionId;
    @Value("${afs.accessKeyId:LTAI5tReS2RUu628HbZMKE7M}")
    private String accessKeyId;
    @Value("${afs.accessKeySecret:******************************}")
    private String accessKeySecret;
    @Value("${afs.appKey:FFFF0N1N000000005E30}")
    private String appKey;

    IAcsClient client;

    @PostConstruct
    void init() {
        try {
            IClientProfile profile= DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            client = new DefaultAcsClient(profile);
            DefaultProfile.addEndpoint(regionId, regionId, "afs", "afs.aliyuncs.com");
        }catch (Exception e){
            log.error("初始化阿里云人机识别异常",e);
        }
    }

    //验证
    public boolean verify(String remoteIp, String scene, String sessionId, String sig, String token) {
        AuthenticateSigRequest request = new AuthenticateSigRequest();
        request.setSessionId(sessionId);// 会话ID。必填参数，从前端success回调中获取，不可更改。
        request.setSig(sig);// 签名串。必填参数，从前端success回调中获取，不可更改。
        request.setToken(token);// 请求唯一标识。必填参数，从前端success回调中获取，不可更改。
        request.setScene(scene);// 场景标识。必填参数，与前端页面填写数据一致，不可更改。
        request.setAppKey(appKey);// 应用类型标识。必填参数，后端填写。
        request.setRemoteIp(remoteIp);// 客户端IP。必填参数，后端填写。
        try {
            //response的code枚举：100验签通过，900验签失败。
            AuthenticateSigResponse response = client.getAcsResponse(request);

            return response != null && CommonConstant.AFS_SUCCESS_CODE.equals(response.getCode());
        } catch (Exception e) {
            log.error("人机识别异常",e);
            return false;
        }
    }




}
