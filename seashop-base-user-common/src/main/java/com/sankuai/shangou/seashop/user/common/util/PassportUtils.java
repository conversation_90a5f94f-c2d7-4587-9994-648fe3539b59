package com.sankuai.shangou.seashop.user.common.util;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.google.common.base.CharMatcher;
import com.google.common.base.Strings;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.user.common.config.UserLionConfigClient;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;

/**
 * <AUTHOR>
 */
@Component
public class PassportUtils {
    @Resource
    private UserLionConfigClient userLionConfigClient;
    private static final CharMatcher ACCOUNT_LOGIN_NAME_CHAR_MATCHER = CharMatcher.ascii().and(CharMatcher.javaLetterOrDigit().or(CharMatcher.is('_')));

    /**
     * 检查密码和账户名是否合法
     *
     * @param password
     * @param login
     */
    public void checkPasswordLegal(String password, String login) {
        checkPassword(password);
        if (!Strings.isNullOrEmpty(login) && password.equals(login)) {
            throw new BusinessException(UserResultCodeEnum.ACCOUNT_NAME_AND_PASSWORD_CANNOT_BE_THE_SAME);
        }
        if (Strings.isNullOrEmpty(login)
                || login.length() < 5
                || login.length() > 20
                || !ACCOUNT_LOGIN_NAME_CHAR_MATCHER.matchesAllOf(login)) {
            throw new BusinessException(UserResultCodeEnum.ACCOUNT_NAME_LENGTH_ERROR);
        }
    }

    /**
     * 检查密码是否合法
     *
     * @param password
     */
    public void checkPassword(String password) {
        if (Strings.isNullOrEmpty(password)
                || !checkPasswordLengthLegal(password.length())
                || CharMatcher.javaDigit().matchesNoneOf(password)
                || CharMatcher.javaLetter().matchesNoneOf(password)) {
            throw new BusinessException(UserResultCodeEnum.PASSWORD_FORMAT_ERROR);

        }
        if (userLionConfigClient.getPasswordBlackList().contains(password)) {
            throw new BusinessException(UserResultCodeEnum.PASSWORD_IN_BLACKLIST);
        }
        if (password.startsWith("http")) {
            throw new BusinessException(UserResultCodeEnum.PASSWORD_CANNOT_START_WITH_HTTP);
        }
    }

    private static boolean checkPasswordLengthLegal(int length) {
        return length >= 6
                && length <= 16;
    }
}
