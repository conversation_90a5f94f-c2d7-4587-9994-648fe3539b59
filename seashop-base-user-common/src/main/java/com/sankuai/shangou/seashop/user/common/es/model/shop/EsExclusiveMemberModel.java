package com.sankuai.shangou.seashop.user.common.es.model.shop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;

/**
 * @author: lhx
 * @date: 2024/1/9/009
 * @description:
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EsExclusiveMemberModel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 供应商Id
     */
    private Long shopId;

    /**
     * 商家Id
     */
    private Long userId;

}
