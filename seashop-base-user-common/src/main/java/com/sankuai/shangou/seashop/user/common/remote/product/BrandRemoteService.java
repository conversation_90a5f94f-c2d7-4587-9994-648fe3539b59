package com.sankuai.shangou.seashop.user.common.remote.product;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/04/18 19:21
 */
@Service
@Slf4j
public class BrandRemoteService {

    //@Resource
    //private BrandQueryThriftService brandQueryThriftService;
    //
    //public List<BrandDto> getBrandList(List<Long> brandIds) {
    //    if (CollectionUtils.isEmpty(brandIds)) {
    //        return Collections.emptyList();
    //    }
    //
    //    brandIds = brandIds.stream().distinct().collect(Collectors.toList());
    //    BatchQueryBrandReq req = new BatchQueryBrandReq();
    //    req.setIdList(brandIds);
    //    req.setUseCache(true);
    //    BrandListResp resp = ThriftResponseHelper.executeThriftCall(() -> brandQueryThriftService.queryBrandList(req));
    //    return resp.getBrandList();
    //}

}
