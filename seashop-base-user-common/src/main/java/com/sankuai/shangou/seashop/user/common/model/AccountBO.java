package com.sankuai.shangou.seashop.user.common.model;

import lombok.*;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class AccountBO {
    /**
     * 账户ID，内部
     */
    private String accountId;
    /**
     * 账户登录名
     */
    private String accountLoginName;
    /**
     * 账户昵称
     */
    private String accountName;
    /**
     * 绑定手机号
     */
    private String mobile;
    /**
     * 密码，密文
     */
    private String password;
    /**
     * ep对应的账户ID
     */
    private Integer epAccountId;
}
