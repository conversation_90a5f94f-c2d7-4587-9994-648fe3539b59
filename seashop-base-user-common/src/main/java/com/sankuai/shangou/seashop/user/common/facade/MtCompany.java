package com.sankuai.shangou.seashop.user.common.facade;

import com.sankuai.shangou.seashop.user.common.model.CompanyCertBO;
import com.sankuai.shangou.seashop.user.common.model.OwnerCompany;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "mt.company")
public class MtCompany {
    private String companyName;
    private String regCode;
    private String certifierName;
    private String certifierIdentity;
    private String certifierMobile;
    private String contactMobile;

    @Resource
    private CertFacade certFacade;

    private static OwnerCompany company;

    public static OwnerCompany getCreateCompany() {
        return company;
    }

    @PostConstruct
    public void mtCompanyInit() {
        CompanyCertBO mtCompany = new CompanyCertBO();
        mtCompany.setCompanyName(companyName);
        mtCompany.setRegCode(regCode);
        mtCompany.setContactorMobile(contactMobile);
        //设置法人信息
        mtCompany.setCertifierName(certifierName);
        mtCompany.setCertifierIdentity(certifierIdentity);
        mtCompany.setCertifierMobile(certifierMobile);
        //认证，美团
        String customId = certFacade.applyCompanyCert(mtCompany);
        company = OwnerCompany.builder()
                .companyName(companyName)
                .regCode(regCode)
                .contactorMobile(contactMobile)
                .certifierName(certifierName)
                .certifierIdentity(certifierIdentity)
                .certifierMobile(certifierMobile)
                .customId(customId)
                .build();
    }
}
