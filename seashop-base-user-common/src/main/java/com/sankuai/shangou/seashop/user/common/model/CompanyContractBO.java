package com.sankuai.shangou.seashop.user.common.model;

import lombok.*;
import lombok.experimental.SuperBuilder;
import com.sankuai.shangou.seashop.user.common.enums.CertifierTypeEnum;

/**
 * <AUTHOR>
 */
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class CompanyContractBO extends BaseContractBO {
    /**
     * 合同名称
     */
    private String title;
    /**
     * 描述
     */
    private String description;
    /**
     * 企业工商注册号 必填， 如三证合一后则为统一社会信用代码
     */
    private String regCode;
    /**
     * 企业税务登记证号， 如三证合一后则为统一社会信用代码
     */
    private String taxCode;
    /**
     * 企业组织机构代码， 如三证合一后则为统一社会信用代码
     */
    private String orgCode;

    /**
     * 认证人名称，法人代表或授权人名称
     * <p>
     * 使用[\\u4E00-\\u9FA5]{2,5}(?:·[\\u4E00-\\u9FA5]{2,5})*正则验证
     */
    private String certifierName;
    /**
     * 认证人身份证证件号，法人代表或授权人证件号码(可以填联系人的证件号码)
     */
    private String certifierIdentity;
    /**
     * 认证人手机号，法人代表或授权人手机号码，全数字
     */
    private String certifierMobile;
    /**
     * 认证类型
     */
    private CertifierTypeEnum certifierTypeEnum;
}
