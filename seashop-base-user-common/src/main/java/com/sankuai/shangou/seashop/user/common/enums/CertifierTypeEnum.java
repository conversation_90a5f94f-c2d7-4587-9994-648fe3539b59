package com.sankuai.shangou.seashop.user.common.enums;



/**
 * <AUTHOR>
 */
public enum CertifierTypeEnum {
    COMPANY_LEGAL_PERSON("0", "企业法人"),

    COMPANY_AUTHORIZER("1", "企业授权人"),

    PERSON("2", "个人");

    private final String code;
    private final String name;

    CertifierTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static CertifierTypeEnum fromCode(String code) {
        for (CertifierTypeEnum certifierType : CertifierTypeEnum.values()) {
            if (certifierType.getCode().equals(code)) {
                return certifierType;
            }
        }
        //默认为授权人类型
        return COMPANY_AUTHORIZER;
    }
}
