package com.sankuai.shangou.seashop.user.common.enums;

import com.sankuai.shangou.seashop.base.boot.enums.Code;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
public enum UserResultCodeEnum implements Code {

    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * 4xx-公共异常
     * 2xx-商家相关
     * 0xx-店铺相关
     * 5xx-管理员相关
     * </pre>
     */
    VERIFICATION_CODE_ERROR(40040410, "验证码校验失败"),
    CONTACT_INFO_ERROR(40040411, "联系方式不正确"),
    IMAGE_CODE_ERROR(********, "图片验证码不正确"),

    THE_USERNAME_ALREADY(********, "用户名已存在"),
    THE_ROLE_HAS_ADMIN(********, "该权限组还有管理员，不允许删除"),
    ROLE_NOT_EXIST(********, "权限组不存在"),
    ROLE_NAME_EXIST(********, "权限组已存在"),

    SHOP_NOT_FIND(********, "店铺不存在"),
    INCORRECT_BUSINESS_TYPE(********, "业务类型不正确"),
    ADA_PAY_STATUS_ERROR(********, "汇付状态不正确"),
    BANK_ADDRESS_INFO_ERROR(********, "银行地址信息不正确"),
    CONTRACT_NOT_EXIST(********, "合同不存在"),
    CONTRACT_STATUS_ERROR(********, "合同状态不正确"),
    SIGN_CONTRACT_ERROR(********, "签署合同失败"),
    APPLY_NOT_FIND(********, "申请不存在"),
    BUSINESS_CATEGORY_NOT_EXIST(********, "店铺类目不存在"),
    REFUSE_REASON_NOT_NULL(********, "拒绝原因不能为空"),
    TAX_ID_CANNOT_BE_EMPTY(********, "税号不能为空"),
    INVOICE_TITLE_CANNOT_BE_EMPTY(********, "发票抬头不能为空"),
    INVOICE_TITLE_ALREADY_EXISTS(********, "发票抬头已存在"),
    SHOP_FROZEN(********, "店铺已冻结"),
    SHOP_NAME_ALREADY_EXISTS(********, "店铺名称已存在"),
    FAILED_TO_ADD_CATEGORY(********, "添加类目失败"),
    THE_APPLY_MOT_BELONG_TO_THIS_STORE(********, "审核记录不属于该店铺"),
    THE_AUDIT_RECORD_HAS_BEEN_APPROVED(********, "该审核记录已审核"),
    THE_REASON_FOR_REJECTION_CANNOT_BE_EMPTY(40040021, "拒绝原因不能为空"),
    FAILED_TO_GENERATE_APP_SECRET(40040022, "生成appSecret失败"),
    CREATE_QR_ERROR(40040023, "生成二维码失败"),
    HI_CHAT_DOMAIN_NOT_FIND(40040024, "hiChat域名不存在"),
    ENCRYPT_ERROR(40040025, "加密失败"),
    DECRYPT_ERROR(40040026, "解密失败"),
    CERTIFICATES_UPLOAD_ERROR(40040027, "证件上传失败"),

    NEW_PASSWORD_CANNOT_BE_THE_SAME_AS_THE_OLD_PASSWORD(40040028, "新密码不能和旧密码相同"),
    NO_SUCH_USER(40050001, "账户不存在"),
    USER_NEEDS_TO_GET_EP_ID(40050002, "用户需要获取epid"),
    PASSWORD_ERROR(40050003, "密码错误,请输入正确的密码"),
    MANAGER_HAS_TRANSFERRED(40050004, "管理员已经转移过了"),
    THE_USERNAME_IS_ILLEGAL(40050005, "用户名不合法"),
    USER_HAS_TRANSFERRED(40050006, "用户已经转移过了"),
    GET_OPENID_ERROR(40050007, "获取openid失败"),
    MANAGER_ALREADY_EXISTS(40050008, "管理员名称已存在"),
    PASSWORD_ERROR_THREE_TIMES(40050009, "您已连续输错3次密码，当天连续累计输错6次会冻结一小时。连续输错6次后1小时内不能再进行迁移"),
    PASSWORD_ERROR_SIX_TIMES(40050010, "您已连续输错6次密码，请一个小时后再试。"),
    OLD_PASSWORD_ERROR(40050011, "原密码错误,请输入正确的密码"),
    MANAGER_PHONE_ALREADY_EXISTS(********, "手机号已被其他管理员绑定"),

    COMPANY_NAME_NOT_NULL(********, "公司名称不能为空"),
    COMPANY_ADDRESS_NOT_NULL(********, "公司地址不能为空"),
    COMPANY_REGION_NOT_NULL(********, "公司所在地不能为空"),
    REAL_NAME_NOT_NULL(********, "真实姓名不能为空"),
    CONTRACT_EXIST(********, "合同已存在"),
    CONTACT_INFO_ALREADY_EXISTS(********, "联系方式已被绑定"),
    CREATE_ADA_ACCOUNT_ERROR(********, "创建汇付账号失败"),

    BUSINESS_CATEGORY_ALREADY_EXISTS(********, "经营类目已存在"),
    USER_NOT_EXIST(********, "用户不存在"),

    // 存在待审核的经营类目
    EXIST_PENDING_BUSINESS_CATEGORY(********, "存在待审核的经营类目"),

    USER_HAS_SHOP(********, "供应商不能注销"),
    USER_HAS_MANAGER(********, "【{}】为店铺管理员，无法注销"),
    USER_IS_FREEZE(********, "用户已被冻结,如需解冻请联系平台运营人员处理,解冻后才能进行迁移"),
    PASSWORD_CONFIRMATION_IS_INCORRECT(********, "两次输入的密码不一致"),
    SHOP_STATUS_ERROR(********, "店铺状态错误"),
    SHOP_BUSINESS_TYPE_ERROR(********, "已经提交过审核的，不能修改店铺id"),
    USER_HAS_SHOP_MEMBER(********, "有供应商的商家不能直接冻结"),
    LABEL_NAME_EXIST(********, "标签名称已存在"),
    SHOP_AGREEMENT_APPLY_ID_NOT_NULL(********, "店铺协议申请id不能为空"),
    //"密码格式错误"
    PASSWORD_FORMAT_ERROR(********, "密码格式错误"),
    //"密码在黑名单中"
    PASSWORD_IN_BLACKLIST(********, "密码在黑名单中"),
    //"密码不能以http开头"
    PASSWORD_CANNOT_START_WITH_HTTP(********, "密码不能以http开头"),
    //账户名和密码不能相同
    ACCOUNT_NAME_AND_PASSWORD_CANNOT_BE_THE_SAME(********, "账户名和密码不能相同"),
    //账户名需要在5-20位
    ACCOUNT_NAME_LENGTH_ERROR(********, "账户名需要在5-20位"),
    LABEL_NOT_EXIST(********, "标签不存在"),
    USER_REGISTER_FAILED(********, "用户注册失败"),
    PHONE_FORMAT_ERROR(********, "手机号格式错误"),
    SMS_CODE_EXIST(********, "验证码已发送,请稍后再试"),
    ADAPAY_AUDIT_STATUS_ERROR(********, "该店铺等待汇付审核中，不允许再次编辑"),
    SHOP_BANK_INFO_EDIT_LOCK(********, "该店铺银行信息正在编辑中，不允许再次编辑，请稍后再试！"),
    EP_ACCOUNT_ID_NOT_EXIST(********, "EP账户不存在"),
//    用户名或密码不正确
    USER_NAME_OR_PASSWORD_ERROR(********, "用户名或密码不正确"),

    ADA_AUDITING(********, "汇付正在审核中，请稍后再试"),
    ;


    private int code;
    private String msg;

    UserResultCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    @Override
    public Integer value() {
        return code;
    }

    @Override
    public String desc() {
        return msg;
    }
}
