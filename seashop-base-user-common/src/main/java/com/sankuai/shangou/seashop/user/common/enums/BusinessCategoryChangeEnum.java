package com.sankuai.shangou.seashop.user.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/02 9:49
 */
@Getter
public enum BusinessCategoryChangeEnum {

    // 0-申请 1-审核通过(新增) 2-审核拒绝 3-编辑 4-补充资料 5-冻结 6-删除经营类目
    APPLY(0, "申请"),
    AUDIT_PASS(1, "审核通过"),
    AUDIT_REFUSE(2, "审核拒绝"),
    EDIT(3, "编辑"),
    SUPPLY_DATA(4, "补充资料"),
    FREEZE(5, "冻结"),
    DELETE(6, "删除"),
    ;

    private final Integer code;
    private final String name;

    BusinessCategoryChangeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
