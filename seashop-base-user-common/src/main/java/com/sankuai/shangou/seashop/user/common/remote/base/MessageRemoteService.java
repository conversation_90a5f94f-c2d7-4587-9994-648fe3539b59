package com.sankuai.shangou.seashop.user.common.remote.base;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.thrift.core.MessageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.ContactReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SmsBodyReq;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.user.common.config.UserLionConfigClient;
import com.sankuai.shangou.seashop.user.common.constant.LeafConstant;
import com.sankuai.shangou.seashop.user.common.constant.SmsConstant;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息发送远程接口
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageRemoteService {
//
    @Resource
    private MessageCMDFeign messageCMDFeign;

    @Resource
    private SquirrelUtil squirrelUtil;

    @Resource
    private LeafService leafService;
    @Resource
    private UserLionConfigClient userLionConfigClient;

    @Value("${himall.sms.white-code:}")
    private String whiteSmsCode;
    @Value("${himall.email.white-code:}")
    private String whiteEmailCode;

    public void sendSms(Long code, String param, String mobile) {
        // 发送短信
        SmsBodyReq smsBodyReq = new SmsBodyReq();
        smsBodyReq.setTemplateId(code);
        smsBodyReq.setParam(param);
        smsBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
        // 设置收信人
        ContactReq contactReq = new ContactReq();
        contactReq.setMobile(mobile);
        smsBodyReq.setContactList(Collections.singletonList(contactReq));
        ThriftResponseHelper.executeThriftCall(() -> messageCMDFeign.sendSms(smsBodyReq));
    }

    //批量发送短信
//    public void batchSendSms(SmsEnum.Template template,  List<String> mobile) {
//        // 发送短信
//        SmsBodyReq smsBodyReq = new SmsBodyReq();
//        smsBodyReq.setTemplateId(template.getCode());
//        smsBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
//        // 设置收信人
//        List<ContactReq> contactReqList = mobile.stream().map(e -> {
//            ContactReq contactReq = new ContactReq();
//            contactReq.setMobile(e);
//            return contactReq;
//        }).collect(Collectors.toList());
//        smsBodyReq.setContactList(contactReqList);
//        ThriftResponseHelper.executeThriftCall(() -> messageCMDThriftService.sendSms(smsBodyReq));
//    }

//    public void sendEmail(String subject, String body, List<String> email) {
//        EmailBodyReq emailBodyReq = new EmailBodyReq();
//        emailBodyReq.setBody(body);
//        emailBodyReq.setSendFrom("test?");
//        emailBodyReq.setSubject(subject);
//        emailBodyReq.setRequestId(leafService.generateNoBySnowFlake(LeafConstant.KEY_USER_NO));
//        // 设置收信人
//        List<ContactReq> contactReqList = email.stream().map(e -> {
//            ContactReq contactReq = new ContactReq();
//            contactReq.setEmail(e);
//            return contactReq;
//        }).collect(Collectors.toList());
//        emailBodyReq.setContactList(contactReqList);
//
//        ThriftResponseHelper.executeThriftCall(() -> messageCMDThriftService.sendEmail(emailBodyReq));
//    }


//    /**
//     * 发送短信验证码
//     * @param mobile 手机号
//     */
//    public void sendSmsCode(String mobile) {
//        //生成随机6位数字验证码
//        String code = RandomUtil.randomNumbers(6);
//        Map<String, String> params = new HashMap<>();
//        params.put(SmsConstant.BASE_SMS_CODE_KEY, code);
//        String paramStr = JSONUtil.toJsonStr(params);
//        //缓存验证码
//        if (squirrelUtil.get(SmsConstant.SMS_CODE_KEY + mobile) != null) {
//            throw new BusinessException(UserResultCodeEnum.SMS_CODE_EXIST);
//        }
//        squirrelUtil.set(SmsConstant.SMS_CODE_KEY + mobile, code, SmsConstant.SMS_CODE_EXPIRE_TIME);
//        sendSms(userLionConfigClient.getVerificationCode(), paramStr, mobile);
//        }

    /**
     * 发送邮箱验证码
     * @param eMail 邮箱
     */
//    public void sendEmailCode(String eMail) {
//        //生成随机6位数字验证码
//        String code = RandomUtil.randomNumbers(6);
//        String message = StrUtil.format(SmsConstant.EMAIL_CODE_CONTENT, code);
//        //缓存验证码
//        if (squirrelUtil.get(SmsConstant.EMAIL_CODE_KEY + eMail) != null) {
//            throw new BusinessException(UserResultCodeEnum.SMS_CODE_EXIST);
//        }
//        squirrelUtil.set(SmsConstant.EMAIL_CODE_KEY + eMail, code, SmsConstant.SMS_CODE_EXPIRE_TIME);
//        sendEmail(SmsConstant.EMAIL_CODE_SUBJECT, message, Collections.singletonList(eMail));
//    }

    /**
     * 校验短信验证码
     * @param code 验证码
     * @param mobile 手机号
     * @return 是否正确
     */
    public boolean checkSmsCode(String mobile,String code) {
        // 白名单
        if (StrUtil.isNotEmpty(whiteSmsCode) && whiteSmsCode.equals(code)) {
            return true;
        }

        String cacheCode = (String) squirrelUtil.get(SmsConstant.SMS_CODE_KEY + mobile);
        boolean success = StrUtil.equals(code, cacheCode);
        // 验证成功, 则清除缓存
        if (success) {
            squirrelUtil.deleteKey(SmsConstant.SMS_CODE_KEY + mobile);
        }
        return success;
    }

    /**
     * 校验邮箱验证码
     * @param code 验证码
     * @param eMail 邮箱
     * @return 是否正确
     */
    public boolean checkEmailCode(String eMail, String code) {
        // 白名单
        if (StrUtil.isNotEmpty(whiteEmailCode) && whiteEmailCode.equals(code)) {
            return true;
        }

        String cacheCode = (String) squirrelUtil.get(SmsConstant.EMAIL_CODE_KEY + eMail);
        return StrUtil.equals(code, cacheCode);
    }

    public String imageVerificationCode(String UUID) {
        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(100, 50);
        squirrelUtil.set(SmsConstant.IMAGE_CODE_KEY + UUID, lineCaptcha.getCode(), SmsConstant.SMS_CODE_EXPIRE_TIME);
        return lineCaptcha.getImageBase64Data();
    }

    public void checkImageVerificationCode(String imageKey, String imageCode) {
        String cacheCode = (String) squirrelUtil.get(SmsConstant.IMAGE_CODE_KEY + imageKey);
        if (!StrUtil.equalsIgnoreCase(imageCode, cacheCode)) {
            throw new BusinessException(UserResultCodeEnum.IMAGE_CODE_ERROR);
        }
    }
}
