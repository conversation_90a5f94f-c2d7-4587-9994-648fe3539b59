package com.sankuai.shangou.seashop.user.common.enums.account;

/**
 * @description: 用户类型枚举类 0email 1phone
 */
public enum ContactUserTypeEnum {
    EMAIL(0, "email"),
    PHONE(1, "phone");

    private Integer code;
    private String desc;

    ContactUserTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ContactUserTypeEnum getByCode(Integer code) {
        for (ContactUserTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
