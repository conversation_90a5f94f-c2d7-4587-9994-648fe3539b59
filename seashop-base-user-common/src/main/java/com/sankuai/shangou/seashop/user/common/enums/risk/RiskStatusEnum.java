package com.sankuai.shangou.seashop.user.common.enums.risk;

/**
 * 除商品外的其余的风控使用该枚举
 *
 * <AUTHOR>
 * @date 2023/12/05 11:27
 */
public enum RiskStatusEnum {

    WAIT_AUDIT(0, "待审核"),
    PASS(1, "审核通过"),
    REFUSE(2, "审核拒绝");

    private final Integer code;
    private final String desc;

    RiskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (RiskStatusEnum riskStatus : RiskStatusEnum.values()) {
            if (riskStatus.getCode().equals(code)) {
                return riskStatus.getDesc();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
