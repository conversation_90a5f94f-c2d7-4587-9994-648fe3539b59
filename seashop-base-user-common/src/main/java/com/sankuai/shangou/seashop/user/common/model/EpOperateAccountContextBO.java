package com.sankuai.shangou.seashop.user.common.model;

import lombok.*;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class EpOperateAccountContextBO {
    /**
     * 区分应用
     */
    private String appKey;
    /**
     * 操作人ip
     */
    private String operatorIp;
    /**
     * 2:Web / H5,6:微信小程序
     */
    private Integer platform;
    /**
     * ua
     */
    private String userAgent;
    /**
     * 域名
     */
    private String domain;
    /**
     * 操作的商家ep账号ID
     */
    private Integer bizEpAccountId;
    /**
     * 操作类型
     * 1：修改密码，0：未知，7：创建账号
     */
    private Integer operateType;
}
