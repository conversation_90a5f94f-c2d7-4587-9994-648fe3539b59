package com.sankuai.shangou.seashop.user.common.remote.product.model;

import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/29 20:15
 */
@Getter
@Setter
public class RemoteCategoryBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 上级类目id
     */
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    private BigDecimal commissionRate;

    /**
     * 类目的深度
     */
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    private String path;

    /**
     * 规格模板Id
     */
    private Long specTemplateId;

    /**
     * 是否已删除
     */
    private Boolean whetherDelete;

    /**
     * 自定义表单Id
     */
    private Long customFormId;

    /**
     * 类目全路径
     */
    private String fullCategoryName;

    /**
     * 类目全路径
     */
    private List<Long> categoryIds;

    /**
     * 保证金
     */
    private BigDecimal cashDeposit;
    /**
     * 是否显示
     */
    private Boolean whetherShow;

}
