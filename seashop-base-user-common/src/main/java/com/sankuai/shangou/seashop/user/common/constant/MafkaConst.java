package com.sankuai.shangou.seashop.user.common.constant;

/**
 * @description:
 * @author: LXH
 **/
public class MafkaConst {
    // mafka命名空间，用于区分不同的业务，这里固定为waimai
    public static final String DEFAULT_NAMESPACE = "waimai";
    public final static String TOPIC_MEMBER_CREATE = "com.sankuai.sjst.ecom.epassportweb.signup.done";
    public final static String TOPIC_MEMBER_CREATE_GROUP = "seashop_ep_register_msg_consumer";
    public final static String TOPIC_MEMBER_UPDATE = "com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify";
    public final static String TOPIC_MEMBER_UPDATE_GROUP = "seashop_ep_account_modify_msg_consumer";

    /**
     * 企业账号申请回调topic
     * topic：seashop_corp_member_audit_topic
     */
    public static final String CORP_MEMBER_AUDIT_TOPIC = "seashop_corp_member_audit_topic";


    /**
     * 企业账号申请回调消费者组
     * group：seashop_corp_member_audit_consumer
     */
    public static final String CORP_MEMBER_AUDIT_CONSUMER = "seashop_corp_member_audit_consumer";

    /**
     * 经营类目变动topic
     */
    public static final String TOPIC_BUSINESS_CATEGORY_CHANGE = "seashop_business_category_change_topic";

    /**
     * 经营类目变动消费者组
     */
    public static final String GROUP_BUSINESS_CATEGORY_CHANGE = "seashop_business_category_change_consumer";

    /**
     * 自定义表单变动topic
     */
    public static final String TOPIC_CUSTOM_FORM_CHANGE = "seashop_custom_form_change_topic";

    /**
     * 自定义表单变动消费组
     */
    public static final String GROUP_CUSTOM_FORM_CHANGE = "seashop_custom_form_change_consumer";

}
