package com.sankuai.shangou.seashop.user.common.model;

import lombok.*;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class ContractTemplateProperties {
    /**
     * 开始年份
     */
    private String startYear;
    /**
     * 开始月份
     */
    private int startMonth;
    /**
     * 开始日
     */
    private int startDay;
    /**
     * 结束年份
     */
    private String endYear;
    /**
     * 结束月份
     */
    private int endMonth;
    /**
     * 结束日
     */
    private int endDay;
    /**
     * 签约主体名称
     */
    private String signerName;
    /**
     * 签约主体联系人名称
     */
    private String signerContactName;
    /**
     * 创建主体法人名称
     */
    private String creatorCertifierName;
    /**
     * 创建主体名称
     */
    private String creatorName;

}
