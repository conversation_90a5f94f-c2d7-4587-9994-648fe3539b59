package com.sankuai.shangou.seashop.user.common.util;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.user.common.facade.MtCompany;
import com.sankuai.shangou.seashop.user.common.model.BaseContractBO;
import com.sankuai.shangou.seashop.user.common.model.ContractTemplateProperties;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Component
public class ContractTemplateUtils {
    @Resource
    private PdfFileUtils PdfFileUtils;
    private static final int CONTRACT_SIZE = 300000;
    private static final String CLASSPATH_FILENAME = "HSServiceContract.ftl";

    private static final String CLASSPATH_PATH = "/template/";


    public byte[] createContractContent(BaseContractBO contract) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream(CONTRACT_SIZE)) {
            String htmlString = FreemarkerUtils.loadFtlHtml(CLASSPATH_PATH, CLASSPATH_FILENAME, generateTemplateProperties(contract));
            PdfFileUtils.html2pdf(outputStream, htmlString);
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("创建合同错误：", e);
            throw new BusinessException("创建合同错误");
        }
    }

    private static ContractTemplateProperties generateTemplateProperties(BaseContractBO contract) {
        return ContractTemplateProperties.builder()
                .startYear(String.valueOf(contract.getCreateTime().getYear()))
                .startMonth(contract.getCreateTime().getMonthValue())
                .startDay(contract.getCreateTime().getDayOfMonth())
                .endYear(String.valueOf(contract.getExpireAt().getYear()))
                .endMonth(contract.getExpireAt().getMonthValue())
                .endDay(contract.getExpireAt().getDayOfMonth())
                .signerName(contract.getName())
                .signerContactName(contract.getContactPerson().getName())
                .creatorName(MtCompany.getCreateCompany().getCompanyName())
                .creatorCertifierName(MtCompany.getCreateCompany().getCertifierName())
                .build();
    }
}
