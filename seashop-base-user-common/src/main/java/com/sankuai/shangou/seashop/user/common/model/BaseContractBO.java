package com.sankuai.shangou.seashop.user.common.model;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
@SuperBuilder
public abstract class BaseContractBO {
    /**
     * 企业名称/个人名称
     */
    private String name;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 过期时间
     */
    private LocalDateTime expireAt;
    /**
     * 联系人
     */
    private ContactPersonBO contactPerson;
}
