package com.sankuai.shangou.seashop.user.common.remote.promotion;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author： liweisong
 * @create： 2023/12/13 9:21
 */
@Service
@Slf4j
public class ShopUserPromotionRemoteService {

    //@Resource
    //private ShopUserPromotionCmdThriftService shopUserPromotionCmdThriftService;
    //
    //public BaseResp offSaleAllPromotion(BaseIdReq shopId){
    //    return ThriftResponseHelper.executeThriftCall(()->shopUserPromotionCmdThriftService.offSaleAllPromotion(shopId));
    //}
}
