package com.sankuai.shangou.seashop.user.common.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Getter;


/**
 * todo 修改成自己项目中的 lion/mcc 配置
 * config 说明文档 <a href=https://km.sankuai.com/page/550965600></a>
 *
 * <AUTHOR>
 */

@Component
@Getter
public class UserLionConfigClient {

    @Value("${password.black.list:5000}")
    private List<String> passwordBlackList;
    //保证金通知
    @Value("${deposit.remind:2007554}")
    private Long depositRemind;
    //续签合同
    @Value("${re.sign.contract:2007555}")
    private Long reSignContract;
    //签合同和保证金
    @Value("${sign.contract.and.deposit.remind:2009179}")
    private Long signContractAndDepositRemind;
    //重签合同
    @Value("${renewalContract:2007556}")
    private Long renewalContract;
    //验证码
    @Value("${verification.code:2007552}")
    private Long verificationCode;
    //审核失败
    @Value("${review.failure.reminder:2007587}")
    private Long reviewFailureReminder;
    //审核通过
    @Value("${review.approved.reminder:2007553}")
    private Long reviewApprovedReminder;
    //注册成功
    @Value("${register.success.notice:2007968}")
    private Long registerSuccessNotice;
    //注册成功
    @Value("${migration.account.notice:2008630}")
    private Long migrationAccountNotice;

}