package com.sankuai.shangou.seashop.user.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Configuration
@Getter
public class EncryptConfig {

    /**
     * venus socket
     */
    @Value("${seashop.aes.secret:'ae125efkk4454eeff444ferfkny6oxi8'}")
    private String aesSecret;

}
