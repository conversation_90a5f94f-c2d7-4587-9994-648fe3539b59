package com.sankuai.shangou.seashop.user.common.remote.order;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author： liweisong
 * @create： 2023/12/13 10:44
 */
@Service
@Slf4j
public class OrderRemoteService {

    //@Resource
    //private OrderQueryThriftService orderQueryThriftService;
    //
    //public EachStatusCountResp queryEachStatusCount(Long userId) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryThriftService.queryEachStatusCount(userId));
    //}
    //
    //public OrderInfoDto queryLastOrderInfo(Long userId) {
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryThriftService.queryLastOrderInfo(userId));
    //}
    //
    //public OrderDetailResp queryDetail(String orderId) {
    //    QueryOrderDetailReq queryOrderDetailReq = new QueryOrderDetailReq();
    //    queryOrderDetailReq.setOrderId(orderId);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryThriftService.queryDetail(queryOrderDetailReq));
    //}
    //
    //public CountFlashOrderAndProductResp countFlashOrderAndProduct(Long shopId) {
    //    ShopIdReq shopIdReq = new ShopIdReq();
    //    shopIdReq.setShopId(shopId);
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryThriftService.countFlashOrderAndProduct(shopIdReq));
    //}
    //
    //public OrderStatisticsResp getOrderStatistics(Long userId) {
    //    OrderStatisticsMemberReq orderStatisticsMemberReq = new OrderStatisticsMemberReq();
    //    orderStatisticsMemberReq.setUserId(userId);
    //    orderStatisticsMemberReq.setBeginTime(DateUtil.parse("2000-12-13 00:00:00", "yyyy-MM-dd HH:mm:ss"));
    //    orderStatisticsMemberReq.setEndTime(DateUtil.date());
    //    return ThriftResponseHelper.executeThriftCall(() -> orderQueryThriftService.getOrderStatisticsByMember(orderStatisticsMemberReq));
    //}
}
