package com.sankuai.shangou.seashop.user.common.enums.shop;

/**
 * @author： liweisong
 * @create： 2023/12/14 17:47
 */
public class ShopEnum {

    public enum ShopAuditStatus {

        // 0-待审核、1-审核通过、2-审核不通过
        DEFAULT(0, "默认"),
        UNUSABLE(1, "不可用"),
        WAITAUDIT(2, "待审核"),
        WAITPAY(3, "待付款"),
        REFUSE(4, "审核拒绝"),
        WAITCONFIRM(5, "待确认"),
        FREEZE(6, "冻结"),
        OPEN(7, "开启");


        private Integer code;
        private String desc;

        ShopAuditStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ShopAuditStatus getByCode(Integer code) {
            for (ShopAuditStatus value : ShopAuditStatus.values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
