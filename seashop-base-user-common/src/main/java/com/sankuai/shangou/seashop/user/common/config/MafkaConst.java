package com.sankuai.shangou.seashop.user.common.config;

/**
 * @description:
 * @author: LXH
 **/
public class MafkaConst {
    // mafka命名空间，用于区分不同的业务，这里固定为waimai
    public static final String DEFAULT_NAMESPACE = "waimai";
    // 订单变更topic
    public static final String TOPIC_ORDER_CHANGE = "seashop_order_change_topic";
    // 订单售后topic
    public static final String TOPIC_ORDER_REFUND = "seashop_order_refund_topic";
    // 订单售后消费者组
    public static final String GROUP_ORDER_CHANGE = "seashop_order_change_user_consumer";
    // 订单变更消费者组
    public static final String GROUP_ORDER_REFUND = "seashop_order_refund_user_consumer";

    // 商品收藏topic
    public static final String TOPIC_FAVORITE_PRODUCT = "seashop_favorite_product_topic";
    // 商品收藏消费者组
    public static final String GROUP_FAVORITE_PRODUCT = "seashop_favorite_product_user_consumer";
}
