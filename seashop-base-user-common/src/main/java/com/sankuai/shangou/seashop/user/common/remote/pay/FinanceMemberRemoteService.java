package com.sankuai.shangou.seashop.user.common.remote.pay;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCorpMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UpdatePaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaySettleAccountResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.MemberCmdFeign;
import com.sankuai.shangou.seashop.user.common.constant.ManagerConstant;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 财务用户服务
 * @author: LXH
 **/
@Service
@Slf4j
public class FinanceMemberRemoteService {
    @Resource
    private MemberCmdFeign memberCmdFeign;

    public boolean queryMember(PayMemberReq request) {
        //捕捉mem_id已存在异常
        try {
            ThriftResponseHelper.executeThriftCall(() -> memberCmdFeign.queryMember(request));
            return true;
        }
        catch (BusinessException e) {
            log.error("查询个人账号失败", e);
            return false;
        }
    }

    public BaseResp createMember(PayMemberReq request) {
        //捕捉mem_id已存在异常
        try {
            return ThriftResponseHelper.executeThriftCall(() -> memberCmdFeign.createMember(request));
        } catch (BusinessException e) {
            log.error("创建个人账号失败", e);
            if (e.getMessage().equals(ManagerConstant.MEM_ID_EXIST)) {
                //如果是mem_id已存在异常，直接返回成功
                return BaseResp.of();
            } else {
                throw e;
            }
        }
    }

    public PaySettleAccountResp createSettleAccount(PaySettleAccountReq request) {
        try {
            return ThriftResponseHelper.executeThriftCall(() -> memberCmdFeign.createSettleAccount(request));
        } catch (BusinessException e) {
            log.error("创建结算账号失败", e);
            if (e.getMessage().equals(ManagerConstant.SETTLE_ACCOUNT_EXIST)) {
                //如果是mem_id已存在异常，直接返回成功
                return null;
            } else {
                throw e;
            }
        }

    }


    public PaySettleAccountResp updateSettleAccount(UpdatePaySettleAccountReq request) {
        try {
            return ThriftResponseHelper.executeThriftCall(() -> memberCmdFeign.updateSettleAccount(request));
        }
        catch (BusinessException e) {
            log.error("创建结算账号失败", e);
            if (e.getMessage().equals(ManagerConstant.SETTLE_ACCOUNT_EXIST)) {
                //如果是mem_id已存在异常，直接返回成功
                return null;
            }
            else {
                throw e;
            }
        }

    }

    public BaseResp createCompanyMember(PayCorpMemberReq request) {
        try {
            return ThriftResponseHelper.executeThriftCall(() -> memberCmdFeign.createCompanyMember(request));
        } catch (BusinessException e) {
            log.error("创建企业汇付账号失败", e);
            if (ManagerConstant.MEM_ID_EXIST.equals(e.getMessage())) {
                //如果是mem_id已存在异常，直接返回成功
                return BaseResp.of();
            } if (ManagerConstant.USER_STATUS_ERROR.equals(e.getMessage())) {
                //这种情况是汇付审核中，我们不能直接保存用户新提交的数据，只能返回提示用户
                //"汇付正在审核中，请稍后再试";
                throw new BusinessException(UserResultCodeEnum.ADA_AUDITING);
            }else {
                throw e;
            }
        }
    }

    public Integer updateCompanyMember(PayCorpMemberReq request) {
        try {
            Boolean flag = ThriftResponseHelper.executeThriftCall(() -> memberCmdFeign.updateCompanyMember(request));
            if (flag){
                return ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode();
            }
            return ShopEnum.AdapayAuditStatus.AdapayPending.getCode();
        } catch (BusinessException e) {
            log.error("更新企业汇付账号失败", e);
            throw e;
        }
    }


}
