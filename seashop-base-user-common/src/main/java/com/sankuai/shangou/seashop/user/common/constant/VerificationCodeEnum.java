package com.sankuai.shangou.seashop.user.common.constant;

public enum VerificationCodeEnum {
    SETTLE_IN(0, "入驻"),
    BINDING(1, "绑定"),
    ;
    private final Integer code;
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    VerificationCodeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //根据code获取枚举
    public static VerificationCodeEnum getEnumByCode(Integer code) {
        for (VerificationCodeEnum verificationType : VerificationCodeEnum.values()) {
            if (verificationType.getCode().equals(code)) {
                return verificationType;
            }
        }
        return null;
    }
}
