package com.hishop.himall.report;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.util.Collections;

/**
 * <AUTHOR>
 */
public class ReportGenerate {

    public static void main(String[] args) {
        DataSourceConfig.Builder dataSourceConfig = new DataSourceConfig.Builder(
                "******************************************************************************************************************************",
                "himall",
                "bozIRn5S7hH6C1");
        String outputDir = "D:\\Java\\himall\\himall-report\\himall-report-dao\\src\\main";
        FastAutoGenerator.create(dataSourceConfig)
                .injectionConfig(builder -> builder.beforeOutputFile((tableInfo, stringObjectMap) -> {
                    System.out.println(tableInfo.getName() + "-" + stringObjectMap);
                }))
                // 全局配置
                .globalConfig((scanner, builder) -> builder
                        .disableOpenDir()
                        .outputDir(outputDir + "\\java")
                        .disableServiceInterface()
                        .author("fangen"))
                // 包配置
                .packageConfig((scanner, builder) -> builder
                        .moduleName("report.dao")
                        .entity("domain")
                        .mapper("mapper")
                        .xml("mapper")
                        //.controller("web.controller")
                        .parent("com.hishop.himall")
                        .pathInfo(Collections.singletonMap(OutputFile.xml, outputDir + "\\resources\\mapper")))
                // 策略配置
                .strategyConfig((scanner, builder) -> builder
                        .addInclude(scanner.apply("请输入表名，多个表名用,隔开"))
                        .entityBuilder()
                        .disableSerialVersionUID()
                        .enableLombok()
                        .enableRemoveIsPrefix()
                        .addTableFills(new Column("create_time", FieldFill.INSERT))
                        .addTableFills(new Column("update_time", FieldFill.INSERT))
                        .serviceBuilder()
                )
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }

}
