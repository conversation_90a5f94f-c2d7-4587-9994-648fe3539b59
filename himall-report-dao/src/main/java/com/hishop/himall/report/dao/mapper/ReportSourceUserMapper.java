package com.hishop.himall.report.dao.mapper;

import com.hishop.himall.report.dao.domain.ReportSourceUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface ReportSourceUserMapper extends BaseMapper<ReportSourceUser> {

    void deleteUserIds(@Param("userIds") List<Long> userIds);

    void batchInsert(@Param("list") List<ReportSourceUser> list);

}
