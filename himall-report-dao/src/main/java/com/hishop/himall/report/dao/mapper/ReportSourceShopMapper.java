package com.hishop.himall.report.dao.mapper;

import com.hishop.himall.report.dao.domain.ReportSourceShop;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface ReportSourceShopMapper extends BaseMapper<ReportSourceShop> {

    void deleteShopIds(@Param("shopIds") List<Long> shopIds);

    void batchInsert(@Param("values") List<ReportSourceShop> values);
}
