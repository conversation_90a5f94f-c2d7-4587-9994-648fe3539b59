package com.hishop.himall.report.dao.mapper;

import com.hishop.himall.report.dao.domain.ReportSourceVisit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.models.TradeSource;
import com.hishop.himall.report.dao.models.TradeSummary;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
public interface ReportSourceVisitMapper extends BaseMapper<ReportSourceVisit> {

    /**
     * 商品访问统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getProductVisitSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                             @Param("platform") Integer platform);


    /**
     * 门店访问统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getShopVisitSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                          @Param("shops") List<Long> shops, @Param("platform") Integer platform);

    void batchInsert(@Param("values") List<ReportSourceVisit> values);
}
