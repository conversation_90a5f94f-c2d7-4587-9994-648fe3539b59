package com.hishop.himall.report.dao.mapper;

import com.hishop.himall.report.dao.domain.ReportSourceCart;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.models.TradeSource;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 基础数据源表-加购数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
public interface ReportSourceCartMapper extends BaseMapper<ReportSourceCart> {

    /**
     * 获取商品加购汇总
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getProductCartSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                            @Param("products") List<Long> products,
                                            @Param("platform") Integer platform);

    /**
     * 获取店铺加购汇总
     *
     * @param start
     * @param end
     * @param byShop
     * @return
     */
    List<TradeSource> getShopCartSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                         @Param("platform") Integer platform,
                                         @Param("shops") List<Long> shops,
                                         @Param("byShop") boolean byShop);

    void deleteIds(@Param("ids") List<Long> ids);

    void batchInsert(@Param("list") List<ReportSourceCart> list);
}
