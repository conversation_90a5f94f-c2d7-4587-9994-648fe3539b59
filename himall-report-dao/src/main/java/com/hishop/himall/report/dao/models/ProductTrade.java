package com.hishop.himall.report.dao.models;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class ProductTrade {
    private Integer productId;
    private String productName;
    private String thumbnailUrl;
    private Integer cartUsers;
    private Integer cartQuantity;
    private Integer orderUsers;
    private Integer orderOrders;
    private Integer orderQuantity;
    private BigDecimal orderAmount;
    private Integer paymentUsers;
    private Integer paymentOrders;
    private Integer paymentQuantity;
    private BigDecimal paymentAmount;
    private Integer applyOrders;
    private Integer applyUsers;
    private Integer refundOrders;
    private Integer refundUsers;
    private Integer refundQuantity;
    private Integer refundAmount;
    private Integer visitsUsers;
    private Integer visitsCount;
    private Integer followUsers;

}
