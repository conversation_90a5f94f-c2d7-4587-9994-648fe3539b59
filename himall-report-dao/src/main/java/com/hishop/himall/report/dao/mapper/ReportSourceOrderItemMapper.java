package com.hishop.himall.report.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.domain.ReportSourceOrderItem;
import com.hishop.himall.report.dao.models.TradeSource;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface ReportSourceOrderItemMapper extends BaseMapper<ReportSourceOrderItem> {
    //用户支付统计
    List<TradeSource> getUserPaymentSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                            @Param("platform") Integer platform,
                                            @Param("products") List<Long> products);

    //商品支付统计
    List<TradeSource> getProductPaymentSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                               @Param("platform") Integer platform,
                                               @Param("products") List<Long> products);

    //商品下单统计
    List<TradeSource> getProductOrderSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                             @Param("platform") Integer platform,
                                             @Param("products") List<Long> products,
                                             @Param("shops") List<Long> shops);


    /**
     * 发货包裹统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getShopDeliverPackageSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                                   @Param("shops") List<Long> shops);

    /**
     * 签收包裹统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getShopFinishPackageSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                                  @Param("shops") List<Long> shops);

    /**
     * 商品支付统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getShopPaymentSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                            @Param("platform") Integer platform,
                                            @Param("shops") List<Long> shops,
                                            @Param("byShop") Boolean byShop);


    void deleteOrderItemIds(@Param("orderItemIds") List<Long> orderItemIds);

    void batchInsert(@Param("list") List<ReportSourceOrderItem> list);
}
