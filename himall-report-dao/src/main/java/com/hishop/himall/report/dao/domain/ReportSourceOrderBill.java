package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 源数据表-订单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Getter
@Setter
@TableName("report_source_order_bill")
public class ReportSourceOrderBill {

    @TableId("bill_id")
    private Long billId;
    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 门店ID
     */
    private Long shopId;


    private String expressCompanyName;

    private String expressCompanyCode;
    private String shipOrderNumber;

    /**
     * 下单时间
     */
    private Date deliverTime;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
