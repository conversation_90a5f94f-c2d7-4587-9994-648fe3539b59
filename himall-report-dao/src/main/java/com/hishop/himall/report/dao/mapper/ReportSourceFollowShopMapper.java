package com.hishop.himall.report.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.domain.ReportSourceFollowShop;
import com.hishop.himall.report.dao.models.ReportSourceFollowShopDelete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 基础数据源-关注记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface ReportSourceFollowShopMapper extends BaseMapper<ReportSourceFollowShop> {

    void deleteByFollow(@Param("list") List<ReportSourceFollowShopDelete> list);

    void batchInsert(@Param("list") List<ReportSourceFollowShop> list);

}
