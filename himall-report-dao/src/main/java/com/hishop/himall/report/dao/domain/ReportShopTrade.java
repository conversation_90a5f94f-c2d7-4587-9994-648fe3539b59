package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 门店交易统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Getter
@Setter
@TableName("report_shop_trade")
public class ReportShopTrade {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计范围
     */
    @TableField("`range`")
    private String range;
    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺省份
     */
    private Integer provinceId;

    /**
     * 来源渠道
     */
    private Integer platform;

    /**
     * 用户总数
     */
    private Integer userTotal;
    /**
     * 新用户数
     */
    private Integer userNew;

    /**
     * 领券用户数
     */
    private Integer userCoupons;

    /**
     * 加购用户数
     */
    private Integer cartUsers;
    /**
     * 下单数
     */
    private Integer orderOrders;
    /**
     * 下单件数
     */
    private Integer orderUsers;

    /**
     * 下单金额
     */
    private BigDecimal orderAmount;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 支付用户数
     */
    private Integer paymentUsers;
    /**
     * 支付件数
     */
    private Integer paymentQuantity;
    /**
     * 支付订单数
     */
    private Integer paymentOrders;
    /**
     * 支付商品金额
     */
    private BigDecimal paymentProductAmount;

    /**
     * 新用户支付金额
     */
    private BigDecimal paymentNewAmount;

    /**
     * 新用户支付用户数
     */
    private Integer paymentNewUsers;
    /**
     * 新用户支付订单数
     */
    private Integer paymentNewOrders;

    /**
     * 成功退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 成功退款订单数
     */
    private Integer refundOrders;

    /**
     * 成功退款件数
     */
    private Integer refundQuantity;

    /**
     * 发货包裹数
     */
    private Integer packageDelivery;

    /**
     * 累计发货时长(秒)
     */
    private Long packageDeliverySecond;

    /**
     * 完成包裹数
     */
    private Integer packageFinish;

    /**
     * 累计签收时长(秒)
     */
    private Long packageFinishSecond;

    /**
     * 访客数
     */
    private Integer visitsUsers;

    /**
     * 访问量
     */
    private Integer visitsCount;

    /**
     * 关注人数
     */
    private Integer followUsers;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;

}
