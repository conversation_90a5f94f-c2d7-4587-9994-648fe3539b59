package com.hishop.himall.report.dao.mapper;

import com.hishop.himall.report.dao.domain.ReportSourceOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.models.TradeSource;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 源数据表-订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface ReportSourceOrderMapper extends BaseMapper<ReportSourceOrder> {

    /**
     * 用户订单统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getUserOrderSummary(@Param("start") LocalDate start, @Param("end") LocalDate end);

    /**
     * 用户支付统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getUserPaymentSummary(@Param("start") LocalDate start, @Param("end") LocalDate end);


    List<TradeSource> getShopOrderSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                            @Param("platform") Integer platform,
                                            @Param("shops") List<Long> shops,
                                            @Param("byShop") Boolean byShop);
    /**
     * 店铺订单统计
     *
     * @param start
     * @param end
     * @param byShop
     * @param newUser
     * @return
     */
    List<TradeSource> getShopPaymentSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                            @Param("platform") Integer platform,
                                            @Param("shops") List<Long> shops,
                                            @Param("byShop") Boolean byShop, @Param("newUser") Boolean newUser);

    /**
     * 批量新增
     *
     * @param list
     */
    void batchInsert(@Param("list") List<ReportSourceOrder> list);

    void deleteOrderIds(@Param("orderIds") List<String> orderIds);
}
