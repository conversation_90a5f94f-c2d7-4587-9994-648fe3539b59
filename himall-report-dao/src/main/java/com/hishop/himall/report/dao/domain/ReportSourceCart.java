package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 基础数据源表-加购数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
@Getter
@Setter
@TableName("report_source_cart")
public class ReportSourceCart {
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 来源平台
     */
    private Integer platform;

    /**
     * 创建时间
     */
    private Date createTime;


}
