package com.hishop.himall.report.dao.mapper;

import com.hishop.himall.report.dao.domain.ReportSourceFollowProduct;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.models.SourceFollowProductDelete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 基础数据源-关注记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface ReportSourceFollowProductMapper extends BaseMapper<ReportSourceFollowProduct> {

    void deleteByFollow(@Param("list") List<SourceFollowProductDelete> list);

    void batchInsert(@Param("list")List<ReportSourceFollowProduct> list);
}
