package com.hishop.himall.report.dao.demo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 访问记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Getter
@Setter
@TableName("report_access_records")
public class ReportAccessRecords {

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 访问类型，1：商城，2：店铺，3：商品
     */
    private Short accessType;

    /**
     * 访问url
     */
    private String accessUrl;

    /**
     * 渠道来源（0：小程序，1：PC，2：H5，3：APP）
     */
    private Short platform;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人Id
     */
    private Long createUser;

    /**
     * 更新人Id
     */
    private Long updateUser;
}
