package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 微信小程序统计-用户画像
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
@Getter
@Setter
@TableName("report_wechat_portrait")
public class ReportWechatPortrait {

    private Long id;

    /**
     * 时间纬度
     */
    @TableField("`range`")
    private String range;

    /**
     * 日期
     */
    private Date date;

    /**
     * 分组
     */
    @TableField("`group`")
    private Integer group;

    /**
     * 字段名
     */
    private String name;

    /**
     * 值
     */
    private BigDecimal value;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
