package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 基础源数据-商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
@TableName("report_source_product")
public class ReportSourceProduct {

    /**
     * 商品ID
     */
    @TableId("product_id")
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编码
     */
    private String productSpu;
    /**
     * 商品缩略图
     */
    private String thumbnailUrl;
    /**
     * 一级分类名称
     */
    private String categoryFirst;

    /**
     * 二级分类名称
     */
    private String categorySecond;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
