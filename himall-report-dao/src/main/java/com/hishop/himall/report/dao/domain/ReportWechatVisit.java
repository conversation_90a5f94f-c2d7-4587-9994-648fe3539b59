package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 微信小程序统计-访问统计
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
@Getter
@Setter
@TableName("report_wechat_visit")
public class ReportWechatVisit {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 时间维度
     */
    @TableField("`range`")
    private String range;

    /**
     * 日期
     */
    private Date date;

    /**
     * 总用户数
     */
    private Integer visitors;

    /**
     * 小程序打开次数
     */
    private Integer sessionCount;

    /**
     * 访问次数
     */
    private Integer visitPv;

    /**
     * 访问人数
     */
    private Integer visitUv;

    /**
     * 新用户数
     */
    private Integer visitUvNew;

    /**
     * 人均停留时长
     */
    private float stayTimeUv;

    /**
     * 次均停留时长
     */
    private float stayTimeSession;

    /**
     * 平均访问深度
     */
    private float visitDepth;

    /**
     * 转发次数
     */
    private Integer sharePv;

    /**
     * 转发人数
     */
    private Integer shareUv;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
