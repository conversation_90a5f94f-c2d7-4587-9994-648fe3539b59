package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
@Getter
@Setter
@TableName("report_source_visit")
public class ReportSourceVisit {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 访客标识
     */
    private String visitor;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 访问平台
     */
    private Integer platform;

    /**
     * 访问页面
     */
    private String page;

    /**
     * 访问时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
