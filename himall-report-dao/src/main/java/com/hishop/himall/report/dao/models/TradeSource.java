package com.hishop.himall.report.dao.models;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
public class TradeSource {
    private LocalDate date;
    private Integer year=0;
    private Integer month=0;
    private Integer week=0;

    private Long userId=0L;
    private Long shopId=0L;
    private Long productId=0L;
    private Integer platform=0;
    private BigDecimal amount=BigDecimal.ZERO;
    private Integer quantity=0;
    private Integer orders=0;
    private Integer users=0;
    private Integer packages= 0;
    private Long duration=0L;
}
