package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 源数据表-售后表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Getter
@Setter
@TableName("report_source_refund")
public class ReportSourceRefund {

    @TableId(value = "refund_id", type = IdType.INPUT)
    private Long refundId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单项ID
     */
    private Long orderItemId;

    /**
     * 门店ID
     */
    private Integer shopId;

    private Integer platform;
    /**
     * 商品ID
     */
    private Long productId;

    private Integer quantity;
    /**
     * 规格ID
     */
    private String skuId;

    private Integer status;

    private BigDecimal amount;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 完成时间
     */
    private Date completionTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
