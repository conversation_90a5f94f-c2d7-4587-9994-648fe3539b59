package com.hishop.himall.report.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.himall.report.dao.domain.ReportUserTrade;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.models.IncreaseSource;
import com.hishop.himall.report.dao.models.ProvinceSource;
import com.hishop.himall.report.dao.models.UserSummary;
import com.hishop.himall.report.dao.models.UserTrade;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface ReportUserTradeMapper extends BaseMapper<ReportUserTrade> {

    /**
     * 获取用户概览
     *
     * @param range 时间范围
     * @param start 开始时间
     * @param end   结束时间
     * @return 用户概览
     */
    UserSummary getUserSummary(@Param("range") String range, @Param("start") LocalDate start,
                               @Param("end") LocalDate end);

    /**
     * 获取新增用户
     *
     * @param range 时间范围
     * @param start 开始时间
     * @param end   结束时间
     * @return 新增用户
     */
    List<IncreaseSource> getNewUsers(@Param("range") String range, @Param("start") LocalDate start,
                                     @Param("end") LocalDate end);

    /**
     * 获取省份用户来源
     *
     * @return 省份用户来源
     */
    List<ProvinceSource> getProvinceUsers();

    /**
     * @param page
     * @param range
     * @param start
     * @param end
     * @return
     */
    Page<UserTrade> getUsers(Page<UserTrade> page, @Param("range") String range, @Param("start") Date start,
                             @Param("end") Date end);

}
