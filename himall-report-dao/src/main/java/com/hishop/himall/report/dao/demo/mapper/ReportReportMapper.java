package com.hishop.himall.report.dao.demo.mapper;

import java.time.LocalDateTime;

import org.apache.ibatis.annotations.Param;
import com.hishop.himall.report.dao.demo.domain.ReportReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
public interface ReportReportMapper extends BaseMapper<ReportReport> {

    ReportReport queryByDateTime(@Param("nowTime") LocalDateTime nowTime);

}
