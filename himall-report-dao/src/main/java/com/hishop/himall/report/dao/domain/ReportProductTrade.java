package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Getter
@Setter
@TableName("report_product_trade")
public class ReportProductTrade {

    private Integer id;

    @TableField("`range`")
    private String range;

    private LocalDate date;

    private Long productId;

    private Long shopId;

    private Integer platform;

    /**
     * 一级分类名称
     */
    private String categoryFirst;
    /**
     * 二级分类名称
     */
    private String categorySecond;

    /**
     * 加购人数
     */
    private Integer cartUsers;

    /**
     * 加购件数
     */
    private Integer cartQuantity;

    /**
     * 下单笔数
     */
    private Integer orderOrders;
    /**
     * 下单件数
     */
    private Integer orderQuantity;

    /**
     * 下单金额
     */
    private BigDecimal orderAmount;

    /**
     * 下单人数
     */
    private Integer orderUsers;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付订单数
     */
    private Integer paymentOrders;

    /**
     * 支付件数
     */
    private Integer paymentQuantity;

    /**
     * 支付人数
     */
    private Integer paymentUsers;

    /**
     * 申请售后订单数
     */
    private Integer applyOrders;

    /**
     * 申请售后人数
     */
    private Integer applyUsers;

    /**
     * 退款订单数
     */
    private Integer refundOrders;
    /**
     * 退款件数
     */
    private Integer refundQuantity;
    /**
     * 退款用户数
     */
    private Integer refundUsers;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 访问人数
     */
    private Integer visitsUsers;
    /**
     * 访问次数
     */
    private Integer visitsCount;
    /**
     * 收藏人数
     */
    private Integer followUsers;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
