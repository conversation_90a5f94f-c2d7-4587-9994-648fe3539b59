package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 源数据表-订单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Getter
@Setter
@TableName("report_source_order")
public class ReportSourceOrder {

    /**
     * 订单ID
     */
    @TableId("order_id")
    private String orderId;

    /**
     * 用户ID
     */
    private Long userId;

    private Long provinceId;
    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 支付时间
     */
    private Date paymentTime;

    private Date finishTime;
    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 发货时间
     */
    private Date deliveryTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
