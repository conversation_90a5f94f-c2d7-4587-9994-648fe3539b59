package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
@TableName("report_source_coupon")
public class ReportSourceCoupon {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 优惠券记录
     */
    private Long recordId;

    /**
     * 领取时间
     */
    private Date receiveTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
