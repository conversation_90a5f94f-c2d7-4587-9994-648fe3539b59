package com.hishop.himall.report.dao.demo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Getter
@Setter
@TableName("report_report")
public class ReportReport {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 平台
     */
    private String platforms;

    /**
     * 分组
     */
    private String groups;

    /**
     * 字段
     */
    private String fields;

    /**
     * 纬度
     */
    private String dimension;

    /**
     * 时间范围
     */
    @TableField("`range`")
    private String range;

    /**
     * 时间点
     */
    private Integer time;

    /**
     * 自动更新
     */
    private Boolean automatic;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 截止时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
