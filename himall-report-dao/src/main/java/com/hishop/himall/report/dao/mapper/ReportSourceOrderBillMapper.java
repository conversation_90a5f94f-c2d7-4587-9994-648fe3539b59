package com.hishop.himall.report.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.domain.ReportSourceOrderBill;
import com.hishop.himall.report.dao.models.SourceOrderBillDelete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 源数据表-订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface ReportSourceOrderBillMapper extends BaseMapper<ReportSourceOrderBill> {

    void deleteBills(@Param("list") List<Long> billIds);

    void batchInsert(@Param("list") List<ReportSourceOrderBill> list);
}
