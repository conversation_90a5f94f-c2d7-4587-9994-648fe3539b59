package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Getter
@Setter
@TableName("report_user_trade")
public class ReportUserTrade {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计范围
     */
    @TableField("`range`")
    private String range;

    /**
     * 日期
     */
    private LocalDate date;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 下单订单数
     */
    private Integer orderOrders;

    /**
     * 支付订单数
     */
    private Integer paymentOrders;

    /**
     * 支付商品件数
     */
    private Integer paymentQuantity;

    /**
     * 订单支付金额
     */
    private BigDecimal paymentOrderAmount;

    /**
     * 商品支付金额
     */
    private BigDecimal paymentProductAmount;
    /**
     * 退款金额金额
     */
    private BigDecimal RefundAmount;
    /**
     * 退款订单数
     */
    private Integer RefundOrders;
    /**
     * 发生包裹
     */
    private Integer deliverCount;
    /**
     * 签收包裹
     */
    private Integer receiptCount;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
