package com.hishop.himall.report.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.himall.report.dao.domain.ReportProductTrade;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.models.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface ReportProductTradeMapper extends BaseMapper<ReportProductTrade> {


    /**
     * 获取商品交易
     *
     * @param page
     * @param range
     * @param start
     * @param end
     * @param categoryName
     * @param productName
     * @return
     */
    Page<ProductTrade> getProducts(@Param("shopId") Long shopId,@Param("page") Page<Object> page, @Param("range") String range,
                                   @Param("start") Date start, @Param("end") Date end,
                                   @Param("categoryName") String categoryName, @Param("productName") String productName);

    /**
     * 获取商品交易
     * @param range 日期范围
     * @param start 开始日期
     * @param end 结束日期
     * @param categoryName 类目名称
     * @param productName 商品名称
     * @return 商品交易
     */
    List<ProductTrade> getProducts(@Param("shopId") Long shopId,@Param("range") String range,
                                   @Param("start") Date start, @Param("end") Date end,
                                   @Param("categoryName") String categoryName, @Param("productName") String productName);

    /**
     * @param range 日期范围
     * @param start 开始日期
     * @param end  结束日期
     * @return 商品类目汇总
     */
    List<CategorySummary> getCategorySummary(@Param("shopId") Long shopId,@Param("range") String range,
                                            @Param("start") Date start, @Param("end") Date end);

    /**
     * 商品访问,动销统计
     *
     * @param range 日期范围
     * @param start 开始日期
     * @param end 结束日期
     * @return 商品访问,动销统计
     */
    List<VisitsSummary> getVisitsSummary(@Param("shopId") Long shopId,@Param("range") String range, @Param("start") Date start,
                                         @Param("end") Date end);

    /**
     * @param range 日期范围
     * @param start 开始日期
     * @param end 结束日期
     * @return
     */
    ProductSummary getSummary(@Param("shopId") Long shopId, @Param("range") String range, @Param("start") LocalDate start,
                              @Param("end") LocalDate end);
}
