package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 统计报表-配置单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
@TableName("report_custom")
public class ReportCustom {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long shopId;
    /**
     * 标题
     */
    private String name;

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 分组
     */
    private String groups;

    /**
     * 字段
     */
    private String fields;

    /**
     * 统计维度
     */
    private Integer dimension;

    /**
     * 店铺
     */
    private String shops;


    /**
     * 产品
     */
    private String products;

    /**
     * 时间维度
     */
    @TableField("`range`")
    private String range;

    /**
     * 自动更新
     */
    private Boolean automatic;
    /**
     * 自动更新天数
     */
    private Integer times;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 截止时间
     */
    private LocalDate endDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
