package com.hishop.himall.report.dao.mapper;

import com.hishop.himall.report.dao.domain.ReportSourceProduct;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 基础源数据-商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface ReportSourceProductMapper extends BaseMapper<ReportSourceProduct> {

    void deleteProductIds(@Param("productIds") List<Long> productIds);

    void batchInsert(@Param("list")List<ReportSourceProduct> list);

}
