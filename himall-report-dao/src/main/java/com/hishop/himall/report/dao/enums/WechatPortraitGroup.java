package com.hishop.himall.report.dao.enums;

import lombok.Getter;

public enum WechatPortraitGroup {

    Province(1, "省份"),
    City(2, "城市"),
    Genders(3, "性别"),
    Platforms(4, "系统平台"),
    Devices(5, "手机设备型号"),
    Ages(6, "年龄段"),
    StayTime(7, "停留时间"),
    Depth(8, "访问深度");

    @Getter
    private Integer code;

    @Getter
    private String desc;

    WechatPortraitGroup(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WechatPortraitGroup ofValue(Integer code) {
        for (WechatPortraitGroup group : WechatPortraitGroup.values()) {
            if (group.getCode().equals(code)) {
                return group;
            }
        }
        return null;
    }
}
