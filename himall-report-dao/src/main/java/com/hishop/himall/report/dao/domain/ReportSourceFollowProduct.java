package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 基础数据源-关注记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
@TableName("report_source_follow_product")
public class ReportSourceFollowProduct {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 关注会员
     */
    private Long userId;

    /**
     * 关注商品
     */
    private Long productId;

    /**
     * 关注时间
     */
    private Date followTime;
}
