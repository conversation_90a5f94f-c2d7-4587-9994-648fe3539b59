package com.hishop.himall.report.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.domain.ReportSourceRegion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface ReportSourceRegionMapper extends BaseMapper<ReportSourceRegion> {

    void deleteRegionIds(@Param("regionIds") List<Long> regionIds);

    void batchInsert(@Param("list") List<ReportSourceRegion> list);
}
