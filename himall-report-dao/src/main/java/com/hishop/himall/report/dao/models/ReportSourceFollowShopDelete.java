package com.hishop.himall.report.dao.models;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname ReportSourceFollowShopDelete
 * Description //TODO
 * @date 2024/12/5 16:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportSourceFollowShopDelete implements Serializable {
    /**
     * 关注会员
     */
    private Long userId;

    /**
     * 关注门店
     */
    private Long shopId;
}
