package com.hishop.himall.report.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.domain.ReportSourceRefund;
import com.hishop.himall.report.dao.models.TradeSource;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 源数据表-售后表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
public interface ReportSourceRefundMapper extends BaseMapper<ReportSourceRefund> {

    /**
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getCompletionSummary(@Param("start") LocalDate start, @Param("end") LocalDate end);

    /**
     * 门店售后统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getShopRefundSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                           @Param("shops") List<Long> shops);

    /**
     * 商品售后统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getProductRefundSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                              @Param("products") List<Long> products);

    /**
     * 商品售后申请统计
     *
     * @param start
     * @param end
     * @return
     */
    List<TradeSource> getProductApplyRefundSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                                   @Param("products") List<Long> products);

    void deleteRefundIds(@Param("refundIds") List<Long> refundIds);

    void batchInsert(@Param("list") List<ReportSourceRefund> list);
}
