package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
@TableName("report_source_user")
public class ReportSourceUser {

    @TableId("user_id")
    private Long userId;

    private String nickname;
    private String phone;

    /**
     * 所属省份
     */
    private Integer provinceId;

    /**
     * 注册时间
     */
    private Date registrationTime;

    /**
     * 首次消费时间
     */
    private Date firstPaymentTime;

    private boolean whetherLogOut;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
