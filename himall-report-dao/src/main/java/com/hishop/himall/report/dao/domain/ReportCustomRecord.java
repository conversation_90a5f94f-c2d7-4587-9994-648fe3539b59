package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
@Getter
@Setter
@TableName("report_custom_record")
public class ReportCustomRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long shopId;
    /**
     * 自定义报表ID
     */
    private Long customId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 维度
     */
    private Integer dimension;

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 自动更新
     */
    private Boolean automatic;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 时间维度
     */
    @TableField("`range`")
    private String range;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 状态 1.数据聚合中 2.EXCEL生成中 3.导出完成 4.失败
     */
    private Integer status;

    /**
     * 文件地址
     */
    private String path;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
