package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 基础数据源-关注记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
@TableName("report_source_follow_shop")
public class ReportSourceFollowShop {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 关注会员
     */
    private Long userId;

    /**
     * 关注门店
     */
    private Long shopId;

    /**
     * 关注时间
     */
    private Date followTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
