package com.hishop.himall.report.dao.mapper;

import com.hishop.himall.report.dao.domain.ReportSourceCoupon;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.models.TradeSource;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface ReportSourceCouponMapper extends BaseMapper<ReportSourceCoupon> {

    /**
     * 领券统计
     *
     * @param start
     * @param end
     * @param byShop
     * @return
     */
    List<TradeSource> getShopCouponSummary(@Param("start") LocalDate start, @Param("end") LocalDate end,
                                           @Param("shops") List<Long> shops,
                                           @Param("byShop") boolean byShop);


    /**
     * 根据记录删除
     *
     * @param recordIds
     */
    void deleteByRecordId(@Param("recordIds") List<Long> recordIds);

    void batchInsert(@Param("values") List<ReportSourceCoupon> values);
}
