package com.hishop.himall.report.dao.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
@TableName("report_source_shop")
public class ReportSourceShop {

    /**
     * 门店ID
     */
    @TableId("shop_id")
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 省份
     */
    private Integer provinceId;

    /**
     * 城市
     */
    private Integer city;

    /**
     * 开业时间
     */
    private Date openingTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;
}
