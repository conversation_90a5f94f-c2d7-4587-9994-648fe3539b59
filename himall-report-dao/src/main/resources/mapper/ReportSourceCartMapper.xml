<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceCartMapper">
    <insert id="batchInsert">
        INSERT INTO report_source_cart ( `id`, `user_id`, `product_id`, `shop_id`, `platform`, `quantity` )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.userId},
            #{item.productId},
            #{item.shopId},
            #{item.platform},
            #{item.quantity}
            )
        </foreach>

    </insert>
    <delete id="deleteIds">
        delete FROM report_source_cart
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>

    </delete>
    <select id="getProductCartSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select product_id,
               shop_id,
               count(distinct user_id) as users,
               sum(quantity) as quantity
        from report_source_cart
        where Date(create_time) between #{start,jdbcType=DATE} and #{end,jdbcType=DATE}
            <if test="platform != null">
                and platform = #{platform}
            </if>
        group by product_id, shop_id
    </select>

    <select id="getShopCartSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
        <if test="byShop">
            shop_id,
        </if>
        count(distinct user_id) as users,
        sum(quantity) as quantity
        from report_source_cart
        where Date(create_time) between #{start} and #{end}
        <if test="platform != null">
            and platform = #{platform}
        </if>
        <if test="byShop">
            group by shop_id
        </if>
    </select>
</mapper>
