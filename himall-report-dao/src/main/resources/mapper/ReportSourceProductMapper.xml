<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceProductMapper">
    <insert id="batchInsert">
        INSERT into report_source_product (
        `product_id` ,
        `product_name` ,
        `product_spu` ,
        `thumbnail_url` ,
        `category_first` ,
        `category_second`
        ) VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.productId},#{item.productName},#{item.productSpu},#{item.thumbnailUrl},#{item.categoryFirst},#{item.categorySecond})
        </foreach>
    </insert>

    <delete id="deleteProductIds">
        delete from report_source_product where product_id in
        <foreach item="item" open="(" separator="," close=")" collection="list">
            #{item}
        </foreach>
    </delete>
</mapper>
