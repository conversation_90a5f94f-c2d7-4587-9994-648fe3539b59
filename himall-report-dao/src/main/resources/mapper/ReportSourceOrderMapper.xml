<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceOrderMapper">
    <insert id="batchInsert">
        insert into report_source_order (
        `order_id`,
        `user_id` ,
        `shop_id` ,
        `order_time` ,
        `payment_time` ,
        `delivery_time` ,
        `finish_time` ,
        `payment_amount` ,
        `province_id`
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderId},
            #{item.userId},
            #{item.shopId},
            #{item.orderTime},
            #{item.paymentTime},
            #{item.deliveryTime},
            #{item.finishTime},
            #{item.paymentAmount},
            #{item.provinceId}
            )
        </foreach>
    </insert>
    <delete id="deleteOrderIds">
        delete FROM report_source_order
        where order_id in
        <foreach collection="orderIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </delete>
    <select id="getUserOrderSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        SELECT
            Date(order_time) AS date,
            user_id,
            COUNT(order_id) AS orders
        FROM report_source_order
        WHERE Date (order_time) between #{start,jdbcType=DATE} AND #{end,jdbcType=DATE}
        group by date, user_id
    </select>
    <select id="getUserPaymentSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        SELECT
            Date(payment_time ) AS date,
            user_id,
            COUNT(order_id) AS orders
        FROM report_source_order
        WHERE Date(payment_time) between #{start} AND #{end}
        group by date, user_id
    </select>
    <select id="getShopPaymentSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
        <if test="byShop">shop_id,</if>
        SUM(payment_amount) as amount,
        COUNT(distinct order_id) as orders,
        COUNT(distinct user_id) as users
        from report_source_order
        where DATE(payment_time) between #{start} and #{end}
        <if test="newUser">
            and user_id in (select user_id from report_source_user where DATE(first_payment_time) between #{start} and
            #{end})
        </if>
        <if test="byShop">group by shop_id</if>
    </select>
    <select id="getShopOrderSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
        <if test="byShop">shop_id,</if>
        SUM(payment_amount) as amount,
        COUNT(distinct order_id) as orders,
        COUNT(distinct user_id) as users
        from report_source_order
        where DATE(order_time) between #{start} and #{end}
        <if test="byShop">group by shop_id</if>
    </select>
</mapper>
