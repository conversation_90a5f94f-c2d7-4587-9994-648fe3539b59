<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceVisitMapper">
    <insert id="batchInsert">
        INSERT INTO report_source_visit (
        `visitor` ,
        `shop_id` ,
        `product_id` ,
        `platform` ,
        `page`,
        `create_time`
        )
        VALUES
        <foreach collection="values" item="item" separator=",">
            (
            #{item.visitor},
            #{item.shopId},
            #{item.productId},
            #{item.platform},
            #{item.page},
            #{item.createTime}
            )
        </foreach>

    </insert>
    <select id="getProductVisitSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        SELECT
            product_id,
            shop_id,
            count(distinct visitor) AS users,
            count(0) AS quantity
        FROM report_source_visit
        WHERE date(create_time) BETWEEN #{start} AND #{end}
            <if test="platform!=null">
                AND platform = #{platform}
            </if>
        group by product_id,shop_id
    </select>
    <select id="getShopVisitSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        SELECT
            shop_id,
            count(distinct visitor) AS users,
            count(0) AS quantity
        FROM report_source_visit
        WHERE date(create_time) BETWEEN #{start} AND #{end}
        <if test="platform!=null">
            AND platform = #{platform}
        </if>
        group by shop_id
    </select>
</mapper>
