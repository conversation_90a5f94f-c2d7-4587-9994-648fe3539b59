<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportProductTradeMapper">
    <select id="getProducts" resultType="com.hishop.himall.report.dao.models.ProductTrade">
        select
        t.product_id,
        p.product_name,
        p.thumbnail_url,
        SUM(cart_users) as cart_users,
        SUM(cart_quantity) as cart_quantity,
        SUM(follow_users) as follow_users,
        <PERSON>UM(order_users) as order_users,
        <PERSON>UM(order_orders) as order_orders,
        SUM(order_quantity) as order_quantity,
        SUM(order_amount) as order_amount,
        SUM(payment_users) as payment_users,
        SUM(payment_orders) as payment_orders,
        SUM(payment_amount) as payment_amount,
        SUM(payment_quantity) as payment_quantity,
        SUM(apply_orders) as apply_orders,
        SUM(apply_users) as apply_users,
        SUM(refund_orders) as refund_orders,
        <PERSON><PERSON>(refund_users) as refund_users,
        <PERSON><PERSON>(refund_quantity) as refund_quantity,
        S<PERSON>(refund_amount) as refund_amount,
        SUM(visits_users) as visits_users,
        SUM(visits_count) as visits_count
        from report_product_trade t
        left join report_source_product p
        on t.product_id = p.product_id
        where `range` = #{range} and date between #{start} and #{end}
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
        <if test="categoryName != null and categoryName!='' ">
             and t.product_id in (select id from report_source_product where category_first = #{categoryName} or category_second = #{categoryName})
        </if>
        <if test="productName != null and productName!=''">
             and t.product_id in (select id from report_source_product where product_name like #{productName})
        </if>
    </select>

    <select id="getCategorySummary" resultType="com.hishop.himall.report.dao.models.CategorySummary">
        select category_first as 'categoryName',
            SUM(payment_amount) as 'amount',
            SUM(payment_quantity) as 'quantity'
        from report_product_trade
        where shop_id = #{shopId} and `range` = #{range} and date between #{start} and #{end}
        group by category_first
    </select>
    <select id="getVisitsSummary" resultType="com.hishop.himall.report.dao.models.VisitsSummary">
        select date,
            count(distinct case when visits_count>0 then product_id else null end) as visitsProducts,
            count(distinct case when payment_orders>0 then product_id else null end ) as salesProducts
        from report_product_trade
        where  `range` = #{range} and date between #{start} and #{end}
            <if test="shopId != null">
                    and shop_id = #{shopId}
            </if>
        group by date
    </select>

    <select id="getSummary" resultType="com.hishop.himall.report.dao.models.ProductSummary">
        select
            count(distinct case when visits_count>0 then product_id else null end) as visitsProducts,
            count(distinct case when payment_orders>0 then product_id else null end ) as salesProducts,
            sum(visits_count) as visits_count,
            sum(visits_users) as visits_users,
            sum(cart_quantity) as cart_quantity,
            sum(order_quantity) as order_quantity,
            sum(payment_quantity) as payment_quantity
        from report_product_trade
        where `range` = #{range} and date between #{start} and #{end}
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
    </select>
</mapper>
