<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceFollowProductMapper">
    <insert id="batchInsert">
        insert into report_source_follow_product(user_id,product_id,follow_time) values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId},#{item.productId},#{item.followTime})
        </foreach>
    </insert>

    <delete id="deleteByFollow">
        delete from report_source_follow_product
        where
        <foreach collection="list" item="item" separator=" or ">
            (user_id=#{item.userId} and product_id=#{item.productId})
        </foreach>
    </delete>

</mapper>
