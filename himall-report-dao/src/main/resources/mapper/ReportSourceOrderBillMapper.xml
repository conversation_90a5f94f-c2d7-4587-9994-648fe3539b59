<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceOrderBillMapper">

    <delete id="deleteBills">
        delete from report_source_order_bill
        where
        bill_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>
    <insert id="batchInsert" parameterType="ReportSourceOrderBill">
        INSERT into report_source_order_bill (
        `bill_id`,
        `shop_id` ,
        `order_id` ,
        `express_company_name` ,
        `express_company_code` ,
        `ship_order_number` ,
        `deliver_time`
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.billId},
            #{item.shopId},
            #{item.orderId},
            #{item.expressCompanyName},
            #{item.expressCompanyCode},
            #{item.shipOrderNumber},
            #{item.deliverTime}
            )
        </foreach>
    </insert>
</mapper>
