<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceRefundMapper">
    <insert id="batchInsert">
        INSERT into report_source_refund (
        `refund_id` ,
        `user_id` ,
        `order_id` ,
        `order_item_id` ,
        `shop_id` ,
        `product_id` ,
        `platform` ,
        `sku_id` ,
        `quantity` ,
        `amount` ,
        `apply_time` ,
        `completion_time`,
        `status`
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.refundId},
            #{item.userId},
            #{item.orderId},
            #{item.orderItemId},
            #{item.shopId},
            #{item.productId},
            #{item.platform},
            #{item.skuId},
            #{item.quantity},
            #{item.amount},
            #{item.applyTime},
            #{item.completionTime},
            #{item.status}
            )
        </foreach>

    </insert>

    <delete id="deleteRefundIds">
        delete from report_source_refund
        where refund_id in
        <foreach item="refundId" collection="refundIds" separator="," open="(" close=")">
            #{refundId}
        </foreach>

    </delete>

    <select id="getProductApplyRefundSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        SELECT
        product_id,
        shop_id,
        platform,
        count(distinct order_id) AS orders,
        count(distinct user_id) AS users
        FROM report_source_refund
        WHERE date( apply_time) between #{start,jdbcType=DATE} and #{end,jdbcType=DATE}
        <if test="products !=null">
            AND product_id in (
            <foreach item="id" collection="products" separator=",">
                #{id}
            </foreach>)
        </if>
        group by product_id, shop_id,platform
    </select>

    <select id="getProductRefundSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        SELECT
        product_id,
        shop_id,
        platform,
        count(distinct order_id) AS orders,
        count(distinct user_id) AS users,
        sum(amount) AS amount,
        sum(quantity) AS quantity
        FROM report_source_refund
        WHERE date( completion_time) between #{start,jdbcType=DATE} and #{end,jdbcType=DATE}
        <if test="products !=null">
            AND product_id in (
            <foreach item="id" collection="products" separator=",">
                #{id}
            </foreach>)
        </if>
        group by product_id,shop_id,platform
    </select>
    <select id="getCompletionSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        SELECT count(distinct order_id) AS orders,
               count(distinct user_id)  AS users,
               sum(amount)              AS amount,
               sum(quantity)            AS quantity
        FROM report_source_refund
        WHERE date ( completion_time) between #{start,jdbcType=DATE}
          and #{end,jdbcType=DATE}
    </select>
    <select id="getShopRefundSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        SELECT shop_id,
               count(distinct order_id) AS orders,
               count(distinct user_id)  AS users,
               sum(amount)              AS amount,
               sum(quantity)            AS quantity
        FROM report_source_refund
        WHERE date ( completion_time) between #{start,jdbcType=DATE}
          and #{end,jdbcType=DATE}
        group by shop_id
    </select>


</mapper>
