<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceFollowShopMapper">
    <insert id="batchInsert">

        insert into report_source_follow_shop(user_id,shop_id,follow_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId},#{item.shopId},#{item.followTime})
        </foreach>
    </insert>

    <delete id="deleteByFollow">
        delete from report_source_follow_shop
        where
        <foreach item="item" separator="or" collection="list">
            (user_id =#{item.userId} and shop_id=#{item.shopId})
        </foreach>
    </delete>
</mapper>
