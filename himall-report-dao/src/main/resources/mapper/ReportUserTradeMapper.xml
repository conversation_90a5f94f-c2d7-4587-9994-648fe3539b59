<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportUserTradeMapper">
    <select id="getUserSummary" resultType="com.hishop.himall.report.dao.models.UserSummary">
        select * from report_user_trade
        where shop_id = 0 and `range` = #{range} and date between #{start} and #{end}
    </select>
    <select id="getProvinceUsers" resultType="com.hishop.himall.report.dao.models.ProvinceSource">
        select province_id,
               r.name as province_name,
               COUNT( user_id ) as users
        from report_source_user u
        left join report_source_region r
        on u.province_id = r.id
        group by province_id
    </select>
    <select id="getNewUsers" resultType="com.hishop.himall.report.dao.models.IncreaseSource">
        select
        <choose>
            <when test="range == 'DAY'">Date( opening_time ) as date,</when>
            <when test="range == 'WEEK'">YEARWEEK( opening_time ) as week,</when>
            <when test="range == 'MONTH'">
                YEAR( opening_time ) as year,
                MONTH( opening_time ) as month,
            </when>
        </choose>
        COUNT( user_id ) as count
        from report_source_user
        where DATE(registration_time) between  #{start} and #{end}
        group by
        <choose>
            <when test="range == 'DAY'">date</when>
            <when test="range == 'WEEK'">week</when>
            <when test="range == 'MONTH'">year,month</when>
        </choose>
    </select>
    <select id="getUsers" resultType="com.hishop.himall.report.dao.models.UserTrade" >
        select
            u.user_id,
            u.nickname,
            u.phone,
            u.registration_time,
            sum(order_orders) as order_orders,
            sum(payment_orders) as payment_orders,
            sum(payment_quantity) as payment_quantity,
            sum(payment_order_amount) as payment_order_amount,
            sum(payment_product_amount) as payment_product_amount,
            sum(refund_orders) as refund_orders,
            sum(refund_amount) as refund_amount
        from report_user_trade t
        left join report_source_user u
        on t.user_id = u.user_id
        where `range` = #{range} and date between #{start} and #{end}
    </select>
</mapper>
