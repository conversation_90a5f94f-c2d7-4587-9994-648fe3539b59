<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceShopMapper">
    <insert id="batchInsert">
        insert into report_source_shop (shop_id, shop_name, province_id, city, opening_time)
        values
        <foreach collection="values" item="item" separator=",">
            (#{item.shopId}, #{item.shopName}, #{item.provinceId}, #{item.city}, #{item.openingTime})
        </foreach>
    </insert>

    <delete id="deleteShopIds">
        delete from report_source_shop where shop_id in
        <foreach collection="shopIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
