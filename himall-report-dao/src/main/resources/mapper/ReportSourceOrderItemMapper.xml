<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceOrderItemMapper">
    <insert id="batchInsert">
        INSERT into report_source_order_item (
        `order_item_id`,
        `order_id` ,
        `user_id` ,
        `shop_id`,
        `product_id` ,
        `platform` ,
        `order_time` ,
        `payment_time` ,
        `price` ,
        `quantity` ,
        `discount`,
        `amount`
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.orderItemId},
            #{item.orderId},
            #{item.userId},
            #{item.shopId},
            #{item.productId},
            #{item.platform},
            #{item.orderTime},
            #{item.paymentTime},
            #{item.price},
            #{item.quantity},
            #{item.discount},
            #{item.amount}
            )
        </foreach>
    </insert>
    <delete id="deleteOrderItemIds">
        DELETE FROM report_source_order_item WHERE order_item_id IN
        <foreach item="orderItemId" collection="orderItemIds" separator="," open="(" close=")">
            #{orderItemId}
        </foreach>
    </delete>
    <select id="getUserPaymentSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
        user_id,
        shop_id,
        sum( amount ) AS amount,
        sum( quantity ) AS quantity,
        count( DISTINCT order_id ) AS orders
        FROM report_source_order_item
        WHERE Date(payment_time) between #{start,jdbcType=DATE} AND #{end,jdbcType=DATE}
        <if test="products !=null">
            AND product_id in (
            <foreach item="id" collection="products" separator=",">
                #{id}
            </foreach>)
        </if>
        <if test="platform != null">
            AND platform = #{platform}
        </if>
        group by user_id,shop_id
    </select>
    <select id="getProductOrderSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
        product_id,
        shop_id,
        sum( amount ) AS amount,
        sum( quantity ) AS quantity,
        count( DISTINCT user_id ) AS users
        FROM report_source_order_item
        WHERE date(order_time) between #{start,jdbcType=DATE} AND #{end,jdbcType=DATE}
        <if test="products !=null">
            AND product_id in (
            <foreach item="id" collection="products" separator=",">
                #{id}
            </foreach>)
        </if>
        <if test="shops !=null">
            AND shop_id in (
            <foreach item="id" collection="shops" separator=",">
                #{id}
            </foreach>)
        </if>
        <if test="platform != null">
            AND platform = #{platform}
        </if>
        group by product_id,shop_id
    </select>
    <select id="getProductPaymentSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
        product_id,
        shop_id,
        sum( amount ) AS amount,
        sum( quantity ) AS quantity,
        count( DISTINCT order_id ) AS orders,
        count( DISTINCT user_id ) AS users
        FROM report_source_order_item
        WHERE date(payment_time) between #{start,jdbcType=DATE} AND #{end,jdbcType=DATE}
        <if test="products !=null">
            AND product_id in (
            <foreach item="id" collection="products" separator=",">
                #{id}
            </foreach>)
        </if>
        <if test="platform != null">
            AND platform = #{platform}
        </if>
        group by product_id,shop_id
    </select>

    <select id="getShopDeliverPackageSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
            b.shop_id,
            count(  0 ) AS packages,
            sum(TIMESTAMPDIFF(SECOND,o.payment_time,b.deliver_time)) AS duration
        FROM report_source_order_bill b
        LEFT JOIN report_source_order o
        ON b.order_id = o.order_id
        WHERE date(b.deliver_time) between #{start,jdbcType=DATE} AND #{end,jdbcType=DATE}
        <if test="shops !=null">
            AND b.shop_id in (
            <foreach item="id" collection="shops" separator=",">
                #{id}
            </foreach>)
        </if>
        GROUP BY b.shop_id
    </select>

    <select id="getShopFinishPackageSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
            b.shop_id,
            count(0) AS packages,
            sum(TIMESTAMPDIFF(SECOND,b.deliver_time,o.finish_time)) AS duration
        FROM report_source_order_bill b
        LEFT JOIN report_source_order o
        ON b.order_id = o.order_id
        WHERE date(o.finish_time) between #{start,jdbcType=DATE} AND #{end,jdbcType=DATE}
            <if test="shops !=null">
                AND b.shop_id in (
                <foreach item="id" collection="shops" separator=",">
                #{id}
                </foreach>)
            </if>
        GROUP BY b.shop_id
    </select>

    <select id="getShopPaymentSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
            <if test="byShop">shop_id,</if>
            sum( amount ) AS amount,
            sum( quantity ) AS quantity
        FROM report_source_order_item
        WHERE date(payment_time) between #{start,jdbcType=DATE} AND #{end,jdbcType=DATE}
        <if test="platform != null">
            AND platform = #{platform}
        </if>
        <if test="byShop">GROUP BY shop_id</if>
    </select>
</mapper>
