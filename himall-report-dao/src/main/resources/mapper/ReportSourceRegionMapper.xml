<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceRegionMapper">
    <insert id="batchInsert">
        INSERT into report_source_region (
        `id` ,
        `name` ,
        `parent_id` ,
        `level` ,
        `name` ,
        `parent_id` ,
        `level`
        ) VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.id},#{item.name},#{item.parentId},#{item.level},#{item.name},#{item.parentId},#{item.level})
        </foreach>

    </insert>

    <delete id="deleteRegionIds">
        delete from report_source_region where region_id in
        <foreach item="item" open="(" separator="," close=")" collection="list">
            #{item}
        </foreach>
    </delete>
</mapper>
