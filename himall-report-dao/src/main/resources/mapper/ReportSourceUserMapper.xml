<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceUserMapper">
    <insert id="batchInsert">
        INSERT into report_source_user (
        `user_id` ,
        `nickname` ,
        `phone` ,
        `province_id` ,
        `registration_time` ,
        `first_payment_time`
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userId},
            #{item.nickname},
            #{item.phone},
            #{item.provinceId},
            #{item.registrationTime},
            #{item.firstPaymentTime}
            )
        </foreach>

    </insert>

    <delete id="deleteUserIds">
        delete from report_source_user where user_id in
        <foreach collection="userIds" item="userId" open="(" separator=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
