<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportSourceCouponMapper">
    <insert id="batchInsert">
        insert into report_source_coupon(
        `shop_id` ,
        `user_id`,
        `record_id` ,
        `receive_time`
        )
        VALUES
        <foreach collection="values" item="item" separator=",">
            (#{item.shopId},
             #{item.userId},
             #{item.recordId},
             #{item.receiveTime})
        </foreach>

    </insert>
    <delete id="deleteByRecordId">
        delete from report_source_coupon
        where record_id in
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>

    </delete>


    <select id="getShopCouponSummary" resultType="com.hishop.himall.report.dao.models.TradeSource">
        select
        <if test="byShop">
            shop_id,
        </if>
        count(distinct user_id) as users
        from report_source_coupon
        where receive_time between #{start} and #{end}
        <if test="byShop">
            group by shop_id
        </if>
    </select>
</mapper>
