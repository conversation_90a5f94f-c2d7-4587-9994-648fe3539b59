<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
        <relativePath>../himall-parent/pom.xml</relativePath>
    </parent>

    <artifactId>himall-demo</artifactId>
    <packaging>jar</packaging>




    <dependencies>

        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>himall-web-spring-boot-starter</artifactId>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>io.minio</groupId>-->
<!--            <artifactId>minio</artifactId>-->
<!--            <version>8.5.8</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.workoss.boot</groupId>-->
<!--            <artifactId>aws-storage-client</artifactId>-->
<!--            <version>1.1.0-RC1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.workoss.boot</groupId>-->
<!--            <artifactId>storage-client-spring-boot-starter</artifactId>-->
<!--            <version>1.1.0-RC1</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.workoss.boot</groupId>-->
<!--                    <artifactId>aws2-storage-client</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->


<!--        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-v5-client-spring-boot-starter</artifactId>
            <version>2.3.0</version>
        </dependency>-->

        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>hishop-s3-spring-boot-starter</artifactId>
        </dependency>



    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>