//package com.meituan.shangou;
//
//import java.io.IOException;
//import java.security.InvalidKeyException;
//import java.security.NoSuchAlgorithmException;
//
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import com.workoss.boot.storage.StorageTemplate;
//import com.workoss.boot.storage.model.StorageFileInfoListing;
//import com.workoss.boot.util.json.JsonMapper;
//
//import io.minio.ListObjectsArgs;
//import io.minio.MinioClient;
//import io.minio.Result;
//import io.minio.credentials.StaticProvider;
//import io.minio.errors.ErrorResponseException;
//import io.minio.errors.InsufficientDataException;
//import io.minio.errors.InternalException;
//import io.minio.errors.InvalidResponseException;
//import io.minio.errors.ServerException;
//import io.minio.errors.XmlParserException;
//import io.minio.messages.Item;
//import okhttp3.MediaType;
//import okhttp3.OkHttpClient;
//import okhttp3.Request;
//import okhttp3.RequestBody;
//import okhttp3.Response;
//
///**
// * <AUTHOR>
// */
//class StorageClientTest extends DemoApplicationTest {
//
//    @Autowired
//    private StorageTemplate storageTemplate;
//
//    @Test
//    void testQuery() throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
//        StorageFileInfoListing storageFileInfoListing = storageTemplate.client().listObjects("/", null, null, 100);
//        System.out.println(JsonMapper.toJSONString(storageFileInfoListing));
//
//        OkHttpClient client = new OkHttpClient().newBuilder()
//                .build();
//        MediaType mediaType = MediaType.parse("application/octet-stream");
////        RequestBody body = RequestBody.create(mediaType, "");
//
//        Request request = new Request.Builder()
//                .get()
//                .url("https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/?list-type=2&prefix=&max-keys=100&delimiter=")
////                .method("GET", body)
//                .addHeader("User-Agent", "MinIO (Windows 10; amd64) minio-java/8.5.8")
//                .addHeader("Content-MD5", "1B2M2Y8AsgTpgAmY7PhCfg==")
//                .addHeader("x-amz-content-sha256", "UNSIGNED-PAYLOAD")
//                .addHeader("X-Amz-Date", "20240229T055117Z")
//                .addHeader("Authorization", "AWS4-HMAC-SHA256 Credential=UFZKPSFWLGLVK7LR24HA/20240229/obs/s3/aws4_request, SignedHeaders=content-md5;host;x-amz-content-sha256;x-amz-date, Signature=b536bf1c272a5b9a48ad73b90ef43b7e23d50573101b7fa9e1f4c81aee18dfc7")
//                .addHeader("Content-Type", "application/octet-stream")
//                .addHeader("Accept", "*/*")
//                .addHeader("Host", "hishop-wine.obs.cn-north-1.myhuaweicloud.com")
//                .addHeader("Connection", "keep-alive")
//                .build();
//        Response response = client.newCall(request).execute();
//        System.out.println(response.body().string());
//
////        access-key: UFZKPSFWLGLVK7LR24HA
////        secret-key: X4R2PK6tnhEs91di1hUWvT4UtZoGlQUbIsUo084g
//        MinioClient minioClient = MinioClient.builder()
//                .credentialsProvider(new StaticProvider("UFZKPSFWLGLVK7LR24HA","X4R2PK6tnhEs91di1hUWvT4UtZoGlQUbIsUo084g",null))
//                .endpoint("https://obs.cn-north-1.myhuaweicloud.com")
//                .region("obs")
//                .build();
//
////        minioClient.listBuckets().forEach(bucket -> {
////            System.out.println(bucket.name());
////        });
//
//        ListObjectsArgs.Builder builder = ListObjectsArgs.builder()
//                .bucket("hishop-wine")
//                .prefix("")
//                .recursive(true)
//                .includeUserMetadata(true)
//
//                .useUrlEncodingType(false)
//                .fetchOwner(false)
//                .maxKeys(100);
//        minioClient.enableVirtualStyleEndpoint();
//        Iterable<Result<Item>> results = minioClient.listObjects(builder.build());
//        for (Result<Item> result : results) {
//            Item item = result.get();
//            System.out.println(item.objectName());
//        }
//    }
//}
