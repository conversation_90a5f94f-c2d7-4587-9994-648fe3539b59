server:
  port: 8080
  servlet:
    context-path: /demo

spring:
  application:
    name: demo
  redis:
    database: 1
    host: **************
    port: 6379


cosid:
  namespace: ${spring.application.name}
  machine:
    distributor:
      type: redis
    enabled: true
  snowflake:
    enabled: true
  generator:
    enabled: true
  segment:
    distributor:
      type: redis
    enabled: true

logging:
  level:
    root: info
    com.amazonaws.http: debug
    io.minio: debug



boot:
  storage:
    default-client:
      storage-type: obs
      access-key: UFZKPSFWLGLVK7LR24HA
      secret-key: X4R2PK6tnhEs91di1hUWvT4UtZoGlQUbIsUo084g
      endpoint: https://obs.cn-north-1.myhuaweicloud.com
      bucket-name: hishop-wine
