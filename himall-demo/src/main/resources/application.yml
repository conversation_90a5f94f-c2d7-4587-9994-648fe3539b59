
spring:
  application:
    name: himall-demo
  profiles:
    active: develop
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
  redis:
    host: **************
    port: 6379
    database: 1
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:**************:8848}
      username: dev
      password: dev@hishop
      config:
        namespace: ${spring.profiles.active}
        group: 1.0.0
      discovery:
        namespace: ${spring.profiles.active}
        group: 1.0.0
  config:
    import:
#      - optional:nacos:himall-common.yml
      - optional:nacos:${spring.application.name}.yml

rocketmq:
  name-server: **********:9876
  producer:
    group: seashop_operation_log_topic
  pull-consumer:
    topic: seashop_operation_log_topic
    group: seashop_operation_log_consumer


#rocketmq:
#  producer:
#    endpoints: **************:8081
#    topic: seashop_operation_log_topic
#  simple-consumer:
#    endpoints: **************:8081

hishop:
  storage:
    storage-type: OBS
    bucket-name: himall-test
    endpoint: https://obs.cn-south-1.myhuaweicloud.com
    access-key: UEDFC3T2O7J1RXQQIAVO
    secret-key: FzRPLhtiitbYO3MTrXxArFLUT3KBIkIdmw9MIKEL
    domain: https://himall-obs.35hiw.com
cosid:
  enabled: false

