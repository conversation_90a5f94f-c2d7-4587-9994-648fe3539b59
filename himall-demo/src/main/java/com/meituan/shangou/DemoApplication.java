package com.meituan.shangou;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration;

import com.hishop.starter.storage.client.StorageClient;

/**
 * <AUTHOR>
 */
@SpringBootApplication
public class DemoApplication implements CommandLineRunner {

//    @Resource
//    private RocketMQClientTemplate rocketMQClientTemplate;

    @Autowired(required = true)
    private StorageClient storageClient;

    public DemoApplication() {
        System.out.println("1111111111111");
    }


    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        //This is an example of pull consumer using rocketMQTemplate.
//        List<String> messages = rocketMQTemplate.receive(String.class);

//        rocketMQClientTemplate.convertAndSend("seashop_operation_log_topic","{\"id\":1,\"name\":\"test\"}");
//        System.out.printf("receive from rocketMQTemplate, messages=%s %n", messages);
    }

}
