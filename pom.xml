<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <name>himall</name>

    <groupId>com.hishop.himall</groupId>
    <artifactId>himall</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>

        <module>himall-parent</module>
        <module>himall-bom</module>
<!--        <module>himall-demo</module>-->
    </modules>

    <distributionManagement>
        <snapshotRepository>
            <id>hishop-snapshot</id>
            <name>HiShop Snapshot</name>
            <url>https://nexus.35hiw.com/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>hishop-release</id>
            <name>HiShop Release</name>
            <url>https://nexus.35hiw.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>
</project>
