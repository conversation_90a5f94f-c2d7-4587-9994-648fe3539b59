<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.starter</groupId>
        <artifactId>hishop-parent</artifactId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>himall-gw</name>
    <groupId>com.hishop.himall</groupId>
    <artifactId>himall-gw</artifactId>
    <version>1.0.3-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <trade.version>1.0.3-SNAPSHOT</trade.version>
        <base.version>1.0.3-SNAPSHOT</base.version>
        <order.version>1.0.3-SNAPSHOT</order.version>
        <gw.version>1.0.3-SNAPSHOT</gw.version>
        <report.version>1.0.3-SNAPSHOT</report.version>
        <erp.version>1.0.1-SNAPSHOT</erp.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-api</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-boot</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-security</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-util</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-trade-api</artifactId>
                <version>${trade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-api</artifactId>
                <version>${order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-gw-api</artifactId>
                <version>${gw.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-gw-common</artifactId>
                <version>${gw.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-gw-core</artifactId>
                <version>${gw.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-report-api</artifactId>
                <version>${report.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-erp-api</artifactId>
                <version>${erp.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <modules>
        <module>seashop-gw-api</module>
        <module>seashop-gw-common</module>
        <module>seashop-gw-server</module>
        <module>seashop-gw-core</module>
    </modules>
    <repositories>
        <repository>
            <id>central</id>
            <url>http://nexus.35hiw.com/repository/maven-central/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>huaweicloud</id>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>maven-public</id>
            <url>https://nexus.35hiw.com/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

    </repositories>
</project>