<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hishop.starter</groupId>
        <artifactId>hishop-parent</artifactId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.hishop.himall</groupId>
    <artifactId>himall-report</artifactId>
    <version>1.0.3-SNAPSHOT</version>
    <packaging>pom</packaging>
    <properties>
        <report.version>1.0.3-SNAPSHOT</report.version>
        <report.api.version>1.0.3-SNAPSHOT</report.api.version>
        <base.version>1.0.3-SNAPSHOT</base.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-report-api</artifactId>
                <version>${report.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-report-dao</artifactId>
                <version>${report.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-report-common</artifactId>
                <version>${report.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-report-core</artifactId>
                <version>${report.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-boot</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-util</artifactId>
                <version>${base.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <modules>
        <module>himall-report-api</module>
        <module>himall-report-dao</module>
        <module>himall-report-common</module>
        <module>himall-report-core</module>
        <module>himall-report-server</module>
    </modules>
    <repositories>
        <repository>
            <id>central</id>
            <url>http://nexus.35hiw.com/repository/maven-central/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>huaweicloud</id>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>maven-public</id>
            <url>https://nexus.35hiw.com/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

    </repositories>
</project>