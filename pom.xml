<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.starter</groupId>
        <artifactId>hishop-parent</artifactId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>himall-trade</name>
    <groupId>com.hishop.himall</groupId>
    <artifactId>himall-trade</artifactId>
    <version>1.0.3-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <trade.version>1.0.3-SNAPSHOT</trade.version>
        <trade.api.version>1.0.3-SNAPSHOT</trade.api.version>
        <erp.version>1.0.1-SNAPSHOT</erp.version>
        <base.version>1.0.3-SNAPSHOT</base.version>
        <base.api.version>1.0.3-SNAPSHOT</base.api.version>
        <order.version>1.0.3-SNAPSHOT</order.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>
        <histore.report.version>1.0.3-SNAPSHOT</histore.report.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-api</artifactId>
                <version>${base.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-util</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-boot</artifactId>
                <version>${base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-trade-api</artifactId>
                <version>${trade.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-api</artifactId>
                <version>${order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-trade-common</artifactId>
                <version>${trade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-trade-dao</artifactId>
                <version>${trade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-trade-core</artifactId>
                <version>${trade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-report-api</artifactId>
                <version>${histore.report.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-erp-api</artifactId>
                <version>${erp.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <modules>
        <module>seashop-trade-api</module>
        <module>seashop-trade-common</module>
        <module>seashop-trade-dao</module>
        <module>seashop-trade-server</module>
        <module>seashop-trade-core</module>
    </modules>

    <repositories>
        <repository>
            <id>central</id>
            <url>http://nexus.35hiw.com/repository/maven-central/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>huaweicloud</id>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>maven-public</id>
            <url>https://nexus.35hiw.com/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

    </repositories>
</project>
