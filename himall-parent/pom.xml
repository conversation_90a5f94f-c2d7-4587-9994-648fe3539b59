<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hishop.starter</groupId>
        <artifactId>hishop-parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
        <relativePath />
    </parent>
    <groupId>com.hishop.himall</groupId>
    <artifactId>himall-parent</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <adapay.version>1.2.10</adapay.version>
        <weixin-java-miniapp.version>4.6.0</weixin-java-miniapp.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>

        <hishop-starter.version>1.0.1-SNAPSHOT</hishop-starter.version>

    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-bom</artifactId>
                <version>1.0.1-SNAPSHOT</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin-java-miniapp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huifu.adapay.core</groupId>
                <artifactId>adapay-core-sdk</artifactId>
                <version>${adapay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huifu.adapay</groupId>
                <artifactId>adapay-java-sdk</artifactId>
                <version>${adapay.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-boot</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel-core</artifactId>
                <version>3.2.1</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.meituan.servicecatalog</groupId>
                <artifactId>api-annotations</artifactId>
                <version>1.0.8</version>
            </dependency>

            <dependency>
                <groupId>com.facebook.swift</groupId>
                <artifactId>swift-annotations</artifactId>
                <version>0.15.6</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>himall-web-spring-boot-starter</artifactId>
                <version>${hishop-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>hishop-sms-spring-boot-starter</artifactId>
                <version>${hishop-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>hishop-logistics-spring-boot-starter</artifactId>
                <version>${hishop-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>hishop-version-spring-boot-starter</artifactId>
                <version>${hishop-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>hishop-license-spring-boot-starter</artifactId>
                <version>${hishop-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>hishop-s3-spring-boot-starter</artifactId>
                <version>${hishop-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>xxl-job-client-spring-boot-starter</artifactId>
                <version>${hishop-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>html2pdf</artifactId>
                <version>2.0.2</version>
            </dependency>
            <dependency>
                <groupId>org.finance.keykaiser</groupId>
                <artifactId>keykaiser</artifactId>
                <version>0.96</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-statemachine</artifactId>
                <version>4.3.1</version>
            </dependency>

<!--            导入pagehelper包-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>6.1.0</version>
            </dependency>

            <!--        lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.30</version>
            </dependency>
<!--            阿里云滑块验证-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.5.13</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-afs</artifactId>
                <version>1.0.1</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <repositories>
        <repository>
            <id>maven-public</id>
            <url>https://nexus.35hiw.com/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>4.9.10</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>initialize</phase>
                    </execution>
                </executions>
                <configuration>
                    <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
                    <prefix>git</prefix>
                    <dateFormat>yyyy-MM-dd'T'HH:mm:ssZ</dateFormat>
                    <dateFormatTimeZone>${user.timezone}</dateFormatTimeZone>
                    <!-- 指定git信息文件是否生成。缺失则默认为false -->
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties</generateGitPropertiesFilename>
                    <skipPoms>true</skipPoms>
                    <format>properties</format>
                    <injectAllReactorProjects>false</injectAllReactorProjects>
<!--                    <includeOnlyProperties>-->
<!--                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>-->
<!--                        <includeOnlyProperty>^git.commit.id.(abbrev|full)$</includeOnlyProperty>-->
<!--                    </includeOnlyProperties>-->
                    <commitIdGenerationMode>full</commitIdGenerationMode>
                </configuration>
            </plugin>
        </plugins>

    </build>

</project>