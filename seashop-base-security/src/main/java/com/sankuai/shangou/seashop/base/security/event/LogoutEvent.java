package com.sankuai.shangou.seashop.base.security.event;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description:
 */
@Getter
public class LogoutEvent extends ApplicationEvent {
    private String token;
    private RoleEnum userType;
    public LogoutEvent(Object source,String token,RoleEnum userType) {
        super(source);
        this.token = token;
        this.userType = userType;
    }
}
