package com.sankuai.shangou.seashop.base.security.aop;


import cn.hutool.core.util.ReflectUtil;
import com.sankuai.shangou.seashop.base.boot.dto.BaseAccountDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.handler.AuthenticationHandler;
import com.sankuai.shangou.seashop.base.security.utils.TokenUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: LXH
 **/
@Aspect
@Component
@ConditionalOnProperty(value = "loginCheck", havingValue = "true", matchIfMissing = false)
public class LoginAspect {
    private static final Logger log = LoggerFactory.getLogger(LoginAspect.class);

    @Resource
    private List<AuthenticationHandler> authenticationHandlerList;

    @Pointcut("@annotation(com.sankuai.shangou.seashop.base.security.annotation.NeedLogin)")
    public void pointCut() {
    }


    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        //获取方法上注解详细信息 授权
        MethodSignature sign = (MethodSignature) joinPoint.getSignature();
        //获取方法上的注解
        NeedLogin annotation = sign.getMethod().getAnnotation(NeedLogin.class);
        //验证类型
        RoleEnum authUserType = annotation.userType();
        LoginBaseDto loginDto = null;
        //是否强制登录
        boolean force = annotation.force();
        //获取方法参数
        Object[] args = joinPoint.getArgs();
        //如果不是强制校验的接口检查出错直接fangx
        //运营后台或卖家后台登录才需要校验 角色权限及功能权限
        try {
            if (RoleEnum.MANAGER.equals(authUserType) || RoleEnum.SHOP.equals(authUserType)) {
                loginDto = forceCheckLogin(annotation, authUserType);
            } else {
                loginDto = forceCheckUserLogin(authUserType);
            }
        }catch (Exception e){
            log.error("login aop method execute error:{}", e);
            //如果不是强制登录的直接放行
            if (!force){
                return joinPoint.proceed(args);
            }
            throw e;
        }
        Object result = null;
        //赋值参数登录信息
        parseLoginUserParams(args, sign, authUserType, loginDto);
        try {
            result = joinPoint.proceed(args);
        } catch (Exception e) {
            log.error(" login aop method execute error:{}", e);
        } finally {
            TracerUtil.clear(authUserType);
        }

        return result;
    }

    /**
     * 强制校验平台用户是否登录
     *
     * @param annotation
     * @return
     */
    private LoginBaseDto forceCheckLogin(NeedLogin annotation, RoleEnum authUserType) {
        AuthenticationHandler authHandler = getAuthenticationHandler(authUserType);
        //解析请求中的token
        String tokenValue = TokenUtil.getRequestToken(authHandler.getTokenKey());
        if (StringUtils.isEmpty(tokenValue)) {
            throw new LoginException(LoginErrorEnum.INVALID_TOKEN);
        }if (Objects.isNull(authHandler)){
            return null;
        }
        //获取登录用户数据
        LoginBaseDto loginDto = authHandler.getAccountAndCheckToken(tokenValue);
        //验证请求接口功能权限
        authHandler.checkUri(annotation, loginDto);
        //设置当前thread中的用户信息
        TracerUtil.putUserContext(loginDto, tokenValue, authUserType);
        return loginDto;
    }

    /**
     * 强制校验会员/ 卖家是否登录
     *
     * @return
     */
    private LoginBaseDto forceCheckUserLogin(RoleEnum authUserType) {
        AuthenticationHandler authHandler = getAuthenticationHandler(authUserType);
        if (Objects.isNull(authHandler)){
            return null;
        }
        //解析请求中的token
        String tokenValue = TokenUtil.getRequestToken(authHandler.getTokenKey());
        if (StringUtils.isEmpty(tokenValue)) {
            throw new LoginException(LoginErrorEnum.INVALID_TOKEN);
        }
        //获取登录用户数据
        LoginBaseDto loginDto = authHandler.getAccountAndCheckToken(tokenValue);
        //设置当前thread中的用户信息
        TracerUtil.putUserContext(loginDto, tokenValue, authUserType);
        return loginDto;
    }

    /**
     * 赋值接口中存在需要赋值的参数
     *
     * @param args
     * @param sign
     * @param loginDto
     */
    private void parseLoginUserParams(Object[] args, MethodSignature sign, RoleEnum authUserType, LoginBaseDto loginDto) {
        if (Objects.isNull(loginDto)) {
            return;
        }
        Class<Object>[] classes = sign.getParameterTypes();
        log.debug("登录信息:{}", JsonUtil.toJsonString(loginDto));
        try {
            for (int i = 0; i < classes.length; i++) {
                Class<Object> clazz = classes[i];
                if (clazz.equals(BaseAccountDto.class) || clazz.getSuperclass().equals(BaseAccountDto.class)) {
                    //设置参数中的用户信息
                    ReflectUtil.setFieldValue(args[i], "id", loginDto.getId());
                    ReflectUtil.setFieldValue(args[i], "name", loginDto.getId());
                } else if (clazz.equals(BaseParamReq.class) || clazz.getSuperclass().equals(BaseParamReq.class)) {
                    if (RoleEnum.SHOP.equals(authUserType)) {
                        LoginShopDto shopDto = (LoginShopDto) loginDto;
                        //设置参数中的用户信息
                        ReflectUtil.setFieldValue(args[i], "operationShopId", shopDto.getShopId());
                        ReflectUtil.setFieldValue(args[i], "operationUserId", shopDto.getManagerId());
                    } else {
                        //设置参数中的用户信息
                        ReflectUtil.setFieldValue(args[i], "operationUserId", loginDto.getId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("The assignment failed because of the :{}", e.getMessage());
        }
    }

    AuthenticationHandler getAuthenticationHandler(RoleEnum roleEnum){
        return authenticationHandlerList.stream().filter(authenticationHandler -> authenticationHandler.getRoleEnum().equals(roleEnum)).findFirst().orElse(null);
    }

}
