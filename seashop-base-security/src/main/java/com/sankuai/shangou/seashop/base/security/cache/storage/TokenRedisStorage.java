package com.sankuai.shangou.seashop.base.security.cache.storage;

import com.sankuai.shangou.seashop.base.security.utils.FoxUtl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: cdd
 * @date: 2024/5/13/013
 * @description: token redis 存储
 */
@Component
public class TokenRedisStorage {
    /**
     * 字符串类型redisTemplate
     */
    public StringRedisTemplate stringRedisTemplate;
    /**
     * 泛型object redisTemplate
     */
    public RedisTemplate<String, Object> objectRedisTemplate;
    /**
     * 是否初始化完成
     */
    public boolean isInit;

    public TokenRedisStorage() {
    }

    /**
     * 初始化 redis 操作对象
     *
     * @param connectionFactory
     */
    @Autowired
    public void init(RedisConnectionFactory connectionFactory) {
        if (!this.isInit) {
            StringRedisSerializer keySerializer = new StringRedisSerializer();
            Jackson2JsonRedisSerializer<Object> valueSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
            StringRedisTemplate stringTemplate = new StringRedisTemplate();
            stringTemplate.setConnectionFactory(connectionFactory);
            stringTemplate.afterPropertiesSet();
            RedisTemplate<String, Object> template = new RedisTemplate();
            template.setConnectionFactory(connectionFactory);
            template.setKeySerializer(keySerializer);
            template.setHashKeySerializer(keySerializer);
            template.setValueSerializer(valueSerializer);
            template.setHashValueSerializer(valueSerializer);
            template.afterPropertiesSet();
            this.stringRedisTemplate = stringTemplate;
            this.objectRedisTemplate = template;
            this.isInit = true;
        }
    }

    /***
     * 根据key获取 string value
     * @param key
     * @return
     */
    public String get(String key) {
        //compress
        return (String) this.stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * 根据 key value timeout设置一个值到redis
     *
     * @param key
     * @param value
     * @param seconds -1 代表永久有效
     */
    public void set(String key, String value, long seconds) {
        if (seconds != 0L && seconds > -2L) {
            if (seconds == -1L) {
                this.stringRedisTemplate.opsForValue().set(key, value);
            } else {
                this.stringRedisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
            }
        }
    }

    /**
     * 根据key 更新 value
     *
     * @param key
     * @param value
     */
    public void update(String key, String value) {
        long expire = this.getTimeout(key);
        if (expire != -2L) {
            this.set(key, value, expire);
        }
    }

    /**
     * 根据key删除
     *
     * @param key
     */
    public void delete(String key) {
        this.stringRedisTemplate.delete(key);
    }

    /**
     * 根据key 获取剩余ttl
     *
     * @param key
     * @return
     */
    public long getTimeout(String key) {
        return this.stringRedisTemplate.getExpire(key);
    }

    /**
     * 重新设置key的 ttl
     *
     * @param key
     * @param timeout
     */
    public void updateTimeout(String key, long timeout) {
        if (timeout == -1L) {
            long expire = this.getTimeout(key);
            if (expire != -1L) {
                this.set(key, this.get(key), timeout);
            }

        } else {
            this.stringRedisTemplate.expire(key, timeout, TimeUnit.SECONDS);
        }
    }

    /***
     * 根据key获取Object数据
     * @param key
     * @return
     */
    public Object getObject(String key) {
        return this.objectRedisTemplate.opsForValue().get(key);
    }

    /***
     * 设置一个object值到redis
     * @param key
     * @param object
     * @param timeout
     */
    public void setObject(String key, Object object, long timeout) {
        if (timeout != 0L && timeout > -2L) {
            if (timeout == -1L) {
                this.objectRedisTemplate.opsForValue().set(key, object);
            } else {
                this.objectRedisTemplate.opsForValue().set(key, object, timeout, TimeUnit.SECONDS);
            }

        }
    }

    /***
     * 更新
     * @param key
     * @param object
     */
    public void updateObject(String key, Object object) {
        long expire = this.getObjectTimeout(key);
        if (expire != -2L) {
            this.setObject(key, object, expire);
        }
    }

    /**
     * 删除
     *
     * @param key
     */
    public void deleteObject(String key) {
        this.objectRedisTemplate.delete(key);
    }

    /**
     * 获取ttl
     *
     * @param key
     * @return
     */
    public long getObjectTimeout(String key) {
        return this.objectRedisTemplate.getExpire(key);
    }

    /**
     * 更新ttl
     *
     * @param key
     * @param timeout
     */
    public void updateObjectTimeout(String key, long timeout) {
        if (timeout == -1L) {
            long expire = this.getObjectTimeout(key);
            if (expire != -1L) {
                this.setObject(key, this.getObject(key), timeout);
            }

        } else {
            this.objectRedisTemplate.expire(key, timeout, TimeUnit.SECONDS);
        }
    }

    /**
     * 搜索
     *
     * @param prefix   key前缀
     * @param keyword  关键字
     * @param start    开始
     * @param size     步长
     * @param sortType 排序类型
     * @return
     */
    public List<String> searchData(String prefix, String keyword, int start, int size, boolean sortType) {
        List<String> list = scan(prefix, keyword, size);
        return FoxUtl.searchList(list, start, size, sortType);
    }

    /**
     * 使用scan替换keys *搜索key
     *
     * @param prefix
     * @param keyword
     * @return
     */
    public List<String> scan(String prefix, String keyword, int count) {
        List<String> list = this.stringRedisTemplate.execute((RedisCallback<List<String>>) connection -> {
            Cursor<byte[]> x = connection.scan(ScanOptions.scanOptions().count(count).match(prefix + "*" + keyword + "*").build());
            List<String> keys = new ArrayList(count);
            x.forEachRemaining((bytes -> {
                keys.add(new String(bytes));
            }));
            return keys;
        });
        return list;
    }
}
