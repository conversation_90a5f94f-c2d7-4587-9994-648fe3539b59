package com.sankuai.shangou.seashop.base.security.utils;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: cdd
 * @date: 2024/5/13/013
 * @description:
 */
public class ServletUtil {
    /**
     * 获取request
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return requestAttributes.getRequest();
    }

    /**
     * 获取response对象
     *
     * @return
     */
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return requestAttributes.getResponse();
    }

    /**
     * 获取请求uri
     *
     * @return
     */
    public static String getUri() {
        return getRequest().getRequestURI();
    }

    /**
     * 获取请求domain
     *
     * @return
     */
    public static String getDomain() {
        return getRequest().getServerName();
    }

    /**
     * 获取IP地址
     *
     * @return
     */
    public static String ip() {
        return cn.hutool.extra.servlet.ServletUtil.getClientIP(getRequest());
    }

    /**
     * 获取IP地址
     *
     * @return
     */
    public static String getUA() {
        return getRequest().getHeader("User-Agent");
    }

    /**
     * 获取所有请求cookie
     *
     * @return
     */
    public static Cookie[] getCookies() {
        return getRequest().getCookies();
    }

    /**
     * 获取url参数
     *
     * @param name
     * @return
     */
    public static String getParam(String name) {
        return getRequest().getParameter(name);
    }


    /**
     * 获取请求header 头信息
     *
     * @param name
     * @return
     */
    public static String getHeader(String name) {
        return getRequest().getHeader(name);
    }

    /**
     * 设置response header
     *
     * @param name
     * @param value
     */
    public static void setHeader(String name, String value) {
        getResponse().setHeader(name, value);
    }

    /**
     * 设置cookie
     * @param name
     * @param value
     */
    public static void setCookie(String name, String value) {
        Cookie cookie = new Cookie(name, value);
        getResponse().addCookie(cookie);
    }

    /**
     * 获取单个cookie
     *
     * @param name
     * @return
     */
    public static Cookie getCookie(String name) {
        Cookie[] cookies = getCookies();
        if (cookies == null || cookies.length == 0) {
            return null;
        }
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals(name)) {
                return cookie;
            }
        }
        return null;
    }

    /**
     * 获取cookie值
     *
     * @param name
     * @return
     */
    public static String getCookieValue(String name) {
        Cookie cookie = getCookie(name);
        if (cookie != null) {
            return cookie.getValue();
        }
        return null;
    }
}
