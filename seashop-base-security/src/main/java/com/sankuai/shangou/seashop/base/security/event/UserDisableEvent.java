package com.sankuai.shangou.seashop.base.security.event;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @author: cdd
 * @date: 2024/5/20/020
 * @description:
 */
@Getter
public class UserDisableEvent extends ApplicationEvent {
    private LoginBaseDto baseDto;

    public UserDisableEvent(Object source, LoginBaseDto baseDto) {
        super(source);
        this.baseDto = baseDto;
    }
}
