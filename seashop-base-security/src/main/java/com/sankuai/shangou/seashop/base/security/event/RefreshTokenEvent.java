package com.sankuai.shangou.seashop.base.security.event;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description:
 */
@Getter
public class RefreshTokenEvent extends ApplicationEvent {
    private String token;
    private String newToken;
    private RoleEnum userType;
    private RoleEnum newType;
    public RefreshTokenEvent(Object source, String token, String newToken, RoleEnum userType, RoleEnum newType) {
        super(source);
        this.token = token;
        this.newToken = newToken;
        this.userType = userType;
        this.newType = newType;
    }
}
