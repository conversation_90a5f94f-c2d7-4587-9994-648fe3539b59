package com.sankuai.shangou.seashop.base.security.event;

import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description:
 */
@Slf4j
@Getter
public class LoginEvent extends ApplicationEvent {
    private TokenCache tokenCache;
    private Object userInfo;

    public LoginEvent(Object source, TokenCache tokenCache, Object userInfo) {
        super(source);
        this.tokenCache = tokenCache;
        this.userInfo = userInfo;
    }
}
