package com.sankuai.shangou.seashop.base.security.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: cdd
 * @date: 2024/5/11/011
 * @description: 登录权限配置注入
 */
@Configuration
public class LoginSecurityAutoConfig {

    @Bean
    @ConfigurationProperties(
        prefix = "hm-token"
    )
    public LoginSecurityConfig getSecurityConfig() {
        return new LoginSecurityConfig();
    }

}
