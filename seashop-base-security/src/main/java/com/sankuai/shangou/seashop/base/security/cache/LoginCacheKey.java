package com.sankuai.shangou.seashop.base.security.cache;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.constant.LoginConstant;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;

/**
 * @author: cdd
 * @date: 2024/5/15/015
 * @description: 获取缓存key
 */
public class LoginCacheKey {
    /**
     * 根据用户类型构建缓存key
     *
     * @param userType
     * @param token
     * @return
     */
    public static String getUserTokenKey(RoleEnum userType, String token) {
        return LoginConstant.LOGIN_KEY_PREFIX + LoginConstant.TOKEN_KEY + getKeyWord(userType) + StrUtil.C_COLON + token;
    }

    /**
     * 根据用户类型构建缓存key
     *
     * @param userType
     * @param userId
     * @return
     */
    public static String getUserKey(RoleEnum userType, Long userId) {
        return LoginConstant.LOGIN_KEY_PREFIX + LoginConstant.USER_INFO_KEY + getKey<PERSON>ord(userType) + StrUtil.C_COLON + userId;
    }

    /**
     * userId+token做key
     *
     * @param userType
     * @param userId
     * @return
     */
    public static String getUserBindTokenKey(RoleEnum userType, Long userId, String token) {
        return LoginConstant.LOGIN_KEY_PREFIX + LoginConstant.BIND_TOKEN_KEY + getKeyWord(userType) + StrUtil.C_COLON + userId + StrUtil.C_COLON + token;
    }

    public static String getUserBindTokenKey(RoleEnum userType, String token) {
        return LoginConstant.LOGIN_KEY_PREFIX + LoginConstant.BIND_TOKEN_KEY + getKeyWord(userType) + StrUtil.C_COLON + "*" + StrUtil.C_COLON + token;
    }

    /**
     * 提取关键字
     *
     * @param userType
     * @return
     */
    public static String getKeyWord(RoleEnum userType) {
        return userType.name().toLowerCase();
    }

    /**
     * 构建登录失败次数key
     * @param userType 用户类型
     * @param userId 用户id
     * @return key
     */
    public static String getLoginFailKey(RoleEnum userType, Long userId) {
        return LoginConstant.LOGIN_KEY_PREFIX + LoginConstant.LOGIN_FAIL_KEY + getKeyWord(userType) + StrUtil.C_COLON + userId;
    }
}
