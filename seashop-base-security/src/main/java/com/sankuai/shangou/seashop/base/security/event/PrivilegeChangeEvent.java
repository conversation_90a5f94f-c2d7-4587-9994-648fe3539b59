package com.sankuai.shangou.seashop.base.security.event;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @author: cdd
 * @date: 2024/5/20/020
 * @description: 权限变更事件
 */
@Getter
public class PrivilegeChangeEvent extends ApplicationEvent {
    private LoginBaseDto baseDto;

    public PrivilegeChangeEvent(Object source, LoginBaseDto baseDto) {
        super(source);
        this.baseDto = baseDto;
    }
}
