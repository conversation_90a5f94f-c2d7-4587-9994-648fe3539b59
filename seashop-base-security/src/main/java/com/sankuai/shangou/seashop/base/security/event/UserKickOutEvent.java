package com.sankuai.shangou.seashop.base.security.event;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class UserKickOutEvent extends ApplicationEvent {
    private final LoginBaseDto baseDto;

    public UserKickOutEvent(Object source, LoginBaseDto baseDto) {
        super(source);
        this.baseDto = baseDto;
    }
}