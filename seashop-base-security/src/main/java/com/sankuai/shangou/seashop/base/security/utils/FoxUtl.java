package com.sankuai.shangou.seashop.base.security.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * @author: cdd
 * @date: 2024/5/13/013
 * @description:
 */
public class FoxUtl {
    private static final String randomStr = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final String randomNumberStr = "1234567890";
    /**
     * 从集合里查询数据
     * @param list 数据集合
     * @param start 起始位置
     * @param size  获取条数 (-1代表从start处一直取到末尾)
     * @param sortType 排序类型（true=正序，false=反序）
     * @return 符合条件的新数据集合
     */
    public static List<String> searchList(List<String> list, int start, int size, boolean sortType) {
        // 如果是反序的话
        if( ! sortType) {
            Collections.reverse(list);
        }
        // start 至少为0
        if (start < 0) {
            start = 0;
        }
        // size为-1时，代表一直取到末尾，否则取到 start + size
        int end;
        if(size == -1) {
            end = list.size();
        } else {
            end = start + size;
        }
        // 取出的数据放到新集合中
        List<String> list2 = new ArrayList<>();
        for (int i = start; i < end; i++) {
            // 如果已经取到list的末尾，则直接退出
            if (i >= list.size()) {
                return list2;
            }
            list2.add(list.get(i));
        }
        return list2;
    }

    /**
     * 生成指定长度的随机字符串
     *
     * @param length 字符串的长度
     * @return 一个随机字符串
     */
    public static String getRandomString(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = ThreadLocalRandom.current().nextInt(62);
            sb.append(randomStr.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 随机生成数字字符串
     *
     * @param length 字符串的长度
     * @return 一个随机字符串
     */
    public static String getRandomNumberStr(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = ThreadLocalRandom.current().nextInt(9);
            sb.append(randomNumberStr.charAt(number));
        }
        return sb.toString();
    }
}
