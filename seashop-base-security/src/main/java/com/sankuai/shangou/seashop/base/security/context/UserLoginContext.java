package com.sankuai.shangou.seashop.base.security.context;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.security.utils.ServletUtil;
import lombok.*;

import java.util.Date;

/**
 * @author: cdd
 * @date: 2024/5/13/013
 * @description: session用户信息
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserLoginContext {
    /**
     * 请求ip
     **/
    private String ip;
    /**
     * 解析出的token
     **/
    private String token;
    /**
     * 请求uri
     **/
    private String uri;
    /**
     * 请求参数
     */
    private String queryString;
    /**
     * 请求service name
     **/
    private String domain;
    /**
     * 请求user-agent
     **/
    private String ua;

    /**
     * 请求时间
     */
    private Date requestTime;

    /**
     * 用户信息
     **/
    private LoginBaseDto loginUser;


    /**
     * 构建请求上下文
     *
     * @return
     */
    public UserLoginContext build(String token) {
        UserLoginContext context = UserLoginContext.builder()
            .ip(ServletUtil.ip())
            .uri(ServletUtil.getUri())
            .domain(ServletUtil.getDomain())
            .ua(ServletUtil.getUA())
            .queryString(ServletUtil.getRequest().getQueryString())
            .token(token)
            .requestTime(new Date())
            .build();
        return context;
    }

}
