package com.sankuai.shangou.seashop.base.security.handler;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;

/**
 * @author: cdd
 * @date: 2024/5/14/014
 * @description: 登录获取用户信息 需子类实现
 */
public interface BaseLoginService {

    /**
     * 根据userId获取平台管理员
     * @param loginBaseDto
     * @return
     */
    public LoginManagerDto getManagerUser(LoginBaseDto loginBaseDto);

    /**
     * 根据userId获取商家会员用户
     * @param loginBaseDto
     * @return
     */
    public LoginMemberDto getMemberUser(LoginBaseDto loginBaseDto);

    /**
     * 根据userId获取为商家信息
     * @param loginBaseDto
     * @return
     */
    public LoginShopDto getShopUser(LoginBaseDto loginBaseDto);

     /**
     * 用户禁用
     *
     * @param userId 用户id
     * @return true:禁用成功 false:禁用失败
     */
    public boolean disableMember(Long userId);

    /**
     * 禁用用户(只是修改cache 中的信息)
     *
     * @param userType 用户类型
     * @param userId   用户id
     * @return true:禁用成功 false:禁用失败
     */
    public boolean disable(RoleEnum userType, Long userId);

    /**
     * 注销用户
     *
     * @param userType 用户类型
     * @param userId   用户id
     * @return true:注销成功 false:注销失败
     */
    public boolean delete(RoleEnum userType, Long userId);

    boolean kickOut(RoleEnum userType, Long userId);
}
