package com.sankuai.shangou.seashop.base.security.annotation;


import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-05-13
 * 权限注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface NeedLogin {
    /**
     * 区分需要拦截的角色权限
     * @return
     */
    RoleEnum userType() default RoleEnum.MEMBER;

    /**
     * 是否校验url权限
     * @return
     */
    boolean checkPrivilege() default false;

    /**
     * 是否强制校验
     * @return boolean
     */
    boolean force() default true;


}