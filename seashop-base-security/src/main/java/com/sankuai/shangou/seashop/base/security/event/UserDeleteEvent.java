package com.sankuai.shangou.seashop.base.security.event;

import org.springframework.context.ApplicationEvent;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;

import lombok.Getter;

/**
 * @author: cdd
 * @date: 2024/5/20/020
 * @description:
 */
@Getter
public class UserDeleteEvent extends ApplicationEvent {
    private LoginBaseDto baseDto;

    public UserDeleteEvent(Object source, LoginBaseDto baseDto) {
        super(source);
        this.baseDto = baseDto;
    }
}
