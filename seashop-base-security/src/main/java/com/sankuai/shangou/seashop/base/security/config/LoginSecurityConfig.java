package com.sankuai.shangou.seashop.base.security.config;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @author: cdd
 * @date: 2024/5/11/011
 * @description: 登录配置
 */
@Getter
@Setter
public class LoginSecurityConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 平台 token 名称
     */
    public static final String TOKEN_NAME = "hm-token";


    /**
     * token 有效期（单位：秒） 默认30天，-1 代表永久有效
     */
    private long timeout = 60 * 60 * 24 * 30;


    /**
     * 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录
     */
    private Boolean isConcurrent = true;

    /**
     * 是否尝试从请参数中读取 token
     */
    private Boolean isReadParams = true;

    /**
     * 是否尝试从 header 里读取 token
     */
    private Boolean isReadHeader = true;


    /**
     * token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128）
     */
    private String tokenStyle = "uuid";



}
