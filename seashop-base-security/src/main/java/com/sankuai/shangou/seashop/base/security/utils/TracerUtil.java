package com.sankuai.shangou.seashop.base.security.utils;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.context.UserLoginContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
/**
 * @description:
 * @author: LXH
 **/
public class TracerUtil {
    /**
     * 使用InheritableThreadLocal 可以让主子线程直接自动传递数据
     */
    private static InheritableThreadLocal<UserLoginContext> TL_MANAGER = new InheritableThreadLocal<>();
    private static InheritableThreadLocal<UserLoginContext> TL_MEMBER = new InheritableThreadLocal<>();

    /**
     * 获取当前系统登录用户信息
     *
     * @return 用户信息
     */
    public static LoginBaseDto getBaseAccount(RoleEnum userType) {
        UserLoginContext context = getThreadLocal(userType).get();
        if (context != null) {
            log.info("token校验通过，获取上下文：{}", JSONUtil.toJsonStr(context.getLoginUser()));
            return context.getLoginUser();
        }
        log.info("token校验通过，获取上下文为空");
        return null;
    }

    public static LoginManagerDto getManagerDto() {
        RoleEnum userType = RoleEnum.MANAGER;
        UserLoginContext context = getThreadLocal(userType).get();
        if (context != null) {
            return (LoginManagerDto) context.getLoginUser();
        }
        return null;
    }

    public static String getIp() {
        RoleEnum userType = RoleEnum.MANAGER;
        UserLoginContext context = getThreadLocal(userType).get();
        if (context != null) {
            return context.getIp();
        }
        return null;
    }

    public static LoginShopDto getShopDto() {
        RoleEnum userType = RoleEnum.SHOP;
        UserLoginContext context = getThreadLocal(userType).get();
        if (context != null) {
            return (LoginShopDto) context.getLoginUser();
        }
        return null;
    }

    public static LoginMemberDto getMemberDto() {
        RoleEnum userType = RoleEnum.MEMBER;
        UserLoginContext context = getThreadLocal(userType).get();
        if (context != null) {
            return (LoginMemberDto) context.getLoginUser();
        }
        return null;
    }

    /**
     * 设置用户信息到上下文
     *
     * @param loginDto
     * @param tokenValue
     * @param userType
     */
    public static void putUserContext(LoginBaseDto loginDto, String tokenValue, RoleEnum userType) {
        UserLoginContext sessionContext = new UserLoginContext();
        sessionContext.build(tokenValue);
        sessionContext.setLoginUser(loginDto);
        getThreadLocal(userType).set(sessionContext);
        if(loginDto != null) {
            log.info("设置用户信息到上下文：{}", JSONUtil.toJsonStr(loginDto));
        }else {
            log.info("设置用户信息到上下文为空");
        }
    }


    /***
     * 获取threadLocal中登录上下文
     * @param userType
     * @return
     */
    public static UserLoginContext getUserContext(RoleEnum userType) {
        InheritableThreadLocal<UserLoginContext> threadLocal = getThreadLocal(userType);
        return threadLocal.get();
    }

    /**
     * 使用完thread local 记得情理，防止内存溢出
     */
    public static void clear(RoleEnum userType) {
        getThreadLocal(userType).remove();
    }


    /**
     * 根据用户类型获取本地线程对象
     *
     * @param userType
     * @return
     */
    private static InheritableThreadLocal<UserLoginContext> getThreadLocal(RoleEnum userType) {
        switch (userType) {
            case MANAGER:
                return TL_MANAGER;
            case MEMBER:
                return TL_MEMBER;
            case SHOP:
                return TL_MEMBER;
        }
        return null;
    }


}
