package com.sankuai.shangou.seashop.base.security.context;

import com.sankuai.shangou.seashop.base.boot.enums.LoginTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description:
 */
@Component
@Slf4j
public class LoginStrategyContext implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private Map<String,LoginStrategy> loginStrategyContext;

    @Override
    public void afterPropertiesSet() throws Exception {
        loginStrategyContext = applicationContext.getBeansOfType(LoginStrategy.class);
        log.info("ImportHandlerContainer init success");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public LoginStrategy getLoginStrategy(LoginTypeEnum loginTypeEnum){
        return loginStrategyContext.get(loginTypeEnum.getCode());
    }
}
