package com.sankuai.shangou.seashop.base.security.handler;


import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.security.cache.storage.TokenRedisStorage;
import com.sankuai.shangou.seashop.base.security.utils.TokenUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 鉴权
 * @author: cdd
 **/

public abstract class AbstractAuthenticationHandler implements AuthenticationHandler {
    @Resource
    protected TokenRedisStorage storage;

    /**
     * 获取适配的角色类型
     */
    public abstract RoleEnum getRoleEnum();

    public abstract String getTokenKey();

    /**
     * 自动获取用户信息
     *
     * @return
     */
    public LoginBaseDto getBaseLoginInfo() {
        //转token缓存对象，并获取用户信息
        TokenCache tokenCache = getTokenCache();
        return tokenCacheToLoginInfo(tokenCache);
    }


    /**
     * 根据请求头部token获取缓存
     *
     * @return
     */
    public TokenCache getTokenCache() {

        String token = TokenUtil.getRequestToken(getTokenKey());
        if (StrUtil.isEmpty(token)) {
            return null;
        }
        Object obj = storage.getObject(LoginCacheKey.getUserTokenKey(getRoleEnum(), token));
        return JsonUtil.parseObject(JsonUtil.toJsonString(obj), TokenCache.class);
    }

    /**
     * @return 用户信息
     */
    public LoginBaseDto getBaseAccountByToken(String token){
        //先从线程threadLocal中获取用户信息
        LoginBaseDto accountDto = TracerUtil.getBaseAccount(getRoleEnum());
        if (Objects.isNull(accountDto)) {
            //从redis缓存中获取
            Object obj = storage.getObject(LoginCacheKey.getUserTokenKey(getRoleEnum(), token));
            if (Objects.nonNull(obj)) {
                TokenCache tokenCache = JsonUtil.parseObject(JsonUtil.toJsonString(obj), TokenCache.class);
                return tokenCacheToLoginInfo(tokenCache);
            }
        }
        return null;
    }

    /**
     * token缓存赚 具体用户信息
     *
     * @param tokenCache
     * @return
     */
    public abstract LoginBaseDto tokenCacheToLoginInfo(TokenCache tokenCache) ;

    /**
     * 验证请求接口是否有权限访问
     *
     * @param annotation
     */
    public abstract void checkUri(NeedLogin annotation, LoginBaseDto loginDto);

    /**
     * 校验token，并返回用户信息
     *
     * @param tokenValue   token
     * @return 用户信息
     */
    public abstract LoginBaseDto getAccountAndCheckToken(String tokenValue);
}
