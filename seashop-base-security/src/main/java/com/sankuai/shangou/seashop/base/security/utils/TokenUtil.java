package com.sankuai.shangou.seashop.base.security.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.seashop.base.boot.constant.LoginConstant;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.base.security.config.LoginSecurityConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

/**
 * @author: cdd
 * @date: 2024/5/13/013
 * @description:
 */
public class TokenUtil {

    /**
     * 检查token是否为空
     *
     * @param token
     */
    public static void checkLoginToke(String token) {
        if (token == null || token.trim().length() == 0) {
            throw new LoginException(LoginErrorEnum.NO_LOGIN);
        }
    }

    /**
     * 检查登录时 用户信息是否为空
     *
     * @param object
     */
    public static void checkLoginObject(Object object) {
        if (object == null) {
            throw new LoginException(LoginErrorEnum.INVALID_TOKEN);
        }
    }

    /**
     * 获取请求信息中token
     *
     * @return
     */
    public static String getRequestToken(String tokenName) {
        LoginSecurityConfig securityConfig = SpringUtil.getBean(LoginSecurityConfig.class);
        if (StringUtils.isEmpty(tokenName)) {
            return null;
        }
        String tokenValue = null;
        // 1. 再尝试从 请求体 参数 读取
        if (StrUtil.isEmpty(tokenValue) && securityConfig.getIsReadParams()) {
            tokenValue = ServletUtil.getParam(tokenName);
        }
        // 2. 再尝试从 header 头里读取
        if (StrUtil.isEmpty(tokenValue) && securityConfig.getIsReadHeader()) {
            tokenValue = ServletUtil.getHeader(tokenName);
        }
        // 3. 至此，不管有没有读取到，都不再尝试了，直接返回
        return tokenValue;
    }


    /**
     * 构建token
     *
     * @return
     */
    public static String createToken() {
        LoginSecurityConfig securityConfig = SpringUtil.getBean(LoginSecurityConfig.class);
        String tokenStyle = securityConfig.getTokenStyle();
        switch (tokenStyle) {
            // uuid
            case LoginConstant.TOKEN_STYLE_UUID:
                return UUID.randomUUID().toString();

            // 简单uuid (不带下划线)
            case LoginConstant.TOKEN_STYLE_SIMPLE_UUID:
                return UUID.randomUUID().toString().replaceAll("-", "");

            // 32位随机字符串
            case LoginConstant.TOKEN_STYLE_RANDOM_32:
                return FoxUtl.getRandomString(32);

            // 64位随机字符串
            case LoginConstant.TOKEN_STYLE_RANDOM_64:
                return FoxUtl.getRandomString(64);

            // 128位随机字符串
            case LoginConstant.TOKEN_STYLE_RANDOM_128:
                return FoxUtl.getRandomString(128);

            // 默认，还是uuid
            default:
                return UUID.randomUUID().toString();
        }
    }


    public static void main(String[] args) {

        TokenCache token = new TokenCache();
        token.setToken("sdfsdfs666666666666666666666666666666666666666");
        token.setUserId(1000L);
        token.setExpiresTime(1716176279890L);
        token.setUserType("MANAGER");
        String data = JSON.toJSONString(token);
        String key =MD5.create().digestHex(data);
        long start = System.currentTimeMillis();
        String aaa = AesUtil.encrypt(data,key);
        System.out.println(aaa);
        System.out.println(key);
        System.out.println(data);
        String aesDecoder = AesUtil.decrypt(aaa,key);
        System.out.println(aesDecoder);
        System.out.println(System.currentTimeMillis()-start);


    }
}
