package com.sankuai.shangou.seashop.base.security.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.SimpleApplicationEventMulticaster;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description: 给event添加异步执行，并使用线程池
 */
@Configuration
public class LoginEvenConfig {

    @Autowired
    @Qualifier("applicationTaskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 使用系统默认的线程池，默认线程池的队列是无边界的，可能会造成线程过多
     * 故一般需要自定义；
     * 这里不自定义，直接注入。需要业务系统创建并覆盖即可
     * @return
     */
    @Bean
    public SimpleApplicationEventMulticaster applicationEventMulticaster() {
        SimpleApplicationEventMulticaster simpleApplicationEventMulticaster = new SimpleApplicationEventMulticaster();
        simpleApplicationEventMulticaster.setTaskExecutor(taskExecutor);
        return simpleApplicationEventMulticaster;
    }
}
