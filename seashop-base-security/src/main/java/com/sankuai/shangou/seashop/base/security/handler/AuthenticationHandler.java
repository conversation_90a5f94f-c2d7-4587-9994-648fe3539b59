package com.sankuai.shangou.seashop.base.security.handler;


import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;

/**
 * @description: 鉴权
 * @author: cdd
 **/

public interface AuthenticationHandler {
    /**
     * 获取适配的角色类型
     *@return  角色类型
     */
    RoleEnum getRoleEnum();

    /**
     * 获取token的key
     *
     * @return  token的key
     */
    String getTokenKey();

    /**
     * 自动获取用户信息
     *
     * @return 用户信息
     */
    LoginBaseDto getBaseLoginInfo();


    /**
     * 根据请求头部token获取缓存
     *
     * @return
     */
    TokenCache getTokenCache();

    /**
     * @return 用户信息
     */
    LoginBaseDto getBaseAccountByToken(String token);

    /**
     * token缓存赚 具体用户信息
     *
     * @param tokenCache
     * @return
     */
    LoginBaseDto tokenCacheToLoginInfo(TokenCache tokenCache);

    /**
     * 验证请求接口是否有权限访问
     *
     * @param annotation
     */
    void checkUri(NeedLogin annotation, LoginBaseDto loginDto);

    /**
     * 校验token，并返回用户信息
     *
     * @param tokenValue   token
     * @return 用户信息
     */
     LoginBaseDto getAccountAndCheckToken(String tokenValue);

}
