package com.sankuai.shangou.seashop.user.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 微信OAuth2获取access_token的响应结果
 * 
 * @author: 系统生成
 * @date: 2025/01/02
 * @description: 微信网页授权access_token响应对象
 */
@Data
public class WxOAuth2AccessTokenResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 网页授权接口调用凭证，注意：此access_token与基础支持的access_token不同
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * access_token接口调用凭证超时时间，单位（秒）
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;

    /**
     * 用户刷新access_token
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * 用户唯一标识，请注意，在未关注公众号时，用户访问公众号的网页，也会产生一个用户和公众号唯一的OpenID
     */
    @JsonProperty("openid")
    private String openId;

    /**
     * 用户授权的作用域，使用逗号（,）分隔
     */
    @JsonProperty("scope")
    private String scope;

    /**
     * 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段
     */
    @JsonProperty("unionid")
    private String unionId;

    /**
     * 错误码，当请求失败时返回
     */
    @JsonProperty("errcode")
    private Integer errcode;

    /**
     * 错误信息，当请求失败时返回
     */
    @JsonProperty("errmsg")
    private String errmsg;

    /**
     * 判断是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return errcode == null || errcode == 0;
    }

    /**
     * 判断access_token是否有效
     * 
     * @return true表示有效，false表示无效
     */
    public boolean isAccessTokenValid() {
        return isSuccess() && accessToken != null && !accessToken.trim().isEmpty();
    }
}
