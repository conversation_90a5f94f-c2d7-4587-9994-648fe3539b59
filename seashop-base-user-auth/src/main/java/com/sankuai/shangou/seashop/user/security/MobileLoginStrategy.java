package com.sankuai.shangou.seashop.user.security;

import java.util.Objects;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.user.service.WxOAuth2AccessTokenResult;
import com.sankuai.shangou.seashop.user.service.WxOAuth2Service;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.enums.LoginPlatformEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.context.LoginStrategy;
import com.sankuai.shangou.seashop.user.account.service.MemberService;
import com.sankuai.shangou.seashop.user.common.config.EncryptConfig;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.common.remote.base.MessageRemoteService;
import com.sankuai.shangou.seashop.user.convert.LoginDtoConvert;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.service.WxMiniService;
import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberEnum;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description: 手机验证码登录
 */
@Component("mobile")
public class MobileLoginStrategy implements LoginStrategy {
    @Resource
    private MemberService memberService;
    @Resource
    private MemberRepository memberRepository;
    @Resource
    private MessageRemoteService messageRemoteService;
    @Resource
    private ManagerRepository managerRepository;
    @Resource
    private EncryptConfig encryptConfig;
    @Resource
    private WxMiniService wxMiniService;
    @Resource
    private MemberAssist memberAssist;
    @Resource
    private WxOAuth2Service wxOAuth2Service;

    @Override
    public LoginBaseDto process(LoginReq req) {
        String phone = req.getPhone();
        String code = req.getPhoneCode();
        LoginPlatformEnum loginPlatform = LoginPlatformEnum.valueOf(req.getLoginPlatform());
        boolean checkResult = messageRemoteService.checkSmsCode(phone, code);
        if (!checkResult) {
            throw new LoginException(UserResultCodeEnum.VERIFICATION_CODE_ERROR.getMsg());
        }
        //数据库时加密的手机号，所以需要加密查询
        String orgPhone = phone;
        phone = AesUtil.encrypt(phone, encryptConfig.getAesSecret());
        Manager manager = null;
        Member member = null;
        switch (loginPlatform) {
            case PLATFORM_BE:
                //平台后台登录
                manager = managerRepository.getByPhone(phone);
                AssertUtil.throwIfNull(manager, UserResultCodeEnum.NO_SUCH_USER);
                //卖家的店铺ID才大于0，所以不允许登录后台
                AssertUtil.throwIfTrue(manager.getShopId() != null && manager.getShopId() > 0, UserResultCodeEnum.NO_SUCH_USER);
                return LoginDtoConvert.managerToLoginDto(manager);
            case SELLER_BE:
                manager = managerRepository.getByPhone(phone);
                AssertUtil.throwIfNull(manager, UserResultCodeEnum.NO_SUCH_USER);
                AssertUtil.throwIfTrue(manager.getShopId() <= 0, LoginErrorEnum.LOGIN_FAIL);
                member = memberRepository.getByPhone(phone);
                memberAssist.decryptPhone(member);
                return LoginDtoConvert.shopToLoginDto(member, manager);
            case BUSINESS_FE:
                //卖家前端登录
                member = memberRepository.getByPhone(phone);
                memberAssist.decryptPhone(member);
                if (Objects.isNull(member)) {
                    String openId = null;
                    String unionId = null;
                    // 解析微信信息
                    if (StrUtil.isNotEmpty(req.getWeCode())) {
                        WxMaJscode2SessionResult userInfo = wxMiniService.getUserInfoByCode(req.getWeCode());
                        if (userInfo != null) {
                            openId = userInfo.getOpenid();
                            unionId = userInfo.getUnionid();
                        }
                    }
                    //自动注册
                    MemberResp memberResp = memberService.autoRegister(orgPhone, openId, unionId, MemberEnum.PlatformType.WeiXin);
                    if (memberResp != null) {
                        member = JsonUtil.copy(memberResp, Member.class);
                        member.setCellPhone(orgPhone);
                    }
                } else {
                    AssertUtil.throwIfTrue(member.getWhetherLogOut(), "对不起，该账号已注销！");
                    AssertUtil.throwIfTrue(member.getDisabled(), LoginErrorEnum.ACCOUNT_FROZEN);
                    manager = managerRepository.getByUserName(member.getUserName());
                }
                String wxMpOpenId = null;
                String wxMpUnionId = null;
                if(StrUtil.isNotEmpty(req.getCode())) {
                    WxOAuth2AccessTokenResult result = wxOAuth2Service.getAccessToken(req.getCode());
                    wxMpOpenId = result.getOpenId();
                    wxMpUnionId = result.getUnionId();
                    Member upMember = new Member();
                    upMember.setId(member.getId());
                    // 更新到member中
                    memberService.updateWxmp(upMember);
                }
                return LoginDtoConvert.memberToLoginDto(member, manager);
        }
        throw new LoginException("登录失败,非法登录平台");
    }
}
