package com.sankuai.shangou.seashop.user.event.listener;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.security.cache.storage.TokenRedisStorage;
import com.sankuai.shangou.seashop.base.security.event.PrivilegeChangeEvent;
import com.sankuai.shangou.seashop.base.security.handler.BaseLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: cdd
 * @date: 2024/5/20/020
 * @description: 用户权限变更事件处理
 */
@Slf4j
@Component
public class PrivilegeChangeEventListener implements ApplicationListener<PrivilegeChangeEvent> {
    @Resource
    private TokenRedisStorage storage;
    @Resource
    private BaseLoginService baseLoginService;

    @Override
    public void onApplicationEvent(PrivilegeChangeEvent event) {
        LoginBaseDto baseDto = event.getBaseDto();
        if (Objects.isNull(baseDto)) {
            log.debug("权限变更事件，loginBaseDto 不能为空");
        }
        RoleEnum userType = baseDto.getRoleType();
        if (!RoleEnum.MANAGER.equals(userType) && !RoleEnum.SHOP.equals(userType)) {
            log.debug("用户角色:{}，不支持权限变更");
            return;
        }
        String key = LoginCacheKey.getUserKey(userType, baseDto.getId());
        switch (userType) {
            case MANAGER:
                LoginManagerDto managerDto = baseLoginService.getManagerUser(baseDto);
                storage.update(key, JsonUtil.toJsonString(managerDto));
                break;
            case SHOP:
                LoginShopDto shopDto = baseLoginService.getShopUser(baseDto);
                storage.update(key, JsonUtil.toJsonString(shopDto));
                break;
        }
    }
}
