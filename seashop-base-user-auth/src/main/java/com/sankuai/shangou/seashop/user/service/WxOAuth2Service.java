package com.sankuai.shangou.seashop.user.service;

/**
 * 微信OAuth2服务接口
 * 用于处理微信网页授权相关功能
 * 
 * @author: 系统生成
 * @date: 2025/01/02
 * @description: 微信OAuth2授权服务接口
 */
public interface WxOAuth2Service {

    /**
     * 通过授权码获取access_token
     * 
     * @param code 微信授权码，通过微信网页授权获取
     * @return 包含access_token、refresh_token、openid等信息的结果对象
     * @throws com.sankuai.shangou.seashop.base.boot.exception.LoginException 当授权失败时抛出
     */
    WxOAuth2AccessTokenResult getAccessToken(String code);

    /**
     * 刷新access_token
     * 
     * @param refreshToken 刷新令牌
     * @return 包含新的access_token、refresh_token等信息的结果对象
     * @throws com.sankuai.shangou.seashop.base.boot.exception.LoginException 当刷新失败时抛出
     */
    WxOAuth2AccessTokenResult refreshAccessToken(String refreshToken);

    /**
     * 获取用户信息
     * 
     * @param accessToken 网页授权接口调用凭证
     * @param openId 用户的唯一标识
     * @return 用户信息对象
     * @throws com.sankuai.shangou.seashop.base.boot.exception.LoginException 当获取用户信息失败时抛出
     */
    WxOAuth2UserInfo getUserInfo(String accessToken, String openId);
}
