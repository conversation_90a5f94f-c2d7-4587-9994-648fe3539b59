package com.sankuai.shangou.seashop.user.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.service.WxMiniService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: cdd
 * @date: 2024/5/15/015
 * @description:
 */
@Service
@Slf4j
public class WxMiniServiceImpl  implements WxMiniService {
    @Resource(name = "wxMaService")
    public WxMaService wxMaService;
    @Override
    public String getPhoneByCode(String code) {
        try {
            return wxMaService.getUserService().getPhoneNoInfo(code).getPurePhoneNumber();
        }catch (Exception e){
            log.error("获取小程序手机号失败:code={},error={}", e.getMessage());
            throw new LoginException(e.getMessage());
        }
    }

    @Override
    public WxMaJscode2SessionResult getUserInfoByCode(String code) {
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(code);
            return sessionInfo;
        } catch (WxErrorException e) {
            log.error("js授权获取openId失败 code={},error={}", e.getMessage());
        }
        return null;
    }
}
