package com.sankuai.shangou.seashop.user.security;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.security.context.LoginStrategy;
import com.sankuai.shangou.seashop.user.account.service.MemberService;
import com.sankuai.shangou.seashop.user.common.config.EncryptConfig;
import com.sankuai.shangou.seashop.user.common.constant.LockConstant;
import com.sankuai.shangou.seashop.user.convert.LoginDtoConvert;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.service.WxMiniService;
import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberEnum;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description: 微信小程序登录
 */
@Component("wx_mini_code")
@Slf4j
public class WxMiniLoginStrategy implements LoginStrategy {
    @Resource
    private WxMiniService wxMiniService;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private ManagerRepository managerRepository;

    @Resource
    private MemberService memberService;

    @Resource
    private DistributedLockService distributedLockService;

    @Resource
    private EncryptConfig encryptConfig;
    @Resource
    private MemberAssist memberAssist;

    @Override
    public LoginBaseDto process(LoginReq req) {
        String mobileCode = req.getMobileCode();

        // 首先解析手机号
        String phone = wxMiniService.getPhoneByCode(mobileCode);
        //数据库时加密的手机号，所以需要加密查询
        String encryptPhone = AesUtil.encrypt(phone, encryptConfig.getAesSecret());

        // 卖家前端登录
        Member member = memberRepository.getByPhone(encryptPhone);
        // CVE: 当用户首次登录为验证码登陆时 密码会正常加密 第二次使用微信登录时 密码会被以下代码解密并set进member类 进行更新
        // 根据以上漏洞 已补充 第 104 行代码
        memberAssist.decryptPhone(member);
        Manager manager = null;
        if (Objects.isNull(member)) {
            String openId = null;
            String unionId = null;
            // 解析微信信息
            if (StrUtil.isNotEmpty(req.getWeCode())) {
                WxMaJscode2SessionResult userInfo = wxMiniService.getUserInfoByCode(req.getWeCode());
                if (userInfo != null) {
                    openId = userInfo.getOpenid();
                    unionId = userInfo.getUnionid();
                }
            }

            //自动注册
            MemberResp memberResp = memberService.autoRegister(phone, openId, unionId, MemberEnum.PlatformType.WeiXin);
            if (memberResp != null) {
                member = JsonUtil.copy(memberResp, Member.class);
                member.setCellPhone(phone);
            }
        } else {
            if (!StringUtils.hasLength(member.getOpenId()) || !StringUtils.hasLength(member.getUnionId())) {
                log.info("微信授权登录，更新openId");
                if (StrUtil.isNotEmpty(req.getWeCode())) {
                    WxMaJscode2SessionResult userInfo = wxMiniService.getUserInfoByCode(req.getWeCode());
                    if (userInfo != null) {
                        String openId = userInfo.getOpenid();
                        String unionId = userInfo.getUnionid();
                        member.setOpenId(openId);
                        member.setUnionId(unionId);
                        // 将加密后的密码set进去进行更新 防止出现手机号改变导致账号查询不到 详细看 72 行注释
                        member.setCellPhone(encryptPhone);
                        memberRepository.updateById(member);
                    }
                }
            }
            AssertUtil.throwIfTrue(member.getWhetherLogOut(), "对不起，该账号已注销！");
            AssertUtil.throwIfTrue(member.getDisabled(), LoginErrorEnum.ACCOUNT_FROZEN);
            manager = managerRepository.getByUserName(member.getUserName());
        }

        return LoginDtoConvert.memberToLoginDto(member, manager);

        /*String wxCode = req.getWeCode();
        WxMaJscode2SessionResult userResult = wxMiniService.getUserInfoByCode(wxCode);
        AssertUtil.throwIfNull(userResult, LoginErrorEnum.FAILED);
        Member member = memberRepository.getByWxId(userResult.getOpenid(), userResult.getUnionid());
        if (Objects.isNull(member)) {
            member = autoRegister(userResult);
        }
        AssertUtil.throwIfTrue(member.getWhetherLogOut(), "对不起，该账号已注销！");
        Manager manager = managerRepository.getByUserName(member.getUserName());
        return LoginDtoConvert.memberToLoginDto(member, manager);*/
    }


    /**
     * member自动注册
     *
     * @param userResult
     * @return
     */
    private Member autoRegister(WxMaJscode2SessionResult userResult) {
        String key = LockConstant.LOCK_USER_WX_LOGIN_LOCK + userResult.getOpenid();
        try {
            //不存在，分布式锁，防止重复自动注册
            return distributedLockService.tryLock(new LockKey(LockConstant.SCENE_USER_WX_LOGIN_SCENE, key), () -> {
                MemberResp memberResp = memberService.autoRegister(userResult.getOpenid(), userResult.getUnionid());
                AssertUtil.throwIfNull(memberResp, LoginErrorEnum.FAILED);
                return JsonUtil.copy(memberResp, Member.class);
            });
        } catch (Exception e) {
            log.error("微信小程序手机号授权登录失败:自动注册用户失败");
            throw new LoginException(LoginErrorEnum.FAILED);
        } catch (Throwable e) {
            log.error("微信小程序手机号授权登录失败:{}", e);
            throw new LoginException(LoginErrorEnum.FAILED);
        }
    }

}
