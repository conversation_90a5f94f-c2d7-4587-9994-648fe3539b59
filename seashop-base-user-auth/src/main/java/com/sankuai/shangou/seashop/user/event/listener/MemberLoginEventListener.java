package com.sankuai.shangou.seashop.user.event.listener;

import javax.annotation.Resource;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.event.LoginEvent;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description: 登录后的监听事件。可以增加其他逻辑，或发送mq通知其他系统
 */
@Slf4j
@Component
public class MemberLoginEventListener implements ApplicationListener<LoginEvent> {

    @Resource
    private MemberRepository memberRepository;

    @Override
    public void onApplicationEvent(LoginEvent event) {
        log.info("用户:{}, 会员登录成功，事件监听", event.getTokenCache().getUserId());

        try {
            String typeName = event.getTokenCache().getUserType();
            RoleEnum userType =  RoleEnum.nameOf(typeName);
            if (ObjectUtil.notEqual(RoleEnum.MEMBER, userType)) {
                log.info("用户:{} 不是会员，不处理", event.getTokenCache().getUserId());
                return;
            }

            // 更新会员最后登录的事件
            memberRepository.updateMemberLastLoginTime(event.getTokenCache().getUserId());
            log.info("用户:{}, 会员登录成功，更新最后登录时间成功", event.getTokenCache().getUserId());
        } catch (Exception e) {
            log.error("会员登录处理失败:{}", e);
        }

    }
}
