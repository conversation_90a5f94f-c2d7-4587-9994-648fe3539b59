package com.sankuai.shangou.seashop.user.security;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.enums.LoginPlatformEnum;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.request.AfsReq;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.security.context.LoginStrategy;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.common.service.AfsService;
import com.sankuai.shangou.seashop.user.convert.LoginDtoConvert;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberEnum;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component("sub_acount")
public class SubAccountLoginStrategy implements LoginStrategy {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ManagerRepository managerRepository;
    @Resource
    private MemberRepository memberRepository;
    @Resource
    private AfsService afsService;
    @Resource
    private MemberAssist memberAssist;

    @Override
    public LoginBaseDto process(LoginReq req) {
        String loginName = req.getUserName();
        String password = req.getPassword();
        LoginPlatformEnum loginPlatform = LoginPlatformEnum.valueOf(req.getLoginPlatform());
        Manager manager;
        String loginFailKey;
        switch (loginPlatform) {
            case PLATFORM_BE:
                //平台后台
                manager = managerRepository.getByUserNameOrCellPhone(loginName);
                AssertUtil.throwIfNull(manager, UserResultCodeEnum.NO_SUCH_USER);
                //卖家的店铺ID才大于0，所以不允许登录后台
                AssertUtil.throwIfTrue(manager.getShopId() > 0, LoginErrorEnum.LOGIN_FAIL);
                loginFailKey = LoginCacheKey.getLoginFailKey(RoleEnum.MANAGER,manager.getId());
                checkAfs(req.getSlideCode(), loginFailKey);
                checkPassword(password, manager.getPassword(), manager.getPasswordSalt(), loginFailKey);
                return LoginDtoConvert.managerToLoginDto(manager);
            case SELLER_BE:
                //卖家后台登录
                manager = managerRepository.getByUserNameOrCellPhone(loginName);
                AssertUtil.throwIfNull(manager, UserResultCodeEnum.NO_SUCH_USER);
                AssertUtil.throwIfTrue(manager.getShopId() <= 0, LoginErrorEnum.LOGIN_FAIL);
                loginFailKey = LoginCacheKey.getLoginFailKey(RoleEnum.SHOP,manager.getId());
                checkAfs(req.getSlideCode(), loginFailKey);
                checkPassword(password, manager.getPassword(), manager.getPasswordSalt(), loginFailKey);
                return LoginDtoConvert.managerToLoginDto(manager);
            case BUSINESS_FE:
                manager = managerRepository.getByUserNameOrCellPhone(loginName);
                AssertUtil.throwIfNull(manager, UserResultCodeEnum.NO_SUCH_USER);
                AssertUtil.throwIfTrue(manager.getShopId() <= 0, LoginErrorEnum.LOGIN_FAIL);
                loginFailKey = LoginCacheKey.getLoginFailKey(RoleEnum.SHOP,manager.getId());
                checkAfs(req.getSlideCode(), loginFailKey);
                checkPassword(password, manager.getPassword(), manager.getPasswordSalt(), loginFailKey);
                LoginShopDto r = LoginDtoConvert.shopToLoginDto(null, manager);
                r.setRoleType(RoleEnum.MEMBER);
                return r;
        }
        throw new LoginException("登录失败,非法登录平台");
    }

    private void checkAfs(AfsReq slideCode, String loginFailKey) {
//        如果失败次数大于三次，则抛出异常
        Long times = stringRedisTemplate.opsForValue().increment(loginFailKey,0);
        if (times != null && times > 3) {
            if (slideCode == null){
                throw new BusinessException(LoginErrorEnum.FAILED_TOO_MANY_TIMES);
            }
            AssertUtil.throwIfTrue(!afsService.verify(slideCode.getRemoteIp(), slideCode.getScene(), slideCode.getSessionId(), slideCode.getSig(), slideCode.getToken()), LoginErrorEnum.FAILED_MACHINE_VERIFY);
        }
    }


    /***
     * 密码校验
     * @param password 登录密码
     * @param password1 存储的加密密码
     * @param passwordSalt 存储的密码salt
     */
    private void checkPassword(String password, String password1, String passwordSalt) {
        //校验密码
        String passwordStr = password + passwordSalt;
        String sha256Password = SecureUtil.sha256(passwordStr);
        if (!StrUtil.equals(sha256Password, password1)) {
            throw new BusinessException(LoginErrorEnum.LOGIN_FAIL);
        }
    }

    /***
     * 密码校验并记录失败次数
     * @param password 登录密码
     * @param password1 存储的加密密码
     * @param passwordSalt 存储的密码salt
     */
    private void checkPassword(String password, String password1, String passwordSalt, String key) {
        //校验密码
        String passwordStr = password + passwordSalt;
        String sha256Password = SecureUtil.sha256(passwordStr);
        if (!StrUtil.equals(sha256Password, password1)) {
//            记录密码失败次数
            Long times = stringRedisTemplate.opsForValue().increment(key);
            if (times != null && times > 3) {
                throw new BusinessException(LoginErrorEnum.FAILED_TOO_MANY_TIMES);
            }
            throw new BusinessException(LoginErrorEnum.LOGIN_FAIL);
        }
//        密码校验通过，重置密码失败次数
        stringRedisTemplate.delete(key);
    }
}
