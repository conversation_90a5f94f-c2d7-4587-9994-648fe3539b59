package com.sankuai.shangou.seashop.user.event.listener;

import javax.annotation.Resource;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.security.cache.storage.TokenRedisStorage;
import com.sankuai.shangou.seashop.base.security.event.UserDisableEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: cdd
 * @date: 2024/5/20/020
 * @description: 用户禁用
 */
@Slf4j
@Component
public class DisableEventListener implements ApplicationListener<UserDisableEvent> {
    @Resource
    private TokenRedisStorage storage;

    @Override
    public void onApplicationEvent(UserDisableEvent event) {
        LoginBaseDto baseDto = event.getBaseDto();
        baseDto.setDisable(true);
        storage.updateObject(LoginCacheKey.getUserKey(baseDto.getRoleType(), baseDto.getId()), baseDto);
    }
}
