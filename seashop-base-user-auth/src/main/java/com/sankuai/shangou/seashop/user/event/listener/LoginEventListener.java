package com.sankuai.shangou.seashop.user.event.listener;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.constant.LoginConstant;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.security.cache.storage.TokenRedisStorage;
import com.sankuai.shangou.seashop.base.security.config.LoginSecurityConfig;
import com.sankuai.shangou.seashop.base.security.event.LoginEvent;
import com.sankuai.shangou.seashop.user.thrift.auth.LoginUserFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description: 登录后的监听事件。可以增加其他逻辑，或发送mq通知其他系统
 */
@Slf4j
@Component
public class LoginEventListener implements ApplicationListener<LoginEvent> {

    @Resource
    private TokenRedisStorage storage;

    @Resource
    private LoginSecurityConfig securityConfig;

    @Resource
    private LoginUserFeign loginUserService;

    @Override
    public void onApplicationEvent(LoginEvent event) {
        log.debug("用户:{},token:{} 登录成功，事件监听", event.getTokenCache().getUserId(), event.getTokenCache().getToken());
        try {
            long timeout = securityConfig.getTimeout();
            TokenCache tokenCache = event.getTokenCache();
            Long userId = event.getTokenCache().getUserId();
            String token = event.getTokenCache().getToken();
            String typeName = event.getTokenCache().getUserType();
            RoleEnum userType =  RoleEnum.nameOf(typeName);
            //将对象存入redis
            String userIdKey = LoginCacheKey.getUserKey(userType, userId);
            String tokenKey = LoginCacheKey.getUserTokenKey(userType, token);
            String userBindTokenKey = LoginCacheKey.getUserBindTokenKey(userType, userId, token);
            //储存 userId -->用户信息 到缓存
            storage.set(userIdKey, JsonUtil.toJsonString(event.getUserInfo()), timeout);
            //储存 token --> token信息到缓存
            storage.set(tokenKey, JsonUtil.toJsonString(tokenCache), timeout);
            //储存 user+token --->是否被踢到缓存
            storage.set(userBindTokenKey, LoginConstant.NO_KICKED, timeout);
            //检查是否允许多人登录
            concurrentCheck(userId, userType, token);
        } catch (Exception e) {
            log.error("登录处理失败:{}", e);
            throw new LoginException(LoginErrorEnum.FAILED);
        }

    }

    /**
     * 检查是否可以多人同时登录
     *
     * @param userId
     * @param userType
     * @param token
     */
    private void concurrentCheck(Long userId, RoleEnum userType, String token) {
        //不允许多人登录
        if (!securityConfig.getIsConcurrent()) {
            String searchKey = LoginCacheKey.getUserBindTokenKey(userType, userId, "");
            //最多取100个同时登录
            List<String> keys = storage.scan("", searchKey, 100);

            if (CollUtil.isNotEmpty(keys)) {
                //根据角色提取key中关键字 并且 排除自己
                String keyword = LoginCacheKey.getKeyWord(userType);
                //排除掉key中 结尾属于当前人token或当前人ID 的key。 同时key属于同一个系统角色的
                keys = keys.stream().filter(key -> (!key.endsWith(token) && key.contains(keyword) && !key.endsWith(":" + userId))).collect(Collectors.toList());
                for (String key : keys){
                    TokenCache tokenCache = new TokenCache();
                    tokenCache.setToken(key);
                    tokenCache.setUserId(userId);
                    tokenCache.setUserType(userType.name());
                    loginUserService.logout(tokenCache);
                }
            }
        }
    }
}
