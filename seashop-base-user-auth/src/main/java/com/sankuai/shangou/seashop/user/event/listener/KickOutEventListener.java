package com.sankuai.shangou.seashop.user.event.listener;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.security.cache.storage.TokenRedisStorage;
import com.sankuai.shangou.seashop.base.security.event.UserKickOutEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class KickOutEventListener implements ApplicationListener<UserKickOutEvent> {
    @Resource
    private TokenRedisStorage storage;

    @Override
    public void onApplicationEvent(UserKickOutEvent event) {
        LoginBaseDto baseDto = event.getBaseDto();
        storage.delete(LoginCacheKey.getUserKey(baseDto.getRoleType(), baseDto.getId()));
    }
}
