package com.sankuai.shangou.seashop.user.controller;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.service.WxOAuth2AccessTokenResult;
import com.sankuai.shangou.seashop.user.service.WxOAuth2Service;
import com.sankuai.shangou.seashop.user.service.WxOAuth2UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 微信OAuth2授权控制器
 * 提供微信网页授权相关接口
 * 
 * @author: 系统生成
 * @date: 2025/01/02
 * @description: 微信OAuth2授权接口
 */
@RestController
@RequestMapping("/wxOAuth2")
@Slf4j
public class WxOAuth2Controller {

    @Resource
    private WxOAuth2Service wxOAuth2Service;

    /**
     * 通过授权码获取access_token
     * 
     * @param code 微信授权码
     * @return access_token信息
     */
    @GetMapping("/getAccessToken")
    public ResultDto<WxOAuth2AccessTokenResult> getAccessToken(@RequestParam("code") String code) {
        return ThriftResponseHelper.responseInvoke("getAccessToken", code, req -> {
            log.info("开始获取微信OAuth2 access_token，code: {}", code);
            WxOAuth2AccessTokenResult result = wxOAuth2Service.getAccessToken(code);
            log.info("成功获取微信OAuth2 access_token，openId: {}", result.getOpenId());
            return result;
        });
    }

    /**
     * 刷新access_token
     * 
     * @param refreshToken 刷新令牌
     * @return 新的access_token信息
     */
    @GetMapping("/refreshAccessToken")
    public ResultDto<WxOAuth2AccessTokenResult> refreshAccessToken(@RequestParam("refresh_token") String refreshToken) {
        return ThriftResponseHelper.responseInvoke("refreshAccessToken", refreshToken, req -> {
            log.info("开始刷新微信OAuth2 access_token");
            WxOAuth2AccessTokenResult result = wxOAuth2Service.refreshAccessToken(refreshToken);
            log.info("成功刷新微信OAuth2 access_token，openId: {}", result.getOpenId());
            return result;
        });
    }

    /**
     * 获取用户信息
     * 
     * @param accessToken 网页授权接口调用凭证
     * @param openId 用户的唯一标识
     * @return 用户信息
     */
    @GetMapping("/getUserInfo")
    public ResultDto<WxOAuth2UserInfo> getUserInfo(@RequestParam("access_token") String accessToken,
                                                   @RequestParam("openid") String openId) {
        return ThriftResponseHelper.responseInvoke("getUserInfo", accessToken + ":" + openId, req -> {
            log.info("开始获取微信OAuth2用户信息，openId: {}", openId);
            WxOAuth2UserInfo result = wxOAuth2Service.getUserInfo(accessToken, openId);
            log.info("成功获取微信OAuth2用户信息，nickname: {}", result.getNickname());
            return result;
        });
    }

    /**
     * 一键获取用户信息（通过code直接获取用户信息）
     * 
     * @param code 微信授权码
     * @return 用户信息
     */
    @GetMapping("/getUserInfoByCode")
    public ResultDto<WxOAuth2UserInfo> getUserInfoByCode(@RequestParam("code") String code) {
        return ThriftResponseHelper.responseInvoke("getUserInfoByCode", code, req -> {
            log.info("开始通过code获取微信OAuth2用户信息，code: {}", code);
            
            // 第一步：获取access_token
            WxOAuth2AccessTokenResult tokenResult = wxOAuth2Service.getAccessToken(code);
            if (!tokenResult.isAccessTokenValid()) {
                throw new RuntimeException("获取access_token失败");
            }
            
            // 第二步：获取用户信息
            WxOAuth2UserInfo userInfo = wxOAuth2Service.getUserInfo(tokenResult.getAccessToken(), tokenResult.getOpenId());
            if (!userInfo.isUserInfoValid()) {
                throw new RuntimeException("获取用户信息失败");
            }
            
            log.info("成功通过code获取微信OAuth2用户信息，openId: {}, nickname: {}", 
                    userInfo.getOpenId(), userInfo.getNickname());
            return userInfo;
        });
    }
}
