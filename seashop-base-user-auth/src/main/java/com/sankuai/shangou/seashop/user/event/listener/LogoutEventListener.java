package com.sankuai.shangou.seashop.user.event.listener;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.security.cache.storage.TokenRedisStorage;
import com.sankuai.shangou.seashop.base.security.event.LogoutEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: cdd
 * @date: 2024/5/16/016
 * @description: 退出登录监听事件
 */
@Slf4j
@Component
public class LogoutEventListener implements ApplicationListener<LogoutEvent> {
    @Resource
    private TokenRedisStorage storage;
    @Override
    public void onApplicationEvent(LogoutEvent event) {
        log.debug("用户token:{} 退出登录，事件监听",event.getToken());

        RoleEnum userType = event.getUserType();
        String token = event.getToken();
        String tokenKey = LoginCacheKey.getUserTokenKey(userType, token);
        String tokeCacheJson = storage.get(tokenKey);
        if (StringUtils.isEmpty(tokeCacheJson)) {
            return;
        }
        TokenCache tokenCache = JsonUtil.parseObject(tokeCacheJson, TokenCache.class);
        Long userId = tokenCache.getUserId();
        String userIdKey = LoginCacheKey.getUserKey(userType, userId);
        String bindKey = LoginCacheKey.getUserBindTokenKey(userType, userId, token);
        storage.delete(tokenKey);
        // storage.delete(userIdKey);
        storage.delete(bindKey);

    }
}
