package com.sankuai.shangou.seashop.user.controller;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.security.LoginService;
import com.sankuai.shangou.seashop.user.thrift.auth.AuthUserFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: cdd
 * @date: 2024/5/17/017
 * @description:
 */
@RestController
@RequestMapping("/authInfo")
public class AuthInfoController implements AuthUserFeign {

    @Autowired
    private LoginService loginService;

    @PostMapping(value = "/getManagerUser",consumes = "application/json" )
    @Override
    public ResultDto<LoginManagerDto> getManagerUser(@RequestBody LoginBaseDto loginBaseDto) {
        return ThriftResponseHelper.responseInvoke("getManagerInfo",loginBaseDto,req-> loginService.getManagerUser(loginBaseDto));
    }

    @PostMapping(value = "/getMemberUser",consumes = "application/json" )
    @Override
    public ResultDto<LoginMemberDto> getMemberUser(@RequestBody LoginBaseDto loginBaseDto) {
        return ThriftResponseHelper.responseInvoke("getMemberUser",loginBaseDto,req-> loginService.getMemberUser(loginBaseDto));
    }

    @PostMapping(value = "/getShopUser",consumes = "application/json" )
    @Override
    public ResultDto<LoginShopDto> getShopUser(@RequestBody LoginBaseDto loginBaseDto) {
        return ThriftResponseHelper.responseInvoke("getShopUser",loginBaseDto,req-> loginService.getShopUser(loginBaseDto));
    }
}
