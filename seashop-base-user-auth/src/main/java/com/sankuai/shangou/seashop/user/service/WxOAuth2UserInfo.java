package com.sankuai.shangou.seashop.user.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 微信OAuth2获取用户信息的响应结果
 * 
 * @author: 系统生成
 * @date: 2025/01/02
 * @description: 微信网页授权用户信息响应对象
 */
@Data
public class WxOAuth2UserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户的唯一标识
     */
    @JsonProperty("openid")
    private String openId;

    /**
     * 用户昵称
     */
    @JsonProperty("nickname")
    private String nickname;

    /**
     * 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    @JsonProperty("sex")
    private Integer sex;

    /**
     * 用户个人资料填写的省份
     */
    @JsonProperty("province")
    private String province;

    /**
     * 普通用户个人资料填写的城市
     */
    @JsonProperty("city")
    private String city;

    /**
     * 国家，如中国为CN
     */
    @JsonProperty("country")
    private String country;

    /**
     * 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），
     * 用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。
     */
    @JsonProperty("headimgurl")
    private String headImgUrl;

    /**
     * 用户特权信息，json 数组，如微信沃卡用户为（chinaunicom）
     */
    @JsonProperty("privilege")
    private List<String> privilege;

    /**
     * 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段
     */
    @JsonProperty("unionid")
    private String unionId;

    /**
     * 错误码，当请求失败时返回
     */
    @JsonProperty("errcode")
    private Integer errcode;

    /**
     * 错误信息，当请求失败时返回
     */
    @JsonProperty("errmsg")
    private String errmsg;

    /**
     * 判断是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return errcode == null || errcode == 0;
    }

    /**
     * 获取性别描述
     * 
     * @return 性别描述字符串
     */
    public String getSexDesc() {
        if (sex == null) {
            return "未知";
        }
        switch (sex) {
            case 1:
                return "男";
            case 2:
                return "女";
            default:
                return "未知";
        }
    }

    /**
     * 判断用户信息是否有效
     * 
     * @return true表示有效，false表示无效
     */
    public boolean isUserInfoValid() {
        return isSuccess() && openId != null && !openId.trim().isEmpty();
    }
}
