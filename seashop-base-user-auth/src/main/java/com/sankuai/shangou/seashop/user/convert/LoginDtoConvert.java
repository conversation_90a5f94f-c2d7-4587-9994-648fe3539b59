package com.sankuai.shangou.seashop.user.convert;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;

import java.util.Objects;

/**
 * @author: cdd
 * @date: 2024/5/15/015
 * @description:
 */
public class LoginDtoConvert {

    public static LoginBaseDto managerToLoginDto(Manager manager) {
        if (Objects.isNull(manager)) {
            return null;
        }
        LoginManagerDto managerDto = new LoginManagerDto();
        managerDto.setId(manager.getId());
        managerDto.setName(manager.getUserName());
        managerDto.setRoleType(RoleEnum.MANAGER);
        managerDto.setDisable(false);
        return managerDto;

    }


    public static LoginBaseDto memberToLoginDto(Member member, Manager manager) {
        LoginMemberDto memberDto = new LoginMemberDto();
        if (Objects.isNull(member)) {
            memberDto.setRoleType(RoleEnum.MEMBER);
            memberDto.setId(0L);
            memberDto.setName(manager.getUserName());
            memberDto.setUserPhone(manager.getCellphone());
            memberDto.setDisable(false);
            if (Objects.nonNull(manager)){
                memberDto.setShopId(manager.getShopId());
                memberDto.setManagerId(manager.getId());
            }
            return memberDto;
        }
        else{
            memberDto.setRoleType(RoleEnum.MEMBER);
            memberDto.setId(member.getId());
            memberDto.setName(member.getUserName());
            memberDto.setUserPhone(member.getCellPhone());
            memberDto.setWeiXinOpenId(member.getOpenId());
            memberDto.setWeiXinUnionId(member.getUnionId());
            memberDto.setWeiXinUser(StrUtil.isNotBlank(member.getOpenId()) ? true : false);
            memberDto.setDisable(false);
            if (Objects.nonNull(manager)){
                memberDto.setShopId(manager.getShopId());
                memberDto.setManagerId(manager.getId());
            }
            return memberDto;
        }
    }

    public static LoginShopDto shopToLoginDto(Member member, Manager manager) {
        if (Objects.isNull(manager)){
            return null;
        }
        LoginShopDto shopDto = new LoginShopDto();
        shopDto.setId(manager.getId());
        shopDto.setName(manager.getUserName());
        shopDto.setManagerId(manager.getId());
        shopDto.setManagerName(manager.getUserName());
        shopDto.setShopId(manager.getShopId());
        if (Objects.nonNull(member)) {
            shopDto.setUserId(member.getId());
            shopDto.setUserName(member.getUserName());
            shopDto.setUserPhone(member.getCellPhone());
        }
        shopDto.setDisable(false);
        shopDto.setRoleType(RoleEnum.SHOP);
        return shopDto;
    }
}
