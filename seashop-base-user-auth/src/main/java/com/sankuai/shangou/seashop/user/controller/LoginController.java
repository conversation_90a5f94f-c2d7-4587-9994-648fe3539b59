package com.sankuai.shangou.seashop.user.controller;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.common.util.PassportUtils;
import com.sankuai.shangou.seashop.user.security.LoginService;
import com.sankuai.shangou.seashop.user.service.WxMiniService;
import com.sankuai.shangou.seashop.user.thrift.auth.LoginUserFeign;
import com.sankuai.shangou.seashop.user.thrift.auth.request.LoginSmsReq;
import com.sankuai.shangou.seashop.user.thrift.auth.request.RefreshTokenReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: cdd
 * @date: 2024/5/15/015
 * @description:登录相关接口
 */
@RestController
@RequestMapping("/login")
public class LoginController implements LoginUserFeign {

    @Autowired
    private LoginService loginService;

    @Resource
    private PassportUtils passportUtils;

    @Resource
    private WxMiniService wxMiniService;

    /**
     * 统一登录接口
     * @param loginReq
     * @return 登录令牌
     */
    @PostMapping(value = "/doLogin", consumes = "application/json")
    public ResultDto<LoginResp> login(@RequestBody LoginReq loginReq) {
        return ThriftResponseHelper.responseInvoke("login", loginReq, req -> {
            loginReq.checkParameter();
//            passportUtils.checkPasswordLegal(req.getPassword(), req.getUserName());
            return loginService.login(req);
        });
    }


    /**
     * 发送登录短信
     * @return 短信发送结果
     */
    @PostMapping(value = "/sendLoginSms", consumes = "application/json")
    public ResultDto<BaseResp> sendLoginSms(@RequestBody LoginSmsReq loginSms) {
        return ThriftResponseHelper.responseInvoke("sendLoginSms", loginSms, req -> {
            loginSms.checkParameter();
            return loginService.sendSms(req);
        });
    }

    /**
     * 小程序动态令牌换取手机号
     * @param code
     * @return
     */
    @GetMapping(value = "/getWxUserPhone")
    public ResultDto<String> getWxUserPhone(@RequestParam(value = "code", required = false) String code) {
        return ThriftResponseHelper.responseInvoke("getWxUserPhone", code, req -> {
            if (StrUtil.isBlank(code)) {
                throw new BusinessException("动态令牌不能为空");
            }
            return wxMiniService.getPhoneByCode(code);
        });
    }

    /**
     * 退出，根据登录平台类型退出
     * 需要携带token退出
     * @return
     */
    @PostMapping(value = "/logout", consumes = "application/json")
    public ResultDto<BaseResp> logout(@RequestBody TokenCache tokenCache) {
        return ThriftResponseHelper.responseInvoke("logout", tokenCache, req -> {
            loginService.logout(req);
            return BaseResp.of();
        });
    }

    //刷新token
    @PostMapping(value = "/refreshToken", consumes = "application/json")
    @Override
    public ResultDto<LoginResp> refreshToken(@RequestBody RefreshTokenReq refreshTokenReq) {
        return ThriftResponseHelper.responseInvoke("refreshToken", refreshTokenReq, req -> {
            return loginService.refreshToken(req);
        });
    }
}
