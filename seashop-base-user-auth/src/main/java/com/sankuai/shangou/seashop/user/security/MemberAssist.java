package com.sankuai.shangou.seashop.user.security;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.user.common.config.EncryptConfig;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2024/11/27 14:45
 */
@Component
public class MemberAssist {

    @Resource
    private EncryptConfig encryptConfig;

    public void decryptPhone(Member member) {
        if (member == null || StrUtil.isEmpty(member.getCellPhone())) {
            return;
        }
        member.setCellPhone(AesUtil.decrypt(member.getCellPhone(), encryptConfig.getAesSecret()));
    }
}
