package com.sankuai.shangou.seashop.user.service.impl;

import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
import com.sankuai.shangou.seashop.base.utils.OkHttpUtil;
import com.sankuai.shangou.seashop.user.service.WxOAuth2AccessTokenResult;
import com.sankuai.shangou.seashop.user.service.WxOAuth2Service;
import com.sankuai.shangou.seashop.user.service.WxOAuth2UserInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信OAuth2服务实现类
 * 用于处理微信网页授权相关接口调用
 * 
 * @author: 系统生成
 * @date: 2025/01/02
 * @description: 微信OAuth2 access_token获取服务
 */
@Service
@Slf4j
public class WxOAuth2ServiceImpl implements WxOAuth2Service {

    /**
     * 微信OAuth2获取access_token的API地址
     */
    private static final String WX_OAUTH2_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token";
    
    /**
     * 微信OAuth2刷新access_token的API地址
     */
    private static final String WX_OAUTH2_REFRESH_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/refresh_token";
    
    /**
     * 微信OAuth2获取用户信息的API地址
     */
    private static final String WX_OAUTH2_USERINFO_URL = "https://api.weixin.qq.com/sns/userinfo";

    /**
     * 微信公众号AppID
     */
    //@Value("${wx.mp.appId:}")
    private String appId;

    /**
     * 微信公众号AppSecret
     */
    //@Value("${wx.mp.appSecret:}")
    private String appSecret;

    /**
     * HTTP客户端
     */
    private final OkHttpClient httpClient;

    public WxOAuth2ServiceImpl() {
        this.httpClient = OkHttpUtil.getInstance();
    }

    @Resource
    private SiteSettingService siteSettingService;

    private void getWxMpInfo() {
        siteSettingService.query(Arrays.asList("weixinMpAppId", "weixinMpAppSecret")).forEach(setting -> {
            if ("weixinMpAppId".equals(setting.getKey())) {
                appId = setting.getValue();
            }
            if ("weixinMpAppSecret".equals(setting.getKey())) {
                appSecret = setting.getValue();
            }
        });
        if(appId == null || appSecret == null) {
            throw new LoginException("微信公众号AppID或AppSecret未配置");
        }
    }

    @Override
    public WxOAuth2AccessTokenResult getAccessToken(String code) {
        if (!StringUtils.hasText(code)) {
            throw new LoginException("授权码code不能为空");
        }
        // 从数据库里面取
        this.getWxMpInfo();

        // 构建请求URL
        String url = buildAccessTokenUrl(code);
        
        try {
            // 发送HTTP请求
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("微信OAuth2获取access_token失败，HTTP状态码: {}", response.code());
                    throw new LoginException("微信授权失败，请重试");
                }

                String responseBody = response.body().string();
                log.info("微信OAuth2获取access_token响应: {}", responseBody);

                // 解析响应结果
                WxOAuth2AccessTokenResult result = JsonUtil.parseObject(responseBody, WxOAuth2AccessTokenResult.class);
                
                // 检查是否有错误
                if (result.getErrcode() != null && result.getErrcode() != 0) {
                    log.error("微信OAuth2获取access_token失败，错误码: {}, 错误信息: {}", 
                            result.getErrcode(), result.getErrmsg());
                    throw new LoginException("微信授权失败: " + result.getErrmsg());
                }

                return result;
            }
        } catch (IOException e) {
            log.error("微信OAuth2获取access_token网络请求失败，code={}, error={}", code, e.getMessage(), e);
            throw new LoginException("网络请求失败，请检查网络连接");
        } catch (Exception e) {
            log.error("微信OAuth2获取access_token失败，code={}, error={}", code, e.getMessage(), e);
            throw new LoginException("微信授权失败: " + e.getMessage());
        }
    }

    @Override
    public WxOAuth2AccessTokenResult refreshAccessToken(String refreshToken) {
        if (!StringUtils.hasText(refreshToken)) {
            throw new LoginException("刷新令牌refresh_token不能为空");
        }
        // 从数据库里面取
        this.getWxMpInfo();
        // 构建请求URL
        String url = buildRefreshTokenUrl(refreshToken);
        
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("微信OAuth2刷新access_token失败，HTTP状态码: {}", response.code());
                    throw new LoginException("刷新微信授权失败，请重新授权");
                }

                String responseBody = response.body().string();
                log.info("微信OAuth2刷新access_token响应: {}", responseBody);

                WxOAuth2AccessTokenResult result = JsonUtil.parseObject(responseBody, WxOAuth2AccessTokenResult.class);
                
                if (result.getErrcode() != null && result.getErrcode() != 0) {
                    log.error("微信OAuth2刷新access_token失败，错误码: {}, 错误信息: {}", 
                            result.getErrcode(), result.getErrmsg());
                    throw new LoginException("刷新微信授权失败: " + result.getErrmsg());
                }

                return result;
            }
        } catch (IOException e) {
            log.error("微信OAuth2刷新access_token网络请求失败，refreshToken={}, error={}", refreshToken, e.getMessage(), e);
            throw new LoginException("网络请求失败，请检查网络连接");
        } catch (Exception e) {
            log.error("微信OAuth2刷新access_token失败，refreshToken={}, error={}", refreshToken, e.getMessage(), e);
            throw new LoginException("刷新微信授权失败: " + e.getMessage());
        }
    }

    @Override
    public WxOAuth2UserInfo getUserInfo(String accessToken, String openId) {
        if (!StringUtils.hasText(accessToken) || !StringUtils.hasText(openId)) {
            throw new LoginException("access_token和openid不能为空");
        }
        // 从数据库里面取
        this.getWxMpInfo();
        // 构建请求URL
        String url = buildUserInfoUrl(accessToken, openId);
        
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("微信OAuth2获取用户信息失败，HTTP状态码: {}", response.code());
                    throw new LoginException("获取用户信息失败，请重试");
                }

                String responseBody = response.body().string();
                log.info("微信OAuth2获取用户信息响应: {}", responseBody);

                WxOAuth2UserInfo result = JsonUtil.parseObject(responseBody, WxOAuth2UserInfo.class);
                
                if (result.getErrcode() != null && result.getErrcode() != 0) {
                    log.error("微信OAuth2获取用户信息失败，错误码: {}, 错误信息: {}", 
                            result.getErrcode(), result.getErrmsg());
                    throw new LoginException("获取用户信息失败: " + result.getErrmsg());
                }

                return result;
            }
        } catch (IOException e) {
            log.error("微信OAuth2获取用户信息网络请求失败，accessToken={}, openId={}, error={}", 
                    accessToken, openId, e.getMessage(), e);
            throw new LoginException("网络请求失败，请检查网络连接");
        } catch (Exception e) {
            log.error("微信OAuth2获取用户信息失败，accessToken={}, openId={}, error={}", 
                    accessToken, openId, e.getMessage(), e);
            throw new LoginException("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 构建获取access_token的URL
     */
    private String buildAccessTokenUrl(String code) {
        Map<String, String> params = new HashMap<>();
        params.put("appid", appId);
        params.put("secret", appSecret);
        params.put("code", code);
        params.put("grant_type", "authorization_code");
        
        return buildUrl(WX_OAUTH2_ACCESS_TOKEN_URL, params);
    }

    /**
     * 构建刷新access_token的URL
     */
    private String buildRefreshTokenUrl(String refreshToken) {
        Map<String, String> params = new HashMap<>();
        params.put("appid", appId);
        params.put("grant_type", "refresh_token");
        params.put("refresh_token", refreshToken);
        
        return buildUrl(WX_OAUTH2_REFRESH_TOKEN_URL, params);
    }

    /**
     * 构建获取用户信息的URL
     */
    private String buildUserInfoUrl(String accessToken, String openId) {
        Map<String, String> params = new HashMap<>();
        params.put("access_token", accessToken);
        params.put("openid", openId);
        params.put("lang", "zh_CN");
        
        return buildUrl(WX_OAUTH2_USERINFO_URL, params);
    }

    /**
     * 构建带参数的URL
     */
    private String buildUrl(String baseUrl, Map<String, String> params) {
        StringBuilder url = new StringBuilder(baseUrl);
        url.append("?");
        
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!first) {
                url.append("&");
            }
            url.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }
        
        return url.toString();
    }
}
