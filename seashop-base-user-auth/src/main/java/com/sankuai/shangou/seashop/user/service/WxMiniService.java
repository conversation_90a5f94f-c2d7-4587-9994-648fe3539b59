package com.sankuai.shangou.seashop.user.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;

/**
 * @author: cdd
 * @date: 2024/5/15/015
 * @description:
 */
public interface WxMiniService {

    /**
     * 通过动态令牌获取手机号
     * @param code
     * @return
     */
    String getPhoneByCode(String code);

    /**
     * 授权码获取微信用户信息
     * @param code
     * @return
     */
    WxMaJscode2SessionResult getUserInfoByCode(String code);
}
