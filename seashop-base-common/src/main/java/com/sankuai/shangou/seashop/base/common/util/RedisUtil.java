package com.sankuai.shangou.seashop.base.common.util;

import cn.hutool.extra.spring.SpringUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.common.constant.CommonConstants;
import com.sankuai.shangou.seashop.base.common.util.function.VoidSupplier;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;


@Slf4j
public class RedisUtil {

    private static final RedisTemplate<String, Object> redisTemplate = SpringUtil.getBean("redisTemplate", RedisTemplate.class);
    public static final StringRedisTemplate STRING_REDIS_TEMPLATE = SpringUtil.getBean("stringRedisTemplate", StringRedisTemplate.class);
    private static final RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);

    /**
     * 默认获取锁的最大等待时间(秒)
     */
    private static final int DEFAULT_WAIT_TIME_SECONDS = 5;

    /**
     * 自动释放的时间(秒)
     */
    private static final int DEFAULT_LEASE_TIME_SECONDS = 30;

    /**
     * 默认缓存过期时间
     */
    private static final long DEFAULT_EXPIRE = CommonConstants.DEFAULT_CACHE_TIME;

    /**
     * 默认缓存过期时间单位
     */
    private static final TimeUnit DEFAULT_TIME_UNIT = TimeUnit.SECONDS;

    /**
     * 默认获取锁失败时的提示信息
     */
    private static final String DEFAULT_LOCK_MESSAGE = "服务器繁忙，请稍后再试";

    //=============================common============================

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public static boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("设置redis指定key失效时间错误:", e);
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效 失效时间为负数，说明该主键未设置失效时间（失效时间默认为-1）
     */
    public static Long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false 不存在
     */
    public static Boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("redis判断key是否存在错误：", e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public static void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(Arrays.asList(key));
            }
        }
    }

    //============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key) {
        return key == null ? null : (T) redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("设置redis缓存错误：", e);
            return false;
        }

    }

    /**
     * 普通缓存放入并设置默认的过期时间
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean setDefaultTime(String key, Object value) {
        return set(key, value, DEFAULT_EXPIRE, DEFAULT_TIME_UNIT);
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public static boolean set(String key, Object value, long time) {
        return set(key, value, time, TimeUnit.SECONDS);
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key      键
     * @param value    值
     * @param time     时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @param timeUnit 时间单位
     * @return true成功 false 失败
     */
    public static boolean set(String key, Object value, long time, TimeUnit timeUnit) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, timeUnit);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 递增 此时value值必须为int类型 否则报错
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return
     */
    public static Long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return STRING_REDIS_TEMPLATE.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return
     */
    public static Long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return STRING_REDIS_TEMPLATE.opsForValue().increment(key, -delta);
    }

    /**
     * 缓存
     *
     * @param key      缓存key
     * @param time     缓存时间
     * @param timeUnit 缓存时间单位
     * @param supplier 缓存数据
     * @param <T>      缓存数据类型
     * @return 缓存数据
     */
    public static <T> T cache(String key, long time, TimeUnit timeUnit, Supplier<T> supplier) {
        time = time > 0 ? time : DEFAULT_EXPIRE;
        timeUnit = timeUnit != null ? timeUnit : DEFAULT_TIME_UNIT;
        T value = get(key);
        if (value != null) {
            return value;
        }
        value = supplier.get();
        set(key, value, time, timeUnit);
        return value;
    }

    /**
     * 缓存
     *
     * @param key      缓存key
     * @param supplier 缓存数据
     * @param <T>      缓存数据类型
     * @return 缓存数据
     */
    public static <T> T cache(String key, Supplier<T> supplier) {
        return cache(key, DEFAULT_EXPIRE, DEFAULT_TIME_UNIT, supplier);
    }

    /**
     * 获取锁
     *
     * @param lockKey  锁对应的键
     * @param supplier 获取锁后执行的操作
     */
    public static void lock(String lockKey, VoidSupplier supplier) {
        lock(lockKey, DEFAULT_WAIT_TIME_SECONDS, supplier, DEFAULT_LEASE_TIME_SECONDS, null);
    }

    /**
     * 获取锁
     *
     * @param lockKey  锁对应的键
     * @param supplier 获取锁后执行的操作
     * @param message  获取锁失败时的提示信息
     */
    public static void lock(String lockKey, VoidSupplier supplier, String message) {
        lock(lockKey, DEFAULT_WAIT_TIME_SECONDS, supplier, DEFAULT_LEASE_TIME_SECONDS, message);
    }

    /**
     * 获取锁
     *
     * @param lockKey            锁对应的键
     * @param waitTimeoutSeconds 获取锁等待超时时间(秒)
     * @param supplier           获取锁后执行的操作
     */
    public static void lock(String lockKey, int waitTimeoutSeconds, VoidSupplier supplier) {
        lock(lockKey, waitTimeoutSeconds, supplier, DEFAULT_LEASE_TIME_SECONDS, null);
    }

    /**
     * 获取锁
     *
     * @param lockKey            锁对应的键
     * @param waitTimeoutSeconds 获取锁等待超时时间(秒)
     * @param supplier           获取锁后执行的操作
     * @param message            获取锁失败时的提示信息
     */
    public static void lock(String lockKey, int waitTimeoutSeconds, VoidSupplier supplier, String message) {
        lock(lockKey, waitTimeoutSeconds, supplier, DEFAULT_LEASE_TIME_SECONDS, message);
    }

    /**
     * 获取锁
     *
     * @param lockKey            锁对应的键
     * @param waitTimeoutSeconds 获取锁等待超时时间(秒)
     * @param supplier           获取锁后执行的操作
     * @param leaseTimeSeconds   自动释放锁的时间(秒)
     */
    public static void lock(String lockKey, int waitTimeoutSeconds, VoidSupplier supplier, int leaseTimeSeconds, String message) {
        String actualLockKey = getActualLockKey(lockKey);
        RLock lock = redissonClient.getLock(actualLockKey);

        int actualWaitTimeSeconds = waitTimeoutSeconds > 0 ? waitTimeoutSeconds : DEFAULT_WAIT_TIME_SECONDS;
        int actualLeaseTimeSeconds = leaseTimeSeconds > 0 ? leaseTimeSeconds : DEFAULT_LEASE_TIME_SECONDS;
        try {
            //按指定最大等待时间获取锁，防止一直等待而占用资源
            if (lock.tryLock(actualWaitTimeSeconds, actualLeaseTimeSeconds, TimeUnit.SECONDS)) {
                supplier.apply();
            } else {
                log.warn("线程[{}]获取锁[{}]超时(已等待{}秒)", Thread.currentThread().getName(), lockKey, actualWaitTimeSeconds);
                throw new BusinessException(StringUtils.isNotBlank(message) ? message : DEFAULT_LOCK_MESSAGE);
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                //最后需要及时释放锁
                lock.unlock();
            }
        }
    }

    /**
     * 获取锁
     *
     * @param lockKey          锁对应的键
     * @param supplier         获取锁后执行的操作
     * @param leaseTimeSeconds 自动释放锁的时间(秒)
     */
    public static void lock(String lockKey, VoidSupplier supplier, int leaseTimeSeconds) {
        lock(lockKey, DEFAULT_WAIT_TIME_SECONDS, supplier, leaseTimeSeconds, null);
    }

    /**
     * 获取锁
     *
     * @param lockKey          锁对应的键
     * @param supplier         获取锁后执行的操作
     * @param leaseTimeSeconds 自动释放锁的时间(秒)
     * @param message          获取锁失败时的提示信息
     */
    public static void lock(String lockKey, VoidSupplier supplier, int leaseTimeSeconds, String message) {
        lock(lockKey, DEFAULT_WAIT_TIME_SECONDS, supplier, leaseTimeSeconds, message);
    }

    /**
     * 根据指定的锁的键值获取实际键值
     *
     * @param lockKey 锁的键值
     * @return 返回用于存储锁的实际键
     */
    private static String getActualLockKey(String lockKey) {
        return String.format("HI_SHOP_LOCK:%s", lockKey);
    }
}