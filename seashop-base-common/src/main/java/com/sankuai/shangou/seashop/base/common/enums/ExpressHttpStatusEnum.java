package com.sankuai.shangou.seashop.base.common.enums;


public enum ExpressHttpStatusEnum {
    OK(200, "请求成功");
    Integer code;
    String desc;

    ExpressHttpStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ExpressHttpStatusEnum of(Integer code) {
        if (code == null) {
            return null;
        }
        for (ExpressHttpStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
