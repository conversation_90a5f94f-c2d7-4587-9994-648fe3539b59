package com.sankuai.shangou.seashop.base.common.enums.messageRecord;

import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * @description: 消息发送状态枚举类 消息类型 0微信 1邮件 2优惠券 3短信
 * @date: 2020/12/30 下午3:59
 **/
@TypeDoc(description = "消息发送状态枚举类")
public enum MessageTypeEnum {
    WECHAT(0, "微信"),
    EMAIL(1, "邮件"),
    COUPON(2, "优惠券"),
    SMS(3, "短信"),
    ;

    private Integer code;
    private String desc;

    MessageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (MessageTypeEnum value : MessageTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
