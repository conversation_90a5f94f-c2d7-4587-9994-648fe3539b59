package com.sankuai.shangou.seashop.base.common.enums;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
public enum BaseResultCodeEnum {

    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * </pre>
     */

    // 限时购轮播图最大数量不能超过6个
    LIMIT_TIME_BUY_MAX_COUNT(40020001, "限时购轮播图最大数量不能超过%s个"),
    // 限时购轮播图数据不存在
    LIMIT_TIME_BUY_DATA_NOT_EXIST(40020002, "限时购轮播图数据不存在"),

    ;

    private Integer code;
    private String msg;

    BaseResultCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
