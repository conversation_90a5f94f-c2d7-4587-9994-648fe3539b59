package com.sankuai.shangou.seashop.base.common.util.template;


import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.common.enums.BaseTemplateClientTypeEnum;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TemplateStorageService {
    public String getTemplatePath(String client, BaseTemplateClientTypeEnum type, long shopId) {
        String result = "";
        switch (type) {
            case WapIndex:
                result = "Areas/Admin/Templates/vshop/" + client + "/";
                break;
            case WapSpecial:
            case SellerWapSpecial:
            case AppSpecial:
                if (!StringUtils.isEmpty(client) || client.equals("0")) {
                    client = "empty";
                }
                result = "Special/" + client + "/";
                break;
            case WXSmallProgramSpecial:
            case SellerWxSmallProgramSpecial:
                result = "AppletSpecial/" + client + "/";
                break;
            case SellerWapIndex:
                result = "Areas/SellerAdmin/Templates/vshop/" + shopId + "/" + client + "/";
                break;
            case PCTOPIC:
            case PCTOPIC_SELLER:
                result = "Web/" + shopId + "/" + client + "/";
                break;
            case PCIndex_SELLER:
                result = "Web/Index" + shopId + "/";
                break;
            case WXSmallProgramSellerWapIndex:
                result = "Areas/SellerAdmin/Templates/smallprogvshop/" + shopId + "/" + client + "/";
                break;
            case WXSmallProgram:
                result = "AppletHome/";
                break;
            case AppIndex:
                result = "AppHome/";
                break;
            case PCIndex:
                result = "PCHome/";
                break;
            case Header:
                result = "Header/";
                break;
            case HEADER_SELLER:
                result = "Header/seller/" + shopId + "/";
                break;
            case Footer:
                result = "Footer/";
                break;
            case FOOTER_SELLER:
                result = "Footer/seller/" + shopId + "/";
                break;
        }
        return result;
    }
}
