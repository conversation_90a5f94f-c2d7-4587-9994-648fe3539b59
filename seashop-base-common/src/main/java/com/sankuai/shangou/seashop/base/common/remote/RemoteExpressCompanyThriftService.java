package com.sankuai.shangou.seashop.base.common.remote;

import java.util.ArrayList;
import java.util.List;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.dianping.sc.express.dto.ExpressCompanyDTO;
import com.dianping.sc.express.dto.ExpressMainDTO;
import com.dianping.sc.express.dto.LoadSubscribedExpressRequest;
import com.dianping.sc.express.dto.SubmitExpressSubscribeRequest;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RemoteExpressCompanyThriftService {

    @Value("${express.bizType:1}")
    private Integer bizType;

//    @Resource
//TODO    private LogisticsClient logisticsClient;

//    @ThriftClientProxy(remoteAppKey = "com.sankuai.thh.express.server", filterByServiceName = true)
//    private ExpressCompanyThriftService.Iface expressCompanyThriftService;


//    @Reference(remoteAppKey = "sc-express-service" ,timeout = 3000)
//    private ExpressSubscribeService expressSubscribeService;


//    @Reference(remoteAppKey = "sc-express-service" ,timeout = 3000)
//    private ExpressQueryService expressQueryService;

    /**
     * 查询所有支持的公司
     *
     * @return
     * @throws TException
     */
    @Deprecated
    public List<ExpressCompanyDTO> loadBizSupportCompany() throws TException {
//        BizSupportExpressCompanyRequest request = new BizSupportExpressCompanyRequest();
//        request.setBizType(bizType);
//        BizSupportExpressCompanyResult result = expressCompanyThriftService.loadBizSupportCompany(request);
//
//        if (result.getCode() != 0) {
//            throw new SystemException("获取快递公司接口失败:" + result.getMsg());
//        }
//        return result.expressCompanyList;
        return new ArrayList<>();
    }

    /**
     * 提交订阅快递单号
     *
     * @param request
     * @return
     */
    @Deprecated
    public Long submitExpressSubscribe(SubmitExpressSubscribeRequest request) {
//        request.setBizType(bizType);
//        Response<Long> result = expressSubscribeService.submitExpressSubscribe(request);
//        if (result.getCode() != 200) {
//            throw new SystemException("订阅快递单号失败:" + result.getMsg());
//        }
//        return result.getData();
        return null;
    }

    /**
     * 查询物流轨迹
     *
     * @param request
     * @return
     */
    public ExpressMainDTO loadSubscribedExpressByCompanyCodeAndExpressNo(LoadSubscribedExpressRequest request) {
//        request.setBizType(bizType);
//        ExpressMainDTO expressMainDTO = expressQueryService.loadSubscribedExpressByCompanyCodeAndExpressNo(request);
//        return expressMainDTO;
//        logisticsClient.query();
        return null;
    }

}
