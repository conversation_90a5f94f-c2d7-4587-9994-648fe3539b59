package com.sankuai.shangou.seashop.base.common.enums;



/**
 * @author： liweisong
 * @create： 2023/12/11 10:53
 */
public class WxTemplatesEnum {

    public enum TemplateNumEnum {
        // @TODO 跳转页面现在是写的假的，需要修改
        TKTZ(30805, "用户-退款通知", "pages/returndetail/returndetail?orderid={id}"),
        DDFHTZ(30766, "用户-订单发货通知", "pages/orderdetails/orderdetails?orderid={id}")
        ;

        private final Integer code;
        private final String desc;
        private final String pageUrl;

        TemplateNumEnum(Integer code, String desc, String pageUrl) {
            this.code = code;
            this.desc = desc;
            this.pageUrl = pageUrl;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public String getPageUrl() {
            return pageUrl;
        }

        public static String getPageUrlByCode(Integer code) {
            for (TemplateNumEnum templateNumEnum : TemplateNumEnum.values()) {
                if (templateNumEnum.getCode().equals(code)) {
                    return templateNumEnum.getPageUrl();
                }
            }
            return null;
        }
    }

    public enum MessageTypeEnum{
        FIRST(0, "初始"),
        TKSHHTZSJ(6, "退款审核后通知商家"),
        KDPSDDFHHTZ(23, "快递配送订单发货后通知"),
        ZTDDFKCGHTZ(14, "自提订单付款成功后通知"),
        ;

        private final Integer code;
        private final String desc;

        MessageTypeEnum(Integer code, String desc) {

            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
