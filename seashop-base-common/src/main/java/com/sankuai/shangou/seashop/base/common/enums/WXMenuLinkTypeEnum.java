package com.sankuai.shangou.seashop.base.common.enums;


/**
 * 公众号菜单链接类型
 */
public enum WXMenuLinkTypeEnum {
    None(0, "无链接"),
    WXH5(1, "微商城"),
    Applet(2, "小程序"),
    ;

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    WXMenuLinkTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WXMenuLinkTypeEnum getByCode(Integer code) {
        for (WXMenuLinkTypeEnum value : WXMenuLinkTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
