package com.sankuai.shangou.seashop.base.common.enums.messageRecord;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * @description: 消息内容类型枚举类 内容类型 0图文消息 1文本 2语音 3图片 4视频 5卡券
 * @date: 2020/12/30 下午3:59
 **/
@TypeDoc(description = "消息内容类型枚举类")
public enum MessageContentTypeEnum {
    GRAPHIC_MESSAGE(0, "图文消息"),
    TEXT(1, "文本"),
    VOICE(2, "语音"),
    IMAGE(3, "图片"),
    VIDEO(4, "视频"),
    COUPON(5, "卡券");

    private Integer code;
    private String desc;

    MessageContentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (MessageContentTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
