package com.sankuai.shangou.seashop.base.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = HimallConfigProperties.PREFIX)
@Getter
@Setter
public class HimallConfigProperties {

    public static final String PREFIX = "himall";
    /**
     * 短信配置
     */
    private final SmsProperties sms = new SmsProperties();

    @Getter
    @Setter
    public static class SmsProperties {
        /**
         * 短信模板配置 key:模板ID
         */
        private Map<String, SmsTemplate> templates = new HashMap<>();

    }

}
