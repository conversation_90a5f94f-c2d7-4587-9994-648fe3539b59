package com.sankuai.shangou.seashop.base.common.enums.messageRecord;

/**
 * @description: 消息发送状态枚举类 消息状态 0发送失败 1发送成功
 * @date: 2020/12/30 下午3:59
 **/
public enum SendMessageStateEnum {
    SEND_FAIL(0, "发送失败"),
    SEND_SUCCESS(1, "发送成功");

    private Integer code;
    private String desc;

    SendMessageStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (SendMessageStateEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
    public Integer getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }
}
