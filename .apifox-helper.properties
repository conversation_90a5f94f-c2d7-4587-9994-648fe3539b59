
#configs
#dev=false
#auto.format.url=true
#max.deep=10
#max.elements=512

## ignore field 'log' typed xxx.xxx.Log
#json.rule.field.ignore=erpShopId,loginDto
## json.rule.field.ignore=loginDto
field.ignore=groovy:it.name()=="erpShopId"

#module=#module

folder.name=groovy:```
  def className = it.containingClass().name()

  def lastServiceClassName = tool.substringAfterLast(className,".")

 logger.info("package:"+tool.substringAfterLast(className,"thrift.impl.")+" className:"+className) 

  def baseServiceFolder = tool.substringAfterLast(className,"thrift.impl.").replaceFirst("\\."+lastServiceClassName,"")
  baseServiceFolder = tool.substringAfterLast(baseServiceFolder,".")

  def serviceFolder = baseServiceFolder + "/"+ lastServiceClassName.replaceFirst("QueryThriftServiceImpl","").replaceFirst("CMDThriftServiceImpl","").replaceFirst\
  ("CmdThriftServiceImpl","").replaceFirst("ThriftServiceImpl","")
  logger.info("serviceFolder:"+serviceFolder)
  if(className.contains("seashop.mall.")){
    return "mallApi/"+serviceFolder
  }else if(className.contains("seashop.seller.")){
        return "sellerApi/"+serviceFolder
  }else if(className.contains("seashop.m.")){
     return "mApi/"+serviceFolder
  }

  return serviceFolder
```

method.description=@com.meituan.servicecatalog.api.annotations.MethodDoc#description


param.required=@javax.validation.constraints.NotBlank
field.required=@javax.validation.constraints.NotBlank
param.required=@javax.validation.constraints.NotNull
field.required=@javax.validation.constraints.NotNull
param.required=@javax.validation.constraints.NotEmpty
field.required=@javax.validation.constraints.NotEmpty

param.type[@org.springframework.web.bind.annotation.RequestParam]=query
param.type=form

field.required=groovy:it.ann("com.meituan.servicecatalog.api.annotations.FieldDoc","requiredness")=="Requiredness.REQUIRED"

field.schema.permit.null=@javax.validation.constraints.NotNull
field.schema.permit.null=@javax.validation.constraints.NotBlank
field.schema.permit.null=@javax.validation.constraints.NotEmpty

field.schema.permit.null=groovy:it.ann("com.meituan.servicecatalog.api.annotations.FieldDoc","requiredness")=="Requiredness.REQUIRED"


field.name=@com.fasterxml.jackson.annotation.JsonProperty#value
field.name=@com.fasterxml.jackson.annotation.JsonAlias#value

field.schema.title=groovy:```
def description = it.ann("com.meituan.servicecatalog.api.annotations.FieldDoc","description")
if(description){
    logger.info("description:"+description)
  return description
}
return it.doc()
```

#method.return.body.type[#return]=json
#method.return.body.type[@com.meituan.servicecatalog.api.annotations.MethodDoc]=application/json
method.return.body.type=application/json

#method.return.main[groovy:it.returnType().isExtend("com.sankuai.shangou.seashop.erp.thrift.channel.dto.mt.ErpMtResultDto")]=data
#method.return.main[groovy:it.returnType().isExtend("com.sankuai.shangou.seashop.erp.thrift.channel.dto.yjp.ErpYjpResultDto")]=data

class.prefix.path=/himall-gw