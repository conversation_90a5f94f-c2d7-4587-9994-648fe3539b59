package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.SessionCmdReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiTreeBankRegionResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.HiChatSessionListResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.HiChatSessionResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiCustomerServiceResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.SessionsResp;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.AppSitSettingRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseSitSettingRes;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallCustomerServiceRemoteService;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallMemberRemoteService;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallShopRemoteService;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.shop.CustomerServiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopOpenApiSettingResp;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: LXH
 **/
@RestController
@RequestMapping("/mallApi/apiMessage")
@Slf4j
public class MallApiMessageQueryController {
    @Resource
    private CustomerServiceQueryFeign customerServiceQueryFeign;
    @Resource
    private MallCustomerServiceRemoteService mallCustomerServiceRemoteService;
    @Resource
    private MallShopRemoteService mallShopRemoteService;
    @Resource
    private MallMemberRemoteService mallMemberRemoteService;
    @Value("${venus.hostName:''}")
    private String venusHostName;
    @Resource
    private SiteSettingQueryFeign siteSettingQueryFeign;

    @PostMapping(value = "/getSessions", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<HiChatSessionListResp> getSessions(@RequestBody BasePageReq basePageReq) {
        AppSitSettingRes appSitSettingRes = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getAppSettings());
        BaseSitSettingRes baseSitSettingRes = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getSetting());
        String url = appSitSettingRes.getChatDomain() + "/clientApi/OAuth/domainSessions";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("domain", baseSitSettingRes.getSiteUrl());
        paramMap.put("pager", basePageReq.getPageNo());
        paramMap.put("size", basePageReq.getPageSize());
        ShopOpenApiSettingResp settingResp = mallCustomerServiceRemoteService.getSelfShopApi();
        
        if (TracerUtil.getMemberDto() == null) {
            return ResultDto.newWithData(new HiChatSessionListResp());
        }
        paramMap.put("openId", TracerUtil.getMemberDto().getId());
        /*String token = mallCustomerServiceRemoteService.getToken(appSitSettingRes.getChatDomain(), settingResp.getAppKey(),
            settingResp.getAppSecret(), TracerUtil.getMemberDto().getId().toString());
        log.info("获取会话列表token:{},入参：{}", token, JsonUtil.toJsonString(paramMap));
        HttpResponse response = HttpUtil.createGet(url).form(paramMap).header("Authorization", "Bearer " + token).execute();*/
        HttpResponse response = HttpUtil.createGet(url).form(paramMap).execute();
        if (Objects.equals(response.getStatus(), 200)) {
            String data = response.body();
            log.info("获取会话列表成功:{}", data);
            data = JsonUtil.getAsString(data, "data");
            List<HiChatSessionResp> sessions = JsonUtil.parseArray(data, HiChatSessionResp.class);
            return ResultDto.newWithData(new HiChatSessionListResp(sessions));
        }
        else {
            throw new BusinessException("获取会话列表失败");
        }
    }


    @PostMapping(value = "/getPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiCustomerServiceResp>> getPage(@RequestBody BaseIdReq baseIdReq) {
        return ThriftResponseHelper.responseInvoke("getPage", baseIdReq, req -> {
            if (req == null || req.getId() == null) {
                req = new BaseIdReq();
                req.setId(mallShopRemoteService.querySelfShopInfo().getId());
            }
            BasePageResp<CustomerServiceResp> serviceRespBasePageReq = mallCustomerServiceRemoteService.queryPage(req);
            return PageResultHelper.transfer(serviceRespBasePageReq, ApiCustomerServiceResp.class);
        });
    }

    @PostMapping(value = "/getSession", consumes = "application/json")
    @NeedLogin
    public ResultDto<SessionsResp> getSession(@RequestBody SessionCmdReq sessionCmdReq) {
        return ThriftResponseHelper.responseInvoke("getPage", sessionCmdReq, req -> {
            AppSitSettingRes appSitSettingRes = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getAppSettings());
            ShopOpenApiSettingResp apiSettingResp = ThriftResponseHelper.executeThriftCall(() ->
                    customerServiceQueryFeign.queryOpenApiSetting(req.getAppKey()));
            MemberResp memberResp = mallMemberRemoteService.getMemberById(TracerUtil.getMemberDto().getId());
            String sessionData = mallCustomerServiceRemoteService.getSessionToken(appSitSettingRes.getChatDomain(), memberResp.getNick(), venusHostName + memberResp.getPhoto(), apiSettingResp.getAppKey(),
                    apiSettingResp.getAppSecret(), TracerUtil.getMemberDto().getId().toString());
            String sessionToken = JSONUtil.getByPath(JSONUtil.parse(sessionData), "data.token").toString();
            String avatar = JSONUtil.getByPath(JSONUtil.parse(sessionData), "data.avatar").toString();
            SessionsResp sessionsResp = new SessionsResp();
            if (StrUtil.isNotEmpty(memberResp.getPhoto())) {
                sessionsResp.setMemberAvatar(venusHostName + memberResp.getPhoto());
            }
            sessionsResp.setAvatar(avatar);
            sessionsResp.setDomain(appSitSettingRes.getChatDomain());
            sessionsResp.setToken(sessionToken);
            sessionsResp.setUri("/clientApi/chat/messages");
            return sessionsResp;
        });
    }

    @GetMapping(value = "/getBankRegion")
    public ResultDto<String> getBankRegion() {
        return ThriftResponseHelper.responseInvoke("getBankRegion", null, req -> mallShopRemoteService.getBankRegion());
    }

    @GetMapping(value = "/getRegionByParentId")
    public ResultDto<List<ApiTreeBankRegionResp>> getRegionByParentId(@RequestParam Long id) {
        return ThriftResponseHelper.responseInvoke("getRegionByParentId", id, req -> JsonUtil.copyList(mallShopRemoteService.getRegionByParentId(new BaseIdReq() {{setId(req);}}), ApiTreeBankRegionResp.class));
    }

    @PostMapping(value = "/getRegionsById", consumes = "application/json")
    public ResultDto<List<ApiTreeBankRegionResp>> getRegionsById(@RequestBody BaseIdReq regionId) {
        return ThriftResponseHelper.responseInvoke("getRegionsById", regionId, req -> JsonUtil.copyList(mallShopRemoteService.getRegionsById(req), ApiTreeBankRegionResp.class));
    }
}
