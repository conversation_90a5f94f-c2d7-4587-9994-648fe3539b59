package com.sankuai.shangou.seashop.m.core.service.base.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.WXMenuCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.WXMenuQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWXMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveWXAccountReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.WXAccountResp;
import com.sankuai.shangou.seashop.m.core.service.base.MWXMenuService;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseWXMenuReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiSaveWXAccountReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBaseWXMenuListRes;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBaseWXMenuRes;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiWXAccountResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdCreateQRReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdCreateQRReq;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Service
public class MWXMenuServiceImpl implements MWXMenuService {
    @Resource
    private WXMenuQueryFeign wxMenuQueryFeign;
    @Resource
    private WXMenuCMDFeign wxMenuCMDFeign;

    @Override
    public List<ApiBaseWXMenuListRes> queryWXMenus() {
        List<BaseWXMenuListRes> list = ThriftResponseHelper.executeThriftCall(() -> wxMenuQueryFeign.queryWXMenus());
        return JsonUtil.copyList(list, ApiBaseWXMenuListRes.class);
    }

    @Override
    public ApiBaseWXMenuRes queryById(BaseIdReq idReq) {
        BaseWXMenuRes baseWXMenuRes = ThriftResponseHelper.executeThriftCall(() -> wxMenuQueryFeign.queryWXMenuById(idReq));
        return JsonUtil.copy(baseWXMenuRes, ApiBaseWXMenuRes.class);
    }

    @Override
    public void saveWXAccount(ApiSaveWXAccountReq req) {
        SaveWXAccountReq saveWXAccountReq = JsonUtil.copy(req, SaveWXAccountReq.class);
        ThriftResponseHelper.executeThriftCall(() -> wxMenuCMDFeign.saveWXAccount(saveWXAccountReq));
    }

    @Override
    public Integer create(ApiBaseWXMenuReq req) {
        BaseWXMenuReq baseWXMenuReq = JsonUtil.copy(req, BaseWXMenuReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> wxMenuCMDFeign.create(baseWXMenuReq));
    }

    @Override
    public Boolean update(ApiBaseWXMenuReq wxMenuReq) {
        BaseWXMenuReq baseWXMenuReq = JsonUtil.copy(wxMenuReq, BaseWXMenuReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> wxMenuCMDFeign.update(baseWXMenuReq));
    }

    @Override
    public Boolean delete(BaseIdReq baseIdReq) {
        return ThriftResponseHelper.executeThriftCall(() -> wxMenuCMDFeign.delete(baseIdReq));
    }

    @Override
    public Boolean syncWXMenu() {
        return ThriftResponseHelper.executeThriftCall(() -> wxMenuCMDFeign.syncWXMenu());
    }

    @Override
    public ApiWXAccountResp getWXAccount() {
        WXAccountResp waxAccountResp = ThriftResponseHelper.executeThriftCall(() -> wxMenuCMDFeign.getWXAccount());
        return JsonUtil.copy(waxAccountResp, ApiWXAccountResp.class);
    }

    @Override
    public String createQrCode(ApiCmdCreateQRReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> wxMenuQueryFeign.createQR(JsonUtil.copy(req, CmdCreateQRReq.class)));
    }



}
