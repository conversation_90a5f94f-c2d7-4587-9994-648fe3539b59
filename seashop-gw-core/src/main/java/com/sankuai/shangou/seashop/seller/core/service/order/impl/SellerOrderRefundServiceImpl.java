package com.sankuai.shangou.seashop.seller.core.service.order.impl;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundQueryFeign;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerConfirmReceiveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundLogListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundUserDeliverExpressResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDto;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.order.SellerOrderRefundRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.seller.core.service.order.SellerOrderRefundService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SellerOrderRefundServiceImpl implements SellerOrderRefundService {

    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;
    @Resource
    private SellerOrderRefundRemoteService sellerOrderRefundRemoteService;
    @Resource
    private OrderRefundQueryFeign orderRefundQueryFeign;
    @Resource
    private OrderRefundCmdFeign orderRefundCmdFeign;

    @Override
    public void export(SellerQueryRefundReq queryReq) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        if (queryReq.getTab().getCode() == 1) {
            createExportTaskBo.setTaskType(ExportTaskType.REFUND_ALL_LIST);
        }
        else if (queryReq.getTab().getCode() == 2) {
            createExportTaskBo.setTaskType(ExportTaskType.REFUND_WAIT_PROCESS_LIST);
        }
        else if (queryReq.getTab().getCode() == 3) {
            createExportTaskBo.setTaskType(ExportTaskType.RETURN_ALL_LIST);
        }
        else if (queryReq.getTab().getCode() == 4) {
            createExportTaskBo.setTaskType(ExportTaskType.RETURN_WAIT_PROCESS_LIST);
        }
        else if (queryReq.getTab().getCode() == 5) {
            createExportTaskBo.setTaskType(ExportTaskType.REFUND_BUYER_CANCEL_LIST);
        }
        else if (queryReq.getTab().getCode() == 6) {
            createExportTaskBo.setTaskType(ExportTaskType.RETURN_BUYER_CANCEL_LIST);
        }
        createExportTaskBo.setExecuteParam(queryReq);
        createExportTaskBo.setOperatorId(queryReq.getUser().getUserId());
        createExportTaskBo.setOperatorName(queryReq.getUser().getUserName());
        sellerExportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public SellerRefundDetailResp queryDetail(SellerQueryRefundDetailReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.sellerQueryDetail(queryReq));
    }

    @Override
    public RefundLogListResp queryRefundLog(BaseIdReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryRefundLog(queryReq));
    }

    @Override
    public BasePageResp<SellerRefundDto> sellerQueryRefundPage(SellerQueryRefundReq queryReq) {
        return sellerOrderRefundRemoteService.sellerQueryRefundPage(queryReq);
    }

    @Override
    public RefundUserDeliverExpressResp queryUserDeliverExpress(BaseIdReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryUserDeliverExpress(queryReq));
    }

    @Override
    public BaseResp sellerApprove(SellerApproveReq sellerApproveReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.sellerApprove(sellerApproveReq));
    }

    @Override
    public BaseResp sellerConfirmReceive(SellerConfirmReceiveReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.sellerConfirmReceive(req));
    }
}
