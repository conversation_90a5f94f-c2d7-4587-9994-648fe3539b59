package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductPageQryReq;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerExclusivePriceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.promotion.ApiExclusivePriceProductDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiExclusivePriceProductPageQryReq;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiExclusivePriceProduct")
public class SellerApiExclusivePriceProductQueryController {

    @Resource
    private SellerExclusivePriceRemoteService sellerExclusivePriceRemoteService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiExclusivePriceProductDto>> pageList(@RequestBody ApiExclusivePriceProductPageQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            ExclusivePriceProductPageQryReq exclusivePriceProductPageQryReq = JsonUtil.copy(req, ExclusivePriceProductPageQryReq.class);
            BasePageResp<ExclusivePriceProductDto> basePageResp = sellerExclusivePriceRemoteService.productPageList(exclusivePriceProductPageQryReq);
            return PageResultHelper.transfer(basePageResp, ApiExclusivePriceProductDto.class);
        });
    }
}
