package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.ShopCategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.QueryShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory.ShopCategoryTreeResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryShopCategoryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiShopCategoryTreeResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:12
 */
@RestController
@RequestMapping("/sellerApi/apiShopCategory")
public class SellerApiShopCategoryQueryController {

    @Resource
    private ShopCategoryQueryFeign shopCategoryQueryFeign;

    @PostMapping(value = "/queryShopCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiShopCategoryTreeResp> queryShopCategory(@RequestBody ApiQueryShopCategoryReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryShopCategory", request, req -> {

            QueryShopCategoryReq param = JsonUtil.copy(req, QueryShopCategoryReq.class);
            param.setShopId(TracerUtil.getShopDto().getShopId());
            ShopCategoryTreeResp treeResp = ThriftResponseHelper.executeThriftCall(() -> shopCategoryQueryFeign.queryShopCategory(param));
            return JsonUtil.copy(treeResp, ApiShopCategoryTreeResp.class);
        });
    }
}
