package com.sankuai.shangou.seashop.mall.core.thrift.impl.pay;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCallBackNotifyPayReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCallBackReq;
import com.sankuai.shangou.seashop.pay.thrift.core.service.PayCallBackFeign;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 商城API支付回调控制器
 * <p>
 * 处理各种支付渠道的回调通知，包括支付回调和退款回调
 * 作为网关层接口，负责接收外部支付平台的回调请求并转发给相应的服务处理
 * </p>
 *
 * <AUTHOR> Lee
 * @see MallApiPayCallBackController
 * @since 2025/6/23 13:57
 */
@Tag(name = "MallApiPayCallBackController", description = "商城API支付回调相关接口")
@RestController
@RequestMapping("/mallApi/apiPayCallBack")
@Slf4j
public class MallApiPayCallBackController {

    @Resource
    private PayCallBackFeign payCallBackFeign;

    @Resource
    private HttpServletRequest httpServletRequest;

    /**
     * 汇付支付回调接口（未验证）
     * <p>
     * 接收汇付支付平台的支付结果回调通知，验证签名并处理支付状态更新
     * </p>
     *
     * @param requestH 汇付支付回调请求参数，包含支付数据和签名信息
     * @return ResultDto&lt;BaseResp&gt; 处理结果，成功时返回success标识
     * @throws TException Thrift服务调用异常
     */
    @Operation(summary = "汇付支付回调接口", description = "接收汇付支付平台的支付结果回调通知，验证签名并处理支付状态更新")
    @PostMapping(value = "/adaPayCallback")
    public ResultDto<BaseResp> adaPayCallback(HttpServletRequest requestH) throws TException {
        log.info("【汇付支付回调接口】开始");
        String data = requestH.getParameter("data");
        String sign = requestH.getParameter("sign");
        String eventType = requestH.getParameter("type");
        String createdTime = requestH.getParameter("created_time");
        String prodMode = requestH.getParameter("prod_mode");
        log.info("【汇付支付回调接口】:data={},sign={},eventType={},createdTime={},prodMode={}", data, sign, eventType, createdTime, prodMode);
        PayCallBackReq request = new PayCallBackReq();
        request.setData(data);
        request.setSign(sign);
        request.setType(eventType);
        return ThriftResponseHelper.responseInvoke("【支付回调】汇付回调接口", request, req -> {
            return ThriftResponseHelper.executeThriftCall(() -> payCallBackFeign.adaPayCallback(req));
        });
    }

    /**
     * 通用支付回调接口
     * <p>
     * 根据支付渠道处理不同平台的支付回调通知
     * 支持多种支付渠道如微信支付、支付宝等
     * </p>
     *
     * @param channel 支付渠道标识，如"wxpay"、"alipay"等 {@link PaymentChannelEnum#getName()}
     * @param notifyData 支付平台回调的原始数据
     * @return Object 返回给支付平台的响应数据，格式由具体支付渠道决定
     */
    @Operation(summary = "通用支付回调接口", description = "根据支付渠道处理不同平台的支付回调通知，支持多种支付渠道如微信支付、支付宝等")
    @PostMapping("/notifyPay/{channel}")
    public Object notifyPay(@PathVariable("channel") String channel, @RequestBody String notifyData) {
        log.info("【支付回调】支付通知回调 channel:{}, notifyData:{}", channel, notifyData);
        PayCallBackNotifyPayReq notifyPayReq = getPayCallBackNotifyPayReq(notifyData);
        log.info("【支付回调】支付通知回调 channel:{}, notifyPayReq:{}", channel, JsonUtil.toJsonString(notifyPayReq));
        return payCallBackFeign.notifyPay(channel, notifyPayReq);
    }

    /**
     * 支付宝支付回调接口（未验证）
     * <p>
     * 专门处理支付宝支付平台的回调通知
     * 从HttpServletRequest中解析支付宝回调参数
     * </p>
     *
     * @param request HTTP请求对象，包含支付宝回调的表单参数
     * @return Object 返回给支付宝的响应数据
     */
    @Operation(summary = "支付宝支付回调接口", description = "专门处理支付宝支付平台的回调通知，从HttpServletRequest中解析支付宝回调参数")
    @PostMapping("/aliPayCallback")
    public Object aliPayCallback(HttpServletRequest request) {
        log.info("【支付回调】支付宝支付回调");
        return payCallBackFeign.aliPayCallback(request);
    }

    /**
     * 通用退款回调接口
     * <p>
     * 根据支付渠道处理不同平台的退款回调通知
     * 更新订单退款状态和相关业务数据
     * </p>
     *
     * @param channel 支付渠道标识，如"wxpay"、"alipay"等 {@link PaymentChannelEnum#getName()}
     * @param notifyData 退款平台回调的原始数据
     * @return Object 返回给支付平台的响应数据，格式由具体支付渠道决定
     */
    @Operation(summary = "通用退款回调接口", description = "根据支付渠道处理不同平台的退款回调通知，更新订单退款状态和相关业务数据")
    @PostMapping("/notifyRefund/{channel}")
    public Object notifyRefund(@PathVariable("channel") String channel, @RequestBody String notifyData) {
        log.info("【退款回调】退款通知回调 channel:{}, notifyData:{}", channel, notifyData);
        PayCallBackNotifyPayReq notifyPayReq = getPayCallBackNotifyPayReq(notifyData);
        log.info("【支付回调】支付通知回调 channel:{}, notifyPayReq:{}", channel, JsonUtil.toJsonString(notifyPayReq));
        return payCallBackFeign.notifyRefund(channel, notifyPayReq);
    }

    /**
     * 支付宝退款回调接口
     * <p>
     * 专门处理支付宝退款平台的回调通知
     * 从HttpServletRequest中解析支付宝退款回调参数
     * </p>
     *
     * @param request HTTP请求对象，包含支付宝退款回调的表单参数
     * @return Object 返回给支付宝的响应数据
     */
    @Operation(summary = "支付宝退款回调接口", description = "专门处理支付宝退款平台的回调通知，从HttpServletRequest中解析支付宝退款回调参数")
    @PostMapping("/aliNotifyRefund")
    public Object aliNotifyRefund(HttpServletRequest request) {
        log.info("【退款回调】支付宝退款回调");
        return payCallBackFeign.aliNotifyRefund(request);
    }

    /**
     * 获取支付回调请求头
     *
     * @param notifyData 支付平台回调的原始数据
     * @return PayCallBackNotifyPayReq 支付回调请求参数
     */
    private PayCallBackNotifyPayReq getPayCallBackNotifyPayReq(String notifyData) {
        Map<String, Object> notifyPaySignatureHeader = getNotifyPaySignatureHeader();
        PayCallBackNotifyPayReq notifyPayReq = PayCallBackNotifyPayReq.builder()
                .notifyData(notifyData)
                .notifySignHeaders(notifyPaySignatureHeader)
                .build();
        return notifyPayReq;
    }

    /**
     * 提取回调请求头
     *
     * @return 回调请求头
     */
    public Map<String, Object> getNotifyPaySignatureHeader() {
        Map<String, Object> header = new LinkedHashMap<>();
        header.put("wechatpay-timestamp", httpServletRequest.getHeader("wechatpay-timestamp"));
        header.put("wechatpay-nonce", httpServletRequest.getHeader("wechatpay-nonce"));
        header.put("wechatpay-signature", httpServletRequest.getHeader("wechatpay-signature"));
        header.put("wechatpay-serial", httpServletRequest.getHeader("wechatpay-serial"));
        return header;
    }
}
