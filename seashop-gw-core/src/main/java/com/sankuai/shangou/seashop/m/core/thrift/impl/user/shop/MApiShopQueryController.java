package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiShopService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryShopPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryShopReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiShopQueryPagerReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiShopQueryReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.*;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiShop")
public class MApiShopQueryController {
    @Resource
    MApiShopService mApiShopService;

    @PostMapping(value = "/getShopList", consumes = "application/json")
    public ResultDto<ApiShopRespList> getShopList(@RequestBody ApiQueryShopPageReq queryShopPageReq) {
        return null;
    }

    @PostMapping(value = "/getShopIds", consumes = "application/json")
    public ResultDto<ApiShopIdsResp> getShopIds(@RequestBody ApiQueryShopReq queryShopReq) {
        return ThriftResponseHelper.responseInvoke("getList", queryShopReq, req -> mApiShopService.getShopIds(queryShopReq));
    }

    @PostMapping(value = "/queryPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiShopResp>> queryPage(@RequestBody ApiShopQueryPagerReq request) {
        return ThriftResponseHelper.responseInvoke("selectList", request,
            req -> mApiShopService.queryPage(request)
        );
    }

    @PostMapping(value = "/queryShopCategoryDetail", consumes = "application/json")
    public ResultDto<String> queryShopCategoryDetail(@RequestBody BaseIdReq shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopCategoryDetail", shopId, v -> {
            return mApiShopService.queryShopCategoryDetail(shopId);
        });
    }

    @PostMapping(value = "/queryDetail", consumes = "application/json")
    public ResultDto<ApiShopDetailResp> queryDetail(@RequestBody BaseIdReq shopId) {
        return ThriftResponseHelper.responseInvoke("queryDetail", shopId,
            req -> mApiShopService.queryDetail(shopId)
        );
    }

    @GetMapping(value = "/countShopUser")
    public ResultDto<ApiShopUserCountResp> countShopUser() throws TException {
        return ThriftResponseHelper.responseInvoke("countShopUser", null,
            req -> mApiShopService.countShopUser()
        );
    }

    @GetMapping(value = "/queryShopsByIds")
    public ResultDto<List<ApiShopResp>> queryShopsByIds(ApiShopQueryReq apiShopQueryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("countShopUser", apiShopQueryReq,
                req -> mApiShopService.queryShopsByIds(req)
        );
    }

    @PostMapping(value = "/querySimplePage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiShopSimpleResp>> querySimplePage(@RequestBody ApiShopQueryPagerReq queryPagerReq) throws TException {
        queryPagerReq.setWhetherFrontSearch(false);
        return ThriftResponseHelper.responseInvoke("querySimplePage", queryPagerReq,
            req -> mApiShopService.querySimplePage(queryPagerReq)
        );
    }
}
