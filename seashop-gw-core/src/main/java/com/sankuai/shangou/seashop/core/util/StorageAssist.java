package com.sankuai.shangou.seashop.core.util;

import java.net.URI;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.hishop.starter.storage.client.StorageClient;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2024/11/28 11:03
 */
@Component
public class StorageAssist {

    @Resource
    private StorageClient storageClient;

    public String formatUrl(String url) {
        // 如果resp为空或者已经有了域名，则直接返回
        if (StrUtil.isEmpty(url) || StrUtil.isNotEmpty(URI.create(url).getHost())) {
            return url;
        }

        return storageClient.formatUrl(url);
    }

}
