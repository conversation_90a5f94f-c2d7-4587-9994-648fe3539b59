package com.sankuai.shangou.seashop.m.core.service.product.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.common.remote.product.MProductRemoteService;
import com.sankuai.shangou.seashop.m.common.remote.promotion.MFlashSaleRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.product.MProductService;
import com.sankuai.shangou.seashop.m.thrift.core.enums.PromotionTypeEnum;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryProductReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryPromotionReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleSimpleResp;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2024/01/02 17:47
 */
@Service
public class MProductServiceImpl implements MProductService {

    @Resource
    private MExportTaskBiz exportTaskBiz;
    @Resource
    private MFlashSaleRemoteService MFlashSaleRemoteService;
    @Resource
    private MProductRemoteService MProductRemoteService;

    @Override
    public void exportProduct(ApiQueryProductReq request) {
        QueryProductReq remoteReq = JsonUtil.copy(request, QueryProductReq.class);
        remoteReq.setStatus(ProductStatusEnum.getByCode(request.getStatusCode()));
        // 如果状态为空，则查询在售、下架、违规的商品
        if (request.getStatusCode() == null || request.getStatusCode() == 0) {
            List<ProductStatusEnum> inStatus = Lists.newArrayList();
            inStatus.add(ProductStatusEnum.ON_SALE);
            inStatus.add(ProductStatusEnum.IN_STOCK);
            inStatus.add(ProductStatusEnum.VIOLATION);
            remoteReq.setInStatus(inStatus);
        }


        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.PRODUCT_PAGE_LIST);
        createExportTaskBo.setExecuteParam(remoteReq);
        createExportTaskBo.setOperatorId(TracerUtil.getManagerDto().getId());
        createExportTaskBo.setOperatorName(TracerUtil.getManagerDto().getName());
        exportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public BasePageResp<ApiProductPageResp> queryProduct(ApiQueryProductPromotionExtReq request) {

        BasePageResp<ApiProductPageResp> resultPage = null;

        ApiQueryPromotionReq promotion = request.getPromotion();
        if (null != promotion) {
            // 如果有营销查询条件，则进行营销查询处理
            PromotionTypeEnum promotionType = PromotionTypeEnum.getByCode(promotion.getPromotionType());
            switch (promotionType) {
                case COUPON:
                    break;
                case FULL_REDUCTION:
                    break;
                case DISCOUNT:
                    break;
                case EXCLUSIVE_PRICE:
                    break;
                case LIMITED_TIME_PURCHASE:
                    List<FlashSaleSimpleResp> flashSaleList = new ArrayList<>();
                    // 限时购处理
                    FlashSaleQueryReq flashSaleQueryReq = new FlashSaleQueryReq();
                    flashSaleQueryReq.setShopId(request.getShopId());
                    flashSaleQueryReq.setStatus(promotion.getPromotionStatus());
                    flashSaleQueryReq.setPageNo(CommonConstant.QUERY_START);
                    flashSaleQueryReq.setPageSize(CommonConstant.QUERY_LIMIT);
                    BasePageResp<FlashSaleSimpleResp> flashSalePage = MFlashSaleRemoteService.pageList(flashSaleQueryReq);
                    if (null != flashSalePage && CollUtil.isNotEmpty(flashSalePage.getData())) {
                        flashSaleList.addAll(flashSalePage.getData());
                        if (flashSalePage.getPages() > CommonConstant.INIT_PAGE_NO) {
                            Integer pages = flashSalePage.getPages();
                            for (int i = CommonConstant.LOOP_START_PAGE_NO; i <= pages; i++) {
                                flashSaleQueryReq.setPageNo(i);
                                BasePageResp<FlashSaleSimpleResp> flashSalePageFor = MFlashSaleRemoteService.pageList(flashSaleQueryReq);
                                if (null != flashSalePageFor && CollUtil.isNotEmpty(flashSalePageFor.getData())) {
                                    flashSaleList.addAll(flashSalePageFor.getData());
                                }
                            }
                        }
                    }
                    if (CollUtil.isEmpty(flashSaleList)) {
                        // 说明没有限时购活动
                        return PageResultHelper.defaultEmpty(request);
                    }
                    Set<Long> productIdSet = flashSaleList.stream().map(FlashSaleSimpleResp::getProductId).collect(Collectors.toSet());
                    QueryProductReq remoteReq = JsonUtil.copy(request, QueryProductReq.class);
                    remoteReq.setProductIds(new ArrayList<>(productIdSet));

                    BasePageResp<ProductPageResp> respPage = MProductRemoteService.queryProduct(remoteReq);
                    resultPage = PageResultHelper.transfer(respPage, ApiProductPageResp.class, resp -> {
                    });
                    break;
                case COMBINATION_PURCHASE:
                    break;
                default:
                    break;
            }
        }
        else {
            QueryProductReq remoteReq = JsonUtil.copy(request, QueryProductReq.class);
            if (null == remoteReq.getStatus()) {
                // 如果没传状态，默认查询销售中的商品
                remoteReq.setStatus(ProductStatusEnum.ON_SALE);
            }
            BasePageResp<ProductPageResp> resp = MProductRemoteService.queryProduct(remoteReq);
            resultPage = PageResultHelper.transfer(resp, ApiProductPageResp.class);
        }
        return resultPage;
    }
}
