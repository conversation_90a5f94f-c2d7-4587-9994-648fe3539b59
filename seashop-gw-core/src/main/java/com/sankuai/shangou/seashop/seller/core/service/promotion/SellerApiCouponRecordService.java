package com.sankuai.shangou.seashop.seller.core.service.promotion;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiCouponRecordQueryReq;

/**
 * @author: lhx
 * @date: 2024/2/23/023
 * @description:
 */
public interface SellerApiCouponRecordService {

    /**
     * 导出优惠券记录
     *
     * @param request
     */
    void exportCouponRecord(ApiCouponRecordQueryReq request, LoginShopDto shopInfo);
}
