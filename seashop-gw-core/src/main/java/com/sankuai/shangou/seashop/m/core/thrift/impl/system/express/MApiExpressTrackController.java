package com.sankuai.shangou.seashop.m.core.thrift.impl.system.express;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressTrackQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.LoadSubscribedExpressReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressTrackRes;
import com.sankuai.shangou.seashop.m.thrift.core.request.express.ExpressQueryReq;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/mApi/apiExpressTrack")
public class MApiExpressTrackController {

    @Value("${express.bizType:47}")
    private Integer bizType;
    @Resource
    private ExpressTrackQueryFeign expressTrackQueryFeign;

    @PostMapping(value = "/loadSubscribedExpressByHiShopCompanyCodeAndExpressNo", consumes = "application/json")
    public ResultDto<ExpressTrackRes> loadSubscribedExpressByHiShopCompanyCodeAndExpressNo(@RequestBody ExpressQueryReq request) {
        return ThriftResponseHelper.responseInvoke("loadSubscribedExpressByHiShopCompanyCodeAndExpressNo", request, req -> {
            LoadSubscribedExpressReq query = new LoadSubscribedExpressReq();
            query.setBizType(bizType);
            query.setExpressNo(req.getExpressNo());
            query.setCompanyCode(req.getCompanyCode());
            query.setReceiveMobile(req.getReceiveMobile());
            ExpressTrackRes result = ThriftResponseHelper.executeThriftCall(() -> expressTrackQueryFeign.loadSubscribedExpressByHiShopCompanyCodeAndExpressNo(query));
            return result;
        });
    }


}
