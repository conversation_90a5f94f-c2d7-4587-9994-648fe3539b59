package com.sankuai.shangou.seashop.m.core.thrift.impl.system.article;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ArticleCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.ArticleQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleCategoryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ArticleCategoryListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseArticleCategoryRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseArticleRes;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseArticleCategoryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseArticleQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseArticleReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseIdsReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiArticleCategoryListRes;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBaseArticleCategoryRes;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBaseArticleRes;

@RestController
@RequestMapping("/mApi/apiArticle")
public class MApiArticleController {
    @Resource
    private ArticleQueryFeign articleQueryFeign;

    @Resource
    private ArticleCMDFeign articleCMDFeign;

    @PostMapping(value = "/create", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> create(@RequestBody ApiBaseArticleReq articleReq) throws TException {
        return ThriftResponseHelper.responseInvoke("SynMTRegionCode", articleReq, req -> {
            req.checkParameter();
            BaseReq baseReq = new BaseReq();
            baseReq.setId(articleReq.getCategoryId());
            BaseArticleCategoryRes articleCategoryRes = ThriftResponseHelper.executeThriftCall(() ->
                articleQueryFeign.getBaseArticleCategoryById(baseReq));

            if (articleCategoryRes == null) {
                throw new IllegalArgumentException("所属分类已被删除");
            }
            BaseArticleReq bean = JsonUtil.copy(req, BaseArticleReq.class);
            Long result = ThriftResponseHelper.executeThriftCall(() ->
                articleCMDFeign.create(bean));
            return result;
        });
    }

    @PostMapping(value = "/update", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> update(@RequestBody ApiBaseArticleReq articleReq) throws TException {
        return ThriftResponseHelper.responseInvoke("SynMTRegionCode", articleReq, req -> {
            req.checkParameter();
            BaseReq baseReq = new BaseReq();
            baseReq.setId(articleReq.getCategoryId());
            BaseArticleCategoryRes articleCategoryRes = ThriftResponseHelper.executeThriftCall(() ->
                articleQueryFeign.getBaseArticleCategoryById(baseReq));

            if (articleCategoryRes == null) {
                throw new IllegalArgumentException("所属分类已被删除");
            }
            BaseArticleReq bean = JsonUtil.copy(req, BaseArticleReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                articleCMDFeign.update(bean));
            return result;
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> delete(@RequestBody ApiBaseIdsReq idsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("SynMTRegionCode", idsReq, req -> {
            req.checkParameter();
            BaseIdsReq bean = JsonUtil.copy(req, BaseIdsReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                articleCMDFeign.delete(bean));
            return result;
        });
    }

    @PostMapping(value = "/queryWithPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBaseArticleRes>> queryWithPage(@RequestBody ApiBaseArticleQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWithPage", query, req -> {
            req.checkParameter();
            BaseArticleQueryReq bean = JsonUtil.copy(req, BaseArticleQueryReq.class);
            BasePageResp<BaseArticleRes> result = ThriftResponseHelper.executeThriftCall(() ->
                articleQueryFeign.queryWithPage(bean));
            return PageResultHelper.transfer(result, ApiBaseArticleRes.class);
        });
    }

    @GetMapping(value = "/queryAllCategory")
    public ResultDto<ApiArticleCategoryListRes> queryAllCategory() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("queryWithPage", i, req -> {
            ArticleCategoryListRes result = ThriftResponseHelper.executeThriftCall(() ->
                articleQueryFeign.queryAllCategory());
            return JsonUtil.copy(result, ApiArticleCategoryListRes.class);
        });
    }

    @PostMapping(value = "/getArticleById", consumes = "application/json")
    public ResultDto<ApiBaseArticleRes> getArticleById(@RequestBody BaseReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWithPage", query, req -> {
            BaseArticleRes result = ThriftResponseHelper.executeThriftCall(() ->
                articleQueryFeign.getArticleById(req));
            return JsonUtil.copy(result, ApiBaseArticleRes.class);
        });
    }

    @PostMapping(value = "/getBaseArticleCategoryById", consumes = "application/json")
    public ResultDto<ApiBaseArticleCategoryRes> getBaseArticleCategoryById(@RequestBody BaseReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWithPage", query, req -> {
            BaseArticleCategoryRes result = ThriftResponseHelper.executeThriftCall(() ->
                articleQueryFeign.getBaseArticleCategoryById(req));
            return JsonUtil.copy(result, ApiBaseArticleCategoryRes.class);
        });
    }

    @PostMapping(value = "/createCategory", consumes = "application/json")
    public ResultDto<Long> createCategory(@RequestBody ApiBaseArticleCategoryReq categoryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createCategory", categoryReq, req -> {
            req.checkParameter();
            if (categoryReq.getDisplaySequence() == null) {
                categoryReq.setDisplaySequence(1L);
            }
            BaseArticleCategoryReq bean = JsonUtil.copy(req, BaseArticleCategoryReq.class);
            Long result = ThriftResponseHelper.executeThriftCall(() ->
                articleCMDFeign.createCategory(bean));
            return result;
        });
    }

    @PostMapping(value = "/updateCategory", consumes = "application/json")
    public ResultDto<Boolean> updateCategory(@RequestBody ApiBaseArticleCategoryReq categoryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategory", categoryReq, req -> {
            req.checkParameter();
            BaseArticleCategoryReq bean = JsonUtil.copy(req, BaseArticleCategoryReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                articleCMDFeign.updateCategory(bean));
            return result;
        });
    }

    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    public ResultDto<Boolean> deleteCategory(@RequestBody ApiBaseIdsReq idsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategory", idsReq, req -> {
            req.checkParameter();
            BaseIdsReq bean = JsonUtil.copy(req, BaseIdsReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                articleCMDFeign.deleteCategory(bean));
            return result;
        });
    }
}
