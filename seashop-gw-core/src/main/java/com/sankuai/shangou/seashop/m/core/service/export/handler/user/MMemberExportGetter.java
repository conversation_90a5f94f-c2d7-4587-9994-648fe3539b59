package com.sankuai.shangou.seashop.m.core.service.export.handler.user;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.common.remote.user.MApiMemberRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.user.eo.MemberEo;
import com.sankuai.shangou.seashop.m.core.service.export.handler.user.wrapper.MemberDataWrapper;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryMemberPageReq;

/**
 * <AUTHOR>
 */
@Service
public class MMemberExportGetter extends AbstractBaseDataGetter<ApiQueryMemberPageReq>
    implements SingleWrapperDataGetter<ApiQueryMemberPageReq> {

    @Resource
    MApiMemberRemoteService MApiMemberRemoteService;

    @Override
    public DataContext selectData(ApiQueryMemberPageReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        // 构造数据包装器，每一个包装器对应一个sheet页，并且每个包装器可以单独定义自己的excel处理器，比如样式、合并单元格等
        BaseExportWrapper<MemberEo, ApiQueryMemberPageReq> memberDataWrapper = new MemberDataWrapper(MApiMemberRemoteService);
        context.setSheetDataList(Collections.singletonList(memberDataWrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.MEMBER_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.MEMBER_LIST.getName();
    }
}
