package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.product.thrift.core.BrandApplyQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.ProductAuditQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductByIdReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.QueryProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandApplyDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.GenProductCodeResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductSkuMergeQueryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.StatisticalProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditPageResp;
import com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.product.SellerProductService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductQueryDetailReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductSkuQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductByIdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiGenProductCodeResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductHasBindResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductPageResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductSkuMergeQueryResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiStatisticalProductResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiProductSkuMergeDto;

import cn.hutool.core.date.DateUtil;

/**
 * <AUTHOR>
 * @date 2023/12/12 17:13
 */
@RestController
@RequestMapping("/sellerApi/apiProduct")
public class SellerApiProductQueryController {

    @Resource
    private SellerProductRemoteService sellerProductRemoteService;
    @Resource
    private SellerProductService sellerProductService;
    @Resource
    private ProductAuditQueryFeign productAuditQueryFeign;
    @Resource
    private BrandApplyQueryFeign brandApplyQueryFeign;

    @PostMapping(value = "/queryProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiProductPageResp>> queryProduct(@RequestBody ApiQueryProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProduct", request, req -> {
            req.checkParameter();

            dealSearchTime(req);
            QueryProductReq remoteReq = JsonUtil.copy(req, QueryProductReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            ProductStatusEnum status = ProductStatusEnum.getByCode(req.getStatusCode());
            remoteReq.setStatus(status);
            // 默认查看销售中的数据
            if (status == null || status.equals(ProductStatusEnum.NONE)) {
                remoteReq.setStatus(ProductStatusEnum.ON_SALE);
            }
            BasePageResp<ProductPageResp> resp = sellerProductRemoteService.queryProduct(remoteReq);
            return PageResultHelper.transfer(resp, ApiProductPageResp.class);
        });
    }

    @PostMapping(value = "/queryProductById", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiProductListResp> queryProductById(@RequestBody ApiQueryProductByIdReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryProductById", request, req -> {

            QueryProductByIdReq remoteReq = new QueryProductByIdReq();
            remoteReq.setProductIds(req.getProductIds());
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            ProductListResp resp = sellerProductRemoteService.queryProductById(remoteReq);
            return ApiProductListResp.builder().productList(JsonUtil.copyList(resp.getProductList(), ApiProductPageResp.class)).build();
        });
    }

    @PostMapping(value = "/queryProductForSelect", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiProductPageResp>> queryProductForSelect(@RequestBody ApiQueryProductPromotionExtReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryProductForSelect", request, req -> {
            req.checkParameter();
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductService.queryProduct(req);
        });
    }

    @PostMapping(value = "/queryProductDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiProductDetailResp> queryProductDetail(@RequestBody ApiProductQueryDetailReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryProductDetail", request, req -> {
            ProductQueryDetailReq remoteReq = JsonUtil.copy(req, ProductQueryDetailReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.queryProductDetail(remoteReq);
        });
    }

    @PostMapping(value = "/queryProductSkuMerge", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiProductSkuMergeQueryResp> queryProductSkuMerge(@RequestBody ApiProductSkuQueryReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryProductSkuMerge", request, req -> {

            ProductSkuQueryReq remoteReq = JsonUtil.copy(req, ProductSkuQueryReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            ProductSkuMergeQueryResp resp = sellerProductRemoteService.queryProductSkuMerge(remoteReq);
            return ApiProductSkuMergeQueryResp.builder().productSkuList(JsonUtil.copyList(resp.getProductSkuList(), ApiProductSkuMergeDto.class)).build();
        });
    }

    @GetMapping(value = "/querySellerStatisticalProduct")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiStatisticalProductResp> querySellerStatisticalProduct() throws TException {
        Long shopId = TracerUtil.getShopDto().getShopId();
        return ThriftResponseHelper.responseInvoke("querySellerStatisticalProduct", shopId, req -> {
            StatisticalProductResp statisticalProductResp = sellerProductRemoteService.querySellerStatisticalProduct(req);
            ApiStatisticalProductResp result = JsonUtil.copy(statisticalProductResp, ApiStatisticalProductResp.class);
            // 仓库中
            QueryProductReq productsInStockReq = new QueryProductReq();
            productsInStockReq.setShopId(TracerUtil.getShopDto().getShopId());
            productsInStockReq.setStatus(ProductStatusEnum.IN_STOCK);
            BasePageResp<ProductPageResp> productsInStockResp = sellerProductRemoteService.queryProduct(productsInStockReq);
            result.setProductsInStock(productsInStockResp.getTotalCount().intValue());
            // 出售中
            QueryProductReq productsOnSaleReq = new QueryProductReq();
            productsOnSaleReq.setShopId(TracerUtil.getShopDto().getShopId());
            productsOnSaleReq.setStatus(ProductStatusEnum.ON_SALE);
            BasePageResp<ProductPageResp> productsOnSaleResp = sellerProductRemoteService.queryProduct(productsOnSaleReq);
            result.setProductsOnSale(productsOnSaleResp.getTotalCount().intValue());
            // 商品待审核
            QueryProductAuditReq productAuditReq = new QueryProductAuditReq();
            productAuditReq.setShopId(TracerUtil.getShopDto().getShopId());
            productAuditReq.setAuditStatus(ProductEnum.AuditStatusEnum.WAIT_AUDIT);
            BasePageResp<ProductAuditPageResp> productAuditResp = ThriftResponseHelper.executeThriftCall(() ->
                    productAuditQueryFeign.queryProductAudit(productAuditReq));
            result.setProductsWaitForAuditing(productAuditResp.getTotalCount().intValue());
            // 授权品牌
            QueryBrandApplyReq brandApplyReq = new QueryBrandApplyReq();
            brandApplyReq.setAuditStatus(BrandEnum.AuditStatusEnum.AUDITED);
            brandApplyReq.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<BrandApplyDto> brandApplyResp = ThriftResponseHelper.executeThriftCall(() ->
                    brandApplyQueryFeign.queryBrandApplyForPage(brandApplyReq));
            result.setProductsBrands(brandApplyResp.getTotalCount().intValue());
            // 违规下架
            QueryProductReq productsInfractionSaleOffReq = new QueryProductReq();
            productsInfractionSaleOffReq.setShopId(TracerUtil.getShopDto().getShopId());
            productsInfractionSaleOffReq.setStatus(ProductStatusEnum.VIOLATION);
            BasePageResp<ProductPageResp> productsInfractionSaleOffResp = sellerProductRemoteService.queryProduct(productsInfractionSaleOffReq);
            result.setProductsInfractionSaleOff(productsInfractionSaleOffResp.getTotalCount().intValue());
            // 库存报警
            QueryProductReq overSafeStockReq = new QueryProductReq();
            overSafeStockReq.setShopId(TracerUtil.getShopDto().getShopId());
            overSafeStockReq.setStatus(ProductStatusEnum.ON_SALE);
            overSafeStockReq.setWhetherBelowSafeStock(true);
            BasePageResp<ProductPageResp> overSafeStockResp = sellerProductRemoteService.queryProduct(overSafeStockReq);
            result.setOverSafeStockProducts(overSafeStockResp.getTotalCount().intValue());
            return result;
        });
    }

    @PostMapping(value = "/exportProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> exportProduct(@RequestBody ApiQueryProductReq request) throws TException {

        

        dealSearchTime(request);
        sellerProductService.exportProduct(request, TracerUtil.getShopDto());
        return ResultDto.newWithData(BaseResp.of());
    }

    @PostMapping(value = "/hasBind", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiProductHasBindResp> hasBind(@RequestBody ApiProductQueryDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("hasBind", request, req -> {
            Boolean hasBind = sellerProductService.hasBind(req.getProductId());

            ApiProductHasBindResp resp = new ApiProductHasBindResp();
            resp.setHasBind(hasBind);
            return resp;
        });
    }

    @GetMapping(value = "/generateProductNo")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiGenProductCodeResp> generateProductNo() throws TException {
        return ThriftResponseHelper.responseInvoke("generateProductNo", null, req -> {
            GenProductCodeResp genProductCodeResp = sellerProductRemoteService.generateProductCode();
            return JsonUtil.copy(genProductCodeResp, ApiGenProductCodeResp.class);
        });
    }

    private void dealSearchTime(ApiQueryProductReq req) {
        if (req.getStartTime() != null) {
            req.setStartTime(DateUtil.beginOfDay(req.getStartTime()));
        }
        if (req.getEndTime() != null) {
            req.setEndTime(DateUtil.endOfDay(req.getEndTime()));
        }
    }
}
