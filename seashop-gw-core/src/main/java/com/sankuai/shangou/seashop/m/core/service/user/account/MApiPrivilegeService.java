package com.sankuai.shangou.seashop.m.core.service.user.account;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryPrivilegeReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ManagerUserInfo;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiPrivilegeRespList;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiUserPrivilegeResp;

public interface MApiPrivilegeService {

    /**
     * 查询权限列表
     *
     * @param queryPrivilegePageReq 查询条件
     * @return PrivilegeRespList
     */
    ApiPrivilegeRespList queryPrivilegeList(ApiQueryPrivilegeReq queryPrivilegePageReq);

    ApiUserPrivilegeResp queryUserPrivilege(LoginManagerDto managerInfo);
}
