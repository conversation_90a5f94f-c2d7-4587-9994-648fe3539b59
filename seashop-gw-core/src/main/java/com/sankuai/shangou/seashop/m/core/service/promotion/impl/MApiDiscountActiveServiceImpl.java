package com.sankuai.shangou.seashop.m.core.service.promotion.impl;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.user.MShopRemoteService;
import com.sankuai.shangou.seashop.m.core.service.promotion.MApiDiscountActiveService;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiDiscountActiveQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiDiscountActiveSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.DiscountActiveQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.DiscountActiveQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@Service
@Slf4j
public class MApiDiscountActiveServiceImpl implements MApiDiscountActiveService {

    @Resource
    private MShopRemoteService mShopRemoteService;

    @Resource
    private DiscountActiveQueryFeign discountActiveQueryFeign;
    @Resource
    private ShopQueryFeign shopQueryFeign;

    @Override
    public BasePageResp<ApiDiscountActiveSimpleResp> pageList(ApiDiscountActiveQueryReq request) {

        DiscountActiveQueryReq bean = JsonUtil.copy(request, DiscountActiveQueryReq.class);

        String shopName = request.getShopName();
        List<ShopSimpleResp> shopSimpleRespList = new ArrayList<>();
        List<Long> shopIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(shopName)) {
            // 先模糊查询店铺
            ShopSimpleListResp shopSimpleListResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(
                ShopSimpleQueryReq.builder()
                    .shopName(shopName).build()
            ));
            // 模糊查询没查到数据，直接返回空
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request);
            }
            shopSimpleRespList = shopSimpleListResp.getList();
            shopIdList = shopSimpleRespList.stream().map(ShopSimpleResp::getId).collect(Collectors.toList());
        }
        bean.setShopIdList(shopIdList);

        BasePageResp<DiscountActiveSimpleResp> activeSimplePage = ThriftResponseHelper.executeThriftCall(() -> discountActiveQueryFeign.pageList(bean));
        if (null == activeSimplePage || CollUtil.isEmpty(activeSimplePage.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }

        List<DiscountActiveSimpleResp> activeSimpleRespList = activeSimplePage.getData();
        if (CollUtil.isEmpty(shopSimpleRespList)) {
            List<Long> shopIds = activeSimpleRespList.stream().map(DiscountActiveSimpleResp::getShopId).distinct().collect(Collectors.toList());
            ShopSimpleListResp shopSimpleListResp = mShopRemoteService.querySimpleList(
                ShopSimpleQueryReq.builder()
                    .shopIdList(shopIds).build()
            );
            if (null != shopSimpleListResp && CollUtil.isNotEmpty(shopSimpleListResp.getList())) {
                shopSimpleRespList = shopSimpleListResp.getList();
            }
        }

        final List<ShopSimpleResp> shopSimpleFinal = shopSimpleRespList;

        return PageResultHelper.transfer(activeSimplePage, ApiDiscountActiveSimpleResp.class, apiDiscountActiveSimpleResp -> {
            if (CollUtil.isNotEmpty(shopSimpleFinal)) {
                shopSimpleFinal.stream().filter(shopSimpleResp -> shopSimpleResp.getId().equals(apiDiscountActiveSimpleResp.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                    apiDiscountActiveSimpleResp.setShopName(shopSimpleResp.getShopName());
                });
            }
        });
    }
}
