package com.sankuai.shangou.seashop.mall.core.service.user.account;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiInvoiceTitleCmdReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiInvoiceTitleQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiInvoiceTitleResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiInvoiceTitleRespList;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.user.thrift.account.InvoiceTitleCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.InvoiceTitleQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.InvoiceTitleCmdReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.InvoiceTitleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.InvoiceTitleRespList;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 发票抬头服务类
 * @author: LXH
 **/
@Service
public class MallApiInvoiceTitleService {
    @Resource
    private InvoiceTitleQueryFeign invoiceTitleQueryFeign;
    @Resource
    private InvoiceTitleCmdFeign invoiceTitleCmdFeign;

    /**
     * 查询发票抬头列表
     *
     * @param queryReq 查询条件
     * @return 发票抬头列表
     */
    public ApiInvoiceTitleRespList queryList(ApiInvoiceTitleQueryReq queryReq) {
        InvoiceTitleQueryReq queryReq1 = JsonUtil.copy(queryReq, InvoiceTitleQueryReq.class);
        InvoiceTitleRespList respList = ThriftResponseHelper.executeThriftCall(() -> invoiceTitleQueryFeign.queryList(queryReq1));
        List<ApiInvoiceTitleResp> respLists = JsonUtil.copyList(respList.getRespList(), ApiInvoiceTitleResp.class);
        return new ApiInvoiceTitleRespList(respLists);
    }

    /**
     * 保存发票抬头
     *
     * @param cmdReq 保存条件
     * @return 保存结果
     */
    public BaseResp save(ApiInvoiceTitleCmdReq cmdReq) {
        //转化入参
        InvoiceTitleCmdReq cmdReq1 = JsonUtil.copy(cmdReq, InvoiceTitleCmdReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> invoiceTitleCmdFeign.save(cmdReq1));
    }

    /**
     * 删除发票抬头
     *
     * @param cmdReq 删除条件
     * @return 删除结果
     */
    public BaseResp delete(BaseIdReq cmdReq, LoginMemberDto userInfo) {
        InvoiceTitleCmdReq cmdReq1 = new InvoiceTitleCmdReq();
        cmdReq1.setId(cmdReq.getId());
        cmdReq1.setUserId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.executeThriftCall(() -> invoiceTitleCmdFeign.delete(cmdReq1));
    }
}
