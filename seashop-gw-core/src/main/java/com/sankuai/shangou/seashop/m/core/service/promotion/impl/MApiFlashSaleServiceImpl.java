package com.sankuai.shangou.seashop.m.core.service.promotion.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.promotion.MApiFlashSaleService;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiFlashSaleQueryReq;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/25/025
 * @description:
 */
@Service
@Slf4j
public class MApiFlashSaleServiceImpl implements MApiFlashSaleService {

    @Resource
    private MExportTaskBiz exportTaskBiz;

    @Override
    public void export(ApiFlashSaleQueryReq request) {
        log.info("限时购导出request：{}", request);

        LoginManagerDto manager = TracerUtil.getManagerDto();
        Long userId = manager.getId();
        String userName = manager.getName();

        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.FLASH_SALE_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(userId);
        createExportTaskBo.setOperatorName(userName);
        exportTaskBiz.create(createExportTaskBo);
    }
}
