package com.sankuai.shangou.seashop.m.core.service.finance;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiPendingSettlementQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPendingSettlementOrderResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPendingSettlementResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPlatCommissionResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
public interface MApiPendingSettlementService {

    /**
     * 获取待结算订单合计列表
     *
     * @param request
     * @return
     */
    BasePageResp<ApiPendingSettlementResp> getPendingSettlementList(ApiPendingSettlementQryReq request);

    /**
     * 获取平台佣金总额
     *
     * @return
     */
    ApiPlatCommissionResp getPlatCommission();

    /**
     * 分页查询待结算订单列表
     *
     * @param request
     * @return
     */
    BasePageResp<ApiPendingSettlementOrderResp> pageList(ApiPendingSettlementOrderQryReq request);

    /**
     * 根据订单id查询订单详情
     *
     * @param request
     * @return
     */
    ApiPendingSettlementOrderResp getDetailByOrderId(ApiOrderIdQryReq request);

    /**
     * 导出待结算订单列表总和
     *
     * @param request
     */
    void exportPendSettleListTotal(ApiPendingSettlementQryReq request);

    /**
     * 导出待结算订单列表
     *
     * @param request
     */
    void exportPendSettleList(ApiPendingSettlementOrderQryReq request);
}
