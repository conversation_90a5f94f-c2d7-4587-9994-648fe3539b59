package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiCategoryCashDepositListReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiCategoryCashDepositReq;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryCashDepositCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.CategoryCashDepositListReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.CategoryCashDepositReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiCategoryCashDeposit")
public class MApiCategoryCashDepositCmdController {

    @Resource
    private CategoryCashDepositCmdFeign categoryCashDepositCmdFeign;

    @PostMapping(value = "/save", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> save(@RequestBody ApiCategoryCashDepositListReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            CategoryCashDepositListReq paramReq = JsonUtil.copy(req, CategoryCashDepositListReq.class);
            paramReq.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> categoryCashDepositCmdFeign.save(paramReq));
        });
    }

    @PostMapping(value = "/saveOne", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> saveOne(@RequestBody ApiCategoryCashDepositReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("saveOne", request, req -> {
            CategoryCashDepositReq paramReq = JsonUtil.copy(req, CategoryCashDepositReq.class);
            paramReq.checkParameter();
            CategoryCashDepositListReq categoryCashDepositListReq = new CategoryCashDepositListReq();
            categoryCashDepositListReq.setList(Arrays.asList(paramReq));
            categoryCashDepositListReq.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> categoryCashDepositCmdFeign.save(categoryCashDepositListReq));
        });
    }
}
