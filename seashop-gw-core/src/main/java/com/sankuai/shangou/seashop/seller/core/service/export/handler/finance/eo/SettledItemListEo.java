package com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
@Getter
@Setter
public class SettledItemListEo {

    //订单ID	店铺名称	支付方式	结算时间	结算手续费	订单金额	平台佣金	退款金额	供应商结算金额	订单完成时间

    @ExcelProperty(value = "订单ID")
    private String orderId;

    @ExcelProperty(value = "店铺id")
    @ExcelIgnore
    private Long shopId;

    @ExcelProperty(value = "店铺名称")
    private String shopName;

    @ExcelProperty(value = "支付方式")
    private String paymentTypeName;

    @ExcelProperty(value = "结算时间")
    private Date settleTime;

    @ExcelProperty(value = "结算手续费")
    private BigDecimal channelAmount;

    @ExcelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ExcelProperty(value = "平台佣金")
    private BigDecimal commissionAmount;

    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ExcelProperty(value = "供应商结算金额")
    private BigDecimal settleAmount;

    @ExcelProperty(value = "付款时间")
    @ExcelIgnore
    private Date payTime;

    @ExcelProperty(value = "订单完成时间")
    private Date orderFinishTime;

}
