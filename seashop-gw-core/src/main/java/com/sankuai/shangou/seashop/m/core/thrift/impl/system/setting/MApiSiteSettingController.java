package com.sankuai.shangou.seashop.m.core.thrift.impl.system.setting;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressTrackQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.MessageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.SystemStyleEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseAgreementReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseAllAgreementReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseCustFormQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseCustomFormReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseSettledReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseShopSitSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseSiteSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.ContactReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.ExpressConfigReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveProductSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SmsBodyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SmsSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SystemStyleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseAgreementRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseAllAgreementRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseSettledRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseShopSitSettingRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseSitSettingRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressConfigRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.ProductSettingResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.SmsSettingRes;
import com.sankuai.shangou.seashop.m.core.thrift.impl.system.convert.MApiSiteSettingConverter;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseAgreementReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseAllAgreementReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseCustFormQueryReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseCustomFormReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseSettledReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseShopSiteSettingReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseSiteSettingReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseSmsSendReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseSmsSettingReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiExpressConfigReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiSaveProductSettingReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiStyleReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiBaseAgreementRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiBaseAllAgreementRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiBaseCustomFormRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiBaseSettledRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiBaseShopSiteSettingRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiBaseSitSettingRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiBaseSmsSettingRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiCategoryResp;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiCustomFormRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiExpressConfigRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiProductSettingResp;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryListReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;

@RestController
@RequestMapping("/mApi/apiSiteSetting")
public class MApiSiteSettingController {

    @Resource
    private SiteSettingQueryFeign siteSettingQueryFeign;

    @Resource
    private SiteSettingCMDFeign siteSettingCMDFeign;

    @Resource
    private CategoryQueryFeign categoryQueryFeign;

    @Resource
    private MApiSiteSettingConverter mApiSiteSettingConverter;

    @Resource
    private MessageCMDFeign messageCMDFeign;

    @Resource
    private ExpressTrackQueryFeign expressTrackQueryFeign;

    @GetMapping(value = "/getSetting")
    public ResultDto<ApiBaseSitSettingRes> getSetting() throws TException {
        return ThriftResponseHelper.responseInvoke("getSetting", null, req -> {
            BaseSitSettingRes settingRes = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getSetting());
            return JsonUtil.copy(settingRes, ApiBaseSitSettingRes.class);
        });
    }

    @PostMapping(value = "/saveSettings", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> saveSettings(@RequestBody ApiBaseSiteSettingReq settingReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveSettings", settingReq, req -> {
            req.checkParameter();

            BaseSiteSettingReq bean = JsonUtil.copy(req, BaseSiteSettingReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.saveSettings(bean));
            return result;
        });
    }

    @GetMapping(value = "/getSettled")
    public ResultDto<ApiBaseSettledRes> getSettled() throws TException {
        return ThriftResponseHelper.responseInvoke("getSettled", null, req -> {
            BaseSettledRes result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getSettled());
            return JsonUtil.copy(result, ApiBaseSettledRes.class);
        });
    }

    @GetMapping(value = "/getAgreement")
    public ResultDto<ApiBaseAgreementRes> getAgreement(@RequestParam int agreementType) throws TException {
        return ThriftResponseHelper.responseInvoke("getAgreement", agreementType, req -> {
            BaseAgreementRes result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getAgreement(req));
            return JsonUtil.copy(result, ApiBaseAgreementRes.class);
        });
    }

    @PostMapping(value = "/saveSettled", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> saveSettled(@RequestBody ApiBaseSettledReq settledReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveSettled", settledReq, req -> {

            BaseSettledReq bean = JsonUtil.copy(req, BaseSettledReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.saveSettled(bean));
            return result;
        });
    }

    @PostMapping(value = "/saveAgreement", consumes = "application/json")
    public ResultDto<Boolean> saveAgreement(@RequestBody ApiBaseAgreementReq agreementReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveAgreement", agreementReq, req -> {
            req.checkParameter();
            BaseAgreementReq bean = JsonUtil.copy(req, BaseAgreementReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.saveAgreement(bean));
            return result;
        });
    }

    @GetMapping(value = "/getCustomForm")
    public ResultDto<ApiBaseCustomFormRes> getCustomForm(@RequestParam long formId) throws TException {
        return ThriftResponseHelper.responseInvoke("saveAgreement", formId, req -> {
            BaseCustomFormRes result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getCustomForm(formId));
            return JsonUtil.copy(result, ApiBaseCustomFormRes.class);
        });
    }

    @PostMapping(value = "/queryCustomFormWithPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiCustomFormRes>> queryCustomFormWithPage(@RequestBody ApiBaseCustFormQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("saveAgreement", query, req -> {
            BaseCustFormQueryReq bean = JsonUtil.copy(req, BaseCustFormQueryReq.class);
            BasePageResp<BaseCustomFormRes> rpcResult = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.queryCustomFormWithPage(bean));

            List<ApiCustomFormRes> list = JsonUtil.copyList(rpcResult.getData(), ApiCustomFormRes.class);
            if (rpcResult.getData() != null && rpcResult.getData().stream().count() > 0) {
                List<Long> customIds = rpcResult.getData().stream().map(t -> t.getId()).collect(Collectors.toList());
                QueryCategoryListReq queryCategory = new QueryCategoryListReq();
                queryCategory.setCustomerFormIds(customIds);
                CategoryListResp category = ThriftResponseHelper.executeThriftCall(() ->
                        categoryQueryFeign.queryCategoryList(queryCategory));

                for (ApiCustomFormRes customFormRes : list) {
                    Boolean hasOne = category.getCategoryRespList().stream().filter(t -> t.getCustomFormId().equals(customFormRes.getId())).findAny().isPresent();
                    if (hasOne) {
                        List<CategoryResp> categoryRes = category.getCategoryRespList().stream().filter(t -> t.getCustomFormId().equals(customFormRes.getId())).collect(Collectors.toList());
                        customFormRes.setCategorys(JsonUtil.copyList(categoryRes, ApiCategoryResp.class));
                    }
                }
            }
            BasePageResp<ApiCustomFormRes> result = new BasePageResp<>();
            result.setData(list);
            result.setPageNo(rpcResult.getPageNo());
            result.setPages(rpcResult.getPages());
            result.setPageSize(rpcResult.getPageSize());
            result.setTotalCount(rpcResult.getTotalCount());
            return result;
        });
    }

    @PostMapping(value = "/createCustomFrom", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> createCustomFrom(@RequestBody ApiBaseCustomFormReq baseCustomFormReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createCustomFrom", baseCustomFormReq, req -> {
            req.checkParameter();
            BaseCustomFormReq bean = JsonUtil.copy(req, BaseCustomFormReq.class);
            Long result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.createCustomFrom(bean));
            return result;
        });
    }

    @PostMapping(value = "/updateCustomFrom", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> updateCustomFrom(@RequestBody ApiBaseCustomFormReq baseCustomFormReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCustomFrom", baseCustomFormReq, req -> {
            req.checkParameter();
            BaseCustomFormReq bean = JsonUtil.copy(req, BaseCustomFormReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.updateCustomFrom(bean));
            return result;
        });
    }

    @GetMapping(value = "/existCustomFrom")
    public ResultDto<Boolean> existCustomFrom(@RequestParam String name) throws TException {
        if (StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("表单名称不能为空");
        }
        return ThriftResponseHelper.responseInvoke("existCustomFrom", name, req -> {
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.existCustomFrom(req));
            return result;
        });
    }

    @PostMapping(value = "/deletesCustomFrom", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> deletesCustomFrom(@RequestBody List<Long> formIds) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCustomFrom", formIds, req -> {

            BaseIdsReq baseIdsReq = new BaseIdsReq();
            baseIdsReq.setIds(req);
//            baseIdsReq.setOperationUserId(AuthUtil.managerUserInfo().getLoginDto().getId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.deletesCustomFrom(baseIdsReq));
            return result;
        });
    }

    @PostMapping(value = "/saveShopSettings", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> saveShopSettings(@RequestBody ApiBaseShopSiteSettingReq settingReq) throws TException {
        //转化参数
        settingReq.checkParameter();
        BaseShopSitSettingReq baseShopSitSettingReq = mApiSiteSettingConverter.convertReq(settingReq);
        return ThriftResponseHelper.responseInvoke("saveShopSettings", baseShopSitSettingReq, req -> ThriftResponseHelper.executeThriftCall(() ->
                siteSettingCMDFeign.saveShopSettings(req)));
    }

    @GetMapping(value = "/getShopSettings")
    public ResultDto<ApiBaseShopSiteSettingRes> getShopSettings() throws TException {
        return ThriftResponseHelper.responseInvoke("getShopSettings", null, req -> {
            BaseShopSitSettingRes shopSitSettingRes = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getShopSettings());
            return mApiSiteSettingConverter.convertRes(shopSitSettingRes);
        });
    }

    @PostMapping(value = "/saveAllAgreement", consumes = "application/json")
    public ResultDto<Boolean> saveAllAgreement(@RequestBody ApiBaseAllAgreementReq agreementReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveAllAgreement", agreementReq, req -> {
            //转化参数
            req.checkParameter();
            BaseAllAgreementReq bean = JsonUtil.copy(req, BaseAllAgreementReq.class);
            boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.saveAllAgreement(bean));
            return result;
        });
    }

    @GetMapping(value = "/getAllAgreement")
    public ResultDto<ApiBaseAllAgreementRes> getAllAgreement() throws TException {
        return ThriftResponseHelper.responseInvoke("getShopSettings", null, req -> {
            BaseAllAgreementRes baseAllAgreementRes = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getAllAgreement());
            return JsonUtil.copy(baseAllAgreementRes, ApiBaseAllAgreementRes.class);
        });
    }

    @PostMapping(value = "/saveProductSettings", consumes = "application/json")
    public ResultDto<BaseResp> saveProductSettings(@RequestBody ApiSaveProductSettingReq settingReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveProductSettings", settingReq, req -> {
            req.checkParameter();
            SaveProductSettingReq bean = JsonUtil.copy(req, SaveProductSettingReq.class);
            BaseResp baseResp = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingCMDFeign.saveProductSettings(bean));
            return baseResp;
        });
    }

    @GetMapping(value = "/getProductSettings")
    public ResultDto<ApiProductSettingResp> getProductSettings() throws TException {
        return ThriftResponseHelper.responseInvoke("getProductSettings", null, req -> {
            ProductSettingResp productSettingResp = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getProductSettings());
            return JsonUtil.copy(productSettingResp, ApiProductSettingResp.class);
        });
    }

    @GetMapping(value = "/querySettingsValueByKey")
    public ResultDto<String> querySettingsValueByKey(@RequestParam String key) throws TException {
        return ThriftResponseHelper.responseInvoke("querySettingsValueByKey", key, req -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.querySettingsValueByKey(req);
            });
        });
    }

    @GetMapping(value = "/querySmsBalance")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> querySmsBalance() {
        return ThriftResponseHelper.responseInvoke("querySmsBalance", null, req -> ThriftResponseHelper.executeThriftCall(() -> messageCMDFeign.querySmsBalance()));
    }


    @GetMapping(value = "/querySmsSetting")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiBaseSmsSettingRes> querySmsSetting() {
        return ThriftResponseHelper.responseInvoke("querySmsSetting", null, req -> {
            SmsSettingRes settingRes = ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.querySmsSetting();
            });
            return JsonUtil.copy(settingRes, ApiBaseSmsSettingRes.class);
        });
    }

    @PostMapping(value = "/saveSmsSetting", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> saveSmsSetting(@RequestBody ApiBaseSmsSettingReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("saveSmsSetting", req, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingCMDFeign.saveSmsSetting(JsonUtil.copy(fun1, SmsSettingReq.class));
            });
        });
    }

    @PostMapping(value = "/trySendSms", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> trySendSms(@RequestBody ApiBaseSmsSendReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("trySendSms", req, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                SmsBodyReq smsBodyReq = new SmsBodyReq();
                smsBodyReq.setTemplateId(0L);
                ContactReq contactReq = new ContactReq();
                contactReq.setMobile(fun1.getMobile());
                smsBodyReq.setContactList(Collections.singletonList(contactReq));
                return messageCMDFeign.sendSms(smsBodyReq);
            });
        });
    }

    @GetMapping(value = "/getShopTheme")
    public ResultDto<String> getShopTheme() {
        return ThriftResponseHelper.responseInvoke("getShopTheme", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.SHOPTHEME);
            });
        });
    }

    @GetMapping(value = "/getProductCatetory")
    public ResultDto<String> getProductCatetory() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.PRODUCTCATETORY);
            });
        });
    }

    @GetMapping(value = "/getUserCenter")
    public ResultDto<String> getUserCenter() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.USERCENTER);
            });
        });
    }

    @GetMapping(value = "/getPCShopTheme")
    public ResultDto<String> getPCShopTheme() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.PCSHOPTHEME);
            });
        });
    }


    @PostMapping(value = "/saveShopTheme", consumes = "application/json")
    public ResultDto<BaseResp> saveShopTheme(@RequestBody ApiStyleReq req) {
        return ThriftResponseHelper.responseInvoke("saveShopTheme", req, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                boolean isJson = JsonUtil.isJson(fun1.getJsonData());
                if (!isJson) {
                    throw new IllegalArgumentException("请传入正确的json数据");
                }
                SystemStyleReq systemStyleReq = new SystemStyleReq();
                systemStyleReq.setSystemStyleEnum(SystemStyleEnum.SHOPTHEME);
                systemStyleReq.setValue(fun1.getJsonData());
                return siteSettingCMDFeign.saveShopStyle(systemStyleReq);
            });
        });
    }

    @PostMapping(value = "/saveProductCatetory", consumes = "application/json")
    public ResultDto<BaseResp> saveProductCatetory(@RequestBody ApiStyleReq req) {
        return ThriftResponseHelper.responseInvoke("saveProductCatetory", req, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                boolean isJson = JsonUtil.isJson(fun1.getJsonData());
                if (!isJson) {
                    throw new IllegalArgumentException("请传入正确的json数据");
                }
                SystemStyleReq systemStyleReq = new SystemStyleReq();
                systemStyleReq.setSystemStyleEnum(SystemStyleEnum.PRODUCTCATETORY);
                systemStyleReq.setValue(fun1.getJsonData());
                return siteSettingCMDFeign.saveShopStyle(systemStyleReq);
            });
        });
    }

    @PostMapping(value = "/saveUserCenter", consumes = "application/json")
    public ResultDto<BaseResp> saveUserCenter(@RequestBody ApiStyleReq req) {
        return ThriftResponseHelper.responseInvoke("saveUserCenter", req, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                boolean isJson = JsonUtil.isJson(fun1.getJsonData());
                if (!isJson) {
                    throw new IllegalArgumentException("请传入正确的json数据");
                }
                SystemStyleReq systemStyleReq = new SystemStyleReq();
                systemStyleReq.setSystemStyleEnum(SystemStyleEnum.USERCENTER);
                systemStyleReq.setValue(fun1.getJsonData());
                return siteSettingCMDFeign.saveShopStyle(systemStyleReq);
            });
        });
    }

    @PostMapping(value = "/savePCShopTheme", consumes = "application/json")
    public ResultDto<BaseResp> savePCShopTheme(@RequestBody ApiStyleReq req) {
        return ThriftResponseHelper.responseInvoke("saveUserCenter", req, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                boolean isJson = JsonUtil.isJson(fun1.getJsonData());
                if (!isJson) {
                    throw new IllegalArgumentException("请传入正确的json数据");
                }
                SystemStyleReq systemStyleReq = new SystemStyleReq();
                systemStyleReq.setSystemStyleEnum(SystemStyleEnum.PCSHOPTHEME);
                systemStyleReq.setValue(fun1.getJsonData());
                return siteSettingCMDFeign.saveShopStyle(systemStyleReq);
            });
        });
    }

    @PostMapping(value = "/saveExpressConfig", consumes = "application/json")
    public ResultDto<BaseResp> saveExpressConfig(@RequestBody ApiExpressConfigReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("saveExpressConfig", req, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                ExpressConfigReq expressConfigReq = JsonUtil.copy(fun1, ExpressConfigReq.class);
                return siteSettingCMDFeign.saveExpressConfig(expressConfigReq);
            });
        });
    }

    @GetMapping(value = "/queryExpressConfig")
    public ResultDto<ApiExpressConfigRes> queryExpressConfig() {
        return ThriftResponseHelper.responseInvoke("saveExpressConfig", null, fun1 -> {
            ExpressConfigRes configRes = ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryExpressConfig();
            });

            Long balance = ThriftResponseHelper.executeThriftCall(() -> {
                return expressTrackQueryFeign.queryBalance();
            });
            ApiExpressConfigRes res = JsonUtil.copy(configRes, ApiExpressConfigRes.class);
            res.setBalance(balance);
            return res;
        });
    }

    @GetMapping(value = "/queryInitConfig")
    public ResultDto<ApiExpressConfigRes> queryInitConfig() throws TException {
        return ThriftResponseHelper.responseInvoke("saveExpressConfig", null, fun1 -> {
            ExpressConfigRes configRes = ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryExpressConfig();
            });

            Long balance = ThriftResponseHelper.executeThriftCall(() -> {
                return expressTrackQueryFeign.queryBalance();
            });
            ApiExpressConfigRes res = JsonUtil.copy(configRes, ApiExpressConfigRes.class);
            res.setBalance(balance);
            return res;
        });
    }
}
