package com.sankuai.shangou.seashop.seller.core.thrift.impl.report;

import com.hishop.himall.report.api.request.CustomPageReq;
import com.hishop.himall.report.api.request.CustomRecordPageReq;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.TradeReq;
import com.hishop.himall.report.api.response.*;
import com.hishop.himall.report.api.service.ReportCustomFeign;
import com.hishop.himall.report.api.service.ReportTradeFeign;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.util.model.ResultInfo;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sellerApi/report/trade")
public class SellerApiTradeController {

    @Resource
    private ReportTradeFeign reportTradeFeign;

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/summary", consumes = "application/json")
    public ResultDto<TradeSummaryResp> queryTradeSummary(@RequestBody ReportReq request) {
        return ThriftResponseHelper.responseInvoke("queryTradeSummary", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportTradeFeign.queryProductSummary(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> echarts(@RequestBody ReportReq request) {
        return ThriftResponseHelper.responseInvoke("queryTrade", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportTradeFeign.echarts(req));
        });
    }
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/province", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryProvince(@RequestBody ReportReq request){
        return ThriftResponseHelper.responseInvoke("queryProvince", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportTradeFeign.queryProvince(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<PageResult<TradeResp>> queryTrade(@RequestBody TradeReq req) {
        return ThriftResponseHelper.responseInvoke("queryTrade", req, request -> {
            request.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportTradeFeign.queryTrade(request));
        });
    }

    /**
     * 导出交易分析报表
     *
     * @param req
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<PageResult<Object>> export(@RequestBody TradeReq req){
        return ThriftResponseHelper.responseInvoke("export", req, request -> {
            request.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportTradeFeign.export(request));
        });
    }

}
