package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryShopBrandReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiShopBrandListResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiShopBrandDto;
import com.sankuai.shangou.seashop.product.thrift.core.ShopBrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryShopBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.ShopBrandListResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/15 12:09
 */
@RestController
@RequestMapping("/mApi/apiShopBrand")
public class MApiShopBrandQueryController {

    @Resource
    private ShopBrandQueryFeign shopBrandQueryFeign;

    @PostMapping(value = "/queryShopBrandForList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiShopBrandListResp> queryShopBrandForList(@RequestBody ApiQueryShopBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopBrandForList", request, req -> {

            QueryShopBrandReq remoteReq = new QueryShopBrandReq();
            remoteReq.setShopId(req.getShopId());
            ShopBrandListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopBrandQueryFeign.queryShopBrandForList(remoteReq));
            return ApiShopBrandListResp.builder().brandList(JsonUtil.copyList(resp.getBrandList(), ApiShopBrandDto.class)).build();
        });
    }
}
