package com.sankuai.shangou.seashop.mall.core.service.export.handler.refund.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.mall.common.remote.order.MallOrderRefundRemoteService;
import com.sankuai.shangou.seashop.mall.core.service.export.handler.refund.eo.ReturnExportEo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundItemDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundDto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class ReturnWrapper extends PageExportWrapper<ReturnExportEo, UserQueryRefundReq> {

    private final MallOrderRefundRemoteService mallOrderRefundRemoteService = SpringUtil.getBean(MallOrderRefundRemoteService.class);

    @Override
    public List<ReturnExportEo> getPageList(UserQueryRefundReq param) {
        log.info("【售后-退货】查询售后导出参数:{}", JsonUtil.toJsonString(param));
        BasePageResp<UserRefundDto> pageResp = mallOrderRefundRemoteService.userQueryRefundPage(param);
        if (pageResp == null || CollUtil.isEmpty(pageResp.getData())) {
            return null;
        }
        return JsonUtil.copyList(pageResp.getData(), ReturnExportEo.class, (from, to) -> {
            if (RefundModeEnum.ORDER_REFUND.getCode().equals(from.getRefundMode()) || Boolean.TRUE.equals(from.getHasAllReturn())) {
                to.setProductName("订单所有商品");
            }
            else {
                RefundItemDto item = from.getItemList().get(0);
                to.setProductName(item.getProductName());
                to.setSkuDesc(item.getSkuDesc());
            }
            // 非完成，不返回完成时间
            if (!RefundStatusEnum.REFUND_SUCCESS.getCode().equals(from.getRefundStatus())) {
                to.setManagerConfirmDate(null);
            }
        });
    }
}
