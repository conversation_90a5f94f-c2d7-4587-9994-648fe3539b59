package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopSetFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopLogoReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopLogoResp;

/**
 * <AUTHOR>
 * @date 2024/11/21 11:03
 */
@RestController
@RequestMapping("/sellerApi/apiShopPageSet")
public class SellerShopSetController {

    @Resource
    private ShopSetFeign shopSetFeign;

    @PostMapping(value = "/saveShopLogo", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> saveShopLogo(@RequestBody SaveShopLogoReq savePageLogReq) {
        return ThriftResponseHelper.responseInvoke("saveShopLogo", savePageLogReq, req -> {

            Long shopId = Objects.requireNonNull(TracerUtil.getShopDto()).getShopId();
            savePageLogReq.setShopId(shopId);
            return ThriftResponseHelper.executeThriftCall(() -> shopSetFeign.saveShopLogo(savePageLogReq));
        });
    }

    @PostMapping(value = "/queryShopLogo")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ShopLogoResp> getShopLogo() {
        return ThriftResponseHelper.responseInvoke("queryShopLogo", null, req -> {

            Long shopId = Objects.requireNonNull(TracerUtil.getShopDto()).getShopId();
            return ThriftResponseHelper.executeThriftCall(() -> shopSetFeign.getShopLogo(shopId));
        });
    }

}
