package com.sankuai.shangou.seashop.m.core.thrift.impl.system.express;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.RefundReasonCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.RefundReasonQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateRefundReasonReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiAddRefundReasonReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiDeleteRefundReasonReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiUpdateRefundReasonReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiQueryRefundReasonResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:34
 */
@RestController
@RequestMapping("/mApi/apiRefundReason")
public class MApiRefundReasonController {

    @Resource
    private RefundReasonCmdFeign refundReasonCmdFeign;
    @Resource
    private RefundReasonQueryFeign refundReasonQueryFeign;

    @PostMapping(value = "/addRefundReason", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> addRefundReason(@RequestBody ApiAddRefundReasonReq addRefundReasonReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addRefundReason", addRefundReasonReq, req -> {
            req.checkParameter();
            AddRefundReasonReq bean = JsonUtil.copy(req, AddRefundReasonReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> refundReasonCmdFeign.addRefundReason(bean));
        });
    }

    @PostMapping(value = "/updateRefundReason", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateRefundReason(@RequestBody ApiUpdateRefundReasonReq updateRefundReasonReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateRefundReason", updateRefundReasonReq, req -> {
            req.checkParameter();
            UpdateRefundReasonReq bean = JsonUtil.copy(req, UpdateRefundReasonReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> refundReasonCmdFeign.updateRefundReason(bean));
        });
    }

    @PostMapping(value = "/deleteRefundReason", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deleteRefundReason(@RequestBody ApiDeleteRefundReasonReq deleteRefundReasonReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteRefundReason", deleteRefundReasonReq, req -> {
            req.checkParameter();
            DeleteRefundReasonReq bean = JsonUtil.copy(req, DeleteRefundReasonReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> refundReasonCmdFeign.deleteRefundReason(bean));
        });
    }

    @GetMapping(value = "/queryRefundReasonList")
    public ResultDto<ApiQueryRefundReasonResp> queryRefundReasonList() throws TException {
        return ThriftResponseHelper.responseInvoke("queryRefundReasonList", null, req -> {
            return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> refundReasonQueryFeign.queryRefundReasonList()),
                    ApiQueryRefundReasonResp.class);
        });
    }
}
