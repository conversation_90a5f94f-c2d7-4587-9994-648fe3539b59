package com.sankuai.shangou.seashop.m.core.service.export.handler.product.eo;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/01/03 18:03
 */
@Getter
@Setter
public class ProductAuditRefuseEo {

    @ExcelProperty(value = "商品ID")
    private String productId;

    @ExcelProperty(value = "商品名称")
    private String productName;

    @ExcelProperty(value = "拒绝原因")
    private String auditReason;

}
