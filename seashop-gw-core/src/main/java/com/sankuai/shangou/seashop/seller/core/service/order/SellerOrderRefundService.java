package com.sankuai.shangou.seashop.seller.core.service.order;


import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerConfirmReceiveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundLogListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundUserDeliverExpressResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDto;

/**
 * <AUTHOR>
 */
public interface SellerOrderRefundService {

    /**
     * 导出售后列表
     *
     * @param queryReq 请求参数
     */
    void export(SellerQueryRefundReq queryReq);

    /**
     * 查询售后详情
     *
     * @param queryReq 请求参数
     * @return 售后详情
     */
    SellerRefundDetailResp queryDetail(SellerQueryRefundDetailReq queryReq);

    /**
     * 查询售后日志
     *
     * @param queryReq 请求参数
     * @return 售后日志
     */
    RefundLogListResp queryRefundLog(BaseIdReq queryReq);

    /**
     * 供应商查询售后列表
     *
     * @param queryReq 请求参数
     * @return 售后物流
     */
    BasePageResp<SellerRefundDto> sellerQueryRefundPage(SellerQueryRefundReq queryReq);

    /**
     * 查询售后物流
     *
     * @param queryReq 请求参数
     * @return 售后物流
     */
    RefundUserDeliverExpressResp queryUserDeliverExpress(BaseIdReq queryReq);

    /**
     * 供应商审核售后
     *
     * @param sellerApproveReq 请求参数
     * @return 售后物流
     */
    BaseResp sellerApprove(SellerApproveReq sellerApproveReq);

    /**
     * 供应商确认收货
     *
     * @param req 请求参数
     * @return 售后物流
     */
    BaseResp sellerConfirmReceive(SellerConfirmReceiveReq req);

}
