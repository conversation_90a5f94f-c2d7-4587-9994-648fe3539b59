package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiManagerService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdManagerReq;

/**
 * @description: 商家服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiManager")
public class MApiManagerCmdController {
    @Resource
    private MApiManagerService mApiManagerService;

    @PostMapping(value = "/addManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> addManager(@RequestBody ApiCmdManagerReq cmdManagerReq) {
        cmdManagerReq.setOperatorId(TracerUtil.getManagerDto().getId());
        return ThriftResponseHelper.responseInvoke("addManager", cmdManagerReq, req -> mApiManagerService.addManager(cmdManagerReq));
    }

    @PostMapping(value = "/editManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> editManager(@RequestBody ApiCmdManagerReq cmdManagerReq) {
        cmdManagerReq.setOperatorId(TracerUtil.getManagerDto().getId());
        return ThriftResponseHelper.responseInvoke("editManager", cmdManagerReq, req -> mApiManagerService.editManager(cmdManagerReq));
    }

    @PostMapping(value = "/deleteManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> deleteManager(@RequestBody ApiCmdManagerReq cmdManagerReq) {
        cmdManagerReq.setOperatorId(TracerUtil.getManagerDto().getId());
        return ThriftResponseHelper.responseInvoke("deleteManager", cmdManagerReq, req -> mApiManagerService.deleteManager(cmdManagerReq));
    }

    @PostMapping(value = "/batchDeleteManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Integer> batchDeleteManager(@RequestBody ApiCmdManagerReq cmdManagerReq) {
        cmdManagerReq.setOperatorId(TracerUtil.getManagerDto().getId());
        return ThriftResponseHelper.responseInvoke("batchDeleteManager", cmdManagerReq, req -> mApiManagerService.batchDeleteManager(cmdManagerReq));
    }

    @PostMapping(value = "/login",consumes = "application/json")
    public ResultDto<LoginResp> login(@RequestBody ApiLoginReq loginReq) {
        return ThriftResponseHelper.responseInvoke("login", loginReq, req -> mApiManagerService.login(req));
    }

    //登出
    @PostMapping(value = "/logout",consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> logout() {
        return ThriftResponseHelper.responseInvoke("logout", null, req -> mApiManagerService.logout());
    }
}
