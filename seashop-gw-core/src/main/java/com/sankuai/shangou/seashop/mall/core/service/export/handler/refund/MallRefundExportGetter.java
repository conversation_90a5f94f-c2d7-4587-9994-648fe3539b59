package com.sankuai.shangou.seashop.mall.core.service.export.handler.refund;

import java.util.Collections;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.mall.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.mall.core.service.export.handler.refund.eo.RefundExportEo;
import com.sankuai.shangou.seashop.mall.core.service.export.handler.refund.wrapper.RefundWrapper;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundReq;

/**
 * 退款导出
 *
 * <AUTHOR>
 */
@Service
public class MallRefundExportGetter extends AbstractBaseDataGetter<UserQueryRefundReq>
    implements SingleWrapperDataGetter<UserQueryRefundReq> {

    @Override
    public DataContext selectData(UserQueryRefundReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        // 构造数据包装器，每一个包装器对应一个sheet页，并且每个包装器可以单独定义自己的excel处理器，比如样式、合并单元格等
        BaseExportWrapper<RefundExportEo, UserQueryRefundReq> orderWrapper = new RefundWrapper();
        context.setSheetDataList(Collections.singletonList(orderWrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.APPLY_REFUND.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.APPLY_REFUND.getName();
    }
}
