package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopErpRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiSaveShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopErpReq;

/**
 * @author： liweisong
 * @create： 2023/11/28 14:58
 */
@RestController
@RequestMapping("/sellerApi/apiShopErp")
public class SellerApiShopErpCmdController {

    @Resource
    private SellerShopErpRemoteService sellerShopErpRemoteService;

    @PostMapping(value = "/saveShopErp", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> saveShopErp(@RequestBody ApiSaveShopErpReq saveShopErpReq) throws TException {
        

        return ThriftResponseHelper.responseInvoke("saveShopErp", saveShopErpReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerShopErpRemoteService.saveShopErp(JsonUtil.copy(req, SaveShopErpReq.class));
        });
    }
}
