package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.shop;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiQueryFavoriteProductReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiQueryFavoriteProductResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.FavoriteProductQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFavoriteProductReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFavoriteProductResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/11/28 9:48
 */
@RestController
@RequestMapping("/mallApi/apiFavoriteProduct")
public class MallApiFavoriteProductQueryController {

    @Resource
    private FavoriteProductQueryFeign favoriteProductQueryFeign;

    @PostMapping(value = "/pageFavoriteProduct", consumes = "application/json")
    @NeedLogin
    public ResultDto<BasePageResp<ApiQueryFavoriteProductResp>> pageFavoriteProduct(@RequestBody ApiQueryFavoriteProductReq queryFavoriteProductReq) throws TException {
        return ThriftResponseHelper.responseInvoke("pageFavoriteProduct", queryFavoriteProductReq, req -> {
            req.setUserId(TracerUtil.getMemberDto().getId());
            req.checkParameter();
            BasePageResp<QueryFavoriteProductResp> resp = ThriftResponseHelper.executeThriftCall(() ->
                    favoriteProductQueryFeign.pageFavoriteProduct(JsonUtil.copy(req, QueryFavoriteProductReq.class)));
            return PageResultHelper.transfer(resp, ApiQueryFavoriteProductResp.class);
        });

    }
}
