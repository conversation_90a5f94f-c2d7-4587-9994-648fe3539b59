package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.product.MCategoryRemoteService;
import com.sankuai.shangou.seashop.m.core.service.product.MCategoryService;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryCategoryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiCategoryTreeResp;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ShowStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryTreeResp;

import cn.hutool.core.util.ObjectUtil;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:12
 */
@RestController
@RequestMapping("/mApi/apiCategory")
public class MApiCategoryQueryController {

    @Resource
    private MCategoryRemoteService mCategoryRemoteService;
    @Resource
    private MCategoryService mCategoryService;

    @PostMapping(value = "/queryCategoryTree", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiCategoryTreeResp> queryCategoryTree(@RequestBody ApiQueryCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTree", request, req -> {

            QueryCategoryReq remoteReq = JsonUtil.copy(req, QueryCategoryReq.class);
            remoteReq = ObjectUtil.defaultIfNull(remoteReq, new QueryCategoryReq());
            remoteReq.setShowStatus(ObjectUtil.defaultIfNull(remoteReq.getShowStatus(), ShowStatusEnum.SHOW_ALL.getValue()));
            CategoryTreeResp tree = mCategoryRemoteService.queryCategoryTree(remoteReq);
            return JsonUtil.copy(tree, ApiCategoryTreeResp.class);
        });
    }

    @PostMapping(value = "/exportCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportCategory(@RequestBody ApiQueryCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("exportCategory", request, req -> {

            mCategoryService.exportCategory(req);
            return BaseResp.of();
        });
    }
}
