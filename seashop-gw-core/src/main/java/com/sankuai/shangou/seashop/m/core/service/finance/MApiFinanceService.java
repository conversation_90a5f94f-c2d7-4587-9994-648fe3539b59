package com.sankuai.shangou.seashop.m.core.service.finance;

import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderStatisticsReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiFinanceIndexResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiOrderStatisticsListResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
public interface MApiFinanceService {

    /**
     * 获取财务首页统计数据
     *
     * @return
     */
    ApiFinanceIndexResp getFinanceIndex();

    /**
     * 订单统计列表（财务管理首页统计）
     *
     * @param request
     * @return
     */
    ApiOrderStatisticsListResp getOrderStatisticsList(ApiOrderStatisticsReq request);
}
