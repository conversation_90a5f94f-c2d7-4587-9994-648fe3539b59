package com.sankuai.shangou.seashop.m.core.service.export.handler.finance.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.common.remote.MFinanceRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo.SettledByShopEo;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;

/**
 * @author: lhx
 * @date: 2024/3/26/026
 * @description:
 */
public class SettledByShopWrapper extends PageExportWrapper<SettledByShopEo, ApiSettledQryReq> {

    private final MFinanceRemoteService MFinanceRemoteService;

    public SettledByShopWrapper(MFinanceRemoteService MFinanceRemoteService) {
        this.MFinanceRemoteService = MFinanceRemoteService;
    }

    @Override
    public List<SettledByShopEo> getPageList(ApiSettledQryReq param) {
        BasePageResp<SettledResp> settledPage = MFinanceRemoteService.settledPageList(JsonUtil.copy(param, SettledQryReq.class));
        if (null == settledPage || CollUtil.isEmpty(settledPage.getData())) {
            return null;
        }
        List<SettledByShopEo> settledItemListEos = CollUtil.newArrayList();
        settledPage.getData().forEach(s -> {
            SettledByShopEo settledByShopEo = JsonUtil.copy(s, SettledByShopEo.class);
            settledByShopEo.setCycleTime(DateUtil.formatDateTime(settledByShopEo.getStartCycleTime()) + "-" + DateUtil.formatDateTime(settledByShopEo.getEndCycleTime()));
            settledItemListEos.add(settledByShopEo);
        });
        return settledItemListEos;
    }

    @Override
    public Integer getBatchCount() {
        return 1000;
    }
}
