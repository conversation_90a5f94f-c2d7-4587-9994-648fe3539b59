package com.sankuai.shangou.seashop.m.core.service.user.account;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdManagerReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryManagerPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryManagerReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiManagerResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMenuResp;

import java.util.List;

public interface MApiManagerService {
    BasePageResp<ApiManagerResp> queryManagerPage(ApiQueryManagerPageReq queryManagerPageReq);

    ApiManagerResp queryManager(ApiQueryManagerReq queryManagerPageReq);

    Long addManager(ApiCmdManagerReq cmdManagerReq);

    Long editManager(ApiCmdManagerReq cmdManagerReq);

    Long deleteManager(ApiCmdManagerReq cmdManagerReq);

    Integer batchDeleteManager(ApiCmdManagerReq cmdManagerReq);

    /**
     * 登录
     * @param loginReq 登录请求
     * @return token
     */
    LoginResp login(ApiLoginReq loginReq);

    Boolean logout();

    List<ApiMenuResp> queryMenuAuth(Long managerId);
}
