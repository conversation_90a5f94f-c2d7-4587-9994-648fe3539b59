package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.dto.promotion.ApiCouponProductDto;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiCouponProductQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiCouponQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiCouponResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiCouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiCoupon")
public class MApiCouponQueryController {

    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private CouponQueryFeign couponQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiCouponSimpleResp>> pageList(@RequestBody ApiCouponQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            CouponQueryReq couponQueryReq = JsonUtil.copy(req, CouponQueryReq.class);
            BasePageResp<CouponSimpleResp> couponPage = ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.pageList(couponQueryReq));

            final List<ShopSimpleResp> shopSimpleList = new ArrayList<>();
            if (null != couponPage && CollUtil.isNotEmpty(couponPage.getData())) {
                List<Long> shopIdList = couponPage.getData().stream().map(CouponSimpleResp::getShopId).distinct().collect(Collectors.toList());
                ShopSimpleQueryReq queryReq = new ShopSimpleQueryReq();
                queryReq.setShopIdList(shopIdList);
                ShopSimpleListResp shopSimpleListResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(queryReq));
                if (null != shopSimpleListResp && CollUtil.isNotEmpty(shopSimpleListResp.getList())) {
                    shopSimpleList.addAll(shopSimpleListResp.getList());
                }
            }
            return PageResultHelper.transfer(couponPage, ApiCouponSimpleResp.class, couponSimpleResp -> {
                if (CollUtil.isNotEmpty(shopSimpleList)) {
                    for (ShopSimpleResp shopSimpleResp : shopSimpleList) {
                        if (shopSimpleResp.getId().equals(couponSimpleResp.getShopId())) {
                            couponSimpleResp.setShopName(shopSimpleResp.getShopName());
                            break;
                        }
                    }
                }
            });
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiCouponResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            CouponResp couponResp = ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.getById(req));
            return JsonUtil.copy(couponResp, ApiCouponResp.class);
        });
    }

    @PostMapping(value = "/queryCouponProductPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiCouponProductDto>> queryCouponProductPage(@RequestBody ApiCouponProductQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCouponProductList", request, req -> {
            req.checkParameter();

            BasePageResp<CouponProductDto> couponProductPage = ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.queryCouponProductPage(JsonUtil.copy(req,
                CouponProductQueryReq.class)));
            return PageResultHelper.transfer(couponProductPage, ApiCouponProductDto.class);
        });
    }
}
