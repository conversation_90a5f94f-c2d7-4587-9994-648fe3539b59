package com.sankuai.shangou.seashop.m.core.service.promotion;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiDiscountActiveQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiDiscountActiveSimpleResp;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
public interface MApiDiscountActiveService {

    /**
     * 折扣活动列表查询
     *
     * @param request
     * @return
     */
    BasePageResp<ApiDiscountActiveSimpleResp> pageList(ApiDiscountActiveQueryReq request);
}
