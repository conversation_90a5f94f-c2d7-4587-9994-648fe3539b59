package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiQueryPayChannelReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiOrderPayMethodResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderPayQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPayChannelReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayMethodResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiOrderPay")
@Slf4j
public class MallApiOrderPayQueryController {

    @Resource
    private OrderPayQueryFeign orderPayQueryFeign;

    @NeedLogin
    @PostMapping(value = "/queryPcPayChannel", consumes = "application/json")
    public ResultDto<ApiOrderPayMethodResp> queryPcPayChannel(@RequestBody ApiQueryPayChannelReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【支付】PC端获取支付方式", queryReq,
            func -> {
                QueryPayChannelReq req = JsonUtil.copy(queryReq, QueryPayChannelReq.class);
                req.setUserId(TracerUtil.getMemberDto().getId());
                log.info("【支付】PC端获取支付方式, 请求参数={}", JsonUtil.toJsonString(req));
                OrderPayMethodResp resp = ThriftResponseHelper.executeThriftCall(() -> orderPayQueryFeign.queryPcPayChannel(req));

                return JsonUtil.copy(resp, ApiOrderPayMethodResp.class);
            });
    }

    @NeedLogin
    @PostMapping(value = "/confirmPayResult", consumes = "application/json")
    public ResultDto<Boolean> confirmPayResult(@RequestBody ApiQueryPayChannelReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【支付】确认支付结果", queryReq, func -> {
            QueryPayChannelReq req = JsonUtil.copy(queryReq, QueryPayChannelReq.class);
            req.setUserId(TracerUtil.getMemberDto().getId());
            log.info("【支付】确认支付结果, 请求参数={}", JsonUtil.toJsonString(req));
            return ThriftResponseHelper.executeThriftCall(() -> orderPayQueryFeign.confirmPayResult(req));
        });
    }
}
