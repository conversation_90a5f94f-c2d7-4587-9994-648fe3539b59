package com.sankuai.shangou.seashop.mall.core.thrift.impl.system.convert;

import com.sankuai.shangou.seashop.base.thrift.core.response.BaseShopSitSettingRes;
import com.sankuai.shangou.seashop.seller.thrift.core.response.system.ApiBaseShopSitSettingRes;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MallApiSiteSettingConverter {
    ApiBaseShopSitSettingRes convertRes(BaseShopSitSettingRes shopSitSettingRes);
}
