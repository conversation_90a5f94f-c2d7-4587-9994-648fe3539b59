package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.promotion.MDiscountActiveRemoteService;
import com.sankuai.shangou.seashop.m.core.service.promotion.MApiDiscountActiveService;
import com.sankuai.shangou.seashop.m.thrift.core.dto.promotion.ApiDiscountActiveProductDto;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiDiscountActiveQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiQueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiDiscountActiveResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiDiscountActiveSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;

/**
 * @author: lhx
 * @date: 2023/11/10/010
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiDiscountActive")
public class MApiDiscountActiveQueryController {

    @Resource
    private MDiscountActiveRemoteService discountActiveQueryThriftService;
    @Resource
    private MApiDiscountActiveService mApiDiscountActiveService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiDiscountActiveSimpleResp>> pageList(@RequestBody ApiDiscountActiveQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            return mApiDiscountActiveService.pageList(req);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiDiscountActiveResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            DiscountActiveResp discountActiveResp = discountActiveQueryThriftService.getById(req);
            return JsonUtil.copy(discountActiveResp, ApiDiscountActiveResp.class);
        });
    }

    @PostMapping(value = "/queryDiscountActiveProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiDiscountActiveProductDto>> queryDiscountActiveProduct(@RequestBody ApiQueryDiscountActiveProductReq request) {
        return ThriftResponseHelper.responseInvoke("queryDiscountActiveProduct", request, req -> {
            req.checkParameter();

            QueryDiscountActiveProductReq remoteReq = JsonUtil.copy(req, QueryDiscountActiveProductReq.class);
            BasePageResp<DiscountActiveProductDto> resp = discountActiveQueryThriftService.queryDiscountActiveProduct(remoteReq);
            return PageResultHelper.transfer(resp, ApiDiscountActiveProductDto.class);
        });
    }
}
