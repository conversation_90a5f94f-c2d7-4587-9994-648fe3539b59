package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiBindCustomFormReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiSaveCategoryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiUpdateCategoryParamReq;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.BindCustomFormReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.SaveCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.UpdateCategoryParamReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2023/11/13 16:12
 */
@RestController
@RequestMapping("/mApi/apiCategory")
public class MApiCategoryCmdController {

    @Resource
    private CategoryCmdFeign categoryCmdFeign;

    @PostMapping(value = "/createCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> createCategory(@RequestBody ApiSaveCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createCategory", request, req ->
                ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.createCategory(JsonUtil.copy(req, SaveCategoryReq.class))));
    }

    @PostMapping(value = "/updateCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateCategory(@RequestBody ApiSaveCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategory", request, req ->
                ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.updateCategory(JsonUtil.copy(req, SaveCategoryReq.class))));
    }

    @PostMapping(value = "/updateCategoryParam", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateCategoryParam(@RequestBody ApiUpdateCategoryParamReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategoryParam", request, req ->
                ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.updateCategoryParam(JsonUtil.copy(req, UpdateCategoryParamReq.class))));
    }

    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deleteCategory(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteCategory", request, req ->
                ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.deleteCategory(req)));
    }

    @PostMapping(value = "/bindCustomForm", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> bindCustomForm(@RequestBody ApiBindCustomFormReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("bindCustomForm", request, req ->
                ThriftResponseHelper.executeThriftCall(() -> categoryCmdFeign.bindCustomForm(JsonUtil.copy(req, BindCustomFormReq.class))));
    }
}
