package com.sankuai.shangou.seashop.seller.core.user.converter;

import java.util.List;

import org.mapstruct.Mapper;

import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryPrivilegeReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiPrivilegeResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.PrivilegeResp;

@Mapper(componentModel = "spring")
public interface SellerApiPrivilegeConverter {
    QueryPrivilegeReq apiQueryPrivilegeReq2QueryPrivilegeReq(ApiQueryPrivilegeReq queryPrivilegePageReq);

    List<ApiPrivilegeResp> privilegeRespList2ApiPrivilegeRespList(List<PrivilegeResp> privilegeRespList);
}
