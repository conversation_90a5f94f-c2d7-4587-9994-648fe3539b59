package com.sankuai.shangou.seashop.mall.core.thrift.impl.system.express;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.express.ExpressQueryReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressTrackQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.LoadSubscribedExpressReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressTrackRes;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.express.ApiExpressTrackRes;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/mallApi/apiExpressTrack")
@Slf4j
public class MallApiExpressTrackController {

    @Value("${express.bizType:47}")
    private Integer bizType;
    @Resource
    private ExpressTrackQueryFeign expressTrackQueryFeign;

    @PostMapping(value = "/loadSubscribedExpressByHiShopCompanyCodeAndExpressNo", consumes = "application/json")
    public ResultDto<ExpressTrackRes> loadSubscribedExpressByHiShopCompanyCodeAndExpressNo(@RequestBody ExpressQueryReq request) {
        return ThriftResponseHelper.responseInvoke("loadSubscribedExpressByHiShopCompanyCodeAndExpressNo", request, req -> {
            try {
                LoadSubscribedExpressReq query = new LoadSubscribedExpressReq();
                query.setBizType(bizType);
                query.setExpressNo(req.getExpressNo());
                query.setCompanyCode(req.getCompanyCode());
                query.setReceiveMobile(req.getReceiveMobile());
                ExpressTrackRes result =ThriftResponseHelper.executeThriftCall(() -> expressTrackQueryFeign.loadSubscribedExpressByHiShopCompanyCodeAndExpressNo(query));

                return result;
            } catch (Exception e) {
                log.error("物流查询失败, param：{}", JsonUtil.toJsonString(req), e);
                return null;
            }
        });
    }
}
