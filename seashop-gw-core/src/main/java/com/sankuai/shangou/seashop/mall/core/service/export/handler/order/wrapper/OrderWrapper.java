package com.sankuai.shangou.seashop.mall.core.service.export.handler.order.wrapper;

import java.util.Collections;
import java.util.List;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.base.export.writeHandler.FindMergeRegionWhenWriteCell;
import com.sankuai.shangou.seashop.mall.common.remote.order.MallOrderRemoteService;
import com.sankuai.shangou.seashop.mall.core.service.export.handler.order.eo.OrderAndItemExportEo;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryUserOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.OrderAndItemFlatListResp;
import com.sankuai.shangou.seashop.user.thrift.account.enums.InvoiceTypeEnum;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class OrderWrapper extends PageExportWrapper<OrderAndItemExportEo, QueryUserOrderReq> {

    private final MallOrderRemoteService mallOrderRemoteService = SpringUtil.getBean(MallOrderRemoteService.class);

    // 滚动查询的ID，本对象是每次导出时都会创建的新对象，每次导出都是独立的，所以设置为局部变量
    private String scrollId;
    private final static long SCROLL_TIME_VALUE_MINUTE = 5L;

    @Override
    public List<OrderAndItemExportEo> getPageList(QueryUserOrderReq param) {
        log.info("【订单】查询订单导出参数:{}", JsonUtil.toJsonString(param));
        if (StrUtil.isBlank(scrollId)) {
            param.setTimeValueMinutes(SCROLL_TIME_VALUE_MINUTE);
            OrderAndItemFlatListResp scrollResp = mallOrderRemoteService.getScrollIdForUserExport(param);
            if (scrollResp == null || StrUtil.isBlank(scrollResp.getScrollId())) {
                return null;
            }
            scrollId = scrollResp.getScrollId();
            return BeanUtil.copyToList(scrollResp.getDataList(), OrderAndItemExportEo.class);
        }
        EsScrollQueryReq scrollReq = new EsScrollQueryReq();
        scrollReq.setScrollId(scrollId);
        scrollReq.setTimeValueMinutes(SCROLL_TIME_VALUE_MINUTE);
        List<OrderAndItemInfoDto> list = mallOrderRemoteService.listOrderAndItemFlatByScroll(scrollReq);
        return JsonUtil.copyList(list, OrderAndItemExportEo.class, (from, to) -> {
            to.setElectronicInvoiceContent(buildElectronicInvoiceContent(from));
            to.setVatInvoiceContent(buildVatInvoiceContent(from));
        });
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        //return super.getWriteHandlerList();
        return Collections.singletonList(new FindMergeRegionWhenWriteCell(OrderAndItemExportEo.class, null, getDataSupplier(), getPrevNum()));
    }

    @Override
    public Integer getBatchCount() {
        return 20;
    }

    @Override
    public Integer getMaxBatch() {
        return 5000;
    }


    private String buildElectronicInvoiceContent(OrderAndItemInfoDto info) {
        if (info.getInvoiceType() == null) {
            return null;
        }
        if (InvoiceTypeEnum.ELECTRONIC.getValue() != info.getInvoiceType()) {
            return null;
        }
        return "收票人手机号：" + info.getInvoiceCellPhone() + "\n" +
            "收票人邮箱：" + info.getEmail() + "\n";
    }

    private String buildVatInvoiceContent(OrderAndItemInfoDto info) {
        if (info.getInvoiceType() == null) {
            return null;
        }
        if (InvoiceTypeEnum.VAT.getValue() != info.getInvoiceType()) {
            return null;
        }
        return "注册地址：" + info.getRegisterAddress() + "\n" +
            "注册电话：" + info.getRegisterPhone() + "\n" +
            "开户银行：" + info.getBankName() + "\n" +
            "银行帐号：" + info.getBankNo() + "\n" +
            "收票人姓名：" + info.getRealName() + "\n" +
            "收票人手机号：" + info.getInvoiceCellPhone() + "\n" +
            "收票人地址：" + info.getInvoiceFullAddress() + "\n";
    }


}
