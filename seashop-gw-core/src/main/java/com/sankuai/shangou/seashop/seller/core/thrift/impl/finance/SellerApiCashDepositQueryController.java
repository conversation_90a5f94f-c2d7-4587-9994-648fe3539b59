package com.sankuai.shangou.seashop.seller.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.service.finance.SellerApiCashDepositService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiShopIdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiCashDepositResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiCashDeposit")
public class SellerApiCashDepositQueryController {

    @Resource
    private SellerApiCashDepositService sellerApiCashDepositService;

    @PostMapping(value = "/queryOneByShopId", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiCashDepositResp> queryOneByShopId(@RequestBody ApiShopIdReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("queryOneByShopId", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            req.checkParameter();
            return sellerApiCashDepositService.queryOneByShopId(req.getShopId());
        });
    }
}
