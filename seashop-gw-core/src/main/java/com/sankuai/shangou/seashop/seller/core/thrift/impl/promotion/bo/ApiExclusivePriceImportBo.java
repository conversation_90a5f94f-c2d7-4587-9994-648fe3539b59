package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion.bo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
@ToString
@TypeDoc(description = "保存折扣活动请求对象")
public class ApiExclusivePriceImportBo {

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "文件地址")
    private String fileUrl;
}
