package com.sankuai.shangou.seashop.m.core.service.export.handler.refund.wrapper;

import java.util.List;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.core.service.export.handler.refund.eo.OrderRefundEo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.PlatformRefundDto;

/**
 * <AUTHOR>
 */
public class RefundDataWrapper extends PageExportWrapper<OrderRefundEo, PlatformQueryRefundReq> {

    private final OrderRefundQueryFeign orderRefundQueryFeign;

    public RefundDataWrapper(OrderRefundQueryFeign orderRefundQueryFeign) {
        this.orderRefundQueryFeign = orderRefundQueryFeign;
    }


    @Override
    public List<OrderRefundEo> getPageList(PlatformQueryRefundReq param) {
        List<PlatformRefundDto> list = ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.platformQueryRefundPage(param)).getData();
        return JsonUtil.copyList(list, OrderRefundEo.class, (s, t) -> {
            t.setHasAllReturnStr(s.getHasAllReturn() ? "是" : "否");
        });
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return super.getWriteHandlerList();
    }

    @Override
    public String sheetName() {
        return "订单售后";
    }
}
