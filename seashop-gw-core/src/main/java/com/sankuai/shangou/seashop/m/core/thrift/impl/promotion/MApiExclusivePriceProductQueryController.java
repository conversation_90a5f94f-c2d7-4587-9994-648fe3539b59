package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.dto.promotion.ApiExclusivePriceProductDto;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiExclusivePriceProductPageQryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductPageQryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceProductQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiExclusivePriceProduct")
public class MApiExclusivePriceProductQueryController {

    @Resource
    private ExclusivePriceProductQueryFeign exclusivePriceProductQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiExclusivePriceProductDto>> pageList(@RequestBody ApiExclusivePriceProductPageQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            ExclusivePriceProductPageQryReq exclusivePriceProductPageQryReq = JsonUtil.copy(req, ExclusivePriceProductPageQryReq.class);
            BasePageResp<ExclusivePriceProductDto> basePageResp =
                    ThriftResponseHelper.executeThriftCall(() -> exclusivePriceProductQueryFeign.pageList(exclusivePriceProductPageQryReq));
            return PageResultHelper.transfer(basePageResp, ApiExclusivePriceProductDto.class);
        });
    }
}
