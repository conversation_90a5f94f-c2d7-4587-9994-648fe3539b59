package com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiAdvanceResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.AdvanceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.AdvanceQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@RestController
@RequestMapping("/mallApi/apiAdvance")
public class MallApiAdvanceQueryController {

    @Resource
    private AdvanceQueryFeign advanceQueryFeign;

    @GetMapping(value = "/getOne")
    public ResultDto<ApiAdvanceResp> getOne() throws TException {
        return ThriftResponseHelper.responseInvoke("getOne", null, req -> {
            AdvanceResp advanceResp = ThriftResponseHelper.executeThriftCall(() -> advanceQueryFeign.getOne());
            if (null != advanceResp) {
                // 判断当时时间是否在活动时间内
                if (advanceResp.getStartTime() != null && advanceResp.getEndTime() != null) {
                    Date now = new Date();
                    if (now.before(advanceResp.getStartTime()) || now.after(advanceResp.getEndTime())) {
                        return null;
                    }
                }
            }
            return JsonUtil.copy(advanceResp, ApiAdvanceResp.class);
        });
    }
}
