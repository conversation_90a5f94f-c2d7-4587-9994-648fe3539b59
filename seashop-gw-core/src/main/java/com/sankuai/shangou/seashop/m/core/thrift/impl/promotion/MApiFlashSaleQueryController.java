package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.promotion.MFlashSaleRemoteService;
import com.sankuai.shangou.seashop.m.core.service.promotion.MApiFlashSaleService;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiFlashSaleQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiVisualFlashSaleQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiFlashSaleResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiFlashSaleSimpleResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiVisualFlashSaleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.VisualFlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.VisualFlashSaleResp;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFlashSale")
public class MApiFlashSaleQueryController {

    @Resource
    private MFlashSaleRemoteService mFlashSaleRemoteService;
    @Resource
    private MApiFlashSaleService mApiFlashSaleService;
    @Resource
    private FlashSaleQueryFeign flashSaleQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiFlashSaleSimpleResp>> pageList(@RequestBody ApiFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            BasePageResp<FlashSaleSimpleResp> flashSalePage = mFlashSaleRemoteService.pageList(JsonUtil.copy(req, FlashSaleQueryReq.class));
            return PageResultHelper.transfer(flashSalePage, ApiFlashSaleSimpleResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiFlashSaleResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            FlashSaleResp flashSaleResp = ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.getById(req));
            return JsonUtil.parseObject(JsonUtil.toJsonString(flashSaleResp), ApiFlashSaleResp.class);
        });
    }

    @PostMapping(value = "/componentPageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiVisualFlashSaleResp>> componentPageList(@RequestBody ApiVisualFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("componentPageList", request, req -> {
            BasePageResp<VisualFlashSaleResp> flashSalePage = ThriftResponseHelper.executeThriftCall(() ->
                    flashSaleQueryFeign.componentPageList(JsonUtil.copy(req, VisualFlashSaleQueryReq.class)));
            return PageResultHelper.transfer(flashSalePage, ApiVisualFlashSaleResp.class);
        });
    }

    @PostMapping(value = "/export", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> export(@RequestBody ApiFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("export", request, req -> {
            mApiFlashSaleService.export(req);
            return BaseResp.of();
        });
    }
}
