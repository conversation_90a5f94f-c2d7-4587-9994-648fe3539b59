package com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
@Getter
@Setter
public class PendSettleListEo {

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单ID")
    private String orderId;

    /**
     * 订单状态(1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中)
     */
    @ExcelProperty(value = "订单状态(1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中)")
    @ExcelIgnore
    private Integer orderStatus;

    @ExcelProperty(value = "订单状态")
    private String orderStatusStr;

    @ExcelProperty(value = "店铺ID")
    @ExcelIgnore
    private Long shopId;

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "店铺名称")
    private String shopName;

    @ExcelProperty(value = "支付方式")
    private String paymentTypeName;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 平台佣金
     */
    @ExcelProperty(value = "平台佣金")
    private BigDecimal platCommission;

    /**
     * 退款金额
     */
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 结算金额
     */
    @ExcelProperty(value = "供应商结算金额")
    private BigDecimal settlementAmount;

    /**
     * 渠道手续费
     */
    @ExcelProperty(value = "结算手续费")
    private BigDecimal channelAmount;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @ExcelIgnore
    private Date createTime;

    /**
     * 支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银
     */
    @ExcelProperty(value = "支付方式")
    @ExcelIgnore
    private Integer paymentType;

    @ExcelProperty(value = "订单付款时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ExcelProperty(value = "订单完成时间")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date orderFinishTime;

}
