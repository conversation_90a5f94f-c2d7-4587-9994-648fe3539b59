package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiPageMCollocationReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiCollocationResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiPageCollocationResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CollocationDetailReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CollocationResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.PageMCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 15:11
 */
@RestController
@RequestMapping("/mApi/apiCollocation")
public class MApiCollocationQueryController {

    @Resource
    private CollocationQueryFeign collocationQueryFeign;

    @PostMapping(value = "/pageMCollocation", consumes = "application/json")
    public ResultDto<BasePageResp<ApiPageCollocationResp>> pageMCollocation(@RequestBody ApiPageMCollocationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageMCollocation", request, req -> {
            req.checkParameter();
            PageMCollocationReq bean = JsonUtil.copy(req, PageMCollocationReq.class);
            return PageResultHelper.transfer(ThriftResponseHelper.executeThriftCall(() -> collocationQueryFeign.pageMCollocation(bean)),
                    ApiPageCollocationResp.class);
        });
    }

    @GetMapping(value = "/queryCollocationDetail")
    public ResultDto<ApiCollocationResp> queryCollocationDetail(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCollocationDetail", id, req -> {
            AssertUtil.throwIfNull(req, "id不能为空");
            CollocationDetailReq collocationDetailReq = new CollocationDetailReq();
            collocationDetailReq.setId(id);
            collocationDetailReq.setSourceFrom(1);

            CollocationResp collocationResp = ThriftResponseHelper.executeThriftCall(() -> collocationQueryFeign.queryCollocationDetail(collocationDetailReq));


            return JsonUtil.copy(collocationResp,ApiCollocationResp.class);
        });
    }
}
