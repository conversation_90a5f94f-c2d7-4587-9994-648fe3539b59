package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundSellerQueryTabEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundLogListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDto;
import com.sankuai.shangou.seashop.seller.core.service.order.SellerOrderRefundService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiSellerQueryRefundReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.refund.ApiSellerQueryRefundDetailReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.refund.ApiRefundLogResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.refund.ApiRefundUserDeliverExpressResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.refund.ApiSellerRefundDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.refund.ApiSellerRefundDto;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sellerApi/apiOrderRefund")
@Slf4j
public class SellerApiOrderRefundQueryController {

    @Resource
    private SellerOrderRefundService sellerOrderRefundService;

    private final static String[] IGNORE_PROPERTIES = {"tab", "refundStatus"};


    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/queryDetail", consumes = "application/json")
    public ResultDto<ApiSellerRefundDetailResp> queryDetail(@RequestBody ApiSellerQueryRefundDetailReq queryReq) throws TException {

        

        return ThriftResponseHelper.responseInvoke("【订单】供应商查询售后明细", queryReq, func -> {
            // 上下文获取并区分用户与店铺
            LoginShopDto loginShop = TracerUtil.getShopDto();
            SellerQueryRefundDetailReq req = JsonUtil.copy(func, SellerQueryRefundDetailReq.class);
            ShopDto shop = new ShopDto();
            shop.setShopId(loginShop.getShopId());
            req.setShop(shop);
            SellerRefundDetailResp sellerRefundDetailResp = sellerOrderRefundService.queryDetail(req);
            return JsonUtil.copy(sellerRefundDetailResp, ApiSellerRefundDetailResp.class);
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/queryRefundLog", consumes = "application/json")
    public ResultDto<List<ApiRefundLogResp>> queryRefundLog(@RequestBody BaseIdReq queryReq) throws TException {

        

        return ThriftResponseHelper.responseInvoke("【订单】供应商查询售后日志", queryReq, func -> {
            RefundLogListResp resp = sellerOrderRefundService.queryRefundLog(func);
            if (!TracerUtil.getShopDto().getShopId().equals(resp.getShopId())) {
                throw new BusinessException("只能查看自己店铺的数据");
            }
            return JsonUtil.copyList(resp.getLogList(), ApiRefundLogResp.class);
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/sellerQueryRefundPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiSellerRefundDto>> sellerQueryRefundPage(@RequestBody ApiSellerQueryRefundReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】供应商查询售后列表", queryReq, func -> {
            SellerQueryRefundReq req = JsonUtil.copy(queryReq, SellerQueryRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setRefundStatus(queryReq.getRefundStatus());
            req.setTab(RefundSellerQueryTabEnum.valueOf(queryReq.getTab()));
            // 上下文获取并区分用户与店铺
            LoginShopDto loginShop = TracerUtil.getShopDto();
            ShopDto shop = new ShopDto();
            shop.setShopId(loginShop.getShopId());
            req.setShop(shop);

            UserDto user = new UserDto();
            user.setUserId(loginShop.getManagerId());
            user.setUserName(loginShop.getName());
            user.setUserPhone(loginShop.getUserPhone());
            req.setUser(user);
            BasePageResp<SellerRefundDto> sellerRefundDtoBasePageResp = sellerOrderRefundService.sellerQueryRefundPage(req);
            return PageResultHelper.transfer(sellerRefundDtoBasePageResp, ApiSellerRefundDto.class);
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/exportSellerRefundList", consumes = "application/json")
    public ResultDto<BaseResp> exportSellerRefundList(@RequestBody ApiSellerQueryRefundReq queryReq) throws TException {

        

        return ThriftResponseHelper.responseInvoke("【售后】供应商导出售后记录", queryReq, func -> {
            SellerQueryRefundReq req = JsonUtil.copy(queryReq, SellerQueryRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setTab(RefundSellerQueryTabEnum.valueOf(queryReq.getTab()));
            // 上下文获取并区分用户与店铺
            LoginShopDto loginShop = TracerUtil.getShopDto();
            ShopDto shop = new ShopDto();
            shop.setShopId(loginShop.getShopId());
            req.setShop(shop);

            UserDto user = new UserDto();
            user.setUserId(loginShop.getManagerId());
            user.setUserName(loginShop.getManagerName());
            req.setUser(user);

            sellerOrderRefundService.export(req);
            return BaseResp.of();
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/queryUserDeliverExpress", consumes = "application/json")
    public ResultDto<ApiRefundUserDeliverExpressResp> queryUserDeliverExpress(@RequestBody BaseIdReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】供应商查询寄货物流信息", queryReq, func -> {
            // 业务逻辑处理
            return JsonUtil.copy(sellerOrderRefundService.queryUserDeliverExpress(func), ApiRefundUserDeliverExpressResp.class);
        });
    }
}
