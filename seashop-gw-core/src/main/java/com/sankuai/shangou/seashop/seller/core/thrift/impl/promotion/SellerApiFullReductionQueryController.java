package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryFullReductionDetailReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionSimpleResp;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerFullReductionRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiFullReductionQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiFullReductionListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiFullReductionResp;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiFullReduction")
public class SellerApiFullReductionQueryController {

    @Resource
    private SellerFullReductionRemoteService sellerFullReductionRemoteService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiFullReductionListResp>> pageList(@RequestBody ApiFullReductionQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            FullReductionQueryReq fullReductionQueryReq = JsonUtil.copy(req, FullReductionQueryReq.class);
            BasePageResp<FullReductionSimpleResp> listRespBasePageResp = sellerFullReductionRemoteService.pageList(fullReductionQueryReq);
            return PageResultHelper.transfer(listRespBasePageResp, ApiFullReductionListResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiFullReductionResp> getById(@RequestBody BaseIdReq request) throws TException {
        
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();

            QueryFullReductionDetailReq remoteReq = new QueryFullReductionDetailReq();
            remoteReq.setId(req.getId());
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            FullReductionResp fullReductionResp = sellerFullReductionRemoteService.getById(remoteReq);
            return JsonUtil.copy(fullReductionResp, ApiFullReductionResp.class);
        });
    }
}
