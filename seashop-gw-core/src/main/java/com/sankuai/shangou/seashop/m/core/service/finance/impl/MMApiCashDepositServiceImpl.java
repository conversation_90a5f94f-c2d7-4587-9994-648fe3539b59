package com.sankuai.shangou.seashop.m.core.service.finance.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.user.MShopRemoteService;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiCashDepositService;
import com.sankuai.shangou.seashop.m.core.service.finance.model.ApiShopSimpleModel;
import com.sankuai.shangou.seashop.m.thrift.core.enums.finance.ApiCashDepositStatusEnum;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiCashDepositQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositSimpleResp;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdListReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@Service
@Slf4j
public class MMApiCashDepositServiceImpl implements MApiCashDepositService {

    @Resource
    private MShopRemoteService mShopRemoteService;
    @Resource
    private CashDepositQueryFeign cashDepositQueryFeign;
    @Resource
    private ShopQueryFeign shopQueryFeign;

    /**
     * 第一步：
     * 1、如果带了条件，则先根据条件查询出店铺信息，然后根据店铺id查询保证金信息
     * 2、如果没有带条件，则直接查询保证金信息，然后将单页的店铺id拿出来，去查询店铺信息
     * （user和order）
     * <p>
     * 第二步：
     * 1、拿到当前页的店铺id，去查询店铺需要缴纳的最大保证金
     * （user）
     */
    @Override
    public BasePageResp<ApiCashDepositSimpleResp> queryListByParam(ApiCashDepositQueryReq request) {

        List<ApiShopSimpleModel> shopSimpleModels = new ArrayList<>();

        ShopSimpleQueryReq shopSimpleQueryReq = new ShopSimpleQueryReq();
        if (request.getShopId() != null || StrUtil.isNotBlank(request.getShopName())) {
            if (request.getShopId() != null) {
                shopSimpleQueryReq.setShopIdList(Arrays.asList(request.getShopId()));
            }
            if (StrUtil.isNotBlank(request.getShopName())) {
                shopSimpleQueryReq.setShopName(request.getShopName());
            }
            shopSimpleQueryReq.setQueryCashDeposit(Boolean.TRUE);
            ShopSimpleListResp shopSimpleListResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(shopSimpleQueryReq));
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            shopSimpleModels = JsonUtil.copyList(shopSimpleListResp.getList(), ApiShopSimpleModel.class);
        }

        List<Long> shopIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(shopSimpleModels)) {
            // 拿到过滤后的店铺id
            shopIds = shopSimpleModels.stream().map(ApiShopSimpleModel::getId).collect(Collectors.toList());
        }
        // 如果没数据，直接返回
        ShopIdListReq shopIdListReq = new ShopIdListReq();
        if (CollUtil.isNotEmpty(shopIds)) {
            shopIdListReq.setShopIdList(shopIds);
        }
        shopIdListReq.setSortList(request.getSortList());
        shopIdListReq.setPageNo(request.getPageNo());
        shopIdListReq.setPageSize(request.getPageSize());
        BasePageResp<CashDepositResp> cashDepositPage = ThriftResponseHelper.executeThriftCall(() ->
                cashDepositQueryFeign.queryListByShopId(shopIdListReq));
        if (null == cashDepositPage || CollUtil.isEmpty(cashDepositPage.getData())) {
            return PageResultHelper.defaultEmpty(request.buildPage());
        }

        List<CashDepositResp> cashDepositList = cashDepositPage.getData();
        if (CollUtil.isEmpty(shopSimpleModels)) {
            // 没数据说明之前没查过店铺信息，需要去查一下店铺信息
            shopIds = cashDepositList.stream().map(CashDepositResp::getShopId).collect(Collectors.toList());
            shopSimpleQueryReq.setShopIdList(shopIds);
            shopSimpleQueryReq.setQueryCashDeposit(Boolean.TRUE);
            ShopSimpleListResp shopSimpleListResp = mShopRemoteService.querySimpleList(shopSimpleQueryReq);
            shopSimpleModels = JsonUtil.copyList(shopSimpleListResp.getList(), ApiShopSimpleModel.class);
        }

        final List<ApiShopSimpleModel> shopSimpleModelsFinal = shopSimpleModels;
        BasePageResp<ApiCashDepositSimpleResp> result = PageResultHelper.transfer(cashDepositPage, ApiCashDepositSimpleResp.class,
            (cashDepositResp, apiCashDepositSimpleResp) -> {
                BigDecimal maxCashDeposit = BigDecimal.ZERO;
                if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                    ApiShopSimpleModel apiShopSimpleModel = shopSimpleModelsFinal.stream().filter(shopSimpleModel -> shopSimpleModel.getId().equals(apiCashDepositSimpleResp.getShopId())).findFirst().orElse(null);
                    if (null != apiShopSimpleModel) {
                        apiCashDepositSimpleResp.setShopName(apiShopSimpleModel.getShopName());
                        // 需交最大保证金
                        maxCashDeposit = apiShopSimpleModel.getMaxCashDeposit();
                    }
                }
                // 可用余额
                BigDecimal currentBalance = cashDepositResp.getCurrentBalance() == null ? BigDecimal.ZERO : cashDepositResp.getCurrentBalance();
                apiCashDepositSimpleResp.setArrearsBalance(BigDecimal.ZERO);
                // 如果可用余额小于最大保证金，说明欠费
                if (currentBalance.compareTo(maxCashDeposit) < 0) {
                    apiCashDepositSimpleResp.setArrearsBalance(maxCashDeposit);
                    apiCashDepositSimpleResp.setStatus(ApiCashDepositStatusEnum.ARREARS.getCode());
                }
                else {
                    apiCashDepositSimpleResp.setStatus(ApiCashDepositStatusEnum.NORMAL.getCode());
                }
            }
        );
        if (CollectionUtils.isEmpty(request.getSortList())) {
            result.getData().sort(Comparator.comparing(ApiCashDepositSimpleResp::getArrearsBalance).reversed());
        }
        else {
            for (FieldSortReq fieldSortReq : request.getSortList()) {
                String sort = fieldSortReq.getSort();
                if ("arrearsBalanceString".equals(sort)) {
                    if (fieldSortReq.getIzAsc()) {
                        result.getData().sort(Comparator.comparing(ApiCashDepositSimpleResp::getArrearsBalance));
                    }
                    else {
                        result.getData().sort(Comparator.comparing(ApiCashDepositSimpleResp::getArrearsBalance).reversed());
                    }
                }
            }
        }
        return result;
    }
}
