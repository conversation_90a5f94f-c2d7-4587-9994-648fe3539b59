package com.sankuai.shangou.seashop.mall.core.service.export.handler.order.eo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.export.anno.RowKey;
import com.sankuai.shangou.seashop.base.export.anno.ShouldMerge;

import lombok.Getter;
import lombok.Setter;

/**
 * 承载订单导出的数据模型
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderAndItemExportEo {

    @ExcelProperty("订单来源")
    @ShouldMerge
    private String platformDesc;
    @ExcelProperty("订单编号")
    @ShouldMerge
    @RowKey
    private String orderId;
    @ExcelProperty("下单时间")
    @ShouldMerge
    private String orderDateStr;
    @ExcelProperty("付款时间")
    @ShouldMerge
    private String payDateStr;
    @ExcelProperty("完成时间")
    @ShouldMerge
    private String finishDateStr;
    @ExcelProperty("订单类型")
    @ShouldMerge
    private String orderTypeDesc;
//    @ExcelProperty("店铺")
//    @ShouldMerge
//    private String shopName;
    @ExcelProperty("支付方式")
    @ShouldMerge
    private String payMethodDesc;
    @ExcelProperty("交易单号")
    @ShouldMerge
    private String gatewayOrderId;
    @ExcelProperty("商品总额")
    @ShouldMerge
    private BigDecimal productTotalAmount;
    @ExcelProperty("运费")
    @ShouldMerge
    private BigDecimal freight;
    @ExcelProperty("税金")
    @ShouldMerge
    private BigDecimal tax;
    @ExcelProperty("优惠券抵扣")
    @ShouldMerge
    private BigDecimal couponAmount;
    @ExcelProperty("折扣抵扣")
    @ShouldMerge
    private BigDecimal discountAmount;
    @ExcelProperty("满额减")
    @ShouldMerge
    private BigDecimal moneyOffAmount;
    @ExcelProperty("供应商改价")
    @ShouldMerge
    private BigDecimal updateAmount;
    @ExcelProperty("订单实付总额")
    @ShouldMerge
    private BigDecimal totalAmount;
    @ExcelProperty("订单状态")
    @ShouldMerge
    private String orderStatusDesc;
    @ExcelProperty("买家留言")
    @ShouldMerge
    private String userRemark;
    @ExcelProperty("收货人")
    @ShouldMerge
    private String shipTo;
    @ExcelProperty("手机号码")
    @ShouldMerge
    private String cellPhone;
    @ExcelProperty("收货地址")
    @ShouldMerge
    private String address;
    @ExcelProperty("商品ID")
    private String productId;
    @ExcelProperty("商品名称")
    private String productName;
    @ExcelProperty("单价")
    private BigDecimal salePrice;
    @ExcelProperty("数量")
    private Long quantity;
    @ExcelProperty("一级分类")
    private String cateLevel1Name;
    @ExcelProperty("二级分类")
    private String cateLevel2Name;
    @ExcelProperty("三级分类")
    private String cateLevel3Name;
    @ExcelProperty("规格ID")
    private Long skuAutoId;
    @ExcelProperty("SKU货号")
    private String skuCode;

    @ExcelProperty("发票类型")
    @ShouldMerge
    private String invoiceTypeDesc;
    @ExcelProperty("发票抬头")
    @ShouldMerge
    private String invoiceTitle;
    @ExcelProperty("税号")
    @ShouldMerge
    private String invoiceCode;
    @ExcelProperty("发票内容")
    @ShouldMerge
    private String invoiceContext;

    @ExcelProperty("电子普通发票信息")
    @ShouldMerge
    private String electronicInvoiceContent;
    @ExcelProperty("增值税发票信息")
    @ShouldMerge
    private String vatInvoiceContent;

    /*@ExcelProperty("注册地址")
    private String registerAddress;
    @ExcelProperty("注册电话")
    private String registerPhone;
    @ExcelProperty("开户银行")
    private String bankName;
    @ExcelProperty("银行帐号")
    private String bankNo;
    @ExcelProperty("收票人姓名")
    private String realName;
    @ExcelProperty("收票人手机号")
    private String invoiceCellPhone;
    @ExcelProperty("收票人邮箱")
    private String email;
    @ExcelProperty("收票人地址地址")
    private String invoiceFullAddress;*/
}
