package com.sankuai.shangou.seashop.seller.core.service.export;


import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.base.thrift.core.dto.SellerTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.seller.core.service.export.model.ExportTaskBo;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
public interface SellerExportTaskBiz {

    /**
     * 创建导出任务
     *
     * <AUTHOR>
     * @date 2023/4/18
     */
    ExportTaskBo create(CreateExportTaskBo createPO);

    /**
     * 执行导出任务
     *
     * <AUTHOR>
     * @date 2023/4/18
     */
    void execute(ExportTask task);

    /**
     * 分页获取导出任务
     *
     * <AUTHOR>
     * @date 2023/4/18
     */
    BasePageResp<SellerTaskDto> pageList(QueryTaskReq pageReq);

    /**
     * 检查并重做导出任务
     */
    void checkAndRedoIfNecessary();

}
