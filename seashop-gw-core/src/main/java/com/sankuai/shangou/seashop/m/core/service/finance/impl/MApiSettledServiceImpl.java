package com.sankuai.shangou.seashop.m.core.service.finance.impl;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.thrift.finance.SettledQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.*;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.common.remote.MFinanceRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiSettledService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledItemCountQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledItemQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledItemCountResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledItemResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettlementDetailResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ManagerUserInfo;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemCountResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class MApiSettledServiceImpl implements MApiSettledService {

    @Resource
    private MFinanceRemoteService mFinanceRemoteService;
    @Resource
    private MExportTaskBiz exportTaskBiz;
    @Resource
    private SettledQueryFeign settledQueryFeign;

    @Override
    public BasePageResp<ApiSettledResp> pageList(ApiSettledQryReq request) {
        SettledQryReq req = JsonUtil.copy(request, SettledQryReq.class);
        req.checkParameter();
        BasePageResp<SettledResp> settledPage = ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.pageList(req));
        if (null == settledPage || CollUtil.isEmpty(settledPage.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }
        return PageResultHelper.transfer(settledPage, ApiSettledResp.class);
    }

    @Override
    public BasePageResp<ApiSettledItemResp> itemPageList(ApiSettledItemQryReq request) {
        BasePageResp<SettledItemResp> settledItemPage = mFinanceRemoteService.settledItemPageList(JsonUtil.copy(request, SettledItemQryReq.class));
        if (null == settledItemPage || CollUtil.isEmpty(settledItemPage.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }
        return PageResultHelper.transfer(settledItemPage, ApiSettledItemResp.class);
    }

    @Override
    public ApiSettledItemCountResp itemCount(ApiSettledItemCountQryReq request) {
        SettledItemCountQryReq req = JsonUtil.copy(request, SettledItemCountQryReq.class);
        req.checkParameter();
        SettledItemCountResp settledItemCountResp = ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.itemCount(req));
        return JsonUtil.copy(settledItemCountResp, ApiSettledItemCountResp.class);
    }

    @Override
    public ApiSettlementDetailResp getDetailByOrderId(ApiOrderIdQryReq request) {
        OrderIdQryReq req = new OrderIdQryReq();
        req.setOrderId(request.getOrderId());
        req.checkParameter();
        SettlementDetailResp settlementDetailResp =  ThriftResponseHelper.executeThriftCall(() ->
                settledQueryFeign.getDetailByOrderId(req));
        if (null != settlementDetailResp) {
            return JsonUtil.copy(settlementDetailResp, ApiSettlementDetailResp.class);
        }
        return null;
    }

    @Override
    public void exportSettledItemList(ApiSettledItemQryReq request, ManagerUserInfo managerInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.SETTLED_ITEM_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(managerInfo.getLoginDto().getId());
        createExportTaskBo.setOperatorName(managerInfo.getLoginDto().getName());
        exportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public void exportSettledByShop(ApiSettledQryReq request, ManagerUserInfo managerInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.SETTLED_BY_SHOP_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(managerInfo.getLoginDto().getId());
        createExportTaskBo.setOperatorName(managerInfo.getLoginDto().getName());
        exportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public void exportSettledByOrder(ApiSettledQryReq request, ManagerUserInfo managerInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.SETTLED_BY_ORDER_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(managerInfo.getLoginDto().getId());
        createExportTaskBo.setOperatorName(managerInfo.getLoginDto().getName());
        exportTaskBiz.create(createExportTaskBo);
    }
}
