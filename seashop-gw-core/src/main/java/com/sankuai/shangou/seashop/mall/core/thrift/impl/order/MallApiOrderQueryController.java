package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiQueryOrderDetailReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiQueryUserOrderReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiQueryUserOrderWayBillReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiOrderDetailResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiOrderOperationLogResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiOrderWayBillResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.OrderExportResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.dto.ApiOrderInfoDto;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.mall.core.service.order.order.MallOrderService;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderOperationLogResp;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryUserOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryUserOrderWayBillReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderWayBillResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiOrder")
@Slf4j
public class MallApiOrderQueryController {

    @Resource
    private MallOrderService mallOrderService;
    @Resource
    private OrderQueryFeign orderQueryFeign;

    @NeedLogin
    @PostMapping(value = "/pcPageQueryUserOrder", consumes = "application/json")
    public ResultDto<BasePageResp<ApiOrderInfoDto>> pcPageQueryUserOrder(@RequestBody ApiQueryUserOrderReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【订单】PC端商家订单分页列表", queryReq, func -> {
            QueryUserOrderReq req = JsonUtil.copy(queryReq, QueryUserOrderReq.class);
            req.setUserId(TracerUtil.getMemberDto().getId());
            req.setQueryFrom(OrderQueryFromEnum.SHOP_PC);
            BasePageResp<OrderInfoDto> resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.pageQueryUserOrder(req));
            return PageResultHelper.transfer(resp, ApiOrderInfoDto.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/wxPageQueryUserOrder", consumes = "application/json")
    public ResultDto<BasePageResp<ApiOrderInfoDto>> wxPageQueryUserOrder(@RequestBody ApiQueryUserOrderReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【订单】微信小程序商家订单分页列表", queryReq, func -> {
            QueryUserOrderReq req = JsonUtil.copy(queryReq, QueryUserOrderReq.class);
            req.setUserId(TracerUtil.getMemberDto().getId());
            req.setQueryFrom(OrderQueryFromEnum.SHOP_MINI_PROGRAM);
            BasePageResp<OrderInfoDto> resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.pageQueryUserOrder(req));
            return PageResultHelper.transfer(resp, ApiOrderInfoDto.class);
        });
    }

    /**
     * 微信小程序商家可售后订单分页列表
     * TODO 需要再确定细节逻辑 @李国强
     *
     * @param queryReq
     * @return
     * @throws TException
     */
    @NeedLogin
    @PostMapping(value = "/wxPageQueryCanAfterSaleOrder", consumes = "application/json")
    public ResultDto<BasePageResp<ApiOrderInfoDto>> wxPageQueryCanAfterSaleOrder(@RequestBody ApiQueryUserOrderReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】微信小程序商家可售后订单分页列表", queryReq, func -> {
            QueryUserOrderReq req = JsonUtil.copy(queryReq, QueryUserOrderReq.class);
            req.setUserId(TracerUtil.getMemberDto().getId());
            req.setQueryFrom(OrderQueryFromEnum.SHOP_MINI_PROGRAM);
            req.setOrderStatusList(CollUtil.newArrayList(OrderStatusEnum.UNDER_SEND.getCode(), OrderStatusEnum.UNDER_RECEIVE.getCode(), OrderStatusEnum.FINISHED.getCode()));
            BasePageResp<OrderInfoDto> resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.pageQueryUserOrder(req));
            return PageResultHelper.transfer(resp, ApiOrderInfoDto.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/queryDetail", consumes = "application/json")
    public ResultDto<ApiOrderDetailResp> queryDetail(@RequestBody ApiQueryOrderDetailReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【订单】查询订单详情", queryReq, func -> {
            QueryOrderDetailReq req = JsonUtil.copy(queryReq, QueryOrderDetailReq.class);
            req.setQueryFrom(OrderQueryFromEnum.SHOP_PC);
            OrderDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryDetail(req));
            if (!resp.getOrderInfo().getUserId().equals(TracerUtil.getMemberDto().getId())) {
                throw new BusinessException("只能查询自己的订单");
            }
            return JsonUtil.copy(resp, ApiOrderDetailResp.class);
        });
    }

    @NeedLogin
    @GetMapping(value = "/queryOrderLog")
    public ResultDto<List<ApiOrderOperationLogResp>> queryOrderLog(@RequestParam String orderId) {
        return ThriftResponseHelper.responseInvoke("【订单】查询订单操作日志", orderId, func -> {
            // 业务逻辑处理
            List<OrderOperationLogResp> resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryOrderLog(orderId));
            return JsonUtil.copyList(resp, ApiOrderOperationLogResp.class);
        });
    }

    /**
     * 导出单独提供接口，因为按订单维度导出再内存平铺的话，可能一个订单的商品很多，导致批次处理的数据太多
     *
     * @param queryReq
     * <AUTHOR>
     */
    @NeedLogin
    @PostMapping(value = "/exportForUser", consumes = "application/json")
    public ResultDto<OrderExportResp> exportForUser(@RequestBody ApiQueryUserOrderReq queryReq) throws TException {
        log.info("【订单】买家-导出订单分页列表, 请求参数={}", JsonUtil.toJsonString(queryReq));
//        LoginMemberDto loginMember = TracerUtil.getMemberDto();

        QueryUserOrderReq req = JsonUtil.copy(queryReq, QueryUserOrderReq.class);
        req.setUserId(TracerUtil.getMemberDto().getId());
        //req.setUserId(10667L);
        OrderExportResp resp = mallOrderService.exportOrder(req);
        return ResultDto.newWithData(resp);
    }

    @NeedLogin
    @PostMapping(value = "/getUserOrderWayBill", consumes = "application/json")
    public ResultDto<ApiOrderWayBillResp> getUserOrderWayBill(@RequestBody ApiQueryUserOrderWayBillReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】获取用户订单物流信息", queryReq, func -> {
            // 业务逻辑处理
            queryReq.setUserId(TracerUtil.getMemberDto().getId());
            OrderWayBillResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    orderQueryFeign.getUserOrderWayBill(JsonUtil.copy(queryReq, QueryUserOrderWayBillReq.class)));
            return JsonUtil.copy(resp, ApiOrderWayBillResp.class);
        });
    }

}
