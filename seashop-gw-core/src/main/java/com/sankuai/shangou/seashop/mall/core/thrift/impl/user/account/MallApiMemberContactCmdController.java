package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiCmdBindContactReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiCmdCheckCodeReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiCmdSendCodeReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiImageVerificationCodeResp;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.mall.core.service.user.account.MallMemberService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mallApi/apiMemberContact")
public class MallApiMemberContactCmdController {
    @Resource
    private MallMemberService mallMemberService;

    @PostMapping(value = "/sendCode", consumes = "application/json")
    public ResultDto<BaseResp> sendCode(@RequestBody ApiCmdSendCodeReq apiCmdSendCodeReq) {
        return ThriftResponseHelper.responseInvoke("sendCode", apiCmdSendCodeReq, req -> mallMemberService.sendCode(apiCmdSendCodeReq));
    }

    @PostMapping(value = "/checkCode", consumes = "application/json")
    public ResultDto<String> checkCode(@RequestBody ApiCmdCheckCodeReq apiCmdCheckCodeReq) {
        return ThriftResponseHelper.responseInvoke("checkCode", apiCmdCheckCodeReq, req -> mallMemberService.checkCode(apiCmdCheckCodeReq));
    }

    @PostMapping(value = "/bindContact", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> bindContact(@RequestBody ApiCmdBindContactReq apiCheckCodeCmdReq) {
        

        return ThriftResponseHelper.responseInvoke("bindContact", apiCheckCodeCmdReq, req -> mallMemberService.bindContact(req, TracerUtil.getMemberDto()));
    }

    @GetMapping(value = "/imageVerificationCode")
    public ResultDto<ApiImageVerificationCodeResp> imageVerificationCode() {
        return ThriftResponseHelper.responseInvoke("imageVerificationCode", null, req -> mallMemberService.imageVerificationCode());
    }

    @GetMapping(value = "/getOpenId")
    @NeedLogin(force = false)
    public ResultDto<String> getOpenId(@RequestParam String code) {
        

        return ThriftResponseHelper.responseInvoke("getOpenId", code, req -> mallMemberService.getOpenId(code, TracerUtil.getMemberDto()));
    }

}
