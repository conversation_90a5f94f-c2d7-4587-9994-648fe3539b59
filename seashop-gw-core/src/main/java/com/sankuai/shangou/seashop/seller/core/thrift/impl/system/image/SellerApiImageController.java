package com.sankuai.shangou.seashop.seller.core.thrift.impl.system.image;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ImageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.ImageQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BasePhotoSpaceCategoryRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BasePhotoSpaceRes;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.*;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiBasePhotoSpaceCategoryRes;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiBasePhotoSpaceRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/sellerApi/apiImage")
public class SellerApiImageController {

    @Resource
    private ImageQueryFeign imageQueryFeign;
    @Resource
    private ImageCMDFeign imageCMDFeign;


    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/query", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBasePhotoSpaceRes>> query(@RequestBody ApiBasePhotoSpaceQueryReq query) throws TException {
        
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<BasePhotoSpaceRes> result = ThriftResponseHelper.executeThriftCall(() ->
                    imageQueryFeign.query(JsonUtil.copy(req, BasePhotoSpaceQueryReq.class)));
            return PageResultHelper.transfer(result, ApiBasePhotoSpaceRes.class);
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/queryAudit", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBasePhotoSpaceRes>> queryAudit(@RequestBody ApiBasePhotoSpaceQueryReq query) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryAudit", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<BasePhotoSpaceRes> result = ThriftResponseHelper.executeThriftCall(() ->
                    imageQueryFeign.queryAudit(JsonUtil.copy(req, BasePhotoSpaceQueryReq.class)));
            return PageResultHelper.transfer(result, ApiBasePhotoSpaceRes.class);
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/create", consumes = "application/json")
    public ResultDto<Long> create(@RequestBody ApiBasePhotoSpaceReq query) throws TException {
        query.checkParameter();
        
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Long imageId = ThriftResponseHelper.executeThriftCall(() ->
                    imageCMDFeign.create(JsonUtil.copy(req, BasePhotoSpaceReq.class)));
            return imageId;
        });
    }

    @PostMapping(value = "/batchCreate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> batchCreate(@RequestBody List<ApiBasePhotoSpaceReq> query) throws TException {
        
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            for (ApiBasePhotoSpaceReq photoSpaceReq : query) {
                photoSpaceReq.checkParameter();
                photoSpaceReq.setShopId(TracerUtil.getShopDto().getShopId());
            }
            ThriftResponseHelper.executeThriftCall(() -> imageCMDFeign.batchCreate(JsonUtil.copyList(query, BasePhotoSpaceReq.class)));
            return true;
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/update", consumes = "application/json")
    public ResultDto<Boolean> update(@RequestBody ApiUpdatePhotoSpaceReq query) throws TException {
        query.checkParameter();
        
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() -> imageCMDFeign.update(JsonUtil.copy(req, UpdatePhotoSpaceReq.class)));
            return result;
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/delete", consumes = "application/json")
    public ResultDto<Boolean> delete(@RequestBody ApiBasePhotoSpaceReq query) throws TException {
        
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() -> imageCMDFeign.delete(JsonUtil.copy(req, BasePhotoSpaceReq.class)));
            return result;
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/moveImageByCateId", consumes = "application/json")
    public ResultDto<Boolean> moveImageByCateId(@RequestBody ApiBaseMoveCateImageReq query) throws TException {
        query.checkParameter();
        
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    imageCMDFeign.moveImageByCateId(JsonUtil.copy(req, BaseMoveCateImageReq.class)));
            return result;
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/moveImageById", consumes = "application/json")
    public ResultDto<Boolean> moveImageById(@RequestBody ApiBaseMoveImageReq query) throws TException {
        query.checkParameter();
        
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    imageCMDFeign.moveImageById(JsonUtil.copy(req, BaseMoveImageReq.class)));
            return result;
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/deleteImageByIds", consumes = "application/json")
    public ResultDto<Boolean> deleteImageByIds(@RequestBody ApiBaseIdsReq idsReq) throws TException {
        idsReq.checkParameter();
        
        return ThriftResponseHelper.responseInvoke("query", idsReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    imageCMDFeign.deleteImageByIds(JsonUtil.copy(req, BaseIdsReq.class)));
            return result;
        });
    }

    @PostMapping(value = "/createCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> createCategory(@RequestBody ApiBasePhotoSpaceCategoryReq categoryReq) throws TException {
        categoryReq.checkParameter();
        
        return ThriftResponseHelper.responseInvoke("createCategory", categoryReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Long result = ThriftResponseHelper.executeThriftCall(() ->
                    imageCMDFeign.createCategory(JsonUtil.copy(req, BasePhotoSpaceCategoryReq.class)));
            return result;
        });
    }

    @PostMapping(value = "/updateCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> updateCategory(@RequestBody ApiBasePhotoSpaceCategoryReq categoryReq) throws TException {
        categoryReq.checkParameter();
        
        return ThriftResponseHelper.responseInvoke("updateCategory", categoryReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    imageCMDFeign.updateCategory(JsonUtil.copy(req, BasePhotoSpaceCategoryReq.class)));
            return result;
        });
    }

    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> deleteCategory(@RequestBody ApiBaseIdsReq categoryReq) throws TException {
        categoryReq.checkParameter();
        
        return ThriftResponseHelper.responseInvoke("deleteCategory", categoryReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    imageCMDFeign.deleteCategory(JsonUtil.copy(req, BaseIdsReq.class)));
            return result;
        });
    }

    @GetMapping(value = "/queryCategorys")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<List<ApiBasePhotoSpaceCategoryRes>> queryCategorys() throws TException {
        

        return ThriftResponseHelper.responseInvoke("queryCategorys", null, req -> {
            List<BasePhotoSpaceCategoryRes> result = ThriftResponseHelper.executeThriftCall(() ->
                    imageQueryFeign.queryCategorys(TracerUtil.getShopDto().getShopId()));
            return JsonUtil.copyList(result, ApiBasePhotoSpaceCategoryRes.class);
        });
    }
}
