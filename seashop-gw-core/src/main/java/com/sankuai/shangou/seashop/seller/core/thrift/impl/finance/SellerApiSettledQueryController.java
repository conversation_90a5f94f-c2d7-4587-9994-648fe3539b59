package com.sankuai.shangou.seashop.seller.core.thrift.impl.finance;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.service.finance.SellerApiSettledService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledItemQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettledItemResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettledResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettlementDetailResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiSettled")
public class SellerApiSettledQueryController {

    @Resource
    private SellerApiSettledService sellerApiSettledService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiSettledResp>> pageList(@RequestBody ApiSettledQryReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            req.valueInit();
            return sellerApiSettledService.pageList(req);
        });
    }

    @PostMapping(value = "/itemPageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiSettledItemResp>> itemPageList(@RequestBody ApiSettledItemQryReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("itemPageList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            req.checkParameter();
            return sellerApiSettledService.itemPageList(req);
        });
    }

    @PostMapping(value = "/getDetailByOrderId", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiSettlementDetailResp> getDetailByOrderId(@RequestBody ApiOrderIdQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getDetailByOrderId", request, req -> {
            req.checkParameter();
            return sellerApiSettledService.getDetailByOrderId(req);
        });
    }

    @PostMapping(value = "/exportSettledList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> exportSettledList(@RequestBody ApiSettledQryReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("exportPendSettleList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            req.valueInit();
            sellerApiSettledService.exportSettledList(req, loginShopDto);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/exportSettledItemList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> exportSettledItemList(@RequestBody ApiSettledItemQryReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("exportSettledItemList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            req.checkParameter();
            sellerApiSettledService.exportSettledItemList(req, loginShopDto);
            return BaseResp.of();
        });
    }
}
