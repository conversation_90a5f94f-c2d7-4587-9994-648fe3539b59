package com.sankuai.shangou.seashop.seller.core.thrift.impl.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerRoleRemoteService;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiRoleService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiCmdRoleReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryRoleReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiRoleResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiRoleRespList;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.RoleRespList;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class SellerSellerApiRoleServiceImpl implements SellerApiRoleService {

    @Resource
    private SellerRoleRemoteService sellerRoleRemoteService;

    @Override
    public ApiRoleRespList queryRoleList(ApiQueryRoleReq queryRoleReq) {
        //转化参数
        QueryRoleReq queryPrivilegeReq = JsonUtil.copy(queryRoleReq, QueryRoleReq.class);
        //获取结果
        RoleRespList roleRespList = sellerRoleRemoteService.queryRoleList(queryPrivilegeReq);
        //转化结果
        return new ApiRoleRespList(JsonUtil.copyList(roleRespList.getRoleRespList(), ApiRoleResp.class));
    }

    @Override
    public Long addRole(ApiCmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        //获取结果
        return sellerRoleRemoteService.addRole(cmdRoleReq1);
    }

    @Override
    public Long editRole(ApiCmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        //获取结果
        return sellerRoleRemoteService.editRole(cmdRoleReq1);
    }

    @Override
    public Long deleteRole(ApiCmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        //获取结果
        return sellerRoleRemoteService.deleteRole(cmdRoleReq1);
    }
}
