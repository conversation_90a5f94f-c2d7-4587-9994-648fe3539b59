package com.sankuai.shangou.seashop.m.core.service.user.shop.impl;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiBusinessCategoryApplyService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiAuditBusinessCategoryApplyReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryApplyDetailReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryApplyDetailResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryApplyResp;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryApplyCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryApplyQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AuditBusinessCategoryApplyReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyDetailReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MApiBusinessCategoryApplyServiceImpl implements MApiBusinessCategoryApplyService {

    @Resource
    private BusinessCategoryApplyCmdFeign businessCategoryApplyCmdFeign;
    @Resource
    private BusinessCategoryApplyQueryFeign businessCategoryApplyQueryFeign;


    @Override
    public BasePageResp<ApiBusinessCategoryApplyResp> queryPage(ApiQueryBusinessCategoryApplyPageReq queryBusinessCategoryApplyPageReq) {
        BasePageResp<BusinessCategoryApplyResp> businessCategoryApplyPageResp = ThriftResponseHelper.executeThriftCall(() ->
                        businessCategoryApplyQueryFeign.queryPage(JsonUtil.copy(queryBusinessCategoryApplyPageReq,
                                QueryBusinessCategoryApplyPageReq.class)));
        return PageResultHelper.transfer(businessCategoryApplyPageResp, ApiBusinessCategoryApplyResp.class);
    }

    @Override
    public ApiBusinessCategoryApplyDetailResp queryDetail(ApiQueryBusinessCategoryApplyDetailReq req) {
        QueryBusinessCategoryApplyDetailReq remoteReq = JsonUtil.copy(req, QueryBusinessCategoryApplyDetailReq.class);
        BusinessCategoryApplyDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyQueryFeign.queryDetail(remoteReq));
        return JsonUtil.copy(resp, ApiBusinessCategoryApplyDetailResp.class);
    }

    @Override
    public BaseResp auditBusinessCategoryApply(ApiAuditBusinessCategoryApplyReq req) {
        AuditBusinessCategoryApplyReq remoteReq = JsonUtil.copy(req, AuditBusinessCategoryApplyReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyCmdFeign.auditBusinessCategoryApply(remoteReq));
    }
}
