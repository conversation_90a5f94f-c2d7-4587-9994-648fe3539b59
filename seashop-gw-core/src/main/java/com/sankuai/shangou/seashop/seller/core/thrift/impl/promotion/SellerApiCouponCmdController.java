package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponCmdFeign;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiCouponSaveReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: liuhaox
 * @date: 2023/11/7/007
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiCoupon")
public class SellerApiCouponCmdController {

    @Resource
    private CouponCmdFeign couponCmdFeign;

    @PostMapping(value = "/save", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> save(@RequestBody ApiCouponSaveReq request) throws TException {
        

        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        request.setShopId(loginShopDto.getShopId());
        request.setShopName(loginShopDto.getName());
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("save", request, req ->
                ThriftResponseHelper.executeThriftCall(() -> couponCmdFeign.save(JsonUtil.copy(req, CouponSaveReq.class))));
    }

    @PostMapping(value = "/endActive", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("endActive", request, req ->
                ThriftResponseHelper.executeThriftCall(() -> couponCmdFeign.endActive(req)));
    }
}
