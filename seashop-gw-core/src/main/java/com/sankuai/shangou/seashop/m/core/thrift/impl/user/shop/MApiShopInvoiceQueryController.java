package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiShopInvoiceService;
import com.sankuai.shangou.seashop.m.thrift.core.request.user.shop.ApiQueryShopInvoiceReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.user.shop.ApiQueryShopInvoiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/mApi/apiShopInvoice")
public class MApiShopInvoiceQueryController {

    @Resource
    private MApiShopInvoiceService mApiShopInvoiceService;

    @GetMapping(value = "/queryShopInvoice")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiQueryShopInvoiceResp> queryShopInvoice() {
        return ThriftResponseHelper.responseInvoke("queryShopInvoice", null, req -> {
            QueryShopInvoiceResp queryShopInvoiceResp = this.mApiShopInvoiceService.querySelfShopInvoice();
            return JsonUtil.copy(queryShopInvoiceResp, ApiQueryShopInvoiceResp.class);
        });
    }
}
