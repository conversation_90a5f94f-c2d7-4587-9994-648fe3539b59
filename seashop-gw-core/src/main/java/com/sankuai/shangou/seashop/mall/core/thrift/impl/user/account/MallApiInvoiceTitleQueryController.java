package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiInvoiceTitleQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiInvoiceTitleRespList;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.mall.core.service.user.account.MallApiInvoiceTitleService;

/**
 * @description: 发票抬头服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mallApi/apiInvoiceTitle")
public class MallApiInvoiceTitleQueryController {

    @Resource
    private MallApiInvoiceTitleService mallApiInvoiceTitleService;

    @PostMapping(value = "/queryList", consumes = "application/json")
    @NeedLogin
    public ResultDto<ApiInvoiceTitleRespList> queryList(@RequestBody ApiInvoiceTitleQueryReq queryReq) {
        

        queryReq.setUserId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.responseInvoke("queryList", queryReq, req -> mallApiInvoiceTitleService.queryList(req));
    }
}
