package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiAdvanceSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.AdvanceSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.AdvanceCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiAdvance")
public class MApiAdvanceCmdController {

    @Resource
    private AdvanceCmdFeign advanceCmdFeign;

    @PostMapping(value = "/save", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> save(@RequestBody ApiAdvanceSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            AdvanceSaveReq saveReq = JsonUtil.copy(req, AdvanceSaveReq.class);
            saveReq.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> advanceCmdFeign.save(saveReq));
        });
    }
}
