package com.sankuai.shangou.seashop.seller.core.service.export.handler.product;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.product.wrapper.ProductDataWrapper;

/**
 * <AUTHOR>
 * @date 2024/01/03 9:27
 */
@Service
public class ProductExportGetter extends AbstractBaseDataGetter<QueryProductReq>
    implements SingleWrapperDataGetter<QueryProductReq> {

    @Resource
    private SellerProductRemoteService sellerProductRemoteService;

    @Override
    public DataContext selectData(QueryProductReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        // 构造数据包装器，每一个包装器对应一个sheet页，并且每个包装器可以单独定义自己的excel处理器，比如样式、合并单元格等
        ProductDataWrapper wrapper = new ProductDataWrapper(sellerProductRemoteService);
        context.setSheetDataList(Collections.singletonList(wrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.PRODUCT_PAGE_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.PRODUCT_PAGE_LIST.getName();
    }
}
