package com.sankuai.shangou.seashop.seller.core.thrift.impl.finance;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.service.finance.SellerApiFinanceService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderStatisticsReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiShopIdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiFinanceIndexResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiOrderStatisticsListResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiFinance")
public class SellerApiFinanceQueryController {

    @Resource
    private SellerApiFinanceService sellerApiFinanceService;

    @PostMapping(value = "/getFinanceIndex", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiFinanceIndexResp> getFinanceIndex(@RequestBody ApiShopIdReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("getFinanceIndex", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            ApiFinanceIndexResp resp = sellerApiFinanceService.getFinanceIndex(req.getShopId());
            return resp;
        });
    }

    @PostMapping(value = "/getOrderStatisticsList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiOrderStatisticsListResp> getOrderStatisticsList(@RequestBody ApiOrderStatisticsReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("getOrderStatisticsList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            req.checkParameter();
            ApiOrderStatisticsListResp resp = sellerApiFinanceService.getOrderStatisticsList(req);
            return resp;
        });
    }

}
