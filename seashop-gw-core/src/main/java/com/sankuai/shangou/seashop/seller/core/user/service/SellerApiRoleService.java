package com.sankuai.shangou.seashop.seller.core.user.service;


import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiCmdRoleReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryRoleReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiRoleRespList;

public interface SellerApiRoleService {
    /**
     * 查询角色列表
     *
     * @param queryRoleReq 查询条件
     * @return RoleRespList
     */
    ApiRoleRespList queryRoleList(ApiQueryRoleReq queryRoleReq);

    /**
     * 添加角色
     *
     * @param cmdRoleReq 角色信息
     * @return Long
     */
    Long addRole(ApiCmdRoleReq cmdRoleReq);

    /**
     * 修改角色
     *
     * @param cmdRoleReq 角色信息
     * @return Long
     */
    Long editRole(ApiCmdRoleReq cmdRoleReq);

    /**
     * 删除角色
     *
     * @param cmdRoleReq 角色信息
     * @return Long
     */
    Long deleteRole(ApiCmdRoleReq cmdRoleReq);
}
