package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.context;

import java.util.HashMap;
import java.util.Map;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/29 14:12
 */
@Setter
@Getter
public class ExclusivePriceExportContext {

    private Workbook workbook;

    private Sheet sheet;

    private CellStyle titleStyle;

    private CellStyle style;

    private CellStyle cellStyle;

    private int rowNum;

    private Map<Integer, Integer> columnWidthMap = new HashMap<>();

}
