package com.sankuai.shangou.seashop.mall.core.service.refund;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.refund.RefundExportResp;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundReq;

/**
 * <AUTHOR>
 */
public interface MallRefundService {

    /**
     * 导出售后
     *
     * @param queryReq 页面查询参数
     * @return 导出文件下载地址
     */
    RefundExportResp exportRefund(UserQueryRefundReq queryReq);

}
