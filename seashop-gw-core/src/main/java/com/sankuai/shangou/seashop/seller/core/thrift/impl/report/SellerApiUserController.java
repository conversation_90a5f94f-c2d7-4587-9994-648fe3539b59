package com.sankuai.shangou.seashop.seller.core.thrift.impl.report;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.TradeReq;
import com.hishop.himall.report.api.request.UserReq;
import com.hishop.himall.report.api.response.*;
import com.hishop.himall.report.api.service.ReportTradeFeign;
import com.hishop.himall.report.api.service.ReportUserFeign;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.util.model.ResultInfo;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sellerApi/report/user")
public class SellerApiUserController {

    @Resource
    private ReportUserFeign reportUserFeign;

    /**
     * 交易概览
     *
     * @param request
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<UserSummaryResp> queryProductSummary(@RequestBody ReportReq request){
        return ThriftResponseHelper.responseInvoke("queryProductSummary", request, req->{
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportUserFeign.queryProductSummary(req));
        });
    }

    /**
     * 用户新增趋势
     *
     * @param request
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/increase/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryUserIncrease(@RequestBody ReportReq request){
        return ThriftResponseHelper.responseInvoke("queryUserIncrease", request, req->{
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportUserFeign.queryUserIncrease(req));
        });

    }

    /**
     * 用户省份分布
     *
     * @param request
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/province/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryProvince(@RequestBody ReportReq request) {
        return ThriftResponseHelper.responseInvoke("queryProvince", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportUserFeign.queryProvince(req));
        });
    }

    /**
     * 新老会员概览
     * @param req
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/newOld/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<UserNewOldSummaryResp> queryNewOldSummary(@RequestBody ReportReq req){
        return ThriftResponseHelper.responseInvoke("queryNewOldSummary", req, request -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportUserFeign.queryNewOldSummary(req));
        });
    }

    /**
     * 新老会员走势图
     * @param request
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/newOld/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryNewOldEcharts(@RequestBody ReportReq request){
        return ThriftResponseHelper.responseInvoke("queryNewOldEcharts", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportUserFeign.queryNewOldEcharts(req));
        });


    }

    /**
     * 会员分析列表
     * @param req
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<PageResult<UserResp>> queryProducts(@RequestBody UserReq req){
        return ThriftResponseHelper.responseInvoke("query", req, request -> {
            request.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportUserFeign.queryProducts(request));
        });

    }

    /**
     * 会员分析导出
     * @param req
     * @return
     */
    @NeedLogin
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> exportProduct(@RequestBody UserReq req){
        return ThriftResponseHelper.responseInvoke("export", req, request -> {
            request.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportUserFeign.exportProduct(request));
        });
    }

}
