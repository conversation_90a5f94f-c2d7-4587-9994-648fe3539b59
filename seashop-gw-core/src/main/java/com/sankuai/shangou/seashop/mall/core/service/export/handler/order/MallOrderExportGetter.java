package com.sankuai.shangou.seashop.mall.core.service.export.handler.order;

import java.util.Collections;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.mall.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.mall.core.service.export.handler.order.eo.OrderAndItemExportEo;
import com.sankuai.shangou.seashop.mall.core.service.export.handler.order.wrapper.OrderWrapper;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryUserOrderReq;

/**
 * <AUTHOR>
 */
@Service
public class MallOrderExportGetter extends AbstractBaseDataGetter<QueryUserOrderReq>
    implements SingleWrapperDataGetter<QueryUserOrderReq> {

    @Override
    public DataContext selectData(QueryUserOrderReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        // 构造数据包装器，每一个包装器对应一个sheet页，并且每个包装器可以单独定义自己的excel处理器，比如样式、合并单元格等
        BaseExportWrapper<OrderAndItemExportEo, QueryUserOrderReq> orderWrapper = new OrderWrapper();
        context.setSheetDataList(Collections.singletonList(orderWrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.ORDER.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.ORDER.getName();
    }
}
