package com.sankuai.shangou.seashop.m.core.thrift.impl.system.express;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyResp;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.*;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiExpressSiteSettingResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiQueryExpressCompanyResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/mApi/apiExpress")
public class MApiExpressController {

    @Resource
    private ExpressCmdFeign expressCmdFeign;
    @Resource
    private ExpressQueryFeign expressQueryFeign;

    @PostMapping(value = "/createExpressCompany", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> createExpressCompany(@RequestBody ApiAddExpressCompanyReq addExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createExpressCompany", addExpressCompanyReq, req -> {
            req.checkParameter();
            AddExpressCompanyReq bean = JsonUtil.copy(req, AddExpressCompanyReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> expressCmdFeign.createExpressCompany(bean));
        });
    }

    @PostMapping(value = "/updateExpressCompany", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateExpressCompany(@RequestBody ApiCmdExpressCompanyReq cmdExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateExpressCompany", cmdExpressCompanyReq, req -> {
            req.checkParameter();
            CmdExpressCompanyReq bean = JsonUtil.copy(req, CmdExpressCompanyReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> expressCmdFeign.updateExpressCompany(bean));
        });
    }

    @PostMapping(value = "/deleteExpressCompany", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deleteExpressCompany(@RequestBody ApiDeleteExpressCompanyReq deleteExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteExpressCompany", deleteExpressCompanyReq, req -> {
            req.checkParameter();
            DeleteExpressCompanyReq bean = JsonUtil.copy(req, DeleteExpressCompanyReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> expressCmdFeign.deleteExpressCompany(bean));
        });
    }

    @PostMapping(value = "/addOrUpdateSiteSetting", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> addOrUpdateSiteSetting(@RequestBody ApiExpressSiteSettingReq expressSiteSettingReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addOrUpdateSiteSetting", expressSiteSettingReq, req -> {
            req.checkParameter();
            ExpressSiteSettingReq bean = JsonUtil.copy(req, ExpressSiteSettingReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> expressCmdFeign.addOrUpdateSiteSetting(bean));
        });
    }

    @PostMapping(value = "/queryExpressCompanyList", consumes = "application/json")
    public ResultDto<ApiQueryExpressCompanyResp> queryExpressCompanyList(@RequestBody ApiQueryExpressCompanyReq queryExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryExpressCompanyList", queryExpressCompanyReq, req -> {
            QueryExpressCompanyReq bean = JsonUtil.copy(req, QueryExpressCompanyReq.class);
            QueryExpressCompanyResp resp = ThriftResponseHelper.executeThriftCall(() -> expressQueryFeign.queryExpressCompanyList(bean));
            return JsonUtil.copy(resp, ApiQueryExpressCompanyResp.class);
        });
    }

    @GetMapping(value = "/queryExpressSiteSetting")
    public ResultDto<ApiExpressSiteSettingResp> queryExpressSiteSetting() throws TException {
        return ThriftResponseHelper.responseInvoke("queryExpressCompanyList", null, req -> {
            return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> expressQueryFeign.queryExpressSiteSetting()), ApiExpressSiteSettingResp.class);
        });
    }

    @PostMapping(value = "/updateExpressTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateExpressTemplate(@RequestBody ApiUpdateExpressTemplateReq updateExpressTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateExpressTemplate", updateExpressTemplateReq, req -> {
            req.checkParameter();
            UpdateExpressTemplateReq bean = JsonUtil.copy(req, UpdateExpressTemplateReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> expressCmdFeign.updateExpressTemplate(bean));
        });
    }

    @PostMapping(value = "/updateExpressStatus", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateExpressStatus(@RequestBody ApiUpdateExpressStatusReq updateExpressStatusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateExpressTemplate", updateExpressStatusReq, req -> {
            req.checkParameter();
            UpdateExpressStatusReq bean = JsonUtil.copy(req, UpdateExpressStatusReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> expressCmdFeign.updateExpressStatus(bean));
        });
    }

}
