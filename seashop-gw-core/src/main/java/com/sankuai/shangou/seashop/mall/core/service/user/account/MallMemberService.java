package com.sankuai.shangou.seashop.mall.core.service.user.account;

import cn.hutool.core.util.StrUtil;
import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.gw.common.thrift.requests.ApiRefreshTokenReq;
import com.sankuai.gw.common.thrift.requests.ApiRegisterReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.*;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiMemberResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiResetPasswordResp;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.request.BindOpenIdReq;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.core.commmon.auth.MallAuthenticationHandler;
import com.sankuai.shangou.seashop.mall.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.mall.common.enums.MallResultCodeEnum;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallMemberRemoteService;
import com.sankuai.shangou.seashop.user.thrift.account.enums.MemberContactEnum;
import com.sankuai.shangou.seashop.user.thrift.account.request.*;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.ResetPasswordResp;
import com.sankuai.shangou.seashop.user.thrift.auth.LoginUserFeign;
import com.sankuai.shangou.seashop.user.thrift.auth.request.RefreshTokenReq;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CheckCodeCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdSendCodeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

@Service
@Slf4j
public class MallMemberService {
    @Resource
    private MallMemberRemoteService mallMemberRemoteService;
    @Resource
    private MallManagerService mallManagerService;
    @Resource
    private SquirrelUtil squirrelUtil;
    @Resource
    private MallAuthenticationHandler manageAuthenticationHandler;
    @Resource
    private LoginUserFeign loginUserFeign;

    public MemberResp queryMember(Integer epAccountId) {
        QueryMemberReq queryMemberReq = new QueryMemberReq();
        queryMemberReq.setEpAccountId(epAccountId);
        return getMemberResp(queryMemberReq);
    }

    public ApiMemberResp queryMember(BaseIdReq baseIdReq) {
        QueryMemberReq queryMemberReq = new QueryMemberReq();
        queryMemberReq.setId(baseIdReq.getId());
        MemberResp member = getMemberResp(queryMemberReq);
        QueryManagerReq queryManagerReq = new QueryManagerReq();
        queryManagerReq.setManagerName(member.getUserName());
        ManagerResp epManagerResp = mallManagerService.queryManager(queryManagerReq);
        ApiMemberResp memberResp = JsonUtil.copy(member, ApiMemberResp.class);
        //如果没有店铺id
        if (epManagerResp != null && epManagerResp.getShopId() != null) {
            memberResp.setSupplier(epManagerResp.getPlateStatus().equals(ShopEnum.PlatAuditStatus.Open.getCode()));
            memberResp.setShopId(epManagerResp.getShopId());
            memberResp.setWhetherNoticeJoin(false);
            memberResp.setPlateStatus(epManagerResp.getPlateStatus());
        } else {
            memberResp.setSupplier(false);
            memberResp.setWhetherNoticeJoin(!memberResp.getWhetherNoticeJoin());
        }
        return memberResp;
    }

    public MemberResp getMemberResp(QueryMemberReq queryMemberReq) {
        return mallMemberRemoteService.getMemberResp(queryMemberReq);
    }

    public BaseResp updateMember(ApiUpdateMemberReq updateMemberReq) {
        //转化入参
        UpdateMemberReq updateMemberReq1 = JsonUtil.copy(updateMemberReq, UpdateMemberReq.class);
        return mallMemberRemoteService.updateMember(updateMemberReq1);
    }

    public BaseResp sendCode(ApiCmdSendCodeReq apiCmdSendCodeReq) {
        //转化入参
        CmdSendCodeReq sendCodeCmdReq = JsonUtil.copy(apiCmdSendCodeReq, CmdSendCodeReq.class);
        //调用服务
        mallMemberRemoteService.sendCode(sendCodeCmdReq);
        //转化出参
        return BaseResp.of();
    }

    public String checkCode(ApiCmdCheckCodeReq apiCmdCheckCodeReq) {
//        //转化入参
        CheckCodeCmdReq sendCodeCmdReq = JsonUtil.copy(apiCmdCheckCodeReq, CheckCodeCmdReq.class);
//        //调用服务
        mallMemberRemoteService.checkCode(sendCodeCmdReq);
        //        生成业务code
        String token = UUID.randomUUID().toString().replace("-", "");
        //保存token
        squirrelUtil.set(CommonConstant.PERMISSION_CHANGE_MOBILE + token, apiCmdCheckCodeReq.getContact(), 3600);
        return token;
    }

    public BaseResp bindContact(ApiCmdBindContactReq apiCheckCodeCmdReq, LoginMemberDto userInfo) {
//        判断业务token是否存在
        String oldPhone = TracerUtil.getMemberDto().getUserPhone();
        if (StrUtil.isNotBlank(oldPhone)) {
            String token = (String) squirrelUtil.get(CommonConstant.PERMISSION_CHANGE_MOBILE + apiCheckCodeCmdReq.getToken());
            if (token != null) {
                AssertUtil.throwIfNull(apiCheckCodeCmdReq.getToken(), MallResultCodeEnum.PERMISSION_CODE_EXPIRED);
                AssertUtil.throwIfTrue(!StrUtil.equals(token, oldPhone), MallResultCodeEnum.PERMISSION_CODE_EXPIRED);
            }
        }
        BindContactCmdReq sendCodeCmdReq = JsonUtil.copy(apiCheckCodeCmdReq, BindContactCmdReq.class);
        if (sendCodeCmdReq.getUsertype() == null) {
            sendCodeCmdReq.setUsertype(MemberContactEnum.Provider.getEnumByCode(apiCheckCodeCmdReq.getType()).getValue());
        }
        LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();
        sendCodeCmdReq.setId(loginMemberDto.getId());
        mallMemberRemoteService.bindContact(sendCodeCmdReq);
        return BaseResp.of();
    }

    public ApiImageVerificationCodeResp imageVerificationCode() {
        //调用服务
        ImageVerificationCodeResp verificationCodeResp = mallMemberRemoteService.imageVerificationCode();
        return JsonUtil.copy(verificationCodeResp, ApiImageVerificationCodeResp.class);
    }

    public String getOpenId(String code, LoginMemberDto userInfo) {
        //调用服务
        String openId = mallMemberRemoteService.getOpenId(code);
        if (userInfo != null && TracerUtil.getMemberDto() != null) {
            BindOpenIdReq bindOpenIdReq = new BindOpenIdReq();
            bindOpenIdReq.setOpenId(openId);
            bindOpenIdReq.setUserId(TracerUtil.getMemberDto().getId());
            // todo base 服务暂时是个空接口
        }
        return openId;
    }

    public BaseResp updateLoginTime(Long id) {
        return mallMemberRemoteService.updateLoginTime(id);
    }

    public ApiResetPasswordResp resetPassword(ApiResetPasswordReq req) {
        String token = (String) squirrelUtil.get(CommonConstant.PERMISSION_CHANGE_MOBILE + req.getPhoneCheckCode());
        ResetPasswordReq resetPasswordReq = JsonUtil.copy(req, ResetPasswordReq.class);
        resetPasswordReq.setPhone(token);
        ResetPasswordResp resp = mallMemberRemoteService.resetPassword(resetPasswordReq);
        return JsonUtil.copy(resp, ApiResetPasswordResp.class);
    }

    public LoginResp login(ApiLoginReq loginReq) {
        return mallMemberRemoteService.login(loginReq);
    }

    public LoginResp register(ApiRegisterReq req) {
        return mallMemberRemoteService.register(req);
    }

    public Boolean logout() {
        mallMemberRemoteService.logout(manageAuthenticationHandler.getTokenCache());
        return true;
    }

    public ApiResetPasswordResp modifyPassword(ApiModifyPasswordReq req) {
        ModifyPasswordReq modifyPasswordReq = JsonUtil.copy(req, ModifyPasswordReq.class);
        modifyPasswordReq.setId(TracerUtil.getMemberDto().getId());
        return JsonUtil.copy(mallMemberRemoteService.modifyPassword(modifyPasswordReq), ApiResetPasswordResp.class);
    }

    public LoginResp refreshToken(ApiRefreshTokenReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> loginUserFeign.refreshToken(JsonUtil.copy(req, RefreshTokenReq.class)));
    }
}
