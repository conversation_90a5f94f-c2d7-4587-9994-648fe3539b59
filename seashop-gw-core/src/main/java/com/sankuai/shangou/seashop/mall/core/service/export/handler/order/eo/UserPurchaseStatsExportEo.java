package com.sankuai.shangou.seashop.mall.core.service.export.handler.order.eo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class UserPurchaseStatsExportEo {

    @ExcelProperty(value = "订单编号")
    private String orderId;
    @ExcelProperty(value = "订单状态")
    private String orderStatusDesc;
    @ExcelProperty(value = "商品名称")
    private String productName;
    @ExcelProperty(value = "商品ID")
    private String productId;
    @ExcelProperty(value = "规格ID")
    private Long skuAutoId;
    @ExcelProperty(value = "商品规格")
    private String skuDesc;
    @ExcelProperty(value = "商品采购数量")
    private Long quantity;
    @ExcelProperty(value = "商品采购金额")
    private BigDecimal realTotalPrice;

}
