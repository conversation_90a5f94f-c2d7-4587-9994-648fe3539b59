package com.sankuai.shangou.seashop.m.core.thrift.impl.base;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MobileFootQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryFootMenusReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiQueryFootMenusReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiQueryFootMenusResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * @author： liweisong
 * @create： 2023/11/29 11:48
 */
@RestController
@RequestMapping("/mApi/apiMobileFoot")
public class MApiMobileFootQueryController {

    @Resource
    private MobileFootQueryFeign mobileFootQueryFeign;

    @PostMapping(value = "/queryFootMenus", consumes = "application/json")
    public ResultDto<ApiQueryFootMenusResp> queryFootMenus(@RequestBody ApiQueryFootMenusReq queryFootMenusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryFootMenus", queryFootMenusReq, req -> {
            req.checkParameter();
            QueryFootMenusReq bean = JsonUtil.copy(req, QueryFootMenusReq.class);
            return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> mobileFootQueryFeign.queryFootMenus(bean)), ApiQueryFootMenusResp.class);
        });
    }
}
