package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.ApiAddShippingAddressReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.ApiDeleteShippingAddressReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.ApiUpdateShippingAddressReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.user.thrift.account.ShippingAddressCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.AddShippingAddressReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.DeleteShippingAddressReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.UpdateShippingAddressReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiShippingAddress")
@Slf4j
public class MallApiShippingAddressCmdController {

    @Resource
    private ShippingAddressCmdFeign shippingAddressCmdFeign;

    @NeedLogin
    @PostMapping(value = "/addAddress", consumes = "application/json")
    public ResultDto<Long> addAddress(@RequestBody ApiAddShippingAddressReq addReq) throws TException {
        

        log.info("【收货地址】添加收货地址, 请求参数={}", addReq);
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("addAddress", addReq, func -> {
            // 参数基础校验
            addReq.checkParameter();
            // 参数对象转换
            AddShippingAddressReq req = JsonUtil.copy(addReq, AddShippingAddressReq.class);
            req.setUserId(userId);
            // 业务逻辑处理
            return ThriftResponseHelper.executeThriftCall(() -> shippingAddressCmdFeign.addAddress(req));
        });
    }

    @NeedLogin
    @PostMapping(value = "/updateAddress", consumes = "application/json")
    public ResultDto<Boolean> updateAddress(@RequestBody ApiUpdateShippingAddressReq updateReq) throws TException {
        

        log.info("【收货地址】修改收货地址, 请求参数={}", updateReq);
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("updateAddress", updateReq, func -> {
            // 参数基础校验
            updateReq.checkParameter();
            // 参数对象转换
            UpdateShippingAddressReq req = JsonUtil.copy(updateReq, UpdateShippingAddressReq.class);
            req.setUserId(userId);
            // 业务逻辑处理
            return ThriftResponseHelper.executeThriftCall(() -> shippingAddressCmdFeign.updateAddress(req));
        });
    }

    @NeedLogin
    @PostMapping(value = "/deleteAddress", consumes = "application/json")
    public ResultDto<Boolean> deleteAddress(@RequestBody ApiDeleteShippingAddressReq deleteReq) throws TException {
        

        log.info("【收货地址】删除收货地址, 请求参数={}", deleteReq);
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("deleteAddress", deleteReq, func -> {
            // 参数基础校验
            deleteReq.checkParameter();
            // 参数对象转换
            DeleteShippingAddressReq req = JsonUtil.copy(deleteReq, DeleteShippingAddressReq.class);
            req.setUserId(userId);
            // 业务逻辑处理
            return ThriftResponseHelper.executeThriftCall(() -> shippingAddressCmdFeign.deleteAddress(req));
        });
    }
}
