package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.*;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.core.service.promotion.MApiExclusivePriceService;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiExclusivePriceResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiExclusivePriceSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiExclusivePrice")
public class MApiExclusivePriceQueryController {


    @Resource
    private MApiExclusivePriceService mApiExclusivePriceService;
    @Resource
    private ExclusivePriceQueryFeign exclusivePriceQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiExclusivePriceSimpleResp>> pageList(@RequestBody ApiExclusivePriceQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            ExclusivePriceQueryReq exclusivePriceQueryReq = JsonUtil.copy(req, ExclusivePriceQueryReq.class);
            BasePageResp<ExclusivePriceSimpleResp> listRespBasePageResp = ThriftResponseHelper.executeThriftCall(() -> exclusivePriceQueryFeign.pageList(exclusivePriceQueryReq));
            return PageResultHelper.transfer(listRespBasePageResp, ApiExclusivePriceSimpleResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiExclusivePriceResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            ExclusivePriceResp priceResp = ThriftResponseHelper.executeThriftCall(() -> exclusivePriceQueryFeign.getById(req));
            return JsonUtil.copy(priceResp, ApiExclusivePriceResp.class);
        });
    }

    @PostMapping(value = "/export", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> export(@RequestBody ApiExclusivePriceQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("export", request, req -> {
            req.checkParameter();
            mApiExclusivePriceService.export(req, TracerUtil.getManagerDto());
            return BaseResp.of();
        });
    }
}
