package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiQueryRefundReasonResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.RefundReasonQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryRefundReasonResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/25 10:48
 */
@RestController
@RequestMapping("/mallApi/apiRefundReason")
public class MallApiRefundReasonController {

    @Resource
    private RefundReasonQueryFeign refundReasonQueryFeign;

    @GetMapping(value = "/queryMallRefundReasonList")
    public ResultDto<ApiQueryRefundReasonResp> queryMallRefundReasonList() throws TException {
        return ThriftResponseHelper.responseInvoke("queryMallRefundReasonList", null, req -> {

            QueryRefundReasonResp resp = ThriftResponseHelper.executeThriftCall(() -> refundReasonQueryFeign.queryRefundReasonList());
            return JsonUtil.copy(resp, ApiQueryRefundReasonResp.class);
        });
    }
}
