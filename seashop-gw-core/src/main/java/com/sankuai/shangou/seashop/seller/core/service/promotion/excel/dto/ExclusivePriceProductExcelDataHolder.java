package com.sankuai.shangou.seashop.seller.core.service.promotion.excel.dto;


import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Getter
@Setter
public class ExclusivePriceProductExcelDataHolder {

    private List<ExclusivePriceProductExcelDto> productList = new ArrayList<>();
}
