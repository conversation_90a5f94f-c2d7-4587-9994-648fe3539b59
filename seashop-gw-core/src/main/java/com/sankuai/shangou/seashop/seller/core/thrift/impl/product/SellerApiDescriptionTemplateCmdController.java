package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.DescriptionTemplateCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.DeleteDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.SaveDescriptionTemplateReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiSaveDescriptionTemplateReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/21 15:25
 */
@RestController
@RequestMapping("/sellerApi/apiDescriptionTemplate")
public class SellerApiDescriptionTemplateCmdController {

    @Resource
    private DescriptionTemplateCmdFeign descriptionTemplateCmdFeign;

    @PostMapping(value = "/createDescriptionTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> createDescriptionTemplate(@RequestBody ApiSaveDescriptionTemplateReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("createDescriptionTemplate", request, req -> {

            SaveDescriptionTemplateReq remoteReq = JsonUtil.copy(req, SaveDescriptionTemplateReq.class);
            remoteReq.setPosition(DescriptionTemplatePositionEnum.getByCode(req.getPositionCode()));
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> descriptionTemplateCmdFeign.createDescriptionTemplate(remoteReq));
        });
    }

    @PostMapping(value = "/updateDescriptionTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateDescriptionTemplate(@RequestBody ApiSaveDescriptionTemplateReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("updateDescriptionTemplate", request, req -> {

            SaveDescriptionTemplateReq remoteReq = JsonUtil.copy(req, SaveDescriptionTemplateReq.class);
            remoteReq.setPosition(DescriptionTemplatePositionEnum.getByCode(req.getPositionCode()));
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> descriptionTemplateCmdFeign.updateDescriptionTemplate(remoteReq));
        });
    }

    @PostMapping(value = "/deleteDescriptionTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> deleteDescriptionTemplate(@RequestBody BaseIdReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("deleteDescriptionTemplate", request, req -> {

            DeleteDescriptionTemplateReq remoteReq = JsonUtil.copy(req, DeleteDescriptionTemplateReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> descriptionTemplateCmdFeign.deleteDescriptionTemplate(remoteReq));
        });
    }
}
