package com.sankuai.shangou.seashop.seller.core.service.export.handler.order.eo;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/4 11:09
 */
@Getter
@Setter
public class OrderProductDistributionEo {

    @ExcelProperty(value = "商品名称")
    private String productName;

    @ExcelProperty(value = "货号")
    private String productCode;

    @ExcelProperty(value = "规格")
    private String skuName;

    @ExcelProperty(value = "拣货数量")
    private Long quantity;

    @ExcelProperty(value = "现库存数")
    private String stockQuantity;
}
