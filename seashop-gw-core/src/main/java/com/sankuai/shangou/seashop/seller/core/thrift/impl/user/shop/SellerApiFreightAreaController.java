package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerFreightAreaRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.ApiUpdateFreightTemplateReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiAddFreightReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCopyFreightReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiDeleteFreightTemplateReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiQueryFreightTemplateDetailReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiQueryFreightTemplateReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiQueryFreightTemplateDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiQueryFreightTemplateResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CopyFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateDetailReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateResp;


@RestController
@RequestMapping("/sellerApi/apiFreightArea")
public class SellerApiFreightAreaController {

    @Resource
    private SellerFreightAreaRemoteService sellerFreightAreaRemoteService;

    @PostMapping(value = "/deleteFreightTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> deleteFreightTemplate(@RequestBody ApiDeleteFreightTemplateReq deleteFreightTemplateReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("deleteFreightTemplate", deleteFreightTemplateReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerFreightAreaRemoteService.deleteFreightTemplate(JsonUtil.copy(req, DeleteFreightTemplateReq.class));
        });
    }

    @PostMapping(value = "/addFreightTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> addFreightTemplate(@RequestBody ApiAddFreightReq addFreightReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("addFreightTemplate", addFreightReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerFreightAreaRemoteService.addFreightTemplate(JsonUtil.copy(req, AddFreightReq.class));
        });
    }

    @PostMapping(value = "/updateFreightTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateFreightTemplate(@RequestBody ApiUpdateFreightTemplateReq updateFreightReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("updateFreightTemplate", updateFreightReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerFreightAreaRemoteService.updateFreightTemplate(JsonUtil.copy(req, UpdateFreightReq.class));
        });
    }

    @PostMapping(value = "/queryFreightTemplateList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiQueryFreightTemplateResp> queryFreightTemplateList(@RequestBody ApiQueryFreightTemplateReq queryFreightTemplateReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryFreightTemplateList", queryFreightTemplateReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            QueryFreightTemplateResp queryFreightTemplateResp = sellerFreightAreaRemoteService.queryFreightTemplateList(JsonUtil.copy(req, QueryFreightTemplateReq.class));
            return JsonUtil.copy(queryFreightTemplateResp, ApiQueryFreightTemplateResp.class);
        });
    }

    @PostMapping(value = "/queryFreightTemplateDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiQueryFreightTemplateDetailResp> queryFreightTemplateDetail(@RequestBody ApiQueryFreightTemplateDetailReq queryFreightTemplateDetailReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryFreightTemplateDetail", queryFreightTemplateDetailReq, req -> {
            req.checkParameter();
            QueryFreightTemplateDetailReq param = JsonUtil.copy(req, QueryFreightTemplateDetailReq.class);
            param.setShopId(TracerUtil.getShopDto().getShopId());
            QueryFreightTemplateDetailResp queryFreightTemplateDetailResp = sellerFreightAreaRemoteService.queryFreightTemplateDetail(param);
            return JsonUtil.copy(queryFreightTemplateDetailResp, ApiQueryFreightTemplateDetailResp.class);
        });
    }

    @PostMapping(value = "/copyFreightTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> copyFreightTemplate(@RequestBody ApiCopyFreightReq copyFreightReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("copyFreightTemplate", copyFreightReq, req -> {
            req.checkParameter();
            CopyFreightReq param = JsonUtil.copy(req, CopyFreightReq.class);
            param.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerFreightAreaRemoteService.copyFreightTemplate(param);
        });
    }
}
