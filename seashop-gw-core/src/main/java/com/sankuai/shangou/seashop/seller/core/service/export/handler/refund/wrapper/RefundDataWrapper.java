package com.sankuai.shangou.seashop.seller.core.service.export.handler.refund.wrapper;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDto;
import com.sankuai.shangou.seashop.seller.common.config.S3Props;
import com.sankuai.shangou.seashop.seller.common.remote.order.SellerOrderRefundRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.refund.eo.OrderRefundEo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 */
public class RefundDataWrapper extends PageExportWrapper<OrderRefundEo, SellerQueryRefundReq> {

    private final SellerOrderRefundRemoteService sellerOrderRefundRemoteService = SpringUtil.getBean(SellerOrderRefundRemoteService.class);
    private final S3Props s3Props = SpringUtil.getBean(S3Props.class);
    private final S3plusStorageService s3plusStorageService = SpringUtil.getBean(S3plusStorageService.class);

    @Override
    public List<OrderRefundEo> getPageList(SellerQueryRefundReq param) {
        BasePageResp<SellerRefundDto> pageResp = sellerOrderRefundRemoteService.sellerQueryRefundPage(param);
        if (pageResp == null || CollUtil.isEmpty(pageResp.getData())) {
            return null;
        }
        List<SellerRefundDto> list = pageResp.getData();
        return JsonUtil.copyList(list, OrderRefundEo.class, (src, target) -> {
            String certPathDesc = Stream.of(src.getCertPic1(), src.getCertPic2(), src.getCertPic3())
                .filter(Objects::nonNull)
                .filter(StrUtil::isNotBlank)
                .map(path -> path)
                .collect(Collectors.joining(";"));
            target.setCertPicDesc(certPathDesc);
        });
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return super.getWriteHandlerList();
    }

    @Override
    public String sheetName() {
        return "订单售后";
    }
}
