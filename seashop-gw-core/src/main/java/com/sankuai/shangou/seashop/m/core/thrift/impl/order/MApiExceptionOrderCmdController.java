package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiConfirmExceptionOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.ExceptionOrderCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.ConfirmExceptionOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mApi/apiExceptionOrder")
@Slf4j
public class MApiExceptionOrderCmdController {

    @Resource
    private ExceptionOrderCmdFeign exceptionOrderCmdFeign;

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/confirm", consumes = "application/json")
    public ResultDto<BaseResp> confirm(@RequestBody ApiConfirmExceptionOrderReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【异常订单】确认退款", req, func -> {
            ConfirmExceptionOrderReq bean = JsonUtil.copy(req, ConfirmExceptionOrderReq.class);
            UserDto user = new UserDto();
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            user.setUserId(loginDto.getId());
            user.setUserName(loginDto.getName());
            bean.setUser(user);

            bean.setOperationUserId(loginDto.getId());
            return ThriftResponseHelper.executeThriftCall(() -> exceptionOrderCmdFeign.confirm(bean));
        });
    }
}
