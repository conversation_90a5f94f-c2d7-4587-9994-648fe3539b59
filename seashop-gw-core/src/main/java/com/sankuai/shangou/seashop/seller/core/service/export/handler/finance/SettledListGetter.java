package com.sankuai.shangou.seashop.seller.core.service.export.handler.finance;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.eo.SettledListEo;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.wrapper.SettledListWrapper;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledQryReq;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
@Service
public class SettledListGetter extends AbstractBaseDataGetter<ApiSettledQryReq>
    implements SingleWrapperDataGetter<ApiSettledQryReq> {

    @Resource
    @Lazy
    private SellerFinanceRemoteService sellerFinanceRemoteService;

    @Override
    public DataContext selectData(ApiSettledQryReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        BaseExportWrapper<SettledListEo, ApiSettledQryReq> wrapper = new SettledListWrapper(sellerFinanceRemoteService);
        context.setSheetDataList(Collections.singletonList(wrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.SETTLED_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.SETTLED_LIST.getName();
    }
}
