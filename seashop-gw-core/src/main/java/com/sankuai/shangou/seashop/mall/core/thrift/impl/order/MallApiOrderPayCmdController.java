package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiInitiatePayReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiInitiatePayResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderPayCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.InitiatePayReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.InitiatePayResp;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.UpdateMemberReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiOrderPay")
@Slf4j
public class MallApiOrderPayCmdController {

    @Resource
    private OrderPayCmdFeign orderPayCmdFeign;

    @Resource
    private ShopMemberCmdFeign shopMemberCmdFeign;

    @NeedLogin
    @PostMapping(value = "/initiatePay", consumes = "application/json")
    public ResultDto<ApiInitiatePayResp> initiatePay(@RequestBody ApiInitiatePayReq initiatePayReq) {
        log.info("【支付】发起支付, 请求参数={}", initiatePayReq);
        return ThriftResponseHelper.responseInvoke("initiatePay", initiatePayReq,
                func -> {
                    initiatePayReq.checkParameter();
                    Long memberId = TracerUtil.getMemberDto().getId();
                    InitiatePayReq req = JsonUtil.copy(initiatePayReq, InitiatePayReq.class);
                    req.setClientIp(TracerUtil.getIp());
                    req.setUserId(memberId);
                    log.info("【支付】发起支付, 请求参数2={}", JsonUtil.toJsonString(req));
                    InitiatePayResp resp = ThriftResponseHelper.executeThriftCall(() -> orderPayCmdFeign.initiatePay(req));
                    if (StringUtils.hasLength(initiatePayReq.getOpenId())) {
                        // 完善用户openId
                        UpdateMemberReq addMemberReq = new UpdateMemberReq();
                        addMemberReq.setId(memberId);
                        addMemberReq.setOpenId(initiatePayReq.getOpenId());
                        ThriftResponseHelper.executeThriftCall(() -> shopMemberCmdFeign.updateMember(addMemberReq));
                    }
                    return JsonUtil.copy(resp, ApiInitiatePayResp.class);
                });
    }

    @GetMapping(value = "/redirectToPage")
    public ResultDto<BaseResp> redirectToPage() throws TException {
        return ResultDto.newWithData(BaseResp.of());
    }
}
