package com.sankuai.shangou.seashop.mall.core.service.user.shop;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiTradeProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdAgreementReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdCreateQRReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdSendCodeReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdShopStepsOneReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdShopStepsThreeReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdShopStepsTwoReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiProductShopQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiShopMallQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiBusinessCategoryEsResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiProductShopInfoResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiShopBrandEsResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiShopDetailResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiShopEsCombinationResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiShopEsResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiShopIntroductionResp;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.enums.SysLoginResultEnum;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.mall.common.config.MallLionConfigClient;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallFavoriteShopRemoteService;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallMemberRemoteService;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallShopRemoteService;
import com.sankuai.shangou.seashop.trade.thrift.core.TradeProductQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.QueryHotSaleProductReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.MallShopProductResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryFavoriteCountReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.QueryFavoriteCountListResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.QueryFavoriteCountResp;
import com.sankuai.shangou.seashop.user.thrift.shop.constant.ShopSquirrelConstant;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdAgreementReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdCreateQRReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdSendCodeReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStepsOneReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStepsThreeReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStepsTwoReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ProductShopQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopEsQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ProductShopInfoResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopIntroductionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsCombinationResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MallApiShopService {

    @Resource
    private MallShopRemoteService mallShopRemoteService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MallFavoriteShopRemoteService mallFavoriteShopRemoteService;
    @Resource
    private MallLionConfigClient mallLionConfigClient;
    @Resource
    private TradeProductQueryFeign tradeProductQueryFeign;
    @Resource
    private MallMemberRemoteService mallMemberRemoteService;

    /**
     * 默认排序字段
     */
    private static final String DEFAULT_SORT_FIELD = "shopId";
    /**
     * 默认排序字段2
     */
    private static final String SEQ_SORT_FIELD = "serialNumber";


    public String residencyApplication(ApiCmdAgreementReq cmdAgreementReq) {
        return mallShopRemoteService.residencyApplication(JsonUtil.copy(cmdAgreementReq, CmdAgreementReq.class));
    }

    public BaseResp sendCode(ApiCmdSendCodeReq cmdSendCodeReq) {
        return mallShopRemoteService.sendCode(JsonUtil.copy(cmdSendCodeReq, CmdSendCodeReq.class));
    }

    public Long editBaseInfo(ApiCmdShopStepsOneReq cmdShopStepsOneReq) {
        return mallShopRemoteService.editBaseInfo(JsonUtil.copy(cmdShopStepsOneReq, CmdShopStepsOneReq.class));
    }

    public Long editBankInfo(ApiCmdShopStepsTwoReq cmdShopStepsTwoReq) {
        return mallShopRemoteService.editBankInfo(JsonUtil.copy(cmdShopStepsTwoReq, CmdShopStepsTwoReq.class));
    }

    public Long editCategoryInfo(ApiCmdShopStepsThreeReq cmdShopStepsThreeReq) {
        return mallShopRemoteService.editCategoryInfo(JsonUtil.copy(cmdShopStepsThreeReq, CmdShopStepsThreeReq.class));
    }


    public ApiShopDetailResp queryDetail(LoginMemberDto info) {
        if (info == null) {
            throw new BusinessException(SysLoginResultEnum.NOT_LOGGED_IN);
        }
        ShopDetailResp shopDetailResp = null;
        try {
            ShopSimpleResp shopSimpleResp = mallShopRemoteService.shopInfoByUserId(new BaseIdReq() {
                {
                    setId(info.getId());
                }
            });
            BaseIdReq baseIdReq = new BaseIdReq();
            baseIdReq.setId(shopSimpleResp.getId());
            shopDetailResp = mallShopRemoteService.queryDetail(baseIdReq);
        } catch (Exception e) {
            shopDetailResp = new ShopDetailResp();
            shopDetailResp.setStage(ShopEnum.ShopStage.Agreement.getCode());

            MemberResp member = mallMemberRemoteService.getMemberById(info.getId());
            if (member != null) {
                shopDetailResp.setMemberPhone(member.getCellPhone());
            }
            log.error("查询店铺详情异常", e);
        }
        ApiShopDetailResp apiShopDetailResp = JsonUtil.copy(shopDetailResp, ApiShopDetailResp.class);
        apiShopDetailResp.setUserName(info.getName());
        return apiShopDetailResp;
    }


    public ApiProductShopInfoResp queryProductShop(ApiProductShopQueryReq baseIdReq) {
        //转化参数
        ProductShopQueryReq productShopQueryReq = JsonUtil.copy(baseIdReq, ProductShopQueryReq.class);
        //调用服务
        ProductShopInfoResp productShopInfoResp = mallShopRemoteService.queryProductShop(productShopQueryReq);
        //转化返回值
        ApiProductShopInfoResp apiProductShopInfoResp = JsonUtil.copy(productShopInfoResp, ApiProductShopInfoResp.class);
        // 处理，查询商品
        Long shopId = baseIdReq.getShopId();
        QueryHotSaleProductReq request = new QueryHotSaleProductReq();
        request.setShopId(shopId);
        request.setUserId(baseIdReq.getUserId());
        MallShopProductResp mallShopProductResp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.queryMallShopProduct(request));
        if (null != mallShopProductResp && CollUtil.isNotEmpty(mallShopProductResp.getProductList())) {
            apiProductShopInfoResp.setProductCount(mallShopProductResp.getProductCount());
        } else {
            apiProductShopInfoResp.setProductCount(0L);
        }
        // 查询收藏数量
        QueryFavoriteCountReq queryFavoriteCountReq = new QueryFavoriteCountReq();
        queryFavoriteCountReq.setShopIdList(Collections.singletonList(shopId));
        if (null != baseIdReq.getUserId()) {
            queryFavoriteCountReq.setMemberId(baseIdReq.getUserId());
        }
        QueryFavoriteCountListResp favoriteCountList = mallFavoriteShopRemoteService.queryCount(queryFavoriteCountReq);
        if (favoriteCountList != null && CollUtil.isNotEmpty(favoriteCountList.getList())) {
            Optional<QueryFavoriteCountResp> first = favoriteCountList.getList().stream().findFirst();
            if (first.isPresent()) {
                QueryFavoriteCountResp queryFavoriteCountResp = first.get();
                apiProductShopInfoResp.setFollowCount(Long.valueOf(queryFavoriteCountResp.getCount()));
                apiProductShopInfoResp.setFollowFlag(queryFavoriteCountResp.getFavoriteFlag());
            }
        }
        return apiProductShopInfoResp;
    }

    public ApiShopEsCombinationResp queryMallShopList(ApiShopMallQueryReq request) {
        Long userId = request.getUserId();
        ShopEsQueryReq shopEsQueryReq = JsonUtil.copy(request, ShopEsQueryReq.class);
        shopEsQueryReq.setShopStatus(ShopEnum.AuditStatus.Open.getCode());
        shopEsQueryReq.setUserId(userId);
        List<FieldSortReq> sortList = request.getSortList();
        if (CollUtil.isEmpty(sortList)) {
            sortList = CollUtil.newArrayList();
            FieldSortReq seqFieldSortReq = new FieldSortReq();
            seqFieldSortReq.setSort(SEQ_SORT_FIELD);
            seqFieldSortReq.setIzAsc(Boolean.FALSE);
            sortList.add(seqFieldSortReq);
            FieldSortReq fieldSortReq = new FieldSortReq();
            fieldSortReq.setSort(SEQ_SORT_FIELD);
            fieldSortReq.setIzAsc(Boolean.FALSE);
            sortList.add(fieldSortReq);
            shopEsQueryReq.setSortList(sortList);
        }

        ShopEsCombinationResp shopEsCombinationResp = mallShopRemoteService.queryByShopEs(shopEsQueryReq);

        BasePageResp<ShopEsResp> shopEsPageResp = shopEsCombinationResp.getShopList();

        BasePageResp<ApiShopEsResp> resultResp = PageResultHelper.transfer(shopEsPageResp, ApiShopEsResp.class);
        if (null != resultResp && CollUtil.isNotEmpty(resultResp.getData())) {

            List<Long> shopIdList = resultResp.getData().stream().map(ApiShopEsResp::getShopId).collect(Collectors.toList());
            QueryFavoriteCountReq queryFavoriteCountReq = new QueryFavoriteCountReq();
            queryFavoriteCountReq.setShopIdList(shopIdList);
            queryFavoriteCountReq.setMemberId(request.getUserId());
            QueryFavoriteCountListResp queryFavoriteCountListResp = mallFavoriteShopRemoteService.queryCount(queryFavoriteCountReq);
            List<QueryFavoriteCountResp> favoriteCountList = queryFavoriteCountListResp.getList();

            List<ApiShopEsResp> shopList = resultResp.getData();

            shopList.parallelStream().forEach(obj -> {
                // 处理，查询热销商品
                Long shopId = obj.getShopId();
                QueryHotSaleProductReq shopProductReq = new QueryHotSaleProductReq();
                shopProductReq.setShopId(shopId);
                shopProductReq.setUserId(userId);
                MallShopProductResp mallShopProductResp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.queryMallShopProduct(shopProductReq));
                if (null != mallShopProductResp && CollUtil.isNotEmpty(mallShopProductResp.getProductList())) {
                    obj.setProductList(JsonUtil.copyList(mallShopProductResp.getProductList(), ApiTradeProductDto.class));
                    obj.setProductCount(mallShopProductResp.getProductCount());
                } else {
                    obj.setProductList(CollUtil.newArrayList());
                    obj.setProductCount(0L);
                }
                if (CollUtil.isNotEmpty(favoriteCountList)) {
                    favoriteCountList.stream().filter(favoriteCount -> favoriteCount.getShopId().equals(shopId)).findFirst().ifPresent(favoriteCount -> {
                        obj.setFavoriteFlag(favoriteCount.getFavoriteFlag());
                        obj.setFavoriteCount(favoriteCount.getCount());
                    });
                }

                // 为空或者为0，默认为5
                BigDecimal packMark = obj.getPackMark();
                if (null == packMark || packMark.compareTo(BigDecimal.ZERO) == 0) {
                    obj.setPackMark(new BigDecimal("5"));
                }
                BigDecimal serviceMark = obj.getServiceMark();
                if (null == serviceMark || serviceMark.compareTo(BigDecimal.ZERO) == 0) {
                    obj.setServiceMark(new BigDecimal("5"));
                }
                BigDecimal comprehensiveMark = obj.getComprehensiveMark();
                if (null == comprehensiveMark || comprehensiveMark.compareTo(BigDecimal.ZERO) == 0) {
                    obj.setComprehensiveMark(new BigDecimal("5"));
                }
            });
        }

        ApiShopEsCombinationResp apiShopEsCombinationResp = new ApiShopEsCombinationResp();
        apiShopEsCombinationResp.setShopList(resultResp);
        apiShopEsCombinationResp.setShopBrandIds(shopEsCombinationResp.getShopBrandIds());
        apiShopEsCombinationResp.setCategoryIds(shopEsCombinationResp.getCategoryIds());
        apiShopEsCombinationResp.setShopBrandList(JsonUtil.copyList(shopEsCombinationResp.getShopBrandList(), ApiShopBrandEsResp.class));
        apiShopEsCombinationResp.setBusinessCategoryList(JsonUtil.copyList(shopEsCombinationResp.getBusinessCategoryList(), ApiBusinessCategoryEsResp.class));

        // 查询出店铺的经营类目 并且转换成一级类目
        /*List<BusinessCategoryEsResp> businessCategoryList = shopEsCombinationResp.getBusinessCategoryList();
        List<Long> categoryIds = Optional.ofNullable(businessCategoryList)
                .orElse(Collections.emptyList())
                .stream()
                .map(BusinessCategoryEsResp::getCategoryId)
                .collect(Collectors.toList());
        List<CategoryResp> firstCategoryList = categoryRemoteService.getFirstCategoryList(categoryIds);
        apiShopEsCombinationResp.setBusinessCategoryList(firstCategoryList.stream().map(this::convertToApiBusinessCategoryEsResp).collect(Collectors.toList()));*/
        return apiShopEsCombinationResp;
    }


    public BaseResp addShopUv(BaseIdReq cmdShopUvReq, LoginMemberDto userInfo) {
        // 1.判断是否登录
        if (userInfo == null || TracerUtil.getMemberDto() == null) {
            return BaseResp.of();
        }
        // 2.已经登录，增加店铺uv
        // 获取当日时间yyyyMMdd
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String key = StrUtil.format(ShopSquirrelConstant.SHOP_UV_KEY, today, cmdShopUvReq.getId());
        stringRedisTemplate.opsForValue().increment(key);
        return BaseResp.of();
    }

    public ApiShopIntroductionResp shopIntroduction(BaseIdReq req, LoginMemberDto userInfo) {
        ShopIntroductionResp shopIntroductionResp = mallShopRemoteService.queryShopIntroduction(req);
        ApiShopIntroductionResp apiShopIntroductionResp = JsonUtil.copy(shopIntroductionResp, ApiShopIntroductionResp.class);
        // 查询收藏数量
        QueryFavoriteCountReq queryFavoriteCountReq = new QueryFavoriteCountReq();
        queryFavoriteCountReq.setShopIdList(Collections.singletonList(req.getId()));
        if (userInfo != null && null != userInfo.getId()) {
            queryFavoriteCountReq.setMemberId(userInfo.getId());
        }
        QueryFavoriteCountListResp favoriteCountList = mallFavoriteShopRemoteService.queryCount(queryFavoriteCountReq);
        if (favoriteCountList != null && CollUtil.isNotEmpty(favoriteCountList.getList())) {
            Optional<QueryFavoriteCountResp> first = favoriteCountList.getList().stream().findFirst();
            if (first.isPresent()) {
                QueryFavoriteCountResp queryFavoriteCountResp = first.get();
                apiShopIntroductionResp.setFollowCount(Long.valueOf(queryFavoriteCountResp.getCount()));
                apiShopIntroductionResp.setFollowFlag(queryFavoriteCountResp.getFavoriteFlag());
            }
        }
        apiShopIntroductionResp.setMemberLogo(mallLionConfigClient.getShopDefaultImage());
        return apiShopIntroductionResp;
    }

    public String createQrCode(ApiCmdCreateQRReq cmdCreateQRReq) {
        return mallShopRemoteService.createQrCode(JsonUtil.copy(cmdCreateQRReq, CmdCreateQRReq.class));
    }
}
