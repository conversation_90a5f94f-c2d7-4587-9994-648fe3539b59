package com.sankuai.shangou.seashop.seller.core.thrift.impl.system.article;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ArticleQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseArticleRes;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiBaseArticleRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sellerApi/apiArticle")
public class SellerApiArticleController {

    @Resource
    private ArticleQueryFeign articleQueryFeign;

    @PostMapping(value = "/getArticleById", consumes = "application/json")
    public ResultDto<ApiBaseArticleRes> getArticleById(@RequestBody BaseIdReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getArticleById", query, req -> {
            BaseArticleRes result = ThriftResponseHelper.executeThriftCall(() ->
                    articleQueryFeign.getArticleById(JsonUtil.copy(req, BaseReq.class)));
            return JsonUtil.copy(result, ApiBaseArticleRes.class);
        });
    }


}
