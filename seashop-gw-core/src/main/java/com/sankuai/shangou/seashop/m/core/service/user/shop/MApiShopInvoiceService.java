package com.sankuai.shangou.seashop.m.core.service.user.shop;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;

public interface MApiShopInvoiceService {
    /**
     * 查询自营店铺发票
     *
     * @return 返回自营店铺发票设置
     */
    QueryShopInvoiceResp querySelfShopInvoice();

    /**
     * 修改自营店铺发票
     *
     * @param req 请求参数
     * @return 基础响应
     */
    BaseResp saveShopInvoice(SaveShopInvoiceReq req);
}
