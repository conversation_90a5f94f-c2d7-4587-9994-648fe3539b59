package com.sankuai.shangou.seashop.m.core.service.export.model;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ExportTaskBo {

    /**
     * 主键id
     */
    private String taskId;
    /**
     * 业务模块
     */
    private Integer moduleType;
    /**
     * 业务模块描述
     */
    private String moduleTypeName;
    /**
     * 查询条件
     */
    private String paramContent;
    /**
     * 进度。0：未开始；10：进行中；100：已完成；-1：失败
     */
    private Integer status;
    /**
     * 耗时
     */
    private Integer cost;
    /**
     * 总行数
     */
    private Integer dataCount;
    /**
     * 异常
     */
    private String exception;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 完成时间
     */
    private Date completeTime;
    /**
     * 操作者ID
     */
    private Long operatorId;
    /**
     * 操作人
     */
    private String operatorName;
    /**
     * 排队任务数
     */
    private long waitCount;

}
