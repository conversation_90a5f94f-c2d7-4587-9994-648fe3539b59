package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiInvoiceTitleCmdReq;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.mall.core.service.user.account.MallApiInvoiceTitleService;

/**
 * @description: 发票抬头服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mallApi/apiInvoiceTitle")
public class MallApiInvoiceTitleCmdController {
    @Resource
    private MallApiInvoiceTitleService mallApiInvoiceTitleService;

    @PostMapping(value = "/save", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> save(@RequestBody ApiInvoiceTitleCmdReq cmdReq) {
        

        cmdReq.setUserId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.responseInvoke("save", cmdReq, req -> mallApiInvoiceTitleService.save(req));
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> delete(@RequestBody BaseIdReq cmdReq) {
        

        return ThriftResponseHelper.responseInvoke("delete", cmdReq, req -> mallApiInvoiceTitleService.delete(req, TracerUtil.getMemberDto()));
    }
}
