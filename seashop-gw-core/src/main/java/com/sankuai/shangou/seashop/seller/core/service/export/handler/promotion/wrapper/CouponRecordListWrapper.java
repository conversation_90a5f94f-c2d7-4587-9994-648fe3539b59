package com.sankuai.shangou.seashop.seller.core.service.export.handler.promotion.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerCouponRecordRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.promotion.eo.CouponRecordListEo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiCouponRecordQueryReq;

import cn.hutool.core.collection.CollUtil;

/**
 * @author: lhx
 * @date: 2024/2/23/023
 * @description:
 */
public class CouponRecordListWrapper extends PageExportWrapper<CouponRecordListEo, ApiCouponRecordQueryReq> {

    private final SellerCouponRecordRemoteService sellerCouponRecordRemoteService;

    public CouponRecordListWrapper(SellerCouponRecordRemoteService sellerCouponRecordRemoteService) {
        this.sellerCouponRecordRemoteService = sellerCouponRecordRemoteService;
    }

    @Override
    public List<CouponRecordListEo> getPageList(ApiCouponRecordQueryReq param) {
        CouponRecordQueryReq queryReq = JsonUtil.copy(param, CouponRecordQueryReq.class);
        BasePageResp<CouponRecordSimpleResp> pageList = sellerCouponRecordRemoteService.pageList(queryReq);
        if (null == pageList || CollUtil.isEmpty(pageList.getData())) {
            return null;
        }
        List<CouponRecordListEo> couponRecordListEos = JsonUtil.copyList(pageList.getData(), CouponRecordListEo.class);
        return couponRecordListEos;
    }
}
