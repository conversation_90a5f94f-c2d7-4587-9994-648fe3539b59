package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.ShopFlashSaleConfigReq;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerFlashSaleRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiShopFlashSaleConfigReq;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiFlashSale")
public class SellerApiFlashSaleConfigCmdController {

    @Resource
    private SellerFlashSaleRemoteService sellerFlashSaleRemoteService;

    @PostMapping(value = "/updateShopConfig", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateShopConfig(@RequestBody ApiShopFlashSaleConfigReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateShopConfig", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            return sellerFlashSaleRemoteService.updateShopConfig(JsonUtil.copy(req, ShopFlashSaleConfigReq.class));
        });
    }
}
