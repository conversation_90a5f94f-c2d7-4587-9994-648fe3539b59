package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiCashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.enums.finance.CashDepositOperatorTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositDetailQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiCashDepositDetail")
public class MApiCashDepositDetailQueryController {

    @Resource
    private CashDepositDetailQueryFeign cashDepositDetailQueryFeign;

    private static final Integer DAY_HOUR = 24;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiCashDepositDetailResp>> pageList(@RequestBody ApiCashDepositDetailQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.setSourceFrom(1);
            req.checkParameter();
            CashDepositDetailQueryReq cashDepositDetailQueryReq = JsonUtil.copy(req, CashDepositDetailQueryReq.class);
            BasePageResp<CashDepositDetailResp> cashDepositDetailPage = ThriftResponseHelper.executeThriftCall(() ->
                    cashDepositDetailQueryFeign.pageList(cashDepositDetailQueryReq));
            return PageResultHelper.transfer(cashDepositDetailPage, ApiCashDepositDetailResp.class, resp -> {
                if (resp.getOperatorType().equals(CashDepositOperatorTypeEnum.PAY.getType())) {
                    if (null != resp.getAddDate()) {
                        // 获取 addDate 和 当前时间的间隔天数，如果有小数，向上取整
                        long between = DateUtil.between(resp.getAddDate(), DateUtil.date(), DateUnit.HOUR, true);
                        long days = between / DAY_HOUR;
                        if (between % DAY_HOUR > 0) {
                            days++;
                        }
                        resp.setPaymentDays(Integer.valueOf(days + ""));
                    }
                }
            });
        });
    }
}
