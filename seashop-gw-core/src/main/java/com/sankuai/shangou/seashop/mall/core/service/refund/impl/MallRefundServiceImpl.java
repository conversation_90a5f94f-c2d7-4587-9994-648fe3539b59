package com.sankuai.shangou.seashop.mall.core.service.refund.impl;

import java.util.Date;
import java.util.UUID;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.refund.RefundExportResp;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.handler.ExecuteResult;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.base.export.handler.ExportTaskHandler;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.mall.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.mall.core.service.refund.MallRefundService;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundUserQueryTabEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MallRefundServiceImpl implements MallRefundService {

    @Resource
    private ExportTaskHandler exportTaskHandler;
    @Resource
    @Lazy
    private S3plusStorageService s3plusStorageService;
    @Value("${s3plus.hostName:}")
    private String s3plusHostName;

    @Resource
    private StorageClient storageClient;

    @Override
    public RefundExportResp exportRefund(UserQueryRefundReq queryReq) {
        ExportTask task = new ExportTask();
        task.setTaskId(UUID.randomUUID().toString());
        if (RefundUserQueryTabEnum.APPLY_REFUND.equals(queryReq.getTab())) {
            task.setTaskType(ExportTaskType.APPLY_REFUND.getType());
        }
        else if (RefundUserQueryTabEnum.APPLY_RETURN.equals(queryReq.getTab())) {
            task.setTaskType(ExportTaskType.APPLY_RETURN.getType());
        }
        else {
            throw new BusinessException("售后查询类型错误");
        }
        task.setExecuteParam(JsonUtil.toJsonString(queryReq));
        task.setTaskDate(new Date());
        log.info("【售后】导出订单参数:{}", JsonUtil.toJsonString(task));
        ExecuteResult exportResult = exportTaskHandler.execute(task);
        log.info("【售后】导出订单结果:{}", JsonUtil.toJsonString(exportResult));
        if (!exportResult.isSuccess()) {
            throw new BusinessException("售后导出失败");
        }

        String downloadUrl = storageClient.formatUrl(exportResult.getFilePath());
        return new RefundExportResp(downloadUrl);
    }
}
