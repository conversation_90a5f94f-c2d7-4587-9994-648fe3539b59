package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryApplyRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiQueryApplyCategoryAndFormDataReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiQueryBusinessCategoryApplyDetailReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiQueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiBusinessCategoryApplyDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiBusinessCategoryApplyResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiQueryLastCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryApplyCategoryAndFormDataReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyDetailReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryLastCategoryResp;

@RestController
@RequestMapping("/sellerApi/apiBusinessCategoryApply")
public class SellerApiBusinessCategoryApplyQueryController {

    @Resource
    private SellerBusinessCategoryApplyRemoteService sellerBusinessCategoryApplyRemoteService;


    @PostMapping(value = "/queryPageForSeller", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiBusinessCategoryApplyResp>> queryPageForSeller(@RequestBody ApiQueryBusinessCategoryApplyPageReq queryBusinessCategoryApplyPageReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryBusinessCategoryApplyForShop", queryBusinessCategoryApplyPageReq, req -> {

            QueryBusinessCategoryApplyPageReq remoteReq = JsonUtil.copy(req, QueryBusinessCategoryApplyPageReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<BusinessCategoryApplyResp> resp = sellerBusinessCategoryApplyRemoteService.queryPageForSeller(remoteReq);
            return PageResultHelper.transfer(resp, ApiBusinessCategoryApplyResp.class);
        });
    }

    @PostMapping(value = "/queryDetailForSeller", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiBusinessCategoryApplyDetailResp> queryDetailForSeller(@RequestBody ApiQueryBusinessCategoryApplyDetailReq request) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryDetailForSeller", request, req -> {

            QueryBusinessCategoryApplyDetailReq remoteReq = new QueryBusinessCategoryApplyDetailReq();
            remoteReq.setId(req.getId());
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            BusinessCategoryApplyDetailResp resp = sellerBusinessCategoryApplyRemoteService.queryDetailForSeller(remoteReq);
            return JsonUtil.copy(resp, ApiBusinessCategoryApplyDetailResp.class);
        });
    }

    @PostMapping(value = "/queryApplyCategoryAndFormData", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiQueryLastCategoryResp> queryApplyCategoryAndFormData(@RequestBody ApiQueryApplyCategoryAndFormDataReq request) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryApplyCategoryAndFormData", request, req -> {

            QueryApplyCategoryAndFormDataReq remoteReq = new QueryApplyCategoryAndFormDataReq();
            remoteReq.setCategoryId(req.getCategoryId());
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            QueryLastCategoryResp resp = sellerBusinessCategoryApplyRemoteService.queryApplyCategoryAndFormData(remoteReq);
            return JsonUtil.copy(resp, ApiQueryLastCategoryResp.class);
        });
    }
}
