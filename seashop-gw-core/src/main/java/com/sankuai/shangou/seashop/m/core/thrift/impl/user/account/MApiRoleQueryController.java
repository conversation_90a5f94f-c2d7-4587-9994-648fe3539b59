package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiRoleService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryRoleReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiRoleRespList;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiRole")
public class MApiRoleQueryController {
    @Resource
    private MApiRoleService mApiRoleService;

    @PostMapping(value = "/queryRoleList", consumes = "application/json")
    public ResultDto<ApiRoleRespList> queryRoleList(@RequestBody ApiQueryRoleReq queryRoleReq) {
        return ThriftResponseHelper.responseInvoke("queryRoleList", queryRoleReq, req -> mApiRoleService.queryRoleList(queryRoleReq));
    }
}
