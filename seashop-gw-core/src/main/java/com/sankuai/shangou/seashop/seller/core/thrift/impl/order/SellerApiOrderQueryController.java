package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderWayBillReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerOrderReq;
import com.sankuai.shangou.seashop.seller.common.remote.order.SellerOrderRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.order.SellerOrderService;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.order.ApiOrderInfoDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQueryOrderDistributionReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQueryOrderWayBillReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQuerySellerOrderReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.order.ApiOrderDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.order.ApiOrderOperationLogResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.order.ApiOrderWayBillListResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sellerApi/apiOrder")
@Slf4j
public class SellerApiOrderQueryController {


    @Resource
    private SellerOrderService sellerOrderService;
    @Resource
    private SellerOrderRemoteService sellerOrderRemoteService;
    @Resource
    private OrderQueryFeign orderQueryFeign;

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/pageQueryUserOrder", consumes = "application/json")
    public ResultDto<BasePageResp<ApiOrderInfoDto>> pageQueryUserOrder(@RequestBody ApiQuerySellerOrderReq queryReq) {
        List<Integer> paymentTypeList = queryReq.getPaymentTypeList();
        if(CollUtil.isNotEmpty(paymentTypeList)){
            queryReq.setPaymentList(paymentTypeList);
            queryReq.setPaymentTypeList(null);
        }
        return ThriftResponseHelper.responseInvoke("【订单】供应商分页查询订单列表", queryReq,
            func -> {
                QuerySellerOrderReq req = JsonUtil.copy(queryReq, QuerySellerOrderReq.class);
                req.setShopId(TracerUtil.getShopDto().getShopId());
                req.setQueryFrom(OrderQueryFromEnum.SELLER_PC);
                BasePageResp<OrderInfoDto> orderInfoDtoBasePageResp = ThriftResponseHelper.executeThriftCall(() ->
                        orderQueryFeign.pageQuerySellerOrder(req));
                return PageResultHelper.transfer(orderInfoDtoBasePageResp, ApiOrderInfoDto.class);
            });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @GetMapping(value = "/queryDetail")
    public ResultDto<ApiOrderDetailResp> queryDetail(@RequestParam String orderId) {

        return ThriftResponseHelper.responseInvoke("【订单】供应商查询订单详情", orderId,
            func -> JsonUtil.copy(sellerOrderRemoteService.queryDetail(orderId, TracerUtil.getShopDto().getShopId()), ApiOrderDetailResp.class)
        );
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/queryOrderWayBill", consumes = "application/json")
    public ResultDto<ApiOrderWayBillListResp> queryOrderWayBill(@RequestBody ApiQueryOrderWayBillReq queryReq) throws TException {

        

        return ThriftResponseHelper.responseInvoke("【订单】供应商查询订单物流信息", queryReq, func -> {
            queryReq.setShopId(TracerUtil.getShopDto().getShopId());
            return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() ->
                    orderQueryFeign.queryOrderWayBill(JsonUtil.copy(queryReq, QueryOrderWayBillReq.class))), ApiOrderWayBillListResp.class);
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/export", consumes = "application/json")
    public ResultDto<BaseResp> export(@RequestBody  ApiQuerySellerOrderReq queryReq) throws TException {

        

        LoginShopDto shop = TracerUtil.getShopDto();
        Long userId = shop.getManagerId();
        log.info("【订单】供应商导出订单列表, userId={}, queryReq={}", userId, JsonUtil.toJsonString(queryReq));
        UserDto user = new UserDto();
        user.setUserId(userId);
        user.setUserName(shop.getManagerName());
        // 要设置shopId，供应商导出自己店铺的
        queryReq.setShopId(shop.getShopId());
        sellerOrderService.export(queryReq, user);
        return ResultDto.newWithData(BaseResp.of());
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/exportInvoice", consumes = "application/json")
    public ResultDto<BaseResp> exportInvoice(@RequestBody ApiQuerySellerOrderReq request) throws TException {

        

        sellerOrderService.exportOrderInvoice(request, TracerUtil.getShopDto());
        return ResultDto.newWithData(BaseResp.of());
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/exportOrderDistribution", consumes = "application/json")
    public ResultDto<BaseResp> exportOrderDistribution(@RequestBody ApiQueryOrderDistributionReq request) throws TException {

        

        sellerOrderService.exportOrderDistribution(request, TracerUtil.getShopDto());
        return ResultDto.newWithData(BaseResp.of());
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/exportOrderProductDistribution", consumes = "application/json")
    public ResultDto<BaseResp> exportOrderProductDistribution(@RequestBody ApiQueryOrderDistributionReq request) throws TException {

        

        sellerOrderService.exportOrderProductDistribution(request, TracerUtil.getShopDto());
        return ResultDto.newWithData(BaseResp.of());
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @GetMapping(value = "/queryOrderLog")
    public ResultDto<List<ApiOrderOperationLogResp>> queryOrderLog(@RequestParam String orderId) {
        return ThriftResponseHelper.responseInvoke("【订单】查询订单操作日志", orderId, func -> {
            // 业务逻辑处理
            return JsonUtil.copyList(ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryOrderLog(orderId))
                    , ApiOrderOperationLogResp.class);
        });
    }
}
