package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.*;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.order.ApiDealMComplaintReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.order.ApiQueryMComplaintReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.order.ApiQueryOrderComplaintResp;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.DealMComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintRespPage;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description：投诉维权平台接口thrift服务
 * @author： liweisong
 * @create： 2023/11/24 10:10
 */
@RestController
@RequestMapping("/mApi/apiMComplaint")
public class MApiMComplaintController {

    @Resource
    private ComplaintCmdFeign complaintCmdFeign;
    @Resource
    private ComplaintQueryFeign complaintQueryFeign;

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/dealMComplaint", consumes = "application/json")
    public ResultDto<BaseResp> dealMComplaint(@RequestBody ApiDealMComplaintReq dealMComplaintReq) throws TException {
        return ThriftResponseHelper.responseInvoke("dealMComplaint", dealMComplaintReq, req -> {
            req.checkParameter();
            DealMComplaintReq bean = JsonUtil.copy(req, DealMComplaintReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> complaintCmdFeign.dealMComplaint(bean));
        });
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/pageMComplaint", consumes = "application/json")
    public ResultDto<BasePageResp<ApiQueryOrderComplaintResp>> pageMComplaint(@RequestBody ApiQueryMComplaintReq queryMComplaintReq) throws TException {
        return ThriftResponseHelper.responseInvoke("pageMComplaint", queryMComplaintReq, req -> {
            req.checkParameter();
            QueryMComplaintReq bean = JsonUtil.copy(req, QueryMComplaintReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            QueryOrderComplaintRespPage page = ThriftResponseHelper.executeThriftCall(() -> complaintQueryFeign.pageMComplaint(bean));
            return PageResultHelper.transfer(page.getPageResp(), ApiQueryOrderComplaintResp.class);
        });
    }
}
