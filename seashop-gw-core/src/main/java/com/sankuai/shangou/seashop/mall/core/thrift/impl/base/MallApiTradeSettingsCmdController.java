package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiTradeSiteSettingsResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.TradeSettingsQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.TradeSiteSettingsResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * @author： liweisong
 * @create： 2023/11/22 17:22
 */
@RestController
@RequestMapping("/mallApi/apiTradeSettings")
public class MallApiTradeSettingsCmdController {

    @Resource
    private TradeSettingsQueryFeign tradeSettingsQueryFeign;

    @GetMapping(value = "/queryTradeSiteSetting")
    public ResultDto<ApiTradeSiteSettingsResp> queryTradeSiteSetting() throws TException {
        return ThriftResponseHelper.responseInvoke("queryTradeSiteSetting", null, req -> {
            TradeSiteSettingsResp tradeSiteSettingsResp = ThriftResponseHelper.executeThriftCall(() -> tradeSettingsQueryFeign.queryTradeSiteSetting());
            return JsonUtil.copy(tradeSiteSettingsResp, ApiTradeSiteSettingsResp.class);
        });
    }
}
