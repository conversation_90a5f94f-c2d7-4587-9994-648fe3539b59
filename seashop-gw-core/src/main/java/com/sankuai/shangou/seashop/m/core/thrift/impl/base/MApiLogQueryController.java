package com.sankuai.shangou.seashop.m.core.thrift.impl.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.dto.BaseOperationLogDto;
import com.sankuai.shangou.seashop.base.boot.log.producer.LogMafkaProducer;
import com.sankuai.shangou.seashop.base.boot.response.*;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.LogQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogDetailReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogMQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiLogDetailReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiLogMQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiLogDetailResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiLogQueryResp;
import com.sankuai.shangou.seashop.user.thrift.account.ManagerQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:12
 */
@Slf4j
@RestController
@RequestMapping("/mApi/apiLog")
public class MApiLogQueryController {

    @Resource
    private LogQueryFeign logQueryFeign;
    @Resource
    private ManagerQueryFeign managerQueryFeign;

    @Resource
    private LogMafkaProducer logMafkaProducer;

    @PostMapping("/mockMessageSending")
    public ResultDto<BaseResp> mockMessageSending(@RequestBody String message) throws TException {
        return ThriftResponseHelper.responseInvoke("mockMessageSending", null, req -> {
            BaseOperationLogDto bean = BeanUtil.toBean(message, BaseOperationLogDto.class);
            try {
                log.info("测试消息发送：{}", message);
                logMafkaProducer.sendMessage(bean);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/pageMBaseLog", consumes = "application/json")
    public ResultDto<BasePageResp<ApiLogQueryResp>> pageMBaseLog(@RequestBody ApiLogMQueryReq logMQueryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("pageMBaseLog", logMQueryReq, req -> {
            req.checkParameter();
            LogMQueryReq bean = JsonUtil.copy(req, LogMQueryReq.class);
            if (!StringUtils.isEmpty(bean.getUserName())) {
                QueryManagerReq queryManagerPageReq = new QueryManagerReq();
                queryManagerPageReq.setManagerName(bean.getUserName());
                ManagerResp managerResp = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryManager(queryManagerPageReq));
                if (Objects.isNull(managerResp)) {
                    return PageResultHelper.defaultEmpty(req.buildPage());
                }
                bean.setUserName(managerResp.getId().toString());
            }
            BasePageResp<ApiLogQueryResp> result = PageResultHelper.transfer(ThriftResponseHelper.executeThriftCall(() -> logQueryFeign.pageMBaseLog(bean)),
                    ApiLogQueryResp.class);
            List<Long> managerIds = result.getData().stream().map(ApiLogQueryResp::getOperationUserId).collect(Collectors.toList());
            QueryManagerPageReq queryManagerPageReq = new QueryManagerPageReq();
            queryManagerPageReq.setManagerIds(managerIds);
            BasePageResp<ManagerResp> resp = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryManagerPage(queryManagerPageReq));
            List<ManagerResp> managerRespList = resp.getData();
            Map<Long, ManagerResp> map = managerRespList.stream().collect(Collectors.toMap(ManagerResp::getId, Function.identity(), (v1, v2) -> v1));
            result.getData().forEach(apiLogQueryResp -> {
                ManagerResp managerResp = map.get(apiLogQueryResp.getOperationUserId());
                if (Objects.isNull(managerResp)) {
                    return;
                }
                apiLogQueryResp.setOperationUserAccount(managerResp.getUserName());
                apiLogQueryResp.setOperationUserName(managerResp.getUserName());
            });
            return result;
        });
    }

    @PostMapping(value = "/queryBaseLogDetail", consumes = "application/json")
    public ResultDto<ApiLogDetailResp> queryBaseLogDetail(@RequestBody ApiLogDetailReq logDetailReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBaseLogDetail", logDetailReq, req -> {
            req.checkParameter();
            LogDetailReq bean = JsonUtil.copy(req, LogDetailReq.class);
            ApiLogDetailResp resp = JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> logQueryFeign.queryBaseLogDetail(bean)), ApiLogDetailResp.class);
            fillUserInfo(resp);
            return resp;
        });
    }

    /**
     * 填充用户信息
     */
    private void fillUserInfo(ApiLogDetailResp resp) {
        if (resp.getOperationUserId() == null) {
            return;
        }
        QueryManagerPageReq queryManagerPageReq = new QueryManagerPageReq();
        queryManagerPageReq.setManagerIds(Arrays.asList(resp.getOperationUserId()));
        List<ManagerResp> managerRespList = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryManagerPage(queryManagerPageReq)).getData();

        if (CollUtil.isNotEmpty(managerRespList)) {
            resp.setOperationUserAccount(managerRespList.get(0).getUserName());
            resp.setOperationUserName(managerRespList.get(0).getUserName());
        } else {
            resp.setOperationUserAccount("账号已删除");
            resp.setOperationUserName("用户已删除");
        }
    }
}
