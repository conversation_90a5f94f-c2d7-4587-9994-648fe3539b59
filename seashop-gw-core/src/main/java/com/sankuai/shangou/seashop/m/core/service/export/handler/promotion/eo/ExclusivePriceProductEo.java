package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2023/12/26/026
 * @description:
 */
@Getter
@Setter
public class ExclusivePriceProductEo {

    @ExcelProperty(value = "商品ID")
    private Long productId;

    @ExcelProperty(value = "商品名称")
    private String productName;

    @ExcelIgnore
    @ExcelProperty(value = "主键ID")
    private Long id;

    @ExcelIgnore
    @ExcelProperty(value = "活动ID")
    private Long activeId;

    @ExcelIgnore
    @ExcelProperty(value = "skuId")
    private String skuId;

    @ExcelProperty(value = "sku自增id")
    private Long skuAutoId;

    @ExcelProperty(value = "sku名称")
    private String skuName;

    @ExcelProperty(value = "商城价格")
    private BigDecimal mallPrice;

    @ExcelProperty(value = "会员id")
    private Long memberId;

    @ExcelProperty(value = "会员名称")
    private String userName;

    @ExcelProperty(value = "专享价格")
    private BigDecimal price;
}
