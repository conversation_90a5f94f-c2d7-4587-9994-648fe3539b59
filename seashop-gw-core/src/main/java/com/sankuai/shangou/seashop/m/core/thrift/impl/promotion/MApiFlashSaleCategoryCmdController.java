package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiFlashSaleCategoryAddReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleCategoryAddReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCategoryCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFlashSaleCategory")
public class MApiFlashSaleCategoryCmdController {

    @Resource
    private FlashSaleCategoryCmdFeign flashSaleCategoryCmdFeign;

    @PostMapping(value = "/add", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> add(@RequestBody ApiFlashSaleCategoryAddReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("add", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> flashSaleCategoryCmdFeign.add(JsonUtil.copy(req, FlashSaleCategoryAddReq.class)));
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> delete(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("delete", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> flashSaleCategoryCmdFeign.delete(req));
        });
    }
}
