package com.sankuai.shangou.seashop.m.core.service.common.impl;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.common.MHomeService;
import com.sankuai.shangou.seashop.m.thrift.core.response.common.RadiusNumResp;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryApplyQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryWaitFinishContractNumReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopUserCountResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/02/19 13:52
 */
@Service
public class MHomeServiceImpl implements MHomeService {

    @Resource
    private BusinessCategoryApplyQueryFeign businessCategoryApplyQueryFeign;
    @Resource
    private ShopQueryFeign shopQueryFeign;


    @Override
    public RadiusNumResp queryRadiusNum() {
        RadiusNumResp radiusNumResp = new RadiusNumResp();
        // todo 后续添加别的角标数据
        QueryWaitFinishContractNumReq req = new QueryWaitFinishContractNumReq();
        req.setShopId(null);
        Integer applyWaitFinishNum = ThriftResponseHelper.executeThriftCall(() -> businessCategoryApplyQueryFeign.queryWaitFinishContractNum(req)).getApplyWaitFinishNum();
        radiusNumResp.setApplyWaitFinishNum(applyWaitFinishNum);
        ShopUserCountResp countResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.countShopUser());
        radiusNumResp.setResidentAudit(countResp.getWaitAuditSupplierCount());
        radiusNumResp.setShopCount(countResp.getShopCount());
        return radiusNumResp;
    }
}
