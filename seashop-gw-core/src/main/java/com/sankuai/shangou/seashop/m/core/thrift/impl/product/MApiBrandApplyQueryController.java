package com.sankuai.shangou.seashop.m.core.thrift.impl.product;


import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryBrandApplyReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiBrandApplyDetailResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiBrandApplyDto;
import com.sankuai.shangou.seashop.product.thrift.core.BrandApplyQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.BrandApplyDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandApplyDto;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/mApi/apiBrandApply")
public class MApiBrandApplyQueryController {

    @Resource
    private BrandApplyQueryFeign brandApplyQueryFeign;

    @PostMapping(value = "/queryBrandApplyForPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiBrandApplyDto>> queryBrandApplyForPage(@RequestBody ApiQueryBrandApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandApplyForPage", request, req -> {
            req.checkParameter();

            QueryBrandApplyReq brandApplyReq = JsonUtil.copy(req, QueryBrandApplyReq.class);
            brandApplyReq.setAuditStatus(BrandEnum.AuditStatusEnum.getByCode(req.getAuditStatusCode()));
            BasePageResp<BrandApplyDto> pageResult = ThriftResponseHelper.executeThriftCall(() -> brandApplyQueryFeign.queryBrandApplyForPage(brandApplyReq));
            return PageResultHelper.transfer(pageResult, ApiBrandApplyDto.class);
        });
    }

    @PostMapping(value = "/queryBrandApplyDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiBrandApplyDetailResp> queryBrandApplyDetail(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandApplyDetail", request, req -> {
            req.checkParameter();

            QueryBrandApplyDetailReq brandApplyReq = new QueryBrandApplyDetailReq();
            brandApplyReq.setId(req.getId());
            BrandApplyDetailResp detail = ThriftResponseHelper.executeThriftCall(() -> brandApplyQueryFeign.queryBrandApplyDetailForPlatForm(brandApplyReq));
            return ApiBrandApplyDetailResp.builder().result(JsonUtil.copy(detail.getResult(), ApiBrandApplyDto.class)).build();
        });
    }
}
