package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.utils.ValidatorHelper;
import com.sankuai.shangou.seashop.seller.common.remote.SellerCustomerServiceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCustomerServiceCmdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiLoginHiChatReq;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Add;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Delete;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Update;

import lombok.extern.slf4j.Slf4j;

/**
 * @description：供应商客服新增修改删除接口
 * @author： LXH
 * @create： 2023/11/27 9:13
 */
@RestController
@RequestMapping("/sellerApi/apiCustomerService")
@Slf4j
public class SellerApiCustomerServiceCmdController {
    @Resource
    SellerCustomerServiceRemoteService sellerCustomerServiceRemoteService;


    @PostMapping(value = "/add", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> add(@RequestBody ApiCustomerServiceCmdReq customerServiceCmdReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("add", customerServiceCmdReq, func -> {
            customerServiceCmdReq.setShopId(TracerUtil.getShopDto().getShopId());
            // 参数校验
            ValidatorHelper.validate(customerServiceCmdReq, Add.class);
            // 参数对象转换
            return sellerCustomerServiceRemoteService.add(customerServiceCmdReq);
        });
    }

    @PostMapping(value = "/update", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> update(@RequestBody ApiCustomerServiceCmdReq customerServiceCmdReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("update", customerServiceCmdReq, func -> {
            customerServiceCmdReq.setShopId(TracerUtil.getShopDto().getShopId());
            // 参数校验
            ValidatorHelper.validate(customerServiceCmdReq, Update.class);
            // 参数对象转换
            return sellerCustomerServiceRemoteService.update(customerServiceCmdReq);
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> delete(@RequestBody ApiCustomerServiceCmdReq customerServiceCmdReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("delete", customerServiceCmdReq, func -> {
            customerServiceCmdReq.setShopId(TracerUtil.getShopDto().getShopId());
            // 参数校验
            ValidatorHelper.validate(customerServiceCmdReq, Delete.class);
            // 参数对象转换
            return sellerCustomerServiceRemoteService.delete(customerServiceCmdReq);
        });
    }


    @PostMapping(value = "/loginHiChat", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<String> loginHiChat(@RequestBody ApiLoginHiChatReq loginHiChatReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("loginHiChat", loginHiChatReq, func -> {
            // 参数校验
            ValidatorHelper.validate(loginHiChatReq);
            LoginShopDto shopDto = TracerUtil.getShopDto();
            loginHiChatReq.setShopId(shopDto.getId());
            loginHiChatReq.setManagerId(shopDto.getManagerId());
            // 参数对象转换
            return sellerCustomerServiceRemoteService.loginHiChat(loginHiChatReq);
        });
    }

    @GetMapping(value = "/getHiChatUnReadCount")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> getHiChatUnReadCount() throws TException {
        return ThriftResponseHelper.responseInvoke("getHiChatUnReadCount", null, func -> {
            Long managerId = TracerUtil.getManagerDto().getId();
            try {
                return sellerCustomerServiceRemoteService.getHiChatUnReadCount(managerId);
            } catch (Exception e) {
                log.error("HiChat未读消息查询异常", e);
                return 0L;
            }
        });
    }
}
