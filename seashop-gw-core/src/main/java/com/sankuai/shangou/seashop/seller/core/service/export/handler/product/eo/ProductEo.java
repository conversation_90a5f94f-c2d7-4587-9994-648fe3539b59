package com.sankuai.shangou.seashop.seller.core.service.export.handler.product.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.date.DateDateConverter;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/01/02 17:49
 */
@Getter
@Setter
public class ProductEo {

    @ExcelIgnore
    private Long productId;

    @ExcelProperty(value = {"商品基本信息", "商品ID"})
    private String productIdStr;

    @ExcelProperty(value = {"商品基本信息", "商品"})
    private String productName;

    @ExcelProperty(value = {"商品基本信息", "货号"})
    private String productCode;

    @ExcelProperty(value = {"商品基本信息", "一级分类"})
    private String firstCategoryName;

    @ExcelProperty(value = {"商品基本信息", "二级分类"})
    private String secondCategoryName;

    @ExcelProperty(value = {"商品基本信息", "三级分类"})
    private String thirdCategoryName;

    @ExcelProperty(value = {"商品基本信息", "店铺分类"})
    private String shopCategoryNames;

    @ExcelProperty(value = {"商品基本信息", "运费模板"})
    private String freightTemplateName;

    @ExcelProperty(value = {"商品基本信息", "是否开启阶梯价"})
    private String whetherOpenLadder;

    @ExcelProperty(value = {"商品基本信息", "起购量1"})
    private Integer minBath1;

    @ExcelProperty(value = {"商品基本信息", "阶梯价1"})
    private BigDecimal price1;

    @ExcelProperty(value = {"商品基本信息", "起购量2"})
    private Integer minBath2;

    @ExcelProperty(value = {"商品基本信息", "阶梯价2"})
    private BigDecimal price2;

    @ExcelProperty(value = {"商品基本信息", "起购量3"})
    private Integer minBath3;

    @ExcelProperty(value = {"商品基本信息", "阶梯价3"})
    private BigDecimal price3;

    @ExcelProperty(value = {"商品基本信息", "品牌"})
    private String brandName;

    @ExcelProperty(value = {"商品基本信息", "审核状态"})
    private String auditStatusDesc;

    @ExcelProperty(value = {"商品基本信息", "审核备注"})
    private String auditReason;

    @ExcelProperty(value = {"商品基本信息", "发布时间"}, converter = DateDateConverter.class)
    private Date checkTime;

    @ExcelProperty(value = {"商品基本信息", "销售状态"})
    private String saleStatusDesc;

    @ExcelProperty(value = {"商品基本信息", "市场价"})
    private BigDecimal marketPrice;

    @ExcelProperty(value = {"商品基本信息", "商城价"})
    private BigDecimal minSalePrice;

    @ExcelProperty(value = {"商品基本信息", "库存"})
    private Long totalStock;

    @ExcelProperty(value = {"商品基本信息", "计量单位"})
    private String measureUnit;



    @ExcelProperty(value = {"规格信息", "是否开启规格"})
    private String hasSku;

    @ExcelProperty(value = {"规格信息", "SKU_ID"})
    private String skuId;

    @ExcelProperty(value = {"规格信息", "规格ID"})
    private Long skuAutoId;

    @ExcelProperty(value = {"规格信息", "规格"})
    private String specName;

    @ExcelProperty(value = {"规格信息", "价格"})
    private BigDecimal salePrice;

    @ExcelProperty(value = {"规格信息", "库存"})
    private Long stock;

    @ExcelProperty(value = {"规格信息", "规格货号"})
    private String skuCode;

}
