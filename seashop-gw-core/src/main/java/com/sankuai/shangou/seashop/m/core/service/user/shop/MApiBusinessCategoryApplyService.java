package com.sankuai.shangou.seashop.m.core.service.user.shop;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiAuditBusinessCategoryApplyReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryApplyDetailReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryApplyDetailResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryApplyResp;

/**
 * @description: 供应商类目服务类
 * @author: LXH
 **/
public interface MApiBusinessCategoryApplyService {

    /**
     * 根据条件分页查询类目申请信息
     *
     * @param queryBusinessCategoryApplyPageReq 查询条件
     * @return 申请信息
     */
    BasePageResp<ApiBusinessCategoryApplyResp> queryPage(ApiQueryBusinessCategoryApplyPageReq queryBusinessCategoryApplyPageReq);

    /**
     * 查询类目申请详情
     *
     * @param req 查询条件
     * @return 申请详情
     */
    ApiBusinessCategoryApplyDetailResp queryDetail(ApiQueryBusinessCategoryApplyDetailReq req);

    /**
     * 审核类目申请
     *
     * @param req 审核条件
     * @return 审核结果
     */
    BaseResp auditBusinessCategoryApply(ApiAuditBusinessCategoryApplyReq req);
}
