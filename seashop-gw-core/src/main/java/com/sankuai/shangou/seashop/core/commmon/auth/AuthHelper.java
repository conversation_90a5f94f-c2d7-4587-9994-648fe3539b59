package com.sankuai.shangou.seashop.core.commmon.auth;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.security.handler.AuthenticationHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Component
@Slf4j
public class AuthHelper {
    @Resource
    List<AuthenticationHandler> authenticationHandlers;

    public LoginBaseDto getBaseAccountByToken(String token){
        if (StrUtil.isBlank(token)){
            throw new BusinessException(LoginErrorEnum.NO_LOGIN);
        }
//        遍历authenticationHandlers
        for (AuthenticationHandler authenticationHandler : authenticationHandlers) {
            LoginBaseDto loginBaseDto = authenticationHandler.getBaseAccountByToken(token);
            if (loginBaseDto != null) {
                return loginBaseDto;
            }
        }
        throw new BusinessException(LoginErrorEnum.NO_LOGIN);
    }

}
