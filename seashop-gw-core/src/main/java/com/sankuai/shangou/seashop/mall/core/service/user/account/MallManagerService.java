package com.sankuai.shangou.seashop.mall.core.service.user.account;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.account.ManagerQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class MallManagerService {
    @Resource
    private ManagerQueryFeign managerQueryFeign;

    public ManagerResp queryManager(QueryManagerReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> {
            // 业务逻辑处理
            return managerQueryFeign.queryManager(req);
        });
    }

}
