package com.sankuai.shangou.seashop.m.core.thrift.impl.pay;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.pay.ApiBillDownloadReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.pay.ApiBillDownloadResp;
import com.sankuai.shangou.seashop.pay.thrift.core.request.BillDownloadReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.BillDownloadResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.PayQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiPay")
public class MApiPayQueryController {

    @Resource
    private PayQueryFeign payQueryFeign;

    @PostMapping(value = "/billDownload", consumes = "application/json")
    public ResultDto<ApiBillDownloadResp> billDownload(@RequestBody ApiBillDownloadReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("billDownload", request, req -> {
            req.checkParameter();
            BillDownloadReq billDownloadReq = JsonUtil.copy(req, BillDownloadReq.class);
            BillDownloadResp billDownloadResp = ThriftResponseHelper.executeThriftCall(() -> payQueryFeign.billDownload(billDownloadReq));
            return JsonUtil.copy(billDownloadResp, ApiBillDownloadResp.class);
        });
    }
}
