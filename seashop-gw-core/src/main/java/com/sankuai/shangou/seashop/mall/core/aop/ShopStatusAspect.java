package com.sankuai.shangou.seashop.mall.core.aop;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import javax.annotation.Resource;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.enums.SysLoginResultEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallShopRemoteService;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: LXH
 **/
@Slf4j
@Aspect
@Component
public class ShopStatusAspect {
    @Resource
    private MallShopRemoteService mallShopRemoteService;

    final static String GET_SHOP_METHOD = "getShopId";
    final static String SET_SHOP_METHOD = "setShopId";


    @Pointcut("@annotation(com.sankuai.shangou.seashop.mall.core.aop.ShopStatusAnnotation)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        AtomicReference<Long> shopId = new AtomicReference<>();
        //如果只有一个参数并且是Long类型，则直接获取
        if (args.length == 1 && args[0] instanceof Long) {
            shopId.set((Long) args[0]);

        }
//        如果shopId没有值
        if (Objects.isNull(shopId.get())) {
            //如果参数中有BaseParamReq的子类
            //循环遍历args，找到BaseParamReq的子类，并获取shopI
            Arrays.asList(args).forEach(v -> {
                if (v instanceof BaseParamReq) {
                    try {
                        Method method = v.getClass().getMethod(GET_SHOP_METHOD);
                        shopId.set((Long) method.invoke(v));
                    }
                    catch (Exception e) {
                        log.error("获取shopId失败", e);
                    }
                }
            });
        }

        //如果shopId不为空，则判断店铺状态
        if (shopId.get() != null) {
            BaseIdReq baseIdReq = new BaseIdReq();
            baseIdReq.setId(shopId.get());
            ShopResp shop = mallShopRemoteService.queryShop(baseIdReq);
            if (shop == null || !Objects.equals(shop.getShopStatus(), ShopEnum.AuditStatus.Open.getCode())) {
                return new ResultDto<>(SysLoginResultEnum.SHOP_NOT_EXIST_OR_NOT_OPEN.getCode(), SysLoginResultEnum.SHOP_NOT_EXIST_OR_NOT_OPEN.getMsg());
            }
        }
        return joinPoint.proceed(args);
    }

}
