package com.sankuai.shangou.seashop.m.core.thrift.impl.base;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MsgTemplateCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.MsgTemplateAppletReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MsgTemplateDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveMsgTemplateReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiMsgTemplateAppletReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiMsgTemplateDataReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiSaveMsgTemplateReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:06
 */
@RestController
@RequestMapping("/mApi/apiMsgTemplate")
public class MApiMsgTemplateCmdController {

    @Resource
    private MsgTemplateCmdFeign msgTemplateCmdFeign;

    @PostMapping(value = "/saveMsgTemplate", consumes = "application/json")
    public ResultDto<BaseResp> saveMsgTemplate(@RequestBody ApiSaveMsgTemplateReq saveMsgTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveMsgTemplate", saveMsgTemplateReq, req -> {
            req.checkParameter();
            SaveMsgTemplateReq bean = JsonUtil.copy(req, SaveMsgTemplateReq.class);
            return ThriftResponseHelper.executeThriftCall(() -> msgTemplateCmdFeign.saveMsgTemplate(bean));
        });
    }

    @GetMapping(value = "/getAppletSubscribeTmplate")
    public ResultDto<BaseResp> getAppletSubscribeTmplate() throws TException {
        return ThriftResponseHelper.responseInvoke("getAppletSubscribeTmplate", null, req ->
                ThriftResponseHelper.executeThriftCall(() -> msgTemplateCmdFeign.getAppletSubscribeTmplate()));
    }

    @PostMapping(value = "/saveMsgTemplateApplet", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> saveMsgTemplateApplet(@RequestBody ApiMsgTemplateAppletReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("saveMsgTemplateApplet", request, req -> {
            req.checkParameter();
            MsgTemplateAppletReq bean = JsonUtil.copy(req, MsgTemplateAppletReq.class);
            return ThriftResponseHelper.executeThriftCall(() -> msgTemplateCmdFeign.saveMsgTemplateApplet(bean));
        });
    }

    @PostMapping(value = "/saveMsgTemplateData", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> saveMsgTemplateData(@RequestBody ApiMsgTemplateDataReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("saveMsgTemplateData", request, req -> {
            req.setOperationUserId(TracerUtil.getManagerDto().getId());
            req.checkParameter();
            MsgTemplateDataReq bean = JsonUtil.copy(req, MsgTemplateDataReq.class);
            return ThriftResponseHelper.executeThriftCall(() -> msgTemplateCmdFeign.saveMsgTemplateData(bean));
        });
    }
}
