package com.sankuai.shangou.seashop.seller.core.user.converter;

import java.util.List;

import org.mapstruct.Mapper;

import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiCmdRoleReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryRoleReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiRoleResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.RoleResp;

@Mapper(componentModel = "spring")
public interface SellerApiRoleConverter {
    QueryRoleReq apiQueryRoleReq2QueryRoleReq(ApiQueryRoleReq queryRoleReq);

    List<ApiRoleResp> roleRespList2ApiRoleRespList(List<RoleResp> roleRespList);

    CmdRoleReq apiCmdRoleReq2CmdRoleReq(ApiCmdRoleReq cmdRoleReq);
}
