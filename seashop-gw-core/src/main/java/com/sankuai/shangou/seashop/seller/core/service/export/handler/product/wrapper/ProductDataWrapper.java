package com.sankuai.shangou.seashop.seller.core.service.export.handler.product.wrapper;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import org.apache.commons.collections.CollectionUtils;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;
import com.sankuai.shangou.seashop.seller.common.constant.SellerConstant;
import com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.product.eo.ProductEo;

import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2024/01/02 18:08
 */
public class ProductDataWrapper extends PageExportWrapper<ProductEo, QueryProductReq> {

    private final SellerProductRemoteService sellerProductRemoteService;

    public ProductDataWrapper(SellerProductRemoteService sellerProductRemoteService) {
        this.sellerProductRemoteService = sellerProductRemoteService;
    }

    private final String[] IGNORE_FIELDS = {"stock","measureUnit"};

    // 滚动查询的ID，本对象是每次导出时都会创建的新对象，每次导出都是独立的，所以设置为局部变量
    private String scrollId;
    private final static Integer SCROLL_TIME_VALUE_MINUTE = 5;

    @Override
    public List<ProductEo> getPageList(QueryProductReq param) {
        param.setUseScroll(true);
        param.setScrollId(scrollId);
        param.setKeepAliveMinutes(SCROLL_TIME_VALUE_MINUTE);
//        List<FieldSortReq> sortReqList = new ArrayList<>();
//        FieldSortReq fieldSortReq = new FieldSortReq();
//        fieldSortReq.setSort("addedTime");
//        fieldSortReq.setIzAsc(false);
//        sortReqList.add(fieldSortReq);
//        param.setSortList(sortReqList);
        BasePageResp<ProductPageResp> resp = sellerProductRemoteService.queryProduct(param);
        scrollId = resp.getScrollId();
        List<ProductPageResp> productList = resp.getData();
        List<Long> productIds = productList.stream().map(ProductPageResp::getProductId).collect(Collectors.toList());
        List<SkuQueryResp> skuList = sellerProductRemoteService.querySku(productIds);
        List<ProductEo> eoList = productEoBuild(skuList);

        fillProductData(eoList, productList);
        fillLadderPrice(eoList);
//        fillUnit(eoList, skuList);
        // 没有查到完整的一页数据的时候，清空scrollId
        if (CollectionUtils.isEmpty(productList)) {
            sellerProductRemoteService.clearScrollId(scrollId);
        }
        eoList = eoList.stream()
                .sorted(Comparator.comparing(ProductEo::getCheckTime).reversed())
                .collect(Collectors.toList());
        return eoList;
    }

    private void fillUnit(List<ProductEo> eoList, List<SkuQueryResp> skuList) {
        if (CollectionUtils.isEmpty(eoList)) {
            return;
        }
        eoList.forEach(t -> {
            SkuQueryResp sku = skuList.stream().filter(a -> t.getSkuCode().equals(a.getSkuCode())).findFirst().get();
            t.setMeasureUnit(sku.getMeasureUnit());
        });
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return super.getWriteHandlerList();
    }

    @Override
    public String sheetName() {
        return "供应商商品信息";
    }


    private List<ProductEo> productEoBuild(List<SkuQueryResp> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.EMPTY_LIST;
        }

        return skuList.stream().map(sku -> {
            ProductEo productEo = new ProductEo();
            productEo.setSkuId(sku.getSkuId());
            productEo.setProductId(sku.getProductId());
            productEo.setSkuAutoId(sku.getId());
            productEo.setSpecName(sku.getSpecName());
            productEo.setMeasureUnit(sku.getMeasureUnit());
            productEo.setSalePrice(sku.getSalePrice());
            productEo.setStock(sku.getStock());
            productEo.setSkuCode(sku.getSkuCode());
            return productEo;
        }).collect(Collectors.toList());
    }

    private void fillProductData(List<ProductEo> eoList, List<ProductPageResp> productList) {
        if (CollectionUtils.isEmpty(eoList)) {
            return;
        }

        Map<Long, ProductPageResp> productMap = productList.stream().collect(Collectors.toMap(ProductPageResp::getProductId, Function.identity(), (k1, k2) -> k1));
        eoList.forEach(eo -> {
            ProductPageResp product = productMap.get(eo.getProductId());
            if (product == null) {
                return;
            }

            BeanUtil.copyProperties(product, eo, IGNORE_FIELDS);
            List<String> categoryNames = product.getCategoryNames();
            if (CollectionUtils.isNotEmpty(categoryNames)) {
                eo.setFirstCategoryName(categoryNames.size() > 0 ? categoryNames.get(0) : null);
                eo.setSecondCategoryName(categoryNames.size() > 1 ? categoryNames.get(1) : null);
                eo.setThirdCategoryName(categoryNames.size() > 2 ? categoryNames.get(2) : null);
            }
            eo.setHasSku(convertBoolean(product.getHasSku()));
            eo.setWhetherOpenLadder(convertBoolean(product.getWhetherOpenLadder()));
            eo.setTotalStock(product.getStock());
            eo.setProductIdStr(String.valueOf(product.getProductId()));
            // 如果是销售中 标记为审核通过
            if (ProductEnum.AuditStatusEnum.ON_SALE.getCode().equals(product.getAuditStatusCode())) {
                eo.setAuditStatusDesc("审核通过");
            }
            if (ProductEnum.AuditStatusEnum.VIOLATION.getCode().equals(product.getAuditStatusCode())) {
                eo.setSaleStatusDesc("违规下架");
                eo.setAuditStatusDesc("待审核");
            }
        });
    }

    private void fillLadderPrice(List<ProductEo> eoList) {
        if (CollectionUtils.isEmpty(eoList)) {
            return;
        }

        List<Long> productIds = eoList.stream().map(ProductEo::getProductId).collect(Collectors.toList());
        List<LadderPriceDto> ladderPriceList = sellerProductRemoteService.queryLadderPrice(productIds);
        Map<Long, List<LadderPriceDto>> ladderPriceMap = ladderPriceList.stream()
                .collect(Collectors.groupingBy(LadderPriceDto::getProductId));
        eoList.forEach(eo -> {
            List<LadderPriceDto> priceArr = ladderPriceMap.get(eo.getProductId());
            if (CollectionUtils.isEmpty(priceArr)) {
                return;
            }

            eo.setMinBath1(priceArr.size() > 0 ? priceArr.get(0).getMinBath() : null);
            eo.setMinBath2(priceArr.size() > 1 ? priceArr.get(1).getMinBath() : null);
            eo.setMinBath3(priceArr.size() > 2 ? priceArr.get(2).getMinBath() : null);
            eo.setPrice1(priceArr.size() > 0 ? priceArr.get(0).getPrice() : null);
            eo.setPrice2(priceArr.size() > 1 ? priceArr.get(1).getPrice() : null);
            eo.setPrice3(priceArr.size() > 2 ? priceArr.get(2).getPrice() : null);
        });
    }

    private String convertBoolean(Boolean flag) {
        return flag != null && flag ? SellerConstant.YES_STR : SellerConstant.NO_STR;
    }

}
