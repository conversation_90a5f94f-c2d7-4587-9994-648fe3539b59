package com.sankuai.shangou.seashop.seller.core.service.export.handler.order.wrapper;

import java.util.Collections;
import java.util.List;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.base.export.writeHandler.FindMergeRegionWhenWriteCell;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.OrderAndItemFlatListResp;
import com.sankuai.shangou.seashop.seller.common.remote.order.SellerOrderRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.order.eo.OrderInfoEo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class OrderInfoWrapper extends PageExportWrapper<OrderInfoEo, QuerySellerOrderReq> {

    private final SellerOrderRemoteService sellerOrderRemoteService;

    public OrderInfoWrapper(SellerOrderRemoteService sellerOrderRemoteService) {
        this.sellerOrderRemoteService = sellerOrderRemoteService;
    }

    // 滚动查询的ID，本对象是每次导出时都会创建的新对象，每次导出都是独立的，所以设置为局部变量
    private String scrollId;
    private final static long SCROLL_TIME_VALUE_MINUTE = 3L;

    @Override
    public List<OrderInfoEo> getPageList(QuerySellerOrderReq param) {
        log.info("【订单】查询订单导出参数:{}", JsonUtil.toJsonString(param));
        if (StrUtil.isBlank(scrollId)) {
            param.setTimeValueMinutes(SCROLL_TIME_VALUE_MINUTE);
            OrderAndItemFlatListResp scrollResp = sellerOrderRemoteService.getScrollIdForSellerExport(param);
            if (scrollResp == null || StrUtil.isBlank(scrollResp.getScrollId())) {
                return null;
            }
            scrollId = scrollResp.getScrollId();
            return BeanUtil.copyToList(scrollResp.getDataList(), OrderInfoEo.class);
        }
        EsScrollQueryReq scrollReq = new EsScrollQueryReq();
        scrollReq.setScrollId(scrollId);
        scrollReq.setTimeValueMinutes(SCROLL_TIME_VALUE_MINUTE);
        List<OrderAndItemInfoDto> list = sellerOrderRemoteService.listOrderAndItemFlatByScroll(scrollReq);
        return BeanUtil.copyToList(list, OrderInfoEo.class);
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return Collections.singletonList(new FindMergeRegionWhenWriteCell(OrderInfoEo.class, null, getDataSupplier(), getPrevNum()));
    }

    @Override
    public Integer getBatchCount() {
        return 10;
    }

    @Override
    public Integer getMaxBatch() {
        return 5000;
    }
}
