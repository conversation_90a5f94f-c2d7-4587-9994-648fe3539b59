package com.sankuai.shangou.seashop.seller.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ApplyRefundReq;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiApplyRefundReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/2/002
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiCashDeposit")
public class SellerApiCashDepositDetailCmdController {

    @Resource
    private SellerFinanceRemoteService sellerFinanceRemoteService;

    @PostMapping(value = "/applyRefund", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> applyRefund(@RequestBody ApiApplyRefundReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("applyRefund", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerFinanceRemoteService.applyRefund(JsonUtil.copy(req, ApplyRefundReq.class));
        });
    }
}
