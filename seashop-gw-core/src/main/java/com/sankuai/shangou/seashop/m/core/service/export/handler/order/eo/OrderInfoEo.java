package com.sankuai.shangou.seashop.m.core.service.export.handler.order.eo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.export.anno.RowKey;

import lombok.Getter;
import lombok.Setter;

/**
 * 订单导出对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderInfoEo {

    @RowKey
    @ExcelProperty("订单编号")
    private String orderId;
    @ExcelProperty("订单来源")
    private String platformDesc;
    @ExcelProperty("店铺")
    private String shopName;
    @ExcelProperty("买家")
    private String userName;
    @ExcelProperty("下单时间")
    private String orderDateStr;
    @ExcelProperty("付款时间")
    private String payDateStr;
    @ExcelProperty("完成时间")
    private String finishDateStr;
    @ExcelProperty("支付方式")
    private String payMethodDesc;
    @ExcelProperty("交易单号")
    private String gatewayOrderId;
    @ExcelProperty("商品总额")
    private BigDecimal productTotalAmount;
    @ExcelProperty("运费")
    private String freight;
    @ExcelProperty("税金")
    private BigDecimal tax;
    @ExcelProperty("优惠券抵扣")
    private BigDecimal couponAmount;
    @ExcelProperty("满额减")
    private BigDecimal moneyOffAmount;
    @ExcelProperty("门店改价")
    private BigDecimal updateAmount;
    @ExcelProperty("订单实付总额")
    private BigDecimal totalAmount;
    @ExcelProperty("平台佣金")
    private BigDecimal commissionTotalAmount;
    @ExcelProperty("订单状态")
    private String orderStatusDesc;
    @ExcelProperty("买家留言")
    private String userRemark;
    @ExcelProperty("收货人")
    private String shipTo;
    @ExcelProperty("手机号码")
    private String cellPhone;
    @ExcelProperty("收货地址")
    private String address;
    @ExcelProperty("商品ID")
    private Long productId;
    @ExcelProperty("SkuID")
    private String skuId;
    @ExcelProperty("规格ID")
    private Long skuAutoId;
    @ExcelProperty("一级分类")
    private String cateLevel1Name;
    @ExcelProperty("二级分类")
    private String cateLevel2Name;
    @ExcelProperty("三级分类")
    private String cateLevel3Name;
    @ExcelProperty("商品名称")
    private String productName;
    // SKU 级别的货号
    @ExcelProperty("货号")
    private String skuCode;
    @ExcelProperty("单价")
    private BigDecimal salePrice;
    @ExcelProperty("数量")
    private Long quantity;

}
