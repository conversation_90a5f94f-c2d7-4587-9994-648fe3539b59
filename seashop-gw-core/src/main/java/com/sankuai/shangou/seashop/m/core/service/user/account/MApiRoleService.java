package com.sankuai.shangou.seashop.m.core.service.user.account;

import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdRoleReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryRoleReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiRoleRespList;

public interface MApiRoleService {
    /**
     * 查询角色列表
     *
     * @param queryRoleReq 查询条件
     * @return RoleRespList
     */
    ApiRoleRespList queryRoleList(ApiQueryRoleReq queryRoleReq);

    /**
     * 添加角色
     *
     * @param cmdRoleReq 角色信息
     * @return Long
     */
    Long addRole(ApiCmdRoleReq cmdRoleReq);

    /**
     * 修改角色
     *
     * @param cmdRoleReq 角色信息
     * @return Long
     */
    Long editRole(ApiCmdRoleReq cmdRoleReq);

    /**
     * 删除角色
     *
     * @param cmdRoleReq 角色信息
     * @return Long
     */
    Long deleteRole(ApiCmdRoleReq cmdRoleReq);
}
