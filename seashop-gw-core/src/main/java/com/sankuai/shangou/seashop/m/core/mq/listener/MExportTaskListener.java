package com.sankuai.shangou.seashop.m.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.m.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(
    topic = MafkaConst.TOPIC_ASYNC_EXPORT_TASK  + "_${spring.profiles.active}",
    consumerGroup = MafkaConst.GROUP_ASYNC_EXPORT_TASK  + "_${spring.profiles.active}",
    selectorExpression = "*")
public class MExportTaskListener implements RocketMQListener<MessageExt> {

    @Resource
    private MExportTaskBiz exportTaskBiz;


    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【异步导出任务】接收的消息内容为: {}", body);
        try {
            ExportTask task = JsonUtil.parseObject(body, ExportTask.class);
            exportTaskBiz.execute(task);
        }
        catch (Exception e) {
            log.error("【mafka消费】【异步导出任务】执行异常", e);
            throw new RuntimeException(e);
        }
    }
}
