package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiPlatFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.PlatFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleConfigQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFlashSaleConfig")
public class MApiFlashSaleConfigQueryController {

    @Resource
    private FlashSaleConfigQueryFeign flashSaleConfigQueryFeign;

    @GetMapping(value = "/getPlatConfig")
    public ResultDto<ApiPlatFlashSaleConfigResp> getPlatConfig() throws TException {
        return ThriftResponseHelper.responseInvoke("getPlatConfig", null, req -> {
            PlatFlashSaleConfigResp platConfig = ThriftResponseHelper.executeThriftCall(() -> flashSaleConfigQueryFeign.getPlatConfig());
            return JsonUtil.copy(platConfig, ApiPlatFlashSaleConfigResp.class);
        });
    }
}
