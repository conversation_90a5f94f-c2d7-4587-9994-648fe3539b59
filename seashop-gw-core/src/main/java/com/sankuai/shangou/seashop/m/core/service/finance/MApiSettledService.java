package com.sankuai.shangou.seashop.m.core.service.finance;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledItemCountQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledItemQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledItemCountResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledItemResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettlementDetailResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ManagerUserInfo;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
public interface MApiSettledService {

    /**
     * 已结算列表查询
     *
     * @param request
     * @return
     */
    BasePageResp<ApiSettledResp> pageList(ApiSettledQryReq request);

    /**
     * 已结算列表明细查询
     *
     * @param request
     * @return
     */
    BasePageResp<ApiSettledItemResp> itemPageList(ApiSettledItemQryReq request);

    /**
     * 已结算列表统计数据
     *
     * @param request
     * @return
     */
    ApiSettledItemCountResp itemCount(ApiSettledItemCountQryReq request);

    /**
     * 结算明细详情查询
     *
     * @param request
     * @return
     */
    ApiSettlementDetailResp getDetailByOrderId(ApiOrderIdQryReq request);

    /**
     * 导出结算明细列表
     *
     * @param request
     * @param managerInfo
     */
    void exportSettledItemList(ApiSettledItemQryReq request, ManagerUserInfo managerInfo);

    /**
     * 导出供应商维度已结算列表
     *
     * @param request
     * @param managerInfo
     */
    void exportSettledByShop(ApiSettledQryReq request, ManagerUserInfo managerInfo);

    /**
     * 导出订单维度已结算列表
     *
     * @param request
     * @param managerInfo
     */
    void exportSettledByOrder(ApiSettledQryReq request, ManagerUserInfo managerInfo);
}
