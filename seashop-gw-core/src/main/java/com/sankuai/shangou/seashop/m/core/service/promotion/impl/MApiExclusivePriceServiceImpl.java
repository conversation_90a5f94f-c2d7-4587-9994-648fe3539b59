package com.sankuai.shangou.seashop.m.core.service.promotion.impl;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.promotion.MApiExclusivePriceService;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ManagerUserInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/26/026
 * @description:
 */
@Service
@Slf4j
public class MApiExclusivePriceServiceImpl implements MApiExclusivePriceService {

    @Resource
    private MExportTaskBiz exportTaskBiz;

    @Override
    public void export(ApiExclusivePriceQueryReq request, LoginManagerDto managerUserInfo) {
        log.info("专享价导出request：{}", request);
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.EXCLUSIVE_PRICE_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(managerUserInfo.getId());
        createExportTaskBo.setOperatorName(managerUserInfo.getName());
        exportTaskBiz.create(createExportTaskBo);

    }

}
