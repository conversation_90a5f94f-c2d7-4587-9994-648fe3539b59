package com.sankuai.shangou.seashop.core.config;

import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.security.config.LoginSecurityConfig;
import com.sankuai.shangou.seashop.base.security.utils.TokenUtil;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Configuration
public class ServiceConfig {


    @Bean
    public ClientHttpRequestFactory restTemplateFactory(OkHttpClient okHttpClient) {
        return new OkHttp3ClientHttpRequestFactory(okHttpClient);
    }

    @LoadBalanced
    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory restTemplateFactory){
        return new RestTemplate(restTemplateFactory);
    }


//    @Bean
//    public StorageWebPermissionHandler userStorageHandler(LoginHandler loginHandler) {
//        List<RoleEnum> roleEnums = Arrays.stream(RoleEnum.values()).collect(Collectors.toList());
//        return new StorageWebPermissionHandler() {
//            @Override
//            public boolean checkSign(HttpServletRequest request) {
//                return loginHandler.getAccountAndCheckToken(getToken(request), roleEnums) != null;
//            }
//
//            @Override
//            public boolean checkUpload(HttpServletRequest request) {
//                return loginHandler.getAccountAndCheckToken(getToken(request), roleEnums) != null;
//            }
//        };
//    }

    private String getToken(HttpServletRequest request) {
        String tokenValue = TokenUtil.getRequestToken(LoginSecurityConfig.TOKEN_NAME);
        if (StringUtils.isBlank(tokenValue)) {
            throw new LoginException(LoginErrorEnum.INVALID_TOKEN);
        }
        return tokenValue;
    }

}
