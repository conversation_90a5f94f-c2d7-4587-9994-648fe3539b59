package com.sankuai.shangou.seashop.m.core.service.export.handler.product.wrapper;

import java.util.List;
import java.util.stream.Collectors;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.common.remote.product.MProductAuditRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.product.eo.ProductAuditRefuseEo;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.QueryProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditPageResp;

/**
 * <AUTHOR>
 * @date 2024/01/03 18:05
 */
public class ProductAuditRefuseDataWrapper extends PageExportWrapper<ProductAuditRefuseEo, QueryProductAuditReq> {

    private final MProductAuditRemoteService MProductAuditRemoteService;

    public ProductAuditRefuseDataWrapper(MProductAuditRemoteService MProductAuditRemoteService) {
        this.MProductAuditRemoteService = MProductAuditRemoteService;
    }

    @Override
    public List<ProductAuditRefuseEo> getPageList(QueryProductAuditReq param) {
        BasePageResp<ProductAuditPageResp> resp = MProductAuditRemoteService.queryProductAudit(param);
        List<ProductAuditPageResp> productList = resp.getData();
        return productList.stream().map(product -> {
            ProductAuditRefuseEo eo = new ProductAuditRefuseEo();
            eo.setProductId(String.valueOf(product.getProductId()));
            eo.setProductName(product.getProductName());
            eo.setAuditReason(product.getAuditReason());
            return eo;
        }).collect(Collectors.toList());
    }
}
