package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryFreightTemplateReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiQueryFreightTemplateResp;
import com.sankuai.shangou.seashop.user.thrift.shop.FreightAreaQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/25 9:19
 */
@RestController
@RequestMapping("/mApi/apiFreightArea")
public class MApiFreightAreaController {

    @Resource
    private FreightAreaQueryFeign freightAreaQueryFeign;

    @PostMapping(value = "/queryMFreightTemplateList", consumes = "application/json")
    public ResultDto<ApiQueryFreightTemplateResp> queryMFreightTemplateList(@RequestBody ApiQueryFreightTemplateReq queryFreightTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryMFreightTemplateList", queryFreightTemplateReq, req -> {
            QueryFreightTemplateReq bean = JsonUtil.copy(req, QueryFreightTemplateReq.class);
            QueryFreightTemplateResp resp = ThriftResponseHelper.executeThriftCall(() -> freightAreaQueryFeign.queryMFreightTemplateList(bean));
            return JsonUtil.copy(resp, ApiQueryFreightTemplateResp.class);
        });
    }
}
