package com.sankuai.shangou.seashop.mall.core.service.trade.impl;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiQueryProductDetailReq;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.mall.core.service.trade.MallTradeProductService;
import com.sankuai.shangou.seashop.mall.core.service.user.shop.MallApiShopService;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.TradeProductQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.QueryProductDetailReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.ProductBaseInfoResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/05/17 14:52
 */
@Service
public class MallTradeProductServiceImpl implements MallTradeProductService {

    @Resource
    private TradeProductQueryFeign tradeProductQueryFeign;
    @Resource
    private FlashSaleQueryFeign flashSaleQueryFeign;
    @Resource
    private MallApiShopService mallApiShopService;

    @Override
    public ProductBaseInfoResp queryProductBaseInfo(ApiQueryProductDetailReq req, LoginMemberDto userInfo) {
        // 如果传了限时购id 做一层转换
        if (req.getFlashSaleId() != null && req.getFlashSaleId() > 0) {
            BaseIdReq flashSaleIdReq = new BaseIdReq();
            flashSaleIdReq.setId(req.getFlashSaleId());
            FlashSaleResp flashSale = ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.getById(flashSaleIdReq));
            AssertUtil.throwIfNull(flashSale, "限时购活动不存在");
            req.setProductId(flashSale.getProductId());
        }

        // 查询商品详情
        QueryProductDetailReq remoteReq = new QueryProductDetailReq();
        remoteReq.setProductId(req.getProductId());
        remoteReq.setCollocationId(req.getCollocationId());
        if (userInfo != null) {
            LoginMemberDto member = TracerUtil.getMemberDto();
            remoteReq.setUserId(member == null ? null : member.getId());
        }
        ProductBaseInfoResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.queryProductBaseInfo(remoteReq));
        // 增加统计店铺访问量
        if (resp != null) {
            BaseIdReq baseIdReq = new BaseIdReq();
            baseIdReq.setId(resp.getShopId());
            mallApiShopService.addShopUv(baseIdReq, userInfo);
        }
        return resp;
    }
}
