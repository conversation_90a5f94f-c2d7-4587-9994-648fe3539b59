package com.sankuai.shangou.seashop.seller.core.service.finance;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiShopIdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiPendingSettlementOrderResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiPendingSettlementResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
public interface SellerApiPendingSettlementService {

    /**
     * 店铺待结算总额
     *
     * @param request
     * @return
     */
    ApiPendingSettlementResp getTotalSettlementAmount(ApiShopIdReq request);


    /**
     * 分页查询待结算订单列表
     *
     * @param request
     * @return
     */
    BasePageResp<ApiPendingSettlementOrderResp> pageList(ApiPendingSettlementOrderQryReq request);

    /**
     * 根据订单id查询订单详情
     *
     * @param request
     * @return
     */
    ApiPendingSettlementOrderResp getDetailByOrderId(ApiOrderIdQryReq request, Long shopId);

    /**
     * 导出待结算订单列表
     *
     * @param request
     */
    void exportPendSettleList(ApiPendingSettlementOrderQryReq request, LoginShopDto loginShopDto);
}
