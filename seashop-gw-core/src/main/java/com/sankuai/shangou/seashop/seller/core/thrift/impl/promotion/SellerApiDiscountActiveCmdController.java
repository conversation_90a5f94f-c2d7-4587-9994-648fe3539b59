package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.DiscountActiveSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.DiscountActiveCmdFeign;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiDiscountActiveSaveReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/10/010
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiDiscountActive")
public class SellerApiDiscountActiveCmdController {

    @Resource
    private DiscountActiveCmdFeign discountActiveCmdFeign;

    @PostMapping(value = "/save", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> save(@RequestBody ApiDiscountActiveSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            DiscountActiveSaveReq bean = JsonUtil.copy(req, DiscountActiveSaveReq.class);
            bean.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> discountActiveCmdFeign.save(bean));
        });
    }

    @PostMapping(value = "/endActive", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("endActive", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> discountActiveCmdFeign.endActive(req));
        });
    }
}
