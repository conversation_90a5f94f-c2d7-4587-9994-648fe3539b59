package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleCategoryResp;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerFlashSaleRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiFlashSaleCategoryResp;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiFlashSaleCategory")
public class SellerApiFlashSaleCategoryQueryController {

    @Resource
    private SellerFlashSaleRemoteService sellerFlashSaleRemoteService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiFlashSaleCategoryResp>> pageList(@RequestBody BasePageReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            BasePageResp<FlashSaleCategoryResp> flashSaleCategoryPage = sellerFlashSaleRemoteService.categoryPageList(request);
            return PageResultHelper.transfer(flashSaleCategoryPage, ApiFlashSaleCategoryResp.class);
        });
    }
}
