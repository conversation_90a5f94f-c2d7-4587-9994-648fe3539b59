package com.sankuai.shangou.seashop.seller.core.thrift.impl.finance;

import java.net.InetAddress;
import java.net.UnknownHostException;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreatePaymentReq;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiCreatePaymentReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiOrderPayCreateResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/2/002
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiCashDeposit")
public class SellerApiCashDepositCmdController {

    @Resource
    private SellerFinanceRemoteService sellerFinanceRemoteService;

    @PostMapping(value = "/createPayment")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiOrderPayCreateResp> createPayment(@RequestBody ApiCreatePaymentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createPayment", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            CreatePaymentReq paymentReq = JsonUtil.copy(req, CreatePaymentReq.class);
            paymentReq.setDeviceIp(getLocalIp());
            paymentReq.checkParameter();
            return JsonUtil.copy(sellerFinanceRemoteService.createPayment(paymentReq), ApiOrderPayCreateResp.class);
        });
    }

    private String getLocalIp() {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            return "127.0.0.1";
        }
    }
}
