package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiPendingSettlementService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiPendingSettlementQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPendingSettlementOrderResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPendingSettlementResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPlatCommissionResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiPendingSettlement")
public class MApiPendingSettlementQueryController {

    @Resource
    private MApiPendingSettlementService mApiPendingSettlementService;

    @PostMapping(value = "/getPendingSettlementList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiPendingSettlementResp>> getPendingSettlementList(@RequestBody ApiPendingSettlementQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getPendingSettlementList", request,
            req -> mApiPendingSettlementService.getPendingSettlementList(req)
        );
    }

    @GetMapping(value = "/getPlatCommission")
    public ResultDto<ApiPlatCommissionResp> getPlatCommission() throws TException {
        return ThriftResponseHelper.responseInvoke("getPlatCommission", null,
            req -> mApiPendingSettlementService.getPlatCommission()
        );
    }

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiPendingSettlementOrderResp>> pageList(@RequestBody ApiPendingSettlementOrderQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request,
            req -> {
                req.checkParameter();
                return mApiPendingSettlementService.pageList(req);
            }
        );
    }

    @PostMapping(value = "/getDetailByOrderId", consumes = "application/json")
    public ResultDto<ApiPendingSettlementOrderResp> getDetailByOrderId(@RequestBody ApiOrderIdQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getDetailByOrderId", request,
            req -> {
                req.checkParameter();
                return mApiPendingSettlementService.getDetailByOrderId(req);
            }
        );
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/exportPendSettleListTotal", consumes = "application/json")
    public ResultDto<BaseResp> exportPendSettleListTotal(@RequestBody ApiPendingSettlementQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("exportPendSettleListTotal", request,
            req -> {
                mApiPendingSettlementService.exportPendSettleListTotal(req);
                return BaseResp.of();
            }
        );
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/exportPendSettleList", consumes = "application/json")
    public ResultDto<BaseResp> exportPendSettleList(@RequestBody ApiPendingSettlementOrderQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("exportPendingSettlementList", request,
            req -> {
                mApiPendingSettlementService.exportPendSettleList(req);
                return BaseResp.of();
            }
        );
    }
}
