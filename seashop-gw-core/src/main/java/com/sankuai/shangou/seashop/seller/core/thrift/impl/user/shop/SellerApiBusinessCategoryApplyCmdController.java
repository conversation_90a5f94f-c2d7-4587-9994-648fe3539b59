package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryApplyRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiSaveBusinessCategoryApplyReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiSaveSupplyCustomFormReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveBusinessCategoryApplyReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveSupplyCustomFormReq;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:22
 */
@RestController
@RequestMapping("/sellerApi/apiBusinessCategoryApply")
public class SellerApiBusinessCategoryApplyCmdController {

    @Resource
    private SellerBusinessCategoryApplyRemoteService sellerBusinessCategoryApplyRemoteService;

    @PostMapping(value = "/saveBusinessCategoryApply", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> saveBusinessCategoryApply(@RequestBody ApiSaveBusinessCategoryApplyReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("saveBusinessCategoryApply", request, req -> {
            req.checkParameter();

            SaveBusinessCategoryApplyReq remoteReq = JsonUtil.copy(req, SaveBusinessCategoryApplyReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerBusinessCategoryApplyRemoteService.saveBusinessCategoryApply(remoteReq);
        });
    }

    @PostMapping(value = "/supplyCustomForm", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> supplyCustomForm(@RequestBody ApiSaveSupplyCustomFormReq request) throws TException {
        
        return ThriftResponseHelper.responseInvoke("supplyCustomForm", request, req -> {
            req.checkParameter();

            SaveSupplyCustomFormReq remoteReq = JsonUtil.copy(req, SaveSupplyCustomFormReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerBusinessCategoryApplyRemoteService.supplyCustomForm(remoteReq);
        });
    }
}
