package com.sankuai.shangou.seashop.m.core.service.user.account;


import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdLabelReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryLabelPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiLabelResp;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
public interface MApiLabelService {

    /**
     * 查询标签列表
     *
     * @param queryLabelPageReq 查询条件
     * @return LabelRespPage
     */
    BasePageResp<ApiLabelResp> queryLabelPage(ApiQueryLabelPageReq queryLabelPageReq);

    /**
     * 添加标签
     *
     * @param cmdLabelPageReq 标签信息
     * @return Long
     */
    Long addLabel(ApiCmdLabelReq cmdLabelPageReq);

    /**
     * 修改标签
     *
     * @param cmdLabelPageReq 标签信息
     * @return Long
     */
    Long editLabel(ApiCmdLabelReq cmdLabelPageReq);

    /**
     * 删除标签
     *
     * @param cmdLabelPageReq 标签信息
     * @return Long
     */
    Long deleteLabel(ApiCmdLabelReq cmdLabelPageReq);
}
