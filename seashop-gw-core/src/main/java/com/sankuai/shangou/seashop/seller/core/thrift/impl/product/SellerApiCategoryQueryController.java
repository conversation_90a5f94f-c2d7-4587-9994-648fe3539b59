package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import cn.hutool.core.util.ObjectUtil;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ShowStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryForApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryListReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryTreeResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryCategoryForApplyReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryCategoryListReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryCategoryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiCategoryListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiCategoryTreeResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:22
 */
@RestController
@RequestMapping("/sellerApi/apiCategory")
public class SellerApiCategoryQueryController {

    @Resource
    private CategoryQueryFeign categoryQueryFeign;

    @PostMapping(value = "/queryCategoryList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiCategoryListResp> queryCategoryList(@RequestBody ApiQueryCategoryListReq queryCategoryListReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryList", queryCategoryListReq, req -> {

            QueryCategoryListReq remoteReq = JsonUtil.copy(req, QueryCategoryListReq.class);
            CategoryListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryList(remoteReq));
            return JsonUtil.copy(resp, ApiCategoryListResp.class);
        });
    }

    @PostMapping(value = "/queryCategoryTree", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiCategoryTreeResp> queryCategoryTree(@RequestBody ApiQueryCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTree", request, req -> {

            QueryCategoryReq remoteReq = JsonUtil.copy(req, QueryCategoryReq.class);
            remoteReq.setShowStatus(ObjectUtil.defaultIfNull(remoteReq.getShowStatus(), ShowStatusEnum.SHOW_ALL.getValue()));
            CategoryTreeResp tree = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTree(remoteReq));
            return JsonUtil.copy(tree, ApiCategoryTreeResp.class);
        });
    }

    @PostMapping(value = "/queryCategoryTreeHideNoThreeLevel", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiCategoryTreeResp> queryCategoryTreeHideNoThreeLevel(@RequestBody ApiQueryCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTreeHideNoThreeLevel", request, req -> {

            QueryCategoryReq remoteReq = JsonUtil.copy(req, QueryCategoryReq.class);
            remoteReq.setShowStatus(ObjectUtil.defaultIfNull(remoteReq.getShowStatus(), ShowStatusEnum.SHOW_ALL.getValue()));
            CategoryTreeResp tree = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTreeHideNoThreeLevel(remoteReq));
            return JsonUtil.copy(tree, ApiCategoryTreeResp.class);
        });
    }
    @PostMapping(value = "/queryCategoryTreeForApply", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiCategoryTreeResp> queryCategoryTreeForApply(@RequestBody ApiQueryCategoryForApplyReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryCategoryTreeForApply", request, req -> {

            QueryCategoryForApplyReq remoteReq = JsonUtil.copy(req, QueryCategoryForApplyReq.class);
            remoteReq.setShowStatus(ObjectUtil.defaultIfNull(remoteReq.getShowStatus(), ShowStatusEnum.SHOW_ALL.getValue()));
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            CategoryTreeResp tree = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryTreeForApply(remoteReq));
            return JsonUtil.copy(tree, ApiCategoryTreeResp.class);
        });
    }
}
