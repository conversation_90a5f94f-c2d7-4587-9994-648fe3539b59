package com.sankuai.shangou.seashop.seller.core.service.finance.impl;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.seller.core.service.finance.SellerApiSettledService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledItemQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettledItemResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettledResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettlementDetailResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class SellerApiSettledServiceImpl implements SellerApiSettledService {

    @Resource
    private SellerFinanceRemoteService sellerFinanceRemoteService;
    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;

    @Override
    public BasePageResp<ApiSettledResp> pageList(ApiSettledQryReq request) {
        BasePageResp<SettledResp> settledPage = sellerFinanceRemoteService.settledPageList(JsonUtil.copy(request, SettledQryReq.class));
        if (null == settledPage || CollUtil.isEmpty(settledPage.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }
        return PageResultHelper.transfer(settledPage, ApiSettledResp.class);
    }

    @Override
    public BasePageResp<ApiSettledItemResp> itemPageList(ApiSettledItemQryReq request) {
        BasePageResp<SettledItemResp> settledItemPage = sellerFinanceRemoteService.settledItemPageList(JsonUtil.copy(request, SettledItemQryReq.class));
        if (null == settledItemPage || CollUtil.isEmpty(settledItemPage.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }
        return PageResultHelper.transfer(settledItemPage, ApiSettledItemResp.class);
    }

    @Override
    public ApiSettlementDetailResp getDetailByOrderId(ApiOrderIdQryReq request) {
        SettlementDetailResp settlementDetailResp = sellerFinanceRemoteService.getSettledDetailByOrderId(request.getOrderId());
        if (null != settlementDetailResp) {
            return JsonUtil.copy(settlementDetailResp, ApiSettlementDetailResp.class);
        }
        return null;
    }

    @Override
    public void exportSettledList(ApiSettledQryReq request, LoginShopDto shopInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.SETTLED_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getShopDto().getManagerId());
        createExportTaskBo.setOperatorName(TracerUtil.getShopDto().getManagerName());
//        createExportTaskBo.setOperatorId(101L);
//        createExportTaskBo.setOperatorName("测试");

        sellerExportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public void exportSettledItemList(ApiSettledItemQryReq request, LoginShopDto shopInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.SETTLED_ITEM_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getShopDto().getManagerId());
        createExportTaskBo.setOperatorName(TracerUtil.getShopDto().getManagerName());
//        createExportTaskBo.setOperatorId(101L);
//        createExportTaskBo.setOperatorName("测试");

        sellerExportTaskBiz.create(createExportTaskBo);
    }
}
