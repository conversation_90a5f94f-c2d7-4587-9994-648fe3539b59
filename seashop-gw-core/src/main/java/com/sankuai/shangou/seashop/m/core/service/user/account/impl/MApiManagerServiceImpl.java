package com.sankuai.shangou.seashop.m.core.service.user.account.impl;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.shangou.seashop.base.boot.enums.LoginPlatformEnum;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.ServletUtil;
import com.sankuai.shangou.seashop.core.commmon.auth.MAuthenticationHandler;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiManagerService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdManagerReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryManagerPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryManagerReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiManagerResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMenuResp;
import com.sankuai.shangou.seashop.user.thrift.account.ManagerCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.ManagerQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.dto.MenuDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMenuAuthReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;
import com.sankuai.shangou.seashop.user.thrift.auth.LoginUserFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Service
@Slf4j
public class MApiManagerServiceImpl implements MApiManagerService {
    @Resource
    private MAuthenticationHandler mAuthenticationHandler;
    @Resource
    private ManagerQueryFeign managerQueryFeign;
    @Resource
    private ManagerCmdFeign managerCmdFeign;
    @Resource
    private LoginUserFeign loginUserFeign;

    @Override
    public BasePageResp<ApiManagerResp> queryManagerPage(ApiQueryManagerPageReq queryManagerPageReq) {
        queryManagerPageReq.setShopId(ShopEnum.PLATFORM);
        QueryManagerPageReq queryManagerPageReq1 = JsonUtil.copy(queryManagerPageReq, QueryManagerPageReq.class);
        BasePageResp<ManagerResp> managerRespPage = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryManagerPage(queryManagerPageReq1));
        return PageResultHelper.transfer(managerRespPage, ApiManagerResp.class);
    }

    @Override
    public ApiManagerResp queryManager(ApiQueryManagerReq queryManagerPageReq) {
        QueryManagerReq queryManagerReq = JsonUtil.copy(queryManagerPageReq, QueryManagerReq.class);
        ManagerResp managerResp = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryManager(queryManagerReq));
        return JsonUtil.copy(managerResp, ApiManagerResp.class);
    }

    @Override
    public Long addManager(ApiCmdManagerReq cmdManagerReq) {
        cmdManagerReq.setShopId(ShopEnum.PLATFORM);
        CmdManagerReq cmdManagerReq1 = JsonUtil.copy(cmdManagerReq, CmdManagerReq.class);
        cmdManagerReq1.setShopId(ShopEnum.PLATFORM);
        return ThriftResponseHelper.executeThriftCall(() -> managerCmdFeign.addManager(cmdManagerReq1));
    }

    @Override
    public Long editManager(ApiCmdManagerReq cmdManagerReq) {
        cmdManagerReq.setShopId(ShopEnum.PLATFORM);
        CmdManagerReq cmdManagerReq1 = JsonUtil.copy(cmdManagerReq, CmdManagerReq.class);
        cmdManagerReq1.setShopId(ShopEnum.PLATFORM);
        return ThriftResponseHelper.executeThriftCall(() -> managerCmdFeign.editManager(cmdManagerReq1));
    }

    @Override
    public Long deleteManager(ApiCmdManagerReq cmdManagerReq) {
        cmdManagerReq.setShopId(ShopEnum.PLATFORM);
        CmdManagerReq cmdManagerReq1 = JsonUtil.copy(cmdManagerReq, CmdManagerReq.class);
        cmdManagerReq1.setShopId(ShopEnum.PLATFORM);
        return ThriftResponseHelper.executeThriftCall(() -> managerCmdFeign.deleteManager(cmdManagerReq1));
    }

    @Override
    public Integer batchDeleteManager(ApiCmdManagerReq cmdManagerReq) {
        cmdManagerReq.setShopId(ShopEnum.PLATFORM);
        CmdManagerReq cmdManagerReq1 = JsonUtil.copy(cmdManagerReq, CmdManagerReq.class);
        cmdManagerReq1.setShopId(ShopEnum.PLATFORM);
        return ThriftResponseHelper.executeThriftCall(() -> managerCmdFeign.batchDeleteManager(cmdManagerReq1));
    }

    @Override
    public LoginResp login(ApiLoginReq loginReq) {
        LoginReq copy1 = JsonUtil.copy(loginReq, LoginReq.class);
        copy1.setLoginTypeWithDefault(loginReq.getLoginType());
        copy1.setLoginPlatform(LoginPlatformEnum.PLATFORM_BE.name());
        if (loginReq.getSlideCode()!=null){
            copy1.getSlideCode().setRemoteIp(ServletUtil.ip());
        }
        return ThriftResponseHelper.executeThriftCall(() -> loginUserFeign.login(copy1));
    }

    @Override
    public Boolean logout() {
        ThriftResponseHelper.executeThriftCall(() -> loginUserFeign.logout(mAuthenticationHandler.getTokenCache()));
        return true;
    }
    @Override
    public List<ApiMenuResp> queryMenuAuth(Long managerId) {
        QueryMenuAuthReq req=new QueryMenuAuthReq();
        req.setManagerId(managerId);
        req.setPlat(0L);
        List<MenuDto> menuList = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryMenuAuth(req));
        return JsonUtil.copyList(menuList, ApiMenuResp.class);
    }
}
