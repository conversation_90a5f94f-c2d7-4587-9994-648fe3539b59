package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerOrderSettingRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiSaveOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveOrderSettingReq;

/**
 * @author： liweisong
 * @create： 2023/11/27 16:38
 */
@RestController
@RequestMapping("/sellerApi/apiOrderSetting")
public class SellerApiOrderSettingCmdController {

    @Resource
    private SellerOrderSettingRemoteService sellerOrderSettingRemoteService;

    @PostMapping(value = "/addOrUpdateOrderSetting", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> addOrUpdateOrderSetting(@RequestBody ApiSaveOrderSettingReq saveOrderSettingReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("addOrUpdateOrderSetting", saveOrderSettingReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerOrderSettingRemoteService.addOrUpdateOrderSetting(JsonUtil.copy(req, SaveOrderSettingReq.class));
        });
    }
}
