package com.sankuai.shangou.seashop.seller.core.service.export.handler.order;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDistributionReq;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.order.wrapper.OrderDistributionDataWrapper;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/4 10:57
 */
@Service
public class OrderDistributionExportGetter extends AbstractBaseDataGetter<QueryOrderDistributionReq>
    implements SingleWrapperDataGetter<QueryOrderDistributionReq> {

    @Resource
    private OrderQueryFeign orderQueryFeign;

    @Override
    public DataContext selectData(QueryOrderDistributionReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        // 构造数据包装器，每一个包装器对应一个sheet页，并且每个包装器可以单独定义自己的excel处理器，比如样式、合并单元格等
        OrderDistributionDataWrapper wrapper = new OrderDistributionDataWrapper(orderQueryFeign);
        context.setSheetDataList(Collections.singletonList(wrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.EXPORT_ORDER_DISTRIBUTION.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.EXPORT_ORDER_DISTRIBUTION.getName();
    }
}
