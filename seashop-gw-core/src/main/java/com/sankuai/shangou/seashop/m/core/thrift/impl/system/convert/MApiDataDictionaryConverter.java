package com.sankuai.shangou.seashop.m.core.thrift.impl.system.convert;

import java.util.List;

import org.mapstruct.Mapper;

import com.sankuai.shangou.seashop.base.thrift.core.request.BaseStrListReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DataDictionaryRes;
import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseStrListReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiDataDictionaryRes;

@Mapper(componentModel = "spring")
public interface MApiDataDictionaryConverter {

    BaseStrListReq apiBaseStrListReq2BaseStrListReq(ApiBaseStrListReq req);

    List<ApiDataDictionaryRes> dataDictionaryRes2ApiDataDictionaryRes(List<DataDictionaryRes> v);
}
