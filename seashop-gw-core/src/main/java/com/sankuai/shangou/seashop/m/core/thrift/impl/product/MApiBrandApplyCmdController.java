package com.sankuai.shangou.seashop.m.core.thrift.impl.product;


import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiAuditBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.BrandApplyCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.AuditBrandApplyReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/mApi/apiBrandApply")
public class MApiBrandApplyCmdController {

    @Resource
    private BrandApplyCmdFeign brandApplyCmdFeign;

    @PostMapping(value = "/auditBrandApply", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> auditBrandApply(@RequestBody ApiAuditBrandApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("auditBrandApply", request, req -> {
            req.checkParameter();

            return ThriftResponseHelper.executeThriftCall(() -> brandApplyCmdFeign.auditBrandApply(JsonUtil.copy(req, AuditBrandApplyReq.class)));
        });
    }

    @PostMapping(value = "/deleteBrandApply", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deleteBrandApply(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteBrandApply", request, req -> {
            req.checkParameter();

            return ThriftResponseHelper.executeThriftCall(() -> brandApplyCmdFeign.deleteBrandApply(req));
        });
    }
}
