package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.ApiQueryExpressCompanyReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiQueryExpressCompanyResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/mallApi/apiMallExpress")
public class MallApiMallExpressController {

    @Resource
    private ExpressQueryFeign expressQueryFeign;

    @PostMapping(value = "/queryMallExpressCompanyList", consumes = "application/json")
    public ResultDto<ApiQueryExpressCompanyResp> queryMallExpressCompanyList(@RequestBody ApiQueryExpressCompanyReq queryExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryMallExpressCompanyList", queryExpressCompanyReq, req -> {

            QueryExpressCompanyResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    expressQueryFeign.queryExpressCompanyList(JsonUtil.copy(req, QueryExpressCompanyReq.class)));
            return JsonUtil.copy(resp, ApiQueryExpressCompanyResp.class);
        });
    }

}
