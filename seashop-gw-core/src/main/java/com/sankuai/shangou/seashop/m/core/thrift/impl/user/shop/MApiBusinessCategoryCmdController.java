package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiBusinessCategoryService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdImportCategoryReq;

@RestController
@RequestMapping("/mApi/apiBusinessCategory")
public class MApiBusinessCategoryCmdController {
    @Resource
    private MApiBusinessCategoryService mApiBusinessCategoryService;


    @PostMapping(value = "/updateCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateCategory(@RequestBody ApiCmdBusinessCategoryReqList reqList) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategory", reqList, func -> {
            // 参数对象转换
            return mApiBusinessCategoryService.updateBusinessCategory(func);
        });
    }

    @PostMapping(value = "/deleteBusinessCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deleteBusinessCategory(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteCategory", request, req -> {
            req.checkParameter();

            return mApiBusinessCategoryService.deleteBusinessCategory(req);
        });
    }

    @PostMapping(value = "/importBusinessCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseImportResp> importBusinessCategory(@RequestBody ApiCmdImportCategoryReq reqList) throws TException {
        return ThriftResponseHelper.responseInvoke("importCategory", reqList, func -> {
            return mApiBusinessCategoryService.importBusinessCategory(func);
        });
    }
}
