package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopShipperRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiAddShopShipperReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiDeleteShopShipperReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiUpdateShopShipperDefaultReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiUpdateShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateShopShipperDefaultReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateShopShipperReq;

/**
 * @description：供应商发/退货地址
 * @author： liweisong
 * @create： 2023/11/27 10:29
 */
@RestController
@RequestMapping("/sellerApi/apiShopShipper")
public class SellerApiShopShipperCmdController {

    @Resource
    private SellerShopShipperRemoteService sellerShopShipperRemoteService;

    @PostMapping(value = "/addShopShipper", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> addShopShipper(@RequestBody ApiAddShopShipperReq addShopShipperReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("addShopShipper", addShopShipperReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerShopShipperRemoteService.addShopShipper(JsonUtil.copy(req, AddShopShipperReq.class));
        });
    }

    @PostMapping(value = "/updateShopShipper", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateShopShipper(@RequestBody ApiUpdateShopShipperReq updateShopShipperReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("updateShopShipper", updateShopShipperReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerShopShipperRemoteService.updateShopShipper(JsonUtil.copy(req, UpdateShopShipperReq.class));
        });
    }

    @PostMapping(value = "/updateShopShipperDefault", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateShopShipperDefault(@RequestBody ApiUpdateShopShipperDefaultReq updateShopShipperDefaultReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("updateShopShipperDefault", updateShopShipperDefaultReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            // sellerShopShipperRemoteService.updateShopShipperDefault(JsonUtil.copy(req, UpdateShopShipperDefaultReq.class));
            return sellerShopShipperRemoteService.updateShopShipperDefault(JsonUtil.copy(req, UpdateShopShipperDefaultReq.class));
        });
    }

    @PostMapping(value = "/deleteShopShipper", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> deleteShopShipper(@RequestBody ApiDeleteShopShipperReq deleteShopShipperReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("deleteShopShipper", deleteShopShipperReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerShopShipperRemoteService.deleteShopShipper(JsonUtil.copy(req, DeleteShopShipperReq.class));
        });
    }
}
