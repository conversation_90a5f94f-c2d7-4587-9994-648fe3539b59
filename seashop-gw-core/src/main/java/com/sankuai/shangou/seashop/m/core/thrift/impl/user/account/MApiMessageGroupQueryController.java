package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;


import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.enums.MessageTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ToUserEnum;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordDetailResp;
import com.sankuai.shangou.seashop.m.common.remote.base.MApiMessageRecordRemoteService;
import com.sankuai.shangou.seashop.m.common.remote.user.MApiMemberRemoteService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiMessageRecordQueryReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiSendEmailMsgReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMessageRecordDetailResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMessageRecordRes;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 标签服务类
 * @author: LXH
 **/

@Slf4j
@RestController
@RequestMapping("/mApi/apiMessageGroup")
public class MApiMessageGroupQueryController {
    @Resource
    MApiMessageRecordRemoteService mApiMessageRecordRemoteService;
    @Resource
    MApiMemberRemoteService mApiMemberRemoteService;
    @Resource
    private CouponQueryFeign couponQueryFeign;

    @PostMapping(value = "/sendEmailMsg", consumes = "application/json")
    public ResultDto<BaseResp> sendEmailMsg(@RequestBody ApiSendEmailMsgReq apiSendEmailMsgReq) {
        return ThriftResponseHelper.responseInvoke("queryPage", apiSendEmailMsgReq, req -> {
            ToUserEnum toUserEnum = ToUserEnum.ALL;
            //判断是否全部
            if (apiSendEmailMsgReq.getSendAll() != null && apiSendEmailMsgReq.getSendAll()){
                toUserEnum = ToUserEnum.ALL;
            }
            //判断是否指定标签
            if (CollUtil.isNotEmpty(apiSendEmailMsgReq.getLabelId())){
                toUserEnum = ToUserEnum.Label;
            }
            long mesId = mApiMessageRecordRemoteService.addEmailMessageRecord(apiSendEmailMsgReq.getLabelId(), toUserEnum, apiSendEmailMsgReq.getContent());
            try {
                mApiMemberRemoteService.sendEmailMsg(apiSendEmailMsgReq);
            }catch (BusinessException e){
                log.error("发送邮件消息失败, mesId:{}", mesId, e);
                mApiMessageRecordRemoteService.deleteCouponRecord(mesId);
                throw e;
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/queryPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiMessageRecordRes>> queryPage(@RequestBody ApiMessageRecordQueryReq apiSendEmailMsgReq) {
        return ThriftResponseHelper.responseInvoke("queryPage", apiSendEmailMsgReq, req -> mApiMessageRecordRemoteService.queryPage(apiSendEmailMsgReq));
    }

    @PostMapping(value = "/queryDetail", consumes = "application/json")
    public ResultDto<ApiMessageRecordDetailResp> queryDetail(@RequestBody BaseIdReq id) {
        return ThriftResponseHelper.responseInvoke("queryDetail", id, req ->{
            MessageRecordDetailResp messageRecordDetailResp = mApiMessageRecordRemoteService.queryDetail(id);
            ApiMessageRecordDetailResp apiMessageRecordDetailResp = JsonUtil.copy(messageRecordDetailResp, ApiMessageRecordDetailResp.class);

            //判断是否指定优惠券
            if (apiMessageRecordDetailResp.getMessageType().equals(MessageTypeEnum.Coupon.getCode())){
                BaseIdReq couponId = new BaseIdReq();
                couponId.setId(apiMessageRecordDetailResp.getId());
                apiMessageRecordDetailResp.setCouponRespList(ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.getByMessageId(couponId)).getCouponList());
            }
            return apiMessageRecordDetailResp;
        });
    }

}
