package com.sankuai.shangou.seashop.seller.core.thrift.impl.base;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.BanksQueryFeign;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ApiBanksListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ApiBanksResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sellerApi/apiBanks")
public class SellerApiBanksQueryController {

    @Resource
    private BanksQueryFeign banksQueryFeign;


    @GetMapping(value = "/queryList")
    public ResultDto<ApiBanksListResp> queryList() throws TException {
        return ThriftResponseHelper.responseInvoke("queryList", null, req -> {
            ApiBanksListResp resp = new ApiBanksListResp();
            resp.setBanksList(JsonUtil.copyList(ThriftResponseHelper.executeThriftCall(() ->
                    banksQueryFeign.queryList()).getBanksList(), ApiBanksResp.class));
            return resp;
        });
    }
}
