package com.sankuai.shangou.seashop.m.core.service.user.account.impl;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiPrivilegeService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryPrivilegeReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiPrivilegeResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiPrivilegeRespList;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiUserPrivilegeResp;
import com.sankuai.shangou.seashop.user.thrift.account.PrivilegeQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.PrivilegeRespList;
import com.sankuai.shangou.seashop.user.thrift.account.response.UserPrivilegeResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MApiPrivilegeServiceImpl implements MApiPrivilegeService {
    @Resource
    private PrivilegeQueryFeign privilegeQueryFeign;

    @Override
    public ApiPrivilegeRespList queryPrivilegeList(ApiQueryPrivilegeReq queryPrivilegePageReq) {
        //转化参数
        QueryPrivilegeReq queryPrivilegeReq = JsonUtil.copy(queryPrivilegePageReq, QueryPrivilegeReq.class);
        //获取结果
        queryPrivilegeReq.setPlatform(0);
        PrivilegeRespList privilegeRespList =  ThriftResponseHelper.executeThriftCall(() ->
                privilegeQueryFeign.queryPrivilegeList(queryPrivilegeReq));
        //转化结果
        return new ApiPrivilegeRespList(JsonUtil.copyList(privilegeRespList.getPrivilegeRespList(), ApiPrivilegeResp.class));
    }

    @Override
    public ApiUserPrivilegeResp queryUserPrivilege(LoginManagerDto managerInfo) {
        QueryUserPrivilegeReq privilegeReq = new QueryUserPrivilegeReq();
        privilegeReq.setPlatform(0);
        privilegeReq.setManagerId(managerInfo.getId());
        UserPrivilegeResp privilegeRespList = ThriftResponseHelper.executeThriftCall(() -> privilegeQueryFeign.queryUserPrivilege(privilegeReq));
        //转化结果
        return JsonUtil.copy(privilegeRespList, ApiUserPrivilegeResp.class);
    }
}
