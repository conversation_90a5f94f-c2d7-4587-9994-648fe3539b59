package com.sankuai.shangou.seashop.m.core.thrift.impl.system.topic;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.TopicCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.TopicQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.PlateTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicJsonFileReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWapTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseShopReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseTopicRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWapTopicRes;
import com.sankuai.shangou.seashop.m.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.m.thrift.core.request.topic.ApiFooterReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.topic.ApiHeaderReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.topic.ApiPCIndexReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.topic.ApiWapTopicReq;
import com.sankuai.shangou.seashop.m.thrift.system.topic.request.ApiBaseShopReq;
import com.sankuai.shangou.seashop.m.thrift.system.topic.request.ApiBaseTopicQueryReq;
import com.sankuai.shangou.seashop.m.thrift.system.topic.request.ApiBaseWapTopicQueryReq;
import com.sankuai.shangou.seashop.m.thrift.system.topic.response.ApiBaseTopicRes;
import com.sankuai.shangou.seashop.m.thrift.system.topic.response.ApiBaseWapTopicRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;

@RestController
@RequestMapping("/mApi/apiTopic")
@Slf4j
public class MApiTopicController {


    @Resource
    private TopicQueryFeign topicQueryFeign;

    @Resource
    private TopicCMDFeign topicCMDFeign;

    @PostMapping(value = "/create", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> create(@RequestBody ApiWapTopicReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("create", query, req -> {
            req.checkParameter();
            req.setType(TemplateClientTypeEnum.PCTOPIC.getCode());
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            String title = node.get("page").get("title").asText();
//            String tags = node.get("page").get("tags").asText();
//            String icon = node.get("page").get("logo").asText();
            BaseTopicReq topic = new BaseTopicReq();
            topic.setName(title);
//            topic.setTags(tags);
//            topic.setFrontCoverImage(icon);
//            topic.setTopImage(icon);
            topic.setPlatForm(PlateTypeEnum.PC.getCode());
            topic.setShopId(CommonConstant.PLATFORM_ID);
            topic.setOperationUserId(TracerUtil.getManagerDto().getId());
            Long topicId = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.create(topic));

            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(req.getType());
            baseTopicJsonFileReq.setShopId(CommonConstant.PLATFORM_ID);
            baseTopicJsonFileReq.setClient(topicId.toString());
            String filePath = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.uploadTemplate(baseTopicJsonFileReq));
            return topicId;
        });
    }

    @PostMapping(value = "/update", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> update(@RequestBody ApiWapTopicReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("updateWapTopic", query, req -> {
            req.checkParameter();
            req.setType(TemplateClientTypeEnum.PCTOPIC.getCode());
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            String title = node.get("page").get("title").asText();
//            String tags = node.get("page").get("tags").asText();
//            String icon = node.get("page").get("logo").asText();
            BaseTopicReq topic = new BaseTopicReq();
            topic.setName(title);
//            topic.setTags(tags);
//            topic.setFrontCoverImage(icon);
//            topic.setTopImage(icon);
            topic.setId(req.getId());
            topic.setShopId(CommonConstant.PLATFORM_ID);
            topic.setPlatForm(PlateTypeEnum.PC.getCode());
            topic.setTopicModules(new ArrayList<>());
            topic.setOperationUserId(TracerUtil.getManagerDto().getId());
            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(req.getType());
            baseTopicJsonFileReq.setShopId(CommonConstant.PLATFORM_ID);
            baseTopicJsonFileReq.setClient(req.getId().toString());
            String filePath = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.uploadTemplate(baseTopicJsonFileReq));

            ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.update(topic));
            return true;
        });
    }

    @PostMapping(value = "/query", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBaseTopicRes>> query(@RequestBody ApiBaseTopicQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            //查询平台
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.setPlatForm(CommonConstant.TOPIC_PC);
            BaseTopicQueryReq bean = JsonUtil.copy(req, BaseTopicQueryReq.class);
            bean.setPlatForm(CommonConstant.TOPIC_PC);
            BasePageResp<BaseTopicRes> result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.query(bean));
            return PageResultHelper.transfer(result, ApiBaseTopicRes.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiBaseWapTopicRes> getById(@RequestBody ApiBaseShopReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            BaseWapTopicQueryReq bean = JsonUtil.copy(req, BaseWapTopicQueryReq.class);
            bean.setType(TemplateClientTypeEnum.PCTOPIC.getCode());
            bean.setClient(req.getId().toString());

            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getWapTopicById(bean));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> delete(@RequestBody ApiBaseShopReq baseShopReq) throws TException {
        return ThriftResponseHelper.responseInvoke("create", baseShopReq, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BaseShopReq bean = JsonUtil.copy(req, BaseShopReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.delete(bean));
            return result;
        });
    }

    @PostMapping(value = "/queryWxa", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBaseTopicRes>> queryWxa(@RequestBody ApiBaseTopicQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            //查询平台
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.setPlatForm(CommonConstant.TOPIC_WXA);
            BaseTopicQueryReq bean = JsonUtil.copy(req, BaseTopicQueryReq.class);
            BasePageResp<BaseTopicRes> result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.query(bean));
            return PageResultHelper.transfer(result, ApiBaseTopicRes.class);
        });
    }

    @PostMapping(value = "/createWapTopic", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> createWapTopic(@RequestBody ApiWapTopicReq topicReq) throws TException {
        topicReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("createWapTopic", topicReq, req -> {
            req.setType(TemplateClientTypeEnum.WXSmallProgramSpecial.getCode());
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            String title = node.get("page").get("title").asText();
//            String tags = node.get("page").get("tags").asText();
//            String icon = node.get("page").get("logo").asText();
            BaseTopicReq topic = new BaseTopicReq();
            topic.setName(title);
//            topic.setTags(tags);
//            topic.setFrontCoverImage(icon);
//            topic.setTopImage(icon);
            topic.setPlatForm(PlateTypeEnum.WXA.getCode());
            topic.setShopId(CommonConstant.PLATFORM_ID);
            topic.setOperationUserId(TracerUtil.getManagerDto().getId());
            Long topicId = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.create(topic));

            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(req.getType());
            baseTopicJsonFileReq.setShopId(CommonConstant.PLATFORM_ID);
            baseTopicJsonFileReq.setClient(topicId.toString());
            String filePath = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.uploadTemplate(baseTopicJsonFileReq));
            return topicId;
        });
    }

    @PostMapping(value = "/updateWapTopic", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Boolean> updateWapTopic(@RequestBody ApiWapTopicReq topicReq) throws TException {
        topicReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("updateWapTopic", topicReq, req -> {
            req.setType(TemplateClientTypeEnum.WXSmallProgramSpecial.getCode());
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            String title = node.get("page").get("title").asText();
//            String tags = node.get("page").get("tags").asText();
//            String icon = node.get("page").get("logo").asText();

            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(req.getType());
            baseTopicJsonFileReq.setShopId(CommonConstant.PLATFORM_ID);
            baseTopicJsonFileReq.setClient(req.getId().toString());
            String filePath = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.uploadTemplate(baseTopicJsonFileReq));
            BaseTopicReq topic = new BaseTopicReq();
            topic.setName(title);
//            topic.setTags(tags);
//            topic.setFrontCoverImage(icon);
//            topic.setTopImage(icon);
            topic.setId(req.getId());
            topic.setShopId(CommonConstant.PLATFORM_ID);
            topic.setPlatForm(PlateTypeEnum.WXA.getCode());
            topic.setOperationUserId(TracerUtil.getManagerDto().getId());
            topic.setTopicModules(new ArrayList<>());
            ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.update(topic));


            return true;
        });
    }

    @PostMapping(value = "/getWapTopicById", consumes = "application/json")
    public ResultDto<ApiBaseWapTopicRes> getWapTopicById(@RequestBody ApiBaseWapTopicQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getWapTopicById", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.setType(TemplateClientTypeEnum.WXSmallProgramSpecial.getCode());
            BaseWapTopicQueryReq bean = JsonUtil.copy(req, BaseWapTopicQueryReq.class);
            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getWapTopicById(bean));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    //@PostMapping(value = "/editWapIndex", consumes = "application/json")
    //public ResultDto<Boolean> editWapIndex(@RequestBody ApiWapIndexReq indexReq) throws TException {
    //    indexReq.setClient(TemplateClientTypeEnum.WapIndex.getCode().toString());
    //    indexReq.checkParameter();
    //    return ThriftResponseHelper.responseInvoke("editWapIndex", indexReq, req -> {
    //        JsonNode node = JsonUtil.parseObject(indexReq.getContent(), JsonNode.class);
    //        BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
    //        baseTopicJsonFileReq.setJsonContent(indexReq.getContent());
    //        baseTopicJsonFileReq.setType(TemplateClientTypeEnum.WapIndex.getCode());
    //        baseTopicJsonFileReq.setShopId(CommonConstant.PLATFORM_ID);
    //        baseTopicJsonFileReq.setClient(indexReq.getClient());
    //
    //        String filePath = ThriftResponseHelper.executeThriftCall(() ->
    //            topicCMDFeign.uploadTemplate(baseTopicJsonFileReq));
    //        return true;
    //    });
    //}


    @GetMapping(value = "/getWapIndex")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiBaseWapTopicRes> getWapIndex() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        return ThriftResponseHelper.responseInvoke("getWapIndex", query, req -> {
            log.info("店铺首页查询数据:", req);
            String resultRpc = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getPlatWapIndex());
            ApiBaseWapTopicRes result = new ApiBaseWapTopicRes();
            result.setPage(resultRpc);
            return result;
        });
    }

    @PostMapping(value = "/editPCIndex", consumes = "application/json")
    public ResultDto<Boolean> editPCIndex(@RequestBody ApiPCIndexReq indexReq) throws TException {
        indexReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("editPCIndex", indexReq, req -> {
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(TemplateClientTypeEnum.PCIndex.getCode());
            baseTopicJsonFileReq.setShopId(CommonConstant.PLATFORM_ID);
            baseTopicJsonFileReq.setClient("index");

            String filePath = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.uploadTemplate(baseTopicJsonFileReq));
            return true;
        });
    }

    @GetMapping(value = "/getPCIndex")
    public ResultDto<ApiBaseWapTopicRes> getPCIndex() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        return ThriftResponseHelper.responseInvoke("getWapIndex", query, req -> {
            log.info("店铺首页查询数据:", req);
            String resultRpc = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getPlatIndex());
            ApiBaseWapTopicRes result = new ApiBaseWapTopicRes();
            result.setPage(resultRpc);
            return result;
        });
    }

    @PostMapping(value = "/editPCHeader", consumes = "application/json")
    public ResultDto<Boolean> editPCHeader(@RequestBody ApiHeaderReq headerReq) throws TException {
        headerReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("editPCHeader", headerReq, req -> {
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(TemplateClientTypeEnum.Header.getCode());
            baseTopicJsonFileReq.setShopId(CommonConstant.PLATFORM_ID);
            baseTopicJsonFileReq.setClient("header");
            String filePath = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.uploadTemplate(baseTopicJsonFileReq));
            return true;
        });
    }

    @GetMapping(value = "/getPCHeader")
    public ResultDto<ApiBaseWapTopicRes> getPCHeader() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.Header.getCode());
        query.setClient("header");
        query.setShopId(CommonConstant.PLATFORM_ID);
        return ThriftResponseHelper.responseInvoke("getPCHeader", query, req -> {
            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getWapTopicById(req));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    @PostMapping(value = "/editPCFooter", consumes = "application/json")
    public ResultDto<Boolean> editPCFooter(@RequestBody ApiFooterReq footerReq) throws TException {
        footerReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("editPCFooter", footerReq, req -> {
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(TemplateClientTypeEnum.Footer.getCode());
            baseTopicJsonFileReq.setShopId(CommonConstant.PLATFORM_ID);
            baseTopicJsonFileReq.setClient("footer");
            String filePath = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.uploadTemplate(baseTopicJsonFileReq));
            return true;
        });
    }

    @GetMapping(value = "/getPCFooter")
    public ResultDto<ApiBaseWapTopicRes> getPCFooter() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.Footer.getCode());
        query.setClient("footer");
        query.setShopId(CommonConstant.PLATFORM_ID);
        return ThriftResponseHelper.responseInvoke("getPCFooter", query, req -> {
            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getWapTopicById(req));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    @PostMapping(value = "/setHome", consumes = "application/json")
    public ResultDto<Boolean> setHome(@RequestBody ApiBaseShopReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getPCFooter", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BaseShopReq req1 = JsonUtil.copy(req, BaseShopReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                    topicCMDFeign.setHome(req1));
            return result;
        });
    }


}
