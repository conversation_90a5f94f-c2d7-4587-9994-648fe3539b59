package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiStatsUserPurchaseSkuReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiStatsPurchaseSkuExportResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiStatsUserPurchaseSkuResp;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.order.MallOrderRemoteService;
import com.sankuai.shangou.seashop.mall.core.service.order.order.MallOrderService;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.StatsUserPurchaseSkuReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.StatsUserPurchaseSkuResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiOrderStatistics")
@Slf4j
public class MallApiOrderStatisticsQueryController {

    @Resource
    private MallOrderRemoteService mallOrderRemoteService;
    @Resource
    private MallOrderService mallOrderService;

    @NeedLogin
    @PostMapping(value = "/statsUserPurchaseSku", consumes = "application/json")
    public ResultDto<ApiStatsUserPurchaseSkuResp> statsUserPurchaseSku(@RequestBody ApiStatsUserPurchaseSkuReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单统计】商家采购统计", req, func -> {
            // 业务逻辑处理
            StatsUserPurchaseSkuReq queryReq = JsonUtil.copy(req, StatsUserPurchaseSkuReq.class);
            queryReq.setUserId(TracerUtil.getMemberDto().getId());
            StatsUserPurchaseSkuResp statsUserPurchaseSkuResp = mallOrderRemoteService.statsUserPurchaseSku(queryReq);
            return JsonUtil.copy(statsUserPurchaseSkuResp, ApiStatsUserPurchaseSkuResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/exportPurchaseStats", consumes = "application/json")
    public ResultDto<ApiStatsPurchaseSkuExportResp> exportPurchaseStats(@RequestBody ApiStatsUserPurchaseSkuReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单统计】导出商家采购统计", req, func -> {
            // 业务逻辑处理
            StatsUserPurchaseSkuReq queryReq = JsonUtil.copy(req, StatsUserPurchaseSkuReq.class);
            queryReq.setUserId(TracerUtil.getMemberDto().getId());
            return mallOrderService.exportUserPurchaseSku(queryReq);
        });
    }

}
