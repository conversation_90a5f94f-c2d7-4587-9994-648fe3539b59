package com.sankuai.shangou.seashop.mall.core.thrift.impl.trade;

import java.util.Collections;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.trade.thrift.core.ShoppingCartCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiAddFromAddonReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiAddShoppingCartReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiBatchDeleteShoppingCartSkuReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiChangeShoppingCartQuantityReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiClearInvalidShoppingCartSkuReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiDeleteShoppingCartSkuReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiSelectAllReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiSelectShopReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiSelectShoppingCartSkuReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiAddFromAddonResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiShopProductResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiUserShoppingCartResp;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.trade.MallTradeCartRemoteService;
import com.sankuai.shangou.seashop.trade.thrift.core.request.AddFromAddonReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.AddShoppingCartReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ChangeShoppingCartQuantityReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ClearInvalidShoppingCartSkuReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.DeleteShoppingCartSkuReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SelectAllReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SelectShopReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SelectShoppingCartSkuReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.AddFromAddonResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.ShopProductResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.UserShoppingCartResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/shoppingCart")
@Slf4j
public class MallShoppingCartCmdController {

    @Resource
    private MallTradeCartRemoteService mallTradeCartRemoteService;
    @Resource
    private ShoppingCartCmdFeign shoppingCartCmdFeign;

    @NeedLogin
    @PostMapping(value = "/addShoppingCart", consumes = "application/json")
    public ResultDto<BaseResp> addShoppingCart(@RequestBody ApiAddShoppingCartReq addReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】添加购物车", addReq, func -> {
            AddShoppingCartReq addShoppingCartDto = JsonUtil.copy(addReq, AddShoppingCartReq.class);
            addShoppingCartDto.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.addShoppingCart(addShoppingCartDto));
        });
    }

    @NeedLogin
    @PostMapping(value = "/addFromAddon", consumes = "application/json")
    public ResultDto<ApiAddFromAddonResp> addFromAddon(@RequestBody ApiAddFromAddonReq addReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】凑单加购", addReq, func -> {
            AddFromAddonReq req = JsonUtil.copy(addReq, AddFromAddonReq.class);
            req.setUserId(TracerUtil.getMemberDto().getId());
            AddFromAddonResp resp = ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.addFromAddon(req));
            return JsonUtil.copy(resp, ApiAddFromAddonResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/deleteShoppingCart", consumes = "application/json")
    public ResultDto<BaseResp> deleteShoppingCart(@RequestBody ApiDeleteShoppingCartSkuReq deleteReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】删除购物车sku", deleteReq, func -> {
            deleteReq.checkParameter();
            DeleteShoppingCartSkuReq tradeDeleteReq = new DeleteShoppingCartSkuReq();
            tradeDeleteReq.setIdList(Collections.singletonList(deleteReq.getId()));
            tradeDeleteReq.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.deleteShoppingCart(tradeDeleteReq));
        });
    }

    @NeedLogin
    @PostMapping(value = "/batchDeleteShoppingCart", consumes = "application/json")
    public ResultDto<BaseResp> batchDeleteShoppingCart(@RequestBody ApiBatchDeleteShoppingCartSkuReq deleteReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】批量删除购物车sku", deleteReq, func -> {
            deleteReq.checkParameter();
            DeleteShoppingCartSkuReq tradeDeleteReq = new DeleteShoppingCartSkuReq();
            tradeDeleteReq.setIdList(deleteReq.getIdList());
            tradeDeleteReq.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.deleteShoppingCart(tradeDeleteReq));
        });
    }

    @NeedLogin
    @PostMapping(value = "/clearInvalid", consumes = "application/json")
    public ResultDto<BaseResp> clearInvalid(@RequestBody ApiClearInvalidShoppingCartSkuReq clearReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】清除购物车sku", clearReq, func -> {
            ClearInvalidShoppingCartSkuReq tradeClearReq = new ClearInvalidShoppingCartSkuReq();
            tradeClearReq.setIdList(clearReq.getIdList());
            tradeClearReq.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> shoppingCartCmdFeign.clearInvalid(tradeClearReq));
        });
    }

    @NeedLogin
    @PostMapping(value = "/changeShoppingCartSkuCnt", consumes = "application/json")
    public ResultDto<ApiShopProductResp> changeShoppingCartSkuCnt(@RequestBody ApiChangeShoppingCartQuantityReq changeReq) throws TException {
        Long userId = TracerUtil.getMemberDto().getId();
        log.info("【购物车】变更购物车sku数量, userId={}, 请求参数={}", userId, JsonUtil.toJsonString(changeReq));
        ChangeShoppingCartQuantityReq tradeChangeReq = JsonUtil.copy(changeReq, ChangeShoppingCartQuantityReq.class);
        tradeChangeReq.setUserId(userId);

        ResultDto<ShopProductResp> resp = mallTradeCartRemoteService.changeShoppingCartSkuCnt(tradeChangeReq);
        ResultDto<ApiShopProductResp> result = ResultDto.newWithData(JsonUtil.copy(resp.getData(), ApiShopProductResp.class));
        if (!resp.isSuccess()) {
            result.fail(resp.getCode(), resp.getMessage());
        }
        return result;
    }

    @NeedLogin
    @PostMapping(value = "/selectShopSku", consumes = "application/json")
    public ResultDto<ApiShopProductResp> selectShopSku(@RequestBody ApiSelectShoppingCartSkuReq selectReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】选中sku", selectReq, func -> {
            selectReq.setUserId(TracerUtil.getMemberDto().getId());
            ShopProductResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    shoppingCartCmdFeign.selectShopSku(JsonUtil.copy(selectReq, SelectShoppingCartSkuReq.class)));
            return JsonUtil.copy(resp, ApiShopProductResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/selectShop", consumes = "application/json")
    public ResultDto<ApiShopProductResp> selectShop(@RequestBody ApiSelectShopReq selectReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】选中店铺", selectReq, func -> {
            selectReq.setUserId(TracerUtil.getMemberDto().getId());
            ShopProductResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    shoppingCartCmdFeign.selectShop(JsonUtil.copy(selectReq, SelectShopReq.class)));
            return JsonUtil.copy(resp, ApiShopProductResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/selectAll", consumes = "application/json")
    public ResultDto<ApiUserShoppingCartResp> selectAll(@RequestBody ApiSelectAllReq selectReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】选中整个购物车", selectReq, func -> {
            selectReq.setUserId(TracerUtil.getMemberDto().getId());

            UserShoppingCartResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    shoppingCartCmdFeign.selectAll(JsonUtil.copy(selectReq, SelectAllReq.class)));
            return JsonUtil.copy(resp, ApiUserShoppingCartResp.class);
        });
    }

}
