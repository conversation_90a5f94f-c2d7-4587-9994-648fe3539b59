package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiAdvanceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.AdvanceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.AdvanceQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiAdvance")
public class MApiAdvanceQueryController {

    @Resource
    private AdvanceQueryFeign advanceQueryFeign;

    @GetMapping(value = "/getOne")
    public ResultDto<ApiAdvanceResp> getOne() throws TException {
        return ThriftResponseHelper.responseInvoke("getOne", null, req -> {
            AdvanceResp advanceResp = ThriftResponseHelper.executeThriftCall(() -> advanceQueryFeign.getOne());
            return JsonUtil.copy(advanceResp, ApiAdvanceResp.class);
        });
    }
}
