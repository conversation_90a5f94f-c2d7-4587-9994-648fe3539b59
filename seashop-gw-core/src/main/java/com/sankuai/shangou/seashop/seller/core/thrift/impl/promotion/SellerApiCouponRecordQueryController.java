package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerCouponRecordRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.promotion.SellerApiCouponRecordService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiCouponRecordQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiCouponRecordListResp;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiCouponRecord")
public class SellerApiCouponRecordQueryController {

    @Resource
    private SellerCouponRecordRemoteService sellerCouponRecordRemoteService;

    @Resource
    private SellerApiCouponRecordService sellerApiCouponRecordService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiCouponRecordListResp>> pageList(@RequestBody ApiCouponRecordQueryReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            CouponRecordQueryReq queryReq = JsonUtil.copy(req, CouponRecordQueryReq.class);
            queryReq.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<CouponRecordSimpleResp> listRespBasePageResp = sellerCouponRecordRemoteService.pageList(queryReq);
            return PageResultHelper.transfer(listRespBasePageResp, ApiCouponRecordListResp.class);
        });
    }

    @PostMapping(value = "/exportCouponRecord", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> exportCouponRecord(@RequestBody ApiCouponRecordQueryReq request) throws TException {
        
        return ThriftResponseHelper.responseInvoke("exportCouponRecord", request, req -> {
            sellerApiCouponRecordService.exportCouponRecord(req, TracerUtil.getShopDto());
            return BaseResp.of();
        });
    }
}
