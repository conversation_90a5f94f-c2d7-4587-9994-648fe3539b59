package com.sankuai.shangou.seashop.seller.core.thrift.impl.system.task;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.dto.SellerTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.base.ApiSellerTaskDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.task.ApiQueryTaskReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sellerApi/apiSellerTask")
@Slf4j
public class SellerApiSellerTaskQueryController {

    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiSellerTaskDto>> pageList(@RequestBody ApiQueryTaskReq queryReq) throws TException {
        log.info("【供应商后台任务】查询分页列表, 请求参数={}", queryReq);
        
        return ThriftResponseHelper.responseInvoke("pageList", queryReq, func -> {
            LoginShopDto shop = TracerUtil.getShopDto();
            func.setOperatorId(shop.getManagerId());
            // 业务逻辑处理
            BasePageResp<SellerTaskDto> sellerTaskDtoBasePageResp = sellerExportTaskBiz.pageList(JsonUtil.copy(func, QueryTaskReq.class));

            return PageResultHelper.transfer(sellerTaskDtoBasePageResp, ApiSellerTaskDto.class);
        });
    }
}
