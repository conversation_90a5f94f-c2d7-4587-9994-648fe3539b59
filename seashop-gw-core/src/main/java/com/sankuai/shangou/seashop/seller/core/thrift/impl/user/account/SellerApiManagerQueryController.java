package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMenuResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMenuAuthReq;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiManagerService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryManagerPageReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryManagerReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiManagerResp;

import java.util.List;

/**
 * @description: 商家服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/sellerApi/apiManager")
public class SellerApiManagerQueryController {
    @Resource
    private SellerApiManagerService sellerApiManagerService;


    @PostMapping(value = "/queryManagerPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiManagerResp>> queryManagerPage(@RequestBody ApiQueryManagerPageReq queryManagerPageReq) {
        return ThriftResponseHelper.responseInvoke("queryManagerPage", queryManagerPageReq, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            queryManagerPageReq.setShopId(loginShopDto.getShopId());
            return sellerApiManagerService.queryManagerPage(queryManagerPageReq);
        });
    }

    @PostMapping(value = "/queryManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiManagerResp> queryManager(@RequestBody BaseIdReq baseIdReq) {
        return ThriftResponseHelper.responseInvoke("queryManager", baseIdReq, req -> {
            ApiQueryManagerReq queryManagerReq = new ApiQueryManagerReq();
            queryManagerReq.setUserId(baseIdReq.getId());
            queryManagerReq.setOperationShopId(TracerUtil.getShopDto().getShopId());
            return sellerApiManagerService.queryManager(queryManagerReq);
        });
    }


    @GetMapping(value = "/queryMenuAuth")
    @Operation(summary = "查询用户权限")
    @NeedLogin(userType = RoleEnum.SHOP)
    ResultDto<List<ApiMenuResp>> queryMenuAuth() throws TException {
        return ThriftResponseHelper.responseInvoke("queryMenuAuth", null, req -> {
            Long managerId = TracerUtil.getShopDto().getManagerId();
            QueryMenuAuthReq authReq = new QueryMenuAuthReq();
            authReq.setManagerId(managerId);
            authReq.setPlat(1L);
            return sellerApiManagerService.queryMenuAuth(authReq);
        });
    }
}
