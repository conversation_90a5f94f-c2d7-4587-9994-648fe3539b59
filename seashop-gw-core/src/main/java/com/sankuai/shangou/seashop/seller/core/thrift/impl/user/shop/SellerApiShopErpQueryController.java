package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import java.util.Objects;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.jst.ErpJstAuthQueryUrlReq;
import com.sankuai.shangou.seashop.erp.thrift.biz.response.jst.ErpJstAuthQueryUrlResp;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopErpRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.erp.ApiQueryShopErpReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.jst.ApiErpJstAuthQueryUrlReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.erp.ApiQueryShopErpResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.erp.ApiWdtTokenResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.jst.ApiErpJstAuthQueryUrlResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.WdtTokenResp;

/**
 * @author： liweisong
 * @create： 2023/11/28 14:59
 */
@RestController
@RequestMapping("/sellerApi/apiShopErp")
public class SellerApiShopErpQueryController {

    @Resource
    private SellerShopErpRemoteService sellerShopErpRemoteService;

    @PostMapping(value = "/queryShopErp", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiQueryShopErpResp> queryShopErp(@RequestBody ApiQueryShopErpReq queryShopErpReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryShopErp", queryShopErpReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.checkParameter();
            QueryShopErpResp queryShopErpResp = sellerShopErpRemoteService.queryShopErp(JsonUtil.copy(req, QueryShopErpReq.class));

            ErpJstAuthQueryUrlReq erpJstAuthQueryUrlReq = new ErpJstAuthQueryUrlReq();
            erpJstAuthQueryUrlReq.setShopId(TracerUtil.getShopDto().getShopId());
            ErpJstAuthQueryUrlResp erpJstAuthQueryUrlResp = sellerShopErpRemoteService.queryAuthUrl(erpJstAuthQueryUrlReq);
            if (!Objects.isNull(queryShopErpResp) && !Objects.isNull(erpJstAuthQueryUrlResp)) {
                queryShopErpResp.setJstUrl(erpJstAuthQueryUrlResp.getUrl());
            }
            return JsonUtil.copy(queryShopErpResp, ApiQueryShopErpResp.class);
        });
    }

    @PostMapping(value = "/queryWdtTokenByShopId", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiWdtTokenResp> queryWdtTokenByShopId(@RequestBody ApiQueryShopErpReq queryShopErpReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryWdtTokenByShopId", queryShopErpReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.checkParameter();
            WdtTokenResp wdtTokenResp = sellerShopErpRemoteService.queryWdtTokenByShopId(JsonUtil.copy(req, QueryShopErpReq.class));
            return JsonUtil.copy(wdtTokenResp, ApiWdtTokenResp.class);
        });
    }

    @PostMapping(value = "/queryAuthUrl", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiErpJstAuthQueryUrlResp> queryAuthUrl(@RequestBody ApiErpJstAuthQueryUrlReq erpJstAuthQueryUrlReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryAuthUrl", erpJstAuthQueryUrlReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.checkParameter();
            ErpJstAuthQueryUrlResp erpJstAuthQueryUrlResp = sellerShopErpRemoteService.queryAuthUrl(JsonUtil.copy(req, ErpJstAuthQueryUrlReq.class));
            return JsonUtil.copy(erpJstAuthQueryUrlResp, ApiErpJstAuthQueryUrlResp.class);
        });
    }
}
