package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiQueryShopCategoryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiShopCategoryTreeResp;
import com.sankuai.shangou.seashop.product.thrift.core.ShopCategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.QueryShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory.ShopCategoryTreeResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/15 13:56
 */
@RestController
@RequestMapping("/mApi/apiShopCategory")
public class MApiShopCategoryQueryController {

    @Resource
    private ShopCategoryQueryFeign shopCategoryQueryFeign;

    @PostMapping(value = "/queryShopCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiShopCategoryTreeResp> queryShopCategory(@RequestBody ApiQueryShopCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopCategory", request, req -> {

            QueryShopCategoryReq param = JsonUtil.copy(req, QueryShopCategoryReq.class);
            ShopCategoryTreeResp treeResp = ThriftResponseHelper.executeThriftCall(() -> shopCategoryQueryFeign.queryShopCategory(param));
            return JsonUtil.copy(treeResp, ApiShopCategoryTreeResp.class);
        });
    }

}
