package com.sankuai.shangou.seashop.seller.core.service.order.impl;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerOrderReq;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.seller.core.service.order.SellerOrderService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQueryOrderDistributionReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQuerySellerOrderReq;

/**
 * <AUTHOR>
 * @date 2024/01/02 13:39
 */
@Service
public class SellerOrderServiceImpl implements SellerOrderService {

    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;

    @Override
    public void exportOrderInvoice(ApiQuerySellerOrderReq request, LoginShopDto shopInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.ORDER_INVOICE_LIST);

        QuerySellerOrderReq param = JsonUtil.copy(request, QuerySellerOrderReq.class);
        param.setShopId(TracerUtil.getShopDto().getShopId());
        param.setQueryFrom(OrderQueryFromEnum.SELLER_PC);
        createExportTaskBo.setExecuteParam(param);

        createExportTaskBo.setOperatorId(TracerUtil.getShopDto().getManagerId());
        createExportTaskBo.setOperatorName(shopInfo.getManagerName());
        sellerExportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public void exportOrderDistribution(ApiQueryOrderDistributionReq request, LoginShopDto shopInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.EXPORT_ORDER_DISTRIBUTION);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getShopDto().getManagerId());
        createExportTaskBo.setOperatorName(TracerUtil.getShopDto().getManagerName());
        sellerExportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public void exportOrderProductDistribution(ApiQueryOrderDistributionReq request, LoginShopDto shopInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.EXPORT_ORDER_PRODUCT_DISTRIBUTION);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getShopDto().getManagerId());
        createExportTaskBo.setOperatorName(TracerUtil.getShopDto().getManagerName());
        sellerExportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public void export(ApiQuerySellerOrderReq queryReq, UserDto user) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.ORDER_PAGE_LIST);
        createExportTaskBo.setExecuteParam(queryReq);
        createExportTaskBo.setOperatorId(user.getUserId());
        createExportTaskBo.setOperatorName(user.getUserName());
        sellerExportTaskBiz.create(createExportTaskBo);
    }
}
