package com.sankuai.shangou.seashop.m.core.thrift.impl.base;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MobileFootCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddOrUpdateFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteFootMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveFootMenusReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiAddOrUpdateFootMenusReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiDeleteFootMenuReq;

import io.swagger.v3.oas.annotations.Operation;


/**
 * @author： liweisong
 * @create： 2023/11/29 11:47
 */
@RestController
@RequestMapping("/mApi/apiMobileFoot")
public class MApiMobileFootCmdController {

    @Resource
    private MobileFootCmdFeign mobileFootCmdFeign;

    @PostMapping(value = "/addOrUpdateFootMenus", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> addOrUpdateFootMenus(@RequestBody ApiAddOrUpdateFootMenusReq addFootMenusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addOrUpdateFootMenus", addFootMenusReq, req -> {
            req.checkParameter();
            AddOrUpdateFootMenusReq bean = JsonUtil.copy(req, AddOrUpdateFootMenusReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> mobileFootCmdFeign.addOrUpdateFootMenus(bean));
        });
    }

    @PostMapping(value = "/saveFootMenus", consumes = "application/json")
    @Operation(summary = "编辑底部菜单")
    public ResultDto<BaseResp> saveFootMenus(@RequestBody SaveFootMenusReq saveFootMenusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("saveFootMenus", saveFootMenusReq, req -> {
            req.checkParameter();

            return ThriftResponseHelper.executeThriftCall(() -> mobileFootCmdFeign.saveFootMenus(req));
        });
    }

    @PostMapping(value = "/deleteFootMenu", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deleteFootMenu(@RequestBody ApiDeleteFootMenuReq deleteFootMenuReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteFootMenu", deleteFootMenuReq, req -> {
            req.checkParameter();
            DeleteFootMenuReq bean = JsonUtil.copy(req, DeleteFootMenuReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> mobileFootCmdFeign.deleteFootMenu(bean));
        });
    }
}
