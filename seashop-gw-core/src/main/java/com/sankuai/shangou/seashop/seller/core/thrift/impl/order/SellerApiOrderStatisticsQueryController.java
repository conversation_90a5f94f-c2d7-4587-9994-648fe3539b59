package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderStatisticsQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.stats.StatsShopTopNSaleProductReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.TopProductSaleStatsResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.stats.ApiStatsShopTopNSaleProductReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.stats.ApiSellerIndexTradeStatsResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.stats.ApiTopProductSaleStatsResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sellerApi/apiOrderStatistics")
@Slf4j
public class SellerApiOrderStatisticsQueryController {

    @Resource
    private OrderStatisticsQueryFeign orderStatisticsQueryFeign;

    @NeedLogin(userType = RoleEnum.SHOP)
    @GetMapping(value = "/statsSellerIndexTradeData")
    public ResultDto<ApiSellerIndexTradeStatsResp> statsSellerIndexTradeData() throws TException {

        

        return ThriftResponseHelper.responseInvoke("【订单统计】供应商首页交易数据统计", TracerUtil.getShopDto(),
            func -> {
                long shopId = TracerUtil.getShopDto().getShopId();
                return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() ->
                        orderStatisticsQueryFeign.statsSellerIndexTradeData(shopId)), ApiSellerIndexTradeStatsResp.class);
            });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/statsTopNSaleProduct", consumes = "application/json")
    public ResultDto<ApiTopProductSaleStatsResp> statsTopNSaleProduct(@RequestBody ApiStatsShopTopNSaleProductReq req) throws TException {

        

        return ThriftResponseHelper.responseInvoke("【订单统计】供应商首页销售排行前N的商品统计", req,
            func -> {
                long shopId = TracerUtil.getShopDto().getShopId();
                req.setShopId(shopId);
                TopProductSaleStatsResp topProductSaleStatsResp = ThriftResponseHelper.executeThriftCall(() ->
                        orderStatisticsQueryFeign.statsTopNSaleProduct(JsonUtil.copy(req, StatsShopTopNSaleProductReq.class)));
                return JsonUtil.copy(topProductSaleStatsResp, ApiTopProductSaleStatsResp.class);
            });
    }
}
