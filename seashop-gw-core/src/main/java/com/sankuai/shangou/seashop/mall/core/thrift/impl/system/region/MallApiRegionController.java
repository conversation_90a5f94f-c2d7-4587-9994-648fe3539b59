package com.sankuai.shangou.seashop.mall.core.thrift.impl.system.region;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiBaseRegionRes;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.RegionCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseRegionRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mallApi/apiRegion")
public class MallApiRegionController {

    @Resource
    private RegionQueryFeign regionQueryFeign;

    @Resource
    private RegionCMDFeign regionCMDFeign;

    @GetMapping(value = "/getTreeRegions")
    public ResultDto<String> getTreeRegions() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("getTreeRegions", i, req -> {
            String result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getTreeRegions());
            return result;
        });
    }

    @GetMapping(value = "/getAllRegions")
    public ResultDto<List<ApiBaseRegionRes>> getAllRegions() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("getAllRegions", i, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getAllRegions());
            return JsonUtil.copyList(result, ApiBaseRegionRes.class);
        });
    }

    @GetMapping(value = "/getRegionByParentId")
    public ResultDto<List<ApiBaseRegionRes>> getRegionByParentId(@RequestParam long parentId) throws TException {
        return ThriftResponseHelper.responseInvoke("getRegionByParentId", parentId, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getRegionByParentId(req));
            return JsonUtil.copyList(result, ApiBaseRegionRes.class);
        });
    }

    @GetMapping(value = "/getParentRegions")
    public ResultDto<List<ApiBaseRegionRes>> getParentRegions(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getParentRegions", id, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getParentRegions(req));
            return JsonUtil.copyList(result, ApiBaseRegionRes.class);
        });
    }

    @GetMapping(value = "/getSubRegions")
    public ResultDto<List<ApiBaseRegionRes>> getSubRegions(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getSubRegions", id, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getSubRegions(req));
            return JsonUtil.copyList(result, ApiBaseRegionRes.class);
        });
    }

    @GetMapping(value = "/getTrackRegionsById")
    public ResultDto<List<ApiBaseRegionRes>> getTrackRegionsById(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getTrackRegionsById", id, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getTrackRegionsById(req));
            return JsonUtil.copyList(result, ApiBaseRegionRes.class);
        });
    }


}
