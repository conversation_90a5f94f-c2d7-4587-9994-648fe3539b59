package com.sankuai.shangou.seashop.seller.core.service.order;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQueryOrderDistributionReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQuerySellerOrderReq;

/**
 * <AUTHOR>
 * @date 2024/01/02 11:58
 */
public interface SellerOrderService {

    void exportOrderInvoice(ApiQuerySellerOrderReq request, LoginShopDto shopInfo);

    /**
     * 导出订单配货单
     *
     * @param request
     */
    void exportOrderDistribution(ApiQueryOrderDistributionReq request, LoginShopDto shopInfo);

    /**
     * 导出商品配货单
     *
     * @param request
     */
    void exportOrderProductDistribution(ApiQueryOrderDistributionReq request, LoginShopDto shopInfo);

    /**
     * 导出订单
     *
     * @param queryReq
     * @param user
     */
    void export(ApiQuerySellerOrderReq queryReq, UserDto user);
}
