package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.seller.common.remote.SellerRegionRemoteService;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.user.ApiShopFreeShippingAreaDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiProductShopInfoResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiShippingSettingsResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiShopDetailResp;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiShop")
public class SellerApiShopQueryController {

    @Resource
    private SellerShopRemoteService sellerShopRemoteService;

    @Resource
    private SellerRegionRemoteService sellerRegionRemoteService;

    @PostMapping(value = "/getShippingSettings", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiShippingSettingsResp> getShippingSettings(@RequestBody BaseIdReq request) {
        
        return ThriftResponseHelper.responseInvoke("getShippingSettings", request,
            req -> {
                req.setId(TracerUtil.getShopDto().getShopId());
                ApiShippingSettingsResp shippingSettings = sellerShopRemoteService.getShippingSettings(req);
                List<ApiShopFreeShippingAreaDto> areaList = shippingSettings.getAreaList();
                if (CollUtil.isNotEmpty(areaList)) {
                    RegionIdsReq regionIdsReq = new RegionIdsReq();
                    regionIdsReq.setRegionIds(areaList.stream().map(ApiShopFreeShippingAreaDto::getRegionId).collect(Collectors.toList()));
                    Map<String, AllPathRegionResp> allPathRegions = sellerRegionRemoteService.getAllPathRegions(regionIdsReq);
                    if (CollUtil.isNotEmpty(allPathRegions)) {
                        areaList.parallelStream().forEach(area -> {
                            AllPathRegionResp allPathRegionResp = allPathRegions.get(area.getRegionId().toString());
                            if (allPathRegionResp != null) {
                                area.setProvinceId(allPathRegionResp.getProvinceId());
                                area.setProvinceName(allPathRegionResp.getProvinceName());
                                area.setCityId(allPathRegionResp.getCityId());
                                area.setCityName(allPathRegionResp.getCityName());
                            }
                        });
                    }
                }
                return shippingSettings;
            }
        );
    }

    @GetMapping(value = "/queryDetail")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiShopDetailResp> queryDetail() {
        
        LoginShopDto shopDto = TracerUtil.getShopDto();
        BaseIdReq baseIdReq = new BaseIdReq();
        baseIdReq.setId(shopDto.getId());
        return ThriftResponseHelper.responseInvoke("queryDetail", baseIdReq,
            req -> sellerShopRemoteService.queryDetail(baseIdReq)
        );
    }

    @GetMapping(value = "/queryProductShop")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiProductShopInfoResp> queryProductShop() {
        
        //转化返回值
        return ThriftResponseHelper.responseInvoke("queryProductShop", null,
            req -> sellerShopRemoteService.queryProductShop(TracerUtil.getShopDto().getShopId())
        );
    }

}
