package com.sankuai.shangou.seashop.m.core.service.export.handler.order.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.base.export.writeHandler.FindMergeRegionWhenWriteCell;
import com.sankuai.shangou.seashop.m.common.remote.order.MOrderRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.order.eo.OrderInfoEo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.OrderAndItemFlatListResp;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class OrderInfoWrapper extends PageExportWrapper<OrderInfoEo, QueryPlatformOrderReq> {

    private final MOrderRemoteService MOrderRemoteService;
    private OrderQueryFeign orderQueryFeign;

    public OrderInfoWrapper(MOrderRemoteService MOrderRemoteService,OrderQueryFeign orderQueryFeign) {
        this.MOrderRemoteService = MOrderRemoteService;
        this.orderQueryFeign = orderQueryFeign;
    }

    // 滚动查询的ID，本对象是每次导出时都会创建的新对象，每次导出都是独立的，所以设置为局部变量
    private String scrollId;
    private final static long SCROLL_TIME_VALUE_MINUTE = 2L;

    @Override
    public List<OrderInfoEo> getPageList(QueryPlatformOrderReq param) {
        log.info("【订单】查询订单导出参数:{}", JsonUtil.toJsonString(param));
        List<OrderInfoEo> resultList = null;
        if (StrUtil.isBlank(scrollId)) {
            param.setTimeValueMinutes(SCROLL_TIME_VALUE_MINUTE);
            OrderAndItemFlatListResp scrollResp = ThriftResponseHelper.executeThriftCall(() ->
                    orderQueryFeign.getScrollIdForPlatformExport(param));
            if (scrollResp == null || StrUtil.isBlank(scrollResp.getScrollId())) {
                return null;
            }
            scrollId = scrollResp.getScrollId();
            resultList = BeanUtil.copyToList(scrollResp.getDataList(), OrderInfoEo.class);
        }
        else {
            EsScrollQueryReq scrollReq = new EsScrollQueryReq();
            scrollReq.setScrollId(scrollId);
            scrollReq.setTimeValueMinutes(SCROLL_TIME_VALUE_MINUTE);
            List<OrderAndItemInfoDto> list = MOrderRemoteService.listOrderAndItemFlatByScroll(scrollReq);
            resultList = BeanUtil.copyToList(list, OrderInfoEo.class);
        }
        // 平台端需要对商家的收货人，手机号码脱敏
        if (CollUtil.isNotEmpty(resultList)) {
            resultList.forEach(data -> {
                if (StrUtil.isNotBlank(data.getShipTo())) {
                    data.setShipTo(DesensitizedUtil.chineseName(data.getShipTo()));
                }
                if (StrUtil.isNotBlank(data.getCellPhone())) {
                    data.setCellPhone(DesensitizedUtil.mobilePhone(data.getCellPhone()));
                }
            });
        }
        return resultList;
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return Collections.singletonList(new FindMergeRegionWhenWriteCell(OrderInfoEo.class, null, getDataSupplier(), getPrevNum()));
    }

    @Override
    public Integer getBatchCount() {
        return 10;
    }

    @Override
    public Integer getMaxBatch() {
        return 5000;
    }
}
