package com.sankuai.shangou.seashop.mall.core.service.export.handler.refund.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RefundExportEo {

    @ExcelProperty(value = "售后编号")
    private String refundId;
    @ExcelProperty(value = "订单编号")
    private String orderId;
    @ExcelProperty(value = "店铺名称")
    private String shopName;
    @ExcelProperty(value = "商品名称")
    private String productName;
    @ExcelProperty(value = "规格名称")
    private String skuDesc;
    @ExcelProperty(value = "买家账号")
    private String userName;
    @ExcelProperty(value = "联系人")
    private String contactUserName;
    @ExcelProperty(value = "联系电话")
    private String contactUserPhone;
    @ExcelProperty(value = "申请时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date applyDate;
    @ExcelProperty(value = "完成时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date managerConfirmDate;
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ExcelProperty(value = "退款状态")
    private String refundStatusDesc;
    @ExcelProperty(value = "退款方式")
    private String refundPayTypeDesc;
    @ExcelProperty(value = "退款理由")
    private String reason;

}
