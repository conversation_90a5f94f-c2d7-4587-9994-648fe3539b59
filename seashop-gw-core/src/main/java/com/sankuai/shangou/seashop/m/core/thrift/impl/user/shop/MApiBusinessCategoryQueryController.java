package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiBusinessCategoryService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryResp;

@RestController
@RequestMapping("/mApi/apiBusinessCategory")
public class MApiBusinessCategoryQueryController {
    @Resource
    private MApiBusinessCategoryService mApiBusinessCategoryService;

    @PostMapping(value = "/queryPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiBusinessCategoryResp>> queryPage(@RequestBody ApiQueryBusinessCategoryPageReq queryBusinessCategoryPageReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryPage", queryBusinessCategoryPageReq, func -> {
            // 参数对象转换
            return mApiBusinessCategoryService.queryPage(func);
        });
    }
}
