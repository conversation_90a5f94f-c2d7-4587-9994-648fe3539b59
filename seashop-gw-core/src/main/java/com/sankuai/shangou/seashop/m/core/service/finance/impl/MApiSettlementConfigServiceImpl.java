package com.sankuai.shangou.seashop.m.core.service.finance.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiSettlementConfigService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettlementConfigReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettlementConfigResp;
import com.sankuai.shangou.seashop.order.thrift.finance.SettlementConfigCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.SettlementConfigQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementConfigReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettlementConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class MApiSettlementConfigServiceImpl implements MApiSettlementConfigService {

    @Resource
    private SettlementConfigCmdFeign settlementConfigCmdFeign;
    @Resource
    private SettlementConfigQueryFeign settlementConfigQueryFeign;

    @Override
    public ApiSettlementConfigResp getConfig() {
        SettlementConfigResp config = ThriftResponseHelper.executeThriftCall(() -> settlementConfigQueryFeign.getConfig());
        if (null != config) {
            return JsonUtil.copy(config, ApiSettlementConfigResp.class);
        }
        return null;
    }

    @Override
    public BaseResp update(ApiSettlementConfigReq request) {
        SettlementConfigReq req = JsonUtil.copy(request, SettlementConfigReq.class);
        req.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> settlementConfigCmdFeign.update(req));
    }
}
