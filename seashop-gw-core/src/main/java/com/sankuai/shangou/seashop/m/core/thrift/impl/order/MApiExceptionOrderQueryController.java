package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiQueryExceptionOrderReq;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiExceptionOrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.ExceptionOrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryExceptionOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mApi/apiExceptionOrder")
@Slf4j
public class MApiExceptionOrderQueryController {

    @Resource
    private ExceptionOrderQueryFeign exceptionOrderQueryFeign;

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiExceptionOrderInfoDto>> pageList(@RequestBody ApiQueryExceptionOrderReq queryReq) throws TException {
        log.info("【异常订单】分页查询异常订单, 请求参数={}", queryReq);
        return ThriftResponseHelper.responseInvoke("pageList", queryReq, func -> {
            QueryExceptionOrderReq bean = JsonUtil.copy(func, QueryExceptionOrderReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return PageResultHelper.transfer(ThriftResponseHelper.executeThriftCall(() -> exceptionOrderQueryFeign.pageList(bean)), ApiExceptionOrderInfoDto.class);
        });
    }
}
