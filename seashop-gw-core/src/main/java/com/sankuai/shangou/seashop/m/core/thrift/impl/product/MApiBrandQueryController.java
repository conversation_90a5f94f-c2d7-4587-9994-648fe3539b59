package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryBrandReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiBrandDto;
import com.sankuai.shangou.seashop.product.thrift.core.BrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandDto;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/mApi/apiBrand")
public class MApiBrandQueryController {

    @Resource
    private BrandQueryFeign brandQueryFeign;

    @PostMapping(value = "/queryBrandForPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiBrandDto>> queryBrandForPage(@RequestBody ApiQueryBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandForPage", request, req -> {

            BasePageResp<BrandDto> pageResp = ThriftResponseHelper.executeThriftCall(() -> brandQueryFeign.queryBrandForPage(JsonUtil.copy(req, QueryBrandReq.class)));
            return PageResultHelper.transfer(pageResp, ApiBrandDto.class);
        });
    }

    @PostMapping(value = "/queryBrandDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiBrandDto> queryBrandDetail(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandDetail", request, req -> {

            BrandDto brandDto = ThriftResponseHelper.executeThriftCall(() -> brandQueryFeign.queryBrandDetail(req));
            return JsonUtil.copy(brandDto, ApiBrandDto.class);
        });
    }
}
