package com.sankuai.shangou.seashop.m.core.service.user.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdImportCategoryReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryResp;

public interface MApiBusinessCategoryService {
    /**
     * 根据条件分页查询类目申请信息
     *
     * @param queryBusinessCategoryPageReq 查询条件
     * @return 申请信息
     */
    BasePageResp<ApiBusinessCategoryResp> queryPage(ApiQueryBusinessCategoryPageReq queryBusinessCategoryPageReq);

    /**
     * 更新类目申请信息
     *
     * @param reqList 更新参数
     * @return 更新结果
     */
    BaseResp updateBusinessCategory(ApiCmdBusinessCategoryReqList reqList);

    /**
     * 删除类目申请信息
     *
     * @param req 删除参数
     * @return 删除结果
     */
    BaseResp deleteBusinessCategory(BaseIdReq req);

    /**
     * 导入类目申请信息
     *
     * @param apiReq 导入参数
     * @return 导入结果
     */
    BaseImportResp importBusinessCategory(ApiCmdImportCategoryReq apiReq);
}
