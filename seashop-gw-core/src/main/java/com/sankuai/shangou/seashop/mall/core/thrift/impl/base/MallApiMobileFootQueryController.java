package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.ApiQueryFootMenusReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiQueryFootMenusResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MobileFootQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryFootMenusResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * @author： liweisong
 * @create： 2023/11/29 11:48
 */
@RestController
@RequestMapping("/mallApi/apiMobileFoot")
public class MallApiMobileFootQueryController {

    @Resource
    private MobileFootQueryFeign mobileFootQueryFeign;

    @PostMapping(value = "/queryFootMenus", consumes = "application/json")
    public ResultDto<ApiQueryFootMenusResp> queryFootMenus(@RequestBody ApiQueryFootMenusReq queryFootMenusReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryFootMenus", queryFootMenusReq, req -> {

            QueryFootMenusResp resp = ThriftResponseHelper.executeThriftCall(() -> mobileFootQueryFeign.queryFootMenus(JsonUtil.copy(req, QueryFootMenusReq.class)));
            return JsonUtil.copy(resp, ApiQueryFootMenusResp.class);
        });
    }
}
