package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiFlashSaleAuditReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiFlashSaleShowReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleAuditReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleShowReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFlashSale")
public class MApiFlashSaleCmdController {

    @Resource
    private FlashSaleCmdFeign flashSaleCmdFeign;

    @PostMapping(value = "/audit", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> audit(@RequestBody ApiFlashSaleAuditReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("audit", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.audit(JsonUtil.copy(request, FlashSaleAuditReq.class)));
        });
    }

    @PostMapping(value = "/show", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> show(@RequestBody ApiFlashSaleShowReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("show", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.show(JsonUtil.copy(req, FlashSaleShowReq.class)));
        });
    }
}
