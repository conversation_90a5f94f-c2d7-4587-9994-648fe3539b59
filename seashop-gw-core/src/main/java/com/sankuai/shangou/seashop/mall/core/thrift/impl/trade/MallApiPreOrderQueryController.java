package com.sankuai.shangou.seashop.mall.core.thrift.impl.trade;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.trade.thrift.core.PreOrderQueryFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiPreOrder")
@Slf4j
public class MallApiPreOrderQueryController {

    @Resource
    private PreOrderQueryFeign preOrderQueryFeign;

    @NeedLogin
    @GetMapping(value = "/getSubmitToken")
    public ResultDto<String> getSubmitToken() {
        log.info("【预订单】获取订单提交防重token");
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("getSubmitToken", userId,
            func -> ThriftResponseHelper.executeThriftCall(() -> preOrderQueryFeign.getSubmitToken(userId)));
    }
}
