package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiFavoriteShopCmdReq;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.mall.core.service.user.account.MallApiFavoriteShopService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/mallApi/apiFavoriteShop")
@Slf4j
public class MallApiFavoriteShopCmdController {
    @Resource
    private MallApiFavoriteShopService mallApiFavoriteShopService;

    @GetMapping(value = "/addFavoriteShop")
    @NeedLogin
    public ResultDto<BaseResp> addFavoriteShop(@RequestParam Long shopId) throws TException {
        

        return ThriftResponseHelper.responseInvoke("addFavoriteShop", shopId, func -> mallApiFavoriteShopService.addFavoriteShop(func, TracerUtil.getMemberDto() ));
    }

    @PostMapping(value = "/deleteFavoriteShop", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> deleteFavoriteShop(@RequestBody ApiFavoriteShopCmdReq shopId) throws TException {
        //设置用户id
        LoginMemberDto memberDto = TracerUtil.getMemberDto();;
        shopId.setUserId(memberDto.getId());
        return ThriftResponseHelper.responseInvoke("deleteFavoriteShop", shopId, func -> mallApiFavoriteShopService.deleteFavoriteShop(func));
    }
}
