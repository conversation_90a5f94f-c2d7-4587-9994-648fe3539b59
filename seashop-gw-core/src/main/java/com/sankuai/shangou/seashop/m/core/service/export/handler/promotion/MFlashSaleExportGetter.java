package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.common.remote.promotion.MFlashSaleRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo.FlashSaleEo;
import com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.wrapper.FlashSaleDataWrapper;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiFlashSaleQueryReq;

/**
 * @author: lhx
 * @date: 2023/12/25/025
 * @description:
 */
@Service
public class MFlashSaleExportGetter extends AbstractBaseDataGetter<ApiFlashSaleQueryReq>
    implements SingleWrapperDataGetter<ApiFlashSaleQueryReq> {

    @Resource
    private MFlashSaleRemoteService MFlashSaleRemoteService;

    @Override
    public DataContext selectData(ApiFlashSaleQueryReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        // 构造数据包装器，每一个包装器对应一个sheet页，并且每个包装器可以单独定义自己的excel处理器，比如样式、合并单元格等
        BaseExportWrapper<FlashSaleEo, ApiFlashSaleQueryReq> flashSaleDataWrapper = new FlashSaleDataWrapper(MFlashSaleRemoteService);
        context.setSheetDataList(Collections.singletonList(flashSaleDataWrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.FLASH_SALE_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.FLASH_SALE_LIST.getName();
    }
}
