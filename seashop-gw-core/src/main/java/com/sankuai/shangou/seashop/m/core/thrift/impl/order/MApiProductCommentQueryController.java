package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.order.MProductCommentRemoteService;
import com.sankuai.shangou.seashop.m.thrift.core.request.order.ApiQueryProductCommentReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.order.ApiProductCommentResp;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/21 20:00
 */
@RestController
@RequestMapping("/mApi/apiProductComment")
@Slf4j
public class MApiProductCommentQueryController {

    @Resource
    private MProductCommentRemoteService mProductCommentRemoteService;

    @PostMapping(value = "/queryProductCommentForPlatform", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiProductCommentResp>> queryProductCommentForPlatform(@RequestBody ApiQueryProductCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductCommentForPlatform", request, req -> {
            req.checkParameter();

            QueryProductCommentReq remoteReq = JsonUtil.copy(req, QueryProductCommentReq.class);
            BasePageResp<ProductCommentResp> resp = mProductCommentRemoteService.queryProductCommentForPlatform(remoteReq);
            return PageResultHelper.transfer(resp, ApiProductCommentResp.class);
        });
    }
}
