package com.sankuai.shangou.seashop.m.core.service.finance.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositRefundQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.MFinanceRemoteService;
import com.sankuai.shangou.seashop.m.common.remote.user.MShopRemoteService;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiCashDepositRefundService;
import com.sankuai.shangou.seashop.m.core.service.finance.model.ApiShopSimpleModel;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiCashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositRefundResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@Service
@Slf4j
public class MApiCashDepositRefundServiceImpl implements MApiCashDepositRefundService {

    @Resource
    private MFinanceRemoteService mFinanceRemoteService;
    @Resource
    private MShopRemoteService mShopRemoteService;
    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private CashDepositRefundQueryFeign cashDepositRefundQueryFeign;

    @Override
    public BasePageResp<ApiCashDepositRefundResp> refundList(ApiCashDepositRefundQueryReq request) {

        List<ApiShopSimpleModel> shopSimpleModels = new ArrayList<>();

        ShopSimpleQueryReq shopSimpleQueryReq = new ShopSimpleQueryReq();
        if (request.getShopId() != null || StrUtil.isNotBlank(request.getShopName())) {
            List<Long> shopIdList = StringUtils.isEmpty(request.getShopId()) ? null : Arrays.asList(request.getShopId());
            shopSimpleQueryReq.setShopIdList(shopIdList);
            shopSimpleQueryReq.setShopName(request.getShopName());
            ShopSimpleListResp shopSimpleListResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(shopSimpleQueryReq));
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            shopSimpleModels = JsonUtil.copyList(shopSimpleListResp.getList(), ApiShopSimpleModel.class);
        }

        List<Long> shopIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(shopSimpleModels)) {
            // 拿到过滤后的店铺id
            shopIds = shopSimpleModels.stream().map(ApiShopSimpleModel::getId).collect(Collectors.toList());
        }

        CashDepositRefundQueryReq cashDepositRefundQueryReq = JsonUtil.copy(request, CashDepositRefundQueryReq.class);
        cashDepositRefundQueryReq.setShopIdList(shopIds);
        cashDepositRefundQueryReq.setStatus(request.getStatus());
        BasePageResp<CashDepositRefundResp> cashDepositRefundPage = ThriftResponseHelper.executeThriftCall(() ->
                cashDepositRefundQueryFeign.refundList(cashDepositRefundQueryReq));
        if (null == cashDepositRefundPage || CollUtil.isEmpty(cashDepositRefundPage.getData())) {
            return PageResultHelper.defaultEmpty(request.buildPage());
        }

        List<CashDepositRefundResp> cashDepositRefundList = cashDepositRefundPage.getData();
        if (CollUtil.isEmpty(shopSimpleModels)) {
            // 没数据说明之前没查过店铺信息，需要去查一下店铺信息
            shopIds = cashDepositRefundList.stream().map(CashDepositRefundResp::getShopId).collect(Collectors.toList());
            shopSimpleQueryReq.setShopIdList(shopIds);
            ShopSimpleListResp shopSimpleListResp = mShopRemoteService.querySimpleList(shopSimpleQueryReq);
            shopSimpleModels = JsonUtil.copyList(shopSimpleListResp.getList(), ApiShopSimpleModel.class);
        }

        final List<ApiShopSimpleModel> shopSimpleModelsFinal = shopSimpleModels;

        return PageResultHelper.transfer(cashDepositRefundPage, ApiCashDepositRefundResp.class,
            apiCashDepositRefundResp -> {
                // 设置店铺名称
                if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                    ApiShopSimpleModel apiShopSimpleModel = shopSimpleModelsFinal.stream().filter(shopSimpleModel ->
                            shopSimpleModel.getId().equals(apiCashDepositRefundResp.getShopId())).findFirst().orElse(null);
                    if (null != apiShopSimpleModel) {
                        apiCashDepositRefundResp.setShopName(apiShopSimpleModel.getShopName());
                    }
                }
            }
        );
    }

    @Override
    public Long refundAuditNum() {
        CashDepositRefundQueryReq cashDepositRefundQueryReq = new CashDepositRefundQueryReq();
        cashDepositRefundQueryReq.setStatus(0);
        BasePageResp<CashDepositRefundResp> cashDepositRefundPage = ThriftResponseHelper.executeThriftCall(() ->
                cashDepositRefundQueryFeign.refundList(cashDepositRefundQueryReq));
        return cashDepositRefundPage.getTotalCount();
    }

    @Override
    public ApiCashDepositRefundDetailResp refundDetail(Long id) {
        return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundQueryFeign.refundDetail(id)),
                ApiCashDepositRefundDetailResp.class);
    }
}
