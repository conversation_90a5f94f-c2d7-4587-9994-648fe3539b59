package com.sankuai.shangou.seashop.m.core.service.finance;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettlementConfigReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettlementConfigResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
public interface MApiSettlementConfigService {

    /**
     * 获取结算配置
     *
     * @return
     */
    ApiSettlementConfigResp getConfig();

    /**
     * 更新结算配置
     *
     * @param request
     * @return
     */
    BaseResp update(ApiSettlementConfigReq request);
}
