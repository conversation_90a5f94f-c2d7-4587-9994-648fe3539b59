package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiSlideAdListResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.SlideAdQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.SlideAdListResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@RestController
@RequestMapping("/mallApi/apiSlideAd")
public class MallApiSlideAdQueryController {

    @Resource
    private SlideAdQueryFeign slideAdQueryFeign;

    @GetMapping(value = "/queryLimitTimeBuy")
    public ResultDto<ApiSlideAdListResp> queryLimitTimeBuy() throws TException {
        return ThriftResponseHelper.responseInvoke("queryLimitTimeBuy", null, req -> {
            SlideAdListResp slideAdListResp = ThriftResponseHelper.executeThriftCall(() -> slideAdQueryFeign.queryLimitTimeBuy());
            return JsonUtil.copy(slideAdListResp, ApiSlideAdListResp.class);
        });
    }
}
