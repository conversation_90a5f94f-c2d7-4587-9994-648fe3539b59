package com.sankuai.shangou.seashop.openapi.convert;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.openapi.resp.HiCouponDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2024/10/10 11:45
 */
@Component
public class HiCouponConvert {

    public HiCouponDto convertToHiCouponDto(CouponSimpleResp coupon) {
        return Optional.ofNullable(coupon)
                .map(item -> {
                    HiCouponDto dto = new HiCouponDto();
                    dto.setId(item.getId());
                    dto.setName(item.getCouponName());
                    dto.setLimitAmount(item.getOrderAmount());
                    dto.setLimitProduct(item.getUseArea());
                    dto.setAmount(item.getPrice());
                    dto.setStock(item.getNum() - item.getReceiveNum());
                    dto.setGive(item.getReceiveNum());
                    dto.setContent(String.format("满%s减%s", item.getOrderAmount(), item.getPrice()));
                    return dto;
                })
                .orElse(null);
    }

    public List<HiCouponDto> convertToHiCouponDtoList(List<CouponSimpleResp> couponList) {
        if (CollUtil.isEmpty(couponList)) {
            return Collections.emptyList();
        }

        return couponList.stream().map(this::convertToHiCouponDto).collect(Collectors.toList());
    }
}
