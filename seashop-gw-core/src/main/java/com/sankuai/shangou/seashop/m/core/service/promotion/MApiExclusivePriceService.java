package com.sankuai.shangou.seashop.m.core.service.promotion;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ManagerUserInfo;

/**
 * @author: lhx
 * @date: 2023/12/26/026
 * @description:
 */
public interface MApiExclusivePriceService {

    /**
     * 专享价导出
     *
     * @param request
     */
    void export(ApiExclusivePriceQueryReq request, LoginManagerDto managerUserInfo);
}
