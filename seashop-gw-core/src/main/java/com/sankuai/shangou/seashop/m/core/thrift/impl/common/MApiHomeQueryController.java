package com.sankuai.shangou.seashop.m.core.thrift.impl.common;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.common.MHomeService;
import com.sankuai.shangou.seashop.m.thrift.core.response.common.RadiusNumResp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/02/19 13:50
 */
@RestController
@RequestMapping("/mApi/apiHome")
public class MApiHomeQueryController {

    @Resource
    private MHomeService mHomeService;

    @GetMapping(value = "/queryRadiusNum")
    public ResultDto<RadiusNumResp> queryRadiusNum() {
        return ThriftResponseHelper.responseInvoke("queryRadiusNum", null, req -> mHomeService.queryRadiusNum());
    }
}
