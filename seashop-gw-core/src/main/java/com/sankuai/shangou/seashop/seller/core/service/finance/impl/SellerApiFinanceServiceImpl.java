package com.sankuai.shangou.seashop.seller.core.service.finance.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.OrderStatisticsReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceIndexResp;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.finance.SellerApiFinanceService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderStatisticsReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiFinanceIndexResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiOrderStatisticsListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiOrderStatisticsResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class SellerApiFinanceServiceImpl implements SellerApiFinanceService {

    @Resource
    private SellerFinanceRemoteService sellerFinanceRemoteService;
    @Resource
    private OrderQueryFeign orderQueryFeign;

    @Override
    public ApiFinanceIndexResp getFinanceIndex(Long shopId) {

        FinanceIndexResp financeIndex = sellerFinanceRemoteService.getFinanceIndex(shopId);
        OrderStatisticsReq request = new OrderStatisticsReq();
        DateTime yesterday = DateUtil.yesterday();
        request.setBeginTime(DateUtil.beginOfDay(yesterday));
        request.setEndTime(DateUtil.endOfDay(yesterday));
        request.setShopId(shopId);
        OrderStatisticsResp orderStatisticsResp =  ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getOrderStatistics(request));

        ApiFinanceIndexResp apiFinanceIndexResp = new ApiFinanceIndexResp();
        // 初始数据
        apiFinanceIndexResp.setPendingSettlement(BigDecimal.ZERO);
        apiFinanceIndexResp.setSettled(BigDecimal.ZERO);
        if (null != financeIndex) {
            apiFinanceIndexResp.setPendingSettlement(financeIndex.getPendingSettlement());
            apiFinanceIndexResp.setSettled(financeIndex.getSettled());
        }

        ApiOrderStatisticsResp yesterdayOrderStatistics = new ApiOrderStatisticsResp();
        // 初始数据
        yesterdayOrderStatistics.setOrdersNum(0L);
        yesterdayOrderStatistics.setPayOrdersNum(0L);
        yesterdayOrderStatistics.setSaleAmount(BigDecimal.ZERO);
        if (null != orderStatisticsResp) {
            yesterdayOrderStatistics.setOrdersNum(orderStatisticsResp.getOrdersNum());
            yesterdayOrderStatistics.setPayOrdersNum(orderStatisticsResp.getPayOrdersNum());
            yesterdayOrderStatistics.setSaleAmount(orderStatisticsResp.getSaleAmount());
        }

        apiFinanceIndexResp.setYesterdayOrderStatistics(yesterdayOrderStatistics);

        return apiFinanceIndexResp;
    }

    @Override
    public ApiOrderStatisticsListResp getOrderStatisticsList(ApiOrderStatisticsReq request) {
        OrderStatisticsListResp orderStatisticsList = ThriftResponseHelper.executeThriftCall(() ->
                orderQueryFeign.getOrderStatisticsList(JsonUtil.copy(request, OrderStatisticsReq.class)));
        return JsonUtil.copy(orderStatisticsList, ApiOrderStatisticsListResp.class);
    }

}
