package com.sankuai.shangou.seashop.m.core.thrift.impl.product;


import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.*;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.common.remote.order.MProductCommentRemoteService;
import com.sankuai.shangou.seashop.m.common.remote.product.MProductAuditRemoteService;
import com.sankuai.shangou.seashop.m.common.remote.product.MProductRemoteService;
import com.sankuai.shangou.seashop.m.core.service.product.MProductService;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiProductQueryDetailReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryProductReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiMStatisticalProductResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiProductDetailResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiProductPageResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiProductDetailDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentResp;
import com.sankuai.shangou.seashop.product.thrift.core.BrandApplyQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.QueryProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandApplyDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditPageResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/mApi/apiProduct")
public class MApiProductQueryController {

    @Resource
    private MProductRemoteService mProductRemoteService;
    @Resource
    private MProductService mProductService;
    @Resource
    private MProductAuditRemoteService mProductAuditRemoteService;
    @Resource
    private MProductCommentRemoteService mProductCommentRemoteService;
    @Resource
    private BrandApplyQueryFeign brandApplyQueryFeign;

    @PostMapping(value = "/queryProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiProductPageResp>> queryProduct(@RequestBody ApiQueryProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProduct", request, req -> {
            req.checkParameter();

            dealSearchTime(req);
            QueryProductReq remoteReq = JsonUtil.copy(req, QueryProductReq.class);
            remoteReq.setStatus(ProductStatusEnum.getByCode(req.getStatusCode()));
            // 如果状态为空，则查询在售、下架、违规的商品
            if (req.getStatusCode() == null || req.getStatusCode() == 0) {
                List<ProductStatusEnum> inStatus = Lists.newArrayList();
                inStatus.add(ProductStatusEnum.ON_SALE);
                inStatus.add(ProductStatusEnum.IN_STOCK);
                inStatus.add(ProductStatusEnum.VIOLATION);
                remoteReq.setInStatus(inStatus);
            }

            BasePageResp<ProductPageResp> resp = mProductRemoteService.queryProduct(remoteReq);
            return PageResultHelper.transfer(resp, ApiProductPageResp.class);
        });
    }

    @PostMapping(value = "/queryProductForSelect", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiProductPageResp>> queryProductForSelect(@RequestBody ApiQueryProductPromotionExtReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductForSelect", request, req -> {
            req.checkParameter();

            return mProductService.queryProduct(req);
        });
    }

    @PostMapping(value = "/queryProductDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiProductDetailResp> queryProductDetail(@RequestBody ApiProductQueryDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductDetail", request, req -> {

            ProductQueryDetailReq remoteReq = new ProductQueryDetailReq();
            remoteReq.setProductId(req.getProductId());
            ApiProductDetailDto resp = mProductRemoteService.queryProductDetail(remoteReq);
            return ApiProductDetailResp.builder().result(resp).build();
        });
    }

    @GetMapping(value = "/queryMStatisticalProduct")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiMStatisticalProductResp> queryMStatisticalProduct() throws TException {
        return ThriftResponseHelper.responseInvoke("queryMStatisticalProduct", null, req -> {
//            MStatisticalProductResp mStatisticalProductResp = productRemoteService.queryMStatisticalProduct();
//            ApiMStatisticalProductResp result = JsonUtil.copy(mStatisticalProductResp, ApiMStatisticalProductResp.class);
            ApiMStatisticalProductResp result = new ApiMStatisticalProductResp();

            // 商品待审核
            QueryProductAuditReq auditReq = new QueryProductAuditReq();
            auditReq.setAuditStatus(ProductEnum.AuditStatusEnum.WAIT_AUDIT);
            BasePageResp<ProductAuditPageResp> auditResp = mProductAuditRemoteService.queryProductAudit(auditReq);
            result.setProductsWaitForAuditing(auditResp.getTotalCount().intValue());

            // 商品总数
            QueryProductReq queryProductReq = new QueryProductReq();
            List<ProductStatusEnum> inStatus = Lists.newArrayList();
            inStatus.add(ProductStatusEnum.ON_SALE);
            inStatus.add(ProductStatusEnum.IN_STOCK);
            inStatus.add(ProductStatusEnum.VIOLATION);
            queryProductReq.setInStatus(inStatus);
            BasePageResp<ProductPageResp> productResp = mProductRemoteService.queryProduct(queryProductReq);
            result.setTotalNum(productResp.getTotalCount().intValue());

            // 商品评价数
            QueryProductCommentReq queryProductCommentReq = new QueryProductCommentReq();
            queryProductCommentReq.setReplyStatus(0);
            BasePageResp<ProductCommentResp> productCommentResp =
                mProductCommentRemoteService.queryProductCommentForPlatform(queryProductCommentReq);
            result.setProductsComment(productCommentResp.getTotalCount().intValue());

            // 销售中
            QueryProductReq queryProductReqSale = new QueryProductReq();
            queryProductReqSale.setStatus(ProductStatusEnum.getByCode(1));
            BasePageResp<ProductPageResp> onSaleResp = mProductRemoteService.queryProduct(queryProductReqSale);
            result.setProductsOnSale(onSaleResp.getTotalCount().intValue());

            // 品牌待审核
            QueryBrandApplyReq brandApplyReq = new QueryBrandApplyReq();
            brandApplyReq.setAuditStatus(BrandEnum.AuditStatusEnum.getByCode(0));
            BasePageResp<BrandApplyDto> pageResult = ThriftResponseHelper.executeThriftCall(() -> brandApplyQueryFeign.queryBrandApplyForPage(brandApplyReq));
            result.setProductsBrands(pageResult.getTotalCount().intValue());
            return result;
        });
    }

    @PostMapping(value = "/exportProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportProduct(@RequestBody ApiQueryProductReq request) throws TException {
        dealSearchTime(request);
        mProductService.exportProduct(request);
        return ResultDto.newWithData(BaseResp.of());
    }

    private void dealSearchTime(ApiQueryProductReq req) {
        if (req.getStartTime() != null) {
            req.setStartTime(DateUtil.beginOfDay(req.getStartTime()));
        }
        if (req.getEndTime() != null) {
            req.setEndTime(DateUtil.endOfDay(req.getEndTime()));
        }
    }
}
