package com.sankuai.shangou.seashop.seller.core.service.export.handler.promotion;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerCouponRecordRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.promotion.eo.CouponRecordListEo;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.promotion.wrapper.CouponRecordListWrapper;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiCouponRecordQueryReq;

/**
 * @author: lhx
 * @date: 2024/2/23/023
 * @description:
 */
@Service
public class CouponRecordListExportGetter extends AbstractBaseDataGetter<ApiCouponRecordQueryReq>
    implements SingleWrapperDataGetter<ApiCouponRecordQueryReq> {

    @Resource
    private SellerCouponRecordRemoteService sellerCouponRecordRemoteService;

    @Override
    public DataContext selectData(ApiCouponRecordQueryReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        BaseExportWrapper<CouponRecordListEo, ApiCouponRecordQueryReq> wrapper = new CouponRecordListWrapper(sellerCouponRecordRemoteService);
        context.setSheetDataList(Collections.singletonList(wrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.COUPON_RECORD_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.COUPON_RECORD_LIST.getName();
    }
}
