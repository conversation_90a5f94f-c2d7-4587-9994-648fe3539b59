package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.account;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiPrivilegeService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryPrivilegeReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiPrivilegeRespList;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiUserPrivilegeResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserPrivilegeReq;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/sellerApi/apiPrivilege")
public class SellerApiPrivilegeQueryController {

    @Resource
    private SellerApiPrivilegeService privilegeService;

    @PostMapping(value = "/queryPrivilegeList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiPrivilegeRespList> queryPrivilegeList(@RequestBody ApiQueryPrivilegeReq queryPrivilegeReq) {
        
        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        queryPrivilegeReq.setShopId(loginShopDto.getShopId());
        return ThriftResponseHelper.responseInvoke("queryPrivilegeList", queryPrivilegeReq, req -> privilegeService.queryPrivilegeList(queryPrivilegeReq));
    }

    @GetMapping(value = "/queryUserPrivilege")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiUserPrivilegeResp> queryUserPrivilege() throws TException {
        QueryUserPrivilegeReq baseIdReq = new QueryUserPrivilegeReq();
        
        baseIdReq.setManagerId(TracerUtil.getShopDto().getManagerId());
        baseIdReq.setPlatform(1);
        return ThriftResponseHelper.responseInvoke("queryUserPrivilege", baseIdReq, req -> privilegeService.queryUserPrivilege(req));
    }
}
