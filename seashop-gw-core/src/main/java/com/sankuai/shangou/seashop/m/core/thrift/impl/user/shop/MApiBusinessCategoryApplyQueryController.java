package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiBusinessCategoryApplyService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryApplyDetailReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryApplyDetailResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryApplyResp;

@RestController
@RequestMapping("/mApi/apiBusinessCategoryApply")
public class MApiBusinessCategoryApplyQueryController {
    @Resource
    private MApiBusinessCategoryApplyService mApiBusinessCategoryApplyService;

    @PostMapping(value = "/queryPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiBusinessCategoryApplyResp>> queryPage(@RequestBody ApiQueryBusinessCategoryApplyPageReq queryApiBusinessCategoryApplyPageReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryPage", queryApiBusinessCategoryApplyPageReq, req -> {
            // 参数对象转换
            return mApiBusinessCategoryApplyService.queryPage(req);
        });
    }

    @PostMapping(value = "/queryDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiBusinessCategoryApplyDetailResp> queryDetail(@RequestBody ApiQueryBusinessCategoryApplyDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryDetail", request, req -> {
            req.checkParameter();

            ApiBusinessCategoryApplyDetailResp resp = mApiBusinessCategoryApplyService.queryDetail(req);
            return resp;
        });
    }
}
