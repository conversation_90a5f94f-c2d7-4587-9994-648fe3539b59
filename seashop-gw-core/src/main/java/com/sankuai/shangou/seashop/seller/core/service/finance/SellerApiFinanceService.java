package com.sankuai.shangou.seashop.seller.core.service.finance;


import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderStatisticsReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiFinanceIndexResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiOrderStatisticsListResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
public interface SellerApiFinanceService {

    /**
     * 获取财务首页统计数据
     *
     * @return
     */
    ApiFinanceIndexResp getFinanceIndex(Long shopId);

    /**
     * 订单统计列表（财务管理首页统计）
     *
     * @param request
     * @return
     */
    ApiOrderStatisticsListResp getOrderStatisticsList(ApiOrderStatisticsReq request);
}
