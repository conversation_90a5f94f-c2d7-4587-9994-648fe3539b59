package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.mall.core.service.user.shop.MallApiBusinessCategoryService;

@RestController
@RequestMapping("/mallApi/apiBusinessCategory")
public class MallApiBusinessCategoryCmdController {
    @Resource
    private MallApiBusinessCategoryService businessCategoryService;


    @PostMapping(value = "/updateCategory", consumes = "application/json")
    public ResultDto<BaseResp> updateCategory(@RequestBody ApiCmdBusinessCategoryReqList reqList) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategory", reqList, func -> {
            // 参数对象转换
            return businessCategoryService.updateCategory(func);
        });
    }
}
