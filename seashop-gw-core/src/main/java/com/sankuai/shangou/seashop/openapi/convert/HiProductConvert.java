package com.sankuai.shangou.seashop.openapi.convert;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.openapi.req.HiProductQueryReq;
import com.sankuai.shangou.seashop.openapi.resp.HiPageResp;
import com.sankuai.shangou.seashop.openapi.resp.HiProductDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2024/10/10 10:25
 */
@Component
public class HiProductConvert {

    public QueryProductReq convertToProductQuery(HiProductQueryReq hiReq) {
        return Optional.ofNullable(hiReq)
                .map(item -> JsonUtil.copy(item, QueryProductReq.class))
                .orElse(null);
    }

    public HiProductDto convertToHiProductDto(ProductPageResp productBasicDto) {
        return Optional.ofNullable(productBasicDto)
                .map(dto -> {
                    HiProductDto resp = JsonUtil.copy(dto, HiProductDto.class);
                    resp.setCid(dto.getCategoryId());
                    if (resp.getList_time() == null) {
                        resp.setList_time(dto.getAddedDate());
                    }
                    return resp;
                })
                .orElse(null);
    }

    public List<HiProductDto> convertToHiProductDtoList(List<ProductPageResp> productBasicDtoList) {
        if (CollUtil.isEmpty(productBasicDtoList)) {
            return Collections.emptyList();
        }

        return productBasicDtoList.stream()
                .map(productBasicDto -> convertToHiProductDto(productBasicDto))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public HiPageResp<HiProductDto> convertToHiProductResp(BasePageResp<ProductPageResp> result) {
        return Optional.ofNullable(result)
                .map(dto -> {
                    HiPageResp hiPageResp = new HiPageResp();
                    hiPageResp.setTotal(result.getTotalCount());
                    hiPageResp.setData(convertToHiProductDtoList(result.getData()));
                    return hiPageResp;
                })
                .orElse(null);
    }
}
