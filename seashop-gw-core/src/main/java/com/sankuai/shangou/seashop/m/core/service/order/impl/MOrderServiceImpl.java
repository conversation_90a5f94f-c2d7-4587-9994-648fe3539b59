package com.sankuai.shangou.seashop.m.core.service.order.impl;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.order.MOrderService;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiQueryPlatformOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.OrderStatisticsQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderOperationLogResp;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.PlatformIndexTradeDataStatsResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/02 11:58
 */
@Service
public class MOrderServiceImpl implements MOrderService {

    @Resource
    private MExportTaskBiz exportTaskBiz;
    @Resource
    private OrderQueryFeign orderQueryFeign;
    @Resource
    private OrderStatisticsQueryFeign orderStatisticsQueryFeign;

    @Override
    public void exportOrderInvoice(ApiQueryPlatformOrderReq request) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.ORDER_INVOICE_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getManagerDto().getId());
        createExportTaskBo.setOperatorName(TracerUtil.getManagerDto().getName());
        exportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public void export(ApiQueryPlatformOrderReq queryReq, UserDto user) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.ORDER_LIST);
        createExportTaskBo.setExecuteParam(queryReq);
        createExportTaskBo.setOperatorId(user.getUserId());
        createExportTaskBo.setOperatorName(user.getUserName());
        exportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public BasePageResp<OrderInfoDto> pageQueryPlatformOrder(QueryPlatformOrderReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.pageQueryPlatformOrder(queryReq));
    }

    @Override
    public OrderDetailResp queryDetail(QueryOrderDetailReq queryReq) {
        queryReq.setQueryFrom(OrderQueryFromEnum.PLATFORM_PC);
        return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryDetailForPlatform(queryReq));
    }

    @Override
    public List<OrderOperationLogResp> queryOrderOperationLog(String orderId) {
        return ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.queryOrderLog(orderId));
    }

    @Override
    public PlatformIndexTradeDataStatsResp statsPlatformIndexTradeData() {
        return ThriftResponseHelper.executeThriftCall(() -> orderStatisticsQueryFeign.statsPlatformIndexTradeData());
    }
}
