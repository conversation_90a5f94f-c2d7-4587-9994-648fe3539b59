package com.sankuai.shangou.seashop.m.core.service.export.handler.finance;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.common.remote.MFinanceRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo.SettledByOrderEo;
import com.sankuai.shangou.seashop.m.core.service.export.handler.finance.wrapper.SettledByOrderWrapper;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledQryReq;

/**
 * @author: lhx
 * @date: 2024/3/26/026
 * @description:
 */
@Service
public class MSettledByOrderGetter extends AbstractBaseDataGetter<ApiSettledQryReq>
    implements SingleWrapperDataGetter<ApiSettledQryReq> {

    @Resource
    @Lazy
    private MFinanceRemoteService MFinanceRemoteService;

    @Override
    public DataContext selectData(ApiSettledQryReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        BaseExportWrapper<SettledByOrderEo, ApiSettledQryReq> wrapper = new SettledByOrderWrapper(MFinanceRemoteService);
        context.setSheetDataList(Collections.singletonList(wrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.SETTLED_BY_ORDER_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.SETTLED_BY_ORDER_LIST.getName();
    }
}
