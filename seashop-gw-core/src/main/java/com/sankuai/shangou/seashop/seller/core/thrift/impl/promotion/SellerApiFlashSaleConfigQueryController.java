package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopFlashSaleConfigResp;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerFlashSaleRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiShopIdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiShopFlashSaleConfigResp;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiFlashSaleConfig")
public class SellerApiFlashSaleConfigQueryController {

    @Resource
    private SellerFlashSaleRemoteService sellerFlashSaleRemoteService;

    @PostMapping(value = "/getShopConfig", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiShopFlashSaleConfigResp> getShopConfig(@RequestBody ApiShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getShopConfig", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            ShopFlashSaleConfigResp shopConfig = sellerFlashSaleRemoteService.getShopConfig(JsonUtil.copy(req, ShopIdReq.class));
            return JsonUtil.copy(shopConfig, ApiShopFlashSaleConfigResp.class);
        });
    }
}
