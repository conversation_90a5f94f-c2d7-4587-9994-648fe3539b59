package com.sankuai.shangou.seashop.openapi;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.openapi.convert.HiCouponConvert;
import com.sankuai.shangou.seashop.openapi.convert.HiProductConvert;
import com.sankuai.shangou.seashop.openapi.enums.HiCouponTypeEnum;
import com.sankuai.shangou.seashop.openapi.req.HiCouponReq;
import com.sankuai.shangou.seashop.openapi.req.HiProductQueryReq;
import com.sankuai.shangou.seashop.openapi.resp.HiCouponResp;
import com.sankuai.shangou.seashop.openapi.resp.HiPageResp;
import com.sankuai.shangou.seashop.openapi.resp.HiProductDto;
import com.sankuai.shangou.seashop.openapi.resp.HiProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerCouponRemoteService;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 提供给hi 客服的接口
 *
 * <AUTHOR>
 * @date 2024/10/10 9:14
 */
@RestController
@RequestMapping("/hi-customer-service")
@Slf4j
public class HiCustomerServiceImpl {

    @Resource
    private SellerProductRemoteService sellerProductRemoteService;
    @Resource
    private SellerCouponRemoteService sellerCouponRemoteService;

    @Resource
    private HiProductConvert hiProductConvert;
    @Resource
    private HiCouponConvert hiCouponConvert;

    @GetMapping(value = "/{shopId}/product/pageList")
    public HiProductResp productPageList(HiProductQueryReq queryReq, @PathVariable("shopId") Long shopId) {
        HiProductResp hiResp = new HiProductResp();
        try {
            log.info("【Hi客服】商品列表查询, 请求参数={}", JsonUtil.toJsonString(queryReq));
            QueryProductReq remoteReq = hiProductConvert.convertToProductQuery(queryReq);
            remoteReq.setNeedH5Url(Boolean.TRUE);
            remoteReq.setStatus(ProductStatusEnum.ON_SALE);
            remoteReq.setShopId(shopId);
            BasePageResp<ProductPageResp> result = sellerProductRemoteService.queryProduct(remoteReq);
            HiPageResp<HiProductDto> hiPageResp = hiProductConvert.convertToHiProductResp(result);
            log.info("【Hi客服】商品列表查询, 返回结果={}", JsonUtil.toJsonString(hiPageResp));
            hiResp.setResponse(hiPageResp);
        } catch (Exception e) {
            log.error("【Hi客服】商品列表查询异常", e);
        }
        return hiResp;
    }

    @GetMapping(value = "/{shopId}/coupon/list")
    public HiCouponResp listCoupon(HiCouponReq queryReq, @PathVariable("shopId") Long shopId) {
        log.info("【Hi客服】优惠券列表查询, 请求参数={}", JsonUtil.toJsonString(queryReq));
        HiCouponResp hiResp = new HiCouponResp();
        // 如果是膨胀券 直接返回
        if (HiCouponTypeEnum.INFLATION.getType().equals(queryReq.getType())) {
            hiResp.setData(Collections.emptyList());
            log.info("【Hi客服】优惠券列表查询, 返回结果={}", JsonUtil.toJsonString(hiResp));
            return hiResp;
        }

        try {
            CouponQueryReq remoteReq = new CouponQueryReq();
            remoteReq.setPageSize(ObjectUtil.defaultIfNull(queryReq.getTop(), 10));
            remoteReq.setStatus(1);
            remoteReq.setClaimable(true);
            remoteReq.setReceiveTypeList(Collections.singletonList(0));
            remoteReq.setShopId(shopId);
            BasePageResp<CouponSimpleResp> result = sellerCouponRemoteService.pageList(remoteReq);
            hiResp.setData(hiCouponConvert.convertToHiCouponDtoList(result.getData()));
            log.info("【Hi客服】优惠券列表查询, 返回结果={}", JsonUtil.toJsonString(hiResp));
        } catch (Exception e) {
            hiResp.setSuccess(Boolean.FALSE);
            log.error("【Hi客服】优惠券列表查询异常", e);
        }
        return hiResp;
    }
}
