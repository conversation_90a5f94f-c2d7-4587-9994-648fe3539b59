package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import java.util.List;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiShopResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopQueryReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdSendCodeReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdShopManagerReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdShopStepsOneReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdShopStepsTwoReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCompanyCmdShopReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiPersonalCmdShopReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiShippingSettingsSaveReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiTreeBankRegionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopManagerReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStepsOneReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStepsTwoReq;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiShop")
public class SellerApiShopCmdController {

    @Resource
    private SellerShopRemoteService sellerShopRemoteService;


    @PostMapping(value = "/saveShippingSettings", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> saveShippingSettings(@RequestBody ApiShippingSettingsSaveReq request) {
        
        request.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("saveShippingSettings", request,
            req -> sellerShopRemoteService.saveShippingSettings(request)
        );
    }

    @PostMapping(value = "/editShopPersonal", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> editShopPersonal(@RequestBody ApiPersonalCmdShopReq cmdShopReq) {
        
        cmdShopReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("editShopPersonal", cmdShopReq, req -> sellerShopRemoteService.editShopPersonal(cmdShopReq));
    }

    @PostMapping(value = "/editShopEnterprise", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> editShopEnterprise(@RequestBody ApiCompanyCmdShopReq cmdShopReq) {
        
        cmdShopReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("editShopEnterprise", cmdShopReq, req -> sellerShopRemoteService.editShopEnterprise(cmdShopReq));
    }

    @PostMapping(value = "/sendCode", consumes = "application/json")
    public ResultDto<BaseResp> sendCode(@RequestBody ApiCmdSendCodeReq cmdShopReq) {
        return ThriftResponseHelper.responseInvoke("sendCode", cmdShopReq, req -> sellerShopRemoteService.sendCode(req));
    }

    @PostMapping(value = "/modifyBankInfo", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> modifyBankInfo(@RequestBody ApiCmdShopStepsTwoReq cmdShopStepsTwoReq) throws TException {
        
        cmdShopStepsTwoReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("modifyBankInfo", cmdShopStepsTwoReq, req ->
            sellerShopRemoteService.modifyBankInfo(JsonUtil.copy(req, CmdShopStepsTwoReq.class)));
    }

    @PostMapping(value = "/modifyBaseInfo", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> modifyBaseInfo(@RequestBody ApiCmdShopStepsOneReq cmdShopBaseReq) throws TException {
        
        cmdShopBaseReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("modifyBaseInfo", cmdShopBaseReq, req -> {
            return sellerShopRemoteService.modifyBaseInfo(JsonUtil.copy(req, CmdShopStepsOneReq.class));
        });
    }

    @PostMapping(value = "/modifyManagerInfo", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> modifyManagerInfo(@RequestBody ApiCmdShopManagerReq cmdShopManagerReq) throws TException {
        
        cmdShopManagerReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("modifyManagerInfo", cmdShopManagerReq, req -> {
            return sellerShopRemoteService.modifyManagerInfo(JsonUtil.copy(req, CmdShopManagerReq.class));
        });
    }

    @GetMapping(value = "/getBankRegion")
    public ResultDto<String> getBankRegion() {
        return ThriftResponseHelper.responseInvoke("getBankRegion", null, req -> sellerShopRemoteService.getBankRegion());
    }

    @PostMapping(value = "/getRegionByParentId", consumes = "application/json")
    public ResultDto<List<ApiTreeBankRegionResp>> getRegionByParentId(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("getRegionByParentId", baseIdReq, req -> JsonUtil.copyList(sellerShopRemoteService.getRegionByParentId(baseIdReq), ApiTreeBankRegionResp.class));
    }

    @PostMapping(value = "/getRegionsById", consumes = "application/json")
    public ResultDto<List<ApiTreeBankRegionResp>> getRegionsById(@RequestBody BaseIdReq regionId) {
        return ThriftResponseHelper.responseInvoke("getRegionsById", regionId, req -> JsonUtil.copyList(sellerShopRemoteService.getRegionsById(req), ApiTreeBankRegionResp.class));
    }

//    queryShopsByIds
    @PostMapping(value = "/queryShopsByIds", consumes = "application/json")
    public ResultDto<List<ApiShopResp>> queryShopsByIds(@RequestBody ShopQueryReq baseBatchIdReq) {
        return ThriftResponseHelper.responseInvoke("queryShopsByIds", baseBatchIdReq, req -> JsonUtil.copyList(sellerShopRemoteService.queryShopsByIds(req), ApiShopResp.class));
    }
}
