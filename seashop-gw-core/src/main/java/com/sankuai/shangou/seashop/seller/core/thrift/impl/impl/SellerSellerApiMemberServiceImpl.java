package com.sankuai.shangou.seashop.seller.core.thrift.impl.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.seller.common.remote.user.SellerMemberRemoteService;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiMemberService;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SellerSellerApiMemberServiceImpl implements SellerApiMemberService {
    @Resource
    private SellerMemberRemoteService sellerMemberRemoteService;

    @Override
    public MemberResp queryMember(Integer epAccountId) {
        return sellerMemberRemoteService.queryMember(epAccountId);
    }

}
