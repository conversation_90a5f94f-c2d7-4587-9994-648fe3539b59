package com.sankuai.shangou.seashop.mall.core.service.user.account;

import java.util.List;

import javax.annotation.Resource;

import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiTradeProductDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import org.springframework.stereotype.Service;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.MemberUserInfo;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiFavoriteShopCmdReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiFavoriteShopPageReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiFavoriteShopRes;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallFavoriteShopRemoteService;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.FavoriteShopCmdReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.FavoriteShopPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.FavoriteShopRes;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: LXH
 **/
@Service
@Slf4j
public class MallApiFavoriteShopService {
    @Resource
    private MallFavoriteShopRemoteService mallFavoriteShopRemoteService;

    public BaseResp addFavoriteShop(Long func, LoginMemberDto userInfo) {
        return mallFavoriteShopRemoteService.addFavoriteShop(func, userInfo);
    }

    public BaseResp deleteFavoriteShop(ApiFavoriteShopCmdReq func) {
        //转化参数
        FavoriteShopCmdReq req = JsonUtil.copy(func, FavoriteShopCmdReq.class);
        //调用远程服务
        return mallFavoriteShopRemoteService.deleteFavoriteShop(req);
    }

    public BasePageResp<ApiFavoriteShopRes> queryPage(ApiFavoriteShopPageReq func) {
        //转化参数
        FavoriteShopPageReq req = JsonUtil.copy(func, FavoriteShopPageReq.class);
        //调用远程服务
        BasePageResp<FavoriteShopRes> resp = mallFavoriteShopRemoteService.queryPage(req);
        //转化结果
        BasePageResp<ApiFavoriteShopRes> transfer = PageResultHelper.transfer(resp, ApiFavoriteShopRes.class);
        //判空
        if (CollUtil.isEmpty(transfer.getData())) {
            return transfer;
        }
        //遍历店铺
        transfer.getData().forEach(shop -> {
            //查询店铺的热销商品
            List<TradeProductDto> hotSaleList = queryHotSaleProduct(shop.getShopId(), func.getUserId());
            shop.setHotSaleProductList(JsonUtil.copyList(hotSaleList, ApiTradeProductDto.class));
            // 设置新上架商品列表
            List<TradeProductDto> newList = queryNewestProduct(shop.getShopId(), func.getUserId());
            shop.setNewProductList(JsonUtil.copyList(newList, ApiTradeProductDto.class));
        });
        return transfer;
    }

    public List<TradeProductDto> queryNewestProduct(Long shopId, Long userId) {
        return mallFavoriteShopRemoteService.queryNewestProduct(shopId, userId);
    }

    public List<TradeProductDto> queryHotSaleProduct(Long shopId, Long userId) {
        return mallFavoriteShopRemoteService.queryHotSaleProduct(shopId, userId);
    }
}
