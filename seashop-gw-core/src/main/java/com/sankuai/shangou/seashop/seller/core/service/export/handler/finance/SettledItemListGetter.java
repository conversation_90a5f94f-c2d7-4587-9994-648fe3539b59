package com.sankuai.shangou.seashop.seller.core.service.export.handler.finance;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.eo.SettledItemListEo;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.wrapper.SettleditemListWrapper;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledItemQryReq;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
@Service
public class SettledItemListGetter extends AbstractBaseDataGetter<ApiSettledItemQryReq>
    implements SingleWrapperDataGetter<ApiSettledItemQryReq> {

    @Resource
    @Lazy
    private SellerFinanceRemoteService sellerFinanceRemoteService;

    @Override
    public DataContext selectData(ApiSettledItemQryReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        BaseExportWrapper<SettledItemListEo, ApiSettledItemQryReq> wrapper = new SettleditemListWrapper(sellerFinanceRemoteService);
        context.setSheetDataList(Collections.singletonList(wrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.SETTLED_ITEM_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.SETTLED_ITEM_LIST.getName();
    }
}
