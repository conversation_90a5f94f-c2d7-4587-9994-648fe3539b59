package com.sankuai.shangou.seashop.seller.core.service.export.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.config.ExportTaskProps;
import com.sankuai.shangou.seashop.base.export.enums.TaskBizType;
import com.sankuai.shangou.seashop.base.export.enums.TaskType;
import com.sankuai.shangou.seashop.base.export.exec.AbstractExcelExportExec;
import com.sankuai.shangou.seashop.base.export.exec.ExcelExportExec;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.base.export.handler.ExportTaskHandler;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.thrift.core.SellerTaskCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.SellerTaskQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.dto.SellerTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TaskStatusEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.CompleteTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.CreateTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.ExceptionTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.task.SellerTaskListResp;
import com.sankuai.shangou.seashop.seller.common.remote.model.CompleteTaskBo;
import com.sankuai.shangou.seashop.seller.core.mq.publisher.SellerExportTaskPublisher;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.seller.core.service.export.model.ExportTaskBo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SellerExportTaskBizImpl extends AbstractExcelExportExec implements SellerExportTaskBiz, ExcelExportExec {

    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private SellerExportTaskPublisher sellerExportTaskPublisher;
    @Resource
    @Lazy
    private S3plusStorageService s3plusStorageService;
    @Resource
    private ExportTaskProps exportTaskProps;
    @Resource
    private SellerTaskCmdFeign sellerTaskCmdFeign;
    @Resource
    private SellerTaskQueryFeign sellerTaskQueryFeign;

    @Resource
    private StorageClient storageClient;


    public SellerExportTaskBizImpl(ExportTaskHandler exportTaskHandler, ExportTaskProps exportTaskProps) {
        super(exportTaskHandler, exportTaskProps);
    }

    @Override
    public List<ExportTask> getTaskList() {
        return null;
    }

    @Override
    public void start(ExportTask task) {
        Long taskId = Long.parseLong(task.getTaskId());
        BaseIdReq baseIdReq = new BaseIdReq();
        baseIdReq.setId(taskId);
        ThriftResponseHelper.executeThriftCall(() -> sellerTaskCmdFeign.start(baseIdReq));
    }

    @Override
    public void complete(ExportTask task) {
        Long taskId = Long.parseLong(task.getTaskId());
        CompleteTaskBo completeTaskBo = new CompleteTaskBo();
        completeTaskBo.setTaskId(taskId);
        completeTaskBo.setExecuteResult("");
        completeTaskBo.setTotalNum(task.getCount());
        completeTaskBo.setSuccessNum(0);
        completeTaskBo.setFailedNum(0);
        completeTaskBo.setFilePath(task.getFilePath());
        CompleteTaskReq req = JsonUtil.copy(completeTaskBo, CompleteTaskReq.class);
        ThriftResponseHelper.executeThriftCall(() -> sellerTaskCmdFeign.complete(req));
    }

    @Override
    public void exception(ExportTask task) {
        Long taskId = Long.parseLong(task.getTaskId());

        ExceptionTaskReq exceptionTaskReq = new ExceptionTaskReq();
        exceptionTaskReq.setTaskId(taskId);
        exceptionTaskReq.setExceptionContent(task.getException());
        ThriftResponseHelper.executeThriftCall(() -> sellerTaskCmdFeign.exception(exceptionTaskReq));
    }

    @Override
    public List<ExportTask> getRedoTaskList() {
        QueryTaskReq queryReq = new QueryTaskReq();
        queryReq.setEnv(env);
        queryReq.setTaskStatusList(CollUtil.newArrayList(TaskStatusEnum.PROCESSING.getCode(), TaskStatusEnum.FAILED.getCode()));
        SellerTaskListResp resp = ThriftResponseHelper.executeThriftCall(() -> sellerTaskQueryFeign.queryList(queryReq));
        List<SellerTaskDto> needRedoTaskList = resp.getTaskList();
        if (CollUtil.isEmpty(needRedoTaskList)) {
            return null;
        }
        Date now = new Date();
        return needRedoTaskList.stream()
            // 过滤掉重试次数超过配置的任务
            .filter(task -> task.getRetryTimes() < exportTaskProps.getMaxRetryTimes() + 1)
            // 过滤出执行中，但是执行时间超过配置时间的；或者是失败的
            .filter(task -> {
                if (TaskStatusEnum.PROCESSING.getCode().equals(task.getTaskStatus())) {
                    return now.getTime() - task.getBeginTime().getTime() > exportTaskProps.getProcessTimeoutMillis();
                }
                return TaskStatusEnum.FAILED.getCode().equals(task.getTaskStatus());
            })
            .map(task -> {
                ExportTask exportTask = new ExportTask();
                exportTask.setTaskType(task.getTaskType());
                exportTask.setExecuteParam(task.getExecuteParam());
                exportTask.setTaskId(String.valueOf(task.getId()));
                exportTask.setTaskDate(task.getCreateTime());
                return exportTask;
            })
            .collect(Collectors.toList());
    }

    @Override
    public ExportTaskBo create(CreateExportTaskBo createPo) {
        CreateTaskReq createTaskReq = new CreateTaskReq();

        TaskType taskType = createPo.getTaskType();
        createTaskReq.setTaskType(taskType.getType());
        createTaskReq.setTaskName(taskType.getName());
        createTaskReq.setBizType(TaskBizType.EXPORT.getCode());
        createTaskReq.setOperatorId(createPo.getOperatorId());
        createTaskReq.setOperatorAccount(createPo.getOperatorName());
        if (createPo.getExecuteParam() != null) {
            createTaskReq.setExecuteParam(JsonUtil.toJsonString(createPo.getExecuteParam()));
        }
        else {
            createTaskReq.setExecuteParam("");
        }
        createTaskReq.setEnv(env);
        Long taskId = ThriftResponseHelper.executeThriftCall(() -> sellerTaskCmdFeign.createTask(createTaskReq));

        // 发送MQ消息，如果MQ消息发送失败，后续会有定时任务检查补偿
        ExportTask exportTask = new ExportTask();
        exportTask.setTaskType(createTaskReq.getTaskType());
        exportTask.setExecuteParam(createTaskReq.getExecuteParam());
        exportTask.setTaskId(String.valueOf(taskId));
        exportTask.setTaskDate(new Date());
        sellerExportTaskPublisher.sendAsyncExportTaskMessage(exportTask);
        return null;
    }

    @Override
    public void execute(ExportTask task) {
        super.executeTask(task);
    }

    @Override
    public BasePageResp<SellerTaskDto> pageList(QueryTaskReq queryReq) {
        queryReq.setEnv(env);
        BasePageResp<SellerTaskDto> pageList = ThriftResponseHelper.executeThriftCall(() -> sellerTaskQueryFeign.pageList(queryReq));
        if (pageList == null || CollUtil.isEmpty(pageList.getData())) {
            return PageResultHelper.defaultEmpty(queryReq);
        }
        pageList.getData()
            .forEach(data -> {
                if (StrUtil.isNotBlank(data.getFilePath())) {
                    data.setDownloadUrl(storageClient.formatUrl(data.getFilePath()));
                }
            });
        return pageList;
    }

    @Override
    public void checkAndRedoIfNecessary() {
        super.checkAndRedo();
    }
}
