package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.promotion.MApiPromotionService;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiPromotionAuditStatisticsReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiPromotionAuditStatisticsResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2024/3/5/005
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFullReduction")
public class MApiPromotionQueryController {

    @Resource
    private MApiPromotionService mApiPromotionService;

    @PostMapping(value = "/auditStatisticsQuery", consumes = "application/json")
    public ResultDto<ApiPromotionAuditStatisticsResp> auditStatisticsQuery(@RequestBody ApiPromotionAuditStatisticsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("auditStatisticsQuery", request, req ->
            mApiPromotionService.auditStatisticsQuery(req));
    }
}
