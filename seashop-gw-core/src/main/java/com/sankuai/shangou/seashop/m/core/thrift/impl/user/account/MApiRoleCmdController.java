package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiRoleService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdRoleReq;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiRole")
public class MApiRoleCmdController {
    @Resource
    private MApiRoleService mApiRoleService;

    @PostMapping(value = "/addRole", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> addRole(@RequestBody ApiCmdRoleReq cmdRoleReq) {
        return ThriftResponseHelper.responseInvoke("addRole", cmdRoleReq, req -> mApiRoleService.addRole(cmdRoleReq));
    }

    @PostMapping(value = "/editRole", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> editRole(@RequestBody ApiCmdRoleReq cmdRoleReq) {
        return ThriftResponseHelper.responseInvoke("editRole", cmdRoleReq, req -> mApiRoleService.editRole(cmdRoleReq));
    }

    @PostMapping(value = "/deleteRole", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> deleteRole(@RequestBody ApiCmdRoleReq cmdRoleReq) {
        return ThriftResponseHelper.responseInvoke("deleteRole", cmdRoleReq, req -> mApiRoleService.deleteRole(cmdRoleReq));
    }
}
