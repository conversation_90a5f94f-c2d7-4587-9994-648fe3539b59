package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.*;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiQueryOrderComplaintResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiQueryOrderRightsResp;
import com.sankuai.shangou.seashop.base.boot.response.*;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderRightsResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/11/24 9:52
 */
@RestController
@RequestMapping("/mallApi/apiMallComplaint")
public class MallApiMallComplaintController {

    @Resource
    private ComplaintCmdFeign complaintCmdFeign;
    @Resource
    private ComplaintQueryFeign complaintQueryFeign;
    @PostMapping(value = "/startComplaint", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> startComplaint(@RequestBody ApiStartComplaintReq cmdOrderComplaintReq) throws TException {
        return ThriftResponseHelper.responseInvoke("startComplaint", cmdOrderComplaintReq, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> complaintCmdFeign.startComplaint(JsonUtil.copy(req, StartComplaintReq.class)));
        });
    }

    @PostMapping(value = "/cancelComplaint", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> cancelComplaint(@RequestBody ApiCancelComplaintReq cancelComplaintReq) throws TException {
        return ThriftResponseHelper.responseInvoke("cancelComplaint", cancelComplaintReq, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> complaintCmdFeign.cancelComplaint(JsonUtil.copy(req, CancelComplaintReq.class)));
        });
    }

    @PostMapping(value = "/pageMallComplaint", consumes = "application/json")
    @NeedLogin
    public ResultDto<BasePageResp<ApiQueryOrderComplaintResp>> pageMallComplaint(@RequestBody ApiQueryMallComplaintReq queryMallComplaintReq) throws TException {
        return ThriftResponseHelper.responseInvoke("pageMallComplaint", queryMallComplaintReq, req -> {
            
            req.setUserId(TracerUtil.getMemberDto().getId());
            req.checkParameter();

            BasePageResp<QueryOrderComplaintResp> resp = ThriftResponseHelper.executeThriftCall(() ->
                    complaintQueryFeign.pageMallComplaint(JsonUtil.copy(req, QueryMallComplaintReq.class)));
            return PageResultHelper.transfer(resp, ApiQueryOrderComplaintResp.class);
        });
    }

    @PostMapping(value = "/pageQueryOrderRights", consumes = "application/json")
    @NeedLogin
    public ResultDto<BasePageResp<ApiQueryOrderRightsResp>> pageQueryOrderRights(@RequestBody ApiQueryOrderRightsReq queryOrderRightsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("pageQueryOrderRights", queryOrderRightsReq, req -> {
            req.setUserId(TracerUtil.getMemberDto().getId());
            req.checkParameter();

            BasePageResp<QueryOrderRightsResp> resp = ThriftResponseHelper.executeThriftCall(() ->
                    complaintQueryFeign.pageQueryOrderRights(JsonUtil.copy(req, QueryOrderRightsReq.class)));
            return PageResultHelper.transfer(resp, ApiQueryOrderRightsResp.class);
        });
    }

    @PostMapping(value = "/applyArbitrateComplaint", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> applyArbitrateComplaint(@RequestBody ApiApplyArbitrateComplaintReq applyArbitrateComplaintReq) throws TException {
        return ThriftResponseHelper.responseInvoke("applyArbitrateComplaint", applyArbitrateComplaintReq, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() ->
                    complaintCmdFeign.applyMallArbitrateComplaint(JsonUtil.copy(req, ApplyArbitrateComplaintReq.class)));
        });
    }
}
