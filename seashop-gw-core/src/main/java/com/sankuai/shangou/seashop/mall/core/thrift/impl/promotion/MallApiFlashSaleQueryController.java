package com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiFlashSaleQueryByIdReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiMallAppletFlashSaleQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiMallFlashSaleQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiFlashSaleResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiMallAppletFlashSaleListResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiMallFlashSaleResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiVisualFlashSaleListResp;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryByIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.MallAppletFlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.MallFlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.MallAppletFlashSaleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.MallFlashSaleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.VisualFlashSaleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@RestController
@RequestMapping("/mallApi/apiFlashSale")
public class MallApiFlashSaleQueryController {

    @Resource
    private FlashSaleQueryFeign flashSaleQueryFeign;

    @PostMapping(value = "/mallPageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiMallFlashSaleResp>> mallPageList(@RequestBody ApiMallFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("mallPageList", request, req -> {
            MallFlashSaleQueryReq saleQueryReq = JsonUtil.copy(req, MallFlashSaleQueryReq.class);
            BasePageResp<MallFlashSaleResp> mallPageList = ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.mallPageList(saleQueryReq));
            return PageResultHelper.transfer(mallPageList, ApiMallFlashSaleResp.class, m -> {
                /*
                 针对前端需要的同意处理
                 */
                m.setMarketPrice(m.getSalePrice());
                m.setSalePrice(m.getMinPrice());
            });
        });
    }

    @PostMapping(value = "/mallAppletPageList", consumes = "application/json")
    public ResultDto<ApiMallAppletFlashSaleListResp> mallAppletPageList(@RequestBody ApiMallAppletFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("mallAppletPageList", request, req -> {
            req.checkParameter();

            MallAppletFlashSaleListResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    flashSaleQueryFeign.mallAppletPageList(JsonUtil.copy(req, MallAppletFlashSaleQueryReq.class)));
            return JsonUtil.copy(resp, ApiMallAppletFlashSaleListResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiFlashSaleResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> flashSaleQueryFeign.getById(req)), ApiFlashSaleResp.class);
        });
    }

    @PostMapping(value = "/queryVisualFlashSaleList", consumes = "application/json")
    public ResultDto<ApiVisualFlashSaleListResp> queryVisualFlashSaleList(@RequestBody ApiFlashSaleQueryByIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryVisualFlashSaleList", request, req -> {
            req.checkParameter();

            VisualFlashSaleListResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    flashSaleQueryFeign.queryVisualFlashSaleList(JsonUtil.copy(req, FlashSaleQueryByIdReq.class)));
            return JsonUtil.copy(resp, ApiVisualFlashSaleListResp.class);
        });
    }
}
