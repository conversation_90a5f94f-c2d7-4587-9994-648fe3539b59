package com.sankuai.shangou.seashop.seller.core.thrift.impl.finance;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.service.finance.SellerApiPendingSettlementService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiShopIdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiPendingSettlementOrderResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiPendingSettlementResp;

/**
 * @author: lhx
 * @date: 2023/12/8/008
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiPendingSettlement")
public class SellerApiPendingSettlementQueryController {

    @Resource
    private SellerApiPendingSettlementService sellerApiPendingSettlementService;

    @PostMapping(value = "/getTotalSettlementAmount", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiPendingSettlementResp> getTotalSettlementAmount(@RequestBody ApiShopIdReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("getTotalSettlementAmount", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            return sellerApiPendingSettlementService.getTotalSettlementAmount(req);
        });
    }

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiPendingSettlementOrderResp>> pageList(@RequestBody ApiPendingSettlementOrderQryReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            return sellerApiPendingSettlementService.pageList(req);
        });
    }

    @PostMapping(value = "/getDetailByOrderId", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiPendingSettlementOrderResp> getDetailByOrderId(@RequestBody ApiOrderIdQryReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("getDetailByOrderId", request, req -> {
            req.checkParameter();
            return sellerApiPendingSettlementService.getDetailByOrderId(req, TracerUtil.getShopDto().getShopId());
        });
    }

    @PostMapping(value = "/exportPendSettleList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> exportPendSettleList(@RequestBody ApiPendingSettlementOrderQryReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("exportPendSettleList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
//            req.setShopId(101L);
            sellerApiPendingSettlementService.exportPendSettleList(req, loginShopDto);
            return BaseResp.of();
        });
    }
}
