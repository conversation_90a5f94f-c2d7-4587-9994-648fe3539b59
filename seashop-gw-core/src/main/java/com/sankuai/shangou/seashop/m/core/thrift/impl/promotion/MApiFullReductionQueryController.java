package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiFullReductionQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiFullReductionResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiFullReductionSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryFullReductionDetailReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FullReductionQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFullReduction")
public class MApiFullReductionQueryController {

    @Resource
    private FullReductionQueryFeign fullReductionQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiFullReductionSimpleResp>> pageList(@RequestBody ApiFullReductionQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            FullReductionQueryReq fullReductionQueryReq = JsonUtil.copy(req, FullReductionQueryReq.class);
            BasePageResp<FullReductionSimpleResp> listRespBasePageResp = ThriftResponseHelper.executeThriftCall(() ->
                    fullReductionQueryFeign.pageList(fullReductionQueryReq));
            return PageResultHelper.transfer(listRespBasePageResp, ApiFullReductionSimpleResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiFullReductionResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();

            QueryFullReductionDetailReq remoteReq = new QueryFullReductionDetailReq();
            remoteReq.setId(req.getId());
            FullReductionResp fullReductionResp = ThriftResponseHelper.executeThriftCall(() -> fullReductionQueryFeign.getById(remoteReq));
            return JsonUtil.copy(fullReductionResp, ApiFullReductionResp.class);
        });
    }
}
