package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.SpecificationFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateNameReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateValueReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecificationReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationNameResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationValueResp;

@RestController
@RequestMapping("/sellerApi/specification")
public class SellerSpecificationController {
    @Resource
    private SpecificationFeign specificationFeign;

    @PostMapping(value = "/query", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<SpecificationResp>> query(@RequestBody SpecificationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("query", request, req -> {
            Long shopId = TracerUtil.getShopDto().getShopId();
            request.setShopId(shopId);
            return ThriftResponseHelper.executeThriftCall(() -> specificationFeign.query(request));
        });
    }


    @PostMapping(value = "/create", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> create(@RequestBody SpecificationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("create", request, req -> {
            Long shopId = TracerUtil.getShopDto().getShopId();
            request.setShopId(shopId);
            ThriftResponseHelper.executeThriftCall(() -> specificationFeign.create(request));
            return new BaseResp();
        });
    }

    @PostMapping(value = "/save", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> save(@RequestBody SpecificationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            Long shopId = TracerUtil.getShopDto().getShopId();
            request.setShopId(shopId);
            ThriftResponseHelper.executeThriftCall(() -> specificationFeign.save(request));
            return new BaseResp();
        });
    }


    @PostMapping(value = "/remove", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> remove(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("save", baseIdReq, req -> {
            req.checkParameter();
            Long shopId = TracerUtil.getShopDto().getShopId();
            ThriftResponseHelper.executeThriftCall(() -> specificationFeign.remove(shopId, req.getId()));
            return new BaseResp();
        });
    }


    @PostMapping(value = "/createName", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> createName(@RequestBody CreateNameReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createName", request, req -> {
            Long shopId = TracerUtil.getShopDto().getShopId();
            request.setShopId(shopId);
            return ThriftResponseHelper.executeThriftCall(() -> specificationFeign.createName(request));
        });
    }


    @PostMapping(value = "/createValue", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> createValue(@RequestBody CreateValueReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createValue", request, req -> {
            Long shopId = TracerUtil.getShopDto().getShopId();
            request.setShopId(shopId);
            return ThriftResponseHelper.executeThriftCall(() -> specificationFeign.createValue(request));
        });
    }


    @GetMapping(value = "/names")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<List<SpecificationNameResp>> getNames() throws TException {
        return ThriftResponseHelper.responseInvoke("getNames", null,
            req -> {
                Long shopId = TracerUtil.getShopDto().getShopId();
                return ThriftResponseHelper.executeThriftCall(() -> specificationFeign.getNames(shopId));
            });
    }


    @GetMapping(value = "/values")
    public ResultDto<List<SpecificationValueResp>> getValues(@RequestParam Long nameId) throws TException {
        return ThriftResponseHelper.responseInvoke("getValues", null,
            req -> ThriftResponseHelper.executeThriftCall(() -> specificationFeign.getValues(nameId)));
    }
}
