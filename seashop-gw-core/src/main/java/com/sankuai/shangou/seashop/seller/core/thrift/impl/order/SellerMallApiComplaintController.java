package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.*;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.ApplyArbitrateComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.DealSellerComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiApplyArbitrateComplaintReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiDealSellerComplaintReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQuerySellerComplaintReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.order.ApiQueryOrderComplaintResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description：投诉维权新增、修改、删除类接口
 * @author： liweisong
 * @create： 2023/11/21 18:02
 */

@RestController
@RequestMapping("/sellerApi/apiComplaint")
public class SellerMallApiComplaintController {

    @Resource
    private ComplaintCmdFeign complaintCmdFeign;
    @Resource
    private ComplaintQueryFeign complaintQueryFeign;

    @PostMapping(value = "/dealSellerComplaint", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> dealSellerComplaint(@RequestBody ApiDealSellerComplaintReq dealSellerComplaintReq) {
        

        return ThriftResponseHelper.responseInvoke("dealSellerComplaint", dealSellerComplaintReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> complaintCmdFeign.dealSellerComplaint(JsonUtil.copy(req, DealSellerComplaintReq.class)));
        });
    }

    @PostMapping(value = "/applyArbitrateComplaint", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> applyArbitrateComplaint(@RequestBody ApiApplyArbitrateComplaintReq applyArbitrateComplaintReq) {

        

        return ThriftResponseHelper.responseInvoke("applyArbitrateComplaint", applyArbitrateComplaintReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> complaintCmdFeign.applyArbitrateComplaint(JsonUtil.copy(req, ApplyArbitrateComplaintReq.class)));
        });
    }

    @PostMapping(value = "/pageSellerComplaint", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiQueryOrderComplaintResp>> pageSellerComplaint(@RequestBody ApiQuerySellerComplaintReq querySellerComplaintReq) {

        

        return ThriftResponseHelper.responseInvoke("pageSellerComplaint", querySellerComplaintReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            BasePageResp<QueryOrderComplaintResp> queryOrderComplaintRespBasePageResp = ThriftResponseHelper.executeThriftCall(() ->
                    complaintQueryFeign.pageSellerComplaint(JsonUtil.copy(req, QuerySellerComplaintReq.class)));
            return PageResultHelper.transfer(queryOrderComplaintRespBasePageResp, ApiQueryOrderComplaintResp.class);
        });
    }
}
