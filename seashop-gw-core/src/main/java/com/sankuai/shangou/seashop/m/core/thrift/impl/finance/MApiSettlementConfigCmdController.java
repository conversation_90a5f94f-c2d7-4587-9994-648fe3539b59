package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiSettlementConfigService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettlementConfigReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiSettlementConfig")
public class MApiSettlementConfigCmdController {

    @Resource
    private MApiSettlementConfigService mApiSettlementConfigService;

    @PostMapping(value = "/update", consumes = "application/json")
    public ResultDto<BaseResp> update(@RequestBody ApiSettlementConfigReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("update", request, req -> {
            req.checkParameter();
            return mApiSettlementConfigService.update(req);
        });
    }
}
