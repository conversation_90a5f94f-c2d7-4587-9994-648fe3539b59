package com.sankuai.shangou.seashop.m.core.service.export.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.PlatformTaskQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.task.PlatformTaskListResp;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.config.ExportTaskProps;
import com.sankuai.shangou.seashop.base.export.enums.TaskBizType;
import com.sankuai.shangou.seashop.base.export.enums.TaskType;
import com.sankuai.shangou.seashop.base.export.exec.AbstractExcelExportExec;
import com.sankuai.shangou.seashop.base.export.exec.ExcelExportExec;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.base.export.handler.ExportTaskHandler;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.thrift.core.dto.PlatformTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TaskStatusEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;
import com.sankuai.shangou.seashop.m.common.remote.base.MTaskRemoteService;
import com.sankuai.shangou.seashop.m.common.remote.model.base.CompleteTaskBo;
import com.sankuai.shangou.seashop.m.common.remote.model.base.CreateTaskBo;
import com.sankuai.shangou.seashop.m.common.remote.model.base.ExceptionTaskBo;
import com.sankuai.shangou.seashop.m.core.mq.publisher.MExportTaskPublisher;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.export.model.ExportTaskBo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
@Service
public class MExportTaskBizImpl extends AbstractExcelExportExec implements MExportTaskBiz, ExcelExportExec {

    @Resource
    private MTaskRemoteService mTaskRemoteService;
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private MExportTaskPublisher mExportTaskPublisher;
    @Resource
    @Lazy
    private S3plusStorageService s3plusStorageService;
    @Value("${s3plus.hostName:}")
    private String s3plusHostName;
    @Resource
    private ExportTaskProps exportTaskProps;
    @Resource
    private PlatformTaskQueryFeign platformTaskQueryFeign;
    @Resource
    private StorageClient storageClient;


    public MExportTaskBizImpl(ExportTaskHandler exportTaskHandler, ExportTaskProps exportTaskProps) {
        super(exportTaskHandler, exportTaskProps);
    }

    @Override
    public List<ExportTask> getTaskList() {
        return null;
    }

    @Override
    public void start(ExportTask task) {
        Long taskId = Long.parseLong(task.getTaskId());
        mTaskRemoteService.startTask(taskId);
    }

    @Override
    public void complete(ExportTask task) {
        Long taskId = Long.parseLong(task.getTaskId());
        CompleteTaskBo completeTaskBo = new CompleteTaskBo();
        completeTaskBo.setTaskId(taskId);
        completeTaskBo.setExecuteResult("");
        completeTaskBo.setTotalNum(task.getCount());
        completeTaskBo.setSuccessNum(0);
        completeTaskBo.setFailedNum(0);
        completeTaskBo.setFilePath(task.getFilePath());
        mTaskRemoteService.completeTask(completeTaskBo);
    }

    @Override
    public void exception(ExportTask task) {
        Long taskId = Long.parseLong(task.getTaskId());

        ExceptionTaskBo exceptionTaskBo = new ExceptionTaskBo();
        exceptionTaskBo.setTaskId(taskId);
        exceptionTaskBo.setExceptionContent(task.getException());
        mTaskRemoteService.exceptionTask(exceptionTaskBo);
    }

    @Override
    public List<ExportTask> getRedoTaskList() {
        QueryTaskReq queryReq = new QueryTaskReq();
        queryReq.setEnv(env);
        queryReq.setTaskStatusList(CollUtil.newArrayList(TaskStatusEnum.READY.getCode(), TaskStatusEnum.PROCESSING.getCode(), TaskStatusEnum.FAILED.getCode()));
        PlatformTaskListResp resp = ThriftResponseHelper.executeThriftCall(() -> platformTaskQueryFeign.queryList(queryReq));
        List<PlatformTaskDto> needRedoTaskList =  resp.getTaskList();
        if (CollUtil.isEmpty(needRedoTaskList)) {
            return null;
        }
        Date now = new Date();
        return needRedoTaskList.stream()
            // 过滤掉重试次数超过配置的任务
            .filter(task -> task.getRetryTimes() < exportTaskProps.getMaxRetryTimes() + 1)
            // 过滤出执行中，但是执行时间超过配置时间的；或者是失败的
            .filter(task -> {
                if (TaskStatusEnum.PROCESSING.getCode().equals(task.getTaskStatus())) {
                    return now.getTime() - task.getBeginTime().getTime() > exportTaskProps.getProcessTimeoutMillis();
                }
                if (TaskStatusEnum.READY.getCode().equals(task.getTaskStatus())) {
                    return now.getTime() - task.getCreateTime().getTime() > exportTaskProps.getReadyTimeoutMillis();
                }
                return TaskStatusEnum.FAILED.getCode().equals(task.getTaskStatus());
            })
            .map(task -> {
                ExportTask exportTask = new ExportTask();
                exportTask.setTaskType(task.getTaskType());
                exportTask.setExecuteParam(task.getExecuteParam());
                exportTask.setTaskId(String.valueOf(task.getId()));
                exportTask.setTaskDate(task.getCreateTime());
                return exportTask;
            })
            .collect(Collectors.toList());
    }

    @Override
    public ExportTaskBo create(CreateExportTaskBo createPo) {
        CreateTaskBo createTaskBo = new CreateTaskBo();

        TaskType taskType = createPo.getTaskType();
        createTaskBo.setTaskType(taskType.getType());
        createTaskBo.setTaskName(taskType.getName());
        createTaskBo.setBizType(TaskBizType.EXPORT.getCode());
        createTaskBo.setOperatorId(createPo.getOperatorId());
        createTaskBo.setOperatorAccount(createPo.getOperatorName());
        if (createPo.getExecuteParam() != null) {
            createTaskBo.setExecuteParam(JsonUtil.toJsonString(createPo.getExecuteParam()));
        }
        else {
            createTaskBo.setExecuteParam("");
        }

        // 自定义任务名称 默认是根据taskType获取的
        if (StrUtil.isNotEmpty(createPo.getCustomTaskName())) {
            createTaskBo.setTaskName(createPo.getCustomTaskName());
        }

        createTaskBo.setEnv(env);
        Long taskId = mTaskRemoteService.createTask(createTaskBo);

        // 发送MQ消息，如果MQ消息发送失败，后续会有定时任务检查补偿
        ExportTask exportTask = new ExportTask();
        exportTask.setTaskType(createTaskBo.getTaskType());
        exportTask.setExecuteParam(createTaskBo.getExecuteParam());
        exportTask.setTaskId(String.valueOf(taskId));
        exportTask.setTaskDate(new Date());
        mExportTaskPublisher.sendAsyncExportTaskMessage(exportTask);
        return null;
    }

    @Override
    public void execute(ExportTask task) {
        super.executeTask(task);
    }

    @Override
    public BasePageResp<PlatformTaskDto> pageList(QueryTaskReq queryReq) {
        queryReq.setEnv(env);
        BasePageResp<PlatformTaskDto> pageList = ThriftResponseHelper.executeThriftCall(() -> platformTaskQueryFeign.pageList(queryReq));
        if (pageList == null || CollUtil.isEmpty(pageList.getData())) {
            return PageResultHelper.defaultEmpty(queryReq);
        }
        pageList.getData()
            .forEach(data -> {
                if (StrUtil.isNotBlank(data.getFilePath())) {
                    data.setDownloadUrl(storageClient.formatUrl(data.getFilePath()));
                }
            });
        return pageList;
    }

    @Override
    public void checkAndRedoIfNecessary() {
        super.checkAndRedo();
    }
}
