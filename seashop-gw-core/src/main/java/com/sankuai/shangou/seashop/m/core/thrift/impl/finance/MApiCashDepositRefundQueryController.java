package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiCashDepositRefundService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiCashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositRefundResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiCashDepositRefund")
public class MApiCashDepositRefundQueryController {

    @Resource
    private MApiCashDepositRefundService mApiCashDepositRefundService;

    @PostMapping(value = "/refundList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiCashDepositRefundResp>> refundList(@RequestBody ApiCashDepositRefundQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("refundList", request, req ->
            mApiCashDepositRefundService.refundList(req));
    }

    @GetMapping(value = "/refundAuditNum")
    public ResultDto<Long> refundAuditNum() throws TException {
        return ThriftResponseHelper.responseInvoke("refundAuditNum", null, req ->
            mApiCashDepositRefundService.refundAuditNum());
    }

    @GetMapping(value = "/refundDetail")
    public ResultDto<ApiCashDepositRefundDetailResp> refundDetail(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("refundDetail", id, req ->
            mApiCashDepositRefundService.refundDetail(req));
    }
}
