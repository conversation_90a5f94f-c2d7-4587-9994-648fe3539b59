package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiFinanceService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderStatisticsReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiFinanceIndexResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiOrderStatisticsListResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFinance")
public class MApiFinanceQueryController {

    @Resource
    private MApiFinanceService mApiFinanceService;

    @GetMapping(value = "/getFinanceIndex")
    public ResultDto<ApiFinanceIndexResp> getFinanceIndex() throws TException {
        return ThriftResponseHelper.responseInvoke("getFinanceIndex", null, req -> {
            ApiFinanceIndexResp resp = mApiFinanceService.getFinanceIndex();
            return resp;
        });
    }

    @PostMapping(value = "/getOrderStatisticsList", consumes = "application/json")
    public ResultDto<ApiOrderStatisticsListResp> getOrderStatisticsList(@RequestBody ApiOrderStatisticsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getOrderStatisticsList", request, req -> {
            req.checkParameter();
            ApiOrderStatisticsListResp resp = mApiFinanceService.getOrderStatisticsList(req);
            return resp;
        });
    }

}
