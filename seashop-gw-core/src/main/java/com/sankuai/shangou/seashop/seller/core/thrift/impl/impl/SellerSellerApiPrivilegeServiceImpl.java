package com.sankuai.shangou.seashop.seller.core.thrift.impl.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerPrivilegeRemoteService;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiPrivilegeService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryPrivilegeReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiPrivilegeResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiPrivilegeRespList;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiUserPrivilegeResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.PrivilegeRespList;
import com.sankuai.shangou.seashop.user.thrift.account.response.UserPrivilegeResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class SellerSellerApiPrivilegeServiceImpl implements SellerApiPrivilegeService {
    @Resource
    private SellerPrivilegeRemoteService sellerPrivilegeRemoteService;


    @Override
    public ApiPrivilegeRespList queryPrivilegeList(ApiQueryPrivilegeReq queryPrivilegePageReq) {
        //转化参数
        QueryPrivilegeReq queryPrivilegeReq = JsonUtil.copy(queryPrivilegePageReq, QueryPrivilegeReq.class);
        //获取结果
        PrivilegeRespList privilegeRespList = sellerPrivilegeRemoteService.queryPrivilegeList(queryPrivilegeReq);
        //转化结果
        return new ApiPrivilegeRespList(JsonUtil.copyList(privilegeRespList.getPrivilegeRespList(), ApiPrivilegeResp.class));
    }

    @Override
    public ApiUserPrivilegeResp queryUserPrivilege(QueryUserPrivilegeReq privilegeResp) {
        UserPrivilegeResp userPrivilegeResp = sellerPrivilegeRemoteService.queryUserPrivilege(privilegeResp);
        //转化结果
        return JsonUtil.copy(userPrivilegeResp, ApiUserPrivilegeResp.class);
    }
}
