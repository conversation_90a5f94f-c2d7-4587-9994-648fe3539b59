package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiBusinessCategoryApplyService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiAuditBusinessCategoryApplyReq;

@RestController
@RequestMapping("/mApi/apiBusinessCategoryApply")
public class MApiBusinessCategoryApplyCmdController {
    @Resource
    private MApiBusinessCategoryApplyService mApiBusinessCategoryApplyService;

    @PostMapping(value = "/auditBusinessCategoryApply", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> auditBusinessCategoryApply(@RequestBody ApiAuditBusinessCategoryApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("auditBusinessCategoryApply", request, req -> {
            req.checkParameter();

            BaseResp resp = mApiBusinessCategoryApplyService.auditBusinessCategoryApply(req);
            return resp;
        });
    }
}
