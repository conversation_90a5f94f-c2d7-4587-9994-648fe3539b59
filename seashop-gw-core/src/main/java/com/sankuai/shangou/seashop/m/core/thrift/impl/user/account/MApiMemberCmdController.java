package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.enums.ToUserEnum;
import com.sankuai.shangou.seashop.m.common.remote.base.MApiMessageRecordRemoteService;
import com.sankuai.shangou.seashop.m.common.remote.user.MApiMemberRemoteService;
import com.sankuai.shangou.seashop.m.core.mq.dto.SendMessageBody;
import com.sankuai.shangou.seashop.m.core.mq.dto.SendType;
import com.sankuai.shangou.seashop.m.core.mq.publisher.SendMessagePublisher;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiBatchCmdMemberLabelReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiBatchCmdMemberReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdMemberReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdSendAllCoupon;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdSendCoupon;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryMemberReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMemberLabelRespList;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiMember")
public class MApiMemberCmdController {
    @Resource
    private MApiMemberRemoteService mApiMemberRemoteService;
    @Resource
    private MApiMessageRecordRemoteService mApiMessageRecordRemoteService;
    @Resource
    private SendMessagePublisher sendMessagePublisher;

    @PostMapping(value = "/freezeMember", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> freezeMember(@RequestBody ApiCmdMemberReq cmdMemberReq) {
        return ThriftResponseHelper.responseInvoke("freezeMember", cmdMemberReq, req -> mApiMemberRemoteService.freezeMember(cmdMemberReq));
    }

    @PostMapping(value = "/thawingMember", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> thawingMember(@RequestBody ApiCmdMemberReq cmdMemberReq) {
        return ThriftResponseHelper.responseInvoke("thawingMember", cmdMemberReq, req -> mApiMemberRemoteService.thawingMember(cmdMemberReq));
    }

    @PostMapping(value = "/changePassword", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> changePassword(@RequestBody ApiCmdMemberReq cmdMemberReq) {
        return ThriftResponseHelper.responseInvoke("changePassword", cmdMemberReq, req -> mApiMemberRemoteService.changePassword(cmdMemberReq));
    }

    @PostMapping(value = "/unbindPhone", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> unbindPhone(@RequestBody ApiCmdMemberReq cmdMemberReq) {
        return ThriftResponseHelper.responseInvoke("unbindPhone", cmdMemberReq, req -> mApiMemberRemoteService.unbindPhone(cmdMemberReq));
    }

    @PostMapping(value = "/batchDelete", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> batchDelete(@RequestBody ApiBatchCmdMemberReq batchCmdMemberReq) {
        return ThriftResponseHelper.responseInvoke("batchDelete", batchCmdMemberReq, req -> mApiMemberRemoteService.batchDelete(batchCmdMemberReq));
    }

    @PostMapping(value = "/getMemberLabel", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiMemberLabelRespList> getMemberLabel(@RequestBody ApiQueryMemberReq queryMemberReq) {
        return ThriftResponseHelper.responseInvoke("getMemberLabel", queryMemberReq, req -> mApiMemberRemoteService.getMemberLabel(queryMemberReq));
    }

    @PostMapping(value = "/setMemberLabel", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> setMemberLabel(@RequestBody ApiCmdMemberReq cmdMemberReq) {
        return ThriftResponseHelper.responseInvoke("setMemberLabel", cmdMemberReq, req -> mApiMemberRemoteService.setMemberLabel(cmdMemberReq));
    }

    @PostMapping(value = "/sendAllCoupon", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> sendAllCoupon(@RequestBody ApiCmdSendAllCoupon apiCmdSendAllCoupon) throws TException {
        return ThriftResponseHelper.responseInvoke("sendAllCoupon", apiCmdSendAllCoupon, req -> {
            SendMessageBody sendMessageBody = new SendMessageBody();
            sendMessageBody.setSendType(SendType.MEMBER.getCode());
            sendMessageBody.setMessage(JsonUtil.toJsonString(req));
            sendMessagePublisher.sendTaskMessage(sendMessageBody);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/sendCoupon", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> sendCoupon(@RequestBody ApiCmdSendCoupon sendCoupon) throws TException {
        return ThriftResponseHelper.responseInvoke("sendCoupon", sendCoupon, req -> {
            sendCoupon.setMsgId(mApiMessageRecordRemoteService.addCouponRecord(null, ToUserEnum.User, req.getCouponIds(),
                req.getOperationUserId()));
            try {
                mApiMemberRemoteService.sendCoupon(sendCoupon);
            }
            catch (BusinessException e) {
                mApiMessageRecordRemoteService.deleteCouponRecord(sendCoupon.getMsgId());
                throw e;
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/batchAddMemberLabel", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> batchAddMemberLabel(@RequestBody ApiBatchCmdMemberLabelReq cmdMemberReq) {
        return ThriftResponseHelper.responseInvoke("batchAddMemberLabel", cmdMemberReq, req -> mApiMemberRemoteService.batchAddMemberLabel(cmdMemberReq));
    }
}
