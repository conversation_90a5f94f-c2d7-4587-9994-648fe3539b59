package com.sankuai.shangou.seashop.seller.core.task.export;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
// 调整端口号，避免端口冲突
//@CraneConfiguration(taskAcceptorPort = 8422)
@Component
public class ExportFailTask {

    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;

    /**
     * 导出任务补偿检查
     * 每5分钟：0 0/5 * * * ?
     */
//    @Crane("SellerExportTaskFailCheckTask")
    public void exportTaskFailCheckTask() {
        log.info("【定时任务】【导出任务补偿检查】...start...");
        sellerExportTaskBiz.checkAndRedoIfNecessary();
    }

}
