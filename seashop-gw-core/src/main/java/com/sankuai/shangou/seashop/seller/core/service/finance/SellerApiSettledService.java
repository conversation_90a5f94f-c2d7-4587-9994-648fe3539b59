package com.sankuai.shangou.seashop.seller.core.service.finance;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledItemQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettledItemResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettledResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiSettlementDetailResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
public interface SellerApiSettledService {

    /**
     * 已结算列表查询
     *
     * @param request
     * @return
     */
    BasePageResp<ApiSettledResp> pageList(ApiSettledQryReq request);

    /**
     * 已结算列表明细查询
     *
     * @param request
     * @return
     */
    BasePageResp<ApiSettledItemResp> itemPageList(ApiSettledItemQryReq request);

    /**
     * 结算明细详情查询
     *
     * @param request
     * @return
     */
    ApiSettlementDetailResp getDetailByOrderId(ApiOrderIdQryReq request);

    /**
     * 导出结算列表
     *
     * @param request
     * @param shopInfo
     */
    void exportSettledList(ApiSettledQryReq request, LoginShopDto shopInfo);

    /**
     * 导出结算明细列表
     *
     * @param request
     * @param shopInfo
     */
    void exportSettledItemList(ApiSettledItemQryReq request, LoginShopDto shopInfo);
}
