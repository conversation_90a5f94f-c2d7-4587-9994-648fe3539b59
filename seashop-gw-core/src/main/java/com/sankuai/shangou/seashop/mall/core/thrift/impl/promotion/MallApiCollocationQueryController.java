package com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiMallCollocationReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiMallCollocationResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.MallCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.MallCollocationResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 15:48
 */
@RestController
@RequestMapping("/mallApi/apiCollocation")
public class MallApiCollocationQueryController {

    @Resource
    private CollocationQueryFeign collocationQueryFeign;

    @PostMapping(value = "/queryMallCollocationList", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<ApiMallCollocationResp> queryMallCollocationList(@RequestBody ApiMallCollocationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryMallCollocationList", request, req -> {
            req.checkParameter();

            MallCollocationResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    collocationQueryFeign.queryMallCollocationList(JsonUtil.copy(req, MallCollocationReq.class)));
            return JsonUtil.copy(resp, ApiMallCollocationResp.class);
        });
    }
}
