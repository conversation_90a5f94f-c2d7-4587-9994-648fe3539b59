package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiFlashSaleCategoryResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleCategoryResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCategoryQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFlashSaleCategory")
public class MApiFlashSaleCategoryQueryController {

    @Resource
    private FlashSaleCategoryQueryFeign flashSaleCategoryQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiFlashSaleCategoryResp>> pageList(@RequestBody BasePageReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.checkParameter();
            BasePageResp<FlashSaleCategoryResp> flashSaleCategoryPage = ThriftResponseHelper.executeThriftCall(() -> flashSaleCategoryQueryFeign.pageList(req));
            return JsonUtil.parseObject(JsonUtil.toJsonString(flashSaleCategoryPage), new TypeReference<BasePageResp<ApiFlashSaleCategoryResp>>() {
            });
        });
    }
}
