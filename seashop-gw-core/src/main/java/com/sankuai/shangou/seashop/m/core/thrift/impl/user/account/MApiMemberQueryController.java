package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.common.remote.user.MApiMemberRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryMemberPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryMemberReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMemberGroupingResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMemberResp;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiMember")
public class MApiMemberQueryController {

    @Resource
    private MApiMemberRemoteService mApiMemberRemoteService;
    @Resource
    private MExportTaskBiz exportTaskBiz;

    @PostMapping(value = "/queryMemberPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiMemberResp>> queryMemberPage(@RequestBody ApiQueryMemberPageReq queryMemberPageReq) {
        return ThriftResponseHelper.responseInvoke("queryMemberPage", queryMemberPageReq,
            req -> mApiMemberRemoteService.queryMemberPage(queryMemberPageReq));
    }

    @PostMapping(value = "/queryMember", consumes = "application/json")
    public ResultDto<ApiMemberResp> queryMember(@RequestBody ApiQueryMemberReq queryMemberReq) {
        return ThriftResponseHelper.responseInvoke("queryMember", queryMemberReq,
            req -> mApiMemberRemoteService.queryMember(queryMemberReq));
    }

    @GetMapping(value = "/queryMemberGrouping")
    public ResultDto<ApiMemberGroupingResp> queryMemberGrouping() throws TException {
        return ThriftResponseHelper.responseInvoke("queryMemberGrouping", null, req -> mApiMemberRemoteService.queryMemberGrouping());
    }

    @PostMapping(value = "/exportMember", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportMember(@RequestBody ApiQueryMemberPageReq queryMemberPageReq) throws TException {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.MEMBER_LIST);
        createExportTaskBo.setExecuteParam(queryMemberPageReq);
        LoginManagerDto loginDto = TracerUtil.getManagerDto();
        createExportTaskBo.setOperatorId(loginDto.getId());
        createExportTaskBo.setOperatorName(loginDto.getName());
        exportTaskBiz.create(createExportTaskBo);
        return ResultDto.newWithData(BaseResp.of());
    }


}
