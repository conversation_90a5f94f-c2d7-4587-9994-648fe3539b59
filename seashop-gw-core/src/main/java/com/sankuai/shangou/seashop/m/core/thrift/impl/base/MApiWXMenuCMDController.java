package com.sankuai.shangou.seashop.m.core.thrift.impl.base;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.base.MWXMenuService;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseWXMenuReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiSaveWXAccountReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiWXAccountResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdCreateQRReq;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/mApi/wxMenu")
public class MApiWXMenuCMDController {
    @Resource
    private MWXMenuService mWxMenuService;


    @PostMapping(value = "/saveWXAccount", consumes = "application/json")
    public ResultDto<BaseResp> saveWXAccount(@RequestBody ApiSaveWXAccountReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("saveWXAccount", request, req -> {
            mWxMenuService.saveWXAccount(req);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/create", consumes = "application/json")
    public ResultDto<Integer> create(@RequestBody ApiBaseWXMenuReq wxMenuReq) throws TException {
        wxMenuReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("createWXMenu", wxMenuReq, req -> {
            Integer id = mWxMenuService.create(req);
            return id;
        });
    }

    @PostMapping(value = "/update", consumes = "application/json")
    public ResultDto<Boolean> update(@RequestBody ApiBaseWXMenuReq wxMenuReq) throws TException {
        wxMenuReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("updateWXMenu", wxMenuReq, req -> {
            return mWxMenuService.update(wxMenuReq);
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    public ResultDto<Boolean> delete(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteWXMenu", baseIdReq, req -> {
            return mWxMenuService.delete(baseIdReq);
        });
    }

    //菜单同步到微信
    @PostMapping(value = "/syncWXMenu", consumes = "application/json")
    public ResultDto<Boolean> syncWXMenu() throws TException {
        return ThriftResponseHelper.responseInvoke("syncWXMenu", null, req -> {
            return mWxMenuService.syncWXMenu();
        });
    }

    //菜单同步到微信
    @GetMapping(value = "/getWXAccount")
    @Operation(summary = "获取公众号账户")
    public ResultDto<ApiWXAccountResp> getWXAccount() throws TException {
        return ThriftResponseHelper.responseInvoke("getWXAccount", null, req -> {
            return mWxMenuService.getWXAccount();
        });
    }

    @PostMapping(value = "/createQrCode", consumes = "application/json")
    @Operation(summary = "获取二维码")
    public ResultDto<String> createQrCode(@RequestBody ApiCmdCreateQRReq qrCode) throws TException {
        return ThriftResponseHelper.responseInvoke("getWXAccount", qrCode, req -> {
            return mWxMenuService.createQrCode(req);
        });
    }

}
