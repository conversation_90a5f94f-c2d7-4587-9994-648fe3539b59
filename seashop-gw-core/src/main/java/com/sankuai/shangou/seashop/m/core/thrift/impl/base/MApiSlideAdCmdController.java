package com.sankuai.shangou.seashop.m.core.thrift.impl.base;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.SlideAdCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MoveLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateLimitTimeBuyReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiAddLimitTimeBuyReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiMoveLimitTimeBuyReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiUpdateLimitTimeBuyReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiSlideAd")
public class MApiSlideAdCmdController {

    @Resource
    private SlideAdCmdFeign slideAdCmdFeign;

    @PostMapping(value = "/addLimitTimeBuy", consumes = "application/json")
    public ResultDto<BaseResp> addLimitTimeBuy(@RequestBody ApiAddLimitTimeBuyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("addLimitTimeBuy", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> slideAdCmdFeign.addLimitTimeBuy(JsonUtil.copy(req, AddLimitTimeBuyReq.class)));
        });
    }

    @PostMapping(value = "/updateLimitTimeBuy", consumes = "application/json")
    public ResultDto<BaseResp> updateLimitTimeBuy(@RequestBody ApiUpdateLimitTimeBuyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateLimitTimeBuy", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> slideAdCmdFeign.updateLimitTimeBuy(JsonUtil.copy(req, UpdateLimitTimeBuyReq.class)));
        });
    }

    @PostMapping(value = "/moveLimitTimeBuy", consumes = "application/json")
    public ResultDto<BaseResp> moveLimitTimeBuy(@RequestBody ApiMoveLimitTimeBuyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("moveLimitTimeBuy", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> slideAdCmdFeign.moveLimitTimeBuy(JsonUtil.copy(req, MoveLimitTimeBuyReq.class)));
        });
    }

    @PostMapping(value = "/deleteLimitTimeBuy", consumes = "application/json")
    public ResultDto<BaseResp> deleteLimitTimeBuy(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteLimitTimeBuy", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> slideAdCmdFeign.deleteLimitTimeBuy(req));
        });
    }
}
