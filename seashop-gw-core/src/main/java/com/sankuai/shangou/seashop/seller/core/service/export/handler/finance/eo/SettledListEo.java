package com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
@Getter
@Setter
public class SettledListEo {

    @ExcelProperty(value = "主键")
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "店铺id")
    @ExcelIgnore
    private Long shopId;

    @ExcelProperty(value = "店铺名称")
    @ExcelIgnore
    private String shopName;

    @ExcelProperty(value = "结算详情id")
    @ExcelIgnore
    private Long detailId;

    @ExcelProperty(value = "结算时间")
    private Date settleTime;

    @ExcelProperty(value = "结算周期-开始时间")
    @ExcelIgnore
    private Date startCycleTime;

    @ExcelProperty(value = "结算周期-结束时间")
    @ExcelIgnore
    private Date endCycleTime;

    @ExcelProperty(value = "结算周期")
    private String cycleTime;

    @ExcelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ExcelProperty(value = "佣金金额")
    private BigDecimal commissionAmount;

    @ExcelProperty(value = "结算金额")
    private BigDecimal settleAmount;

    @ExcelProperty(value = "手续费金额")
    private BigDecimal channelAmount;

}
