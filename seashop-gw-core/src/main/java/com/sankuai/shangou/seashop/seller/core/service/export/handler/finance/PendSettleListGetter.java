package com.sankuai.shangou.seashop.seller.core.service.export.handler.finance;

import java.util.Collections;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.eo.PendSettleListEo;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.wrapper.PendSettleListWrapper;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
@Service
public class PendSettleListGetter extends AbstractBaseDataGetter<ApiPendingSettlementOrderQryReq>
    implements SingleWrapperDataGetter<ApiPendingSettlementOrderQryReq> {

    @Resource
    @Lazy
    private SellerFinanceRemoteService sellerFinanceRemoteService;

    @Override
    public DataContext selectData(ApiPendingSettlementOrderQryReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        BaseExportWrapper<PendSettleListEo, ApiPendingSettlementOrderQryReq> wrapper = new PendSettleListWrapper(sellerFinanceRemoteService);
        context.setSheetDataList(Collections.singletonList(wrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.PEND_SETTLE_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.PEND_SETTLE_LIST.getName();
    }
}
