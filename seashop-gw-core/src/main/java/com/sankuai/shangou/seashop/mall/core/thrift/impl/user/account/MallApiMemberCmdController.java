package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.gw.common.thrift.requests.ApiRefreshTokenReq;
import com.sankuai.gw.common.thrift.requests.ApiRegisterReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiModifyPasswordReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiResetPasswordReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiUpdateMemberReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiResetPasswordResp;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.config.LoginSecurityConfig;
import com.sankuai.shangou.seashop.base.security.utils.TokenUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.mall.core.service.user.account.MallMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/12/15 16:13
 */
@RestController
@RequestMapping("/mallApi/apiMember")
@Slf4j
public class MallApiMemberCmdController {

    @Resource
    private MallMemberService mallMemberService;

    @PostMapping(value = "/updateMember", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> updateMember(@RequestBody ApiUpdateMemberReq apiUpdateMemberReq) throws TException {


        LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();
        apiUpdateMemberReq.setId(loginMemberDto.getId());
        apiUpdateMemberReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("queryUserCenterHome", apiUpdateMemberReq, req ->
            mallMemberService.updateMember(apiUpdateMemberReq));
    }

    @GetMapping(value = "/updateLoginTime")
    @NeedLogin(force = false)
    public ResultDto<BaseResp> updateLoginTime() throws TException {
        if (TracerUtil.getMemberDto() == null) {
            return ResultDto.newWithData(BaseResp.of());
        }
        LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();
        return ThriftResponseHelper.responseInvoke("updateLoginTime", TracerUtil.getMemberDto() , req ->
            mallMemberService.updateLoginTime(loginMemberDto.getId()));
    }

    @PostMapping(value = "/modifyPassword", consumes = "application/json")
    @NeedLogin
    public ResultDto<ApiResetPasswordResp> modifyPassword(@RequestBody ApiModifyPasswordReq resetReq) throws TException {
        return ThriftResponseHelper.responseInvoke("resetPassword", resetReq, req -> {
            req.checkParameter();

            return mallMemberService.modifyPassword(req);
        });
    }

    @PostMapping(value = "/resetPassword", consumes = "application/json")
    public ResultDto<ApiResetPasswordResp> resetPassword(@RequestBody ApiResetPasswordReq resetReq) throws TException {
        return ThriftResponseHelper.responseInvoke("resetPassword", resetReq, req -> {
            req.checkParameter();

            return mallMemberService.resetPassword(req);
        });
    }


    @PostMapping(value = "/login",consumes = "application/json")
    public ResultDto<LoginResp> login(@RequestBody ApiLoginReq loginReq) throws TException {
        return ThriftResponseHelper.responseInvoke("login", loginReq, req -> {
            return mallMemberService.login(loginReq);
        });
    }

    //刷新token
    @GetMapping(value = "/refreshToken", consumes = "application/json")
    @NeedLogin
    public ResultDto<LoginResp> refreshToken() {
        return ThriftResponseHelper.responseInvoke("refreshToken", null, req -> {
            LoginMemberDto memberDto = TracerUtil.getMemberDto();
            TokenCache tokenCache = new TokenCache();
            String token = TokenUtil.getRequestToken(LoginSecurityConfig.TOKEN_NAME);
            tokenCache.setToken(token);
            tokenCache.setUserId(memberDto.getId());
            tokenCache.setUserType(RoleEnum.MEMBER.name());
            ApiRefreshTokenReq req1 = new ApiRefreshTokenReq();
            req1.setToken(tokenCache);
            req1.setRoleType(RoleEnum.SHOP.name());
            return mallMemberService.refreshToken(req1);
        });
    }

    @PostMapping(value = "/register",consumes = "application/json")
    public ResultDto<LoginResp> register(@RequestBody ApiRegisterReq registerReq) throws TException {
        return ThriftResponseHelper.responseInvoke("register", registerReq, req -> {
            return mallMemberService.register(req);
        });
    }

    //退出登录
    @GetMapping(value = "/logout")
    @NeedLogin
    public ResultDto<Boolean> logout() throws TException {
        return ThriftResponseHelper.responseInvoke("logout", null, req -> {
            return mallMemberService.logout();
        });
    }

}
