package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiExcessPaymentRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundRecordCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ExcessPaymentRefundReq;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/1/9/009
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiOrderRefundRecord")
@Slf4j
public class MApiOrderRefundRecordCmdController {

    @Resource
    private OrderRefundRecordCmdFeign orderRefundRecordCmdFeign;

    @PostMapping(value = "/excessPaymentRefund", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> excessPaymentRefund(@RequestBody ApiExcessPaymentRefundReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("excessPaymentRefund", request, req -> {
//            LoginManagerDto manager = TracerUtil.getManager();
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            ExcessPaymentRefundReq excessPaymentRefundReq = JsonUtil.copy(req, ExcessPaymentRefundReq.class);
            excessPaymentRefundReq.setOperatorId(loginDto.getId());
            excessPaymentRefundReq.setOperatorName(loginDto.getName());
            excessPaymentRefundReq.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> orderRefundRecordCmdFeign.excessPaymentRefund(excessPaymentRefundReq));
        });
    }
}
