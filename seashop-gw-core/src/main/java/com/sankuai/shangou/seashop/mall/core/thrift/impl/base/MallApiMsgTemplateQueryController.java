package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.ApiQueryAppletTemplateReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.ApiQueryWxAppletFormDataReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiBaseMsgTemplateResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiQueryWxAppletFormDataResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MsgTemplateQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryAppletTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryWxAppletFormDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryWxAppletFormDataResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:07
 */
@RestController
@RequestMapping("/mallApi/apiMsgTemplate")
public class MallApiMsgTemplateQueryController {

    @Resource
    private MsgTemplateQueryFeign msgTemplateQueryFeign;

    @PostMapping(value = "/queryWxAppletFormData", consumes = "application/json")
    public ResultDto<List<ApiQueryWxAppletFormDataResp>> queryWxAppletFormData(@RequestBody ApiQueryWxAppletFormDataReq queryWxAppletFormDataReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWxAppletFormData", queryWxAppletFormDataReq, req -> {

            List<QueryWxAppletFormDataResp> resp = ThriftResponseHelper.executeThriftCall(() ->
                    msgTemplateQueryFeign.queryWxAppletFormData(JsonUtil.copy(req, QueryWxAppletFormDataReq.class)));
            return JsonUtil.copyList(resp, ApiQueryWxAppletFormDataResp.class);
        });
    }

    @PostMapping(value = "/queryAppletTemplate", consumes = "application/json")
    public ResultDto<List<ApiBaseMsgTemplateResp>> queryAppletTemplate(@RequestBody ApiQueryAppletTemplateReq queryAppletTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryAppletTemplate", queryAppletTemplateReq, req -> {

            List<BaseMsgTemplateResp> resp = ThriftResponseHelper.executeThriftCall(() ->
                    msgTemplateQueryFeign.queryAppletTemplate(JsonUtil.copy(req, QueryAppletTemplateReq.class)));
            return JsonUtil.copyList(resp, ApiBaseMsgTemplateResp.class);
        });
    }


}
