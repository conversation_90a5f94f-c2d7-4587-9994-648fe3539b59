package com.sankuai.shangou.seashop.mall.core.thrift.impl.trade;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiAddonActivityDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiTradeProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.*;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.*;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.product.ApiProductBaseInfoResp;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.mall.core.service.trade.MallTradeProductService;
import com.sankuai.shangou.seashop.mall.core.service.user.shop.MallApiShopService;
import com.sankuai.shangou.seashop.trade.thrift.core.TradeProductQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.*;
import com.sankuai.shangou.seashop.trade.thrift.core.response.*;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.CalculateFreightResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.ProductBaseInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiTradeProduct")
@Slf4j
public class MallApiTradeProductQueryController {

    @Resource
    private MallTradeProductService mallTradeProductService;
    @Resource
    private TradeProductQueryFeign tradeProductQueryFeign;

    @NeedLogin(force = false)
    @PostMapping(value = "/search", consumes = "application/json")
    public ResultDto<ApiSearchTradeProductResp> search(@RequestBody ApiSearchTradeProductReq searchReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【交易商品】搜索商品列表", searchReq, func -> {
            SearchTradeProductReq req = JsonUtil.copy(searchReq, SearchTradeProductReq.class);
            if (TracerUtil.getMemberDto() != null) {
                req.setUserId(TracerUtil.getMemberDto().getId());
            }
            SearchTradeProductResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.search(req));
            return JsonUtil.copy(resp, ApiSearchTradeProductResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/searchAddonProduct", consumes = "application/json")
    public ResultDto<ApiSearchAddonProductResp> searchAddonProduct(@RequestBody ApiQueryAddonProductReq searchReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【凑单】获取凑单商品列表", searchReq, func -> {

            QueryAddonProductReq remoteReq = JsonUtil.copy(func, QueryAddonProductReq.class);
            UserDto user = new UserDto();
            LoginMemberDto member = TracerUtil.getMemberDto();
            user.setUserId(member.getId());
            user.setUserName(member.getName());
            user.setUserPhone(member.getUserPhone());
            remoteReq.setUser(user);
            SearchAddonProductResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.searchAddonProduct(remoteReq));
            return JsonUtil.copy(resp, ApiSearchAddonProductResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/getShopAddonActivity", consumes = "application/json")
    public ResultDto<ApiAddonActivityResp> getShopAddonActivity(@RequestBody ApiQueryShopAddonActivityReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【凑单】获取凑单活动列表", req, func -> {
            req.checkParameter();
            QueryShopAddonActivityReq param = JsonUtil.copy(req, QueryShopAddonActivityReq.class);
            param.setUserId(TracerUtil.getMemberDto().getId());
            AddonActivityResp result = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.getShopAddonActivity(param));
            List<ApiAddonActivityDto> activityDtoList = JsonUtil.copyList(result.getActivityList(), ApiAddonActivityDto.class);
            ApiAddonActivityResp resp = new ApiAddonActivityResp();
            resp.setActivityList(activityDtoList);
            return resp;
        });
    }

    @PostMapping(value = "/queryProductBaseInfo", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<ApiProductBaseInfoResp> queryProductBaseInfo(@RequestBody ApiQueryProductDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductBaseInfo", request, req -> {
            req.checkParameter();

            ProductBaseInfoResp resp = mallTradeProductService.queryProductBaseInfo(req, TracerUtil.getMemberDto());
            return JsonUtil.copy(resp, ApiProductBaseInfoResp.class);
        });
    }

    @PostMapping(value = "/queryHotSaleProduct", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<ApiHotSaleProductResp> queryHotSaleProduct(@RequestBody ApiQueryHotSaleProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryHotSaleProduct", request, req -> {
            req.checkParameter();

            QueryHotSaleProductReq remoteReq = JsonUtil.copy(req, QueryHotSaleProductReq.class);

            if (TracerUtil.getMemberDto() != null) {
                remoteReq.setUserId(TracerUtil.getMemberDto().getId());
            }
            HotSaleProductResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.queryHotSaleProduct(remoteReq));
            return JsonUtil.copy(resp, ApiHotSaleProductResp.class);
        });
    }

    @PostMapping(value = "/queryHotAttentionProduct", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<ApiHotAttentionProductResp> queryHotAttentionProduct(@RequestBody ApiQueryHotAttentionProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryHotAttentionProduct", request, req -> {
            req.checkParameter();

            QueryHotAttentionProductReq remoteReq = JsonUtil.copy(req, QueryHotAttentionProductReq.class);

            if (TracerUtil.getMemberDto() != null) {
                remoteReq.setUserId(TracerUtil.getMemberDto().getId());
            }
            HotAttentionProductResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.queryHotAttentionProduct(remoteReq));
            return JsonUtil.copy(resp, ApiHotAttentionProductResp.class);
        });
    }

    @NeedLogin(force = false)
    @PostMapping(value = "/queryGuessYouLike", consumes = "application/json")
    public ResultDto<ApiGuessYouLikeProductResp> queryGuessYouLike(@RequestBody ApiQueryGuessYouLikeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryGuessYouLike", request, req -> {
            req.checkParameter();

            QueryGuessYouLikeReq remoteReq = JsonUtil.copy(req, QueryGuessYouLikeReq.class);

            if (TracerUtil.getMemberDto() != null) {
                remoteReq.setUserId(TracerUtil.getMemberDto().getId());
            }
            GuessYouLikeProductResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.queryGuessYouLike(remoteReq));
            return JsonUtil.copy(resp, ApiGuessYouLikeProductResp.class);
        });
    }

    @NeedLogin(force = false)
    @PostMapping(value = "/searchInShop", consumes = "application/json")
    public ResultDto<BasePageResp<ApiTradeProductDto>> searchInShop(@RequestBody ApiSearchProductInShopReq searchReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【交易商品】店铺内搜索商品", searchReq, func -> {
            SearchProductInShopReq req = JsonUtil.copy(searchReq, SearchProductInShopReq.class);

            if (TracerUtil.getMemberDto() != null) {
                UserDto user = new UserDto();
                LoginMemberDto member = TracerUtil.getMemberDto();
                user.setUserId(member.getId());
                user.setUserName(member.getName());
                user.setUserPhone(member.getUserPhone());
                req.setUser(user);
            }
            BasePageResp<TradeProductDto> resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.searchInShop(req));
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<ApiTradeProductDto>>() {
            });
        });
    }

    @PostMapping(value = "/calculateFreight", consumes = "application/json")
    public ResultDto<ApiCalculateFreightResp> calculateFreight(@RequestBody ApiCalculateFreightReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("calculateFreight", request, req -> {
            req.checkParameter();

            CalculateFreightReq remoteReq = JsonUtil.copy(req, CalculateFreightReq.class);
            CalculateFreightResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.calculateFreight(remoteReq));
            return JsonUtil.copy(resp, ApiCalculateFreightResp.class);
        });
    }
}
