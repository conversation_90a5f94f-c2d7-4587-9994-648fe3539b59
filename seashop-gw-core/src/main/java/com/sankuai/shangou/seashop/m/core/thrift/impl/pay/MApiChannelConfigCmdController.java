package com.sankuai.shangou.seashop.m.core.thrift.impl.pay;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.pay.ApiChannelConfigSaveReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ChannelConfigSaveReq;
import com.sankuai.shangou.seashop.pay.thrift.core.service.ChannelConfigCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiChannelConfig")
public class MApiChannelConfigCmdController {

    @Resource
    private ChannelConfigCmdFeign channelConfigCmdFeign;

    @PostMapping(value = "/update", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> update(@RequestBody ApiChannelConfigSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("update", request, req -> {
            request.setOperationUserId(TracerUtil.getManagerDto().getId());
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> channelConfigCmdFeign.update(JsonUtil.copy(req, ChannelConfigSaveReq.class)));
        });
    }
}
