package com.sankuai.shangou.seashop.mock;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.thrift.core.dto.MockPayCallBack;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.PayCallBackFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/mock")
@Slf4j
public class MockController {

    @Resource
    private PayCallBackFeign payCallBackFeign;

    @PostMapping(value = "/pay", consumes = "application/json")
    public ResultDto<String> mockCreatePay(@RequestBody OrderPayResp request) {
        log.info("mockCreatePay:{}", JsonUtil.toJsonString(request));
        try {
            return payCallBackFeign.mockCreatePay(request);
        } catch (TException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping(value = "/collBack", consumes = "application/json")
    public ResultDto<BaseResp> mockCallBack(@RequestBody MockPayCallBack callBack) {
        log.info("mockCallBack:{}", JsonUtil.toJsonString(callBack));
        try {
            return payCallBackFeign.mockCallBack(callBack);
        } catch (TException e) {
            throw new RuntimeException(e);
        }
    }
}
