package com.sankuai.shangou.seashop.m.core.mq.dto;

/**
 * @description:
 * @author: LXH
 **/
public enum SendType {

    MEMBER(0, "商家"),
    Purchasing(1, "购买力"),
    ;
    private Integer code;
    private String desc;


    SendType(Integer code, String desc) {

        this.code = code;
        this.desc = desc;
    }


    public Integer getCode() {

        return code;
    }

    public String getDesc() {

        return desc;
    }


    public static SendType getByCode(Integer code) {

        for (SendType type : SendType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }
}
