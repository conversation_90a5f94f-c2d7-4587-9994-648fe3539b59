package com.sankuai.shangou.seashop.m.core.service.export.handler.finance.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.common.remote.MFinanceRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo.SettledByOrderEo;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;

import cn.hutool.core.collection.CollUtil;

/**
 * @author: lhx
 * @date: 2024/3/26/026
 * @description:
 */
public class SettledByOrderWrapper extends PageExportWrapper<SettledByOrderEo, ApiSettledQryReq> {

    private final MFinanceRemoteService MFinanceRemoteService;

    public SettledByOrderWrapper(MFinanceRemoteService MFinanceRemoteService) {
        this.MFinanceRemoteService = MFinanceRemoteService;
    }

    @Override
    public List<SettledByOrderEo> getPageList(ApiSettledQryReq param) {
        // 这里入参为结算列表的参数，但是需要查结算明细，这里才有订单数据，明细的参数兼容结算列表的参数
        BasePageResp<SettledItemResp> settledItemPage = MFinanceRemoteService.settledItemPageList(JsonUtil.copy(param, SettledItemQryReq.class));
        if (null == settledItemPage || CollUtil.isEmpty(settledItemPage.getData())) {
            return null;
        }
        List<SettledByOrderEo> settledByOrderEoList = CollUtil.newArrayList();
        settledItemPage.getData().forEach(s -> {
            SettledByOrderEo settledByShopEo = JsonUtil.copy(s, SettledByOrderEo.class);
            settledByOrderEoList.add(settledByShopEo);
        });
        return settledByOrderEoList;
    }

    @Override
    public Integer getBatchCount() {
        return 1000;
    }
}
