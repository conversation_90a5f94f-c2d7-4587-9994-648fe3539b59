package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ManagerUserInfo;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiSettledService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledItemCountQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledItemQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledItemCountResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledItemResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettledResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettlementDetailResp;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiSettled")
public class MApiSettledQueryController {

    @Resource
    private MApiSettledService mApiSettledService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiSettledResp>> pageList(@RequestBody ApiSettledQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.valueInit();
            return mApiSettledService.pageList(req);
        });
    }

    @PostMapping(value = "/itemPageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiSettledItemResp>> itemPageList(@RequestBody ApiSettledItemQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("itemPageList", request, req -> {
            req.checkParameter();
            return mApiSettledService.itemPageList(req);
        });
    }

    @PostMapping(value = "/itemCount", consumes = "application/json")
    public ResultDto<ApiSettledItemCountResp> itemCount(@RequestBody ApiSettledItemCountQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("itemCount", request, req -> {
            req.checkParameter();
            return mApiSettledService.itemCount(req);
        });
    }

    @PostMapping(value = "/getDetailByOrderId", consumes = "application/json")
    public ResultDto<ApiSettlementDetailResp> getDetailByOrderId(@RequestBody ApiOrderIdQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getDetailByOrderId", request, req -> {
            req.checkParameter();
            return mApiSettledService.getDetailByOrderId(req);
        });
    }

    @PostMapping(value = "/exportSettledItemList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportSettledItemList(@RequestBody ApiSettledItemQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("exportSettledItemList", request, req -> {
            req.checkParameter();
            ManagerUserInfo managerUserInfo = new ManagerUserInfo();
            managerUserInfo.setLoginDto(TracerUtil.getManagerDto());
            mApiSettledService.exportSettledItemList(req, managerUserInfo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/exportSettledByShop", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportSettledByShop(@RequestBody ApiSettledQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("exportSettledByShop", request, req -> {
            req.valueInit();
            req.checkParameter();
            ManagerUserInfo managerUserInfo = new ManagerUserInfo();
            managerUserInfo.setLoginDto(TracerUtil.getManagerDto());
            mApiSettledService.exportSettledByShop(req, managerUserInfo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/exportSettledByOrder", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportSettledByOrder(@RequestBody ApiSettledQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("exportSettledByOrder", request, req -> {
            ManagerUserInfo managerUserInfo = new ManagerUserInfo();
            managerUserInfo.setLoginDto(TracerUtil.getManagerDto());
            mApiSettledService.exportSettledByOrder(req, managerUserInfo);
            return BaseResp.of();
        });
    }
}
