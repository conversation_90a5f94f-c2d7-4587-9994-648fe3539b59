package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiManagerService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryManagerPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryManagerReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiManagerResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMenuResp;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 商家服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiManager")
public class MApiManagerQueryController {
    @Resource
    private MApiManagerService mApiManagerService;


    @PostMapping(value = "/queryManagerPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiManagerResp>> queryManagerPage(@RequestBody ApiQueryManagerPageReq queryManagerPageReq) {
        return ThriftResponseHelper.responseInvoke("queryManagerPage", queryManagerPageReq, req -> mApiManagerService.queryManagerPage(queryManagerPageReq));
    }

    @PostMapping(value = "/queryManager", consumes = "application/json")
    public ResultDto<ApiManagerResp> queryManager(@RequestBody ApiQueryManagerReq queryManagerReq) {
        return ThriftResponseHelper.responseInvoke("queryManager", queryManagerReq, req -> mApiManagerService.queryManager(req));
    }

    @GetMapping(value = "/queryMenuAuth")
    @Operation(summary = "查询用户权限")
    @NeedLogin(userType = RoleEnum.MANAGER)
    ResultDto<List<ApiMenuResp>> queryMenuAuth() throws TException {
        return ThriftResponseHelper.responseInvoke("queryMenuAuth", null, req -> {
            Long managerId = TracerUtil.getManagerDto().getId();
            return mApiManagerService.queryMenuAuth(managerId);
        });
    }
}
