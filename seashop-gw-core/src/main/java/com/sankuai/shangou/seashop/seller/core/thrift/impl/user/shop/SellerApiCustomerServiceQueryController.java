package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.common.remote.SellerCustomerServiceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCustomerServiceQueryPageReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiCustomerServiceResp;

/**
 * @description：供应商客服查询接口
 * @author： LXH
 * @create： 2023/11/27 9:13
 */
@RestController
@RequestMapping("/sellerApi/apiCustomerService")
public class SellerApiCustomerServiceQueryController {
    @Resource
    SellerCustomerServiceRemoteService sellerCustomerServiceRemoteService;


    @PostMapping(value = "/queryPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiCustomerServiceResp>> queryPage(@RequestBody ApiCustomerServiceQueryPageReq baseIdReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryPage", null, func -> {
            // 参数对象转换
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            baseIdReq.setShopId(loginShopDto.getShopId());
            return sellerCustomerServiceRemoteService.queryPage(baseIdReq);
        });
    }

    @PostMapping(value = "/queryDetail", consumes = "application/json")
    public ResultDto<ApiCustomerServiceResp> queryDetail(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryDetail", baseIdReq, func -> {
            // 参数对象转换
            return sellerCustomerServiceRemoteService.queryDetail(baseIdReq);
        });
    }
}
