package com.sankuai.shangou.seashop.m.core.service.user.account.impl;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiRoleService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdRoleReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryRoleReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiRoleResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiRoleRespList;
import com.sankuai.shangou.seashop.user.thrift.account.RoleCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.RoleQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.RoleRespList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MApiRoleServiceImpl implements MApiRoleService {

    @Resource
    private RoleQueryFeign roleQueryFeign;
    @Resource
    private RoleCmdFeign roleCmdFeign;

    @Override
    public ApiRoleRespList queryRoleList(ApiQueryRoleReq queryRoleReq) {
        //转化参数
        QueryRoleReq queryPrivilegeReq = JsonUtil.copy(queryRoleReq, QueryRoleReq.class);
        queryPrivilegeReq.setShopId(0L);
        //获取结果
        RoleRespList roleRespList = ThriftResponseHelper.executeThriftCall(() -> roleQueryFeign.queryRoleList(queryPrivilegeReq));
        //转化结果
        return new ApiRoleRespList(JsonUtil.copyList(roleRespList.getRoleRespList(), ApiRoleResp.class));
    }

    @Override
    public Long addRole(ApiCmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        cmdRoleReq1.setShopId(0L);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> roleCmdFeign.addRole(cmdRoleReq1));
    }

    @Override
    public Long editRole(ApiCmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> roleCmdFeign.editRole(cmdRoleReq1));
    }

    @Override
    public Long deleteRole(ApiCmdRoleReq cmdRoleReq) {
        //转化参数
        CmdRoleReq cmdRoleReq1 = JsonUtil.copy(cmdRoleReq, CmdRoleReq.class);
        //获取结果
        return ThriftResponseHelper.executeThriftCall(() -> roleCmdFeign.deleteRole(cmdRoleReq1));
    }
}
