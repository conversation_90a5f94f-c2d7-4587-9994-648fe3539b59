package com.sankuai.shangou.seashop.seller.core.user.service;


import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryPrivilegeReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiPrivilegeRespList;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiUserPrivilegeResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserPrivilegeReq;

public interface SellerApiPrivilegeService {

    /**
     * 查询权限列表
     *
     * @param queryPrivilegePageReq 查询条件
     * @return PrivilegeRespList
     */
    ApiPrivilegeRespList queryPrivilegeList(ApiQueryPrivilegeReq queryPrivilegePageReq);

    /**
     * 查询用户权限
     *
     * @param privilegeResp 查询条件
     * @return ApiUserPrivilegeResp
     */
    ApiUserPrivilegeResp queryUserPrivilege(QueryUserPrivilegeReq privilegeResp);
}
