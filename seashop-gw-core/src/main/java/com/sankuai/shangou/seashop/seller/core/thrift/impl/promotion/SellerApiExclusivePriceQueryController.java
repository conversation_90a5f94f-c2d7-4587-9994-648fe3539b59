package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceSimpleResp;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerExclusivePriceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiExclusivePriceListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiExclusivePriceResp;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiExclusivePrice")
public class SellerApiExclusivePriceQueryController {

    @Resource
    private SellerExclusivePriceRemoteService sellerExclusivePriceRemoteService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiExclusivePriceListResp>> pageList(@RequestBody ApiExclusivePriceQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            ExclusivePriceQueryReq exclusivePriceQueryReq = JsonUtil.copy(req, ExclusivePriceQueryReq.class);
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            exclusivePriceQueryReq.setShopId(loginShopDto.getShopId());
            BasePageResp<ExclusivePriceSimpleResp> basePageResp = sellerExclusivePriceRemoteService.pageList(exclusivePriceQueryReq);
            return PageResultHelper.transfer(basePageResp, ApiExclusivePriceListResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiExclusivePriceResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            ExclusivePriceResp priceResp = sellerExclusivePriceRemoteService.getById(req);
            return JsonUtil.copy(priceResp, ApiExclusivePriceResp.class);
        });
    }
}
