package com.sankuai.shangou.seashop.m.core.service.user.shop.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiShopInvoiceService;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopInvoiceCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopInvoiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MApiShopInvoiceServiceImpl implements MApiShopInvoiceService {
    @Resource
    private ShopInvoiceQueryFeign shopInvoiceQueryFeign;
    @Resource
    private ShopInvoiceCmdFeign shopInvoiceCmdFeign;

    @Override
    public QueryShopInvoiceResp querySelfShopInvoice() {
        return ThriftResponseHelper.executeThriftCall(() -> shopInvoiceQueryFeign.querySelfShopInvoice());
    }

    @Override
    public BaseResp saveShopInvoice(SaveShopInvoiceReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> shopInvoiceCmdFeign.saveSelfShopInvoice(req));
    }
}
