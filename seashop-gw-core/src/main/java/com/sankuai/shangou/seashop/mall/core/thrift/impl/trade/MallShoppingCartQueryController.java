package com.sankuai.shangou.seashop.mall.core.thrift.impl.trade;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiOrderAdditionalDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShoppingCartShopProductDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiPreviewOrderReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiPreviewBuyNowReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiPreviewCollocationOrderReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiPreviewFlashSaleOrderReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiPreviewOrderResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.trade.ApiUserShoppingCartResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.trade.thrift.core.ShoppingCartQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewBuyNowReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewCollocationOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewFlashSaleOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.UserShoppingCartResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/shoppingCart")
@Slf4j
public class MallShoppingCartQueryController {

    @Resource
    private ShoppingCartQueryFeign shoppingCartQueryFeign;

    @NeedLogin(force = false)
    @GetMapping(value = "/getUserShoppingCartList")
    public ResultDto<ApiUserShoppingCartResp> getUserShoppingCartList() throws TException {
        
        log.info("【购物车】获取用户购物车列表, userInfo={}", JsonUtil.toJsonString(TracerUtil.getMemberDto()));
        if (null == TracerUtil.getMemberDto()) {
            // 未登录时返回默认数据
            return ResultDto.newWithData(ApiUserShoppingCartResp.defaultEmpty());
        }
        Long loginUserId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("【购物车】获取用户购物车列表", loginUserId, req -> {
            UserShoppingCartResp resp = ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.getUserShoppingCartList(loginUserId));
            ApiUserShoppingCartResp apiUserShoppingCartResp = JsonUtil.copy(resp, ApiUserShoppingCartResp.class);
            BigDecimal totalDiscountAmount = BigDecimal.ZERO;
            if (null != apiUserShoppingCartResp && CollUtil.isNotEmpty(apiUserShoppingCartResp.getShopProductList())) {
                List<ApiShoppingCartShopProductDto> shopProductList = apiUserShoppingCartResp.getShopProductList();
                for (ApiShoppingCartShopProductDto shopProductDto : shopProductList) {
                    ApiOrderAdditionalDto additional = shopProductDto.getAdditional();
                    if (null != additional) {
                        BigDecimal discountAmount = null != additional.getDiscountAmount() ? additional.getDiscountAmount() : BigDecimal.ZERO;
                        BigDecimal couponAmount = null != additional.getCouponAmount() ? additional.getCouponAmount() : BigDecimal.ZERO;
                        BigDecimal reductionAmount = null != additional.getReductionAmount() ? additional.getReductionAmount() : BigDecimal.ZERO;
                        totalDiscountAmount = totalDiscountAmount.add(discountAmount).add(couponAmount).add(reductionAmount);
                    }
                }
            }
            apiUserShoppingCartResp.setTotalDiscountAmount(totalDiscountAmount);
            return apiUserShoppingCartResp;
        });
    }

    @NeedLogin(force = false)
    @GetMapping(value = "/getUserShoppingCartCount")
    public ResultDto<Integer> getUserShoppingCartCount() throws TException {
        
        log.info("【购物车】获取用户购物车sku数量, userInfo={}", JsonUtil.toJsonString(TracerUtil.getMemberDto()));
        if (null == TracerUtil.getMemberDto()) {
            // 未登录时返回默认数据
            return ResultDto.newWithData(0);
        }
        Long loginUserId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("【购物车】获取用户购物车sku数量", loginUserId,
            func -> ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.getUserShoppingCartCount(loginUserId))
        );
    }

    @NeedLogin
    @PostMapping(value = "/previewOrder", consumes = "application/json")
    public ResultDto<ApiPreviewOrderResp> previewOrder(@RequestBody ApiPreviewOrderReq previewOrderReq) throws TException {
        
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("【购物车】获取订单预览信息", previewOrderReq, func -> {
            PreviewOrderReq req = JsonUtil.copy(previewOrderReq, PreviewOrderReq.class);
            req.setUserId(userId);

            PreviewOrderResp resp = ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.previewOrder(req));
            return JsonUtil.copy(resp, ApiPreviewOrderResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/previewFlashSaleOrder")
    public ResultDto<ApiPreviewOrderResp> previewFlashSaleOrder(@RequestBody ApiPreviewFlashSaleOrderReq previewOrderReq) throws TException {
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("【购物车】获取限时购订单预览信息", previewOrderReq, func -> {
            PreviewFlashSaleOrderReq req = JsonUtil.copy(previewOrderReq, PreviewFlashSaleOrderReq.class);
            req.setUserId(userId);

            PreviewOrderResp resp = ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.previewFlashSaleOrder(req));
            return JsonUtil.copy(resp, ApiPreviewOrderResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/previewCollocationOrder")
    public ResultDto<ApiPreviewOrderResp> previewCollocationOrder(@RequestBody ApiPreviewCollocationOrderReq previewOrderReq) throws TException {
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("【购物车】获取组合购订单预览信息", previewOrderReq, func -> {
            PreviewCollocationOrderReq req = JsonUtil.copy(previewOrderReq, PreviewCollocationOrderReq.class);
            req.setUserId(userId);

            PreviewOrderResp resp = ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.previewCollocationOrder(req));
            return JsonUtil.copy(resp, ApiPreviewOrderResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/previewBuyNowOrder")
    public ResultDto<ApiPreviewOrderResp> previewBuyNowOrder(@RequestBody ApiPreviewBuyNowReq previewOrderReq) throws TException {
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("【购物车】获取立即购买订单预览信息", previewOrderReq, func -> {
            previewOrderReq.setUserId(userId);
            // 业务逻辑处理
            PreviewOrderResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    shoppingCartQueryFeign.previewBuyNowOrder(JsonUtil.copy(previewOrderReq, PreviewBuyNowReq.class)));
            return JsonUtil.copy(resp, ApiPreviewOrderResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/checkCanSubmit")
    public ResultDto<BaseResp> checkCanSubmit(@RequestBody ApiPreviewOrderReq previewOrderReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【购物车】校验购物车是否可以提交", previewOrderReq, func -> {
            PreviewOrderReq req = JsonUtil.copy(previewOrderReq, PreviewOrderReq.class);
            req.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> shoppingCartQueryFeign.checkCanSubmit(req));
        });
    }


}
