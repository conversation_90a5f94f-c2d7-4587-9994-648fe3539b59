package com.sankuai.shangou.seashop.seller.core.thrift.impl.impl;

import javax.annotation.Resource;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.gw.common.thrift.requests.ApiRefreshTokenReq;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.core.commmon.auth.ShopAuthenticationHandler;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMenuResp;
import com.sankuai.shangou.seashop.user.thrift.account.ManagerQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.dto.MenuDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMenuAuthReq;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerManagerRemoteService;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiManagerService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiCmdManagerReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryManagerPageReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryManagerReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.EpManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description: 管理员服务实现类
 * @author: LXH
 **/
@Service
@Slf4j
public class SellerSellerApiManagerServiceImpl implements SellerApiManagerService {
    @Resource
    private SellerManagerRemoteService sellerManagerRemoteService;
    @Resource
    private ShopAuthenticationHandler shopAuthenticationHandler;

    @Resource
    private ManagerQueryFeign managerQueryFeign;

    @Override
    public EpManagerResp queryManager(Integer id) {
        return sellerManagerRemoteService.queryManager(id);
    }

    @Override
    public BasePageResp<ApiManagerResp> queryManagerPage(ApiQueryManagerPageReq queryManagerPageReq) {
        BasePageResp<ManagerResp> managerRespPage = sellerManagerRemoteService.queryManagerPage(JsonUtil.copy(queryManagerPageReq, QueryManagerPageReq.class));
        return PageResultHelper.transfer(managerRespPage, managerResp -> JsonUtil.copy(managerResp, ApiManagerResp.class));
    }

    @Override
    public ApiManagerResp queryManager(ApiQueryManagerReq queryManagerPageReq) {
        ManagerResp managerResp = sellerManagerRemoteService.queryManager(JsonUtil.copy(queryManagerPageReq, QueryManagerReq.class));
        return JsonUtil.copy(managerResp, ApiManagerResp.class);
    }

    @Override
    public Long addManager(ApiCmdManagerReq cmdManagerReq) {
        return sellerManagerRemoteService.addManager(JsonUtil.copy(cmdManagerReq, CmdManagerReq.class));
    }

    @Override
    public Long editManager(ApiCmdManagerReq cmdManagerReq) {
        return sellerManagerRemoteService.editManager(JsonUtil.copy(cmdManagerReq, CmdManagerReq.class));
    }

    @Override
    public Long deleteManager(ApiCmdManagerReq cmdManagerReq) {
        return sellerManagerRemoteService.deleteManager(JsonUtil.copy(cmdManagerReq, CmdManagerReq.class));
    }

    @Override
    public Integer batchDeleteManager(ApiCmdManagerReq cmdManagerReq) {
        return sellerManagerRemoteService.batchDeleteManager(JsonUtil.copy(cmdManagerReq, CmdManagerReq.class));
    }

    @Override
    public LoginResp login(ApiLoginReq loginReq) {
        return sellerManagerRemoteService.login(loginReq);
    }

    @Override
    public LoginResp refreshToken(ApiRefreshTokenReq req) {
        return sellerManagerRemoteService.refreshToken(req);
    }

    @Override
    public Boolean logout() {
        return sellerManagerRemoteService.logout(shopAuthenticationHandler.getTokenCache());
    }

    @Override
    public List<ApiMenuResp> queryMenuAuth(QueryMenuAuthReq authReq) {
        List<MenuDto> menuList = ThriftResponseHelper.executeThriftCall(() -> managerQueryFeign.queryMenuAuth(authReq));
        return JsonUtil.copyList(menuList, ApiMenuResp.class);
    }


}
