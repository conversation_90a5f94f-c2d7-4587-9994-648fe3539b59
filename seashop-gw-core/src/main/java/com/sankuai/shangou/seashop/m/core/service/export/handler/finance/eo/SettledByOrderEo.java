package com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2024/3/26/026
 * @description:
 */
@Getter
@Setter
public class SettledByOrderEo {

    @ExcelProperty(value = "店铺id")
    private Long shopId;

    @ExcelProperty(value = "店铺名称")
    private String shopName;

    @ExcelProperty(value = "订单ID")
    private String orderId;

    @ExcelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ExcelProperty(value = "佣金金额")
    private BigDecimal commissionAmount;

    @ExcelProperty(value = "结算金额")
    private BigDecimal settleAmount;

    @ExcelProperty(value = "手续费金额")
    private BigDecimal channelAmount;

    @ExcelProperty(value = "结算时间")
    private Date settleTime;

    @ExcelProperty(value = "付款时间")
    @ExcelIgnore
    private Date payTime;

    @ExcelProperty(value = "订单完成时间")
    @ExcelIgnore
    private Date orderFinishTime;

    @ExcelProperty(value = "支付方式：1：支付宝扫码；2：支付宝H5；3：微信小程序；4：微信H5；5：企业网银；6：个人网银")
    @ExcelIgnore
    private Integer paymentType;

    @ExcelProperty(value = "支付方式名称")
    @ExcelIgnore
    private String paymentTypeName;
}
