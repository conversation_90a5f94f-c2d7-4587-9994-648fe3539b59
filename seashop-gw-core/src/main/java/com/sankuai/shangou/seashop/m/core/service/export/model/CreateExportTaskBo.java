package com.sankuai.shangou.seashop.m.core.service.export.model;

import com.sankuai.shangou.seashop.base.export.enums.TaskType;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class CreateExportTaskBo {

    /**
     * 查询条件。原对象传过来就行
     */
    private Object executeParam;
    /**
     * 业务类型
     */
    private TaskType taskType;
    /**
     * 操作者ID
     */
    private Long operatorId;
    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 自定义任务名称
     */
    private String customTaskName;

}
