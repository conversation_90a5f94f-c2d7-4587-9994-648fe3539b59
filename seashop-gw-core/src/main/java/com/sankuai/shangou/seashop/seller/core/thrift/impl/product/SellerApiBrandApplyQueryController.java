package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.product.thrift.core.BrandApplyQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.CheckBrandNameReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.BrandApplyDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.CheckBrandNameResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandApplyDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiCheckBrandNameReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryBrandApplyReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiBrandApplyDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiCheckBrandNameResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiBrandApplyDto;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/sellerApi/apiBrandApply")
public class SellerApiBrandApplyQueryController {

    @Resource
    private BrandApplyQueryFeign brandApplyQueryFeign;

    @PostMapping(value = "/queryBrandApplyForPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiBrandApplyDto>> queryBrandApplyForPage(@RequestBody ApiQueryBrandApplyReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryBrandApplyForPage", request, req -> {

            QueryBrandApplyReq pageReq = JsonUtil.copy(req, QueryBrandApplyReq.class);
            pageReq.setAuditStatus(BrandEnum.AuditStatusEnum.getByCode(req.getAuditStatusCode()));
            pageReq.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<BrandApplyDto> pageResp = ThriftResponseHelper.executeThriftCall(() -> brandApplyQueryFeign.queryBrandApplyForPage(pageReq));
            return PageResultHelper.transfer(pageResp, ApiBrandApplyDto.class);
        });
    }

    @PostMapping(value = "/queryBrandApplyDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiBrandApplyDetailResp> queryBrandApplyDetail(@RequestBody BaseIdReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryBrandApplyDetail", request, req -> {
            req.checkParameter();
            QueryBrandApplyDetailReq queryReq = QueryBrandApplyDetailReq.builder().id(req.getId()).shopId(TracerUtil.getShopDto().getShopId()).build();
            BrandApplyDetailResp detail = ThriftResponseHelper.executeThriftCall(() -> brandApplyQueryFeign.queryBrandApplyDetailForSeller(queryReq));
            return JsonUtil.copy(detail, ApiBrandApplyDetailResp.class);
        });
    }

    @PostMapping(value = "/checkBrandName", consumes = "application/json")
    public ResultDto<ApiCheckBrandNameResp> checkBrandName(@RequestBody ApiCheckBrandNameReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("checkBrandName", request, req -> {
            CheckBrandNameReq checkBrandNameReq = JsonUtil.copy(req, CheckBrandNameReq.class);
            checkBrandNameReq.checkParameter();
            CheckBrandNameResp checkBrandNameResp = ThriftResponseHelper.executeThriftCall(() ->
                    brandApplyQueryFeign.checkBrandName(checkBrandNameReq));
            return JsonUtil.copy(checkBrandNameResp, ApiCheckBrandNameResp.class);
        });
    }
}
