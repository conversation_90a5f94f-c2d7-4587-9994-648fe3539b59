package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.handler.AbstractCustomDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.CustomDataGetter;
import com.sankuai.shangou.seashop.m.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.context.ExclusivePriceExportContext;
import com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo.ExclusivePriceEo;
import com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo.ExclusivePriceProductEo;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceDetailResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceQueryFeign;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * @author: lhx
 * @date: 2024/1/19/019
 * @description:
 */
@Service
@Slf4j
public class MExclusivePriceExportGetter extends AbstractCustomDataGetter<ApiExclusivePriceQueryReq>
    implements CustomDataGetter<ApiExclusivePriceQueryReq> {

    @Resource
    private ExclusivePriceQueryFeign exclusivePriceQueryFeign;

    @Override
    public Integer getModule() {
        return ExportTaskType.EXCLUSIVE_PRICE_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.EXCLUSIVE_PRICE_LIST.getName();
    }

    @Override
    public ByteArrayOutputStream writeToOutputStream(ApiExclusivePriceQueryReq param) {
        param.setPageNo(CommonConstant.INIT_PAGE_NO);
        param.setPageSize(1);
        ExclusivePriceQueryReq exclusivePriceQueryReq = JsonUtil.copy(param, ExclusivePriceQueryReq.class);
        log.info("专享价导出参数:{}", exclusivePriceQueryReq);
        BasePageResp<ExclusivePriceDetailResp> detailPageResp = ThriftResponseHelper.executeThriftCall(() -> exclusivePriceQueryFeign.detailPageList(exclusivePriceQueryReq));
        log.info("专享价导出结果数:{}", detailPageResp.getTotalCount());
        if (null == detailPageResp || CollUtil.isEmpty(detailPageResp.getData())) {
            return null;
        }

        // 初始化上下文
        ExclusivePriceExportContext context = initContext();
        writeToExcel(context, JsonUtil.copyList(detailPageResp.getData(), ExclusivePriceEo.class));

        Long startTime = System.currentTimeMillis();
        // Integer pages = detailPageResp.getPages();
        Integer pages = 2;
        if (pages > CommonConstant.INIT_PAGE_NO) {
            for (int i = CommonConstant.LOOP_START_PAGE_NO; i <= pages; i++) {
                log.info("专享价导出第{}页", i);
                param.setPageNo(i);
                BasePageResp<ExclusivePriceDetailResp> detailPageResp1 = ThriftResponseHelper.executeThriftCall(() ->
                        exclusivePriceQueryFeign.detailPageList(JsonUtil.copy(param, ExclusivePriceQueryReq.class)));
                writeToExcel(context, JsonUtil.copyList(detailPageResp1.getData(), ExclusivePriceEo.class));
            }
        }

        log.info("数据写入完成, 总耗时: {}ms", System.currentTimeMillis() - startTime);
        Workbook workbook = context.getWorkbook();
        // 自适应列
        autoSizeColumn(context);
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            return outputStream;
        }
        catch (IOException e) {
            log.info("专享价导出异常", e);
        }
        return null;
    }

    private void autoSizeColumn(ExclusivePriceExportContext context) {
        Sheet sheet = context.getSheet();
        Map<Integer, Integer> columnWidthMap = context.getColumnWidthMap();
        columnWidthMap.forEach((k, v) -> {
            sheet.setColumnWidth(k, v);
        });
    }

    @SneakyThrows
    private ExclusivePriceExportContext initContext() {
        Workbook workbook = WorkbookFactory.create(true);
        Sheet sheet = workbook.createSheet("专享价");
        int rowNum = 0;

        // 创建一个样式对象
        CellStyle titleStyle = workbook.createCellStyle();
        // 设置样式：加粗、居中
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);

        CellStyle style = workbook.createCellStyle();
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 添加对象 A 的标题
        Row aTitleRow = sheet.createRow(rowNum++);

        aTitleRow.createCell(0).setCellValue("活动名称");
        aTitleRow.createCell(1).setCellValue("店铺ID");
        aTitleRow.createCell(2).setCellValue("店铺名称");
        aTitleRow.createCell(3).setCellValue("活动时间");
        aTitleRow.createCell(4).setCellValue("参与活动商品");

        for (int i = 0; i < 5; i++) {
            aTitleRow.getCell(i).setCellStyle(titleStyle);
        }

        // 处理 “参与活动商品”
        CellRangeAddress region = new CellRangeAddress(0, 0, 4, 11);
        sheet.addMergedRegionUnsafe(region);

        // 构建导出上下文对象
        ExclusivePriceExportContext context = new ExclusivePriceExportContext();
        context.setWorkbook(workbook);
        context.setSheet(sheet);
        context.setTitleStyle(titleStyle);
        context.setStyle(style);
        context.setCellStyle(cellStyle);
        context.setRowNum(rowNum);
        return context;
    }

    @SneakyThrows
    public void writeToExcel(ExclusivePriceExportContext context, List<ExclusivePriceEo> dataList) {
        int rowNum = context.getRowNum();

        Map<Integer, Integer> cellMap = new TreeMap<>();

        for (ExclusivePriceEo a : dataList) {

            Integer startRow = rowNum++;

            Row row = context.getSheet().createRow(startRow);
            row.createCell(0).setCellValue(a.getName());
            row.createCell(1).setCellValue(a.getShopId());
            row.createCell(2).setCellValue(a.getShopName());
            row.createCell(3).setCellValue(cn.hutool.core.date.DateUtil.formatDateTime(a.getStartTime()) + "~" + cn.hutool.core.date.DateUtil.formatDateTime(a.getEndTime()));

            for (int i = 0; i < 4; i++) {
                row.getCell(i).setCellStyle(context.getStyle());
            }

            // 添加对象 B 的标题
            row.createCell(4).setCellValue("商品ID");
            row.createCell(5).setCellValue("商品名称");
            row.createCell(6).setCellValue("规格ID");
            row.createCell(7).setCellValue("规格名称");
            row.createCell(8).setCellValue("商城价");
            row.createCell(9).setCellValue("商家ID");
            row.createCell(10).setCellValue("商家账号");
            row.createCell(11).setCellValue("专享价");

            for (int i = 4; i < 12; i++) {
                row.getCell(i).setCellStyle(context.getTitleStyle());
            }

            if (CollectionUtils.isEmpty(a.getProductList())) {
                a.setProductList(Collections.emptyList());
            }

            // 添加对象 B 的数据
            for (ExclusivePriceProductEo b : a.getProductList()) {
                Row bRow = context.getSheet().createRow(rowNum++);
                bRow.createCell(4).setCellValue(b.getProductId().toString());
                bRow.createCell(5).setCellValue(b.getProductName());
                bRow.createCell(6).setCellValue(null != b.getSkuAutoId() ? b.getSkuAutoId().toString() : "");
                bRow.createCell(7).setCellValue(b.getSkuName());
                bRow.createCell(8).setCellValue(null != b.getMallPrice() ? b.getMallPrice().toString() : "");
                bRow.createCell(9).setCellValue(b.getMemberId());
                bRow.createCell(10).setCellValue(b.getUserName());
                bRow.createCell(11).setCellValue(null != b.getPrice() ? b.getPrice().toString() : "");

                for (int i = 4; i < 12; i++) {
                    bRow.getCell(i).setCellStyle(context.getCellStyle());
                    Map<Integer, Integer> columnWidthMap = context.getColumnWidthMap();
                    Integer columnWidth = columnWidthMap.getOrDefault(i, 256);
                    columnWidthMap.put(i, Math.max(columnWidth, getAutoWidth(bRow.getCell(i))));
                }

            }

            Integer endRow = rowNum - 1;
            cellMap.put(startRow, endRow);
        }

        cellMap.forEach((startRow, endRow) -> {
            if (!startRow.equals(endRow)) {
                CellRangeAddress region0 = new CellRangeAddress(startRow, endRow, 0, 0);
                context.getSheet().addMergedRegionUnsafe(region0);
                CellRangeAddress region1 = new CellRangeAddress(startRow, endRow, 1, 1);
                context.getSheet().addMergedRegionUnsafe(region1);
                CellRangeAddress region2 = new CellRangeAddress(startRow, endRow, 2, 2);
                context.getSheet().addMergedRegionUnsafe(region2);
                CellRangeAddress region3 = new CellRangeAddress(startRow, endRow, 3, 3);
                context.getSheet().addMergedRegionUnsafe(region3);
            }
        });

        context.setRowNum(rowNum);
    }

    private Integer getAutoWidth(Cell cell) {
        String cellValue = "";
        if (cell != null) {
            switch (cell.getCellType()) {
                case STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    cellValue = NumberToTextConverter.toText(cell.getNumericCellValue());
                    break;
                case BOOLEAN:
                    cellValue = Boolean.toString(cell.getBooleanCellValue());
                    break;
                default:
                    break;
            }
        }
        return (cellValue.length() + 1) * 256;
    }
}
