package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.product.MProductAuditRemoteService;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.ProductAuditReq;

/**
 * <AUTHOR>
 * @date 2023/12/21 15:11
 */
@RestController
@RequestMapping("/mApi/apiProductAudit")
public class MApiProductAuditCmdController {

    @Resource
    private MProductAuditRemoteService mProductAuditRemoteService;

    @PostMapping(value = "/batchAuditProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> batchAuditProduct(@RequestBody ApiProductAuditReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchAuditProduct", request, req -> {

            ProductAuditReq remoteReq = JsonUtil.copy(req, ProductAuditReq.class);
            return mProductAuditRemoteService.batchAuditProduct(remoteReq);
        });
    }
}
