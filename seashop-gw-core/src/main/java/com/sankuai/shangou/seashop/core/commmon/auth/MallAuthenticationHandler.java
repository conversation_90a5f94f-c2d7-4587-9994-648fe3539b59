package com.sankuai.shangou.seashop.core.commmon.auth;


import com.sankuai.shangou.seashop.base.boot.constant.LoginConstant;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.security.cache.storage.TokenRedisStorage;
import com.sankuai.shangou.seashop.base.security.config.LoginSecurityConfig;
import com.sankuai.shangou.seashop.base.security.handler.AbstractAuthenticationHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * @description: 鉴权
 * @author: cdd
 **/

@Component
public class MallAuthenticationHandler extends AbstractAuthenticationHandler {
    private static final Logger log = LoggerFactory.getLogger(MallAuthenticationHandler.class);

    @Resource
    private TokenRedisStorage storage;


    @PostConstruct
    public void init() {
        //修改token的key
    }

    @Override
    public RoleEnum getRoleEnum() {
        return RoleEnum.MEMBER;
    }

    @Override
    public String getTokenKey() {
        return LoginSecurityConfig.TOKEN_NAME;
    }

    @Override
    public LoginBaseDto tokenCacheToLoginInfo(TokenCache tokenCache) {
        if (Objects.equals(LoginConstant.KICKED, tokenCache.getKick())) {
            throw new LoginException(LoginErrorEnum.KICKED);
        }
        RoleEnum userType = RoleEnum.nameOf(tokenCache.getUserType());
        Object userJson = storage.getObject(LoginCacheKey.getUserKey(userType, tokenCache.getUserId()));
        return JsonUtil.parseObject(JsonUtil.toJsonString(userJson), LoginMemberDto.class);
    }

    @Override
    public void checkUri(NeedLogin annotation, LoginBaseDto loginDto) {
        if (!(loginDto instanceof LoginMemberDto)){
            throw new LoginException(LoginErrorEnum.FAILED);
        }
    }

    @Override
    public LoginBaseDto getAccountAndCheckToken(String tokenValue) {
        LoginBaseDto accountDto = getBaseAccountByToken(tokenValue);
        if (accountDto == null) {
            //未登录
            throw new LoginException(LoginErrorEnum.NO_LOGIN);
        }

        // 账号被注销
        if (Boolean.TRUE.equals(accountDto.getWhetherDelete())) {
            throw new LoginException(LoginErrorEnum.PROHIBIT_LOGIN.getCode(), "用户已注销");
        }

        // 账号被冻结
        if (accountDto.getDisable()) {
            throw new LoginException(LoginErrorEnum.ACCOUNT_FROZEN);
        }
        return accountDto;
    }
}
