package com.sankuai.shangou.seashop.m.core.task.export;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
// 调整端口号，避免端口冲突
public class ExportFailTask {

    @Resource
    private MExportTaskBiz exportTaskBiz;

    /**
     * 导出任务补偿检查
     * 每5分钟：0 0/5 * * * ?
     */
//    @Crane("ExportTaskFailCheckTask")
    public void exportTaskFailCheckTask() {
        log.info("【定时任务】【导出任务补偿检查】...start...");
        exportTaskBiz.checkAndRedoIfNecessary();
    }

}
