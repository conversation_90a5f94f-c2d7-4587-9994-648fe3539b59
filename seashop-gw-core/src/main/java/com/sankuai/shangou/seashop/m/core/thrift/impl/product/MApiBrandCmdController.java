package com.sankuai.shangou.seashop.m.core.thrift.impl.product;


import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiCreateBrandReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiDeleteBrandReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiUpdateBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.BrandCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.CreateBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.DeleteBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.UpdateBrandReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/mApi/apiBrand")
public class MApiBrandCmdController {

    @Resource
    private BrandCmdFeign brandCmdFeign;

    @PostMapping(value = "/createBrand", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> createBrand(@RequestBody ApiCreateBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createBrand", request, req -> {
            req.checkParameter();

            return ThriftResponseHelper.executeThriftCall(() -> brandCmdFeign.createBrand(JsonUtil.copy(req, CreateBrandReq.class)));
        });
    }

    @PostMapping(value = "/updateBrand", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updateBrand(@RequestBody ApiUpdateBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateBrand", request, req -> {
            req.checkParameter();

            return ThriftResponseHelper.executeThriftCall(() -> brandCmdFeign.updateBrand(JsonUtil.copy(req, UpdateBrandReq.class)));
        });
    }

    @PostMapping(value = "/deleteBrand", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deleteBrand(@RequestBody ApiDeleteBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteBrand", request, req -> {
            req.checkParameter();

            return ThriftResponseHelper.executeThriftCall(() -> brandCmdFeign.deleteBrand(JsonUtil.copy(req, DeleteBrandReq.class)));
        });
    }
}
