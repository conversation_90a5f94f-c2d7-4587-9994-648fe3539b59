package com.sankuai.shangou.seashop.seller.core.service.common.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.common.SellerHomeService;
import com.sankuai.shangou.seashop.seller.thrift.core.response.common.RadiusNumResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/04/28 14:09
 */
@Service
@Slf4j
public class SellerHomeServiceImpl implements SellerHomeService {

    @Resource
    private SellerBusinessCategoryRemoteService sellerBusinessCategoryRemoteService;

    @Override
    public RadiusNumResp queryRadiusNum(Long shopId) {
        RadiusNumResp resp = new RadiusNumResp();
        resp.setApplyWaitFinishNum(sellerBusinessCategoryRemoteService.queryWaitFinishContractNum(shopId));
        return resp;
    }
}
