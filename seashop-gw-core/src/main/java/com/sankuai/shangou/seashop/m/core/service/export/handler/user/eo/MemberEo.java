package com.sankuai.shangou.seashop.m.core.service.export.handler.user.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class MemberEo {
    @ExcelProperty("商家名称")
    private String userName;
    @ExcelProperty("真实姓名")
    private String realName;
    @ExcelProperty("qq")
    private String qq;
    @ExcelProperty("邮箱")
    private String email;
    @ExcelProperty("微信昵称")
    private String nick;
    //    @ExcelProperty("等级")
//    private String level;
//    @ExcelProperty("积分")
//    private Integer points;
    @ExcelProperty("净消费")
    private BigDecimal netAmount;
    @ExcelProperty("最后登录时间")
    private Date lastLoginDate;
    @ExcelProperty("手机")
    private String cellPhone;
    @ExcelProperty("创建日期")
    private Date createTime;
    // 注解 @ExcelIgnore 忽略该字段
    @ExcelIgnore
    private Boolean disabled;
    @ExcelProperty("状态")
    private String status;
    @ExcelProperty("标签")
    private String label;

}
