package com.sankuai.shangou.seashop.m.core.service.export.handler.finance.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo.PendSettleListTotalEo;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiPendingSettlementService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiPendingSettlementQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPendingSettlementResp;

import cn.hutool.core.collection.CollUtil;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
public class PendSettleListTotalWrapper extends PageExportWrapper<PendSettleListTotalEo, ApiPendingSettlementQryReq> {

    private final MApiPendingSettlementService mApiPendingSettlementService;

    public PendSettleListTotalWrapper(MApiPendingSettlementService mApiPendingSettlementService) {
        this.mApiPendingSettlementService = mApiPendingSettlementService;
    }

    @Override
    public List<PendSettleListTotalEo> getPageList(ApiPendingSettlementQryReq param) {
        BasePageResp<ApiPendingSettlementResp> pendingSettlementList = mApiPendingSettlementService.getPendingSettlementList(param);
        if (null == pendingSettlementList || CollUtil.isEmpty(pendingSettlementList.getData())) {
            return null;
        }
        List<PendSettleListTotalEo> pendSettleListTotalEos = CollUtil.newArrayList();
        pendingSettlementList.getData().forEach(p -> {
            PendSettleListTotalEo pendSettleListTotalEo = JsonUtil.copy(p, PendSettleListTotalEo.class);
            pendSettleListTotalEo.setSettlementCycle(p.getSettlementStartTimeStr() + "--" + p.getSettlementEndTimeStr());
            pendSettleListTotalEos.add(pendSettleListTotalEo);
        });
        return pendSettleListTotalEos;
    }

    @Override
    public Integer getBatchCount() {
        return 1000;
    }
}
