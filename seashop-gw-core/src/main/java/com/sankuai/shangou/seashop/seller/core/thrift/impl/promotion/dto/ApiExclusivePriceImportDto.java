package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion.dto;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.promotion.ApiMemberProductDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@TypeDoc(description = "专享价导入响应体")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
@ToString
public class ApiExclusivePriceImportDto {

    @FieldDoc(description = "专享价商品列表")
    private List<ApiMemberProductDto> productDtoList;

    @FieldDoc(description = "错误信息文件地址")
    private String errFileUrl;

    @FieldDoc(description = "成功导入数量")
    private Integer successCount;

    @FieldDoc(description = "失败导入数量")
    private Integer errorCount;

}
