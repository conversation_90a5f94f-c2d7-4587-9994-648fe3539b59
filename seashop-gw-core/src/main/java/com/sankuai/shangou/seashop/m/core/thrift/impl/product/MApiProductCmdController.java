package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.request.BaseImportReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.product.MProductRemoteService;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiProductImportReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiProductUpdateSequenceReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiProductUpdateVirtualSaleCountsReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiProductViolationReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.VirtualSaleCountsTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductImportReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateSequenceReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateVirtualSaleCountsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductViolationReq;


/**
 * <AUTHOR>
 * @date 2023/11/14 11:45
 */
@RestController
@RequestMapping("/mApi/apiProduct")
public class MApiProductCmdController {

    @Resource
    private MProductRemoteService mProductRemoteService;


    @PostMapping(value = "/batchSaveProductSequence", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> batchSaveProductSequence(@RequestBody ApiProductUpdateSequenceReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchSaveProductSequence", request, req -> {
            req.checkParameter();

            return mProductRemoteService.batchSaveProductSequence(JsonUtil.copy(req, ProductUpdateSequenceReq.class));
        });
    }

    @PostMapping(value = "/batchUpdateVirtualSales", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> batchUpdateVirtualSales(@RequestBody ApiProductUpdateVirtualSaleCountsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchUpdateVirtualSales", request, req -> {
            req.checkParameter();

            ProductUpdateVirtualSaleCountsReq remoteReq = JsonUtil.copy(req, ProductUpdateVirtualSaleCountsReq.class);
            remoteReq.setVirtualSaleCountsType(VirtualSaleCountsTypeEnum.getByCode(req.getVirtualSaleCountsTypeCode()));
            return mProductRemoteService.batchUpdateVirtualSales(remoteReq);
        });
    }

    @PostMapping(value = "/batchViolationOffSale", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> batchViolationOffSale(@RequestBody ApiProductViolationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchViolationOffSale", request, req -> {
            req.checkParameter();

            return mProductRemoteService.batchViolationOffSale(JsonUtil.copy(req, ProductViolationReq.class));
        });
    }

    @PostMapping(value = "/importViolationOffSale", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseImportResp> importViolationOffSale(@RequestBody BaseImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("importViolationOffSale", request, req -> {
            req.checkParameter();

            return mProductRemoteService.importViolationOffSale(JsonUtil.copy(req, ProductImportReq.class));
        });
    }

    @PostMapping(value = "/platformImportProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseImportResp> platformImportProduct(@RequestBody ApiProductImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("platformImportProduct", request, req -> {
            req.checkParameter();

            return mProductRemoteService.platformImportProduct(JsonUtil.copy(req, ProductImportReq.class));
        });
    }
}
