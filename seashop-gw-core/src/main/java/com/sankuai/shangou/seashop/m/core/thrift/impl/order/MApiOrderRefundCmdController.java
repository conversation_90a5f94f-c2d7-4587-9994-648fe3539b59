package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.order.MOrderRefundService;
import com.sankuai.shangou.seashop.m.thrift.core.request.order.ApiPlatformApproveBatchReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.order.ApiPlatformApproveBatchResp;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiPlatformApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.PlatformApproveBatchReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PlatformApproveBatchResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mApi/apiOrderRefund")
@Slf4j
public class MApiOrderRefundCmdController {

    @Resource
    private MOrderRefundService mOrderRefundService;

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/platformConfirm", consumes = "application/json")
    public ResultDto<BaseResp> platformConfirm(@RequestBody ApiPlatformApproveReq req) {
        return ThriftResponseHelper.responseInvoke("【售后】平台审核通过", req, func -> {
//            LoginManagerDto manager = managerInfo.getLoginDto();
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            UserDto user = new UserDto();
            user.setUserId(loginDto.getId());
            user.setUserName(loginDto.getName());
            func.setUser(user);
            PlatformApproveReq bean = JsonUtil.copy(func, PlatformApproveReq.class);
            bean.setOperationUserId(loginDto.getId());
            return mOrderRefundService.platformConfirm(bean);
        });
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/platformReject", consumes = "application/json")
    public ResultDto<BaseResp> platformReject(@RequestBody ApiPlatformApproveReq req) {
        return ThriftResponseHelper.responseInvoke("【售后】平台驳回", req, func -> {
//            LoginManagerDto manager = managerInfo.getLoginDto();
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            UserDto user = new UserDto();
            user.setUserId(loginDto.getId());
            user.setUserName(loginDto.getName());
            func.setUser(user);
            PlatformApproveReq bean = JsonUtil.copy(func, PlatformApproveReq.class);
            bean.setOperationUserId(loginDto.getId());
            return mOrderRefundService.platformReject(bean);
        });
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/platformBatchConfirm", consumes = "application/json")
    public ResultDto<ApiPlatformApproveBatchResp> platformBatchConfirm(@RequestBody ApiPlatformApproveBatchReq batchReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】平台批量审核通过", batchReq, func -> {
            batchReq.checkParameter();
            LoginManagerDto manager = TracerUtil.getManagerDto();

            UserDto user = new UserDto();
            user.setUserId(manager.getId());
            user.setUserName(manager.getName());

            PlatformApproveBatchReq bean = JsonUtil.copy(batchReq, PlatformApproveBatchReq.class);
            bean.setOperationUserId(manager.getId());
            bean.setUser(user);
            // 业务逻辑处理
            PlatformApproveBatchResp resp = mOrderRefundService.platformBatchConfirm(bean);
            return JsonUtil.copy(resp, ApiPlatformApproveBatchResp.class);
        });
    }
}
