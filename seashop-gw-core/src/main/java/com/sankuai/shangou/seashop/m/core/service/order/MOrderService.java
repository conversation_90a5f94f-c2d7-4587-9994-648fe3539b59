package com.sankuai.shangou.seashop.m.core.service.order;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiQueryPlatformOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderOperationLogResp;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.PlatformIndexTradeDataStatsResp;

/**
 * <AUTHOR>
 * @date 2024/01/02 11:58
 */
public interface MOrderService {

    void exportOrderInvoice(ApiQueryPlatformOrderReq request);

    /**
     * 导出订单
     *
     * @param queryReq 查询条件
     * @param user     用户信息
     */
    void export(ApiQueryPlatformOrderReq queryReq, UserDto user);

    /**
     * 平台分页查询订单列表
     *
     * @param queryReq 查询条件
     * @return 订单列表
     */
    BasePageResp<OrderInfoDto> pageQueryPlatformOrder(QueryPlatformOrderReq queryReq);

    /**
     * 平台查询订单详情
     *
     * @param queryReq 查询条件
     * @return 订单详情
     */
    OrderDetailResp queryDetail(QueryOrderDetailReq queryReq);

    /**
     * 查询订单操作日志
     *
     * @param orderId 订单id
     * @return 订单操作日志
     */
    List<OrderOperationLogResp> queryOrderOperationLog(String orderId);

    /**
     * 平台首页交易数据统计
     *
     * @return 平台首页交易数据统计
     */
    PlatformIndexTradeDataStatsResp statsPlatformIndexTradeData();

}
