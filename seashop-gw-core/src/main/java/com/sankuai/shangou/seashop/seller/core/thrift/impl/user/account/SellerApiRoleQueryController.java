package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.account;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiRoleService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryRoleReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiRoleRespList;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/sellerApi/apiRole")
public class SellerApiRoleQueryController {
    @Resource
    private SellerApiRoleService roleService;

    @PostMapping(value = "/queryRoleList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiRoleRespList> queryRoleList(@RequestBody ApiQueryRoleReq queryRoleReq) {
        

        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        queryRoleReq.setShopId(loginShopDto.getShopId());
        return ThriftResponseHelper.responseInvoke("queryRoleList", queryRoleReq, req -> roleService.queryRoleList(queryRoleReq));
    }
}
