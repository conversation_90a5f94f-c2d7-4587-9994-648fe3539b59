package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.remote.product.MProductAuditRemoteService;
import com.sankuai.shangou.seashop.m.core.service.product.MProductAuditService;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiProductQueryDetailReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryProductAuditReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiProductAuditDetailResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiProductAuditPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductAuditQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.QueryProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditPageResp;

import cn.hutool.core.date.DateUtil;

/**
 * <AUTHOR>
 * @date 2023/12/21 15:04
 */
@RestController
@RequestMapping("/mApi/apiProductAudit")
public class MApiProductAuditQueryController {

    @Resource
    private MProductAuditService mProductAuditService;
    @Resource
    private MProductAuditRemoteService mProductAuditRemoteService;

    private static final String[] IGNORE_FIELDS = new String[]{"auditStatus"};

    @PostMapping(value = "/queryProductAudit", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiProductAuditPageResp>> queryProductAudit(@RequestBody ApiQueryProductAuditReq request) throws TException {

        Integer auditStatus = request.getAuditStatusCode();
        request.setAuditStatus(auditStatus);

        return ThriftResponseHelper.responseInvoke("queryProductAudit", request, req -> {
            req.checkParameter();

            dealSearchTime(req);
            QueryProductAuditReq remoteReq = JsonUtil.copy(req, QueryProductAuditReq.class, IGNORE_FIELDS);
            remoteReq.setAuditStatus(ProductEnum.AuditStatusEnum.getByCode(req.getAuditStatus()));
            BasePageResp<ProductAuditPageResp> resp = mProductAuditRemoteService.queryProductAudit(remoteReq);
            return PageResultHelper.transfer(resp, ApiProductAuditPageResp.class);
        });
    }

    @PostMapping(value = "/queryProductAuditDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiProductAuditDetailResp> queryProductAuditDetail(@RequestBody ApiProductQueryDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductDetail", request, req -> {
            req.checkParameter();

            ProductAuditQueryDetailReq remoteReq = JsonUtil.copy(req, ProductAuditQueryDetailReq.class);
            remoteReq.setNeedOriginProduct(Boolean.TRUE);
            ApiProductAuditDetailResp resp = mProductAuditRemoteService.queryProductAuditDetail(remoteReq);
            return resp;
        });
    }

    @PostMapping(value = "/exportProductAuditRefuse", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportProductAuditRefuse(@RequestBody ApiQueryProductAuditReq request) throws TException {
        dealSearchTime(request);
        QueryProductAuditReq remoteReq = JsonUtil.copy(request, QueryProductAuditReq.class, IGNORE_FIELDS);
        remoteReq.setAuditStatus(ProductEnum.AuditStatusEnum.NOT_PASS);
        mProductAuditService.exportProductAuditRefuse(request);
        return ResultDto.newWithData(BaseResp.of());
    }

    private void dealSearchTime(ApiQueryProductAuditReq req) {
        if (req.getStartTime() != null) {
            req.setStartTime(DateUtil.beginOfDay(req.getStartTime()));
        }
        if (req.getEndTime() != null) {
            req.setEndTime(DateUtil.endOfDay(req.getEndTime()));
        }
    }
}
