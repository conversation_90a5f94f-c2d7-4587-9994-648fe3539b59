package com.sankuai.shangou.seashop.m.core.service.export.handler.user.wrapper;

import java.util.Collections;
import java.util.List;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.common.remote.user.MApiMemberRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.user.eo.MemberEo;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryMemberPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMemberResp;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 */
public class MemberDataWrapper extends PageExportWrapper<MemberEo, ApiQueryMemberPageReq> {

    private final MApiMemberRemoteService MApiMemberRemoteService;

    public MemberDataWrapper(MApiMemberRemoteService MApiMemberRemoteService) {
        this.MApiMemberRemoteService = MApiMemberRemoteService;
    }


    @Override
    public List<MemberEo> getPageList(ApiQueryMemberPageReq param) {
        List<ApiMemberResp> list = MApiMemberRemoteService.queryMemberPageAndLabel(param).getData();
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<MemberEo> memberEoList = BeanUtil.copyToList(list, MemberEo.class);

        memberEoList.forEach(eo -> eo.setStatus(eo.getDisabled() ? "禁用" : "正常"));
        return memberEoList;
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return super.getWriteHandlerList();
    }

    @Override
    public String sheetName() {
        return "商家列表";
    }
}
