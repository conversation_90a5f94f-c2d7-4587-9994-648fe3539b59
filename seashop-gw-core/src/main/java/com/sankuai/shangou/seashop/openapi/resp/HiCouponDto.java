package com.sankuai.shangou.seashop.openapi.resp;

import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/10 11:26
 */
@Data
public class HiCouponDto {

    private Long id;

    private Long recordId = 0L;

    private String name;

    private Integer type = 0;

    private String typeDesc = "满减券";

    // 使用门槛-金额
    private BigDecimal limitAmount;

    // 使用门槛-数量
    private Integer limitQuantity = 1;

    // 限制商品可用
    private Integer limitProduct;

    // 折扣
    private BigDecimal rebate = BigDecimal.ZERO;

    // 优惠金额
    private BigDecimal amount;

    private BigDecimal randomMin = BigDecimal.ZERO;

    private BigDecimal randomMax = BigDecimal.ZERO;

    private BigDecimal overAmount = BigDecimal.ZERO;

    private Integer validity = 0;

    private Integer validityDay = 0;

    private Integer delayDay = 0;

    // 库存
    private Integer stock;

    // 领取数量
    private Integer give;

    private String content;

}
