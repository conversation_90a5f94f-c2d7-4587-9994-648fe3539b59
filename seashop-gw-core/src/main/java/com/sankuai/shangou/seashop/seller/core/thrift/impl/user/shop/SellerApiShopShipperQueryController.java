package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopShipperRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiQueryShopShipperReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiQueryShopShipperResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopShipperResp;

/**
 * @description：供应商发/退货地址
 * @author： liweisong
 * @create： 2023/11/27 10:30
 */
@RestController
@RequestMapping("/sellerApi/apiShopShipper")
public class SellerApiShopShipperQueryController {

    @Resource
    private SellerShopShipperRemoteService sellerShopShipperRemoteService;

    @PostMapping(value = "/queryShopShipperList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiQueryShopShipperResp> queryShopShipperList(@RequestBody ApiQueryShopShipperReq queryShopShipperReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryShopShipperList", queryShopShipperReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.checkParameter();
            QueryShopShipperResp queryShopShipperResp = sellerShopShipperRemoteService.queryShopShipperList(JsonUtil.copy(req, QueryShopShipperReq.class));
            return JsonUtil.copy(queryShopShipperResp, ApiQueryShopShipperResp.class);
        });
    }
}
