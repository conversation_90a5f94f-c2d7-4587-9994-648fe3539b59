package com.sankuai.shangou.seashop.seller.core.user.service;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.gw.common.thrift.requests.ApiRefreshTokenReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiMenuResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiCmdManagerReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryManagerPageReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiQueryManagerReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.account.ApiManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMenuAuthReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.EpManagerResp;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
public interface SellerApiManagerService {
    public EpManagerResp queryManager(Integer id);

    BasePageResp<ApiManagerResp> queryManagerPage(ApiQueryManagerPageReq queryManagerPageReq);

    ApiManagerResp queryManager(ApiQueryManagerReq queryManagerPageReq);

    Long addManager(ApiCmdManagerReq cmdManagerReq);

    Long editManager(ApiCmdManagerReq cmdManagerReq);

    Long deleteManager(ApiCmdManagerReq cmdManagerReq);

    Integer batchDeleteManager(ApiCmdManagerReq cmdManagerReq);

    LoginResp login(ApiLoginReq loginReq);

    LoginResp refreshToken(ApiRefreshTokenReq req);

    Boolean logout();

    List<ApiMenuResp> queryMenuAuth(QueryMenuAuthReq authReq);
}
