package com.sankuai.shangou.seashop.m.core.thrift.impl.system.task;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiQueryTaskReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiPlatformTaskDto;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mApi/apiPlatformTask")
@Slf4j
public class MApiPlatformTaskQueryController {

    @Resource
    private MExportTaskBiz mExportTaskBiz;


    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiPlatformTaskDto>> pageList(@RequestBody ApiQueryTaskReq queryReq) throws TException {
        log.info("【平台后台任务】查询分页列表, 请求参数={}", queryReq);
        return ThriftResponseHelper.responseInvoke("pageList", queryReq, func -> {
            // 业务逻辑处理
//            LoginManagerDto manager = TracerUtil.getManager();
            func.setOperatorId(TracerUtil.getManagerDto().getId());
            QueryTaskReq bean = JsonUtil.copy(func, QueryTaskReq.class);
            return PageResultHelper.transfer(mExportTaskBiz.pageList(bean), ApiPlatformTaskDto.class);
        });
    }


//    @PostMapping(value = "/test", consumes = "application/json")
//    public ResultDto<String> test() throws TException {
//
//        return ThriftResponseHelper.responseInvoke("pageList", null, func -> {
//
//            String json = "{\"taskId\":\"1811471462627115032\",\"taskType\":101150050,\"executeParam\":\"{\\\"operationShopId\\\":0,\\\"pageSize\\\":10,\\\"pageNo\\\":1,\\\"productId\\\":883,\\\"inStatus\\\":[\\\"ON_SALE\\\",\\\"IN_STOCK\\\",\\\"VIOLATION\\\"]}\",\"cost\":0,\"count\":0,\"taskDate\":\"2024-07-29 17:32:24\"}";
//            ExportTask task = JsonUtil.parseObject(json, ExportTask.class);
//            mExportTaskBiz.execute(task);
//            return "PageResultHelper.transfer(mExportTaskBiz.pageList(bean), ApiPlatformTaskDto.class)";
//        });
//    }
}
