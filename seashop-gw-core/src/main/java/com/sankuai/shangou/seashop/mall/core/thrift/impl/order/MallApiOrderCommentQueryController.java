package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiOrderCommentResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiQueryOrderCommentDetailReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiOrderCommentDetailResp;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderCommentDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderCommentDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderCommentResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/21 19:19
 */
@RestController
@RequestMapping("/mallApi/apiOrderComment")
@Slf4j
public class MallApiOrderCommentQueryController {

    @Resource
    private OrderCommentQueryFeign orderCommentQueryFeign;

    @PostMapping(value = "/queryOrderCommentForBuyer", consumes = "application/json")
    @NeedLogin
    public ResultDto<BasePageResp<ApiOrderCommentResp>> queryOrderCommentForBuyer(@RequestBody BasePageReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderCommentForBuyer", request, req -> {

            QueryOrderCommentReq remoteReq = JsonUtil.copy(req, QueryOrderCommentReq.class);
            remoteReq.setUserId(TracerUtil.getMemberDto().getId());
            BasePageResp<OrderCommentResp> resp = ThriftResponseHelper.executeThriftCall(() -> orderCommentQueryFeign.queryOrderCommentForBuyer(remoteReq));
            return PageResultHelper.transfer(resp, ApiOrderCommentResp.class);
        });
    }

    @PostMapping(value = "/queryOrderCommentDetailForBuyer", consumes = "application/json")
    @NeedLogin
    public ResultDto<ApiOrderCommentDetailResp> queryOrderCommentDetailForBuyer(@RequestBody ApiQueryOrderCommentDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderCommentDetailForBuyer", request, req -> {
            req.checkParameter();

            QueryOrderCommentDetailReq remoteReq = new QueryOrderCommentDetailReq();
            remoteReq.setUserId(TracerUtil.getMemberDto().getId());
            remoteReq.setOrderId(req.getOrderId());
            OrderCommentDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> orderCommentQueryFeign.queryOrderCommentDetailForBuyer(remoteReq));
            return JsonUtil.copy(resp, ApiOrderCommentDetailResp.class);
        });
    }
}
