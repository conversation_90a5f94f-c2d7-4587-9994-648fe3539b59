package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiCategoryCashDepositListResp;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryCashDepositQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiCategoryCashDeposit")
public class MApiCategoryCashDepositQueryController {

    @Resource
    private CategoryCashDepositQueryFeign categoryCashDepositQueryFeign;

    @GetMapping(value = "/queryAll")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiCategoryCashDepositListResp> queryAll() throws TException {
        return ThriftResponseHelper.responseInvoke("queryAll", null, req ->
                JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> categoryCashDepositQueryFeign.queryAll())
                        , ApiCategoryCashDepositListResp.class));
    }
}
