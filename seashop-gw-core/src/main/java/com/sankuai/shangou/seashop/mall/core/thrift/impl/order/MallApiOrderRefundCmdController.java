package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.refund.*;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiOrderRefund")
@Slf4j
public class MallApiOrderRefundCmdController {

    @Resource
    private OrderRefundCmdFeign orderRefundCmdFeign;
    // 目前的属性拷贝使用的是json，把枚举去掉，否则回报错
    private final static String[] IGNORE_PROPERTIES = {"tab", "refundStatus", "orderStatus", "refundType", "refundPayType"};

    @NeedLogin
    @PostMapping(value = "/applyRefund", consumes = "application/json")
    public ResultDto<Long> applyRefund(@RequestBody ApiApplyOrderRefundReq applyReq) throws TException {
        log.info("【售后】待发货状态下，申请订单售后, 请求参数={}", applyReq);
        return ThriftResponseHelper.responseInvoke("applyRefund", applyReq, func -> {
            // 入参转换
            ApplyOrderRefundReq req = JsonUtil.copy(applyReq, ApplyOrderRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setRefundType(RefundTypeEnum.valueOf(applyReq.getRefundType()));
            req.setRefundPayType(RefundPayTypeEnum.valueOf(applyReq.getRefundPayType()));

            // 设置用户其他信息
            UserDto user = new UserDto();
            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            // 业务逻辑处理
            return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.applyRefund(req));
        });
    }

    @NeedLogin
    @PostMapping(value = "/applyWholeOrderRefund", consumes = "application/json")
    public ResultDto<Long> applyWholeOrderRefund(@RequestBody ApiApplyOrderRefundReq applyReq) throws TException {
        log.info("【售后】待收货/已完成状态下，申请整单退款退货, 请求参数={}", applyReq);
        return ThriftResponseHelper.responseInvoke("applyWholeOrderRefund", applyReq, func -> {
            // 入参转换
            ApplyOrderRefundReq req = JsonUtil.copy(applyReq, ApplyOrderRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setRefundType(RefundTypeEnum.valueOf(applyReq.getRefundType()));
            req.setRefundPayType(RefundPayTypeEnum.valueOf(applyReq.getRefundPayType()));
            // 设置用户其他信息
            UserDto user = new UserDto();

            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            // 业务逻辑处理
            return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.applyWholeOrderRefund(req));
        });
    }

    @NeedLogin
    @PostMapping(value = "/reapplyOrderRefund", consumes = "application/json")
    public ResultDto<BaseResp> reapplyOrderRefund(@RequestBody ApiReapplyRefundReq applyReq) throws TException {
        log.info("【售后】重新申请售后, 请求参数={}", applyReq);
        return ThriftResponseHelper.responseInvoke("reapplyOrderRefund", applyReq, func -> {
            // 入参转换
            ReapplyRefundReq req = JsonUtil.copy(applyReq, ReapplyRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setRefundType(RefundTypeEnum.valueOf(applyReq.getRefundType()));
            req.setRefundPayType(RefundPayTypeEnum.valueOf(applyReq.getRefundPayType()));
            // 设置用户其他信息
            UserDto user = new UserDto();

            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.reapplyOrderRefund(req));
        });
    }

    @NeedLogin
    @PostMapping(value = "/applyItemRefund", consumes = "application/json")
    public ResultDto<Long> applyItemRefund(@RequestBody ApiApplyOrderItemRefundReq applyReq) throws TException {
        log.info("【售后】订单明细申请退货/退款, 请求参数={}", applyReq);
        return ThriftResponseHelper.responseInvoke("applyItemRefund", applyReq, func -> {
            // 入参转换
            ApplyOrderItemRefundReq req = JsonUtil.copy(applyReq, ApplyOrderItemRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setRefundType(RefundTypeEnum.valueOf(applyReq.getRefundType()));
            req.setRefundPayType(RefundPayTypeEnum.valueOf(applyReq.getRefundPayType()));
            // 设置用户其他信息
            UserDto user = new UserDto();

            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            // 业务逻辑处理
            return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.applyItemRefund(req));
        });
    }

    @NeedLogin
    @PostMapping(value = "/cancelRefund", consumes = "application/json")
    public ResultDto<BaseResp> cancelRefund(@RequestBody ApiCancelOrderRefundReq cancelReq) throws TException {
        log.info("【售后】取消售后, 请求参数={}", cancelReq);
        return ThriftResponseHelper.responseInvoke("cancelRefund", cancelReq, func -> {
            // 入参转换
            CancelOrderRefundReq req = JsonUtil.copy(cancelReq, CancelOrderRefundReq.class);
            // 设置用户其他信息
            UserDto user = new UserDto();
            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.cancelRefund(req));
        });
    }

    @NeedLogin
    @PostMapping(value = "/userDeliver", consumes = "application/json")
    public ResultDto<BaseResp> userDeliver(@RequestBody ApiUserDeliverReq req) throws TException {
        log.info("【售后】买家寄货, 请求参数={}", req);
        return ThriftResponseHelper.responseInvoke("userDeliver", req, func -> {
            // 入参转换
            UserDeliverReq userDeliverReq = JsonUtil.copy(req, UserDeliverReq.class);
            // 设置用户其他信息
            UserDto user = new UserDto();
            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            userDeliverReq.setUser(user);
            // 业务逻辑处理
            return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.userDeliver(userDeliverReq));
        });
    }
}
