package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiQueryUserCenterHomeReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiMemberResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiProductBaseResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiQueryUserCenterHomeResp;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.mall.core.service.user.account.MallMemberService;
import com.sankuai.shangou.seashop.trade.thrift.core.TradeProductQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SearchTradeProductReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.SearchTradeProductResp;
import com.sankuai.shangou.seashop.user.thrift.account.UserCenterQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserCenterHomeReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.QueryUserCenterHomeResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.UserBaseResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author： liweisong
 * @create： 2023/12/15 16:13
 */
@RestController
@RequestMapping("/mallApi/apiUserCenter")
@Slf4j
public class MallApiUserCenterQueryController {

    @Resource
    private UserCenterQueryFeign userCenterQueryFeign;

    @Resource
    private TradeProductQueryFeign tradeProductQueryFeign;

    @Resource
    private MallMemberService mallMemberService;

    @PostMapping(value = "/queryUserCenterHome", consumes = "application/json")
    @NeedLogin
    public ResultDto<ApiQueryUserCenterHomeResp> queryUserCenterHome(@RequestBody ApiQueryUserCenterHomeReq queryUserCenterHomeReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryUserCenterHome", queryUserCenterHomeReq, req -> {
            

            req.setUserId(TracerUtil.getMemberDto().getId());
            req.setUserName(TracerUtil.getMemberDto().getName());
            req.checkParameter();
            QueryUserCenterHomeReq bean = JsonUtil.copy(req, QueryUserCenterHomeReq.class);
            QueryUserCenterHomeResp resp = ThriftResponseHelper.executeThriftCall(() -> userCenterQueryFeign.queryUserCenterHome(bean));
            ApiQueryUserCenterHomeResp result = JsonUtil.copy(resp, ApiQueryUserCenterHomeResp.class);

            BaseIdReq baseIdReq = new BaseIdReq();
            baseIdReq.setId(TracerUtil.getMemberDto().getId());
            ApiMemberResp apiMemberResp = mallMemberService.queryMember(baseIdReq);

            UserBaseResp userBaseResp = new UserBaseResp();
            userBaseResp.setSupplier(apiMemberResp.getSupplier());
            userBaseResp.setPlateStatus(apiMemberResp.getPlateStatus());
            AtomicInteger accountSafetyLevel = new AtomicInteger(0);
            // 测试跟产品说了按照登录密码来判断，那就先加个1，每个账号都有登录密码。
            accountSafetyLevel.getAndIncrement();
            AtomicBoolean bindEmail = new AtomicBoolean(false);
            AtomicBoolean bindPhone = new AtomicBoolean(false);
            if (!StringUtils.isEmpty(apiMemberResp.getEmail())) {
                accountSafetyLevel.getAndIncrement();
                bindEmail.set(true);
            }
            if (!StringUtils.isEmpty(apiMemberResp.getCellPhone())) {
                accountSafetyLevel.getAndIncrement();
                bindPhone.set(true);
            }
            userBaseResp.setPayPwd(apiMemberResp.getPayPwd());
            userBaseResp.setBindEmail(bindEmail.get());
            userBaseResp.setBindPhone(bindPhone.get());
            userBaseResp.setPhoto(apiMemberResp.getPhoto());
            userBaseResp.setUserName(req.getUserName());
            userBaseResp.setAccountSafetyLevel(accountSafetyLevel.get());
            userBaseResp.setCellPhone(apiMemberResp.getCellPhone());
            userBaseResp.setEmail(apiMemberResp.getEmail());
            result.setUserBaseResp(userBaseResp);

            // 为了去计算商品的价格规格，专享价，阶梯价，优惠券等
            List<String> productIds = new ArrayList<>();
            // 商品推荐
            List<ApiProductBaseResp> recommendList = result.getRecommendList();
            if (!CollectionUtils.isEmpty(recommendList)) {
                List<String> recommendProductIds = recommendList.stream().map(ApiProductBaseResp::getProductId).collect(Collectors.toList());
                productIds.addAll(recommendProductIds);
            }
            // 商品收藏
            List<ApiProductBaseResp> favoriteProductList = result.getFavoriteProductList();
            if (!CollectionUtils.isEmpty(favoriteProductList)) {
                List<String> favoriteProductIds = favoriteProductList.stream().map(ApiProductBaseResp::getProductId).collect(Collectors.toList());
                productIds.addAll(favoriteProductIds);
            }
            Map<String, TradeProductDto> map = new HashMap<>();
            if (!CollectionUtils.isEmpty(productIds)) {
                SearchTradeProductReq search = new SearchTradeProductReq();
                search.setUserId(TracerUtil.getMemberDto().getId());
                search.setProductIds(productIds);
                search.setPageNo(1);
                search.setPageSize(200);
                SearchTradeProductResp searchResp = ThriftResponseHelper.executeThriftCall(() -> tradeProductQueryFeign.search(search));
                map = searchResp.getProductPage().getData().stream().collect(Collectors.toMap(TradeProductDto::getProductId, Function.identity(), (o1, o2) -> o1));
            }
            if (!CollectionUtils.isEmpty(recommendList)) {
                Map<String, TradeProductDto> finalMap = map;
                recommendList.forEach(item -> {
                    TradeProductDto tradeProductDto = finalMap.get(item.getProductId());
                    if (Objects.isNull(tradeProductDto)) {
                        return;
                    }
                    item.setSalePrice(tradeProductDto.getSalePrice());
                    item.setSalePriceString(tradeProductDto.getSalePriceString());
                    item.setMarketPrice(tradeProductDto.getMarketPrice());
                    item.setMarketPriceString(tradeProductDto.getMarketPriceString());
                });
            }
            if (!CollectionUtils.isEmpty(favoriteProductList)) {
                Map<String, TradeProductDto> finalMap1 = map;
                favoriteProductList.forEach(item -> {
                    TradeProductDto tradeProductDto = finalMap1.get(item.getProductId());
                    if (Objects.isNull(tradeProductDto)) {
                        return;
                    }
                    item.setSalePrice(tradeProductDto.getSalePrice());
                    item.setSalePriceString(tradeProductDto.getSalePriceString());
                    item.setMarketPrice(tradeProductDto.getMarketPrice());
                    item.setMarketPriceString(tradeProductDto.getMarketPriceString());
                });
            }
            return result;
        });
    }
}
