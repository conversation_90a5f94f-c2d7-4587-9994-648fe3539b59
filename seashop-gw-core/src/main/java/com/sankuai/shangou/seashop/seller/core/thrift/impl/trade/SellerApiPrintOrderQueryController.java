package com.sankuai.shangou.seashop.seller.core.thrift.impl.trade;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.trade.SellerPrintOrderRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.trade.ApiPrintOrderReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.trade.ApiPrintOrderResp;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PrintOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PrintOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 17:51
 */
@RestController
@RequestMapping("/sellerApi/apiPrintOrder")
@Slf4j
public class SellerApiPrintOrderQueryController {

    @Resource
    private SellerPrintOrderRemoteService sellerPrintOrderRemoteService;

    @PostMapping(value = "/getOrderPrint", consumes = "application/json")
    public ResultDto<ApiPrintOrderResp> getOrderPrint(@RequestBody ApiPrintOrderReq printOrderReq) throws TException {
        return ThriftResponseHelper.responseInvoke("getOrderPrint", printOrderReq, req -> {
            PrintOrderResp orderPrint = sellerPrintOrderRemoteService.getOrderPrint(JsonUtil.copy(req, PrintOrderReq.class));
            return JsonUtil.copy(orderPrint, ApiPrintOrderResp.class);
        });
    }
}
