package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopInvoiceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiSaveShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopInvoiceReq;

/**
 * @author： liweisong
 * @create： 2023/11/29 9:01
 */
@RestController
@RequestMapping("/sellerApi/apiShopInvoice")
public class SellerApiShopInvoiceCmdController {

    @Resource
    private SellerShopInvoiceRemoteService sellerShopInvoiceRemoteService;

    @PostMapping(value = "/saveShopInvoice", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> saveShopInvoice(@RequestBody ApiSaveShopInvoiceReq saveShopInvoiceReq) throws TException {
        
        return ThriftResponseHelper.responseInvoke("saveShopInvoice", saveShopInvoiceReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return sellerShopInvoiceRemoteService.saveShopInvoice(JsonUtil.copy(req, SaveShopInvoiceReq.class));
        });
    }
}
