package com.sankuai.shangou.seashop.m.core.service.finance.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.m.common.remote.order.MOrderRemoteService;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiFinanceService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderStatisticsReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiFinanceIndexResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiOrderStatisticsListResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiOrderStatisticsResp;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.OrderStatisticsReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsResp;
import com.sankuai.shangou.seashop.order.thrift.finance.FinanceQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceIndexResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class MApiFinanceServiceImpl implements MApiFinanceService {

    @Resource
    private FinanceQueryFeign financeQueryFeign;
    @Resource
    private MOrderRemoteService mOrderRemoteService;
    @Resource
    private OrderQueryFeign orderQueryFeign;

    @Override
    public ApiFinanceIndexResp getFinanceIndex() {
        BaseIdReq request = new BaseIdReq();
        FinanceIndexResp financeIndex =  ThriftResponseHelper.executeThriftCall(() -> financeQueryFeign.getFinanceIndex(request));
        OrderStatisticsResp orderStatisticsResp = mOrderRemoteService.getYesterdayOrderStatistics();

        ApiFinanceIndexResp apiFinanceIndexResp = new ApiFinanceIndexResp();
        // 初始数据
        apiFinanceIndexResp.setPendingSettlement(BigDecimal.ZERO);
        apiFinanceIndexResp.setSettled(BigDecimal.ZERO);
        if (null != financeIndex) {
            apiFinanceIndexResp.setPendingSettlement(financeIndex.getPendingSettlement());
            apiFinanceIndexResp.setSettled(financeIndex.getSettled());
        }

        ApiOrderStatisticsResp yesterdayOrderStatistics = new ApiOrderStatisticsResp();
        // 初始数据
        yesterdayOrderStatistics.setOrdersNum(CommonConstant.DEFAULT_COUNT);
        yesterdayOrderStatistics.setPayOrdersNum(CommonConstant.DEFAULT_COUNT);
        yesterdayOrderStatistics.setSaleAmount(BigDecimal.ZERO);
        if (null != orderStatisticsResp) {
            yesterdayOrderStatistics.setOrdersNum(orderStatisticsResp.getOrdersNum());
            yesterdayOrderStatistics.setPayOrdersNum(orderStatisticsResp.getPayOrdersNum());
            yesterdayOrderStatistics.setSaleAmount(orderStatisticsResp.getSaleAmount());
        }

        apiFinanceIndexResp.setYesterdayOrderStatistics(yesterdayOrderStatistics);

        return apiFinanceIndexResp;
    }

    @Override
    public ApiOrderStatisticsListResp getOrderStatisticsList(ApiOrderStatisticsReq request) {
        OrderStatisticsListResp orderStatisticsList = ThriftResponseHelper.executeThriftCall(() ->
                orderQueryFeign.getOrderStatisticsList(JsonUtil.copy(request, OrderStatisticsReq.class)));
        return JsonUtil.copy(orderStatisticsList, ApiOrderStatisticsListResp.class);
    }

}
