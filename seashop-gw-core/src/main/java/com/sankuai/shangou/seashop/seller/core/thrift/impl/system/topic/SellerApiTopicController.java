package com.sankuai.shangou.seashop.seller.core.thrift.impl.system.topic;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.TopicCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.TopicQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.PlateTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseShopReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseTopicRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWapTopicRes;
import com.sankuai.shangou.seashop.m.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.m.thrift.core.request.topic.ApiFooterReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.topic.ApiHeaderReq;
import com.sankuai.shangou.seashop.seller.common.remote.base.SellerTopicRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ApiBaseShopReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ApiBaseTopicQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ApiBaseWapTopicQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.enums.ClientTypeEnum;
import com.sankuai.shangou.seashop.seller.thrift.core.request.topic.ApiWapIndexReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.topic.ApiWapTopicReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiBaseTopicRes;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiBaseWapTopicRes;
import com.sankuai.shangou.seashop.seller.thrift.core.response.system.SellerIndexPageRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;

@RestController
@RequestMapping("/sellerApi/apiTopic")
@Slf4j
public class SellerApiTopicController {

    @Resource
    private SellerTopicRemoteService sellerTopicRemoteService;
    @Resource
    private TopicQueryFeign topicQueryFeign;
    @Resource
    private TopicCMDFeign topicCMDFeign;

    @PostMapping(value = "/query", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiBaseTopicRes>> query(@RequestBody ApiBaseTopicQueryReq query) throws TException {

        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            //查询平台
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setPlatForm(CommonConstant.TOPIC_PC);
            BasePageResp<BaseTopicRes> result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.query(JsonUtil.copy(req, BaseTopicQueryReq.class)));
            return PageResultHelper.transfer(result, ApiBaseTopicRes.class);
        });

    }


    @PostMapping(value = "/queryWxa", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiBaseTopicRes>> queryWxa(@RequestBody ApiBaseTopicQueryReq query) throws TException {

        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            //查询平台
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setPlatForm(CommonConstant.TOPIC_WXA);
            BasePageResp<BaseTopicRes> result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.query(JsonUtil.copy(req, BaseTopicQueryReq.class)));
            return PageResultHelper.transfer(result, ApiBaseTopicRes.class);
        });
    }

    @PostMapping(value = "/createTopic", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> createTopic(@RequestBody ApiWapTopicReq topicReq) throws TException {
        topicReq.checkParameter();

        topicReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("createTopic", topicReq, req -> {
            req.setType(TemplateClientTypeEnum.PCTOPIC_SELLER.getCode());
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            String title = node.get("page").get("title").asText();
//            String tags = node.get("page").get("tags").asText();
//            String icon = node.get("page").get("logo").asText();
            BaseTopicReq topic = new BaseTopicReq();
            topic.setName(title);
//            topic.setTags(tags);
//            topic.setFrontCoverImage(icon);
//            topic.setTopImage(icon);
            topic.setPlatForm(PlateTypeEnum.PC.getCode());
            topic.setShopId(req.getShopId());
            topic.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            Long topicId = ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.create(topic));
            topic.setTopicModules(new ArrayList<>());
            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(req.getType());
            baseTopicJsonFileReq.setShopId(TracerUtil.getShopDto().getShopId());
            baseTopicJsonFileReq.setClient(topicId.toString());
            String filePath = sellerTopicRemoteService.uploadTemplate(baseTopicJsonFileReq);
            return topicId;
        });
    }

    @PostMapping(value = "/updateTopic", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> updateTopic(@RequestBody ApiWapTopicReq topicReq) throws TException {
        topicReq.checkParameter();

        topicReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("updateWapTopic", topicReq, req -> {
            req.setType(TemplateClientTypeEnum.PCTOPIC_SELLER.getCode());
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            String title = node.get("page").get("title").asText();
//            String tags = node.get("page").get("tags").asText();
//            String icon = node.get("page").get("logo").asText();
            BaseTopicReq topic = new BaseTopicReq();
            topic.setName(title);
//            topic.setTags(tags);
//            topic.setTopImage(icon);
//            topic.setFrontCoverImage(icon);
            topic.setId(req.getId());
            topic.setPlatForm(PlateTypeEnum.PC.getCode());
            topic.setShopId(req.getShopId());
            topic.setTopicModules(new ArrayList<>());
            topic.setOperationUserId(TracerUtil.getShopDto().getManagerId());
//            topic.setShopId(req.getCurrentShopId());


            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(req.getType());
            baseTopicJsonFileReq.setShopId(req.getShopId());
            baseTopicJsonFileReq.setClient(req.getId().toString());
            String filePath = sellerTopicRemoteService.uploadTemplate(baseTopicJsonFileReq);

            ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.update(topic));
            return true;
        });
    }

    @PostMapping(value = "/createWapTopic", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> createWapTopic(@RequestBody ApiWapTopicReq topicReq) throws TException {
        topicReq.checkParameter();

        topicReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("createWapTopic", topicReq, req -> {
            req.setType(TemplateClientTypeEnum.WXSmallProgramSpecial.getCode());
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            String title = node.get("page").get("title").asText();
//            String tags = node.get("page").get("tags").asText();
//            String icon = node.get("page").get("logo").asText();
            BaseTopicReq topic = new BaseTopicReq();
            topic.setName(title);
//            topic.setTags(tags);
//            topic.setFrontCoverImage(icon);
//            topic.setTopImage(icon);
            topic.setPlatForm(PlateTypeEnum.WXA.getCode());
            topic.setShopId(req.getShopId());
            topic.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            Long topicId = ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.create(topic));
            topic.setTopicModules(new ArrayList<>());
            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(req.getType());
            baseTopicJsonFileReq.setShopId(TracerUtil.getShopDto().getShopId());
            baseTopicJsonFileReq.setClient(topicId.toString());
            String filePath = sellerTopicRemoteService.uploadTemplate(baseTopicJsonFileReq);
            return topicId;
        });
    }

    @PostMapping(value = "/updateWapTopic", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> updateWapTopic(@RequestBody ApiWapTopicReq topicReq) throws TException {
        topicReq.checkParameter();

        topicReq.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("updateWapTopic", topicReq, req -> {
            req.setType(TemplateClientTypeEnum.WXSmallProgramSpecial.getCode());
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            String title = node.get("page").get("title").asText();
            BaseTopicReq topic = new BaseTopicReq();
            topic.setName(title);
            topic.setId(req.getId());
            topic.setPlatForm(PlateTypeEnum.WXA.getCode());
            topic.setShopId(req.getShopId());
            topic.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            topic.setTopicModules(new ArrayList<>());


            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(req.getType());
            baseTopicJsonFileReq.setShopId(req.getShopId());
            baseTopicJsonFileReq.setClient(req.getId().toString());
            String filePath = sellerTopicRemoteService.uploadTemplate(baseTopicJsonFileReq);

            ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.update(topic));
            return true;
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiBaseWapTopicRes> getById(@RequestBody ApiBaseShopReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            BaseWapTopicQueryReq bean = JsonUtil.copy(req, BaseWapTopicQueryReq.class);
            bean.setType(TemplateClientTypeEnum.PCTOPIC_SELLER.getCode());
            bean.setClient(req.getId().toString());
            bean.setShopId(req.getShopId());
            bean.setCurrentShopId(req.getShopId());
            BaseWapTopicRes result = sellerTopicRemoteService.getWapTopicById(bean);
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    @PostMapping(value = "/getWapTopicById", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiBaseWapTopicRes> getWapTopicById(@RequestBody ApiBaseWapTopicQueryReq query) throws TException {

        query.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("getWapTopicById", query, req -> {
            req.setType(TemplateClientTypeEnum.WXSmallProgramSpecial.getCode());
            BaseWapTopicRes result = sellerTopicRemoteService.getWapTopicById(JsonUtil.copy(req, BaseWapTopicQueryReq.class));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    @PostMapping(value = "/editWapIndex", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> editWapIndex(@RequestBody ApiWapIndexReq indexReq) throws TException {

        indexReq.setCurrentShopId(TracerUtil.getShopDto().getShopId());

        indexReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("editWapIndex", indexReq, req -> {
            TemplatePageIndexReq pageIndexReq = new TemplatePageIndexReq();
            pageIndexReq.setShopId(TracerUtil.getShopDto().getShopId());
            pageIndexReq.setContent(req.getContent());
            pageIndexReq.setType(TemplateClientTypeEnum.WXSmallProgramSellerWapIndex.getCode());
            Boolean result = ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.editSellerIndex(pageIndexReq));
            return result;
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> delete(@RequestBody ApiBaseShopReq baseShopReq) throws TException {

        baseShopReq.setShopId(TracerUtil.getShopDto().getShopId());
        baseShopReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("create", baseShopReq, req -> {
            Boolean result = ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.delete(JsonUtil.copy(req, BaseShopReq.class)));
            return result;
        });
    }


    @GetMapping(value = "/getPCIndex")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<SellerIndexPageRes> getPCIndex(@RequestParam(required = false) Long currentShopId) throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();

        query.setType(TemplateClientTypeEnum.PCIndex_SELLER.getCode());
        query.setCurrentShopId(TracerUtil.getShopDto().getShopId());
        query.setShopId(TracerUtil.getShopDto().getShopId());
        query.setClient(ClientTypeEnum.Default.getCode());
        return ThriftResponseHelper.responseInvoke("getPCIndex", query, req -> {
            log.info("店铺首页查询数据:", req);
            String resultRpc = ThriftResponseHelper.executeThriftCall(() -> topicQueryFeign.getSellerPCIndex(TracerUtil.getShopDto().getShopId()));
            SellerIndexPageRes result = new SellerIndexPageRes();
            result.setPage(resultRpc);
            return result;
        });
    }

    @GetMapping(value = "/getWapIndex")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<SellerIndexPageRes> getWapIndex(@RequestParam(required = false) Long currentShopId) throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();

        query.setType(TemplateClientTypeEnum.WXSmallProgramSellerWapIndex.getCode());
        query.setCurrentShopId(TracerUtil.getShopDto().getShopId());
        query.setShopId(TracerUtil.getShopDto().getShopId());
        query.setClient(ClientTypeEnum.Default.getCode());
        return ThriftResponseHelper.responseInvoke("getWapIndex", query, req -> {
            log.info("店铺首页查询数据:", req);
            String resultRpc = ThriftResponseHelper.executeThriftCall(() -> topicQueryFeign.getSellerIndex(TracerUtil.getShopDto().getShopId()));
            SellerIndexPageRes result = new SellerIndexPageRes();
            result.setPage(resultRpc);
            return result;
        });
    }

    @PostMapping(value = "/setHome", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> setHome(@RequestBody com.sankuai.shangou.seashop.m.thrift.system.topic.request.ApiBaseShopReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getPCFooter", query, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.checkParameter();
            BaseShopReq req1 = JsonUtil.copy(req, BaseShopReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() -> topicCMDFeign.setHome(req1));
            return result;
        });
    }

    @PostMapping(value = "/editPCHeader", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> editPCHeader(@RequestBody ApiHeaderReq headerReq) throws TException {
        headerReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("editPCHeader", headerReq, req -> {
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(TemplateClientTypeEnum.HEADER_SELLER.getCode());
            baseTopicJsonFileReq.setShopId(TracerUtil.getShopDto().getShopId());
            baseTopicJsonFileReq.setClient("header");
            String filePath = sellerTopicRemoteService.uploadTemplate(baseTopicJsonFileReq);
            return true;
        });
    }

    @GetMapping(value = "/getPCHeader")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiBaseWapTopicRes> getPCHeader() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.HEADER_SELLER.getCode());
        query.setClient("header");
        query.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("getPCHeader", query, req -> {
            BaseWapTopicRes result =
                    sellerTopicRemoteService.getWapTopicById(req);
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    @PostMapping(value = "/editPCFooter", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> editPCFooter(@RequestBody ApiFooterReq footerReq) throws TException {
        footerReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("editPCFooter", footerReq, req -> {
            JsonNode node = JsonUtil.parseObject(req.getContent(), JsonNode.class);
            BaseTopicJsonFileReq baseTopicJsonFileReq = new BaseTopicJsonFileReq();
            baseTopicJsonFileReq.setJsonContent(req.getContent());
            baseTopicJsonFileReq.setType(TemplateClientTypeEnum.FOOTER_SELLER.getCode());
            baseTopicJsonFileReq.setShopId(TracerUtil.getShopDto().getShopId());
            baseTopicJsonFileReq.setClient("footer");
            String filePath = sellerTopicRemoteService.uploadTemplate(baseTopicJsonFileReq);
            return true;
        });
    }

    @GetMapping(value = "/getPCFooter")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiBaseWapTopicRes> getPCFooter() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.FOOTER_SELLER.getCode());
        query.setClient("footer");
        query.setShopId(TracerUtil.getShopDto().getShopId());
        return ThriftResponseHelper.responseInvoke("getPCFooter", query, req -> {
            BaseWapTopicRes result = sellerTopicRemoteService.getWapTopicById(req);
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }
}
