package com.sankuai.shangou.seashop.seller.core.service.promotion;

import com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion.bo.ApiExclusivePriceImportBo;
import com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion.dto.ApiExclusivePriceImportDto;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
public interface SellerApiExclusivePriceService {

    /**
     * 专享价商品导入
     *
     * @param bo
     * @return
     */
    ApiExclusivePriceImportDto importData(ApiExclusivePriceImportBo bo);
}
