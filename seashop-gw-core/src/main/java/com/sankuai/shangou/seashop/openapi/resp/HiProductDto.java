package com.sankuai.shangou.seashop.openapi.resp;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/10 9:22
 */
@Data
public class HiProductDto {

    private Long cid;

    @JsonAlias("shopCategoryNames")
    private String cat_name;

    @JsonAlias("brandId")
    private Long brand_id;

    @JsonAlias("brandName")
    private String brand_name;

    @JsonAlias("typeId")
    private Integer type_id = 0;

    @JsonAlias("typeName")
    private String type_name = "";

    @JsonAlias("productId")
    private Long num_iid;

    @JsonAlias("productCode")
    private String outerId;

    @JsonAlias("productName")
    private String title;

    @JsonAlias("imageList")
    @JsonUrlFormat(deserializer = false)
    private List<String> pic_url;

    @JsonAlias("onsaleTime")
    private Date list_time;

    @JsonAlias("updateTime")
    private Date modified;

    @JsonAlias("saleStatusDesc")
    private String approve_status;

    @JsonAlias("saleCounts")
    private Integer sold_quantity;

    @JsonAlias("totalStock")
    private Integer num;

    @JsonAlias("minSalePrice")
    private BigDecimal price;

    @JsonAlias("h5Url")
    private String h5_url;

}
