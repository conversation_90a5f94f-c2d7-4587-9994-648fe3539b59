package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;


import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiLabelService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdLabelReq;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiLabel")
@Slf4j
public class MApiLabelCmdController {

    @Resource
    private MApiLabelService mApiLabelService;

    @PostMapping(value = "/addLabel", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> addLabel(@RequestBody ApiCmdLabelReq cmdLabelPageReq) {
        cmdLabelPageReq.setOperatorId(TracerUtil.getManagerDto().getId());
        return ThriftResponseHelper.responseInvoke("addLabel", cmdLabelPageReq, req -> mApiLabelService.addLabel(cmdLabelPageReq));
    }

    @PostMapping(value = "/editLabel", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> editLabel(@RequestBody ApiCmdLabelReq cmdLabelPageReq) {
        cmdLabelPageReq.setOperatorId(TracerUtil.getManagerDto().getId());
        return ThriftResponseHelper.responseInvoke("editLabel", cmdLabelPageReq, req -> mApiLabelService.editLabel(cmdLabelPageReq));
    }

    @PostMapping(value = "/deleteLabel", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<Long> deleteLabel(@RequestBody ApiCmdLabelReq cmdLabelPageReq) {
        cmdLabelPageReq.setOperatorId(TracerUtil.getManagerDto().getId());
        return ThriftResponseHelper.responseInvoke("deleteLabel", cmdLabelPageReq, req -> mApiLabelService.deleteLabel(cmdLabelPageReq));
    }

}
