package com.sankuai.shangou.seashop.m.core.service.export.handler.finance.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo.PendSettleListEo;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiPendingSettlementService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPendingSettlementOrderResp;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;

import cn.hutool.core.collection.CollUtil;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
public class PendSettleListWrapper extends PageExportWrapper<PendSettleListEo, ApiPendingSettlementOrderQryReq> {

    private final MApiPendingSettlementService mApiPendingSettlementService;

    public PendSettleListWrapper(MApiPendingSettlementService mApiPendingSettlementService) {
        this.mApiPendingSettlementService = mApiPendingSettlementService;
    }

    @Override
    public List<PendSettleListEo> getPageList(ApiPendingSettlementOrderQryReq param) {
        BasePageResp<ApiPendingSettlementOrderResp> pendingSettlementList = mApiPendingSettlementService.pageList(param);
        if (null == pendingSettlementList || CollUtil.isEmpty(pendingSettlementList.getData())) {
            return null;
        }
        List<PendSettleListEo> pendSettleListEos = CollUtil.newArrayList();
        pendingSettlementList.getData().forEach(p -> {
            PendSettleListEo pendSettleListEo = JsonUtil.copy(p, PendSettleListEo.class);
            pendSettleListEo.setOrderStatusStr(OrderStatusEnum.getDesc(p.getOrderStatus()));
            pendSettleListEos.add(pendSettleListEo);
        });
        return pendSettleListEos;
    }

    @Override
    public Integer getBatchCount() {
        return 1000;
    }
}
