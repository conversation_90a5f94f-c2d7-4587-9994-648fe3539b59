package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.WayBillFeign;
import com.sankuai.shangou.seashop.order.thrift.core.HishopWayBillFegin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description：电子面单相关接口
 * @author： chenpeng
 */
@RestController
@RequestMapping("/sellerApi/wayBill")
public class SellerApiWayBillController {

    @Resource
    private HishopWayBillFegin hishopWayBillFegin;
    @Resource
    private WayBillFeign wayBillFeign;



    /**
     *获取电子面单平台地址
     */
    @GetMapping(value = "/goExpressBills")
    public ResultDto<String> goExpressBills() {
        return ThriftResponseHelper.responseInvoke("获取电子面单平台地址", null, func -> {
            String result =  ThriftResponseHelper.executeThriftCall(() -> wayBillFeign.goExpressBills());
            return result;
        });
    }

}
