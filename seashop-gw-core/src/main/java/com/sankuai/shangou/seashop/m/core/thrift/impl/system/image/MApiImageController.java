package com.sankuai.shangou.seashop.m.core.thrift.impl.system.image;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ImageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.ImageQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BasePhotoSpaceCategoryRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BasePhotoSpaceRes;
import com.sankuai.shangou.seashop.m.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.*;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBasePhotoSpaceCategoryRes;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBasePhotoSpaceRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mApi/apiImage")
public class MApiImageController {

    @Resource
    private ImageQueryFeign imageQueryFeign;

    @Resource
    private ImageCMDFeign imageCMDFeign;

    @PostMapping(value = "/query", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBasePhotoSpaceRes>> query(@RequestBody ApiBasePhotoSpaceQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            BasePhotoSpaceQueryReq bean = JsonUtil.copy(req, BasePhotoSpaceQueryReq.class);
            BasePageResp<BasePhotoSpaceRes> result = ThriftResponseHelper.executeThriftCall(() ->
                imageQueryFeign.query(bean));
            return PageResultHelper.transfer(result, ApiBasePhotoSpaceRes.class);
        });
    }

    @PostMapping(value = "/queryAudit", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBasePhotoSpaceRes>> queryAudit(@RequestBody ApiBasePhotoSpaceQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryAudit", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            BasePhotoSpaceQueryReq bean = JsonUtil.copy(req, BasePhotoSpaceQueryReq.class);
            BasePageResp<BasePhotoSpaceRes> result = ThriftResponseHelper.executeThriftCall(() ->
                imageQueryFeign.queryAudit(bean));
            return PageResultHelper.transfer(result, ApiBasePhotoSpaceRes.class);
        });
    }

    @PostMapping(value = "/create", consumes = "application/json")
    public ResultDto<Long> create(@RequestBody ApiBasePhotoSpaceReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BasePhotoSpaceReq bean = JsonUtil.copy(req, BasePhotoSpaceReq.class);
            Long imageId = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.create(bean));
            return imageId;
        });
    }

    @PostMapping(value = "/batchCreate", consumes = "application/json")
    public ResultDto<Boolean> batchCreate(@RequestBody List<ApiBasePhotoSpaceReq> query) throws TException {

        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            for (ApiBasePhotoSpaceReq photoSpaceReq : query) {
                photoSpaceReq.checkParameter();
                photoSpaceReq.setShopId(CommonConstant.PLATFORM_ID);
            }
            List<BasePhotoSpaceReq> beanList = JsonUtil.copyList(req, BasePhotoSpaceReq.class);
            ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.batchCreate(beanList));
            return true;
        });
    }

    @PostMapping(value = "/update", consumes = "application/json")
    public ResultDto<Boolean> update(@RequestBody ApiUpdatePhotoSpaceReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            UpdatePhotoSpaceReq bean = JsonUtil.copy(req, UpdatePhotoSpaceReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.update(bean));
            return result;
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    public ResultDto<Boolean> delete(@RequestBody ApiBasePhotoSpaceReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            BasePhotoSpaceReq bean = JsonUtil.copy(req, BasePhotoSpaceReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.delete(bean));
            return result;
        });
    }

    @PostMapping(value = "/moveImageByCateId", consumes = "application/json")
    public ResultDto<Boolean> moveImageByCateId(@RequestBody ApiBaseMoveCateImageReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BaseMoveCateImageReq bean = JsonUtil.copy(req, BaseMoveCateImageReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.moveImageByCateId(bean));
            return result;
        });
    }

    @PostMapping(value = "/moveImageById", consumes = "application/json")
    public ResultDto<Boolean> moveImageById(@RequestBody ApiBaseMoveImageReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("query", query, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BaseMoveImageReq bean = JsonUtil.copy(req, BaseMoveImageReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.moveImageById(bean));
            return result;
        });
    }

    @PostMapping(value = "/deleteImageByIds", consumes = "application/json")
    public ResultDto<Boolean> deleteImageByIds(@RequestBody ApiBaseIdsReq idsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("query", idsReq, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BaseIdsReq bean = JsonUtil.copy(req, BaseIdsReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.deleteImageByIds(bean));
            return result;
        });
    }

    @PostMapping(value = "/createCategory", consumes = "application/json")
    public ResultDto<Long> createCategory(@RequestBody ApiBasePhotoSpaceCategoryReq categoryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createCategory", categoryReq, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BasePhotoSpaceCategoryReq bean = JsonUtil.copy(req, BasePhotoSpaceCategoryReq.class);
            Long result = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.createCategory(bean));
            return result;
        });
    }

    @PostMapping(value = "/updateCategory", consumes = "application/json")
    public ResultDto<Boolean> updateCategory(@RequestBody ApiBasePhotoSpaceCategoryReq categoryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategory", categoryReq, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BasePhotoSpaceCategoryReq bean = JsonUtil.copy(req, BasePhotoSpaceCategoryReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.updateCategory(bean));
            return result;
        });
    }

    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    public ResultDto<Boolean> deleteCategory(@RequestBody ApiBaseIdsReq categoryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteCategory", categoryReq, req -> {
            req.setShopId(CommonConstant.PLATFORM_ID);
            req.checkParameter();
            BaseIdsReq bean = JsonUtil.copy(req, BaseIdsReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                imageCMDFeign.deleteCategory(bean));
            return result;
        });
    }

    @GetMapping(value = "/queryCategorys")
    public ResultDto<List<ApiBasePhotoSpaceCategoryRes>> queryCategorys() throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategorys", null, req -> {
            List<BasePhotoSpaceCategoryRes> result = ThriftResponseHelper.executeThriftCall(() ->
                imageQueryFeign.queryCategorys(CommonConstant.PLATFORM_ID));
            return JsonUtil.copyList(result, ApiBasePhotoSpaceCategoryRes.class);
        });
    }
}
