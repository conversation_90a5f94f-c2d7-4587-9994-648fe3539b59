package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.product.thrift.core.ProductAuditQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductAuditQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.QueryProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditPageResp;
import com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductAuditRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductQueryDetailReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductAuditReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductAuditDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductAuditPageResp;

import cn.hutool.core.date.DateUtil;

/**
 * <AUTHOR>
 * @date 2023/12/14 14:42
 */
@RestController
@RequestMapping("/sellerApi/apiProduct")
public class SellerApiProductAuditQueryController {

    @Resource
    private SellerProductAuditRemoteService sellerProductAuditRemoteService;
    @Resource
    private ProductAuditQueryFeign productAuditQueryFeign;

    @PostMapping(value = "/queryProductAudit", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiProductAuditPageResp>> queryProductAudit(@RequestBody ApiQueryProductAuditReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryProductAudit", request, req -> {
            req.checkParameter();

            dealSearchTime(req);
            QueryProductAuditReq remoteReq = JsonUtil.copy(req, QueryProductAuditReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            remoteReq.setAuditStatus(ProductEnum.AuditStatusEnum.getByCode(req.getAuditStatusCode()));
            BasePageResp<ProductAuditPageResp> resp = ThriftResponseHelper.executeThriftCall(() ->
                    productAuditQueryFeign.queryProductAudit(remoteReq));
            return PageResultHelper.transfer(resp, ApiProductAuditPageResp.class);
        });
    }

    @PostMapping(value = "/queryProductAuditDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiProductAuditDetailResp> queryProductAuditDetail(@RequestBody ApiProductQueryDetailReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryProductAuditDetail", request, req -> {
            req.checkParameter();

            ProductAuditQueryDetailReq remoteReq = JsonUtil.copy(req, ProductAuditQueryDetailReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            remoteReq.setNeedOriginProduct(Boolean.TRUE);
            ApiProductAuditDetailResp resp = sellerProductAuditRemoteService.queryProductAuditDetail(remoteReq);
            return resp;
        });
    }

    private void dealSearchTime(ApiQueryProductAuditReq req) {
        if (req.getStartTime() != null) {
            req.setStartTime(DateUtil.beginOfDay(req.getStartTime()));
        }
        if (req.getEndTime() != null) {
            req.setEndTime(DateUtil.endOfDay(req.getEndTime()));
        }
    }
}
