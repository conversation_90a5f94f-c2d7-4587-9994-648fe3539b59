package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.order.ApiQueryOrderCommentReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.order.ApiOrderCommentResp;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderCommentResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/21 19:50
 */
@RestController
@RequestMapping("/mApi/apiOrderComment")
@Slf4j
public class MApiOrderCommentQueryController {

    @Resource
    private OrderCommentQueryFeign orderCommentQueryFeign;

    @PostMapping(value = "/queryOrderCommentForPlatform", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BasePageResp<ApiOrderCommentResp>> queryOrderCommentForPlatform(@RequestBody ApiQueryOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderCommentForPlatform", request, req -> {
            req.checkParameter();

            QueryOrderCommentReq remoteReq = JsonUtil.copy(req, QueryOrderCommentReq.class);
            BasePageResp<OrderCommentResp> resp = ThriftResponseHelper.executeThriftCall(() -> orderCommentQueryFeign.queryOrderCommentForPlatform(remoteReq));
            return PageResultHelper.transfer(resp, ApiOrderCommentResp.class);
        });
    }
}
