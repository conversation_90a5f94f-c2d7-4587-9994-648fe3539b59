package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerBusinessCategoryRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiBusinessCategoryResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiBusinessCategoryTreeResp;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryBusinessCategoryTreeReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryTreeResp;

/**
 * <AUTHOR>
 * @date 2023/12/14 19:41
 */
@RestController
@RequestMapping("/sellerApi/apiBusinessCategory")
public class SellerApiBusinessCategoryQueryController {

    @Resource
    private SellerBusinessCategoryRemoteService sellerBusinessCategoryRemoteService;

    @GetMapping(value = "/queryBusinessCategoryTree")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiBusinessCategoryTreeResp> queryBusinessCategoryTree(String categoryName) throws TException {

        return ThriftResponseHelper.responseInvoke("queryBusinessCategoryTree", categoryName, req -> {

            QueryBusinessCategoryTreeReq remoteReq = new QueryBusinessCategoryTreeReq();
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            remoteReq.setCategoryName(categoryName); // 设置搜索参数
            BusinessCategoryTreeResp resp = sellerBusinessCategoryRemoteService.queryBusinessCategoryTree(remoteReq);
            return ApiBusinessCategoryTreeResp.builder().result(resp.getResult()).build();
        });
    }

    @PostMapping(value = "/queryPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiBusinessCategoryResp>> queryPage(@RequestBody BasePageReq request) throws TException {
        
        return ThriftResponseHelper.responseInvoke("queryPage", request, req -> {

            QueryBusinessCategoryPageReq remoteReq = JsonUtil.copy(req, QueryBusinessCategoryPageReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<BusinessCategoryResp> resp = sellerBusinessCategoryRemoteService.queryPage(remoteReq);
            return PageResultHelper.transfer(resp, ApiBusinessCategoryResp.class);
        });
    }
}
