package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiPrivilegeService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryPrivilegeReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiPrivilegeRespList;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiUserPrivilegeResp;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiPrivilege")
public class MApiPrivilegeQueryController {

    @Resource
    private MApiPrivilegeService mApiPrivilegeService;

    @PostMapping(value = "/queryPrivilegeList", consumes = "application/json")
    public ResultDto<ApiPrivilegeRespList> queryPrivilegeList(@RequestBody ApiQueryPrivilegeReq queryPrivilegeReq) {
        return ThriftResponseHelper.responseInvoke("queryPrivilegeList", queryPrivilegeReq, req -> mApiPrivilegeService.queryPrivilegeList(queryPrivilegeReq));
    }

    @PostMapping(value = "/queryUserPrivilege", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiUserPrivilegeResp> queryUserPrivilege() throws TException {
        LoginManagerDto managerInfo = TracerUtil.getManagerDto();
        return ThriftResponseHelper.responseInvoke("queryUserPrivilege", managerInfo, req -> mApiPrivilegeService.queryUserPrivilege(managerInfo));
    }
}
