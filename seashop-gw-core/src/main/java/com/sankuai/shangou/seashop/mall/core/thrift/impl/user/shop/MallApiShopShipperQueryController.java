package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.shop;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallShopShipperRemoteService;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopDefaultShipperResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description：供应商发/退货地址
 * @author： liweisong
 * @create： 2023/11/27 10:30
 */
@RestController
@RequestMapping("/mallApi/apiShopShipper")
public class MallApiShopShipperQueryController {

    @Resource
    private MallShopShipperRemoteService mallShopShipperRemoteService;

    @PostMapping(value = "/queryShopDefaultShipperList", consumes = "application/json")
    public ResultDto<QueryShopDefaultShipperResp> queryShopDefaultShipperList(@RequestBody QueryShopShipperReq queryShopShipperReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopShipperList", queryShopShipperReq, req -> {
            req.checkParameter();
            return mallShopShipperRemoteService.queryShopDefaultShipperList(req);
        });
    }
}
