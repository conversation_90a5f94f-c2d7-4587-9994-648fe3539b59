package com.sankuai.shangou.seashop.seller.core.thrift.impl.common;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.service.common.SellerHomeService;
import com.sankuai.shangou.seashop.seller.thrift.core.response.common.RadiusNumResp;

/**
 * <AUTHOR>
 * @date 2024/04/28 14:00
 */
@RestController
@RequestMapping("/sellerApi/apiHome")
public class SellerApiHomeQueryController {

    @Resource
    private SellerHomeService sellerHomeService;

    @GetMapping(value = "/queryRadiusNum")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<RadiusNumResp> queryRadiusNum() throws TException {
        return ThriftResponseHelper.responseInvoke("queryRadiusNum", null, req -> {
            return sellerHomeService.queryRadiusNum(TracerUtil.getShopDto().getShopId());
        });
    }
}
