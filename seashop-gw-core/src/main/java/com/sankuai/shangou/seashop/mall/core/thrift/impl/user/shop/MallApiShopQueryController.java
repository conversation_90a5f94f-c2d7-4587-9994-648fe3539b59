package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdCreateQRReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiProductShopQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiShopMallQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiProductShopInfoResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiShopDetailResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiShopEsCombinationResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiShopIntroductionResp;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.mall.core.service.user.shop.MallApiShopService;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mallApi/apiShop")
public class MallApiShopQueryController {
    @Resource
    MallApiShopService shopService;


    @GetMapping(value = "/queryDetail")
    @NeedLogin
    public ResultDto<ApiShopDetailResp> queryDetail() {

        return ThriftResponseHelper.responseInvoke("queryDetail", TracerUtil.getMemberDto(),
            req -> shopService.queryDetail(req)
        );
    }

    @PostMapping(value = "/queryProductShop", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<ApiProductShopInfoResp> queryProductShop(@RequestBody ApiProductShopQueryReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductShop", baseIdReq, req -> {
            req.setUserId(TracerUtil.getMemberDto()== null ? null : TracerUtil.getMemberDto().getId());
            return shopService.queryProductShop(req);
        });
    }

    @PostMapping(value = "/queryMallShopList", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<ApiShopEsCombinationResp> queryMallShopList(@RequestBody ApiShopMallQueryReq request) throws TException {
        LoginMemberDto member = TracerUtil.getMemberDto();
        if (null != member) {
            request.setUserId(member.getId());
        }
        return ThriftResponseHelper.responseInvoke("queryMallShopList", request,
            req -> shopService.queryMallShopList(req)
        );
    }

    @PostMapping(value = "/shopIntroduction", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<ApiShopIntroductionResp> shopIntroduction(@RequestBody BaseIdReq baseIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("shopIntroduction", baseIdReq,
            req -> shopService.shopIntroduction(req, TracerUtil.getMemberDto())
        );
    }

    @PostMapping(value = "/createQrCode", consumes = "application/json")
    public ResultDto<String> createQrCode(@RequestBody ApiCmdCreateQRReq cmdCreateQRReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createQrCode", cmdCreateQRReq,
            req -> shopService.createQrCode(cmdCreateQRReq)
        );
    }

}
