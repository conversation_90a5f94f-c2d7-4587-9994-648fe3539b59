package com.sankuai.shangou.seashop.seller.core.service.promotion.excel.listener;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.seller.core.service.promotion.excel.dto.ExclusivePriceProductExcelDataHolder;
import com.sankuai.shangou.seashop.seller.core.service.promotion.excel.dto.ExclusivePriceProductExcelDto;
import com.sankuai.shangou.seashop.seller.core.service.promotion.excel.po.ExclusivePriceProductExcelPo;

import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@Slf4j
public class ImportExclusivePriceProductListener implements ReadListener<ExclusivePriceProductExcelPo> {

    private final ExclusivePriceProductExcelDataHolder exclusivePriceProductExcelDataHolder = new ExclusivePriceProductExcelDataHolder();

    private final static String TEMPLATE_HEADERS = "商品Id,会员账号,价格";

    /**
     * 金额校验正则
     */
    private static final String AMOUNT_CHECK_REGULAR = "^\\d+(\\.\\d{1,2})?$|^0$";

    @Override
    public void invoke(ExclusivePriceProductExcelPo exclusivePriceProductExcelPo, AnalysisContext analysisContext) {
        ExclusivePriceProductExcelDto exclusivePriceProductExcelDto = ExclusivePriceProductExcelDto.builder()
                .productId(StringUtils.trim(exclusivePriceProductExcelPo.getProductId()))
                .userName(StringUtils.trim(exclusivePriceProductExcelPo.getUserName()))
                .exclusivePrice(StringUtils.trim(exclusivePriceProductExcelPo.getExclusivePrice()))
                .build();
        StringBuilder msg = new StringBuilder();
        if (StringUtils.isBlank(exclusivePriceProductExcelDto.getProductId())) {
            msg.append("商品Id不能为空;");
        } else if (!NumberUtil.isLong(exclusivePriceProductExcelDto.getProductId())) {
            msg.append("商品Id格式不正确;");
        }
        if (StringUtils.isBlank(exclusivePriceProductExcelDto.getUserName())) {
            msg.append("会员账号不能为空;");
        }
        if (StringUtils.isBlank(exclusivePriceProductExcelDto.getExclusivePrice())) {
            msg.append("价格不能为空;");
        } else if (!exclusivePriceProductExcelDto.getExclusivePrice().matches(AMOUNT_CHECK_REGULAR)) {
            msg.append("价格格式不正确;");
        }
        exclusivePriceProductExcelDto.setMsg(msg.toString());
        exclusivePriceProductExcelDataHolder.getProductList().add(exclusivePriceProductExcelDto);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("解析完成:analysisContext={}", analysisContext);
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析表头:headMap={},context={}", headMap, context);
        List<String> curHeaderList = headMap.values().stream().map(CellData::getStringValue).collect(Collectors.toList());
        String curHeaderStr = String.join(",", Optional.of(curHeaderList).orElse(Collections.emptyList()));
        if (!curHeaderStr.equals(TEMPLATE_HEADERS)) {
            throw new BusinessException("导入模板不正确,请下载最新模板!");
        }
    }

    public ExclusivePriceProductExcelDataHolder getData() {
        return exclusivePriceProductExcelDataHolder;
    }

}
