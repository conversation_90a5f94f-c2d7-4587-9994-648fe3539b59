package com.sankuai.shangou.seashop.seller.core.thrift.impl.system.setting;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.thrift.core.ArticleQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.SystemStyleEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.*;
import com.sankuai.shangou.seashop.core.util.AuthUtil;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiProductSettingResp;
import com.sankuai.shangou.seashop.seller.common.util.PageNameHelp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.enums.ApiRiskPageTypeEnum;
import com.sankuai.shangou.seashop.seller.thrift.core.request.enums.CategoryTypeEnum;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.*;
import com.sankuai.shangou.seashop.seller.thrift.core.response.system.ApiBaseShopSitSettingRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sellerApi/apiSiteSetting")
public class SellerApiSiteSettingController {
    @Resource
    private ArticleQueryFeign articleQueryFeign;
    @Resource
    private SiteSettingQueryFeign siteSettingQueryFeign;

    @GetMapping(value = "/getSetting")
    public ResultDto<ApiBaseSitSettingRes> getSetting() throws TException {
        return ThriftResponseHelper.responseInvoke("getSetting", null, req -> {
            BaseSitSettingRes settingRes = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getSetting());
            return JsonUtil.copy(settingRes, ApiBaseSitSettingRes.class);
        });
    }

    @GetMapping(value = "/getSettled")
    public ResultDto<ApiBaseSettledRes> getSettled() throws TException {
        return ThriftResponseHelper.responseInvoke("getSettled", null, req -> {
            BaseSettledRes result = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getSettled());
            return JsonUtil.copy(result, ApiBaseSettledRes.class);
        });
    }

    @GetMapping(value = "/getProductSettings")
    public ResultDto<ApiProductSettingResp> getProductSettings() throws TException {
        return ThriftResponseHelper.responseInvoke("getProductSettings", null, req -> {
            ProductSettingResp productSettingResp = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getProductSettings());
            return JsonUtil.copy(productSettingResp, ApiProductSettingResp.class);
        });
    }

    @GetMapping(value = "/getAgreement")
    public ResultDto<ApiBaseAgreementRes> getAgreement(@RequestParam int agreementType) throws TException {
        return ThriftResponseHelper.responseInvoke("getAgreement", agreementType, req -> {
            BaseAgreementRes result = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getAgreement(req));
            return JsonUtil.copy(result, ApiBaseAgreementRes.class);
        });
    }

    @PostMapping(value = "/systemNoticeWithPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBaseArticleRes>> systemNoticeWithPage(@RequestBody BasePageReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("systemNoticeWithPage", query, req -> {
            BaseReq categoryQuery = new BaseReq();
            categoryQuery.setId((long) CategoryTypeEnum.PlatformNews.getCode());
            BaseArticleCategoryRes categoryRes = ThriftResponseHelper.executeThriftCall(() ->
                    articleQueryFeign.getBaseArticleCategoryById(categoryQuery));

            if (categoryRes == null) {
                return null;
            }

            BaseArticleQueryReq request = new BaseArticleQueryReq();
            request.setPageNo(req.getPageNo());
            request.setCategoryId(categoryRes.getId());
            request.setPageSize(req.getPageSize());
            request.setSortList(req.getSortList());
            request.setIsRelease(true);

            BasePageResp<BaseArticleRes> result = ThriftResponseHelper.executeThriftCall(() -> articleQueryFeign.queryWithPage(request));
            return PageResultHelper.transfer(result, ApiBaseArticleRes.class);

        });
    }


    @GetMapping(value = "/getShopSettings")
    public ResultDto<ApiBaseShopSitSettingRes> getShopSettings() throws TException {
        return ThriftResponseHelper.responseInvoke("getShopSettings", null, req -> {
            BaseShopSitSettingRes shopSitSettingRes = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getShopSettings());
            return JsonUtil.copy(shopSitSettingRes, ApiBaseShopSitSettingRes.class);
        });
    }

    @GetMapping(value = "/getShopTheme")
    public ResultDto<String> getShopTheme() {
        return ThriftResponseHelper.responseInvoke("getShopTheme", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.SHOPTHEME));
        });
    }

    @GetMapping(value = "/getProductCatetory")
    public ResultDto<String> getProductCatetory() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.PRODUCTCATETORY));
        });
    }

    @GetMapping(value = "/getUserCenter")
    public ResultDto<String> getUserCenter() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.USERCENTER));
        });
    }

    @GetMapping(value = "/getPCShopTheme")
    public ResultDto<String> getPCShopTheme() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.PCSHOPTHEME));
        });
    }

}
