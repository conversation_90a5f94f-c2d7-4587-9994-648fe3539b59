package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiDeductionReq;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositDetailCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.DeductionReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiCashDepositDetail")
public class MApiCashDepositDetailCmdController {

    @Resource
    private CashDepositDetailCmdFeign cashDepositDetailCmdFeign;

    @PostMapping(value = "/deduction", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deduction(@RequestBody ApiDeductionReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deduction", request, req -> {
                req.checkParameter();
                DeductionReq deductionReq = JsonUtil.copy(req, DeductionReq.class);
                LoginManagerDto loginDto = TracerUtil.getManagerDto();
                deductionReq.setOperator(loginDto.getName());
                deductionReq.setOperationUserId(loginDto.getId());
                deductionReq.checkParameter();
                return ThriftResponseHelper.executeThriftCall(() -> cashDepositDetailCmdFeign.deduction(deductionReq));
            }
        );
    }
}
