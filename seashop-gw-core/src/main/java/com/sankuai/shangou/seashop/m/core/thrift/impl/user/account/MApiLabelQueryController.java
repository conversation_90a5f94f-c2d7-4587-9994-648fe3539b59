package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;


import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiLabelService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryLabelPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiLabelResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiLabel")
public class MApiLabelQueryController {

    @Resource
    private MApiLabelService mApilabelService;

    @PostMapping(value = "/queryLabelPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiLabelResp>> queryLabelPage(@RequestBody ApiQueryLabelPageReq queryLabelPageReq) {
        return ThriftResponseHelper.responseInvoke("queryLabelPage", queryLabelPageReq, req -> mApilabelService.queryLabelPage(queryLabelPageReq));
    }
}
