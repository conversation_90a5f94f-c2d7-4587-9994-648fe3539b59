package com.sankuai.shangou.seashop.seller.core.service.export.handler.order.eo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.export.anno.RowKey;
import com.sankuai.shangou.seashop.base.export.anno.ShouldMerge;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/01/02 10:56
 */
@Getter
@Setter
public class OrderInvoiceEo {

    @RowKey
    @ShouldMerge
    @ExcelProperty(value = "订单编号")
    private String orderId;

    @ShouldMerge
    @ExcelProperty(value = "买家")
    private String userName;

    @ShouldMerge
    @ExcelProperty(value = "发票类型")
    private String invoiceType;

    @ShouldMerge
    @ExcelProperty(value = "发票抬头")
    private String invoiceTitle;

    @ShouldMerge
    @ExcelProperty(value = "发票税号")
    private String invoiceCode;

    @ShouldMerge
    @ExcelProperty(value = "发票内容")
    private String invoiceContext;

    @ExcelProperty(value = {"电子普通发票信息", ""})
    private String elcInvoiceRow1;

    @ExcelProperty(value = {"电子普通发票信息", ""})
    private String elcInvoiceRow2;

    @ExcelProperty(value = {"增值税发票信息", ""})
    private String addedTaxInvoiceRow1;

    @ExcelProperty(value = {"增值税发票信息", ""})
    private String addedTaxInvoiceRow2;

}
