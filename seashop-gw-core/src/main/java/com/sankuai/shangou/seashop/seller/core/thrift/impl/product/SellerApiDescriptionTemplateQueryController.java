package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.DescriptionTemplateQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.QueryDescriptionTemplateDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.QueryDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.DescriptionTemplateDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.DescriptionTemplateListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.dto.DescriptionTemplateDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryDescriptionTemplateReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiDescriptionTemplateDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiDescriptionTemplateListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiDescriptionTemplateDetailDto;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiDescriptionTemplateDto;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/15 9:05
 */
@RestController
@RequestMapping("/sellerApi/apiDescriptionTemplate")
public class SellerApiDescriptionTemplateQueryController {

    @Resource
    private DescriptionTemplateQueryFeign descriptionTemplateQueryFeign;

    @PostMapping(value = "/queryDescriptionTemplateForList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiDescriptionTemplateListResp> queryDescriptionTemplateForList(@RequestBody ApiQueryDescriptionTemplateReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryDescriptionTemplateForList", request, req -> {

            QueryDescriptionTemplateReq remoteReq = JsonUtil.copy(req, QueryDescriptionTemplateReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            remoteReq.setPosition(DescriptionTemplatePositionEnum.getByCode(req.getPositionCode()));
            DescriptionTemplateListResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    descriptionTemplateQueryFeign.queryDescriptionTemplateForList(remoteReq));
            return ApiDescriptionTemplateListResp.builder().templateList(JsonUtil.copyList(resp.getTemplateList(), ApiDescriptionTemplateDto.class)).build();
        });
    }

    @PostMapping(value = "/queryDescriptionTemplateForPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiDescriptionTemplateDto>> queryDescriptionTemplateForPage(@RequestBody ApiQueryDescriptionTemplateReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryDescriptionTemplateForPage", request, req -> {

            QueryDescriptionTemplateReq remoteReq = JsonUtil.copy(req, QueryDescriptionTemplateReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            remoteReq.setPosition(DescriptionTemplatePositionEnum.getByCode(req.getPositionCode()));
            BasePageResp<DescriptionTemplateDto> resp = ThriftResponseHelper.executeThriftCall(() ->
                    descriptionTemplateQueryFeign.queryDescriptionTemplateForPage(remoteReq));
            return PageResultHelper.transfer(resp, ApiDescriptionTemplateDto.class);
        });
    }

    @PostMapping(value = "/queryDescriptionTemplateDetail", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiDescriptionTemplateDetailResp> queryDescriptionTemplateDetail(@RequestBody BaseIdReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryDescriptionTemplateDetail", request, req -> {

            QueryDescriptionTemplateDetailReq remoteReq = new QueryDescriptionTemplateDetailReq();
            remoteReq.setId(req.getId());
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            DescriptionTemplateDetailResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    descriptionTemplateQueryFeign.queryDescriptionTemplateDetail(remoteReq));
            return ApiDescriptionTemplateDetailResp.builder().result(JsonUtil.copy(resp.getResult(), ApiDescriptionTemplateDetailDto.class)).build();
        });
    }
}
