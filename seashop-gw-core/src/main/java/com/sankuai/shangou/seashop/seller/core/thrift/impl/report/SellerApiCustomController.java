package com.sankuai.shangou.seashop.seller.core.thrift.impl.report;

import com.hishop.himall.report.api.request.CustomPageReq;
import com.hishop.himall.report.api.request.CustomRecordPageReq;
import com.hishop.himall.report.api.request.CustomReq;
import com.hishop.himall.report.api.request.ReportQueryReq;
import com.hishop.himall.report.api.response.CustomPageResp;
import com.hishop.himall.report.api.response.CustomRecordPageResp;
import com.hishop.himall.report.api.response.CustomResp;
import com.hishop.himall.report.api.service.ReportCustomFeign;
import com.hishop.starter.util.model.PageResult;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import javax.annotation.Resource;

@RestController
@RequestMapping("/sellerApi/report/custom")
public class SellerApiCustomController {

    @Resource
    private ReportCustomFeign reportCustomFeign;

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/query", consumes = "application/json")
    public ResultDto<BasePageResp<CustomPageResp>> query(@RequestBody CustomPageReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCustomForPage", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportCustomFeign.query(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/record/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BasePageResp<CustomRecordPageResp>> query(@RequestBody CustomRecordPageReq request){
        return ThriftResponseHelper.responseInvoke("queryCustomForPage", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportCustomFeign.query(req));
        });
    }
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/deleteById")
    public ResultDto<Object> delete(@RequestBody BaseIdReq idReq){
        return ThriftResponseHelper.responseInvoke("delete", idReq, req ->
                ThriftResponseHelper.executeThriftCall(() -> reportCustomFeign.delete(req.getId())));
    }
    @NeedLogin(userType = RoleEnum.SHOP)
    @GetMapping(value = "/get/{id}")
    public ResultDto<CustomResp> get(@PathVariable Long id) {
        return ThriftResponseHelper.responseInvoke("get", id, req ->
                ThriftResponseHelper.executeThriftCall(() -> reportCustomFeign.get(id)));
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Object> save(@RequestBody CustomReq request) {
        return ThriftResponseHelper.responseInvoke("get", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportCustomFeign.save(req));
        });
    }

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Long> export(@RequestBody ReportQueryReq req){
        return ThriftResponseHelper.responseInvoke("exportCustom", req, request -> {
            request.setOperatorId(TracerUtil.getShopDto().getManagerId());
            request.setOperatorName(TracerUtil.getShopDto().getManagerName());
            return ThriftResponseHelper.executeThriftCall(() -> reportCustomFeign.export(request));
        });
    }
}
