package com.sankuai.shangou.seashop.m.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiPlatFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.PlatFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleConfigCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/13/013
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiFlashSaleConfig")
public class MApiFlashSaleConfigCmdController {

    @Resource
    private FlashSaleConfigCmdFeign flashSaleConfigCmdFeign;

    @PostMapping(value = "/updatePlatConfig", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> updatePlatConfig(@RequestBody ApiPlatFlashSaleConfigReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updatePlatConfig", request, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() ->
                    flashSaleConfigCmdFeign.updatePlatConfig(JsonUtil.copy(req, PlatFlashSaleConfigReq.class)));
        });
    }
}
