package com.sankuai.shangou.seashop.seller.core.thrift.impl.report;

import com.hishop.himall.report.api.request.ProductReq;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.response.*;
import com.hishop.himall.report.api.service.ReportProductFeign;
import com.hishop.starter.util.model.PageResult;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sellerApi/report/product")
public class SellerApiProductController {

    @Resource
    private ReportProductFeign reportProductFeign;

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<ProductSummaryResp> queryProductSummary(@RequestBody ReportReq request){
        return ThriftResponseHelper.responseInvoke("queryCustomForPage", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportProductFeign.queryProductSummary(req));
        });
    }

    /**
     * 商品走势图
     * @param request
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/visitsEcharts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryVisits(@RequestBody ReportReq request){
        return ThriftResponseHelper.responseInvoke("queryVisits", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportProductFeign.queryVisits(req));
        });
    }

    /**
     * 分类饼图
     * @param request
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/categoryEcharts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<Echarts> queryCategory(@RequestBody ReportReq request){
        return ThriftResponseHelper.responseInvoke("queryCategory", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportProductFeign.queryCategory(req));
        });
    }

    /**
     * 商品分析列表
     * @param request
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<PageResult<ProductResp>> queryProducts(@RequestBody ProductReq request){
        return ThriftResponseHelper.responseInvoke("queryProducts", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportProductFeign.queryProducts(req));
        });
    }

    /**
     * 商品分析导出
     * @param request
     * @return
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultDto<BaseResp> exportProduct(@RequestBody ProductReq request){
        return ThriftResponseHelper.responseInvoke("exportProduct", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> reportProductFeign.exportProduct(req));
        });
    }
}
