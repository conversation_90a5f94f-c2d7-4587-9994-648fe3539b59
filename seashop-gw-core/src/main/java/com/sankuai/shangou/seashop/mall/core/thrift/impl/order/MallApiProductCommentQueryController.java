package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import cn.hutool.core.util.DesensitizedUtil;
import com.facebook.swift.service.ThriftMethod;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiMallQueryProductCommentReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiProductCommentResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.util.HideUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.enums.CommentEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.MallQueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentSummaryReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentImageResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentSummaryResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/21 19:26
 */
@RestController
@RequestMapping("/mallApi/apiProductComment")
@Slf4j
public class MallApiProductCommentQueryController {

    @Resource
    private ProductCommentQueryFeign productCommentQueryFeign;

    @PostMapping(value = "/queryProductCommentForMall", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<BasePageResp<ApiProductCommentResp>> queryProductCommentForMall(@RequestBody ApiMallQueryProductCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductCommentForMall", request, req -> {
            req.checkParameter();

            MallQueryProductCommentReq remoteReq = JsonUtil.copy(req, MallQueryProductCommentReq.class);
            remoteReq.setStatus(CommentEnum.MallCommentStatus.getByCode(req.getStatusCode()));
            BasePageResp<ProductCommentResp> resp = ThriftResponseHelper.executeThriftCall(() -> productCommentQueryFeign.queryProductCommentForMall(remoteReq));
            return PageResultHelper.transfer(resp, ApiProductCommentResp.class, (source, target) -> {
                target.setCommentImageList(getCommentImageList(source.getFirstCommentImageList()));
                target.setAppendImageList(getCommentImageList(source.getAppendCommentImageList()));
                target.setUserName(HideUtil.hideUserName(source.getUserName()));
                target.setUserMobile(DesensitizedUtil.mobilePhone(source.getUserMobile()));
                target.setUserNickName(HideUtil.hideUserName(source.getUserNickName()));
            });
        });
    }

    @ThriftMethod
    @GetMapping(value = "/queryProductCommentSummary")
    public ResultDto<ProductCommentSummaryResp> queryProductCommentSummary(@RequestParam Long productId) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductCommentSummary", null, req -> {

            QueryProductCommentSummaryReq remoteReq = new QueryProductCommentSummaryReq();
            remoteReq.setProductId(productId);
            return ThriftResponseHelper.executeThriftCall(() -> productCommentQueryFeign.queryProductCommentSummary(remoteReq));
        });
    }

    /**
     * 提取评论图片
     *
     * @param imageList 评论图片列表
     * @return 评论图片列表
     */
    private List<String> getCommentImageList(List<ProductCommentImageResp> imageList) {
        if (CollectionUtils.isEmpty(imageList)) {
            return Collections.EMPTY_LIST;
        }
        return imageList.stream().map(ProductCommentImageResp::getCommentImage).collect(Collectors.toList());
    }
}
