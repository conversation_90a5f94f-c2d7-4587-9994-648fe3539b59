package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiAppendOrderCommentReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiDeleteOrderCommentReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.ApiSaveOrderCommentReq;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCommentCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.AppendOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.DeleteOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.SaveOrderCommentReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/21 19:06
 */
@RestController
@RequestMapping("/mallApi/apiOrderComment")
@Slf4j
public class MallApiOrderCommentCmdController {

    @Resource
    private OrderCommentCmdFeign orderCommentCmdFeign;

    @PostMapping(value = "/saveOrderComment", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> saveOrderComment(@RequestBody ApiSaveOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("saveOrderComment", request, req -> {
            req.checkParameter();

            SaveOrderCommentReq remoteReq = JsonUtil.copy(req, SaveOrderCommentReq.class);
            remoteReq.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> orderCommentCmdFeign.saveOrderComment(remoteReq));
        });
    }

    @PostMapping(value = "/appendOrderComment", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> appendOrderComment(@RequestBody ApiAppendOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("appendOrderComment", request, req -> {
            req.checkParameter();

            AppendOrderCommentReq remoteReq = JsonUtil.copy(req, AppendOrderCommentReq.class);
            LoginMemberDto loginDto = TracerUtil.getMemberDto();;
            remoteReq.setUserId(loginDto.getId());
            return ThriftResponseHelper.executeThriftCall(() -> orderCommentCmdFeign.appendOrderComment(remoteReq));
        });
    }

    @PostMapping(value = "/deleteOrderComment", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> deleteOrderComment(@RequestBody ApiDeleteOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteOrderComment", request, req -> {
            req.checkParameter();

            DeleteOrderCommentReq remoteReq = JsonUtil.copy(req, DeleteOrderCommentReq.class);
            remoteReq.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> orderCommentCmdFeign.deleteOrderCommentForBuyer(remoteReq));
        });
    }
}
