package com.sankuai.shangou.seashop.m.core.thrift.impl.pay;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.pay.ApiChannelConfigQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.pay.ApiChannelConfigListResp;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ChannelConfigQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ChannelConfigListResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.ChannelConfigQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiChannelConfig")
public class MApiChannelConfigQueryController {

    @Resource
    private ChannelConfigQueryFeign channelConfigQueryFeign;

    @PostMapping(value = "/queryList", consumes = "application/json")
    public ResultDto<ApiChannelConfigListResp> queryList(@RequestBody ApiChannelConfigQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryList", request, req -> {
            ChannelConfigListResp channelConfigListResp = ThriftResponseHelper.executeThriftCall(() -> channelConfigQueryFeign.queryList(JsonUtil.copy(req,
                ChannelConfigQueryReq.class)));
            return JsonUtil.copy(channelConfigListResp, ApiChannelConfigListResp.class);
        });
    }
}
