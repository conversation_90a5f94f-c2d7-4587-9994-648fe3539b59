package com.sankuai.shangou.seashop.seller.core.service.finance.impl;

import java.util.Arrays;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementOrderResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementResp;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.seller.core.service.finance.SellerApiPendingSettlementService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiShopIdReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiPendingSettlementOrderResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiPendingSettlementResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class SellerApiPendingSettlementServiceImpl implements SellerApiPendingSettlementService {

    @Resource
    private SellerFinanceRemoteService sellerFinanceRemoteService;
    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;

    @Override
    public ApiPendingSettlementResp getTotalSettlementAmount(ApiShopIdReq request) {
        PendingSettlementQryReq req = new PendingSettlementQryReq();
        req.setShopIdList(Arrays.asList(request.getShopId()));
        BasePageResp<PendingSettlementResp> pendingSettlementPage = sellerFinanceRemoteService.getPendingSettlementList(req);
        if (null != pendingSettlementPage && CollUtil.isNotEmpty(pendingSettlementPage.getData())) {
            PendingSettlementResp pendingSettlementResp = pendingSettlementPage.getData().get(0);
            return JsonUtil.copy(pendingSettlementResp, ApiPendingSettlementResp.class);
        }
        return null;
    }

    @Override
    public BasePageResp<ApiPendingSettlementOrderResp> pageList(ApiPendingSettlementOrderQryReq request) {
        BasePageResp<PendingSettlementOrderResp> pendingSettlementOrderPage = sellerFinanceRemoteService.pendingSettlementOrderPageList(JsonUtil.copy(request, PendingSettlementOrderQryReq.class));
        return PageResultHelper.transfer(pendingSettlementOrderPage, ApiPendingSettlementOrderResp.class);
    }

    @Override
    public ApiPendingSettlementOrderResp getDetailByOrderId(ApiOrderIdQryReq request, Long shopId) {
        PendingSettlementOrderResp pendingSettlementOrderResp = sellerFinanceRemoteService.getPendingSettlementOrderDetailByOrderId(request.getOrderId());
        if (null == pendingSettlementOrderResp) {
            return null;
        }
        if (!shopId.equals(pendingSettlementOrderResp.getShopId())) {
            throw new BusinessException("只能查看自己店铺的数据");
        }
        return JsonUtil.copy(pendingSettlementOrderResp, ApiPendingSettlementOrderResp.class);
    }

    @Override
    public void exportPendSettleList(ApiPendingSettlementOrderQryReq request, LoginShopDto loginShopDto) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.PEND_SETTLE_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getShopDto().getManagerId());
        createExportTaskBo.setOperatorName(TracerUtil.getShopDto().getManagerName());
//        createExportTaskBo.setOperatorId(101L);
//        createExportTaskBo.setOperatorName("测试");

        sellerExportTaskBiz.create(createExportTaskBo);
    }
}
