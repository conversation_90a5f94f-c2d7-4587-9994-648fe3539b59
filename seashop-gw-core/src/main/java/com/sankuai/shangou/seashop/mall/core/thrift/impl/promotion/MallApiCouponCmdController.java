package com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiCouponReceiveReq;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponReceiveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: liuhaox
 * @date: 2023/11/7/007
 * @description:
 */
@RestController
@RequestMapping("/mallApi/apiCoupon")
public class MallApiCouponCmdController {

    @Resource
    private CouponCmdFeign couponCmdFeign;

    @PostMapping(value = "/receiveCoupon", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> receiveCoupon(@RequestBody ApiCouponReceiveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("receiveCoupon", request, req -> {
            LoginMemberDto member = TracerUtil.getMemberDto();
            CouponReceiveReq couponReceiveReq = JsonUtil.copy(req, CouponReceiveReq.class);
            couponReceiveReq.setUserId(member.getId());
            couponReceiveReq.setUserName(null != member.getName() ? member.getName() : "");
            couponReceiveReq.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> couponCmdFeign.receiveCoupon(couponReceiveReq));
        });
    }
}
