package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.order.MOrderService;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiPlatformIndexTradeDataStatsResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mApi/apiOrderStatistics")
@Slf4j
public class MApiOrderStatisticsQueryController {

    @Resource
    private MOrderService mOrderService;

    @GetMapping(value = "/statsPlatformIndexTradeData")
    public ResultDto<ApiPlatformIndexTradeDataStatsResp> statsPlatformIndexTradeData() throws TException {
        log.info("【订单统计】平台首页交易数据统计");
        return ThriftResponseHelper.responseInvoke("statsPlatformIndexTradeData", null, func ->
            JsonUtil.copy(mOrderService.statsPlatformIndexTradeData(), ApiPlatformIndexTradeDataStatsResp.class)
        );
    }
}
