package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiQueryBusinessCategoryPageReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiBusinessCategoryResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiQueryLastCategoryResp;
import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.core.service.user.shop.MallApiBusinessCategoryService;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryLastCategoryResp;

@RestController
@RequestMapping("/mallApi/apiBusinessCategory")
public class MallApiBusinessCategoryQueryController {
    @Resource
    private MallApiBusinessCategoryService businessCategoryService;

    @PostMapping(value = "/queryPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBusinessCategoryResp>> queryPage(@RequestBody ApiQueryBusinessCategoryPageReq queryBusinessCategoryPageReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryPage", queryBusinessCategoryPageReq, func -> {
            // 参数对象转换
            return businessCategoryService.queryPage(func);
        });
    }

    @PostMapping(value = "/queryById", consumes = "application/json")
    public ResultDto<ApiQueryLastCategoryResp> queryById(@RequestBody BaseBatchIdReq id) throws TException {
        return ThriftResponseHelper.responseInvoke("queryById", id, func -> {
            // 参数对象转换
            QueryLastCategoryResp resp = businessCategoryService.queryById(func);
            return JsonUtil.copy(resp, ApiQueryLastCategoryResp.class);
        });
    }
}
