package com.sankuai.shangou.seashop.m.core.service.product;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryProductReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiProductPageResp;

/**
 * <AUTHOR>
 * @date 2024/01/02 17:47
 */
public interface MProductService {

    void exportProduct(ApiQueryProductReq request);

    /**
     * 查询商品
     *
     * @param request 查询参数
     * @return 商品列表
     */
    BasePageResp<ApiProductPageResp> queryProduct(ApiQueryProductPromotionExtReq request);

}
