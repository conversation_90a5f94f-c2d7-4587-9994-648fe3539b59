package com.sankuai.shangou.seashop.mall.core.service.export.handler.order.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.mall.common.remote.order.MallOrderRemoteService;
import com.sankuai.shangou.seashop.mall.core.service.export.handler.order.eo.UserPurchaseStatsExportEo;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserPurchaseSkuDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.StatsUserPurchaseSkuReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.StatsUserPurchaseSkuResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class UserPurchaseSkuWrapper extends PageExportWrapper<UserPurchaseStatsExportEo, StatsUserPurchaseSkuReq> {

    private final MallOrderRemoteService mallOrderRemoteService = SpringUtil.getBean(MallOrderRemoteService.class);

    @Override
    public List<UserPurchaseStatsExportEo> getPageList(StatsUserPurchaseSkuReq param) {
        log.info("【商家采购统计】查询采购统计导出参数:{}", JsonUtil.toJsonString(param));
        StatsUserPurchaseSkuResp resp = mallOrderRemoteService.statsUserPurchaseSku(param);
        BasePageResp<UserPurchaseSkuDto> pageData;
        if (resp == null || (pageData = resp.getPageData()) == null || CollUtil.isEmpty(pageData.getData())) {
            return null;
        }
        return JsonUtil.copyList(pageData.getData(), UserPurchaseStatsExportEo.class, (from, to) -> {
            if (CollUtil.isNotEmpty(from.getSkuDescList())) {
                to.setSkuDesc(String.join(",", from.getSkuDescList()));
            }
        });
    }
}
