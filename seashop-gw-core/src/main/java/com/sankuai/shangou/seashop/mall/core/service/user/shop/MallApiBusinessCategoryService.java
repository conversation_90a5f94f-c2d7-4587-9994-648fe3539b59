package com.sankuai.shangou.seashop.mall.core.service.user.shop;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdBusinessCategoryReqList;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiQueryBusinessCategoryPageReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiBusinessCategoryResp;
import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryLastCategoryResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MallApiBusinessCategoryService {
    @Resource
    private BusinessCategoryQueryFeign businessCategoryQueryFeign;
    @Resource
    private BusinessCategoryCmdFeign businessCategoryCmdFeign;

    public BasePageResp<ApiBusinessCategoryResp> queryPage(ApiQueryBusinessCategoryPageReq queryBusinessCategoryPageReq) {
        QueryBusinessCategoryPageReq queryBusinessCategoryPageReq1 = JsonUtil.copy(queryBusinessCategoryPageReq, QueryBusinessCategoryPageReq.class);
        BasePageResp<BusinessCategoryResp> businessCategoryPageResp = ThriftResponseHelper.executeThriftCall(() ->
                businessCategoryQueryFeign.queryPage(queryBusinessCategoryPageReq1));;
        return PageResultHelper.transfer(businessCategoryPageResp, ApiBusinessCategoryResp.class);
    }

    public BaseResp updateCategory(ApiCmdBusinessCategoryReqList reqList) {
        CmdBusinessCategoryReqList cmdBusinessCategoryReqList = JsonUtil.copy(reqList, CmdBusinessCategoryReqList.class);
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryCmdFeign.updateBusinessCategory(cmdBusinessCategoryReqList));
    }

    public QueryLastCategoryResp queryById(BaseBatchIdReq func) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryQueryFeign.queryById(func));
    }
}
