package com.sankuai.shangou.seashop.mall.core.service.order.order.impl;

import java.util.Date;
import java.util.UUID;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiStatsPurchaseSkuExportResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.OrderExportResp;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.handler.ExecuteResult;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.base.export.handler.ExportTaskHandler;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.mall.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.mall.core.service.order.order.MallOrderService;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryUserOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.StatsUserPurchaseSkuReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MallOrderServiceImpl implements MallOrderService {

    @Resource
    private ExportTaskHandler exportTaskHandler;
    @Resource
    @Lazy
    private S3plusStorageService s3plusStorageService;
    @Value("${s3plus.hostName:}")
    private String s3plusHostName;

    @Resource
    private StorageClient storageClient;

    @Override
    public OrderExportResp exportOrder(QueryUserOrderReq queryReq) {
        ExportTask task = new ExportTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setTaskType(ExportTaskType.ORDER.getType());
        task.setExecuteParam(JsonUtil.toJsonString(queryReq));
        task.setTaskDate(new Date());
        log.info("【订单】导出订单参数:{}", JsonUtil.toJsonString(task));
        ExecuteResult exportResult = exportTaskHandler.execute(task);
        log.info("【订单】导出订单结果:{}", JsonUtil.toJsonString(exportResult));
        if (!exportResult.isSuccess()) {
            throw new BusinessException("订单导出失败");
        }
        String downloadUrl = storageClient.formatUrl(exportResult.getFilePath());
        return new OrderExportResp(downloadUrl);
    }

    @Override
    public ApiStatsPurchaseSkuExportResp exportUserPurchaseSku(StatsUserPurchaseSkuReq queryReq) {
        ExportTask task = new ExportTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setTaskType(ExportTaskType.USER_PURCHASE_SKU.getType());
        task.setExecuteParam(JsonUtil.toJsonString(queryReq));
        task.setTaskDate(new Date());
        log.info("【订单】导出订单商家采购统计参数:{}", JsonUtil.toJsonString(task));
        ExecuteResult exportResult = exportTaskHandler.execute(task);
        log.info("【订单】导出订单商家采购统计结果:{}", JsonUtil.toJsonString(exportResult));
        if (!exportResult.isSuccess()) {
            throw new BusinessException("订单商家采购统计导出失败");
        }
        String downloadUrl = storageClient.formatUrl(exportResult.getFilePath());
        return new ApiStatsPurchaseSkuExportResp(downloadUrl);
    }
}
