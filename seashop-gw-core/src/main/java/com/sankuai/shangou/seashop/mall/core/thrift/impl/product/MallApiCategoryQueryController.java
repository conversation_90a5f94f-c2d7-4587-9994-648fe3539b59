package com.sankuai.shangou.seashop.mall.core.thrift.impl.product;


import cn.hutool.core.util.ObjectUtil;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryCategoryListReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryCategoryTreeReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiCategoryListResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiCategoryResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiCategoryTreeResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.product.MallCategoryRemoteService;
import com.sankuai.shangou.seashop.mall.common.remote.product.model.RemoteSimpleCategoryBo;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ShowStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/28 9:01
 */
@RestController
@RequestMapping("/mallApi/apiCategory")
public class MallApiCategoryQueryController {

    @Resource
    private MallCategoryRemoteService mallCategoryRemoteService;


    @PostMapping(value = "/queryCategoryList", consumes = "application/json")
    public ResultDto<ApiCategoryListResp> queryCategoryList(@RequestBody ApiQueryCategoryListReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryList", request, req -> {

            List<CategoryResp> categoryList = mallCategoryRemoteService.getCategoryList(req.getParentId(), Boolean.TRUE);
            return ApiCategoryListResp.builder().categoryList(JsonUtil.copyList(categoryList, ApiCategoryResp.class)).build();
        });
    }

    @PostMapping(value = "/queryCategoryTree", consumes = "application/json")
    public ResultDto<ApiCategoryTreeResp> queryCategoryTree(@RequestBody ApiQueryCategoryTreeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTree", request, req -> {

            QueryCategoryReq remoteReq = JsonUtil.copy(req, QueryCategoryReq.class);
            remoteReq.setShowStatus(ObjectUtil.defaultIfNull(remoteReq.getShowStatus(), ShowStatusEnum.SHOW_OPEN.getValue()));
            List<RemoteSimpleCategoryBo> categoryList = mallCategoryRemoteService.getCategoryTree(remoteReq);
            return ApiCategoryTreeResp.builder().result(JsonUtil.toJsonString(categoryList)).build();
        });
    }

    @PostMapping(value = "/queryCategoryTreeHideNoThreeLevel", consumes = "application/json")
    public ResultDto<ApiCategoryTreeResp> queryCategoryTreeHideNoThreeLevel(@RequestBody ApiQueryCategoryTreeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTreeHideNoThreeLevel", request, req -> {

            QueryCategoryReq remoteReq = JsonUtil.copy(req, QueryCategoryReq.class);
            remoteReq.setShowStatus(ObjectUtil.defaultIfNull(remoteReq.getShowStatus(), ShowStatusEnum.SHOW_OPEN.getValue()));
            List<RemoteSimpleCategoryBo> categoryList = mallCategoryRemoteService.queryCategoryTreeHideNoThreeLevel(remoteReq);
            return ApiCategoryTreeResp.builder().result(JsonUtil.toJsonString(categoryList)).build();
        });
    }
}
