package com.sankuai.shangou.seashop.seller.core.service.export.handler.refund.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderRefundEo {

    @ExcelProperty(value = "售后编号")
    private String refundId;
    @ExcelProperty(value = "订单号")
    private String orderId;
    // 如果是整单，显示【订单所有商品】
    @ExcelProperty(value = "商品")
    private String productDesc;
    @ExcelProperty(value = "供应商")
    private String shopName;
    @ExcelProperty(value = "买家")
    private String userName;
    @ExcelProperty(value = "联系人")
    private String contactPerson;
    @ExcelProperty(value = "联系方式")
    private String contactCellPhone;
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "申请日期")
    private Date applyDate;
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "退款日期")
    private Date managerConfirmDate;
    @ExcelProperty(value = "供应商处理")
    private String sellerRemark;
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ExcelProperty(value = "退款状态")
    private String refundStatusDesc;
    @ExcelProperty(value = "退款方式")
    private String refundPayTypeDesc;
    @ExcelProperty(value = "买家退款理由")
    private String reason;
    @ExcelProperty(value = "退款凭证")
    private String certPicDesc;
    @ExcelProperty(value = "说明")
    private String reasonDetail;
    @ExcelProperty(value = "平台备注")
    private String managerRemark;

}
