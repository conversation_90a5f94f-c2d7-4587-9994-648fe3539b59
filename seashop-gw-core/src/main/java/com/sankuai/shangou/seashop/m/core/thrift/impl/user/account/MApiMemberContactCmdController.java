package com.sankuai.shangou.seashop.m.core.thrift.impl.user.account;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.common.remote.user.MApiMemberRemoteService;
import com.sankuai.shangou.seashop.m.thrift.system.message.request.ApiBindContactCmdReq;
import com.sankuai.shangou.seashop.m.thrift.system.message.request.ApiCheckCodeCmdReq;
import com.sankuai.shangou.seashop.m.thrift.system.message.request.ApiSendCodeCmdReq;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mApi/apiMemberContact")
public class MApiMemberContactCmdController {
    @Resource
    private MApiMemberRemoteService mApiMemberRemoteService;

    @PostMapping(value = "/sendCode", consumes = "application/json")
    public ResultDto<BaseResp> sendCode(@RequestBody ApiSendCodeCmdReq apiSendCodeCmdReq) {
        return ThriftResponseHelper.responseInvoke("sendCode", apiSendCodeCmdReq, req -> mApiMemberRemoteService.sendCode(apiSendCodeCmdReq));
    }

    @PostMapping(value = "/checkCode", consumes = "application/json")
    public ResultDto<BaseResp> checkCode(@RequestBody ApiCheckCodeCmdReq apiCheckCodeCmdReq) {
        return ThriftResponseHelper.responseInvoke("checkCode", apiCheckCodeCmdReq, req -> mApiMemberRemoteService.checkCode(apiCheckCodeCmdReq));
    }

    @PostMapping(value = "/bindContact", consumes = "application/json")
    public ResultDto<BaseResp> bindContact(@RequestBody ApiBindContactCmdReq apiCheckCodeCmdReq) {
        return ThriftResponseHelper.responseInvoke("bindContact", apiCheckCodeCmdReq, req -> mApiMemberRemoteService.bindContact(apiCheckCodeCmdReq));
    }

}
