package com.sankuai.shangou.seashop.m.core.thrift.impl.system.express;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.thrift.core.TradeSettingsCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.TradeSettingsQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.TradeSiteSettingsReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiTradeSiteSettingsReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiTradeSiteSettingsResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * @author： liweisong
 * @create： 2023/11/22 17:22
 */
@RestController
@RequestMapping("/mApi/apiTradeSettings")
public class MApiTradeSettingsCmdController {

    @Resource
    private TradeSettingsCmdFeign tradeSettingsCmdFeign;
    @Resource
    private TradeSettingsQueryFeign tradeSettingsQueryFeign;

    @PostMapping(value = "/addOrUpdateSiteSetting", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> addOrUpdateSiteSetting(@RequestBody ApiTradeSiteSettingsReq tradeSiteSettingsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addOrUpdateSiteSetting", tradeSiteSettingsReq, req -> {
            TradeSiteSettingsReq bean = JsonUtil.copy(req, TradeSiteSettingsReq.class);
            bean.setOperationUserId(TracerUtil.getManagerDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> tradeSettingsCmdFeign.addOrUpdateSiteSetting(bean));
        });
    }

    @GetMapping(value = "/queryTradeSiteSetting")
    public ResultDto<ApiTradeSiteSettingsResp> queryTradeSiteSetting() throws TException {
        return ThriftResponseHelper.responseInvoke("queryTradeSiteSetting", null, req -> {
            return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> tradeSettingsQueryFeign.queryTradeSiteSetting()), ApiTradeSiteSettingsResp.class);
        });
    }
}
