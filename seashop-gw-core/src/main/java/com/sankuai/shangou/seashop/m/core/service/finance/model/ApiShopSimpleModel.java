package com.sankuai.shangou.seashop.m.core.service.finance.model;

import java.math.BigDecimal;

import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@ThriftStruct
@ToString
@TypeDoc(description = "店铺简单查询返回对象")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ApiShopSimpleModel extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "最大保证金")
    private BigDecimal maxCashDeposit;
}
