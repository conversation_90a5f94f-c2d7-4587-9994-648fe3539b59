package com.sankuai.shangou.seashop.seller.core.service.promotion.impl;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.seller.core.service.promotion.SellerApiCouponRecordService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiCouponRecordQueryReq;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/2/23/023
 * @description:
 */
@Service
@Slf4j
public class SellerApiCouponRecordServiceImpl implements SellerApiCouponRecordService {

    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;

    @Override
    public void exportCouponRecord(ApiCouponRecordQueryReq request, LoginShopDto shopInfo) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.COUPON_RECORD_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getShopDto().getManagerId());
        createExportTaskBo.setOperatorName(TracerUtil.getShopDto().getManagerName());
//        createExportTaskBo.setOperatorId(101L);
//        createExportTaskBo.setOperatorName("测试");

        sellerExportTaskBiz.create(createExportTaskBo);
    }
}
