package com.sankuai.shangou.seashop.report.core.thrift.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hishop.himall.report.api.request.*;
import com.hishop.himall.report.api.service.ReportCustomFeign;
import com.hishop.starter.util.json.JsonUtil;
import com.hishop.starter.web.exception.ExceptionMapper;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.config.LoginSecurityConfig;
import com.sankuai.shangou.seashop.base.security.utils.TokenUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.core.commmon.auth.AuthHelper;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/report")
public class ReportController {

    private static final String HOST = "himall-report";
    private static final String REPORT_APP_NAME = "himall-report";

    private final Environment environment;

    private final AuthHelper authHelper;

    private final RestTemplate restTemplate;

    private final ExceptionMapper exceptionMapper;

    private SellerExportTaskBiz sellerExportTaskBiz;
    private ReportCustomFeign reportCustomFeign;

    public ReportController(Environment environment,
                            AuthHelper authHelper,
                            RestTemplate restTemplate,
                            ExceptionMapper exceptionMapper,
                            SellerExportTaskBiz sellerExportTaskBiz,
                            ReportCustomFeign reportCustomFeign) {
        this.environment = environment;
        this.authHelper = authHelper;
        this.restTemplate = restTemplate;
        this.exceptionMapper = exceptionMapper;
        this.sellerExportTaskBiz = sellerExportTaskBiz;
        this.reportCustomFeign = reportCustomFeign;
    }

    @RequestMapping("/**")
    public String httpRequest(HttpServletRequest request) throws IOException {
        HttpMethod httpMethod = HttpMethod.resolve(request.getMethod().toUpperCase(Locale.ROOT));
        if (httpMethod == null) {
            exceptionMapper.throwException(-1, "only POST or PUT methods are supported");
        }
        String tokenValue = TokenUtil.getRequestToken(LoginSecurityConfig.TOKEN_NAME);
        if (StringUtils.isBlank(tokenValue)) {
            throw new LoginException(LoginErrorEnum.INVALID_TOKEN);
        }
        // 获取 登录用户信息
        LoginBaseDto baseAccountByToken = authHelper.getBaseAccountByToken(tokenValue);
        if (baseAccountByToken == null) {
            throw new LoginException(LoginErrorEnum.INVALID_TOKEN);
        }
        RoleEnum roleType = baseAccountByToken.getRoleType();
        ArrayList<RoleEnum> roleEnums = CollectionUtil.newArrayList(RoleEnum.MANAGER, RoleEnum.SHOP);
        if (!roleEnums.contains(roleType)){
            throw new LoginException(LoginErrorEnum.INVALID_TOKEN);
        }

        if (RoleEnum.MANAGER.equals(roleType)) {
            baseAccountByToken.setShopId(0L);
        }

        String applicationName = environment.getProperty("spring.application.name");
        String url = request.getRequestURI().replaceFirst("/" + applicationName, "/" + REPORT_APP_NAME);
        String queryString = request.getQueryString();
        if (StrUtil.isNotBlank(queryString)) {
            url = url + "?" + queryString;
        }
        //get post 转发
        url = "http://" + HOST + url;
        log.info("请求报表服务接口：{}", url);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        ObjectNode jsonNode = null;
        if (HttpMethod.POST.equals(httpMethod) || HttpMethod.PUT.equals(httpMethod)) {
            if (request.getContentLength() > 0) {
                jsonNode = (ObjectNode) JsonUtil.parse(request.getInputStream());
            } else {
                jsonNode = (ObjectNode) JsonUtil.parse("{}");
            }
            //注入登录用户 id name shopId
            jsonNode.put("operatorId", baseAccountByToken.getId());
            jsonNode.put("operatorName", baseAccountByToken.getName());
            jsonNode.put("operatorShopId", baseAccountByToken.getShopId());
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        }
        //header注入登录用户信息
        Map<String, Object> userMap = new HashMap<>(8);
        userMap.put("operatorId", baseAccountByToken.getId());
        userMap.put("operatorName", URLEncoder.encode(baseAccountByToken.getName(), "UTF-8"));
        userMap.put("operatorShopId", baseAccountByToken.getShopId());
        headers.add("hm_user", JsonUtil.toJSONString(userMap));
        log.info("请求报表服务接口参数：{}", jsonNode == null ? null : jsonNode.toString());
        RequestEntity<String> requestEntity = new RequestEntity<>(jsonNode == null ? null : jsonNode.toString(), headers, httpMethod, URI.create(url));
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, Objects.requireNonNull(httpMethod), requestEntity, String.class);
        return responseEntity.getBody();
    }

    @PostMapping(value = "/product/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportProduct(@RequestBody ProductReq req) {
        return ThriftResponseHelper.responseInvoke("exportProduct", req, fun -> {

            CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
            createExportTaskBo.setTaskType(ExportTaskType.PRODUCT_REPORT);
            createExportTaskBo.setExecuteParam(req);
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            createExportTaskBo.setOperatorId(loginDto.getId());
            createExportTaskBo.setOperatorName(loginDto.getName());
            sellerExportTaskBiz.create(createExportTaskBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/user/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportUser(@RequestBody UserReq req) {
        return ThriftResponseHelper.responseInvoke("exportUser", req, fun -> {

            CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
            createExportTaskBo.setTaskType(ExportTaskType.USER_REPORT);
            createExportTaskBo.setExecuteParam(req);
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            createExportTaskBo.setOperatorId(loginDto.getId());
            createExportTaskBo.setOperatorName(loginDto.getName());
            sellerExportTaskBiz.create(createExportTaskBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/trade/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportTrade(@RequestBody TradeReq req) {
        return ThriftResponseHelper.responseInvoke("exportTrade", req, fun -> {

            CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
            createExportTaskBo.setTaskType(ExportTaskType.TRADE_REPORT);
            createExportTaskBo.setExecuteParam(req);
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            createExportTaskBo.setOperatorId(loginDto.getId());
            createExportTaskBo.setOperatorName(loginDto.getName());
            sellerExportTaskBiz.create(createExportTaskBo);
            return BaseResp.of();
        });
    }

    @GetMapping(value = "/custom/export")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportCustom(@RequestParam Long id) {
        return ThriftResponseHelper.responseInvoke("exportCustom", null, fun -> {

            LoginManagerDto loginDto = TracerUtil.getManagerDto();

            ReportQueryReq exportReq = new ReportQueryReq();
            exportReq.setReportId(id);
            exportReq.setOperatorId(loginDto.getId());
            exportReq.setOperatorName(loginDto.getName());
            Long exportId = ThriftResponseHelper.executeThriftCall(() -> reportCustomFeign.export(exportReq));

            CustomProcessReq processReq = new CustomProcessReq();
            processReq.setId(exportId);
            CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
            createExportTaskBo.setTaskType(ExportTaskType.CUSTOM_REPORT);
            createExportTaskBo.setExecuteParam(processReq);
            createExportTaskBo.setOperatorId(loginDto.getId());
            createExportTaskBo.setOperatorName(loginDto.getName());
            sellerExportTaskBiz.create(createExportTaskBo);
            return BaseResp.of();
        });
    }

}
