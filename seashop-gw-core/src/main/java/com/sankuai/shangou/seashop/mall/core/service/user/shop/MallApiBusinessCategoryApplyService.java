package com.sankuai.shangou.seashop.mall.core.service.user.shop;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiQueryBusinessCategoryApplyPageReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiBusinessCategoryApplyResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryApplyQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MallApiBusinessCategoryApplyService {
    @Resource
    private BusinessCategoryApplyQueryFeign businessCategoryApplyQueryFeign;


    public BasePageResp<ApiBusinessCategoryApplyResp> queryPage(ApiQueryBusinessCategoryApplyPageReq queryBusinessCategoryApplyPageReq) {
        BasePageResp<BusinessCategoryApplyResp> businessCategoryApplyPageResp = ThriftResponseHelper.executeThriftCall(() ->
                businessCategoryApplyQueryFeign.queryPage(JsonUtil.copy(queryBusinessCategoryApplyPageReq, QueryBusinessCategoryApplyPageReq.class)));
        return PageResultHelper.transfer(businessCategoryApplyPageResp, ApiBusinessCategoryApplyResp.class);
    }
}
