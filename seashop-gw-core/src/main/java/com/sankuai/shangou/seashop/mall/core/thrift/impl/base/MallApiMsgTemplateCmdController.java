package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.ApiCmdWxAppletFormDataReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MsgTemplateCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.CmdWxAppletFormDataReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:06
 */
@RestController
@RequestMapping("/mallApi/apiMsgTemplate")
public class MallApiMsgTemplateCmdController {

    @Resource
    private MsgTemplateCmdFeign msgTemplateCmdFeign;

    @PostMapping(value = "/insertWxAppletFormData", consumes = "application/json")
    public ResultDto<BaseResp> insertWxAppletFormData(@RequestBody ApiCmdWxAppletFormDataReq cmdWxAppletFormDataReq) throws TException {
        return ThriftResponseHelper.responseInvoke("insertWxAppletFormData", cmdWxAppletFormDataReq, req -> {
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> msgTemplateCmdFeign.insertWxAppletFormData(JsonUtil.copy(req, CmdWxAppletFormDataReq.class)));
        });
    }
}
