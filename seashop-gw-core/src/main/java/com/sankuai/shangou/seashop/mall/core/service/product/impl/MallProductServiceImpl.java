package com.sankuai.shangou.seashop.mall.core.service.product.impl;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.enums.PromotionTypeEnum;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryPromotionReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiProductPageResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.mall.core.service.product.MallProductService;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryFeign;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/01/03 10:41
 */
@Service
public class MallProductServiceImpl implements MallProductService {

    @Resource
    private ProductQueryFeign productQueryFeign;
    @Resource
    private FlashSaleQueryFeign flashSaleQueryFeign;

    @Override
    public BasePageResp<ApiProductPageResp> queryProduct(ApiQueryProductPromotionExtReq request) {

        BasePageResp<ApiProductPageResp> resultPage = null;

        ApiQueryPromotionReq promotion = request.getPromotion();
        if (null != promotion) {
            // 如果有营销查询条件，则进行营销查询处理
            PromotionTypeEnum promotionType = PromotionTypeEnum.getByCode(promotion.getPromotionType());
            switch (promotionType) {
                case COUPON:
                    break;
                case FULL_REDUCTION:
                    break;
                case DISCOUNT:
                    break;
                case EXCLUSIVE_PRICE:
                    break;
                case LIMITED_TIME_PURCHASE:
                    List<FlashSaleSimpleResp> flashSaleList = new ArrayList<>();
                    // 限时购处理
                    FlashSaleQueryReq flashSaleQueryReq = new FlashSaleQueryReq();
                    flashSaleQueryReq.setShopId(request.getShopId());
                    flashSaleQueryReq.setStatus(promotion.getPromotionStatus());
                    flashSaleQueryReq.setPageNo(CommonConstant.QUERY_START);
                    flashSaleQueryReq.setPageSize(CommonConstant.QUERY_LIMIT);
                    flashSaleQueryReq.checkParameter();
                    BasePageResp<FlashSaleSimpleResp> flashSalePage = ThriftResponseHelper.executeThriftCall(() ->
                            flashSaleQueryFeign.pageList(flashSaleQueryReq));
                    if (null != flashSalePage && CollUtil.isNotEmpty(flashSalePage.getData())) {
                        flashSaleList.addAll(flashSalePage.getData());
                        if (flashSalePage.getPages() > CommonConstant.QUERY_START) {
                            Integer pages = flashSalePage.getPages();
                            for (int i = CommonConstant.LOOP_START_PAGE_NO; i <= pages; i++) {
                                flashSaleQueryReq.setPageNo(i);
                                BasePageResp<FlashSaleSimpleResp> flashSalePageFor = ThriftResponseHelper.executeThriftCall(() ->
                                        flashSaleQueryFeign.pageList(flashSaleQueryReq));
                                if (null != flashSalePageFor && CollUtil.isNotEmpty(flashSalePageFor.getData())) {
                                    flashSaleList.addAll(flashSalePageFor.getData());
                                }
                            }
                        }
                    }
                    if (CollUtil.isEmpty(flashSaleList)) {
                        // 说明没有限时购活动
                        return PageResultHelper.defaultEmpty(request);
                    }
                    Set<Long> productIdSet = flashSaleList.stream().map(FlashSaleSimpleResp::getProductId).collect(Collectors.toSet());
                    QueryProductReq remoteReq = JsonUtil.copy(request, QueryProductReq.class);
                    remoteReq.setProductIds(new ArrayList<>(productIdSet));

                    BasePageResp<ProductPageResp> respPage = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProduct(remoteReq));
                    resultPage = PageResultHelper.transfer(respPage, ApiProductPageResp.class, resp -> {
                    });
                    break;
                case COMBINATION_PURCHASE:
                    break;
                default:
                    break;
            }
        }
        else {
            QueryProductReq remoteReq = JsonUtil.copy(request, QueryProductReq.class);
            if (null == remoteReq.getStatus()) {
                // 如果没传状态，默认查询销售中的商品
                remoteReq.setStatus(ProductStatusEnum.ON_SALE);
            }
            BasePageResp<ProductPageResp> resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProduct(remoteReq));
            resultPage = PageResultHelper.transfer(resp, ApiProductPageResp.class);
        }
        return resultPage;
    }
}
