package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;


import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.BrandApplyCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.SaveBrandApplyReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiSaveBrandApplyReq;

/**
 * 品牌申请服务
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/sellerApi/apiBrandApply")
public class SellerApiBrandApplyCmdController {

    @Resource
    private BrandApplyCmdFeign brandApplyCmdFeign;

    /**
     * 创建申请品牌记录
     * @param request 创建申请品牌记录请求入参
     * @return 结果
     * @throws TException
     */
    @PostMapping(value = "/createBrandApply", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> createBrandApply(@RequestBody ApiSaveBrandApplyReq request) throws TException {

        LoginShopDto shopInfo = TracerUtil.getShopDto();

        return ThriftResponseHelper.responseInvoke("createBrandApply", request, req -> {
            req.checkParameter();
            SaveBrandApplyReq brandApplyReq = JsonUtil.copy(req, SaveBrandApplyReq.class);
            brandApplyReq.setApplyMode(BrandEnum.ApplyModeEnum.getByCode(req.getApplyModeCode()));
            brandApplyReq.setShopId(shopInfo.getId());
            return ThriftResponseHelper.executeThriftCall(() -> brandApplyCmdFeign.createBrandApply(brandApplyReq));
        });
    }

    /**
     * 编辑申请品牌记录
     * @param request 编辑申请品牌记录请求入参
     * @return 结果
     * @throws TException
     */
    @PostMapping(value = "/updateBrandApply", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateBrandApply(@RequestBody ApiSaveBrandApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateBrandApply", request, req -> {
            req.checkParameter();

            SaveBrandApplyReq brandApplyReq = JsonUtil.copy(req, SaveBrandApplyReq.class);
            brandApplyReq.setApplyMode(BrandEnum.ApplyModeEnum.getByCode(req.getApplyModeCode()));
            brandApplyReq.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> brandApplyCmdFeign.updateBrandApply(brandApplyReq));
        });
    }
}
