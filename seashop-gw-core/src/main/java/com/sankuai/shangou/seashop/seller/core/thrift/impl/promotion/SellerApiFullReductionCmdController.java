package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionSaveReq;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerFullReductionRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiFullReductionSaveReq;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiFullReduction")
public class SellerApiFullReductionCmdController {

    @Resource
    private SellerFullReductionRemoteService sellerFullReductionRemoteService;

    @PostMapping(value = "/save", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> save(@RequestBody ApiFullReductionSaveReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            req.setShopName(loginShopDto.getName());
            FullReductionSaveReq saveReq = JsonUtil.copy(req, FullReductionSaveReq.class);
            saveReq.checkParameter();
            return sellerFullReductionRemoteService.save(saveReq);
        });
    }

    @PostMapping(value = "/endActive", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("endActive", request, req -> {
            req.checkParameter();
            return sellerFullReductionRemoteService.endActive(req);
        });
    }
}
