package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdAgreementReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdSendCodeReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdShopStepsOneReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdShopStepsThreeReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiCmdShopStepsTwoReq;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.utils.ValidatorHelper;
import com.sankuai.shangou.seashop.mall.core.service.user.shop.MallApiShopService;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/mallApi/apiShop")
public class MallApiShopCmdController {
    @Resource
    MallApiShopService shopService;

    @PostMapping(value = "/residencyApplication", consumes = "application/json")
    @NeedLogin
    public ResultDto<String> residencyApplication(@RequestBody ApiCmdAgreementReq cmdAgreementReq) {
        

        cmdAgreementReq.setUserId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.responseInvoke("residencyApplication", cmdAgreementReq, req -> shopService.residencyApplication(cmdAgreementReq));
    }

    @PostMapping(value = "/sendCode", consumes = "application/json")
    public ResultDto<BaseResp> sendCode(@RequestBody ApiCmdSendCodeReq cmdSendCodeReq) {
        return ThriftResponseHelper.responseInvoke("sendCod", cmdSendCodeReq, req -> shopService.sendCode(cmdSendCodeReq));
    }

    @PostMapping(value = "/editBaseInfo", consumes = "application/json")
    @NeedLogin
    public ResultDto<Long> editBaseInfo(@RequestBody ApiCmdShopStepsOneReq cmdShopStepsOneReq) {
        

        cmdShopStepsOneReq.setShopId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.responseInvoke("editBaseInfo", cmdShopStepsOneReq, req -> {
            return shopService.editBaseInfo(cmdShopStepsOneReq);
        });
    }

    @PostMapping(value = "/editBankInfo", consumes = "application/json")
    @NeedLogin
    public ResultDto<Long> editBankInfo(@RequestBody ApiCmdShopStepsTwoReq cmdShopStepsTwoReq) {
        

        cmdShopStepsTwoReq.setShopId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.responseInvoke("editBankInfo", cmdShopStepsTwoReq, req -> shopService.editBankInfo(cmdShopStepsTwoReq));
    }

    @PostMapping(value = "/editCategoryInfo", consumes = "application/json")
    @NeedLogin
    public ResultDto<Long> editCategoryInfo(@RequestBody ApiCmdShopStepsThreeReq cmdShopStepsThreeReq) {
        

        cmdShopStepsThreeReq.setShopId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.responseInvoke("editCategoryInfo", cmdShopStepsThreeReq, req -> {
            ValidatorHelper.validate(cmdShopStepsThreeReq);
            return shopService.editCategoryInfo(cmdShopStepsThreeReq);
        });
    }
}
