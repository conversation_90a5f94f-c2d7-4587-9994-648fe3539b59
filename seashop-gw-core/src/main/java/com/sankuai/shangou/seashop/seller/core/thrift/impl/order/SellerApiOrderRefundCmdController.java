package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerConfirmReceiveReq;
import com.sankuai.shangou.seashop.seller.core.service.order.SellerOrderRefundService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiSellerApproveReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiSellerConfirmReceiveReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/sellerApi/apiOrderRefund")
@Slf4j
public class SellerApiOrderRefundCmdController {

    private final static String[] IGNORE_PROPERTIES = {"auditStatus", "refundStatus"};
    @Resource
    private SellerOrderRefundService sellerOrderRefundService;

    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/sellerApprove", consumes = "application/json")
    public ResultDto<BaseResp> sellerApprove(@RequestBody ApiSellerApproveReq req) throws TException {

        

        return ThriftResponseHelper.responseInvoke("【售后】供应商审核售后", req, func -> {
            SellerApproveReq sellerApproveReq = JsonUtil.copy(req, SellerApproveReq.class, IGNORE_PROPERTIES);
            // 枚举转换
            sellerApproveReq.setAuditStatus(RefundAuditStatusEnum.valueOf(req.getAuditStatus()));
            // 上下文获取并区分用户与店铺
            LoginShopDto loginShop = TracerUtil.getShopDto();
            ShopDto shop = new ShopDto();
            shop.setShopId(loginShop.getShopId());
            sellerApproveReq.setShop(shop);

            UserDto user = new UserDto();
            user.setUserId(loginShop.getManagerId());
            user.setUserName(loginShop.getName());
            user.setUserPhone(loginShop.getUserPhone());
            sellerApproveReq.setUser(user);
            return sellerOrderRefundService.sellerApprove(sellerApproveReq);
        });
    }

    /**
     * 供应商确认收货审核，仅确认收货逻辑，拒绝调用审核接口
     * 虽然也是审核相关，但是会涉及库存回退等，所以单独接口
     *
     * @param req
     * @return
     * @throws TException
     */
    @NeedLogin(userType = RoleEnum.SHOP)
    @PostMapping(value = "/sellerConfirmReceive", consumes = "application/json")
    public ResultDto<BaseResp> sellerConfirmReceive(@RequestBody ApiSellerConfirmReceiveReq req) throws TException {

        

        return ThriftResponseHelper.responseInvoke("【售后】供应商确认收货", req, func -> {
            // 上下文获取并区分用户与店铺
            LoginShopDto loginShop = TracerUtil.getShopDto();
            SellerConfirmReceiveReq copy = JsonUtil.copy(func, SellerConfirmReceiveReq.class);
            ShopDto shop = new ShopDto();
            shop.setShopId(loginShop.getShopId());
            copy.setShop(shop);

            UserDto user = new UserDto();
            user.setUserId(loginShop.getManagerId());
            user.setUserName(loginShop.getName());
            user.setUserPhone(loginShop.getUserPhone());
            copy.setUser(user);

            sellerOrderRefundService.sellerConfirmReceive(copy);
            return BaseResp.of();
        });
    }

}
