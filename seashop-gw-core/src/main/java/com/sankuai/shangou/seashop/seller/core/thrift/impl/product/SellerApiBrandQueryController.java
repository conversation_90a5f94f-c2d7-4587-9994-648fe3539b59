package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.BrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.ShopBrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryNotApplyBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryShopBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.NotApplyBrandGroupResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.NotApplyBrandResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.ShopBrandDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryBrandReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiNotApplyBrandGroupResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiNotApplyBrandResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiBrandDto;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiBrandGroupDto;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/07 14:34
 */
@RestController
@RequestMapping("/sellerApi/apiBrand")
public class SellerApiBrandQueryController {

    @Resource
    private ShopBrandQueryFeign shopBrandQueryFeign;
    @Resource
    private BrandQueryFeign brandQueryFeign;

    @GetMapping(value = "/queryNotApplyBrand")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiNotApplyBrandResp> queryNotApplyBrand() throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryNotApplyBrand", null, req -> {

            QueryNotApplyBrandReq remoteReq = new QueryNotApplyBrandReq();
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            NotApplyBrandResp resp = ThriftResponseHelper.executeThriftCall(() -> brandQueryFeign.queryNotApplyBrand(remoteReq));
            return ApiNotApplyBrandResp.builder().brandList(JsonUtil.copyList(resp.getBrandList(), ApiBrandDto.class)).build();
        });
    }

    @GetMapping(value = "/queryNotApplyBrandGroup")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiNotApplyBrandGroupResp> queryNotApplyBrandGroup() throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryNotApplyBrandGroup", null, req -> {

            QueryNotApplyBrandReq remoteReq = new QueryNotApplyBrandReq();
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            NotApplyBrandGroupResp resp = ThriftResponseHelper.executeThriftCall(() -> brandQueryFeign.queryNotApplyBrandGroup(remoteReq));
            return ApiNotApplyBrandGroupResp.builder().groupList(JsonUtil.copyList(resp.getGroupList(), ApiBrandGroupDto.class)).build();
        });
    }

    @PostMapping(value = "/queryBrandForPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiBrandDto>> queryBrandForPage(@RequestBody ApiQueryBrandReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryBrandForPage", request, req -> {
            req.checkParameter();

            QueryShopBrandReq remoteReq = JsonUtil.copy(req, QueryShopBrandReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            if (StringUtils.isNotEmpty(req.getName()) && StringUtils.isEmpty(req.getBrandName())) {
                remoteReq.setBrandName(req.getName());
            }
            BasePageResp<ShopBrandDto> resp = ThriftResponseHelper.executeThriftCall(() -> shopBrandQueryFeign.queryShopBrandForPage(remoteReq));
            return PageResultHelper.transfer(resp, ApiBrandDto.class, (source, target) -> {
                target.setId(source.getBrandId());
                target.setName(source.getBrandName());
            });
        });
    }
}
