package com.sankuai.shangou.seashop.m.core.service.user.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.*;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiShopDetailResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiShopIdsResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiShopResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiShopSimpleResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiShopUserCountResp;

import java.util.List;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
public interface MApiShopService {

    /**
     * 获取店铺ID列表
     *
     * @param queryShopReq 查询参数
     * @return 店铺ID列表
     */
    ApiShopIdsResp getShopIds(ApiQueryShopReq queryShopReq);

    /**
     * 供应商入驻申请
     *
     * @param cmdAgreementReq 供应商入驻申请参数
     * @return 供应商入驻申请
     */
    String residencyApplication(ApiCmdAgreementReq cmdAgreementReq);


    /**
     * 供应商入驻申请第一步(基本信息)
     *
     * @param cmdShopStepsOneReq 供应商入驻申请参数第一步
     * @return 供应商入驻申请
     */
    Long editProfilesOne(ApiCmdShopStepsOneReq cmdShopStepsOneReq);

    /**
     * 供应商入驻申请第二步(银行信息)
     *
     * @param cmdShopStepsTwoReq 供应商入驻申请参数第二步
     * @return 供应商入驻申请
     */
    Long editProfilesTwo(ApiCmdShopStepsTwoReq cmdShopStepsTwoReq);

    /**
     * 供应商入驻申请第三步(类目信息)
     *
     * @param cmdShopStepsThreeReq 供应商入驻申请参数第三步
     * @return 供应商入驻申请
     */
    Long editProfilesThree(ApiCmdShopStepsThreeReq cmdShopStepsThreeReq);


    /**
     * 分页查询店铺信息
     *
     * @param request 请求体
     * @return 查询结果
     */
    BasePageResp<ApiShopResp> queryPage(ApiShopQueryPagerReq request);

    /**
     * 查询店铺详情
     *
     * @param shopId 店铺ID
     * @return 店铺详情
     */
    ApiShopDetailResp queryDetail(BaseIdReq shopId);

    /**
     * 审核店铺
     *
     * @param shopId 店铺ID
     * @return 审核结果
     */
    BaseResp auditing(ApiCmdShopStatusReq shopId);

    /**
     * 编辑个人供应商信息
     *
     * @param cmdShopReq 请求体
     * @return 编辑结果
     */
    Long editShopPersonal(ApiPersonalCmdShopReq cmdShopReq);

    /**
     * 编辑店铺供应商信息
     *
     * @param cmdShopReq 请求体
     * @return 编辑结果
     */
    Long editShopEnterprise(ApiCompanyCmdShopReq cmdShopReq);

    /**
     * 冻结店铺
     *
     * @param cmdShopStatusReq 请求体
     * @return 冻结结果
     */
    Long freezeShop(ApiCmdShopStatusReq cmdShopStatusReq);

    BaseResp sendDepositRemind(BaseIdReq baseIdReq);

    /**
     * 统计平台用户
     *
     * @return 统计结果
     */
    ApiShopUserCountResp countShopUser();


    List<ApiShopResp> queryShopsByIds(ApiShopQueryReq baseBatchIdReq);

    /**
     * 通过名称或者id查询已签约店铺列表（id、名称、需要缴纳的最大保证金）
     *
     * @param request 请求体
     * @return 查询结果
     */

    BasePageResp<ApiShopSimpleResp> querySimplePage(ApiShopQueryPagerReq request);

    /**
     * 修改店铺类型
     *
     * @param shopId 请求体
     * @return 查询结果
     */
    BaseResp setShopType(ApiShopTypeCmdReq shopId);

    /**
     * 获取银行地区
     *
     * @return 查询结果
     */
    String getBankRegion();

    String createQrCode(ApiCmdCreateQRReq apiCmdCreateQRReq);

    BaseResp updateSeq(ApiCmdShopSeqReq apiCmdShopSeqReq);

    String queryShopCategoryDetail(BaseIdReq shopId);

    BaseResp bindMemberPhone(ApiCmdBindMemberPhoneReq req);
}
