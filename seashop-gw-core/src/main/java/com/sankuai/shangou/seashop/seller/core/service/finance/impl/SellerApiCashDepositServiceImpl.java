package com.sankuai.shangou.seashop.seller.core.service.finance.impl;

import java.math.BigDecimal;
import java.util.Arrays;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;
import com.sankuai.shangou.seashop.seller.common.enums.SellerResultCodeEnum;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.finance.SellerApiCashDepositService;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiCashDepositResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@Service
@Slf4j
public class SellerApiCashDepositServiceImpl implements SellerApiCashDepositService {

    @Resource
    private SellerShopRemoteService sellerShopRemoteService;
    @Resource
    private SellerFinanceRemoteService sellerFinanceRemoteService;

    @Override
    public ApiCashDepositResp queryOneByShopId(Long shopId) {

        ApiCashDepositResp apiCashDepositResp = new ApiCashDepositResp();

        ShopSimpleQueryReq queryReq = new ShopSimpleQueryReq();
        queryReq.setShopIdList(Arrays.asList(shopId));
        queryReq.setQueryCashDeposit(Boolean.TRUE);
        ShopSimpleListResp shopSimpleListResp = sellerShopRemoteService.querySimpleList(queryReq);
        if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
            throw new BusinessException(SellerResultCodeEnum.SELLER_DATA_NOT_EXIST.getCode(), SellerResultCodeEnum.SELLER_DATA_NOT_EXIST.getMsg());
        }
        ShopSimpleResp shopSimpleResp = shopSimpleListResp.getList().get(0);

        CashDepositResp cashDepositResp = sellerFinanceRemoteService.queryOneByShopId(shopId);
        if (null != cashDepositResp) {
            // 将cashDepositResp的属性copy到apiCashDepositResp中
            apiCashDepositResp.setId(cashDepositResp.getId());
            apiCashDepositResp.setShopId(cashDepositResp.getShopId());
            apiCashDepositResp.setCurrentBalance(cashDepositResp.getCurrentBalance());
            apiCashDepositResp.setTotalBalance(cashDepositResp.getTotalBalance());
            apiCashDepositResp.setDate(cashDepositResp.getDate());
        }
        else {
            apiCashDepositResp.setShopId(shopId);
            apiCashDepositResp.setCurrentBalance(BigDecimal.ZERO);
            apiCashDepositResp.setTotalBalance(BigDecimal.ZERO);
        }
        // 所需最大保证金
        BigDecimal maxCashDeposit = null != shopSimpleResp.getMaxCashDeposit() ? shopSimpleResp.getMaxCashDeposit() : BigDecimal.ZERO;
        apiCashDepositResp.setMaxCashDeposit(maxCashDeposit);
        if (maxCashDeposit.compareTo(apiCashDepositResp.getCurrentBalance()) > 0) {
            apiCashDepositResp.setEnableLabels(Boolean.TRUE);
            apiCashDepositResp.setToPayCashDeposit(maxCashDeposit.subtract(apiCashDepositResp.getCurrentBalance()));
        }

        return apiCashDepositResp;
    }
}
