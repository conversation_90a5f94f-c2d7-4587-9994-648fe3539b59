package com.sankuai.shangou.seashop.m.core.service.export.handler.finance.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.common.remote.MFinanceRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo.SettledItemListEo;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiSettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;

import cn.hutool.core.collection.CollUtil;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
public class SettleditemListWrapper extends PageExportWrapper<SettledItemListEo, ApiSettledItemQryReq> {

    private final MFinanceRemoteService MFinanceRemoteService;

    public SettleditemListWrapper(MFinanceRemoteService MFinanceRemoteService) {
        this.MFinanceRemoteService = MFinanceRemoteService;
    }

    @Override
    public List<SettledItemListEo> getPageList(ApiSettledItemQryReq param) {
        BasePageResp<SettledItemResp> settledItemPage = MFinanceRemoteService.settledItemPageList(JsonUtil.copy(param, SettledItemQryReq.class));
        if (null == settledItemPage || CollUtil.isEmpty(settledItemPage.getData())) {
            return null;
        }
        List<SettledItemListEo> settledItemListEos = CollUtil.newArrayList();
        settledItemPage.getData().forEach(s -> {
            SettledItemListEo settledItemListEo = JsonUtil.copy(s, SettledItemListEo.class);
            settledItemListEos.add(settledItemListEo);
        });
        return settledItemListEos;
    }

    @Override
    public Integer getBatchCount() {
        return 1000;
    }
}
