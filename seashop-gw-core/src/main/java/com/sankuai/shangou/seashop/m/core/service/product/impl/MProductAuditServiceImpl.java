package com.sankuai.shangou.seashop.m.core.service.product.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.product.MProductAuditService;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryProductAuditReq;

/**
 * <AUTHOR>
 * @date 2024/01/03 17:58
 */
@Service
public class MProductAuditServiceImpl implements MProductAuditService {

    @Resource
    private MExportTaskBiz exportTaskBiz;

    @Override
    public void exportProductAuditRefuse(ApiQueryProductAuditReq request) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.PRODUCT_AUDIT_REFUSE_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getManagerDto().getId());
        createExportTaskBo.setOperatorName(TracerUtil.getManagerDto().getName());
        exportTaskBiz.create(createExportTaskBo);
    }

}
