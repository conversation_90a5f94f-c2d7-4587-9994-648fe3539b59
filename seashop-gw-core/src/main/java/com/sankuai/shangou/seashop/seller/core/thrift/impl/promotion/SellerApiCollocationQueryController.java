package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CollocationDetailReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.PageSellerCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.collocation.PageCollocationResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiPageSellerCollocationReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiCollocationResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiPageCollocationResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 15:28
 */
@RestController
@RequestMapping("/sellerApi/apiCollocation")
public class SellerApiCollocationQueryController {

    @Resource
    private CollocationQueryFeign collocationQueryFeign;

    @PostMapping(value = "/pageSellerCollocation", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiPageCollocationResp>> pageSellerCollocation(@RequestBody ApiPageSellerCollocationReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("pageSellerCollocation", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.checkParameter();
            BasePageResp<PageCollocationResp> pageCollocationRespBasePageResp = ThriftResponseHelper.executeThriftCall(() ->
                    collocationQueryFeign.pageSellerCollocation(JsonUtil.copy(req, PageSellerCollocationReq.class)));
            return PageResultHelper.transfer(pageCollocationRespBasePageResp, ApiPageCollocationResp.class);
        });
    }

    @GetMapping(value = "/queryCollocationDetail")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiCollocationResp> queryCollocationDetail(@RequestParam Long id) throws TException {
        AssertUtil.throwIfNull(id, "id不能为空");
        

        return ThriftResponseHelper.responseInvoke("queryCollocationDetail", id, req -> {
            CollocationDetailReq collocationDetailReq = new CollocationDetailReq();
            collocationDetailReq.setId(id);
            collocationDetailReq.setShopId(TracerUtil.getShopDto().getShopId());
            collocationDetailReq.setSourceFrom(2);
            return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() ->
                    collocationQueryFeign.queryCollocationDetail(collocationDetailReq)), ApiCollocationResp.class);
        });
    }
}
