package com.sankuai.shangou.seashop.m.core.service.user.account.impl;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.user.account.MApiLabelService;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdLabelReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiQueryLabelPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.account.response.ApiLabelResp;
import com.sankuai.shangou.seashop.user.thrift.account.LabelCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.account.LabelQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdLabelReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryLabelPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.LabelResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MApiLabelServiceImpl implements MApiLabelService {
    @Resource
    private LabelQueryFeign labelQueryFeign;
    @Resource
    private LabelCmdFeign labelCmdFeign;

    @Override
    public BasePageResp<ApiLabelResp> queryLabelPage(ApiQueryLabelPageReq queryLabelPageReq) {
        QueryLabelPageReq queryLabelPageReq1 = JsonUtil.copy(queryLabelPageReq, QueryLabelPageReq.class);
        BasePageResp<LabelResp> labelRespPage = ThriftResponseHelper.executeThriftCall(() -> labelQueryFeign.queryLabelPage(queryLabelPageReq1));
        return PageResultHelper.transfer(labelRespPage, ApiLabelResp.class);
    }

    @Override
    public Long addLabel(ApiCmdLabelReq cmdLabelPageReq) {
        CmdLabelReq cmdLabelReq = JsonUtil.copy(cmdLabelPageReq, CmdLabelReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> labelCmdFeign.addLabel(cmdLabelReq));
    }

    @Override
    public Long editLabel(ApiCmdLabelReq cmdLabelPageReq) {
        CmdLabelReq cmdLabelReq = JsonUtil.copy(cmdLabelPageReq, CmdLabelReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> labelCmdFeign.editLabel(cmdLabelReq));
    }

    @Override
    public Long deleteLabel(ApiCmdLabelReq cmdLabelPageReq) {
        CmdLabelReq cmdLabelReq = JsonUtil.copy(cmdLabelPageReq, CmdLabelReq.class);
        return ThriftResponseHelper.executeThriftCall(() -> labelCmdFeign.deleteLabel(cmdLabelReq));
    }
}
