package com.sankuai.shangou.seashop.m.core.thrift.impl.system.setting;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MessageNoticeSettingCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.MessageNoticeSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseMessageNoticeSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMessageNoticeSettingRes;
import com.sankuai.shangou.seashop.m.thrift.core.request.system.ApiMessageNoticeSettingReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.ApiMessageNoticeSettingRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mApi/apiMessageNoticeSetting")
public class MApiMessageNoticeSettingController {

    @Resource
    private MessageNoticeSettingCMDFeign messageNoticeSettingCMDFeign;

    @Resource
    private MessageNoticeSettingQueryFeign messageNoticeSettingQueryFeign;

    @PostMapping(value = "/setMessageNoticeSetting", consumes = "application/json")
    public ResultDto<Boolean> setMessageNoticeSetting(@RequestBody ApiMessageNoticeSettingReq messageNoticeSettingReq) throws TException {
        return ThriftResponseHelper.responseInvoke("getSetting", messageNoticeSettingReq, req -> {
            BaseMessageNoticeSettingReq baseMessageNoticeSettingReq = JsonUtil.copy(req, BaseMessageNoticeSettingReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                messageNoticeSettingCMDFeign.setMessageNoticeSetting(baseMessageNoticeSettingReq));
            return result;
        });
    }

    @GetMapping(value = "/getMessageNoticeSetting")
    public ResultDto<List<ApiMessageNoticeSettingRes>> getMessageNoticeSetting() throws TException {
        int i = 1;
        return ThriftResponseHelper.responseInvoke("getSetting", i, req -> {
            List<BaseMessageNoticeSettingRes> resultRpc = ThriftResponseHelper.executeThriftCall(() ->
                messageNoticeSettingQueryFeign.getMessageNoticeSetting());
            List<ApiMessageNoticeSettingRes> result = JsonUtil.copyList(resultRpc, ApiMessageNoticeSettingRes.class);
            return result;
        });
    }
}
