package com.sankuai.shangou.seashop.m.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryDescriptionTemplateReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.ApiDescriptionTemplateListResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.product.dto.ApiDescriptionTemplateDto;
import com.sankuai.shangou.seashop.product.thrift.core.DescriptionTemplateQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.QueryDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.DescriptionTemplateListResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/15 14:14
 */
@RestController
@RequestMapping("/mApi/apiDescriptionTemplate")
public class MApiDescriptionTemplateQueryController {

    @Resource
    private DescriptionTemplateQueryFeign descriptionTemplateQueryFeign;

    @PostMapping(value = "/queryDescriptionTemplateForList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<ApiDescriptionTemplateListResp> queryDescriptionTemplateForList(@RequestBody ApiQueryDescriptionTemplateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryDescriptionTemplateForList", request, req -> {

            QueryDescriptionTemplateReq remoteReq = JsonUtil.copy(req, QueryDescriptionTemplateReq.class);
            remoteReq.setPosition(DescriptionTemplatePositionEnum.getByCode(req.getPositionCode()));
            DescriptionTemplateListResp resp = ThriftResponseHelper.executeThriftCall(() -> descriptionTemplateQueryFeign.queryDescriptionTemplateForList(remoteReq));
            return ApiDescriptionTemplateListResp.builder().templateList(JsonUtil.copyList(resp.getTemplateList(), ApiDescriptionTemplateDto.class)).build();
        });
    }

}
