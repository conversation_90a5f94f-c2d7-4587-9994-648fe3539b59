package com.sankuai.shangou.seashop.m.core.service.export.handler.product.eo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:02
 */
@Getter
@Setter
public class CategoryEo {

    @ExcelProperty(value = "类目名称")
    private String name;

    @ExcelProperty(value = "类目图标")
    private String icon;

    @ExcelProperty(value = "类目排序")
    private Long displaySequence;

    @ExcelProperty(value = "分佣比例")
    private BigDecimal commissionRate;

    @ExcelProperty(value = "是否显示")
    private String whetherShowStr;

}
