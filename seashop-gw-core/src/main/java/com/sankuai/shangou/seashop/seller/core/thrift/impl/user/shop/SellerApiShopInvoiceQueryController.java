package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerShopInvoiceRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiQueryShopInvoiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:59
 */
@RestController
@RequestMapping("/sellerApi/apiShopInvoice")
public class SellerApiShopInvoiceQueryController {

    @Resource
    private SellerShopInvoiceRemoteService sellerShopInvoiceRemoteService;

    @GetMapping(value = "/queryShopInvoice")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiQueryShopInvoiceResp> queryShopInvoice() throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopInvoice", null, req -> {
            QueryShopInvoiceResp queryShopInvoiceResp = sellerShopInvoiceRemoteService.querySelfShopInvoice();
            return JsonUtil.copy(queryShopInvoiceResp, ApiQueryShopInvoiceResp.class);
        });
    }
}
