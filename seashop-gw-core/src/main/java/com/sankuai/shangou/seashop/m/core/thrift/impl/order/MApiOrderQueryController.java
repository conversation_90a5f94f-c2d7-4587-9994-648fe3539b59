package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import java.util.List;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.order.MOrderService;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiQueryPlatformOrderReq;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiOrderDetailResp;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiOrderInfoDto;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiOrderOperationLogResp;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mApi/apiOrder")
@Slf4j
public class MApiOrderQueryController {

    @Resource
    private MOrderService mOrderService;

    @PostMapping(value = "/pcPageQueryUserOrder", consumes = "application/json")
    public ResultDto<BasePageResp<ApiOrderInfoDto>> pcPageQueryUserOrder(@RequestBody ApiQueryPlatformOrderReq queryReq) {
        log.info("【订单】平台分页查询订单列表:{}", queryReq);
        return ThriftResponseHelper.responseInvoke("pcPageQueryUserOrder", queryReq, func -> {
            func.setQueryFrom(OrderQueryFromEnum.PLATFORM_PC);
            QueryPlatformOrderReq bean = JsonUtil.copy(func, QueryPlatformOrderReq.class);
            return PageResultHelper.transfer(mOrderService.pageQueryPlatformOrder(bean), ApiOrderInfoDto.class);
        });
    }

    @GetMapping(value = "/queryDetail")
    public ResultDto<ApiOrderDetailResp> queryDetail(@RequestParam String orderId) {
        log.info("【订单】平台查询订单详情:{}", orderId);
        QueryOrderDetailReq req = new QueryOrderDetailReq();
        req.setOrderId(orderId);
        return ThriftResponseHelper.responseInvoke("queryDetail", orderId, func -> {

            return JsonUtil.copy(mOrderService.queryDetail(req), ApiOrderDetailResp.class);
        });
    }

    @PostMapping(value = "/exportInvoice", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> exportInvoice(@RequestBody ApiQueryPlatformOrderReq request) throws TException {
        mOrderService.exportOrderInvoice(request);
        return ResultDto.newWithData(BaseResp.of());
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/export", consumes = "application/json")
    public ResultDto<BaseResp> export(@RequestBody ApiQueryPlatformOrderReq queryReq) throws TException {
//        LoginManagerDto manager = TracerUtil.getManager();
        LoginManagerDto loginDto = TracerUtil.getManagerDto();
        Long userId = loginDto.getId();
        log.info("【订单】供应商导出订单列表, userId={}, queryReq={}", userId, queryReq);
        UserDto user = new UserDto();
        user.setUserId(userId);
        user.setUserName(loginDto.getName());
        mOrderService.export(queryReq, user);
        return ResultDto.newWithData(BaseResp.of());
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @GetMapping(value = "/queryOrderLog")
    public ResultDto<List<ApiOrderOperationLogResp>> queryOrderLog(@RequestParam String orderId) {
        log.info("【订单】查询订单操作日志, 请求参数={}", orderId);
        return ThriftResponseHelper.responseInvoke("queryOrderLog", orderId, func -> {
            // 业务逻辑处理
            return JsonUtil.copyList(mOrderService.queryOrderOperationLog(orderId), ApiOrderOperationLogResp.class);
        });
    }
}
