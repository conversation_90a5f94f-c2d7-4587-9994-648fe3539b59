package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import java.util.List;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.core.service.order.MOrderRefundService;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiPlatformQueryRefundDetailReq;
import com.sankuai.shangou.seashop.m.thrift.order.request.ApiPlatformQueryRefundReq;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiPlatformRefundDetailResp;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiPlatformRefundDto;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiRefundLogResp;
import com.sankuai.shangou.seashop.m.thrift.order.response.ApiRefundUserDeliverExpressResp;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPlatformQueryTabEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundLogListResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mApi/apiOrderRefund")
@Slf4j
public class MApiOrderRefundQueryController {


    @Resource
    private MOrderRefundService mOrderRefundService;


    private final static String[] IGNORE_PROPERTIES = {"tab"};


    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/queryDetail", consumes = "application/json")
    public ResultDto<ApiPlatformRefundDetailResp> queryDetail(@RequestBody ApiPlatformQueryRefundDetailReq queryReq) throws TException {
        log.info("【订单】平台查询售后明细:{}", queryReq);
        return ThriftResponseHelper.responseInvoke("queryDetail", queryReq, func -> {
            PlatformQueryRefundDetailReq bean = JsonUtil.copy(func, PlatformQueryRefundDetailReq.class);
            return JsonUtil.copy(mOrderRefundService.queryDetail(bean), ApiPlatformRefundDetailResp.class);
        });

    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/queryRefundLog", consumes = "application/json")
    public ResultDto<List<ApiRefundLogResp>> queryRefundLog(@RequestBody BaseIdReq queryReq) throws TException {
        log.info("【订单】平台查询售后日志:{}", queryReq);
        return ThriftResponseHelper.responseInvoke("queryRefundLog", queryReq, func -> {
            RefundLogListResp resp = mOrderRefundService.queryRefundLog(func);
            return JsonUtil.copyList(resp.getLogList(), ApiRefundLogResp.class);
        });
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/platformQueryRefundPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiPlatformRefundDto>> platformQueryRefundPage(@RequestBody ApiPlatformQueryRefundReq queryReq) throws TException {
        log.info("【订单】平台查询售后列表:{}", queryReq);
        return ThriftResponseHelper.responseInvoke("platformQueryRefundPage", queryReq, func -> {
            // 参数校验
            func.checkParameter();
            PlatformQueryRefundReq req = JsonUtil.copy(func, PlatformQueryRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setTab(RefundPlatformQueryTabEnum.valueOf(func.getTab()));

            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            log.info("获取上下文登录信息:{}", loginDto);
            UserDto user = new UserDto();
            user.setUserId(loginDto.getId());
            user.setUserName(loginDto.getName());

            req.setUser(user);

            return PageResultHelper.transfer(mOrderRefundService.platformQueryRefundPage(req), ApiPlatformRefundDto.class);
        });
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/queryUserDeliverExpress", consumes = "application/json")
    public ResultDto<ApiRefundUserDeliverExpressResp> queryUserDeliverExpress(@RequestBody BaseIdReq queryReq) throws TException {
        log.info("【售后】平台查询寄货物流信息, refundId={}", queryReq);
        return ThriftResponseHelper.responseInvoke("queryUserDeliverExpress", queryReq, func -> {
            // 业务逻辑处理
            return JsonUtil.copy(mOrderRefundService.queryUserDeliverExpress(func), ApiRefundUserDeliverExpressResp.class);
        });
    }

    @NeedLogin(userType = RoleEnum.MANAGER)
    @PostMapping(value = "/exportRefundList", consumes = "application/json")
    public ResultDto<BaseResp> exportRefundList(@RequestBody ApiPlatformQueryRefundReq queryReq) throws TException {
        PlatformQueryRefundReq req = JsonUtil.copy(queryReq, PlatformQueryRefundReq.class, IGNORE_PROPERTIES);
        // 枚举转变换
        req.setTab(RefundPlatformQueryTabEnum.valueOf(queryReq.getTab()));
        mOrderRefundService.export(req);
        return ResultDto.newWithData(BaseResp.of());
    }
}
