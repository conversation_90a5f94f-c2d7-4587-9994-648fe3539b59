package com.sankuai.shangou.seashop.seller.core.service.export.handler.order.wrapper;

import java.util.List;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDistributionReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderProductDistributionFormResp;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.order.eo.OrderProductDistributionEo;

import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/4 11:08
 */
public class OrderProductDistributionDataWrapper extends PageExportWrapper<OrderProductDistributionEo, QueryOrderDistributionReq> {

    private final OrderQueryFeign orderQueryFeign;

    public OrderProductDistributionDataWrapper(OrderQueryFeign orderQueryFeign) {
        this.orderQueryFeign = orderQueryFeign;
    }

    @Override
    public List<OrderProductDistributionEo> getPageList(QueryOrderDistributionReq param) {
        param.setPageNo(1);
        param.setPageSize(65535);
        List<OrderProductDistributionFormResp> list = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.exportOrderProductDistribution(param));
        return BeanUtil.copyToList(list, OrderProductDistributionEo.class);
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return super.getWriteHandlerList();
    }

    @Override
    public String sheetName() {
        return "商品配货单";
    }
}
