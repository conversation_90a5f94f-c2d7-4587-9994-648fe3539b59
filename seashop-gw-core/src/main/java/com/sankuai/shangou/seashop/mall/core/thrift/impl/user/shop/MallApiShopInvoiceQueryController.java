package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.shop;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiQueryShopInvoiceReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.shop.ApiQueryShopInvoiceResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopInvoiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:59
 */
@RestController
@RequestMapping("/mallApi/apiShopInvoice")
public class MallApiShopInvoiceQueryController {

    @Resource
    private ShopInvoiceQueryFeign shopInvoiceQueryFeign;

    @PostMapping(value = "/queryShopInvoice", consumes = "application/json")
    public ResultDto<ApiQueryShopInvoiceResp> queryShopInvoice(@RequestBody ApiQueryShopInvoiceReq queryShopInvoiceReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopInvoice", queryShopInvoiceReq, req -> {
            req.checkParameter();

            QueryShopInvoiceResp resp = ThriftResponseHelper.executeThriftCall(() ->
                    shopInvoiceQueryFeign.queryShopInvoice(JsonUtil.copy(req, QueryShopInvoiceReq.class)));
            return JsonUtil.copy(resp, ApiQueryShopInvoiceResp.class);
        });
    }
}
