package com.sankuai.shangou.seashop.m.core.service.promotion.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.m.common.remote.promotion.MFlashSaleRemoteService;
import com.sankuai.shangou.seashop.m.core.service.promotion.MApiPromotionService;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiPromotionAuditStatisticsReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.promotion.ApiPromotionAuditStatisticsResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.FlashSaleStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleSimpleResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/3/5/005
 * @description:
 */
@Service
@Slf4j
public class MApiPromotionServiceImpl implements MApiPromotionService {

    @Resource
    private MFlashSaleRemoteService mFlashSaleRemoteService;

    @Override
    public ApiPromotionAuditStatisticsResp auditStatisticsQuery(ApiPromotionAuditStatisticsReq request) {

        ApiPromotionAuditStatisticsResp resp = new ApiPromotionAuditStatisticsResp();

        // 查询限时购待审核数量
        FlashSaleQueryReq req = new FlashSaleQueryReq();
        req.setPageNo(1);
        req.setPageSize(1);
        req.setStatus(FlashSaleStatusEnum.WAIT_FOR_AUDITING.getStatus());
        BasePageResp<FlashSaleSimpleResp> flashSalePage = mFlashSaleRemoteService.pageList(req);
        if (null != flashSalePage) {
            Long totalCount = flashSalePage.getTotalCount();
            resp.setCount(totalCount);
            resp.setFlashSaleCount(totalCount);
        }

        return resp;
    }
}
