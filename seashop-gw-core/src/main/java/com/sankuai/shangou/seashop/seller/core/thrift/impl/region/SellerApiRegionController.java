package com.sankuai.shangou.seashop.seller.core.thrift.impl.region;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseRegionRes;
import com.sankuai.shangou.seashop.seller.common.remote.SellerRegionRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiBaseRegionRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping("/sellerApi/apiRegion")
public class SellerApiRegionController {

    @Resource
    private SellerRegionRemoteService sellerRegionRemoteService;

    @GetMapping(value = "/getRegionByParentId")
    public ResultDto<List<ApiBaseRegionRes>> getRegionByParentId(@RequestParam long parentId) throws TException {
        List<BaseRegionRes> result = sellerRegionRemoteService.getRegionByParentId(parentId);
        return ResultDto.newWithData(JsonUtil.copyList(result, ApiBaseRegionRes.class));
    }

    @GetMapping(value = "/getTreeRegions")
    public ResultDto<String> getTreeRegions() throws TException {
        String result = sellerRegionRemoteService.getTreeRegions();
        return ResultDto.newWithData(result);
    }

    @GetMapping(value = "/getAllRegions")
    public ResultDto<List<ApiBaseRegionRes>> getAllRegions() throws TException {
        List<BaseRegionRes> result = sellerRegionRemoteService.getAllRegions();
        return ResultDto.newWithData(JsonUtil.copyList(result, ApiBaseRegionRes.class));
    }

    @GetMapping(value = "/getParentRegions")
    public ResultDto<List<ApiBaseRegionRes>> getParentRegions(@RequestParam Long id) throws TException {
        List<BaseRegionRes> result = sellerRegionRemoteService.getParentRegions(id);
        return ResultDto.newWithData(JsonUtil.copyList(result, ApiBaseRegionRes.class));
    }

    @GetMapping(value = "/getSubRegions")
    public ResultDto<List<ApiBaseRegionRes>> getSubRegions(@RequestParam Long id) throws TException {
        List<BaseRegionRes> result = sellerRegionRemoteService.getSubRegions(id);
        return ResultDto.newWithData(JsonUtil.copyList(result, ApiBaseRegionRes.class));
    }

    @GetMapping(value = "/getTrackRegionsById")
    public ResultDto<List<ApiBaseRegionRes>> getTrackRegionsById(@RequestParam Long id) throws TException {
        List<BaseRegionRes> result = sellerRegionRemoteService.getTrackRegionsById(id);
        return ResultDto.newWithData(JsonUtil.copyList(result, ApiBaseRegionRes.class));
    }


}
