package com.sankuai.shangou.seashop.seller.core.service.export.handler.order.wrapper;

import java.util.List;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDistributionReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDistributionFormResp;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.order.eo.OrderDistributionEo;

import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/4 11:08
 */
public class OrderDistributionDataWrapper extends PageExportWrapper<OrderDistributionEo, QueryOrderDistributionReq> {

    private final OrderQueryFeign orderQueryFeign;

    public OrderDistributionDataWrapper(OrderQueryFeign orderQueryFeign) {
        this.orderQueryFeign = orderQueryFeign;
    }

    @Override
    public List<OrderDistributionEo> getPageList(QueryOrderDistributionReq param) {
        param.setPageNo(1);
        param.setPageSize(65535);
        List<OrderDistributionFormResp> list = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.exportOrderDistribution(param));
        return BeanUtil.copyToList(list, OrderDistributionEo.class);
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return super.getWriteHandlerList();
    }

    @Override
    public String sheetName() {
        return "订单配货单";
    }
}
