package com.sankuai.shangou.seashop.m.core.service.order;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.order.thrift.core.request.PlatformApproveBatchReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PlatformApproveBatchResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.PlatformRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.PlatformRefundDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundLogListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundUserDeliverExpressResp;

/**
 * <AUTHOR>
 */
public interface MOrderRefundService {

    /**
     * 导出售后列表
     *
     * @param req 请求参数
     */
    void export(PlatformQueryRefundReq req);

    /**
     * 平台查询售后明细
     *
     * @param queryReq 请求参数
     * @return 售后明细
     */
    PlatformRefundDetailResp queryDetail(PlatformQueryRefundDetailReq queryReq);

    /**
     * 平台查询售后日志
     *
     * @param queryReq 请求参数
     * @return 售后日志
     */
    RefundLogListResp queryRefundLog(BaseIdReq queryReq);

    /**
     * 平台查询售后列表
     *
     * @param queryReq 请求参数
     * @return 售后列表
     */
    BasePageResp<PlatformRefundDto> platformQueryRefundPage(PlatformQueryRefundReq queryReq);

    /**
     * 查询售后用户发货物流信息
     *
     * @param queryReq 请求参数
     * @return 用户发货物流信息
     */
    RefundUserDeliverExpressResp queryUserDeliverExpress(BaseIdReq queryReq);

    /**
     * 平台审核通过
     *
     * @param req 请求参数
     * @return 响应
     */
    public BaseResp platformConfirm(PlatformApproveReq req);

    /**
     * 平台驳回
     *
     * @param req 请求参数
     * @return 响应
     */
    public BaseResp platformReject(PlatformApproveReq req);

    /**
     * 平台批量审核
     *
     * @param batchReq 请求参数
     * @return 响应
     */
    PlatformApproveBatchResp platformBatchConfirm(PlatformApproveBatchReq batchReq);

}
