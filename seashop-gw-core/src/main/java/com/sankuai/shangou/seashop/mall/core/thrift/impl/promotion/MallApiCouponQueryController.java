package com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiCouponQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiCouponResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiCouponSimpleResp;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallShopRemoteService;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.ReceiveTypeEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/27/027
 * @description:
 */
@RestController
@RequestMapping("/mallApi/apiCoupon")
public class MallApiCouponQueryController {

    private static final String DEFAULT_SORT_FIELD = "price";
    @Resource
    private MallShopRemoteService mallShopRemoteService;
    @Resource
    private CouponQueryFeign couponQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiCouponSimpleResp>> pageList(@RequestBody ApiCouponQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            CouponQueryReq queryReq = JsonUtil.copy(req, CouponQueryReq.class);
            if (null == queryReq.getStatus()) {
                queryReq.setClaimable(Boolean.TRUE);
            }

            List<FieldSortReq> sortList = queryReq.getSortList();
            if (null == sortList || CollUtil.isEmpty(sortList)) {
                sortList = new ArrayList<>();
                // 默认按金额倒序排序
                FieldSortReq amountSort = new FieldSortReq();
                amountSort.setSort(DEFAULT_SORT_FIELD);
                amountSort.setIzAsc(Boolean.FALSE);
                sortList.add(amountSort);
                queryReq.setSortList(sortList);
            }

            // 商城端不展示主动发放的优惠券
            queryReq.setReceiveTypeList(Collections.singletonList(ReceiveTypeEnum.SHOP_INDEX.getCode()));
            BasePageResp<CouponSimpleResp> couponPage = ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.pageList(queryReq));

            final List<ShopSimpleResp> shopSimpleList = new ArrayList<>();
            if (null != couponPage && CollUtil.isNotEmpty(couponPage.getData())) {
                List<Long> shopIdList = couponPage.getData().stream().map(CouponSimpleResp::getShopId).distinct().collect(Collectors.toList());
                ShopSimpleQueryReq shopSimpleQueryReq = new ShopSimpleQueryReq();
                shopSimpleQueryReq.setShopIdList(shopIdList);
                ShopSimpleListResp shopSimpleListResp = mallShopRemoteService.querySimpleList(shopSimpleQueryReq);
                if (null != shopSimpleListResp && CollUtil.isNotEmpty(shopSimpleListResp.getList())) {
                    shopSimpleList.addAll(shopSimpleListResp.getList());
                }
            }
            return PageResultHelper.transfer(couponPage, ApiCouponSimpleResp.class, couponSimpleResp -> {
                if (CollUtil.isNotEmpty(shopSimpleList)) {
                    for (ShopSimpleResp shopSimpleResp : shopSimpleList) {
                        if (shopSimpleResp.getId().equals(couponSimpleResp.getShopId())) {
                            couponSimpleResp.setShopName(shopSimpleResp.getShopName());
                            break;
                        }
                    }
                }
            });
        });
    }

    @GetMapping(value = "/flagHasCoupons")
    public ResultDto<Boolean> flagHasCoupons(@RequestParam Long shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("flagHasCoupons", shopId, req -> {
            return ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.flagHasCoupons(req));
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiCouponResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            CouponResp couponResp = ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.getById(req));
            return JsonUtil.copy(couponResp, ApiCouponResp.class);
        });
    }
}
