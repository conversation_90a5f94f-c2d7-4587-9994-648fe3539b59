package com.sankuai.shangou.seashop.openapi.req;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/10 9:18
 */
@Data
public class HiProductQueryReq {

    @JsonProperty("updateTimeStart")
    private Date start_modified;

    @JsonProperty("updateTimeEnd")
    private Date end_modified;

    // 好像没有使用
    @JsonProperty("approveStatus")
    private String approve_status;

    @JsonProperty("productName")
    private String q;

    @JsonProperty("orderBy")
    private String order_by;

    @JsonProperty("pageNo")
    private Integer page_no;

    @JsonProperty("pageSize")
    private Integer page_size;

}
