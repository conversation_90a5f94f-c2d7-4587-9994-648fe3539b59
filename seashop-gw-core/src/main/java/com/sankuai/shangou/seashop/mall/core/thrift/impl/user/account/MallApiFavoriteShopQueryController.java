package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiFavoriteShopPageReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiFavoriteShopRes;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.mall.core.service.user.account.MallApiFavoriteShopService;

@RestController
@RequestMapping("/mallApi/apiFavoriteShop")
public class MallApiFavoriteShopQueryController {
    @Resource
    private MallApiFavoriteShopService mallApiFavoriteShopService;


    @PostMapping(value = "/queryPage", consumes = "application/json")
    @NeedLogin
    public ResultDto<BasePageResp<ApiFavoriteShopRes>> queryPage(@RequestBody ApiFavoriteShopPageReq req) {
        req.setUserId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.responseInvoke("queryPage", req, func -> mallApiFavoriteShopService.queryPage(req));
    }
}
