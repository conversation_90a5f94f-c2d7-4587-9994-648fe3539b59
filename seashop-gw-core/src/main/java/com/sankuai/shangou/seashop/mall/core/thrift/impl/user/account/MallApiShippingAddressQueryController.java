package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import com.sankuai.sgb2b.seashop.mall.api.thrift.dto.trade.ApiShippingAddressDto;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.account.ApiPageShippingAddressReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.ApiDefaultShippingAddressResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.ApiShippingAddressListResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.user.thrift.account.ShippingAddressQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.dto.shippingAddress.ShippingAddressDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.PageShippingAddressReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.shippingAddress.DefaultShippingAddressResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.shippingAddress.ShippingAddressListResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiShippingAddress")
@Slf4j
public class MallApiShippingAddressQueryController {

    @Resource
    private ShippingAddressQueryFeign shippingAddressQueryFeign;

    @NeedLogin
    @GetMapping(value = "/getDefaultAddress")
    public ResultDto<ApiDefaultShippingAddressResp> getDefaultAddress() throws TException {

        

        log.info("【收货地址】获取默认收货地址");
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("getDefaultAddress", userId,
            func -> {
                DefaultShippingAddressResp resp = ThriftResponseHelper.executeThriftCall(() -> shippingAddressQueryFeign.getDefaultAddress(userId));
                return JsonUtil.copy(resp, ApiDefaultShippingAddressResp.class);
            });
    }

    @NeedLogin
    @GetMapping(value = "/getList")
    public ResultDto<ApiShippingAddressListResp> getList() throws TException {
        

        log.info("【收货地址】获取收货地址列表");
        Long userId = TracerUtil.getMemberDto().getId();
        return ThriftResponseHelper.responseInvoke("getList", userId,
            func -> {
                ShippingAddressListResp resp = ThriftResponseHelper.executeThriftCall(() -> shippingAddressQueryFeign.getList(userId));
                return JsonUtil.copy(resp, ApiShippingAddressListResp.class);
            });
    }

    @NeedLogin
    @PostMapping(value = "/pageShippingAddress", consumes = "application/json")
    public ResultDto<BasePageResp<ApiShippingAddressDto>> pageShippingAddress(@RequestBody ApiPageShippingAddressReq request) throws TException {
        

        log.info("【收货地址】分页获取收货地址列表");
        return ThriftResponseHelper.responseInvoke("pageShippingAddress", request, req -> {
            log.info("TracerUtil.getMemberDto()" + TracerUtil.getMemberDto());
            req.setUserId(TracerUtil.getMemberDto().getId());
            BasePageResp<ShippingAddressDto> resp = ThriftResponseHelper.executeThriftCall(() ->
                    shippingAddressQueryFeign.pageShippingAddress(JsonUtil.copy(req, PageShippingAddressReq.class)));
            return PageResultHelper.transfer(resp, ApiShippingAddressDto.class);
        });
    }
}
