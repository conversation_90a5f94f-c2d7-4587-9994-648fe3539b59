package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.ShopBrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryShopBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.ShopBrandListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiShopBrandListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.dto.ApiShopBrandDto;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/14 14:47
 */
@RestController
@RequestMapping("/sellerApi/apiShop")
public class SellerApiShopBrandQueryController {

    @Resource
    private ShopBrandQueryFeign shopBrandQueryFeign;

    @PostMapping(value = "/queryShopBrandForList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiShopBrandListResp> queryShopBrandForList() throws TException {

        

        return ThriftResponseHelper.responseInvoke("queryShopBrandForList", null, req -> {

            QueryShopBrandReq remoteReq = new QueryShopBrandReq();
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            ShopBrandListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopBrandQueryFeign.queryShopBrandForList(remoteReq));
            return ApiShopBrandListResp.builder().brandList(JsonUtil.copyList(resp.getBrandList(), ApiShopBrandDto.class)).build();
        });
    }
}
