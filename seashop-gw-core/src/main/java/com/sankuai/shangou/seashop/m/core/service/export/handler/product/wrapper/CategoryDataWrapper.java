package com.sankuai.shangou.seashop.m.core.service.export.handler.product.wrapper;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.m.common.remote.product.MCategoryRemoteService;
import com.sankuai.shangou.seashop.m.common.remote.product.model.RemoteCategoryBo;
import com.sankuai.shangou.seashop.m.core.service.export.handler.product.eo.CategoryEo;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryReq;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2024/01/02 18:08
 */
public class CategoryDataWrapper extends PageExportWrapper<CategoryEo, BasePageReq> {

    private final MCategoryRemoteService MCategoryRemoteService;

    public CategoryDataWrapper(MCategoryRemoteService MCategoryRemoteService) {
        this.MCategoryRemoteService = MCategoryRemoteService;
    }

    @Override
    public List<CategoryEo> getPageList(BasePageReq param) {
        QueryCategoryReq remoteReq = new QueryCategoryReq();
        remoteReq.setShowStatus(0);
        List<RemoteCategoryBo> categoryList = MCategoryRemoteService.queryCategoryBo(remoteReq);
        List<CategoryEo> eoList = new ArrayList<>();
        categoryEoBuild(categoryList, eoList);
        return eoList;
    }

    private void categoryEoBuild(List<RemoteCategoryBo> categoryList, List<CategoryEo> eoList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }

        categoryList.forEach(category -> {
            eoList.add(categoryEoBuild(category));
            if (CollectionUtils.isNotEmpty(category.getChildren())) {
                categoryEoBuild(category.getChildren(), eoList);
            }
        });
    }

    private CategoryEo categoryEoBuild(RemoteCategoryBo categoryBo) {
        CategoryEo eo = JsonUtil.copy(categoryBo, CategoryEo.class);
        eo.setWhetherShowStr(convertBoolean(categoryBo.getWhetherShow()));
        if (categoryBo.getDepth() != 3) {
            eo.setCommissionRate(null);
        }
        eo.setName(getNamePrefix(categoryBo.getDepth()) + categoryBo.getName());
        return eo;
    }

    private String getNamePrefix(Integer depth) {
        String prefix = StrUtil.EMPTY;
        while (depth > 0) {
            prefix = prefix + "-";
            depth--;
        }
        return prefix;
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return super.getWriteHandlerList();
    }

    @Override
    public String sheetName() {
        return "平台类目信息";
    }

    private String convertBoolean(Boolean flag) {
        return flag != null && flag ? CommonConstant.YES_STR : CommonConstant.NO_STR;
    }

}
