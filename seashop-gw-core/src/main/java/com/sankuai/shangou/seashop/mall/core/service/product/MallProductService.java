package com.sankuai.shangou.seashop.mall.core.service.product;


import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiProductPageResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;

/**
 * <AUTHOR>
 * @date 2024/01/03 10:40
 */
public interface MallProductService {

    /**
     * 查询商品
     *
     * @param request 查询参数
     * @return 商品列表
     */
    BasePageResp<ApiProductPageResp> queryProduct(ApiQueryProductPromotionExtReq request);
}
