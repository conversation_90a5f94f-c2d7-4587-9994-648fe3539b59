package com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiFlashSaleCategoryResp;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleCategoryResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCategoryQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@RestController
@RequestMapping("/mallApi/apiFlashSaleCategory")
public class MallApiFlashSaleCategoryQueryController {

    @Resource
    private FlashSaleCategoryQueryFeign flashSaleCategoryQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    public ResultDto<BasePageResp<ApiFlashSaleCategoryResp>> pageList(@RequestBody BasePageReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            BasePageResp<FlashSaleCategoryResp> flashSaleCategoryPage = ThriftResponseHelper.executeThriftCall(() -> flashSaleCategoryQueryFeign.pageList(req));
            return PageResultHelper.transfer(flashSaleCategoryPage, ApiFlashSaleCategoryResp.class);
        });
    }
}
