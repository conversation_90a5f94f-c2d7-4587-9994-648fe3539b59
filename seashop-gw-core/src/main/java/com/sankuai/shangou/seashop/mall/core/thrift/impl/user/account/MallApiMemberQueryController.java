package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.account;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.user.account.ApiMemberResp;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.mall.core.service.user.account.MallMemberService;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author： liweisong
 * @create： 2023/12/15 16:13
 */
@RestController
@RequestMapping("/mallApi/apiMember")
@Slf4j
public class MallApiMemberQueryController {

    @Resource
    private MallMemberService mallMemberService;

    @PostMapping(value = "/queryMember", consumes = "application/json")
    @NeedLogin(force = false)
    public ResultDto<ApiMemberResp> queryMember(@RequestBody BaseIdReq baseIdReq) throws TException {

        baseIdReq = new BaseIdReq();
        if (TracerUtil.getMemberDto() == null || TracerUtil.getMemberDto().getId() == null) {
            return ResultDto.newWithData(new ApiMemberResp());
        }
        baseIdReq.setId(TracerUtil.getMemberDto().getId());
        return ThriftResponseHelper.responseInvoke("queryMember", baseIdReq, req ->
            mallMemberService.queryMember(req));
    }

    @GetMapping(value = "/queryLoginInfo")
    @NeedLogin(force = false)
    public ResultDto<LoginMemberDto> queryLoginInfo() throws TException {
        

        if (TracerUtil.getMemberDto() == null&&StrUtil.isNotEmpty(TracerUtil.getMemberDto().getName())) {
            LoginMemberDto loginMemberDto = new LoginMemberDto();
            loginMemberDto.setName(TracerUtil.getMemberDto().getName());
            return ResultDto.newWithData(loginMemberDto);
        }
        return ResultDto.newWithData(TracerUtil.getMemberDto());
    }

}
