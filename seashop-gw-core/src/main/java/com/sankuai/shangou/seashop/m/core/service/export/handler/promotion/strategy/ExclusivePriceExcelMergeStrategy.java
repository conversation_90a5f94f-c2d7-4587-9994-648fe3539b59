package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.strategy;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;

/**
 * @author: lhx
 * @date: 2023/12/26/026
 * @description:
 */
public class ExclusivePriceExcelMergeStrategy extends AbstractMergeStrategy {
    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer integer) {

    }
}
