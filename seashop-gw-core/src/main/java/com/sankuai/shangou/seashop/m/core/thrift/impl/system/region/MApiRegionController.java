package com.sankuai.shangou.seashop.m.core.thrift.impl.system.region;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.RegionCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseUpdateRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseRegionRes;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseIdsReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseRegionReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseUpdateRegionReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mApi/apiRegion")
public class MApiRegionController {

    @Resource
    private RegionQueryFeign regionQueryFeign;

    @Resource
    private RegionCMDFeign regionCMDFeign;

    @GetMapping(value = "/SynMTRegionCode")
    public ResultDto<Boolean> SynMTRegionCode() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("SynMTRegionCode", i, req -> {
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                regionCMDFeign.SynMTRegionCode());
            return result;
        });
    }

    @GetMapping(value = "/getTreeRegions")
    public ResultDto<String> getTreeRegions() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("getTreeRegions", i, req -> {
            String result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getTreeRegions());
            return result;
        });
    }

    @GetMapping(value = "/getAllRegions")
    public ResultDto<List<BaseRegionRes>> getAllRegions() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("getAllRegions", i, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getAllRegions());
            return result;
        });
    }

    @GetMapping(value = "/getRegionByParentId")
    public ResultDto<List<BaseRegionRes>> getRegionByParentId(@RequestParam long parentId) throws TException {
        return ThriftResponseHelper.responseInvoke("getRegionByParentId", parentId, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getRegionByParentId(req));
            return result;
        });
    }

    @GetMapping(value = "/getParentRegions")
    public ResultDto<List<BaseRegionRes>> getParentRegions(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getParentRegions", id, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getParentRegions(req));
            return result;
        });
    }

    @GetMapping(value = "/getSubRegions")
    public ResultDto<List<BaseRegionRes>> getSubRegions(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getSubRegions", id, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getSubRegions(req));
            return result;
        });
    }

    @GetMapping(value = "/getTrackRegionsById")
    public ResultDto<List<BaseRegionRes>> getTrackRegionsById(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("getTrackRegionsById", id, req -> {
            List<BaseRegionRes> result = ThriftResponseHelper.executeThriftCall(() ->
                regionQueryFeign.getTrackRegionsById(req));
            return result;
        });
    }

    @PostMapping(value = "/createRegion", consumes = "application/json")
    public ResultDto<Long> createRegion(@RequestBody ApiBaseRegionReq regionReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createRegion", regionReq, req -> {
            BaseRegionReq bean = JsonUtil.copy(req, BaseRegionReq.class);

            Long result = ThriftResponseHelper.executeThriftCall(() ->
                regionCMDFeign.createRegion(bean));
            return result;
        });
    }

    @PostMapping(value = "/deleteRegion", consumes = "application/json")
    public ResultDto<Boolean> deleteRegion(@RequestBody ApiBaseIdsReq idsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createRegion", idsReq, req -> {
            BaseIdsReq bean = JsonUtil.copy(req, BaseIdsReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                regionCMDFeign.deleteRegion(bean));
            return result;
        });
    }

    @PostMapping(value = "/updateRegion", consumes = "application/json")
    public ResultDto<Boolean> updateRegion(@RequestBody ApiBaseUpdateRegionReq regionReq) throws TException {
        return ThriftResponseHelper.responseInvoke("createRegion", regionReq, req -> {
            req.checkParameter();
            BaseUpdateRegionReq bean = JsonUtil.copy(req, BaseUpdateRegionReq.class);
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                regionCMDFeign.updateRegion(bean));
            return result;
        });
    }

    @GetMapping(value = "/reviewRegion")
    public ResultDto<Boolean> reviewRegion() throws TException {
        return ThriftResponseHelper.responseInvoke("reviewRegion", null, req -> {
            Boolean result = ThriftResponseHelper.executeThriftCall(() ->
                regionCMDFeign.reviewRegion());
            return result;
        });
    }
}
