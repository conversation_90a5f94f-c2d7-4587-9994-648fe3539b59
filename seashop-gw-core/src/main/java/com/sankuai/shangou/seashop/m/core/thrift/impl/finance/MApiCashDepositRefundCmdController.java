package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.common.remote.MFinanceRemoteService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiCashDepositRefundConfirmReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiCashDepositRefundRefuseReq;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositRefundCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundConfirmReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundRefuseReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiCashDepositRefund")
public class MApiCashDepositRefundCmdController {

    @Resource
    private MFinanceRemoteService mFinanceRemoteService;
    @Resource
    private CashDepositRefundCmdFeign cashDepositRefundCmdFeign;

    @PostMapping(value = "/refuse", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> refuse(@RequestBody ApiCashDepositRefundRefuseReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("refuse", request, req -> {
            req.checkParameter();
            CashDepositRefundRefuseReq refuseReq = JsonUtil.copy(req, CashDepositRefundRefuseReq.class);
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            refuseReq.setOperator(loginDto.getName());
            refuseReq.setOperationUserId(loginDto.getId());
            refuseReq.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundCmdFeign.refuse(refuseReq));
        });
    }

    @PostMapping(value = "/confirm", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> confirm(@RequestBody ApiCashDepositRefundConfirmReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("confirm", request, req -> {
            req.checkParameter();
            CashDepositRefundConfirmReq confirmReq = JsonUtil.copy(req, CashDepositRefundConfirmReq.class);
            LoginManagerDto loginDto = TracerUtil.getManagerDto();
            confirmReq.setOperator(loginDto.getName());
            confirmReq.setOperationUserId(loginDto.getId());
            confirmReq.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundCmdFeign.confirm(confirmReq));
        });
    }
}
