package com.sankuai.shangou.seashop.mall.core.thrift.impl.product;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryShopCategoryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiShopCategoryTreeResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.common.remote.product.MallShopCategoryRemoteService;
import com.sankuai.shangou.seashop.mall.common.remote.product.model.RemoteShopCategoryBo;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/04 11:53
 */
@RestController
@RequestMapping("/mallApi/apiShopCategory")
public class MallApiShopCategoryQueryController {

    @Resource
    private MallShopCategoryRemoteService mallShopCategoryRemoteService;

    @PostMapping(value = "/queryShopCategory", consumes = "application/json")
    public ResultDto<ApiShopCategoryTreeResp> queryShopCategory(@RequestBody ApiQueryShopCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopCategory", request, req -> {
            req.checkParameter();

            List<RemoteShopCategoryBo> shopCategoryList = mallShopCategoryRemoteService.getShopCategoryList(req.getShopId(), Boolean.TRUE);
            return ApiShopCategoryTreeResp.builder().result(JsonUtil.toJsonString(shopCategoryList)).build();
        });

    }
}
