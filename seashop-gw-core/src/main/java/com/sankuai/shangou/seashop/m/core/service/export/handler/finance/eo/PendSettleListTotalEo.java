package com.sankuai.shangou.seashop.m.core.service.export.handler.finance.eo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
@Getter
@Setter
public class PendSettleListTotalEo {

    @ExcelProperty(value = "店铺ID")
    private Long shopId;

    @ExcelProperty(value = "供应商名称")
    private String shopName;

    @ExcelProperty(value = "本期应结")
    private BigDecimal totalAmount;

    @ExcelProperty(value = "预计结算时间")
    private String settlementTimeStr;

    @ExcelProperty(value = "结算周期")
    private String settlementCycle;
}
