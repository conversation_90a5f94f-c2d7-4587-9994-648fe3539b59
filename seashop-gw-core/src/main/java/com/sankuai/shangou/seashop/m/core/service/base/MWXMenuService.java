package com.sankuai.shangou.seashop.m.core.service.base;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiBaseWXMenuReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiSaveWXAccountReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBaseWXMenuListRes;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBaseWXMenuRes;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiWXAccountResp;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdCreateQRReq;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
public interface MWXMenuService {
    List<ApiBaseWXMenuListRes> queryWXMenus();

    ApiBaseWXMenuRes queryById(BaseIdReq idReq);

    void saveWXAccount(ApiSaveWXAccountReq req);

    Integer create(ApiBaseWXMenuReq req);

    Boolean update(ApiBaseWXMenuReq wxMenuReq);

    Boolean delete(BaseIdReq baseIdReq);

    Boolean syncWXMenu();

    ApiWXAccountResp getWXAccount();

    String createQrCode(ApiCmdCreateQRReq req);

}
