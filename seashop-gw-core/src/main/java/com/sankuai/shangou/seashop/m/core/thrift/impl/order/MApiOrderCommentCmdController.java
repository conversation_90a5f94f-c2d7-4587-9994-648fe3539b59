package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.m.thrift.core.request.order.ApiDeleteOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCommentCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.DeleteOrderCommentReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/21 19:54
 */
@RestController
@RequestMapping("/mApi/apiOrderComment")
@Slf4j
public class MApiOrderCommentCmdController {

    @Resource
    private OrderCommentCmdFeign orderCommentCmdFeign;

    @PostMapping(value = "/deleteOrderCommentForPlatForm", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> deleteOrderCommentForPlatForm(@RequestBody ApiDeleteOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteOrderCommentForPlatForm", request, req -> {
            req.checkParameter();

            return ThriftResponseHelper.executeThriftCall(() ->
                    orderCommentCmdFeign.deleteOrderCommentForPlatForm(JsonUtil.copy(req, DeleteOrderCommentReq.class)));
        });
    }
}
