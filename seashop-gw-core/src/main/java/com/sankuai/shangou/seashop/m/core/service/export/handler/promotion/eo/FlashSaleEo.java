package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2023/12/25/025
 * @description:
 */
@Getter
@Setter
public class FlashSaleEo {

    @ExcelProperty(value = "活动名称")
    private String title;

    @ExcelProperty(value = "活动ID")
    private Long id;

    @ExcelProperty(value = "商品ID")
    private String productId;

    @ExcelProperty(value = "商品名称")
    private String productName;

    /**
     * 状态：1待审核,2进行中,3未通过,4已结束,5已取消,6未开始
     */
    @ExcelIgnore
    @ExcelProperty(value = "状态：1待审核 2进行中 3未通过 4已结束 5已取消 6未开始")
    private Integer status;

    @ExcelProperty(value = "状态")
    private String statusName;

    @ExcelIgnore
    @ExcelProperty(value = "店铺ID")
    private Long shopId;

    @ExcelProperty(value = "店铺")
    private String shopName;

    @ExcelIgnore
    @ExcelProperty(value = "跳转地址")
    private String urlPath;

    @ExcelProperty(value = "开始日期")
    private String beginDate;

    @ExcelProperty(value = "结束日期")
    private String endDate;

    @ExcelIgnore
    @ExcelProperty(value = "限购数量")
    private Integer limitCount;

    @ExcelProperty(value = "购买数")
    private Integer saleCount;
}
