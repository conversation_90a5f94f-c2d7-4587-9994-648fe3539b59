package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.product.thrift.core.ShopCategoryCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.DeleteShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.SaveShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.TransferProductReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiSaveShopCategoryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiTransferProductReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:12
 */
@RestController
@RequestMapping("/sellerApi/apiShopCategory")
public class SellerApiShopCategoryCmdController {

    @Resource
    private ShopCategoryCmdFeign shopCategoryCmdFeign;

    @PostMapping(value = "/createShopCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> createShopCategory(@RequestBody ApiSaveShopCategoryReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("createShopCategory", request, req -> {

            SaveShopCategoryReq saveReq = JsonUtil.copy(req, SaveShopCategoryReq.class);
            saveReq.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> shopCategoryCmdFeign.createShopCategory(saveReq));
        });
    }

    @PostMapping(value = "/updateShopCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateShopCategory(@RequestBody ApiSaveShopCategoryReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("updateShopCategory", request, req -> {

            SaveShopCategoryReq saveReq = JsonUtil.copy(req, SaveShopCategoryReq.class);
            saveReq.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> shopCategoryCmdFeign.updateShopCategory(saveReq));
        });
    }

    @PostMapping(value = "/deleteShopCategory", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> deleteShopCategory(@RequestBody BaseIdReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("deleteShopCategory", request, req -> {

            DeleteShopCategoryReq deleteReq = JsonUtil.copy(req, DeleteShopCategoryReq.class);
            deleteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> shopCategoryCmdFeign.deleteShopCategory(deleteReq));
        });
    }

    @PostMapping(value = "/transferProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> transferProduct(@RequestBody ApiTransferProductReq request) throws TException {

        return ThriftResponseHelper.responseInvoke("transferProduct", request, req -> {
            req.checkParameter();
            TransferProductReq transferProductReq = JsonUtil.copy(req, TransferProductReq.class);
            return ThriftResponseHelper.executeThriftCall(() -> shopCategoryCmdFeign.transferProduct(transferProductReq));
        });
    }
}
