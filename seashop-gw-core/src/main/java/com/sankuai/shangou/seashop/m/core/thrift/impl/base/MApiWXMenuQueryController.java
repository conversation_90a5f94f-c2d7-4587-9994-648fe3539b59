package com.sankuai.shangou.seashop.m.core.thrift.impl.base;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.base.MWXMenuService;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBaseWXMenuListRes;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiBaseWXMenuRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
@RestController
@RequestMapping("/mApi/wxMenu")
public class MApiWXMenuQueryController {
    @Resource
    private MWXMenuService mWxMenuService;
    @GetMapping(value = "getWXMenus")
    public ResultDto<List<ApiBaseWXMenuListRes>> queryWXMenus() throws TException {

        return ThriftResponseHelper.responseInvoke("queryWXMenus",null,rep->{
            return mWxMenuService.queryWXMenus();
        });
    }

    @PostMapping(value = "queryWXMenuById", consumes = "application/json")
    public ResultDto<ApiBaseWXMenuRes> queryWXMenuById(@RequestBody BaseIdReq idReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWXMenuById",idReq,rep->{
            return mWxMenuService.queryById(idReq);
        });
    }
}
