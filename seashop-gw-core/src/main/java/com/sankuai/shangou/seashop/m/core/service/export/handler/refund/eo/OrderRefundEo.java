package com.sankuai.shangou.seashop.m.core.service.export.handler.refund.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderRefundEo {

    @ExcelProperty(value = "退款ID")
    private String refundId;
    @ExcelProperty(value = "订单号")
    private String orderId;
    @ExcelProperty(value = "店铺ID")
    private Long shopId;
    @ExcelProperty(value = "店铺名称")
    private String shopName;
    @ExcelProperty(value = "订单实付金额")
    private BigDecimal orderPayAmount;
    /**
     * 买家ID
     */
    @ExcelProperty(value = "买家ID")
    private Long userId;
    /**
     * 买家账号
     */
    @ExcelProperty(value = "买家账号")
    private String userName;
    /**
     * 退款数量
     */
    @ExcelProperty(value = "退货数量")
    private Long refundQuantity;
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ExcelProperty(value = "申请日期")
    private Date applyDate;
    /*@ExcelProperty(value = "退款状态")
    private Integer refundStatus;*/
    @ExcelProperty(value = "退款状态")
    private String refundStatusDesc;
    /*@ExcelProperty(value = "是否可以重新申请")
    private Boolean canReapply;*/
    @ExcelProperty(value = "是否订单全部退")
    private String hasAllReturnStr;
    /**
     * 是否显示【查看物流】按钮
     * 供应商登录，买家填写了物流信息显示此按钮
     */
    /*@ExcelProperty(value = "是否显示【查看物流】按钮")
    private Boolean showWayBillBtn;*/

}
