package com.sankuai.shangou.seashop.mall.core.thrift.impl.user.shop;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiAddFavoriteProductReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.user.shop.ApiDeleteFavoriteProductReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.FavoriteProductCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddFavoriteProductReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFavoriteProductReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description：商品关注、删除接口
 * @author： liweisong
 * @create： 2023/11/28 9:47
 */
@RestController
@RequestMapping("/mallApi/apiFavoriteProduct")
@Slf4j
public class MallApiFavoriteProductCmdController {

    @Resource
    private FavoriteProductCmdFeign favoriteProductCmdFeign;

    @PostMapping(value = "/addFavoriteProduct", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> addFavoriteProduct(@RequestBody ApiAddFavoriteProductReq addFavoriteProductReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addFavoriteProduct", addFavoriteProductReq, req -> {
            

            req.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() ->
                    favoriteProductCmdFeign.addFavoriteProduct(JsonUtil.copy(req, AddFavoriteProductReq.class)));
        });
    }

    @PostMapping(value = "/deleteFavoriteProduct", consumes = "application/json")
    @NeedLogin
    public ResultDto<BaseResp> deleteFavoriteProduct(@RequestBody ApiDeleteFavoriteProductReq deleteFavoriteProductReq) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteFavoriteProduct", deleteFavoriteProductReq, req -> {
            

            req.setUserId(TracerUtil.getMemberDto().getId());
            log.info("取消商品关注入参deleteFavoriteProductReq:{}", req);
            DeleteFavoriteProductReq bean = JsonUtil.copy(req, DeleteFavoriteProductReq.class);
            log.info("取消商品关注入参bean:{}", bean);
            return ThriftResponseHelper.executeThriftCall(() -> favoriteProductCmdFeign.deleteFavoriteProduct(bean));
        });
    }
}
