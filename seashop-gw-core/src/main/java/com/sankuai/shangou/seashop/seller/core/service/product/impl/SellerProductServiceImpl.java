package com.sankuai.shangou.seashop.seller.core.service.product.impl;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductIdListResp;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.common.remote.erp.SellerErpProductRemoteService;
import com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerPromotionRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.SellerExportTaskBiz;
import com.sankuai.shangou.seashop.seller.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.seller.core.service.product.SellerProductService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.enums.PromotionTypeEnum;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryPromotionReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductPageResp;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2024/01/03 10:41
 */
@Service
public class SellerProductServiceImpl implements SellerProductService {

    @Resource
    private SellerExportTaskBiz sellerExportTaskBiz;
    @Resource
    private SellerErpProductRemoteService sellerErpProductRemoteService;
    @Resource
    private SellerProductRemoteService sellerProductRemoteService;
    @Resource
    private SellerPromotionRemoteService sellerPromotionRemoteService;

    @Override
    public void exportProduct(ApiQueryProductReq request, LoginShopDto shopInfo) {
        QueryProductReq remoteReq = JsonUtil.copy(request, QueryProductReq.class);
        remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
        ProductStatusEnum status = ProductStatusEnum.getByCode(request.getStatusCode());
        remoteReq.setStatus(status);
        // 默认查看销售中的数据
        if (status == null || status.equals(ProductStatusEnum.NONE)) {
            remoteReq.setStatus(ProductStatusEnum.ON_SALE);
        }

        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.PRODUCT_PAGE_LIST);
        createExportTaskBo.setExecuteParam(remoteReq);
        createExportTaskBo.setOperatorId(TracerUtil.getShopDto().getManagerId());
        createExportTaskBo.setOperatorName(shopInfo.getManagerName());
        sellerExportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public Boolean hasBind(Long productId) {
        ProductBasicDto product = sellerProductRemoteService.getProductBasic(productId);
        if (product == null) {
            return Boolean.FALSE;
        }

        return sellerErpProductRemoteService.hasBind(product.getShopId(), productId);
    }

    @Override
    public BasePageResp<ApiProductPageResp> queryProduct(ApiQueryProductPromotionExtReq request) {

        BasePageResp<ApiProductPageResp> resultPage = null;

        ApiQueryPromotionReq promotion = request.getPromotion();
        if (null != promotion) {
            // 如果有营销查询条件，则进行营销查询处理
            PromotionTypeEnum promotionType = PromotionTypeEnum.getByCode(promotion.getPromotionType());
            switch (promotionType) {
                case LIMITED_TIME_PURCHASE:
                case COMBINATION_PURCHASE:
                    /*
                        排除商品：
                        1、已经参加了限时购活动的商品
                        2、已经参加了组合购活动的商品
                        3、已经开启了阶梯价的商品
                     */
                    ShopIdReq shopIdReq = new ShopIdReq();
                    shopIdReq.setShopId(request.getShopId());
                    ProductIdListResp productIdListResp = sellerPromotionRemoteService.collocationFlashSaleProductId(shopIdReq);

                    QueryProductReq remoteReq = JsonUtil.copy(request, QueryProductReq.class);
                    if (null != productIdListResp && CollUtil.isNotEmpty(productIdListResp.getProductIdList())) {
                        // 排除掉已经参加了限时购、组合购活动的商品
                        remoteReq.setExcludeProductIds(productIdListResp.getProductIdList());
                    }
                    if (null == remoteReq.getStatus()) {
                        // 如果没传状态，默认查询销售中的商品
                        remoteReq.setStatus(ProductStatusEnum.ON_SALE);
                    }
                    // 默认不查询阶梯价商品
                    remoteReq.setWhetherOpenLadder(Boolean.FALSE);

                    BasePageResp<ProductPageResp> respPage = sellerProductRemoteService.queryProduct(remoteReq);
                    resultPage = PageResultHelper.transfer(respPage, ApiProductPageResp.class, resp -> {
                    });
                    break;
                default:
                    break;
            }
        }
        else {
            QueryProductReq remoteReq = JsonUtil.copy(request, QueryProductReq.class);
            if (null == remoteReq.getStatus()) {
                // 如果没传状态，默认查询销售中的商品
                remoteReq.setStatus(ProductStatusEnum.ON_SALE);
            }
            BasePageResp<ProductPageResp> resp = sellerProductRemoteService.queryProduct(remoteReq);
            resultPage = PageResultHelper.transfer(resp, ApiProductPageResp.class);
        }
        return resultPage;
    }
}
