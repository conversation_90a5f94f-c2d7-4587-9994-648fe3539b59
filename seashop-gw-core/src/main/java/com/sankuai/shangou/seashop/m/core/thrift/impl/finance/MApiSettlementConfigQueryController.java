package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiSettlementConfigService;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiSettlementConfigResp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiSettlementConfig")
public class MApiSettlementConfigQueryController {

    @Resource
    private MApiSettlementConfigService mApiSettlementConfigService;

    @GetMapping(value = "/getConfig")
    public ResultDto<ApiSettlementConfigResp> getConfig() {
        return ThriftResponseHelper.responseInvoke("getConfig", null, req ->
            mApiSettlementConfigService.getConfig()
        );
    }
}
