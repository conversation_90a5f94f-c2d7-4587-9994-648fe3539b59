package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.promotion.ApiCouponProductDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiCouponProductQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiCouponQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiCouponListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiCouponResp;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiCoupon")
public class SellerApiCouponQueryController {

    @Resource
    private CouponQueryFeign couponQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiCouponListResp>> pageList(@RequestBody ApiCouponQueryReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<CouponSimpleResp> couponPage = ThriftResponseHelper.executeThriftCall(() ->
                    couponQueryFeign.pageList(JsonUtil.copy(req, CouponQueryReq.class)));
            return PageResultHelper.transfer(couponPage, ApiCouponListResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiCouponResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            CouponResp coupon = ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.getById(req));
            return JsonUtil.copy(coupon, ApiCouponResp.class);
        });
    }

    @PostMapping(value = "/queryCouponProductPage", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiCouponProductDto>> queryCouponProductPage(@RequestBody ApiCouponProductQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCouponProductList", request, req -> {
            req.checkParameter();

            BasePageResp<CouponProductDto> couponProductPage = ThriftResponseHelper.executeThriftCall(() ->
                    couponQueryFeign.queryCouponProductPage(JsonUtil.copy(req, CouponProductQueryReq.class)));
            return PageResultHelper.transfer(couponProductPage, ApiCouponProductDto.class);
        });
    }
}
