package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.refund.ApiQueryOrderItemRefundPreviewReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.refund.ApiQueryOrderRefundPreviewReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.refund.ApiQueryRefundDetailReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.refund.ApiUserQueryRefundReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.refund.*;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.refund.dto.ApiUserRefundDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.mall.core.service.refund.MallRefundService;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundUserQueryTabEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.QueryOrderItemRefundPreviewReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.QueryOrderRefundPreviewReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiOrderRefund")
@Slf4j
public class MallApiOrderRefundQueryController {

    @Resource
    private MallRefundService mallRefundService;
    @Resource
    private OrderRefundQueryFeign orderRefundQueryFeign;

    private final static String[] IGNORE_PROPERTIES = {"tab", "orderStatus"};


    @NeedLogin
    @PostMapping(value = "/getOrderRefundPreview", consumes = "application/json")
    public ResultDto<ApiOrderRefundPreviewResp> getOrderRefundPreview(@RequestBody ApiQueryOrderRefundPreviewReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家获取订单退款预览", queryReq, func -> {
            // 入参转换
            QueryOrderRefundPreviewReq req = JsonUtil.copy(queryReq, QueryOrderRefundPreviewReq.class);
            // 设置用户其他信息
            UserDto user = new UserDto();

            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());

            req.setUser(user);
            // 业务逻辑处理
            OrderRefundPreviewResp resp = ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.getOrderRefundPreview(req));
            return JsonUtil.copy(resp, ApiOrderRefundPreviewResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/getOrderItemRefundPreview", consumes = "application/json")
    public ResultDto<ApiOrderItemRefundPreviewResp> getOrderItemRefundPreview(@RequestBody ApiQueryOrderItemRefundPreviewReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家获取订单明细退款预览", queryReq, func -> {
            // 入参转换
            QueryOrderItemRefundPreviewReq req = JsonUtil.copy(queryReq, QueryOrderItemRefundPreviewReq.class);
            // 设置用户其他信息
            UserDto user = new UserDto();
            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            // 业务逻辑处理
            OrderItemRefundPreviewResp resp = ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.getOrderItemRefundPreview(req));
            return JsonUtil.copy(resp, ApiOrderItemRefundPreviewResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/userQueryDetail", consumes = "application/json")
    public ResultDto<ApiUserRefundDetailResp> userQueryDetail(@RequestBody ApiQueryRefundDetailReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】获取售后详情", queryReq, func -> {
            // 入参转换
            UserQueryRefundDetailReq req = JsonUtil.copy(queryReq, UserQueryRefundDetailReq.class);

            UserDto user = new UserDto();
            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            // 业务逻辑处理
            UserRefundDetailResp resp = ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.userQueryDetail(req));
            return JsonUtil.copy(resp, ApiUserRefundDetailResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/userQueryDetailExt", consumes = "application/json")
    public ResultDto<ApiUserRefundDetailExtResp> userQueryDetailExt(@RequestBody ApiQueryRefundDetailReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】获取售后详情", queryReq, func -> {
            // 入参转换
            UserQueryRefundDetailReq req = JsonUtil.copy(queryReq, UserQueryRefundDetailReq.class);

            UserDto user = new UserDto();

            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            // 业务逻辑处理
            UserRefundDetailExtResp resp = ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.userQueryDetailExt(req));
            return JsonUtil.copy(resp, ApiUserRefundDetailExtResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/queryRefundLog", consumes = "application/json")
    public ResultDto<List<ApiRefundLogResp>> queryRefundLog(@RequestBody BaseIdReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】获取售后操作日志", queryReq, func -> {
            // 业务逻辑处理
            RefundLogListResp resp = ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryRefundLog(queryReq));

            LoginMemberDto loginDto = TracerUtil.getMemberDto();;
            if (!loginDto.getId().equals(resp.getUserId())) {
                throw new BusinessException("只能查看自己的数据");
            }
            return JsonUtil.copyList(resp.getLogList(), ApiRefundLogResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/userQueryRefundPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiUserRefundDto>> userQueryRefundPage(@RequestBody ApiUserQueryRefundReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家分页查询售后列表", queryReq, func -> {
            // 入参转换
            UserQueryRefundReq req = JsonUtil.copy(queryReq, UserQueryRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setTab(RefundUserQueryTabEnum.valueOf(queryReq.getTab()));
            req.setOrderStatus(OrderStatusEnum.valueOf(queryReq.getOrderStatus()));
            // 设置用户其他信息
            UserDto user = new UserDto();
            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            // 业务逻辑处理
            BasePageResp<UserRefundDto> resp = ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.userQueryRefundPage(req));
            return PageResultHelper.transfer(resp, ApiUserRefundDto.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/queryUserDeliverExpress", consumes = "application/json")
    public ResultDto<ApiRefundUserDeliverExpressResp> queryUserDeliverExpress(@RequestBody BaseIdReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家查询寄货物流信息", queryReq, func -> {
            // 业务逻辑处理
            RefundUserDeliverExpressResp resp = ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryUserDeliverExpress(queryReq));
            return JsonUtil.copy(resp, ApiRefundUserDeliverExpressResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/exportForUser", consumes = "application/json")
    public ResultDto<RefundExportResp> exportForUser(@RequestBody ApiUserQueryRefundReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】买家-导出售后分页列表", queryReq, func -> {
            // 入参转换
            UserQueryRefundReq req = JsonUtil.copy(queryReq, UserQueryRefundReq.class, IGNORE_PROPERTIES);
            // 枚举转变换
            req.setTab(RefundUserQueryTabEnum.valueOf(queryReq.getTab()));
            req.setOrderStatus(OrderStatusEnum.valueOf(queryReq.getOrderStatus()));
            // 设置用户其他信息
            UserDto user = new UserDto();
            LoginMemberDto loginMemberDto = TracerUtil.getMemberDto();;
            user.setUserId(loginMemberDto.getId());
            user.setUserName(loginMemberDto.getName());
            user.setUserPhone(loginMemberDto.getUserPhone());
            req.setUser(user);
            // 业务逻辑处理
            return mallRefundService.exportRefund(req);
        });
    }

}
