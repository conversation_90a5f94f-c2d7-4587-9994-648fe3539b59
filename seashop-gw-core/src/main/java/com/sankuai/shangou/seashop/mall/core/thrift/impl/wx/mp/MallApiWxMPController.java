package com.sankuai.shangou.seashop.mall.core.thrift.impl.wx.mp;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiBaseRegionRes;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.RegionCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.WXRequestCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.JsApiSignatureReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseRegionRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.JsApiSignatureRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mallApi/wxmp")
public class MallApiWxMPController {

    @Resource
    private WXRequestCMDFeign wxRequestCMDFeign;
    @PostMapping(value = "/getJsApiSignature", consumes = "application/json")
    public ResultDto<JsApiSignatureRes> getJsApiSignature(@RequestBody JsApiSignatureReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getJsApiSignature", request, req -> {
            JsApiSignatureRes result = ThriftResponseHelper.executeThriftCall(() ->
                    wxRequestCMDFeign.getJsApiSignature(req));
            return result;
        });
    }


}
