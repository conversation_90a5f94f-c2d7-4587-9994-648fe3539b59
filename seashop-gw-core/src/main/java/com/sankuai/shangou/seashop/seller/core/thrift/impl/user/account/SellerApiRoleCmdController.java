package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.account;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiRoleService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiCmdRoleReq;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/sellerApi/apiRole")
public class SellerApiRoleCmdController {
    @Resource
    private SellerApiRoleService roleService;

    @PostMapping(value = "/addRole", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> addRole(@RequestBody ApiCmdRoleReq cmdRoleReq) {
        
        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        cmdRoleReq.setShopId(loginShopDto.getShopId());
        return ThriftResponseHelper.responseInvoke("addRole", cmdRoleReq, req -> roleService.addRole(cmdRoleReq));
    }

    @PostMapping(value = "/editRole", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> editRole(@RequestBody ApiCmdRoleReq cmdRoleReq) {
        
        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        cmdRoleReq.setShopId(loginShopDto.getShopId());
        return ThriftResponseHelper.responseInvoke("editRole", cmdRoleReq, req -> roleService.editRole(cmdRoleReq));
    }

    @PostMapping(value = "/deleteRole", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> deleteRole(@RequestBody ApiCmdRoleReq cmdRoleReq) {
        
        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        cmdRoleReq.setShopId(loginShopDto.getShopId());
        return ThriftResponseHelper.responseInvoke("deleteRole", cmdRoleReq, req -> roleService.deleteRole(cmdRoleReq));
    }
}
