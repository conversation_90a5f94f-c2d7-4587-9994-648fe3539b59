package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo;

import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.OnceAbsoluteMerge;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2023/12/26/026
 * @description:
 */
@Getter
@Setter
@OnceAbsoluteMerge(firstColumnIndex = 4, lastColumnIndex = 11)
public class ExclusivePriceEo {

    @ExcelIgnore
    @ExcelProperty(value = "主键ID")
    private Long id;

    @ExcelProperty(value = "活动名称")
    private String name;

    @ExcelProperty(value = "店铺ID")
    private Long shopId;

    @ExcelProperty(value = "店铺名称")
    private String shopName;

    @ExcelProperty(value = "开始时间")
    private Date startTime;

    @ExcelProperty(value = "结束时间")
    private Date endTime;

    @ExcelProperty(value = "专享价明细信息")
    private List<ExclusivePriceProductEo> productList;
}
