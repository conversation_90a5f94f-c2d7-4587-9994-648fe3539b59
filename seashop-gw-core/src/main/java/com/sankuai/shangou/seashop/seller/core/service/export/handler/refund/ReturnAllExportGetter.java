package com.sankuai.shangou.seashop.seller.core.service.export.handler.refund;

import java.util.Collections;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundReq;
import com.sankuai.shangou.seashop.seller.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.refund.eo.OrderReturnEo;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.refund.wrapper.ReturnDataWrapper;

/**
 * <AUTHOR>
 */
@Service
public class ReturnAllExportGetter extends AbstractBaseDataGetter<SellerQueryRefundReq>
    implements SingleWrapperDataGetter<SellerQueryRefundReq> {


    @Override
    public DataContext selectData(SellerQueryRefundReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        // 构造数据包装器，每一个包装器对应一个sheet页，并且每个包装器可以单独定义自己的excel处理器，比如样式、合并单元格等
        BaseExportWrapper<OrderReturnEo, SellerQueryRefundReq> refundWrapper = new ReturnDataWrapper();
        context.setSheetDataList(Collections.singletonList(refundWrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.RETURN_ALL_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.RETURN_ALL_LIST.getName();
    }
}
