package com.sankuai.shangou.seashop.seller.core.thrift.impl.product;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseImportReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.BindDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.BindFreightTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.BindRecommendProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductDeleteReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductImportReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductOnOffSaleReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuUpdatePriceReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateSafeStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateSequenceReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.SaveProductReq;
import com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiBindDescriptionTemplateReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiBindFreightTemplateReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiBindRecommendProductReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductDeleteReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductImportReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductOnOffSaleReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductSkuUpdatePriceReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductUpdateSafeStockReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductUpdateSequenceReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductUpdateStockReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiSaveProductReq;

/**
 * <AUTHOR>
 * @date 2023/11/14 11:45
 */
@RestController
@RequestMapping("/sellerApi/apiProduct")
public class SellerApiProductCmdController {

    @Resource
    private SellerProductRemoteService sellerProductRemoteService;

    @PostMapping(value = "/createProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> createProduct(@RequestBody ApiSaveProductReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("createProduct", request, req -> {
            req.checkParameter();

            SaveProductReq remoteReq = JsonUtil.copy(req, SaveProductReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.createProduct(remoteReq);
        });
    }

    @PostMapping(value = "/updateProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateProduct(@RequestBody ApiSaveProductReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("updateProduct", request, req -> {
            req.checkParameter();

            SaveProductReq remoteReq = JsonUtil.copy(req, SaveProductReq.class);
            if(remoteReq.getDescriptionPrefixId() ==null){
                remoteReq.setDescriptionPrefixId(0L);
            }
            if(remoteReq.getDescriptionSuffixId() ==null){
                remoteReq.setDescriptionSuffixId(0L);
            }
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.updateProduct(remoteReq);
        });
    }

    @PostMapping(value = "/batchOnOffSaleProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> batchOnOffSaleProduct(@RequestBody ApiProductOnOffSaleReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("batchOnOffSaleProduct", request, req -> {
            req.checkParameter();

            ProductOnOffSaleReq remoteReq = JsonUtil.copy(req, ProductOnOffSaleReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.batchOnOffSaleProduct(remoteReq);
        });
    }

    @PostMapping(value = "/batchDeleteProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> batchDeleteProduct(@RequestBody ApiProductDeleteReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("batchDeleteProduct", request, req -> {
            req.checkParameter();

            ProductDeleteReq remoteReq = JsonUtil.copy(req, ProductDeleteReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.batchDeleteProduct(remoteReq);
        });
    }

    @PostMapping(value = "/batchSaveProductShopSequence", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> batchSaveProductShopSequence(@RequestBody ApiProductUpdateSequenceReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("batchSaveProductShopSequence", request, req -> {
            req.checkParameter();

            ProductUpdateSequenceReq remoteReq = JsonUtil.copy(req, ProductUpdateSequenceReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.batchSaveProductShopSequence(remoteReq);
        });
    }

    @PostMapping(value = "/batchBindDescriptionTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> batchBindDescriptionTemplate(@RequestBody ApiBindDescriptionTemplateReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("batchBindDescriptionTemplate", request, req -> {
            req.checkParameter();

            BindDescriptionTemplateReq remoteReq = JsonUtil.copy(req, BindDescriptionTemplateReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.batchBindDescriptionTemplate(remoteReq);
        });
    }

    @PostMapping(value = "/bindRecommendProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> bindRecommendProduct(@RequestBody ApiBindRecommendProductReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("bindRecommendProduct", request, req -> {
            req.checkParameter();

            BindRecommendProductReq remoteReq = JsonUtil.copy(req, BindRecommendProductReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.bindRecommendProduct(remoteReq);
        });
    }

    @PostMapping(value = "/batchBindFreightTemplate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> batchBindFreightTemplate(@RequestBody ApiBindFreightTemplateReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("batchBindFreightTemplate", request, req -> {
            req.checkParameter();

            BindFreightTemplateReq remoteReq = JsonUtil.copy(req, BindFreightTemplateReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.batchBindFreightTemplate(remoteReq);
        });
    }

    @PostMapping(value = "/batchUpdateProductPrice", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> batchUpdateProductPrice(@RequestBody ApiProductSkuUpdatePriceReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("batchUpdateProductPrice", request, req -> {
            req.checkParameter();

            ProductSkuUpdatePriceReq remoteReq = JsonUtil.copy(req, ProductSkuUpdatePriceReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.batchUpdateProductPrice(remoteReq);
        });
    }

    @PostMapping(value = "/batchUpdateSafeStock", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> batchUpdateSafeStock(@RequestBody ApiProductUpdateSafeStockReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("batchUpdateSafeStock", request, req -> {
            req.checkParameter();

            ProductUpdateSafeStockReq remoteReq = JsonUtil.copy(req, ProductUpdateSafeStockReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.batchUpdateSafeStock(remoteReq);
        });
    }

    @PostMapping(value = "/batchUpdateStock", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> batchUpdateStock(@RequestBody ApiProductUpdateStockReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("batchUpdateStock", request, req -> {
            req.checkParameter();

            ProductUpdateStockReq remoteReq = JsonUtil.copy(req, ProductUpdateStockReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.batchUpdateStock(remoteReq);
        });
    }

    @PostMapping(value = "/importStock", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseImportResp> importStock(@RequestBody BaseImportReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("importStock", request, req -> {
            req.checkParameter();

            ProductImportReq remoteReq = JsonUtil.copy(req, ProductImportReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.importStock(remoteReq);
        });
    }

    @PostMapping(value = "/importOffSale", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseImportResp> importOffSale(@RequestBody BaseImportReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("importOffSale", request, req -> {
            req.checkParameter();

            ProductImportReq remoteReq = JsonUtil.copy(req, ProductImportReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.importOffSale(remoteReq);
        });
    }

    @PostMapping(value = "/importPrice", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseImportResp> importPrice(@RequestBody BaseImportReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("importPrice", request, req -> {
            req.checkParameter();

            ProductImportReq remoteReq = JsonUtil.copy(req, ProductImportReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.importPrice(remoteReq);
        });
    }

    @PostMapping(value = "/importProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseImportResp> importProduct(@RequestBody ApiProductImportReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("importProduct", request, req -> {
            req.checkParameter();

            ProductImportReq remoteReq = JsonUtil.copy(req, ProductImportReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.importProduct(remoteReq);
        });
    }

    @PostMapping(value = "/importProductUpdate", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseImportResp> importProductUpdate(@RequestBody BaseImportReq request) throws TException {

        

        return ThriftResponseHelper.responseInvoke("importProductUpdate", request, req -> {
            req.checkParameter();

            ProductImportReq remoteReq = JsonUtil.copy(req, ProductImportReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return sellerProductRemoteService.importProductUpdate(remoteReq);
        });
    }
}
