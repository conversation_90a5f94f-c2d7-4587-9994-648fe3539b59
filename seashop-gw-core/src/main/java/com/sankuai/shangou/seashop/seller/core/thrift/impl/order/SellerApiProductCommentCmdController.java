package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.ReplyProductCommentReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiReplyProductCommentReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/21 19:44
 */
@RestController
@RequestMapping("/sellerApi/apiProductComment")
@Slf4j
public class SellerApiProductCommentCmdController {

    @Resource
    private ProductCommentCmdFeign productCommentCmdFeign;

    @PostMapping(value = "/replyProductCommentForSeller", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> replyProductCommentForSeller(@RequestBody ApiReplyProductCommentReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("replyProductCommentForSeller", request, req -> {
            ReplyProductCommentReq remoteReq = JsonUtil.copy(req, ReplyProductCommentReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            return ThriftResponseHelper.executeThriftCall(() -> productCommentCmdFeign.replyProductCommentForSeller(remoteReq));
        });
    }
}
