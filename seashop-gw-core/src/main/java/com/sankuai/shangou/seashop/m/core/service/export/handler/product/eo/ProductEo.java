package com.sankuai.shangou.seashop.m.core.service.export.handler.product.eo;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.date.DateDateConverter;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/01/02 17:49
 */
@Getter
@Setter
public class ProductEo {

    @ExcelIgnore
    private Long productId;

    @ExcelProperty(value = "商品ID")
    private String productIdStr;

    @ExcelProperty(value = "商品")
    private String productName;

    @ExcelProperty(value = "店铺")
    private String shopName;

    @ExcelProperty(value = "销售状态")
    private String saleStatusDesc;

    @ExcelProperty(value = "一级分类")
    private String firstCategoryName;

    @ExcelProperty(value = "二级分类")
    private String secondCategoryName;

    @ExcelProperty(value = "三级分类")
    private String thirdCategoryName;

    @ExcelProperty(value = "品牌")
    private String brandName;

    @ExcelProperty(value = "货号")
    private String productCode;

    @ExcelProperty(value = "店铺分类")
    private String shopCategoryNames;

    @ExcelProperty(value = "审核状态")
    private String auditStatusDesc;

    @ExcelProperty(value = "审核备注")
    private String auditReason;

    @ExcelProperty(value = "发布时间", converter = DateDateConverter.class)
    private Date addedDate;

    @ExcelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ExcelProperty(value = "计量单位")
    private String measureUnit;

    @ExcelProperty(value = "运费模板")
    private String freightTemplateName;

    @ExcelProperty(value = "商城价")
    private BigDecimal minSalePrice;

    @ExcelProperty(value = "来源")
    private String sourceDesc;

    @ExcelProperty(value = "总库存")
    private Long totalStock;

    @ExcelProperty(value = "是否开启规格")
    private String hasSku;

    @ExcelProperty(value = "SKU_ID")
    private String skuId;

    @ExcelProperty(value = "规格ID")
    private Long skuAutoId;

    @ExcelProperty(value = "规格信息")
    private String specName;

    @ExcelProperty(value = "价格")
    private BigDecimal salePrice;

    @ExcelProperty(value = "库存")
    private Long stock;

    @ExcelProperty(value = "规格货号")
    private String skuCode;

    @ExcelProperty(value = "是否开启阶梯价")
    private String whetherOpenLadder;

    @ExcelProperty(value = "起购量1")
    private Integer minBath1;

    @ExcelProperty(value = "阶梯价1")
    private BigDecimal price1;

    @ExcelProperty(value = "起购量2")
    private Integer minBath2;

    @ExcelProperty(value = "阶梯价2")
    private BigDecimal price2;

    @ExcelProperty(value = "起购量3")
    private Integer minBath3;

    @ExcelProperty(value = "阶梯价3")
    private BigDecimal price3;

    @ExcelProperty(value = "实际销量")
    private Long saleCounts;

}
