package com.sankuai.shangou.seashop.m.core.mq.publisher;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.handler.ExportTask;
import com.sankuai.shangou.seashop.m.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 导出任务异步处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MExportTaskPublisher {

//    @MafkaProducer(namespace = "waimai", topic = MafkaConst.TOPIC_ASYNC_EXPORT_TASK)
//    private IProducerProcessor asyncExportTaskProducer;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    /**
     * 发送异步处理消息，如果发送失败，或者异步处理失败，会有定时任务检查补偿
     *
     * <AUTHOR>
     */
    public void sendAsyncExportTaskMessage(ExportTask exportTask) {
        String message = JsonUtil.toJsonString(exportTask);
        log.info("【mafka生产】【异步导出任务】发送异步导出任务消息, exportTask: {}", message);
        // 发送订单检查消息
        SendResult producerResult = null;
        try {
            producerResult = defaultRocketMq.syncSend(MafkaConst.TOPIC_ASYNC_EXPORT_TASK, message);
//            producerResult = asyncExportTaskProducer.sendMessage(message);
            log.info("【mafka生产】【异步导出任务】发送异步导出任务消息结果: {}", JsonUtil.toJsonString(producerResult));
        }
        catch (Exception e) {
            log.error("【mafka生产】【异步导出任务】发送异步导出任务消息异常", e);
            throw new SystemException();
        }
        if (!SendStatus.SEND_OK.equals(producerResult.getSendStatus())) {
            throw new BusinessException("发送异步导出任务消息失败");
        }
    }

}
