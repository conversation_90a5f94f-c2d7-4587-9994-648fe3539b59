package com.sankuai.shangou.seashop.mall.core.thrift.impl.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.order.*;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiQueryWxStatusResp;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.OrderFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mallApi/apiOrder")
@Slf4j
public class MallApiOrderCmdController {

    @Resource
    private OrderCmdFeign orderCmdFeign;

    @Resource
    private OrderFeign orderFeign;


    @NeedLogin
    @PostMapping(value = "/cancelPay", consumes = "application/json")
    public ResultDto<BaseResp> cancelPay(@RequestBody ApiCancelPayReq cancelPayReq) {
        log.info("【订单】取消支付, 请求参数={}", JsonUtil.toJsonString(cancelPayReq));
        return ThriftResponseHelper.responseInvoke("cancelPay", cancelPayReq, func -> {
            cancelPayReq.checkParameter();
            CancelPayReq req = JsonUtil.copy(cancelPayReq, CancelPayReq.class);
            req.setUserId(TracerUtil.getMemberDto().getId());
            return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.cancelPay(req));
        });
    }

    @NeedLogin
    @PostMapping(value = "/cancelOrder", consumes = "application/json")
    public ResultDto<BaseResp> cancelOrder(@RequestBody ApiCancelOrderReq cancelOrderReq) {
        log.info("【订单】取消订单, 请求参数={}", JsonUtil.toJsonString(cancelOrderReq));
        return ThriftResponseHelper.responseInvoke("cancelOrder", cancelOrderReq,
            func -> {
                CancelOrderReq req = JsonUtil.copy(cancelOrderReq, CancelOrderReq.class);
                LoginMemberDto loginDto = TracerUtil.getMemberDto();;
                req.setUserId(loginDto.getId());
                req.setUserName(loginDto.getName());
                return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.cancelOrder(req));
            });
    }

    @NeedLogin
    @PostMapping(value = "/reBuy", consumes = "application/json")
    public ResultDto<BaseResp> reBuy(@RequestBody ApiReBuyReq reBuyReq) {
        log.info("【订单】订单重新加购, 请求参数={}", JsonUtil.toJsonString(reBuyReq));
        return ThriftResponseHelper.responseInvoke("reBuy", reBuyReq,
            func -> {
                ReBuyReq req = JsonUtil.copy(reBuyReq, ReBuyReq.class);
                req.setUserId(TracerUtil.getMemberDto().getId());
                return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.reBuy(req));
            });
    }

    @NeedLogin
    @PostMapping(value = "/delayReceive", consumes = "application/json")
    public ResultDto<BaseResp> delayReceive(@RequestBody ApiDelayReceiveReq delayReceiveReq) throws TException {
        log.info("【订单】延迟收货, 请求参数={}", delayReceiveReq);
        return ThriftResponseHelper.responseInvoke("delayReceive", delayReceiveReq,
            func -> {
                DelayReceiveReq req = JsonUtil.copy(delayReceiveReq, DelayReceiveReq.class);

                UserDto userDto = new UserDto();
                LoginMemberDto member = TracerUtil.getMemberDto();;
                userDto.setUserId(member.getId());
                userDto.setUserName(member.getName());
                userDto.setUserPhone(member.getUserPhone());
                req.setUser(userDto);
                return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.delayReceive(req));
            });
    }

    @NeedLogin
    @PostMapping(value = "/confirmReceive", consumes = "application/json")
    public ResultDto<BaseResp> confirmReceive(@RequestBody ApiConfirmReceiveReq confirmReceiveReq) throws TException {
        log.info("【订单】确认收货, 请求参数={}", confirmReceiveReq);
        return ThriftResponseHelper.responseInvoke("confirmReceive", confirmReceiveReq,
            func -> {
                ConfirmReceiveReq req = JsonUtil.copy(confirmReceiveReq, ConfirmReceiveReq.class);

                UserDto userDto = new UserDto();
                LoginMemberDto member = TracerUtil.getMemberDto();;
                userDto.setUserId(member.getId());
                userDto.setUserName(member.getName());
                userDto.setUserPhone(member.getUserPhone());
                req.setUser(userDto);
                return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.confirmReceive(req));
            });
    }

    @NeedLogin
    @GetMapping(value = "/queryWxStatus", consumes = "application/json")
    public ResultDto<ApiQueryWxStatusResp> queryWxStatus(@RequestParam String thirdTransactionNo) throws TException {
        log.info("【微信发货】查询状态, 请求参数={}", thirdTransactionNo);
        return ThriftResponseHelper.responseInvoke("queryWxStatus", thirdTransactionNo,
                func -> {
                    Boolean status = ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.queryWxStatus(thirdTransactionNo));
                    ApiQueryWxStatusResp resp = new ApiQueryWxStatusResp();
                    resp.setStatus(status);
                    return resp;
                });
    }

    @NeedLogin
    @PostMapping(value = "/confirmReceiptWx", consumes = "application/json")
    public ResultDto<BaseResp> confirmReceiptWx(@RequestBody @Valid ConfirmReceiptWxReq req) throws TException {
        log.info("【微信发货】确认收货, 请求参数={}", req);
        return ThriftResponseHelper.responseInvoke("confirmReceive", req,
                func -> {
                    return ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.confirmReceiptWx(req));
                });
    }
}
