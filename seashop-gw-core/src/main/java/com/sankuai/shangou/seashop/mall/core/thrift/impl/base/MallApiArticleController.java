package com.sankuai.shangou.seashop.mall.core.thrift.impl.base;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.ApiBaseArticleQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.enums.CommonConstant;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiArticleCategoryListRes;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiBaseArticleCategoryRes;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiBaseArticleRes;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiSystemArticleCategoryRes;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ArticleQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ArticleCategoryListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseArticleRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.SystemArticleCategoryRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mallApi/apiArticle")
public class MallApiArticleController {

    @Resource
    private ArticleQueryFeign articleQueryFeign;

    @PostMapping(value = "/queryWithPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBaseArticleRes>> queryWithPage(@RequestBody ApiBaseArticleQueryReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("queryWithPage", query, req -> {
            req.setIsRelease(true);
            BasePageResp<BaseArticleRes> resp = ThriftResponseHelper.executeThriftCall(() ->
                    articleQueryFeign.queryWithPage(JsonUtil.copy(req, BaseArticleQueryReq.class)));
            return PageResultHelper.transfer(resp, ApiBaseArticleRes.class);
        });
    }

    @PostMapping(value = "/getArticleById", consumes = "application/json")
    public ResultDto<ApiBaseArticleRes> getArticleById(@RequestBody BaseReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("getArticleById", query, req -> {

            BaseArticleRes resp = ThriftResponseHelper.executeThriftCall(() -> articleQueryFeign.getArticleById(req));
            return JsonUtil.copy(resp, ApiBaseArticleRes.class);
        });
    }

    @GetMapping(value = "/queryAllCategory")
    public ResultDto<ApiArticleCategoryListRes> queryAllCategory() throws TException {
        int i = 0;
        return ThriftResponseHelper.responseInvoke("queryAllCategory", i, req -> {
            ArticleCategoryListRes resp = ThriftResponseHelper.executeThriftCall(() -> articleQueryFeign.queryAllCategory());
            ApiArticleCategoryListRes result = JsonUtil.copy(resp, ApiArticleCategoryListRes.class);

            if (result.getCategorys().stream().filter(t -> t.getId().equals(CommonConstant.SELLER_ARTICLE_CID)).findFirst().isPresent()) {
                ApiBaseArticleCategoryRes seller = result.getCategorys().stream().filter(t -> t.getId().equals(4L)).findFirst().get();
                result.getCategorys().remove(seller);
            }
            return result;
        });
    }

    @GetMapping(value = "/getHelpCategory")
    public ResultDto<List<ApiSystemArticleCategoryRes>> getHelpCategory() throws TException {
        return ThriftResponseHelper.responseInvoke("queryAllCategory", null, req -> {
            BaseReq query = new BaseReq();
            query.setId(CommonConstant.FOOTER_ARCTIC_CID);
            List<SystemArticleCategoryRes> rpcResult = ThriftResponseHelper.executeThriftCall(() -> articleQueryFeign.getArticleCategorysByParentId(query));
            List<ApiSystemArticleCategoryRes> resp =  JsonUtil.copyList(rpcResult, ApiSystemArticleCategoryRes.class);
            return resp;
        });
    }
}
