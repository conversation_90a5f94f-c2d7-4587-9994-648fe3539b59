package com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementOrderResp;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.eo.PendSettleListEo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;

import cn.hutool.core.collection.CollUtil;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
public class PendSettleListWrapper extends PageExportWrapper<PendSettleListEo, ApiPendingSettlementOrderQryReq> {

    private final SellerFinanceRemoteService sellerFinanceRemoteService;

    public PendSettleListWrapper(SellerFinanceRemoteService sellerFinanceRemoteService) {
        this.sellerFinanceRemoteService = sellerFinanceRemoteService;
    }

    @Override
    public List<PendSettleListEo> getPageList(ApiPendingSettlementOrderQryReq param) {
        BasePageResp<PendingSettlementOrderResp> pendingSettlementList = sellerFinanceRemoteService.pendingSettlementOrderPageList(JsonUtil.copy(param, PendingSettlementOrderQryReq.class));
        if (null == pendingSettlementList || CollUtil.isEmpty(pendingSettlementList.getData())) {
            return null;
        }
        List<PendSettleListEo> pendSettleListEos = CollUtil.newArrayList();
        pendingSettlementList.getData().forEach(p -> {
            PendSettleListEo pendSettleListEo = JsonUtil.copy(p, PendSettleListEo.class);
            pendSettleListEo.setOrderStatusStr(OrderStatusEnum.getDesc(p.getOrderStatus()));
            pendSettleListEos.add(pendSettleListEo);
        });
        return pendSettleListEos;
    }
}
