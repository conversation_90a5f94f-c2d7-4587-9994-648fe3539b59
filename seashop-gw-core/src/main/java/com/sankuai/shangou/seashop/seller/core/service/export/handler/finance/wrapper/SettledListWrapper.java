package com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;
import com.sankuai.shangou.seashop.seller.common.remote.SellerFinanceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.export.handler.finance.eo.SettledListEo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiSettledQryReq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;

/**
 * @author: lhx
 * @date: 2024/2/20/020
 * @description:
 */
public class SettledListWrapper extends PageExportWrapper<SettledListEo, ApiSettledQryReq> {

    private final SellerFinanceRemoteService sellerFinanceRemoteService;

    public SettledListWrapper(SellerFinanceRemoteService sellerFinanceRemoteService) {
        this.sellerFinanceRemoteService = sellerFinanceRemoteService;
    }

    @Override
    public List<SettledListEo> getPageList(ApiSettledQryReq param) {
        BasePageResp<SettledResp> settledPage = sellerFinanceRemoteService.settledPageList(JsonUtil.copy(param, SettledQryReq.class));
        if (null == settledPage || CollUtil.isEmpty(settledPage.getData())) {
            return null;
        }
        List<SettledListEo> settledListEos = CollUtil.newArrayList();
        settledPage.getData().forEach(s -> {
            SettledListEo settledListEo = JsonUtil.copy(s, SettledListEo.class);
            settledListEo.setCycleTime(DateUtil.formatDateTime(settledListEo.getStartCycleTime()) + "-" + DateUtil.formatDateTime(settledListEo.getEndCycleTime()));
            settledListEos.add(settledListEo);
        });
        return settledListEos;
    }
}
