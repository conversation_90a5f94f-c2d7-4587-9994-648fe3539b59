package com.sankuai.shangou.seashop.m.core.service.user.shop.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.core.util.StorageAssist;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiBusinessCategoryService;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiCmdImportCategoryReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.request.ApiQueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.m.thrift.user.shop.response.ApiBusinessCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdImportCategoryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class MApiBusinessCategoryServiceImpl implements MApiBusinessCategoryService {

    @Resource
    private BusinessCategoryQueryFeign businessCategoryQueryFeign;
    @Resource
    private BusinessCategoryCmdFeign businessCategoryCmdFeign;
    @Resource
    private StorageAssist storageAssist;


    @Override
    public BasePageResp<ApiBusinessCategoryResp> queryPage(ApiQueryBusinessCategoryPageReq queryBusinessCategoryPageReq) {
        QueryBusinessCategoryPageReq queryBusinessCategoryPageReq1 = JsonUtil.copy(queryBusinessCategoryPageReq, QueryBusinessCategoryPageReq.class);
        BasePageResp<BusinessCategoryResp> businessCategoryPageResp = ThriftResponseHelper.executeThriftCall(() ->
                businessCategoryQueryFeign.queryPage(queryBusinessCategoryPageReq1));
        return PageResultHelper.transfer(businessCategoryPageResp, ApiBusinessCategoryResp.class);
    }

    @Override
    public BaseResp updateBusinessCategory(ApiCmdBusinessCategoryReqList reqList) {
        CmdBusinessCategoryReqList cmdBusinessCategoryReqList = JsonUtil.copy(reqList, CmdBusinessCategoryReqList.class);
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryCmdFeign.updateBusinessCategory(cmdBusinessCategoryReqList));
    }

    @Override
    public BaseResp deleteBusinessCategory(BaseIdReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> businessCategoryCmdFeign.deleteBusinessCategory(req));
    }

    @Override
    public BaseImportResp importBusinessCategory(ApiCmdImportCategoryReq apiReq) {
        CmdImportCategoryReq categoryReq = JsonUtil.copy(apiReq, CmdImportCategoryReq.class);
        BaseImportResp resp = ThriftResponseHelper.executeThriftCall(() -> businessCategoryCmdFeign.importBusinessCategory(categoryReq));
        resp.setFilePath(storageAssist.formatUrl(resp.getFilePath()));
        return resp;
    }
}
