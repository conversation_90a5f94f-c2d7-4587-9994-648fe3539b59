package com.sankuai.shangou.seashop.seller.core.thrift.impl.order;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentImageResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.order.ApiQueryProductCommentReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.order.ApiProductCommentResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/21 19:41
 */
@RestController
@RequestMapping("/sellerApi/apiProductComment")
@Slf4j
public class SellerApiProductCommentQueryController {

    @Resource
    private ProductCommentQueryFeign productCommentQueryFeign;

    @PostMapping(value = "/queryProductCommentForSeller", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiProductCommentResp>> queryProductCommentForSeller(@RequestBody ApiQueryProductCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductCommentForSeller", request, req -> {

            QueryProductCommentReq remoteReq = JsonUtil.copy(req, QueryProductCommentReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<ProductCommentResp> resp = ThriftResponseHelper.executeThriftCall(() ->
                    productCommentQueryFeign.queryProductCommentForSeller(remoteReq));
            return PageResultHelper.transfer(resp, ApiProductCommentResp.class, (source, target) -> {
                target.setCommentImageList(getCommentImageList(source.getFirstCommentImageList()));
                target.setAppendImageList(getCommentImageList(source.getAppendCommentImageList()));
            });
        });
    }

    /**
     * 提取评论图片
     *
     * @param imageList 评论图片列表
     * @return 评论图片列表
     */
    private List<String> getCommentImageList(List<ProductCommentImageResp> imageList) {
        if (CollectionUtils.isEmpty(imageList)) {
            return Collections.EMPTY_LIST;
        }
        return imageList.stream().map(ProductCommentImageResp::getCommentImage).collect(Collectors.toList());
    }


}
