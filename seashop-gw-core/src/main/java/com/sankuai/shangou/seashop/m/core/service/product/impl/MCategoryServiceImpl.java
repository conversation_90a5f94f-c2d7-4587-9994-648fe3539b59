package com.sankuai.shangou.seashop.m.core.service.product.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.product.MCategoryService;
import com.sankuai.shangou.seashop.m.thrift.core.request.product.ApiQueryCategoryReq;

/**
 * <AUTHOR>
 * @date 2024/04/29 11:39
 */
@Service
public class MCategoryServiceImpl implements MCategoryService {

    @Resource
    private MExportTaskBiz exportTaskBiz;

    @Override
    public void exportCategory(ApiQueryCategoryReq request) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.CATEGORY_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getManagerDto().getId());
        createExportTaskBo.setOperatorName(TracerUtil.getManagerDto().getName());
        exportTaskBiz.create(createExportTaskBo);
    }
}
