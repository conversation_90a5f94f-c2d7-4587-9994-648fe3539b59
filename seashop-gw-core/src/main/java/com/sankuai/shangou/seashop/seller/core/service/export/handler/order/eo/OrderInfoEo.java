package com.sankuai.shangou.seashop.seller.core.service.export.handler.order.eo;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.export.anno.RowKey;
import com.sankuai.shangou.seashop.base.export.anno.ShouldMerge;

import lombok.Getter;
import lombok.Setter;

/**
 * 订单导出对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderInfoEo {

    @ExcelProperty("订单编号")
    @RowKey
    @ShouldMerge
    private String orderId;
    @ExcelProperty("订单来源")
    @ShouldMerge
    private String platformDesc;
    @ExcelProperty("店铺")
    @ShouldMerge
    private String shopName;
    @ExcelProperty("买家")
    @ShouldMerge
    private String userName;
    @ExcelProperty("下单时间")
    @ShouldMerge
    private String orderDateStr;
    @ExcelProperty("付款时间")
    @ShouldMerge
    private String payDateStr;
    @ExcelProperty("完成时间")
    @ShouldMerge
    private String finishDateStr;
    @ExcelProperty("支付方式")
    @ShouldMerge
    private String payMethodDesc;
    @ExcelProperty("交易单号")
    @ShouldMerge
    private String gatewayOrderId;
    @ExcelProperty("商品总额")
    @ShouldMerge
    private BigDecimal productTotalAmount;
    @ExcelProperty("运费")
    @ShouldMerge
    private String freight;
    @ExcelProperty("税金")
    @ShouldMerge
    private BigDecimal tax;
    @ExcelProperty("优惠券抵扣")
    @ShouldMerge
    private BigDecimal couponAmount;
    @ExcelProperty("满额减")
    @ShouldMerge
    private BigDecimal moneyOffAmount;
    @ExcelProperty("门店改价")
    @ShouldMerge
    private BigDecimal updateAmount;
    @ExcelProperty("订单实付总额")
    @ShouldMerge
    private BigDecimal totalAmount;
    @ExcelProperty("平台佣金")
    @ShouldMerge
    private BigDecimal commissionTotalAmount;
    @ExcelProperty("订单状态")
    @ShouldMerge
    private String orderStatusDesc;
    @ExcelProperty("买家留言")
    @ShouldMerge
    private String userRemark;
    @ExcelProperty("收货人")
    @ShouldMerge
    private String shipTo;
    @ExcelProperty("手机号码")
    @ShouldMerge
    private String cellPhone;
    @ExcelProperty("收货地址")
    @ShouldMerge
    private String address;
    @ExcelProperty("商品ID")
    private Long productId;
    @ExcelProperty("SkuID")
    private String skuId;
    @ExcelProperty("规格ID")
    private Long skuAutoId;
    @ExcelProperty("一级分类")
    private String cateLevel1Name;
    @ExcelProperty("二级分类")
    private String cateLevel2Name;
    @ExcelProperty("三级分类")
    private String cateLevel3Name;
    @ExcelProperty("商品名称")
    private String productName;
    // SKU 级别的货号
    @ExcelProperty("货号")
    private String skuCode;
    @ExcelProperty("单价")
    private BigDecimal salePrice;
    @ExcelProperty("数量")
    private Long quantity;

}
