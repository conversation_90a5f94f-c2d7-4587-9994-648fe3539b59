package com.sankuai.shangou.seashop.m.core.thrift.impl.user.shop;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.core.service.user.shop.MApiShopInvoiceService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiSaveShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopInvoiceReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/mApi/apiShopInvoice")
public class MApiShopInvoiceCmdController {
    @Resource
    private MApiShopInvoiceService mApiShopInvoiceService;

    @PostMapping(value = "/saveSelfShopInvoice", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> saveShopInvoice(@RequestBody ApiSaveShopInvoiceReq saveShopInvoiceReq) {
        return ThriftResponseHelper.responseInvoke("saveShopInvoice", saveShopInvoiceReq, req -> {
            if (TracerUtil.getManagerDto() != null) {
                req.setOperationUserId(TracerUtil.getManagerDto().getId());
            }
            req.checkParameter();
            return this.mApiShopInvoiceService.saveShopInvoice(JsonUtil.copy(req, SaveShopInvoiceReq.class));
        });
    }
}
