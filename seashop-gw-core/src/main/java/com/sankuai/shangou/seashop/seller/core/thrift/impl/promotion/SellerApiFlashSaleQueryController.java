package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.VisualFlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.VisualFlashSaleResp;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerFlashSaleRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiFlashSaleQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiVisualFlashSaleQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiFlashSaleResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiFlashSaleSimpleResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiVisualFlashSaleResp;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiFlashSale")
public class SellerApiFlashSaleQueryController {

    @Resource
    private SellerFlashSaleRemoteService sellerFlashSaleRemoteService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiFlashSaleSimpleResp>> pageList(@RequestBody ApiFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            BasePageResp<FlashSaleSimpleResp> flashSalePage = sellerFlashSaleRemoteService.pageList(JsonUtil.copy(req, FlashSaleQueryReq.class));
            return PageResultHelper.transfer(flashSalePage, ApiFlashSaleSimpleResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiFlashSaleResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            FlashSaleResp flashSale = sellerFlashSaleRemoteService.getById(req);
            return JsonUtil.copy(flashSale, ApiFlashSaleResp.class);
        });
    }

    @PostMapping(value = "/componentPageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiVisualFlashSaleResp>> componentPageList(@RequestBody ApiVisualFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("componentPageList", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            BasePageResp<VisualFlashSaleResp> flashSalePage = sellerFlashSaleRemoteService.componentPageList(JsonUtil.copy(req, VisualFlashSaleQueryReq.class));
            return PageResultHelper.transfer(flashSalePage, ApiVisualFlashSaleResp.class);
        });
    }
}
