package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.account;

import com.sankuai.gw.common.thrift.requests.ApiLoginReq;
import com.sankuai.gw.common.thrift.requests.ApiRefreshTokenReq;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.config.LoginSecurityConfig;
import com.sankuai.shangou.seashop.base.security.utils.TokenUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.seller.core.user.service.SellerApiManagerService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.account.ApiCmdManagerReq;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description: 商家服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/sellerApi/apiManager")
public class SellerApiManagerCmdController {
    @Resource
    private SellerApiManagerService sellerApiManagerService;

    @PostMapping(value = "/addManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> addManager(@RequestBody ApiCmdManagerReq cmdManagerReq) {
        
        return ThriftResponseHelper.responseInvoke("addManager", cmdManagerReq, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            cmdManagerReq.setShopId(loginShopDto.getShopId());
            return sellerApiManagerService.addManager(cmdManagerReq);
        });
    }

    @PostMapping(value = "/editManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> editManager(@RequestBody ApiCmdManagerReq cmdManagerReq) {
        
        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        cmdManagerReq.setShopId(loginShopDto.getShopId());
        return ThriftResponseHelper.responseInvoke("editManager", cmdManagerReq, req -> sellerApiManagerService.editManager(cmdManagerReq));
    }

    @PostMapping(value = "/deleteManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Long> deleteManager(@RequestBody ApiCmdManagerReq cmdManagerReq) {
        
        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        cmdManagerReq.setShopId(loginShopDto.getShopId());
        return ThriftResponseHelper.responseInvoke("deleteManager", cmdManagerReq, req -> sellerApiManagerService.deleteManager(cmdManagerReq));
    }

    @PostMapping(value = "/batchDeleteManager", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Integer> batchDeleteManager(@RequestBody ApiCmdManagerReq cmdManagerReq) {
        
        LoginShopDto loginShopDto = TracerUtil.getShopDto();
        cmdManagerReq.setShopId(loginShopDto.getShopId());
        return ThriftResponseHelper.responseInvoke("batchDeleteManager", cmdManagerReq, req -> sellerApiManagerService.batchDeleteManager(cmdManagerReq));
    }

    @PostMapping(value = "/login",consumes = "application/json")
    public ResultDto<LoginResp> login(@RequestBody ApiLoginReq loginReq) {
        return ThriftResponseHelper.responseInvoke("login", loginReq, req -> {
            return sellerApiManagerService.login(loginReq);
        });
    }

    //刷新token
    @GetMapping(value = "/refreshToken", consumes = "application/json")
    @NeedLogin
    public ResultDto<LoginResp> refreshToken() {
        return ThriftResponseHelper.responseInvoke("refreshToken", null, req -> {
            LoginMemberDto memberDto = TracerUtil.getMemberDto();
            TokenCache tokenCache = new TokenCache();
            String token = TokenUtil.getRequestToken(LoginSecurityConfig.TOKEN_NAME);
            tokenCache.setToken(token);
            tokenCache.setUserId(memberDto.getId());
            tokenCache.setUserType(RoleEnum.MEMBER.name());
            ApiRefreshTokenReq req1 = new ApiRefreshTokenReq();
            req1.setToken(tokenCache);
            req1.setRoleType(RoleEnum.SHOP.name());
            return sellerApiManagerService.refreshToken(req1);
        });
    }

    //退出登录
    @GetMapping(value = "/logout",consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<Boolean> logout() {
        return ThriftResponseHelper.responseInvoke("logout", null, req -> {
            return sellerApiManagerService.logout();
        });
    }
}
