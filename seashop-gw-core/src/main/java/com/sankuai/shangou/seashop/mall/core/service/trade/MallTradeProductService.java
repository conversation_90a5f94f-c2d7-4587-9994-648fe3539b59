package com.sankuai.shangou.seashop.mall.core.service.trade;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.MemberUserInfo;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.trade.ApiQueryProductDetailReq;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.ProductBaseInfoResp;

/**
 * <AUTHOR>
 * @date 2024/05/17 14:51
 */
public interface MallTradeProductService {

    /**
     * 查询商品基本信息
     *
     * @param req      入参
     * @param userInfo 用户信息
     * @return 商品基本信息
     */
    ProductBaseInfoResp queryProductBaseInfo(ApiQueryProductDetailReq req, LoginMemberDto userInfo);
}
