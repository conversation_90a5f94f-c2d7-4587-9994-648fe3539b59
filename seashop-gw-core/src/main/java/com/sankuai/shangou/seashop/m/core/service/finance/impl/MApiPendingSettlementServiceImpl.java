package com.sankuai.shangou.seashop.m.core.service.finance.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.common.remote.MFinanceRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiPendingSettlementService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiOrderIdQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiPendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiPendingSettlementQryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPendingSettlementOrderResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPendingSettlementResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiPlatCommissionResp;
import com.sankuai.shangou.seashop.order.thrift.finance.PendingSettlementQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementOrderResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PlatCommissionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/7/007
 * @description:
 */
@Service
@Slf4j
public class MApiPendingSettlementServiceImpl implements MApiPendingSettlementService {

    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private MFinanceRemoteService mFinanceRemoteService;
    @Resource
    private MExportTaskBiz exportTaskBiz;
    @Resource
    private PendingSettlementQueryFeign pendingSettlementQueryFeign;

    @Override
    public BasePageResp<ApiPendingSettlementResp> getPendingSettlementList(ApiPendingSettlementQryReq request) {

        String shopName = request.getShopName();
        Long shopId = request.getShopId();
        List<Long> shopIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(shopName)) {
            // 先模糊查询店铺
            ShopSimpleListResp shopSimpleListResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(
                ShopSimpleQueryReq.builder()
                    .shopName(shopName).build()
            ));
            // 模糊查询没查到数据，直接返回空
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request);
            }
            shopIdList = shopSimpleListResp.getList().stream().map(shopSimpleResp -> shopSimpleResp.getId()).collect(Collectors.toList());
            if (null != shopId) {
                // 如果同时还传了id进来，那么需要判断id是否在模糊查询的结果中
                if (!shopIdList.contains(shopId)) {
                    return PageResultHelper.defaultEmpty(request);
                }
                else {
                    shopIdList.clear();
                    shopIdList.add(shopId);
                }
            }
        }
        else {
            if (null != shopId) {
                shopIdList.add(shopId);
            }
        }
        PendingSettlementQryReq req = JsonUtil.copy(request, PendingSettlementQryReq.class);
        req.setShopIdList(shopIdList);
        BasePageResp<PendingSettlementResp> pendingSettlementPage = ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.getPendingSettlementList(req));
        return PageResultHelper.transfer(pendingSettlementPage, ApiPendingSettlementResp.class);
    }

    @Override
    public ApiPlatCommissionResp getPlatCommission() {
        PlatCommissionResp platCommission = ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.getPlatCommission());
        return JsonUtil.copy(platCommission, ApiPlatCommissionResp.class);
    }

    @Override
    public BasePageResp<ApiPendingSettlementOrderResp> pageList(ApiPendingSettlementOrderQryReq request) {
        BasePageResp<PendingSettlementOrderResp> pendingSettlementOrderPage = ThriftResponseHelper.executeThriftCall(() ->
                pendingSettlementQueryFeign.pageList(JsonUtil.copy(request, PendingSettlementOrderQryReq.class)));
        return PageResultHelper.transfer(pendingSettlementOrderPage, ApiPendingSettlementOrderResp.class);
    }

    @Override
    public ApiPendingSettlementOrderResp getDetailByOrderId(ApiOrderIdQryReq request) {
        OrderIdQryReq req = new OrderIdQryReq();
        req.setOrderId(request.getOrderId());
        PendingSettlementOrderResp pendingSettlementOrderResp =  ThriftResponseHelper.executeThriftCall(() ->
                pendingSettlementQueryFeign.getDetailByOrderId(req));
        return JsonUtil.copy(pendingSettlementOrderResp, ApiPendingSettlementOrderResp.class);
    }

    @Override
    public void exportPendSettleListTotal(ApiPendingSettlementQryReq request) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.PEND_SETTLE_TOTAL_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getManagerDto().getId());
        createExportTaskBo.setOperatorName(TracerUtil.getManagerDto().getName());
//        createExportTaskBo.setOperatorId(1L);
//        createExportTaskBo.setOperatorName("admin");
        exportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public void exportPendSettleList(ApiPendingSettlementOrderQryReq request) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.PEND_SETTLE_LIST);
        createExportTaskBo.setExecuteParam(request);
        createExportTaskBo.setOperatorId(TracerUtil.getManagerDto().getId());
        createExportTaskBo.setOperatorName(TracerUtil.getManagerDto().getName());
        exportTaskBiz.create(createExportTaskBo);
    }
}
