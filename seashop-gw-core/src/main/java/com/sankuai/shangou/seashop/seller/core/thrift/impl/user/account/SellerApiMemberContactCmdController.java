package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.account;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerMemberRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdBindContactReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdCheckCodeReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiCmdSendCodeReq;

/**
 * @description: 标签服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/sellerApi/apiMemberContact")
public class SellerApiMemberContactCmdController {
    @Resource
    private SellerMemberRemoteService apiSellerMemberRemoteService;

    @PostMapping(value = "/sendCode", consumes = "application/json")
    public ResultDto<BaseResp> sendCode(@RequestBody ApiCmdSendCodeReq apiSendCodeCmdReq) {
        return ThriftResponseHelper.responseInvoke("sendCode", apiSendCodeCmdReq, req -> apiSellerMemberRemoteService.sendCode(apiSendCodeCmdReq));
    }

    @PostMapping(value = "/checkCode", consumes = "application/json")
    public ResultDto<BaseResp> checkCode(@RequestBody ApiCmdCheckCodeReq apiCheckCodeCmdReq) {
        return ThriftResponseHelper.responseInvoke("checkCode", apiCheckCodeCmdReq, req -> apiSellerMemberRemoteService.checkCode(apiCheckCodeCmdReq));
    }

    @PostMapping(value = "/bindContact", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> bindContact(@RequestBody ApiCmdBindContactReq apiCheckCodeCmdReq) {
        
        apiCheckCodeCmdReq.setId(TracerUtil.getShopDto().getUserId());
        return ThriftResponseHelper.responseInvoke("bindContact", apiCheckCodeCmdReq, req -> apiSellerMemberRemoteService.bindContact(apiCheckCodeCmdReq));
    }

}
