//package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.listener;
//
//import com.alibaba.excel.context.AnalysisContext;
//import com.alibaba.excel.context.WriteContext;
//import com.alibaba.excel.event.AnalysisEventListener;
//import com.alibaba.excel.metadata.Head;
//import com.alibaba.excel.metadata.data.CellData;
//import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
//import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
//import com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo.ExclusivePriceEo;
//import com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo.ExclusivePriceProductEo;
//import org.apache.poi.ss.util.CellRangeAddress;
//
//import java.util.List;
//
///**
// * @author: lhx
// * @date: 2023/12/26/026
// * @description:
// */
//public class ExclusivePriceDataListener extends AnalysisEventListener<ExclusivePriceEo> {
//
//    private WriteContext writeContext;
//
//    public ExclusivePriceDataListener(WriteContext writeContext) {
//        this.writeContext = writeContext;
//    }
//
//    @Override
//    public void invoke(ExclusivePriceEo data, AnalysisContext context) {
//        WriteSheetHolder writeSheetHolder = writeContext.writeSheetHolder();
//        WriteTableHolder writeTableHolder = writeSheetHolder.(writeSheetHolder.tab().size() - 1);
//
//        // 写入ExclusivePriceEo的数据
//        for (Head head : writeTableHolder.getHead()) {
//            if ("主键ID".equals(head.())) {
//                writeTableHolder.addCellData(new CellData<>(data.getId()));
//            } else if ("活动名称".equals(head.getValue())) {
//                writeTableHolder.addCellData(new CellData<>(data.getName()));
//            } else if ("店铺ID".equals(head.getValue())) {
//                writeTableHolder.addCellData(new CellData<>(data.getShopId()));
//            } else if ("店铺名称".equals(head.getValue())) {
//                writeTableHolder.addCellData(new CellData<>(data.getShopName()));
//            } else if ("开始时间".equals(head.getValue())) {
//                writeTableHolder.addCellData(new CellData<>(data.getStartTime()));
//            } else if ("结束时间".equals(head.getValue())) {
//                writeTableHolder.addCellData(new CellData<>(data.getEndTime()));
//            }
//        }
//
//        // 写入ExclusivePriceProductEo的数据
//        List<ExclusivePriceProductEo> productList = data.getProductList();
//        if (productList != null && !productList.isEmpty()) {
//            for (ExclusivePriceProductEo product : productList) {
//                writeSheetHolder.getSheet().addMergedRegion(new CellRangeAddress(
//                        writeTableHolder.getCurrentRowIndex(), // first row (0-based)
//                        writeTableHolder.getCurrentRowIndex(), // last row (0-based)
//                        writeTableHolder.getHeadLastCellNum(), // first column (0-based)
//                        writeTableHolder.getHeadLastCellNum() + 1 // last column (0-based)
//                ));
//
//                // 写入ExclusivePriceProductEo的数据
//                for (Head head : writeTableHolder.getHead()) {
//                    if ("商品ID".equals(head.getValue())) {
//                        writeTableHolder.addCellData(new CellData<>(product.getProductId()));
//                    } else if ("商品名称".equals(head.getValue())) {
//                        writeTableHolder.addCellData(new CellData<>(product.getProductName()));
//                    } else if ("sku自增id".equals(head.getValue())) {
//                        writeTableHolder.addCellData(new CellData<>(product.getSkuAutoId()));
//                    } else if ("sku名称".equals(head.getValue())) {
//                        writeTableHolder.addCellData(new CellData<>(product.getSkuName()));
//                    } else if ("商城价格".equals(head.getValue())) {
//                        writeTableHolder.addCellData(new CellData<>(product.getMallPrice()));
//                    } else if ("会员id".equals(head.getValue())) {
//                        writeTableHolder.addCellData(new CellData<>(product.getMemberId()));
//                    } else if ("会员名称".equals(head.getValue())) {
//                        writeTableHolder.addCellData(new CellData<>(product.getUserName()));
//                    } else if ("专享价格".equals(head.getValue())) {
//                        writeTableHolder.addCellData(new CellData<>(product.getPrice()));
//                    }
//                }
//
//                writeTableHolder.nextRow();
//            }
//        }
//
//        writeTableHolder.nextRow();
//    }
//
//    @Override
//    public void doAfterAllAnalysed(AnalysisContext context) {
//        // 数据写入完成后的一些操作，如关闭流等
//    }
//}




