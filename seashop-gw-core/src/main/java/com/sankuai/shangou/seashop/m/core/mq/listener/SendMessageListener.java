package com.sankuai.shangou.seashop.m.core.mq.listener;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.m.common.remote.user.MApiMemberRemoteService;
import com.sankuai.shangou.seashop.m.core.mq.dto.SendMessageBody;
import com.sankuai.shangou.seashop.m.core.mq.dto.SendType;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ApiCmdSendAllCoupon;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(
    topic = MafkaConst.TOPIC_ASYNC_SEND_SMS  + "_${spring.profiles.active}",
    consumerGroup = MafkaConst.GROUP_ASYNC_SEND_SMS  + "_${spring.profiles.active}",
    selectorExpression = "*")
public class SendMessageListener implements RocketMQListener<MessageExt> {

    @Resource
    private MApiMemberRemoteService MApiMemberRemoteService;


    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【异步发送消息任务】接收的消息内容为: {}", body);
        try {
            SendMessageBody task = JsonUtil.parseObject(body, SendMessageBody.class);
            SendType sendType = SendType.getByCode(task.getSendType());
            switch (sendType) {
                case MEMBER:
                    // 会员导出消息
                    ApiCmdSendAllCoupon cmdSendAllCoupon = JsonUtil.parseObject(task.getMessage(), ApiCmdSendAllCoupon.class);
                    MApiMemberRemoteService.sendAllCoupon(cmdSendAllCoupon);
                    break;
                default:
                    log.error("【mafka消费】【异步发送消息任务】未知的发送类型: {}", task.getSendType());
            }
        }
        catch (Exception e) {
            log.error("【mafka消费】【异步发送消息任务】执行异常", e);
            throw new RuntimeException(e);
        }
    }
}
