package com.sankuai.shangou.seashop.mall.core.thrift.impl.system.setting;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.enums.CategoryTypeEnum;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.*;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ArticleQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.SystemStyleEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.*;
import com.sankuai.shangou.seashop.mall.core.thrift.impl.system.convert.MallApiSiteSettingConverter;
import com.sankuai.shangou.seashop.seller.thrift.core.response.system.ApiBaseShopSitSettingRes;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/mallApi/apiSiteSetting")
public class MallApiSiteSettingController {


    @Resource
    private SiteSettingQueryFeign siteSettingQueryFeign;

    @Resource
    private MallApiSiteSettingConverter apiSiteSettingConverter;
    @Resource
    private ArticleQueryFeign articleQueryFeign;


    @GetMapping(value = "/getSetting")
    public ResultDto<ApiBaseSitSettingRes> getSetting() throws TException {
        Long index = 0L;
        return ThriftResponseHelper.responseInvoke("getSetting", index, req -> {
            BaseSitSettingRes settingRes = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getSetting());
            return JsonUtil.copy(settingRes, ApiBaseSitSettingRes.class);
        });
    }

    @GetMapping(value = "/getSettled")
    public ResultDto<ApiBaseSettledRes> getSettled() throws TException {
        long index = 0L;
        return ThriftResponseHelper.responseInvoke("getSettled", index, req -> {
            BaseSettledRes result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getSettled());
            return JsonUtil.copy(result, ApiBaseSettledRes.class);
        });
    }

    @GetMapping(value = "/getAgreement")
    public ResultDto<ApiBaseAgreementRes> getAgreement(@RequestParam int agreementType) throws TException {
        return ThriftResponseHelper.responseInvoke("getAgreement", agreementType, req -> {
            BaseAgreementRes result = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getAgreement(req));
            return JsonUtil.copy(result, ApiBaseAgreementRes.class);
        });
    }


    @PostMapping(value = "/systemNoticeWithPage", consumes = "application/json")
    public ResultDto<BasePageResp<ApiBaseArticleRes>> systemNoticeWithPage(@RequestBody BasePageReq query) throws TException {
        return ThriftResponseHelper.responseInvoke("systemNoticeWithPage", query, req -> {
            BaseReq categoryQuery = new BaseReq();
            categoryQuery.setId((long) CategoryTypeEnum.InfoCenter.getCode());
            List<BaseArticleCategoryRes> categoryRes = ThriftResponseHelper.executeThriftCall(() ->
                    articleQueryFeign.getBaseArticleCategoryByParentId(categoryQuery));

            if (categoryRes == null) {
                return null;
            }

            List<Long> ids = categoryRes.stream().map(t -> t.getId()).collect(Collectors.toList());
            ids.add(categoryQuery.getId());
            BaseArticleQueryReq request = new BaseArticleQueryReq();
            request.setPageNo(req.getPageNo());
            request.setCategoryIds(ids);
            request.setIsRelease(true);
            request.setPageSize(req.getPageSize());
            req.setSortList(req.getSortList());

            BasePageResp<BaseArticleRes> result = ThriftResponseHelper.executeThriftCall(() ->
                    articleQueryFeign.queryWithPage(request));
            return PageResultHelper.transfer(result, ApiBaseArticleRes.class);

        });
    }

    @GetMapping(value = "/getProductSettings")
    public ResultDto<ApiProductSettingResp> getProductSettings() throws TException {
        return ThriftResponseHelper.responseInvoke("getProductSettings", null, req -> {

            ProductSettingResp productSettingResp = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getProductSettings());
            return JsonUtil.copy(productSettingResp, ApiProductSettingResp.class);
        });
    }

    @GetMapping(value = "/getShopSettings")
    public ResultDto<ApiBaseShopSitSettingRes> getShopSettings() {
        return ThriftResponseHelper.responseInvoke("getShopSettings", null, req -> {
            BaseShopSitSettingRes shopSitSettingRes = ThriftResponseHelper.executeThriftCall(() ->
                    siteSettingQueryFeign.getShopSettings());
            return apiSiteSettingConverter.convertRes(shopSitSettingRes);
        });
    }


    @GetMapping(value = "/getShopTheme")
    public ResultDto<String> getShopTheme() {
        return ThriftResponseHelper.responseInvoke("getShopTheme", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.SHOPTHEME);
            });
        });
    }

    @GetMapping(value = "/getOfficeMark")
    public ResultDto<String> getOfficeMark() {
        return ThriftResponseHelper.responseInvoke("getOfficeMark", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryOfficeMark(SystemStyleEnum.OFFICE_MARK);
            });
        });
    }

    @GetMapping(value = "/getProductCatetory")
    public ResultDto<String> getProductCatetory() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.PRODUCTCATETORY);
            });
        });
    }

    @GetMapping(value = "/getUserCenter")
    public ResultDto<String> getUserCenter() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.USERCENTER);
            });
        });
    }

    @GetMapping(value = "/getPCShopTheme")
    public ResultDto<String> getPCShopTheme() {
        return ThriftResponseHelper.responseInvoke("getProductCatetory", null, fun1 -> {
            return ThriftResponseHelper.executeThriftCall(() -> {
                return siteSettingQueryFeign.queryShopStyle(SystemStyleEnum.PCSHOPTHEME);
            });
        });
    }
}
