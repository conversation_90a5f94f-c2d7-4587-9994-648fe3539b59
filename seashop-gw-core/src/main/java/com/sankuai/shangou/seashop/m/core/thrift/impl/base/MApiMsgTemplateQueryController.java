package com.sankuai.shangou.seashop.m.core.thrift.impl.base;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MsgTemplateQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryMsgTemplateReq;
import com.sankuai.shangou.seashop.m.thrift.core.request.base.ApiQueryMsgTemplateReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.base.ApiQueryMsgTemplateResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:07
 */
@RestController
@RequestMapping("/mApi/apiMsgTemplate")
public class MApiMsgTemplateQueryController {

    @Resource
    private MsgTemplateQueryFeign msgTemplateQueryFeign;

    @PostMapping(value = "/queryMsgTemplate", consumes = "application/json")
    public ResultDto<ApiQueryMsgTemplateResp> queryMsgTemplate(@RequestBody ApiQueryMsgTemplateReq queryMsgTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryMsgTemplate", queryMsgTemplateReq, req -> {
            req.checkParameter();
            QueryMsgTemplateReq bean = JsonUtil.copy(req, QueryMsgTemplateReq.class);
            return JsonUtil.copy(ThriftResponseHelper.executeThriftCall(() -> msgTemplateQueryFeign.queryMsgTemplate(bean)), ApiQueryMsgTemplateResp.class);
        });
    }
}
