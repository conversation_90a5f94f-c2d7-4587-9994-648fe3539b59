package com.sankuai.shangou.seashop.seller.core.thrift.impl.user.shop;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.seller.common.remote.user.SellerOrderSettingRemoteService;
import com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop.ApiQueryOrderSettingReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.user.shop.ApiQueryOrderSettingResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryOrderSettingResp;

/**
 * @author： liweisong
 * @create： 2023/11/27 16:39
 */
@RestController
@RequestMapping("/sellerApi/apiOrderSetting")
public class SellerApiOrderSettingQueryController {

    @Resource
    private SellerOrderSettingRemoteService sellerOrderSettingRemoteService;

    @PostMapping(value = "/queryOrderSetting", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiQueryOrderSettingResp> queryOrderSetting(@RequestBody ApiQueryOrderSettingReq queryOrderSettingReq) throws TException {
        

        return ThriftResponseHelper.responseInvoke("queryOrderSetting", queryOrderSettingReq, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.checkParameter();
            QueryOrderSettingResp queryOrderSettingResp = sellerOrderSettingRemoteService.queryOrderSetting(JsonUtil.copy(req, QueryOrderSettingReq.class));
            return JsonUtil.copy(queryOrderSettingResp, ApiQueryOrderSettingResp.class);
        });
    }
}
