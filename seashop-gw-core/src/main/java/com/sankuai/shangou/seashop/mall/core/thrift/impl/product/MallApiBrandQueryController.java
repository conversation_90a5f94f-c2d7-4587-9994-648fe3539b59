package com.sankuai.shangou.seashop.mall.core.thrift.impl.product;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryRecommendBrandReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiBrandListResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.BrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryRecommendBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.BrandListResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/28 10:47
 */
@RestController
@RequestMapping("/mallApi/apiBrand")
public class MallApiBrandQueryController {

    @Resource
    private BrandQueryFeign brandQueryFeign;

    @PostMapping(value = "/queryRecommendBrand", consumes = "application/json")
    public ResultDto<ApiBrandListResp> queryRecommendBrand(@RequestBody ApiQueryRecommendBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryRecommendBrand", request, req -> {

            QueryRecommendBrandReq remoteReq = JsonUtil.copy(req, QueryRecommendBrandReq.class);
            BrandListResp resp = ThriftResponseHelper.executeThriftCall(() -> brandQueryFeign.queryRecommendBrand(remoteReq));
            return JsonUtil.copy(resp, ApiBrandListResp.class);
        });
    }
}
