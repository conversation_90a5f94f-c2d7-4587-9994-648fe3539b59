package com.sankuai.shangou.seashop.m.core.thrift.impl.finance;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.m.core.service.finance.MApiCashDepositService;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiCashDepositQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositSimpleResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@RestController
@RequestMapping("/mApi/apiCashDeposit")
public class MApiCashDepositQueryController {

    @Resource
    private MApiCashDepositService mApiCashDepositService;

    @PostMapping(value = "/queryListByParam", consumes = "application/json")
    public ResultDto<BasePageResp<ApiCashDepositSimpleResp>> queryListByParam(@RequestBody ApiCashDepositQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryListByParam", request, req ->
            mApiCashDepositService.queryListByParam(req));
    }
}
