package com.sankuai.shangou.seashop.mall.core.thrift.impl.promotion;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiCouponRecordIdReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiCouponRecordOrderQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.promotion.ApiCouponRecordQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.ApiCouponRecordListResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiCouponRecordOrderListResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.promotion.ApiCouponRecordSimpleResp;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.mall.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.mall.common.remote.user.MallShopRemoteService;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.CouponStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.UseAreaEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponRecordQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@RestController
@RequestMapping("/mallApi/apiCouponRecord")
public class MallApiCouponRecordQueryController {

    @Resource
    private MallShopRemoteService mallShopRemoteService;
    @Resource
    private CouponRecordQueryFeign couponRecordQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin
    public ResultDto<BasePageResp<ApiCouponRecordListResp>> pageList(@RequestBody ApiCouponRecordQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            CouponRecordQueryReq queryReq = JsonUtil.copy(req, CouponRecordQueryReq.class);
            LoginMemberDto member = TracerUtil.getMemberDto();;
            queryReq.setUserId(member.getId());
            if (null == queryReq.getStatus()) {
                queryReq.setStatus(CouponStatusEnum.NOT_USED.getCode());
            }

            // 如果传入了shopId，要校验店铺状态是否正常
            Long shopId = req.getShopId();
            if (null != shopId) {
                BaseIdReq baseIdReq = new BaseIdReq();
                baseIdReq.setId(shopId);
                ShopDetailResp shopDetailResp = mallShopRemoteService.queryDetail(baseIdReq);
                if (null == shopDetailResp || shopDetailResp.getShopStatus().equals(ShopEnum.AuditStatus.Freeze.getCode())) {
                    return PageResultHelper.defaultEmpty(req);
                }
            }

            // 需要默认一个排序(按价值倒序)
            // queryReq.setOrderByPriceDesc(Boolean.TRUE);
            queryReq.setAutoOrderByStatus(Boolean.TRUE);

            BasePageResp<CouponRecordSimpleResp> listRespBasePageResp = ThriftResponseHelper.executeThriftCall(() ->
                    couponRecordQueryFeign.pageList(queryReq));

            final List<ShopSimpleResp> shopSimpleList = new ArrayList<>();
            if (null != listRespBasePageResp && CollUtil.isNotEmpty(listRespBasePageResp.getData())) {
                List<Long> shopIdList = listRespBasePageResp.getData().stream().map(CouponRecordSimpleResp::getShopId).distinct().collect(Collectors.toList());
                ShopSimpleQueryReq shopSimpleQueryReq = new ShopSimpleQueryReq();
                shopSimpleQueryReq.setShopIdList(shopIdList);
                ShopSimpleListResp shopSimpleListResp = mallShopRemoteService.querySimpleList(shopSimpleQueryReq);
                if (null != shopSimpleListResp && CollUtil.isNotEmpty(shopSimpleListResp.getList())) {
                    shopSimpleList.addAll(shopSimpleListResp.getList());
                }
            }

            return PageResultHelper.transfer(listRespBasePageResp, ApiCouponRecordListResp.class, resp -> {
                Integer useArea = resp.getUseArea();
                if (UseAreaEnum.ALL.getCode().equals(useArea)) {
                    // 全场通用，remark特殊赋值
                    resp.setRemark(CommonConstant.COUPON_ALL_SHOP);
                }

                if (CollUtil.isNotEmpty(shopSimpleList)) {
                    for (ShopSimpleResp shopSimpleResp : shopSimpleList) {
                        if (shopSimpleResp.getId().equals(resp.getShopId())) {
                            resp.setShopName(shopSimpleResp.getShopName());
                            break;
                        }
                    }
                }
            });
        });
    }

    @NeedLogin
    @PostMapping(value = "/getUserRecordById", consumes = "application/json")
    public ResultDto<ApiCouponRecordSimpleResp> getUserRecordById(@RequestBody ApiCouponRecordIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getUserRecordById", request, req -> {
            CouponRecordIdReq couponRecordIdReq = JsonUtil.copy(req, CouponRecordIdReq.class);
            LoginMemberDto member = TracerUtil.getMemberDto();;
            couponRecordIdReq.setUserId(member.getId());
            CouponRecordSimpleResp userRecordById = ThriftResponseHelper.executeThriftCall(() ->
                    couponRecordQueryFeign.getUserRecordById(couponRecordIdReq));
            return JsonUtil.copy(userRecordById, ApiCouponRecordSimpleResp.class);
        });
    }

    @NeedLogin
    @PostMapping(value = "/getRecordByOrder", consumes = "application/json")
    public ResultDto<ApiCouponRecordOrderListResp> getRecordByOrder(@RequestBody ApiCouponRecordOrderQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getRecordByOrder", request, req -> {
            PromotionRecordOrderQueryReq couponRecordOrderQueryReq = JsonUtil.copy(req, PromotionRecordOrderQueryReq.class);
            LoginMemberDto member = TracerUtil.getMemberDto();;
            couponRecordOrderQueryReq.setUserId(member.getId());
            CouponRecordOrderListResp recordByOrder = ThriftResponseHelper.executeThriftCall(() ->
                    couponRecordQueryFeign.getRecordByOrder(couponRecordOrderQueryReq));
            return JsonUtil.copy(recordByOrder, ApiCouponRecordOrderListResp.class);
        });
    }
}
