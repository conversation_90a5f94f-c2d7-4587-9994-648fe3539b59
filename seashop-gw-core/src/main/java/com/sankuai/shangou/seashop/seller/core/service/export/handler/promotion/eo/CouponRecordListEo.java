package com.sankuai.shangou.seashop.seller.core.service.export.handler.promotion.eo;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: lhx
 * @date: 2024/2/23/023
 * @description:
 */
@Getter
@Setter
public class CouponRecordListEo {

    @ExcelProperty(value = "主键ID")
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "优惠券活动ID")
    @ExcelIgnore
    private Long couponId;

    @ExcelProperty(value = "优惠码")
    private String couponSn;

    /**
     * 面值(价格)
     */
    @ExcelProperty(value = "价值")
    private Long price;

    @ExcelProperty(value = "创建时间")
    private Date createTime;

    @ExcelProperty(value = "领取时间")
    private Date couponTime;

    @ExcelProperty(value = "领取人账号")
    private String userName;

    @ExcelProperty(value = "使用时间")
    private Date usedTime;

    @ExcelProperty(value = "用户ID")
    @ExcelIgnore
    private Long userId;

    @ExcelProperty(value = "订单ID")
    private String orderId;

    @ExcelProperty(value = "店铺ID")
    @ExcelIgnore
    private Long shopId;

    @ExcelProperty(value = "店铺名称")
    @ExcelIgnore
    private String shopName;

    @ExcelProperty(value = "优惠券状态 0-未使用 1-已使用 2-已过期")
    @ExcelIgnore
    private Integer couponStatus;

    @ExcelProperty(value = "状态")
    private String couponStatusDesc;

    @ExcelProperty(value = "优惠券开始时间")
    @ExcelIgnore
    private Date startTime;

    @ExcelProperty(value = "优惠券结束时间")
    @ExcelIgnore
    private Date endTime;
}
