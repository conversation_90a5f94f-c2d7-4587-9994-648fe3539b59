package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.DiscountActiveQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.DiscountActiveQueryFeign;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.promotion.ApiDiscountActiveProductDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiDiscountActiveQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiQueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiDiscountActiveListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiDiscountActiveResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/10/010
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiDiscountActive")
public class SellerApiDiscountActiveQueryController {

    @Resource
    private DiscountActiveQueryFeign discountActiveQueryFeign;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiDiscountActiveListResp>> pageList(@RequestBody ApiDiscountActiveQueryReq request) throws TException {
        
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            DiscountActiveQueryReq bean = JsonUtil.copy(req, DiscountActiveQueryReq.class);
            bean.setShopId(TracerUtil.getShopDto().getShopId());
            bean.checkParameter();
            BasePageResp<DiscountActiveSimpleResp> listRespBasePageResp = ThriftResponseHelper.executeThriftCall(() ->
                    discountActiveQueryFeign.pageList(bean));
            return PageResultHelper.transfer(listRespBasePageResp, ApiDiscountActiveListResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiDiscountActiveResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            DiscountActiveResp discountActiveResp = ThriftResponseHelper.executeThriftCall(() -> discountActiveQueryFeign.getById(req));
            return JsonUtil.copy(discountActiveResp, ApiDiscountActiveResp.class);
        });
    }

    @PostMapping(value = "/queryDiscountActiveProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BasePageResp<ApiDiscountActiveProductDto>> queryDiscountActiveProduct(@RequestBody ApiQueryDiscountActiveProductReq request) {
        
        return ThriftResponseHelper.responseInvoke("queryDiscountActiveProduct", request, req -> {
            req.checkParameter();

            QueryDiscountActiveProductReq remoteReq = JsonUtil.copy(req, QueryDiscountActiveProductReq.class);
            remoteReq.setShopId(TracerUtil.getShopDto().getShopId());
            BasePageResp<DiscountActiveProductDto> resp = ThriftResponseHelper.executeThriftCall(() ->
                    discountActiveQueryFeign.queryDiscountActiveProduct(remoteReq));
            return PageResultHelper.transfer(resp, ApiDiscountActiveProductDto.class);
        });
    }
}
