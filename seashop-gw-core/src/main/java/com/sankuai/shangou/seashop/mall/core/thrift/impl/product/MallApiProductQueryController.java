package com.sankuai.shangou.seashop.mall.core.thrift.impl.product;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryProductByIdReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiQueryProductRichTextReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.product.ApiRecommendProductsReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiProductListResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiProductPageResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiProductRichTextResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.product.ApiRecommendProductsResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.mall.core.service.product.MallProductService;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductByIdReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductRichTextReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.RecommendProductsReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductRichTextResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.RecommendProductsResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/27 12:05
 */
@RestController
@RequestMapping("/mallApi/apiProduct")
public class MallApiProductQueryController {

    @Resource
    private MallProductService mallProductService;
    @Resource
    private ProductQueryFeign productQueryFeign;

    @PostMapping(value = "/queryProductRichText", consumes = "application/json")
    public ResultDto<ApiProductRichTextResp> queryProductRichText(@RequestBody ApiQueryProductRichTextReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductRichText", request, req -> {
            req.checkParameter();

            QueryProductRichTextReq remoteReq = JsonUtil.copy(req, QueryProductRichTextReq.class);
            ProductRichTextResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductRichText(remoteReq));
            return JsonUtil.copy(resp, ApiProductRichTextResp.class);
        });
    }

    @PostMapping(value = "/queryProductById", consumes = "application/json")
//    @NeedLogin
    public ResultDto<ApiProductListResp> queryProductById(@RequestBody ApiQueryProductByIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductById", request, req -> {
            QueryProductByIdReq remoteReq = new QueryProductByIdReq();
            remoteReq.setProductIds(req.getProductIds());
            ProductListResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductById(remoteReq));
            List<ApiProductPageResp> productList = JsonUtil.copyList(resp.getProductList(), ApiProductPageResp.class, (source, target) -> {
                target.setSkuId(CollectionUtils.isEmpty(source.getSkuIds()) ? null : source.getSkuIds().get(0));
            });
            // 因为这里要按照接口传入的商品ID排序，所以这里啰嗦的处理一下
            Map<String, ApiProductPageResp> map = productList.stream().collect(Collectors.toMap(ApiProductPageResp::getProductId, Function.identity(), (p1, p2) -> p1));
            List<ApiProductPageResp> result = new ArrayList<>();
            for (Long productId : req.getProductIds()) {
                ApiProductPageResp apiProductPageResp = map.get(String.valueOf(productId));
                if (!Objects.isNull(apiProductPageResp)) {
                    result.add(apiProductPageResp);
                }
            }
            return ApiProductListResp.builder().productList(result).build();
        });
    }

    @PostMapping(value = "/queryProductForSelect", consumes = "application/json")
    public ResultDto<BasePageResp<ApiProductPageResp>> queryProductForSelect(@RequestBody ApiQueryProductPromotionExtReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductForSelect", request, req -> mallProductService.queryProduct(req));
    }

    @PostMapping(value = "/queryRecommendProducts", consumes = "application/json")
    public ResultDto<ApiRecommendProductsResp> queryRecommendProducts(@RequestBody ApiRecommendProductsReq recommendProductsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryRecommendProducts", recommendProductsReq,
            req -> {
                RecommendProductsReq apiRecommendProductsReq = JsonUtil.copy(req, RecommendProductsReq.class);
                RecommendProductsResp recommendProductsResp = ThriftResponseHelper.executeThriftCall(() ->
                        productQueryFeign.queryRecommendProducts(apiRecommendProductsReq));
                return JsonUtil.copy(recommendProductsResp, ApiRecommendProductsResp.class);
            });
    }
}
