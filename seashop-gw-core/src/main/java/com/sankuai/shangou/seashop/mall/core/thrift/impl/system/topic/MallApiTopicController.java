package com.sankuai.shangou.seashop.mall.core.thrift.impl.system.topic;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.ApiBaseShopReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.ApiBaseWapTopicQueryReq;
import com.sankuai.sgb2b.seashop.mall.api.thrift.request.topic.SellerIndexPageRes;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiBaseTopicRes;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.base.ApiBaseWapTopicRes;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.TopicCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.TopicQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWapTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseTopicRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWapTopicRes;
import com.sankuai.shangou.seashop.mall.core.aop.ShopStatusAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/mallApi/apiTopic")
@Slf4j
public class MallApiTopicController {


    @Resource
    private TopicQueryFeign topicQueryFeign;

    @Resource
    private TopicCMDFeign topicCMDFeign;

    @PostMapping(value = "/getById", consumes = "application/json")
    public ResultDto<ApiBaseWapTopicRes> getById(@RequestBody ApiBaseShopReq query) throws TException {
        //        query.setShopId(TracerUtil.getShop().getId());
        return ThriftResponseHelper.responseInvoke("getWapTopicById", query, req -> {

            BaseWapTopicQueryReq bean = JsonUtil.copy(req, BaseWapTopicQueryReq.class);
            if (req.getShopId()==0L){
                bean.setType(TemplateClientTypeEnum.PCTOPIC.getCode());
            }else {
                bean.setType(TemplateClientTypeEnum.PCTOPIC_SELLER.getCode());
            }

            bean.setClient(req.getId().toString());

            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getWapTopicById(bean));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    @GetMapping(value = "/queryRecommend")
    public ResultDto<List<ApiBaseTopicRes>> queryRecommend() throws TException {
        return ThriftResponseHelper.responseInvoke("queryRecommend", "queryRecommend", req -> {
            List<BaseTopicRes> result = ThriftResponseHelper.executeThriftCall(() ->
                topicQueryFeign.queryRecommend());
            return JsonUtil.copyList(result, ApiBaseTopicRes.class);
        });
    }




    @PostMapping(value = "/getWapTopicById", consumes = "application/json")
    public ResultDto<ApiBaseWapTopicRes> getWapTopicById(@RequestBody ApiBaseWapTopicQueryReq query) throws TException {
//        query.setShopId(TracerUtil.getShop().getId());
        return ThriftResponseHelper.responseInvoke("getWapTopicById", query, req -> {
            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                topicQueryFeign.getWapTopicById(JsonUtil.copy(req, BaseWapTopicQueryReq.class)));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }



    @GetMapping(value = "/getWapIndex")
    public ResultDto<ApiBaseWapTopicRes> getWapIndex() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        return ThriftResponseHelper.responseInvoke("getWapIndex", query, req -> {
            log.info("店铺首页查询数据:", req);
            String resultRpc = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getPlatWapIndex());
            ApiBaseWapTopicRes result = new ApiBaseWapTopicRes();
            result.setPage(resultRpc);
            return result;
        });
    }


    @GetMapping(value = "/getShopWapIndex")
    @ShopStatusAnnotation
    public ResultDto<SellerIndexPageRes> getShopWapIndex(@RequestParam Long shopId) throws TException {

        return ThriftResponseHelper.responseInvoke("getPCIndex", shopId, req -> {
            SellerIndexPageRes result = new SellerIndexPageRes();
            String resultRpc = ThriftResponseHelper.executeThriftCall(() ->
                topicQueryFeign.getSellerIndex(req));
            result.setPage(resultRpc);
            return result;
        });
    }


    @GetMapping(value = "/getPCIndex")
    public ResultDto<ApiBaseWapTopicRes> getPCIndex() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        return ThriftResponseHelper.responseInvoke("getWapIndex", query, req -> {
            log.info("店铺首页查询数据:", req);
            String resultRpc = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getPlatIndex());
            ApiBaseWapTopicRes result = new ApiBaseWapTopicRes();
            result.setPage(resultRpc);
            return result;
        });
    }


    @GetMapping(value = "/getPCHeader")
    public ResultDto<ApiBaseWapTopicRes> getPCHeader() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.Header.getCode());
        query.setClient("header");
        query.setShopId(0L);
        return ThriftResponseHelper.responseInvoke("getPCHeader", query, req -> {
            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                topicQueryFeign.getWapTopicById(req));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }


    @GetMapping(value = "/getPCFooter")
    public ResultDto<ApiBaseWapTopicRes> getPCFooter() throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.Footer.getCode());
        query.setClient("footer");
        query.setShopId(0L);
        return ThriftResponseHelper.responseInvoke("getPCFooter", query, req -> {
            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                topicQueryFeign.getWapTopicById(req));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }


    @GetMapping(value = "/getSellerPCHeader")
    public ResultDto<ApiBaseWapTopicRes> getSellerPCHeader(@RequestParam Long shopId) throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.HEADER_SELLER.getCode());
        query.setClient("header");
        query.setShopId(shopId);
        return ThriftResponseHelper.responseInvoke("getPCHeader", query, req -> {
            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getWapTopicById(req));
            return JsonUtil.copy(result,ApiBaseWapTopicRes.class);
        });
    }

    @GetMapping(value = "/getSellerPCFooter")
    public ResultDto<ApiBaseWapTopicRes> getSellerPCFooter(@RequestParam Long shopId) throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.FOOTER_SELLER.getCode());
        query.setClient("footer");
        query.setShopId(shopId);
        return ThriftResponseHelper.responseInvoke("getPCFooter", query, req -> {
            BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getWapTopicById(req));
            return JsonUtil.copy(result, ApiBaseWapTopicRes.class);
        });
    }

    @GetMapping(value = "/getSellerPCIndex")
    public ResultDto<ApiBaseWapTopicRes> getSellerPCIndex(@RequestParam(required = false) Long currentShopId) throws TException {
        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        return ThriftResponseHelper.responseInvoke("getPCIndex", query, req -> {
            log.info("店铺首页查询数据:", req);
            String resultRpc =ThriftResponseHelper.executeThriftCall(() ->
                    topicQueryFeign.getSellerPCIndex(currentShopId));
            ApiBaseWapTopicRes result = new ApiBaseWapTopicRes();
            result.setPage(resultRpc);
            return result;
        });
    }


}
