package com.sankuai.shangou.seashop.seller.core.service.product;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductPromotionExtReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductPageResp;

/**
 * <AUTHOR>
 * @date 2024/01/03 10:40
 */
public interface SellerProductService {

    /**
     * 导出商品
     *
     * @param request 查询参数
     */
    void exportProduct(ApiQueryProductReq request, LoginShopDto shopInfo);

    /**
     * 商品是否绑定牵牛花
     *
     * @param productId 商品id
     * @return 是否绑定牵牛花
     */
    Boolean hasBind(Long productId);

    /**
     * 查询商品
     *
     * @param request 查询参数
     * @return 商品列表
     */
    BasePageResp<ApiProductPageResp> queryProduct(ApiQueryProductPromotionExtReq request);
}
