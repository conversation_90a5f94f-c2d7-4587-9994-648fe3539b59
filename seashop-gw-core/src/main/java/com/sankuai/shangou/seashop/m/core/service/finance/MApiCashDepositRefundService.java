package com.sankuai.shangou.seashop.m.core.service.finance;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.m.thrift.core.request.finance.ApiCashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.m.thrift.core.response.finance.ApiCashDepositRefundResp;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
public interface MApiCashDepositRefundService {

    /**
     * 通过条件查询保证金退款明细信息
     *
     * @param request
     * @return
     */
    BasePageResp<ApiCashDepositRefundResp> refundList(ApiCashDepositRefundQueryReq request);

    /**
     * 查询待审核保证金数量
     *
     * @return
     */
    Long refundAuditNum();

    /**
     * 通过ID查询审核信息
     *
     * @param id
     * @return
     */
    ApiCashDepositRefundDetailResp refundDetail(Long id);
}
