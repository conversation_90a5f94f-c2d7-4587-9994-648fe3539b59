package com.sankuai.shangou.seashop.m.core.service.order.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.core.service.export.MExportTaskBiz;
import com.sankuai.shangou.seashop.m.core.service.export.model.CreateExportTaskBo;
import com.sankuai.shangou.seashop.m.core.service.order.MOrderRefundService;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.PlatformApproveBatchReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PlatformApproveBatchResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.PlatformRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.PlatformRefundDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundLogListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundUserDeliverExpressResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class MOrderRefundServiceImpl implements MOrderRefundService {

    @Resource
    private MExportTaskBiz exportTaskBiz;
    @Resource
    private OrderRefundQueryFeign orderRefundQueryFeign;
    @Resource
    private OrderRefundCmdFeign orderRefundCmdFeign;

    @Override
    public void export(PlatformQueryRefundReq queryReq) {
        CreateExportTaskBo createExportTaskBo = new CreateExportTaskBo();
        createExportTaskBo.setTaskType(ExportTaskType.REFUND_PAGE_LIST);
        createExportTaskBo.setExecuteParam(queryReq);
        createExportTaskBo.setOperatorId(TracerUtil.getManagerDto().getId());
        createExportTaskBo.setOperatorName(TracerUtil.getManagerDto().getName());

        if (queryReq.getTab() != null) {
            createExportTaskBo.setCustomTaskName(queryReq.getTab().getDesc() + "列表导出");
        }
        exportTaskBiz.create(createExportTaskBo);
    }

    @Override
    public PlatformRefundDetailResp queryDetail(PlatformQueryRefundDetailReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.platformQueryDetail(queryReq));
    }

    @Override
    public RefundLogListResp queryRefundLog(BaseIdReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryRefundLog(queryReq));
    }

    @Override
    public BasePageResp<PlatformRefundDto> platformQueryRefundPage(PlatformQueryRefundReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.platformQueryRefundPage(queryReq));
    }

    @Override
    public RefundUserDeliverExpressResp queryUserDeliverExpress(BaseIdReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundQueryFeign.queryUserDeliverExpress(queryReq));
    }

    @Override
    public BaseResp platformConfirm(PlatformApproveReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.platformConfirm(req));
    }

    @Override
    public BaseResp platformReject(PlatformApproveReq req) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.platformReject(req));
    }

    @Override
    public PlatformApproveBatchResp platformBatchConfirm(PlatformApproveBatchReq batchReq) {
        return ThriftResponseHelper.executeThriftCall(() -> orderRefundCmdFeign.platformBatchConfirm(batchReq));
    }
}
