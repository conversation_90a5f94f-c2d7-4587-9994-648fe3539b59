package com.sankuai.shangou.seashop.m.core.service.export.handler.order.wrapper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.base.export.writeHandler.FindMergeRegionWhenWriteCell;
import com.sankuai.shangou.seashop.m.core.service.export.handler.order.eo.OrderInvoiceEo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInvoiceDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderInvoiceTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;

import cn.hutool.core.util.DesensitizedUtil;

/**
 * <AUTHOR>
 * @date 2024/01/02 11:20
 */
public class OrderInvoiceDataWrapper extends PageExportWrapper<OrderInvoiceEo, QueryPlatformOrderReq> {

    private final OrderQueryFeign orderQueryFeign;

    private static String[] ELC_INVOICE_ROW1_FIELDS = {"收票人手机号", "收票人邮箱"};
    private static String[] ADDED_TAX_INVOICE_ROW1_FIELDS = {"注册地址", "注册电话", "开户银行", "银行帐号", "收票人姓名", "收票人手机号", "收票人地址"};


    public OrderInvoiceDataWrapper(OrderQueryFeign orderQueryFeign) {
        super(1);
        this.orderQueryFeign = orderQueryFeign;
    }

    @Override
    public List<OrderInvoiceEo> getPageList(QueryPlatformOrderReq param) {
        param.setHasInvoice(Boolean.TRUE);
        List<OrderInfoDto> orderList = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.pageQueryPlatformOrder(param)).getData();
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.EMPTY_LIST;
        }

        List<OrderInvoiceEo> orderInvoiceList = new ArrayList<>();
        orderList.forEach(order -> {
            OrderInvoiceDto orderInvoice = order.getOrderInvoice();
            if (orderInvoice == null) {
                return;
            }

            if (OrderInvoiceTypeEnum.VAT.getCode().equals(orderInvoice.getInvoiceType())) {
                orderInvoiceList.addAll(addedTaxInvoiceBuild(order));
            }
            else {
                orderInvoiceList.addAll(elcInvoiceBuild(order));
            }
        });
        return orderInvoiceList;
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return Collections.singletonList(new FindMergeRegionWhenWriteCell(OrderInvoiceEo.class, null, getDataSupplier(), getPrevNum()));
    }

    @Override
    public String sheetName() {
        return "平台订单发票信息";
    }

    private List<OrderInvoiceEo> elcInvoiceBuild(OrderInfoDto order) {
        OrderInvoiceEo commonEo = commonInvoiceBuild(order);
        if (commonEo == null) {
            return Collections.EMPTY_LIST;
        }

        List<OrderInvoiceEo> eoList = new ArrayList<>();
        List<String> row2List = getElcInvoiceRow2List(order.getOrderInvoice());
        for (int i = 0; i < row2List.size(); i++) {
            OrderInvoiceEo eo = JsonUtil.copy(commonEo, OrderInvoiceEo.class);
            eo.setElcInvoiceRow1(ELC_INVOICE_ROW1_FIELDS[i]);
            eo.setElcInvoiceRow2(row2List.get(i));
            eoList.add(eo);
        }
        return eoList;
    }

    private List<OrderInvoiceEo> addedTaxInvoiceBuild(OrderInfoDto order) {
        OrderInvoiceEo commonEo = commonInvoiceBuild(order);
        if (commonEo == null) {
            return Collections.EMPTY_LIST;
        }

        List<OrderInvoiceEo> eoList = new ArrayList<>();
        List<String> row2List = getAddedTaxInvoiceRow2List(order.getOrderInvoice());
        for (int i = 0; i < row2List.size(); i++) {
            OrderInvoiceEo eo = JsonUtil.copy(commonEo, OrderInvoiceEo.class);
            eo.setAddedTaxInvoiceRow1(ADDED_TAX_INVOICE_ROW1_FIELDS[i]);
            eo.setAddedTaxInvoiceRow2(row2List.get(i));
            eoList.add(eo);
        }
        return eoList;
    }

    private OrderInvoiceEo commonInvoiceBuild(OrderInfoDto order) {
        OrderInvoiceDto orderInvoice = order.getOrderInvoice();
        if (orderInvoice == null) {
            return null;
        }

        OrderInvoiceEo eo = new OrderInvoiceEo();
        eo.setOrderId(order.getOrderId());
        eo.setUserName(order.getUserName());
        eo.setInvoiceType(OrderInvoiceTypeEnum.getDesc(orderInvoice.getInvoiceType()));
        eo.setInvoiceTitle(orderInvoice.getInvoiceTitle());
        eo.setInvoiceCode(orderInvoice.getInvoiceCode());
        eo.setInvoiceContext(orderInvoice.getInvoiceContext());
        return eo;
    }

    private List<String> getElcInvoiceRow2List(OrderInvoiceDto orderInvoiceDto) {
        List<String> row2List = new ArrayList<>();
        row2List.add(orderInvoiceDto.getCellPhone());
        row2List.add(orderInvoiceDto.getEmail());
        return row2List;
    }

    private List<String> getAddedTaxInvoiceRow2List(OrderInvoiceDto orderInvoiceDto) {
        List<String> row2List = new ArrayList<>();
        row2List.add(orderInvoiceDto.getRegisterAddress());
        row2List.add(orderInvoiceDto.getRegisterPhone());
        row2List.add(orderInvoiceDto.getBankName());
        row2List.add(orderInvoiceDto.getBankNo());
        row2List.add(orderInvoiceDto.getRealName());
        row2List.add(DesensitizedUtil.mobilePhone(orderInvoiceDto.getCellPhone()));
        row2List.add(orderInvoiceDto.getAddress());
        return row2List;
    }
}
