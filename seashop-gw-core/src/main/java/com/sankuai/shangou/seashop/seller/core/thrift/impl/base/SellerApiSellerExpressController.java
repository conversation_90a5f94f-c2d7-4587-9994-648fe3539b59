package com.sankuai.shangou.seashop.seller.core.thrift.impl.base;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyResp;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ApiQueryExpressCompanyReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiQueryExpressCompanyResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/sellerApi/apiSellerExpress")
public class SellerApiSellerExpressController {

    @Resource
    private ExpressQueryFeign expressQueryFeign;

    @PostMapping(value = "/querySellerExpressCompanyList", consumes = "application/json")
    public ResultDto<ApiQueryExpressCompanyResp> querySellerExpressCompanyList(@RequestBody ApiQueryExpressCompanyReq queryExpressCompanyReq) throws TException {
        return ThriftResponseHelper.responseInvoke("querySellerExpressCompanyList", queryExpressCompanyReq, req -> {
            QueryExpressCompanyResp queryExpressCompanyResp = ThriftResponseHelper.executeThriftCall(() ->
                    expressQueryFeign.queryExpressCompanyList(JsonUtil.copy(req, QueryExpressCompanyReq.class)));
            return JsonUtil.copy(queryExpressCompanyResp, ApiQueryExpressCompanyResp.class);
        });
    }

}
