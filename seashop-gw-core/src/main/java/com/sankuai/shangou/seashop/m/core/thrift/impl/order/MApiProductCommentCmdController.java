package com.sankuai.shangou.seashop.m.core.thrift.impl.order;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.m.thrift.core.request.order.ApiHideProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.HideProductCommentReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/22 8:50
 */
@RestController
@RequestMapping("/mApi/apiProductComment")
@Slf4j
public class MApiProductCommentCmdController {

    @Resource
    private ProductCommentCmdFeign productCommentCmdFeign;

    @PostMapping(value = "/hideProductCommentForPlatForm", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.MANAGER)
    public ResultDto<BaseResp> hideProductCommentForPlatForm(@RequestBody ApiHideProductCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("hideProductCommentForPlatForm", request, req -> {
            req.checkParameter();

            HideProductCommentReq remoteReq = JsonUtil.copy(req, HideProductCommentReq.class);
            return ThriftResponseHelper.executeThriftCall(() -> productCommentCmdFeign.hideProductCommentForPlatForm(remoteReq));
        });
    }
}
