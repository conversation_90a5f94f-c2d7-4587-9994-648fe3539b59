package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.AddCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CancelCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.UpdateCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationCmdFeign;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiAddCollocationReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiUpdateCollocationReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 15:29
 */
@RestController
@RequestMapping("/sellerApi/apiCollocation")
public class SellerApiCollocationCmdController {

    @Resource
    private CollocationCmdFeign collocationCmdFeign;

    @PostMapping(value = "/addCollocation", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> addCollocation(@RequestBody ApiAddCollocationReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("addCollocation", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> collocationCmdFeign.addCollocation(JsonUtil.copy(req, AddCollocationReq.class)));
        });
    }

    @PostMapping(value = "/updateCollocation", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateCollocation(@RequestBody ApiUpdateCollocationReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("updateCollocation", request, req -> {
            req.setShopId(TracerUtil.getShopDto().getShopId());
            req.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            req.checkParameter();
            return ThriftResponseHelper.executeThriftCall(() -> collocationCmdFeign.updateCollocation(JsonUtil.copy(req, UpdateCollocationReq.class)));
        });
    }

    @GetMapping(value = "/cancelCollocation")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> cancelCollocation(@RequestParam Long id) throws TException {
        AssertUtil.throwIfNull(id, "id不能为空");

        

        return ThriftResponseHelper.responseInvoke("cancelCollocation", id, req -> {
            CancelCollocationReq cancelCollocationReq = new CancelCollocationReq();
            cancelCollocationReq.setId(id);
            cancelCollocationReq.setShopId(TracerUtil.getShopDto().getShopId());
            cancelCollocationReq.setOperationUserId(TracerUtil.getShopDto().getManagerId());
            return ThriftResponseHelper.executeThriftCall(() -> collocationCmdFeign.cancelCollocation(cancelCollocationReq));
        });
    }
}
