package com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.wrapper;

import java.util.List;

import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.m.common.remote.promotion.MFlashSaleRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.promotion.eo.FlashSaleEo;
import com.sankuai.shangou.seashop.m.thrift.core.request.promotion.ApiFlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleSimpleResp;

import cn.hutool.core.date.DateUtil;

/**
 * @author: lhx
 * @date: 2023/12/25/025
 * @description:
 */
public class FlashSaleDataWrapper extends PageExportWrapper<FlashSaleEo, ApiFlashSaleQueryReq> {

    private final MFlashSaleRemoteService MFlashSaleRemoteService;

    public FlashSaleDataWrapper(MFlashSaleRemoteService MFlashSaleRemoteService) {
        this.MFlashSaleRemoteService = MFlashSaleRemoteService;
    }

    @Override
    public List<FlashSaleEo> getPageList(ApiFlashSaleQueryReq param) {
        BasePageResp<FlashSaleSimpleResp> flashSalePage = MFlashSaleRemoteService.pageList(JsonUtil.copy(param, FlashSaleQueryReq.class));
        if (null != flashSalePage && null != flashSalePage.getData()) {
            BasePageResp<FlashSaleEo> saleEoBasePageResp = PageResultHelper.transfer(flashSalePage, FlashSaleEo.class, (flashSaleSimple, flashSaleEo) -> {
                if (null != flashSaleSimple.getBeginDate()) {
                    flashSaleEo.setBeginDate(DateUtil.format(flashSaleSimple.getBeginDate(), "yyyy-MM-dd HH:mm:ss"));
                }
                if (null != flashSaleSimple.getEndDate()) {
                    flashSaleEo.setEndDate(DateUtil.format(flashSaleSimple.getEndDate(), "yyyy-MM-dd HH:mm:ss"));
                }
            });
            return saleEoBasePageResp.getData();
        }
        return null;
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return super.getWriteHandlerList();
    }

    @Override
    public String sheetName() {
        return "限时购列表";
    }
}
