package com.sankuai.shangou.seashop.core.util;

import com.sankuai.sgb2b.seashop.mall.api.thrift.request.base.MemberUserInfo;
import com.sankuai.shangou.seashop.m.thrift.user.account.request.ManagerUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;

/**
 * <AUTHOR>
 */
public final class AuthUtil {


    // seller 登录用户
    public static ShopUserInfo shopUserInfo() {
        return new ShopUserInfo();
    }

    //m 登录用户
    public static ManagerUserInfo managerUserInfo() {
        return new ManagerUserInfo();
    }

    //mall 登录用户
    public static MemberUserInfo memberUserInfo() {
        return new MemberUserInfo();
    }

    // seller 登录用户
    public static void shopUserInfo(ShopUserInfo shopUserInfo) {

    }

    //m 登录用户
    public static void managerUserInfo(ManagerUserInfo managerUserInfo) {

    }

    //mall 登录用户
    public static void memberUserInfo(MemberUserInfo memberUserInfo) {

    }


}
