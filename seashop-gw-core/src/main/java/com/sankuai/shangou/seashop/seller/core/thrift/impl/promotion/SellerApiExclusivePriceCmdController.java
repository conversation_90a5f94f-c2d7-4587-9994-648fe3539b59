package com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.base.security.utils.TracerUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductUpdateReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceSaveReq;
import com.sankuai.shangou.seashop.seller.common.remote.promotion.SellerExclusivePriceRemoteService;
import com.sankuai.shangou.seashop.seller.core.service.promotion.SellerApiExclusivePriceService;
import com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion.bo.ApiExclusivePriceImportBo;
import com.sankuai.shangou.seashop.seller.core.thrift.impl.promotion.dto.ApiExclusivePriceImportDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiExclusivePriceImportReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiExclusivePriceProductUpdateReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiExclusivePriceSaveReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiExclusivePriceImportResp;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@RestController
@RequestMapping("/sellerApi/apiExclusivePrice")
public class SellerApiExclusivePriceCmdController {

    @Resource
    private SellerApiExclusivePriceService sellerApiExclusivePriceService;

    @Resource
    private SellerExclusivePriceRemoteService sellerExclusivePriceRemoteService;

    @PostMapping(value = "/save", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> save(@RequestBody ApiExclusivePriceSaveReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            ExclusivePriceSaveReq saveReq = JsonUtil.copy(req, ExclusivePriceSaveReq.class);
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            saveReq.setShopId(loginShopDto.getShopId());
            saveReq.checkParameter();
            return sellerExclusivePriceRemoteService.save(saveReq);
        });
    }

    @PostMapping(value = "/endActive", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("endActive", request, req -> {
            req.checkParameter();
            return sellerExclusivePriceRemoteService.endActive(req);
        });
    }

    @PostMapping(value = "/updateProduct", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<BaseResp> updateProduct(@RequestBody ApiExclusivePriceProductUpdateReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("updateProduct", request, req -> {
            LoginShopDto loginShopDto = TracerUtil.getShopDto();
            req.setShopId(loginShopDto.getShopId());
            req.checkParameter();
            return sellerExclusivePriceRemoteService.updateProduct(JsonUtil.copy(req, ExclusivePriceProductUpdateReq.class));
        });
    }

    @PostMapping(value = "/importData", consumes = "application/json")
    @NeedLogin(userType = RoleEnum.SHOP)
    public ResultDto<ApiExclusivePriceImportResp> importData(@RequestBody ApiExclusivePriceImportReq request) throws TException {
        

        return ThriftResponseHelper.responseInvoke("importData", request, req -> {
                LoginShopDto loginShopDto = TracerUtil.getShopDto();
                req.setShopId(loginShopDto.getShopId());
                req.checkParameter();
                ApiExclusivePriceImportDto apiExclusivePriceImportDto = sellerApiExclusivePriceService.importData(JsonUtil.copy(req, ApiExclusivePriceImportBo.class));
                return JsonUtil.copy(apiExclusivePriceImportDto, ApiExclusivePriceImportResp.class);
            }
        );
    }
}
