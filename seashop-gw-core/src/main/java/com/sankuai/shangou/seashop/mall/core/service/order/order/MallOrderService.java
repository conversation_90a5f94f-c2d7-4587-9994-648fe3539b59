package com.sankuai.shangou.seashop.mall.core.service.order.order;

import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.ApiStatsPurchaseSkuExportResp;
import com.sankuai.sgb2b.seashop.mall.api.thrift.response.order.OrderExportResp;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryUserOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.StatsUserPurchaseSkuReq;

/**
 * <AUTHOR>
 */
public interface MallOrderService {

    /**
     * 导出订单
     *
     * @param queryReq
     * @return
     */
    OrderExportResp exportOrder(QueryUserOrderReq queryReq);

    /**
     * 导出用户采购SKU
     *
     * @param queryReq 查询参数
     * @return 下载路径
     */
    ApiStatsPurchaseSkuExportResp exportUserPurchaseSku(StatsUserPurchaseSkuReq queryReq);

}
