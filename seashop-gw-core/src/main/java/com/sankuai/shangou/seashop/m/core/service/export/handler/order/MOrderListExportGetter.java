package com.sankuai.shangou.seashop.m.core.service.export.handler.order;

import java.util.Collections;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.m.common.enums.ExportTaskType;
import com.sankuai.shangou.seashop.m.common.remote.order.MOrderRemoteService;
import com.sankuai.shangou.seashop.m.core.service.export.handler.order.eo.OrderInfoEo;
import com.sankuai.shangou.seashop.m.core.service.export.handler.order.wrapper.OrderInfoWrapper;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;

/**
 * <AUTHOR>
 */
@Service
public class MOrderListExportGetter extends AbstractBaseDataGetter<QueryPlatformOrderReq>
        implements SingleWrapperDataGetter<QueryPlatformOrderReq> {

    @Resource
    private MOrderRemoteService MOrderRemoteService;
    @Resource
    private OrderQueryFeign orderQueryFeign;

    @Override
    public DataContext selectData(QueryPlatformOrderReq param) {
        // 构造组件处理的数据上下文
        DataContext context = new DataContext();
        // 构造数据包装器，每一个包装器对应一个sheet页，并且每个包装器可以单独定义自己的excel处理器，比如样式、合并单元格等
        BaseExportWrapper<OrderInfoEo, QueryPlatformOrderReq> orderWrapper = new OrderInfoWrapper(MOrderRemoteService, orderQueryFeign);
        context.setSheetDataList(Collections.singletonList(orderWrapper));
        return context;
    }

    @Override
    public Integer getModule() {
        return ExportTaskType.ORDER_LIST.getType();
    }

    @Override
    public String getFileName() {
        return ExportTaskType.ORDER_LIST.getName();
    }
}
