package com.sankuai.shangou.seashop.m.core.thrift.impl.system.convert;

import com.sankuai.shangou.seashop.m.thrift.system.setting.request.ApiBaseShopSiteSettingReq;
import com.sankuai.shangou.seashop.m.thrift.system.setting.response.ApiBaseShopSiteSettingRes;
import org.mapstruct.Mapper;

import com.sankuai.shangou.seashop.base.thrift.core.request.BaseShopSitSettingReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseShopSitSettingRes;

@Mapper(componentModel = "spring")
public interface MApiSiteSettingConverter {
    BaseShopSitSettingReq convertReq(ApiBaseShopSiteSettingReq settingReq);

    ApiBaseShopSiteSettingRes convertRes(BaseShopSitSettingRes shopSitSettingRes);
}
