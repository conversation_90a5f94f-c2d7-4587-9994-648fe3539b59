<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-gw</artifactId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>seashop-gw-core</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-gw-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-trade-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>himall-report-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>hishop-xxl-job-client-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>himall-web-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zaxxer</groupId>
                    <artifactId>HikariCP</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.mysql</groupId>
                    <artifactId>mysql-connector-j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>