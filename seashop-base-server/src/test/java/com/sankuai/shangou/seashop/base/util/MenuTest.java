package com.sankuai.shangou.seashop.base.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.user.account.service.ManagerService;
import com.sankuai.shangou.seashop.user.dao.account.domain.FavoriteShop;
import com.sankuai.shangou.seashop.user.dao.account.domain.Privilege;
import com.sankuai.shangou.seashop.user.dao.account.domain.RolePrivilege;
import com.sankuai.shangou.seashop.user.dao.account.repository.PrivilegeRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.RolePrivilegeRepository;
import com.sankuai.shangou.seashop.user.thrift.account.dto.MenuDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024/10/09 10:09
 */
@SpringBootTest
@Slf4j
public class MenuTest {

    private final String menuFilePath = "/Users/<USER>/Desktop/menu/plat.json";

    private final String sellerMenuFilePath = "/Users/<USER>/Desktop/menu/seller.json";
    private final Long DEFAULT_PARENT_ID = 0L;
    private final Integer DEFAULT_SELLER_ID = 1;
    private final Integer DEFAULT_PLATFORM_ID = 0;

    private final Integer DEFAULT_POWER_TYPE = 0;

    @Resource
    private PrivilegeRepository privilegeRepository;
    @Resource
    private RolePrivilegeRepository rolePrivilegeRepository;
    @Resource
    private ManagerService managerService;

    @Test
    public void importPlatMenu() {
        log.info("[导入菜单] start, filePath: {}", menuFilePath);

        // 读取json文件
        log.info("[导入菜单] 读取json文件");
        String json = FileUtil.readString(menuFilePath, "UTF-8");
        List<MenuDto> menuDto = JSONUtil.toList(json, MenuDto.class);
        log.info("[导入菜单] 读取json文件完成，menuDto: {}", JsonUtil.toJsonString(menuDto));

        // 遍历菜单开始导入
        TransactionHelper.doInTransaction(() -> {
            log.info("[导入菜单] 清空数据库");
            LambdaQueryWrapper<Privilege> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Privilege::getPlatformId, DEFAULT_PARENT_ID);
            privilegeRepository.remove(wrapper);

            log.info("[导入菜单] 遍历菜单开始导入");
            importPlatToDb(DEFAULT_PARENT_ID, menuDto);
        });
        log.info("[导入菜单] end");
    }

    private void importPlatToDb(Long parentId, List<MenuDto> menuList) {
        log.info("[导入菜单] parentId: {}, menuList: {}", parentId, JsonUtil.toJsonString(menuList));
        if (CollUtil.isEmpty(menuList)) {
            return;
        }

        AtomicInteger sequence = new AtomicInteger(0);
        menuList.forEach(menuDto -> {
            Privilege privilege = new Privilege();
            privilege.setCode(menuDto.getId());
            privilege.setName(menuDto.getName());
            privilege.setUrl(menuDto.getPath());
            privilege.setIcon(menuDto.getIcon());
            privilege.setIzTab(menuDto.getIzTab());
            privilege.setParentId(parentId);
            privilege.setDisplaySequence(sequence.getAndAdd(1));
            privilege.setPlatformId(DEFAULT_PLATFORM_ID);
            privilege.setPowerType(DEFAULT_POWER_TYPE);
            privilege.setCreateTime(new Date());
            privilege.setUpdateTime(new Date());
            privilegeRepository.save(privilege);
            importPlatToDb(privilege.getId(), menuDto.getMenus());
        });
    }


    @Test
    public void importSellerMenu() {
        log.info("[导入菜单] start, filePath: {}", menuFilePath);

        // 读取json文件
        log.info("[导入菜单] 读取json文件");
        String json = FileUtil.readString(menuFilePath, "UTF-8");
        List<MenuDto> menuDto = JSONUtil.toList(json, MenuDto.class);
        log.info("[导入菜单] 读取json文件完成，menuDto: {}", JsonUtil.toJsonString(menuDto));

        // 遍历菜单开始导入
        TransactionHelper.doInTransaction(() -> {
            log.info("[导入菜单] 清空数据库");
            LambdaQueryWrapper<Privilege> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Privilege::getPlatformId, DEFAULT_SELLER_ID);
            privilegeRepository.remove(wrapper);

            log.info("[导入菜单] 遍历菜单开始导入");
            importSellerToDb(DEFAULT_PARENT_ID, menuDto);
        });
        log.info("[导入菜单] end");
    }

    private void importSellerToDb(Long parentId, List<MenuDto> menuList) {
        log.info("[导入菜单] parentId: {}, menuList: {}", parentId, JsonUtil.toJsonString(menuList));
        if (CollUtil.isEmpty(menuList)) {
            return;
        }

        AtomicInteger sequence = new AtomicInteger(0);
        menuList.forEach(menuDto -> {
            Privilege privilege = new Privilege();
            privilege.setCode(menuDto.getId());
            privilege.setName(menuDto.getName());
            privilege.setUrl(menuDto.getPath());
            privilege.setIcon(menuDto.getIcon());
            privilege.setIzTab(menuDto.getIzTab());
            privilege.setParentId(parentId);
            privilege.setDisplaySequence(sequence.getAndAdd(1));
            privilege.setPlatformId(DEFAULT_SELLER_ID);
            privilege.setPowerType(DEFAULT_POWER_TYPE);
            privilege.setCreateTime(new Date());
            privilege.setUpdateTime(new Date());
            privilegeRepository.save(privilege);
            importSellerToDb(privilege.getId(), menuDto.getMenus());
        });
    }
    @Test
    public void updateRolePrivilege() {
        Long roleId = 1L;
        rolePrivilegeRepository.deleteByRoleId(1L);

        List<Privilege> privilegeList = privilegeRepository.list();
        privilegeList.forEach(privilege -> {
            RolePrivilege rolePrivilege = new RolePrivilege();
            rolePrivilege.setRoleId(roleId);
            rolePrivilege.setPrivilegeId(privilege.getId().intValue());
            rolePrivilege.setCreateTime(new Date());
            rolePrivilege.setUpdateTime(new Date());
            rolePrivilegeRepository.save(rolePrivilege);
        });
    }

//    @Test
//    public void queryMenuAuth() {
//        List<MenuDto> menuDtos = managerService.queryMenuAuth(713L);
//        log.info("[查询菜单权限] menuDtos: {}", JsonUtil.toJsonString(menuDtos));
//    }


}
