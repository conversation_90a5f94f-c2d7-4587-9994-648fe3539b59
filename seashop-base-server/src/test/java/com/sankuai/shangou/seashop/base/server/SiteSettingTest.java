//package com.sankuai.shangou.seashop.base.server;
//
//import cn.hutool.json.JSONUtil;
//import com.sankuai.shangou.seashop.base.core.service.SiteSettingService;
//import com.sankuai.shangou.seashop.base.thrift.core.request.BaseShopSitSettingReq;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
///**
// * @description:
// * @author: LXH
// **/
//@SpringBootTest
//@RunWith(SpringRunner.class)
//@Slf4j
//public class SiteSettingTest {
//    @Resource
//    private SiteSettingService siteSettingService;
//
//    @Test
//    public void getShopSettings(){
//        log.info(JSONUtil.toJsonStr(siteSettingService.getShopSettings()));
//    }
//
//    @Test
//    public void saveShopSettings(){
//        String shopId = "{\n" +
//                "  \"shopConExpDay\": \"2\",\n" +
//                "  \"shopTipDay\": \"1\",\n" +
//                "  \"shopConBeforeExpDay\": \"1\"\n" +
//                "}";
//        BaseShopSitSettingReq req = JSONUtil.toBean(shopId, BaseShopSitSettingReq.class);
//        log.info(JSONUtil.toJsonStr(siteSettingService.saveShopSettings(req)));
//    }
//}
