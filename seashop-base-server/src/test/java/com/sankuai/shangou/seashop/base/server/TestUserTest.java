//package com.sankuai.shangou.seashop.base.server;
//
//import com.sankuai.shangou.seashop.base.StartApp;
//import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
//import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.thrift.TException;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import javax.annotation.Resource;
//import java.util.List;
//
//@SpringBootTest(classes = StartApp.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
//@Slf4j
//public class TestUserTest {
//
//    @Resource
//    TestUserCMDThriftService service;
//
//    @Resource
//    TestUserRepository testUserRepository;
//
//    @Test
//    public void TestCreateUser() throws TException {
//        TestUserReq testUserReq = new TestUserReq();
//        testUserReq.setName("赵本山");
//        testUserReq.setAge(65);
//        testUserReq.setEmail("<EMAIL>");
//        ResultDto<Long> resultDto = service.create(testUserReq);
//        System.out.println("返回值：" + resultDto.getCode());
//    }
//
//    @Test
//    public void TestUpdateUser() throws TException {
//        TestUserReq testUserReq = new TestUserReq();
//        testUserReq.setId(6L);
//        testUserReq.setName("彭江雄2233");
//        ResultDto<Integer> resultDto = service.update(testUserReq);
//        System.out.println("返回值：" + resultDto.getData());
//    }
//
//    @Test
//    public void TestDeleteUserById() throws TException {
//        ResultDto<Integer> resultDto = service.deleteById(7L);
//        System.out.println("返回值：" + resultDto.getData());
//    }
//
//    @Test
//    public void TestGetAll() throws TException {
//        List<TestUser> testUsers = testUserRepository.getAll();
//        testUsers.forEach(System.out::println);
//    }
//
//    @Test
//    public void TestWrapper() throws TException {
//        List<TestUser> testUsers = testUserRepository.getTestUserByCondition();
//        testUsers.forEach(user -> {
//            System.out.println(user.getName());
//        });
//
//    }
//
//    @Test
//    public void TestGetPage() throws TException {
//        BasePageResp testUsers = testUserRepository.getPage();
//        System.out.println("总页数："+ testUsers.getPages());
//        System.out.println("总条数："+testUsers.getTotalCount());
//        System.out.println("当前页码："+testUsers.getPageNo());
//        testUsers.getData().forEach(System.out::println);
//    }
//}
