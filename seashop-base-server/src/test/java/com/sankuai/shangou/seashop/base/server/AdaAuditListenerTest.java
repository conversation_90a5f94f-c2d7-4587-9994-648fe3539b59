package com.sankuai.shangou.seashop.base.server;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.BaseApplication;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.common.constant.LockConstant;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.shop.mq.dto.AdaAuditDTO;
import com.sankuai.shangou.seashop.user.shop.service.ShopService;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopStatusReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/11/30 11:28
 */
@SpringBootTest(classes = BaseApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Slf4j
public class AdaAuditListenerTest {

    @Resource
    private ShopService shopService;
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private DistributedLockService distributedLockService;

    @Test
    public void onMessage() {
        String body = "{\"app_id\":\"app_39b69a1c-d9ad-4e30-bb29-d80833b86885\",\"audit_desc\":\"平台法人信息填写错误\",\"audit_state\":\"B\",\"created_time\":\"20241129163107\",\"member_id\":\"1000002\",\"object\":\"corp_member\",\"order_no\":\"652897940910088221\",\"prod_mode\":\"true\"}";
        log.info("【mafka消费】【汇付审核结果】消息内容为: {}", body);
        try {
            AdaAuditDTO adaAuditDTO = JSONUtil.toBean(body, AdaAuditDTO.class);
            //获取shopId
            Long shopId = Long.parseLong(adaAuditDTO.getMemberId());
            CmdShopStatusReq cmdShopStatusReq = new CmdShopStatusReq();
            cmdShopStatusReq.setShopId(shopId);
            //            加锁
            String lockKey = LockConstant.BUSINESS_SHOP_APPLY_AUDIT_LOCK + shopId;
            String scene = LockConstant.BUSINESS_SHOP_APPLY_AUDIT_SCENE + shopId;
            distributedLockService.tryLock(new LockKey(lockKey, scene), () -> {
                //判断是否是审核通过
                //判断是否更新
                if (CommonConstant.CORP_MEMBER_UPDATE.equals(adaAuditDTO.getObject())) {
                    Shop shop = shopRepository.getById(shopId);
                    //根据shopStatus判断是否需要更新此字段内容
                    boolean isUpdate = shop.getShopStatus().equals(ShopEnum.AuditStatus.WaitAudit.getCode());
                    if (isUpdate) {
                        updateAdaAuditing(cmdShopStatusReq, adaAuditDTO);
                    }
                    else {
                        if (CommonConstant.UPDATE_STATE_SUCCESS.equals(adaAuditDTO.getAuditState())) {
                            shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode());
                        }
                        else {
                            shop.setAdapayStatus(ShopEnum.AdapayAuditStatus.AdapayFailed.getCode());
                            shop.setAdapayReason(adaAuditDTO.getAuditDesc());
                        }
                        shopRepository.updateById(shop);
                    }
                }
                else {
                    adaAuditing(cmdShopStatusReq, adaAuditDTO);
                }
            });
        }
        catch (Exception e) {
            log.error("【mafka消费】【汇付审核结果】处理异常: {}", body, e);
            throw e;
        }
        log.info("【mafka消费】【汇付审核结果】处理完成消息内容为: {}", body);
    }

    void adaAuditing(CmdShopStatusReq cmdShopStatusReq, AdaAuditDTO adaAuditDTO) {
        cmdShopStatusReq.setSettleAccountId(adaAuditDTO.getSettleAccountId());
        if (CommonConstant.AUDIT_STATE_SUCCESS.equals(adaAuditDTO.getAuditState())) {
            //审核通过
            cmdShopStatusReq.setStatus(ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode());
            shopService.adaAuditing(cmdShopStatusReq);
        }
        else if (CommonConstant.AUDIT_STATE_CREATE_SUCCESS.equals(adaAuditDTO.getAuditState())) {
            //审核通过
            cmdShopStatusReq.setStatus(ShopEnum.AdapayAuditStatus.AdapayFailed.getCode());
            cmdShopStatusReq.setRefuseReason(CommonConstant.HFP_SETTLE_ACCOUNT_CREATE_FAIL);
            shopService.adaAuditing(cmdShopStatusReq);
        }
        else {
            //审核失败
            cmdShopStatusReq.setStatus(ShopEnum.AdapayAuditStatus.AdapayFailed.getCode());
            cmdShopStatusReq.setRefuseReason(adaAuditDTO.getAuditDesc());
            shopService.adaAuditing(cmdShopStatusReq);
        }
    }


    void updateAdaAuditing(CmdShopStatusReq cmdShopStatusReq, AdaAuditDTO adaAuditDTO) {
        cmdShopStatusReq.setSettleAccountId(adaAuditDTO.getSettleAccountId());
        if (CommonConstant.UPDATE_STATE_SUCCESS.equals(adaAuditDTO.getAuditState())) {
            //审核通过
            cmdShopStatusReq.setStatus(ShopEnum.AdapayAuditStatus.AdapaySuccess.getCode());
            shopService.adaAuditing(cmdShopStatusReq);
        }
        else {
            //审核失败
            cmdShopStatusReq.setStatus(ShopEnum.AdapayAuditStatus.AdapayFailed.getCode());
            cmdShopStatusReq.setRefuseReason(adaAuditDTO.getAuditDesc());
            shopService.adaAuditing(cmdShopStatusReq);
        }
    }

}
