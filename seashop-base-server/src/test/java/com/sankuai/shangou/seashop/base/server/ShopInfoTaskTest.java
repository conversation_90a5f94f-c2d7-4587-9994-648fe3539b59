package com.sankuai.shangou.seashop.base.server;

import com.sankuai.shangou.seashop.base.BaseApplication;
import com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/11/29 15:52
 */
@SpringBootTest(classes = BaseApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Slf4j
public class ShopInfoTaskTest {

    @Resource
    private ShopInfoTask shopInfoTask;

    @Test
    public void checkShopInfo() {
        shopInfoTask.checkShopInfo("242");
    }
}
