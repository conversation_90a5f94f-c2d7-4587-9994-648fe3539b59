//package com.sankuai.shangou.seashop.base.server;
//
//import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
//import com.sankuai.shangou.seashop.base.core.service.SendMessageRecordService;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
///**
// * @description:
// * @author: LXH
// **/
//@SpringBootTest
//@RunWith(SpringRunner.class)
//@Slf4j
//public class MessageRecordTest {
//    @Resource
//    private SendMessageRecordService sendMessageRecordService;
//
//    @Test
//    public void testQueryDetail() {
//        log.info("{}", sendMessageRecordService.queryDetail(new BaseIdReq(){{setId(45L);}}));
//
//    }
//}
