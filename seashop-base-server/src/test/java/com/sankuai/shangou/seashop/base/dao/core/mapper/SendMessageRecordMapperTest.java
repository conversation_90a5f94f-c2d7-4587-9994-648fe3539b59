package com.sankuai.shangou.seashop.base.dao.core.mapper;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.sankuai.shangou.seashop.base.StartAppTest;
import com.sankuai.shangou.seashop.base.dao.core.domain.SendMessageRecord;

import cn.hutool.core.date.DateUtil;

/**
 * <AUTHOR>
 */
class SendMessageRecordMapperTest extends StartAppTest {

    @Resource
    private SendMessageRecordMapper sendMessageRecordMapper;

    @Test
    void insert() {
        SendMessageRecord sendMessageRecord = new SendMessageRecord();
        sendMessageRecord.setId(111111L);
        sendMessageRecord.setContentType(1);
        sendMessageRecord.setSendContent("test");
        sendMessageRecord.setSendState(1);
        sendMessageRecord.setToUserLabel("test");
        sendMessageRecord.setMessageType(1);
        sendMessageRecord.setSendTime(DateUtil.date());
        sendMessageRecordMapper.insert(sendMessageRecord);
        sendMessageRecordMapper.deleteById(111111L);
    }

}
