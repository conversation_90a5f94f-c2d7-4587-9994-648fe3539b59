package com.sankuai.shangou.seashop.base.server;

import com.sankuai.shangou.seashop.base.BaseApplication;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.shop.service.EsShopBuildService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/30 16:50
 */
@SpringBootTest(classes = BaseApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Slf4j
public class ShopEsTest {

    @Resource
    private EsShopBuildService esShopBuildService;
    @Resource
    private ShopRepository shopRepository;

    @Test
    public void test() {
        List<Shop> shopList = shopRepository.list();
        shopList.forEach(shop -> {
            esShopBuildService.refreshShopEs(shop.getId());
        });
    }


}
