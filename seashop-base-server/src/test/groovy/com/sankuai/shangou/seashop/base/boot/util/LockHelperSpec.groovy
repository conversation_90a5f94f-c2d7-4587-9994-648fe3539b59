package com.sankuai.shangou.seashop.base.boot.util


import com.sankuai.shangou.seashop.base.utils.LockHelper
import com.sankuai.shangou.seashop.base.utils.VoidSupplier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Subject

import java.util.function.Supplier
/**
 * <AUTHOR>
 * @date 2024/03/02 16:17
 */

@SpringBootTest
@ContextConfiguration(classes = com.sankuai.shangou.seashop.base.StartApp.class)
@Subject(LockHelper)
class LockHelperSpec extends Specification {


    def "lock should execute the supplier within the lock"() {
        given:
        def supplierExecuted = false

        when:
        LockHelper.lock("testLock", {
            supplierExecuted = true
        })

        then:
        supplierExecuted == true
    }

    def "lock with supplier should return the result of the supplier"() {
        given:
        def expectedResult = "Result of the supplier"

        when:
        def result = LockHelper.lock("testLock", 5000, new Supplier<String>() {
            @Override
            String get() {
                return expectedResult
            }
        })

        then:
        result == expectedResult
    }

    def "lock with multiple lock names should execute the supplier within the lock"() {
        given:
        def supplierExecuted = false
        String[] locks = ["testLock1", "testLock2"]

        when:
        LockHelper.lock(locks, new VoidSupplier() {
            @Override
            void apply() {
                supplierExecuted = true
            }
        })

        then:
        supplierExecuted
    }

    def "lock with multiple lock names should throw BusinessException when failing to acquire lock within default time"() {
        given:
        def supplierExecuted = false
        String[] locks = ["testLock1", "testLock2"]

        when:
        LockHelper.lock(locks, 5000, new VoidSupplier() {
            @Override
            void apply() {
                supplierExecuted = true
            }
        })

        then:
        supplierExecuted
    }
}
