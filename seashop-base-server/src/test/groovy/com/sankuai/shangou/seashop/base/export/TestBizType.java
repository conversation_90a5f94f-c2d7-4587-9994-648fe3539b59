package com.sankuai.shangou.seashop.base.export;

import com.sankuai.shangou.seashop.base.export.enums.TaskType;

/**
 * <AUTHOR>
 */
public enum TestBizType implements TaskType {

    TEST_BIZ_TYPE(1, "测试业务类型")
    ;

    private final Integer type;
    private final String name;

    TestBizType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    @Override
    public Integer getType() {
        return null;
    }

    @Override
    public String getName() {
        return null;
    }
}
