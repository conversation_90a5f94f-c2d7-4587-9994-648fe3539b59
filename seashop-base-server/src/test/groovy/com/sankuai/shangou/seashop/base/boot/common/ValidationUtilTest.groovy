package com.sankuai.shangou.seashop.base.boot.common

import com.sankuai.shangou.seashop.base.boot.utils.ValidationUtil
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

import java.util.regex.Pattern

@Title("基础校验工具类测试")
@Subject(ValidationUtil)
class ValidationUtilTest extends Specification {

    def "校验手机号, 手机号:#phone, 结果:#result"() {
        expect:
        result == ValidationUtil.isPhone(phone)

        where:
        phone || result
        "12345678901" || true
        "1234567890" || false
    }

    def "校验是否为正整数, 输入: #num, 结果: #result"() {
        expect:
        result == ValidationUtil.isPositiveInteger(num)

        where:
        num || result
        "12" || true
        "0" || false
        "-23" || false
        "hello" || false
    }

    def "校验是否为非负整数, 输入: #num, 结果: #result"() {
        expect:
        result == ValidationUtil.isNonNegativeInteger(num)

        where:
        num || result
        "12" || true
        "0" || true
        "-23" || false
        "hello" || false
    }

    def "校验是否为金额, 输入: #num, 结果: #result"() {
        expect:
        result == ValidationUtil.isAmount(num)

        where:
        num || result
        "12" || true
        "0" || false
        "-23" || false
        "hello" || false
    }

    def "是否匹配正则表达式, 格式: #regex, 匹配串: #Str"() {
        expect:
        result == ValidationUtil.isMatchRegex(Pattern.compile(regex), str)

        where:
        regex || str || result
        "^[0-9]*\$" || "123" || true
        "^[0-9]*\$" || "123a" || false
    }


}
