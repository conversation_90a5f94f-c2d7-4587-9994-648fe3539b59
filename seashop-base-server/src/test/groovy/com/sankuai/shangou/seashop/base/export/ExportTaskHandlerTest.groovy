package com.sankuai.shangou.seashop.base.export


import com.sankuai.shangou.seashop.base.export.config.ExportTaskProps
import com.sankuai.shangou.seashop.base.export.getter.TestBaseExportGetter
import com.sankuai.shangou.seashop.base.export.handler.ExecuteResult
import com.sankuai.shangou.seashop.base.export.handler.ExportDataGetter
import com.sankuai.shangou.seashop.base.export.handler.ExportTask
import com.sankuai.shangou.seashop.base.export.handler.ExportTaskHandler
import com.sankuai.shangou.seashop.base.export.output.UploadOutputWay
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

@Title("导出任务处理器测试")
@Subject(ExportTaskHandler)
class ExportTaskHandlerTest extends Specification {

    def exportTaskProps = Mock(ExportTaskProps)
    def outputWay = new UploadOutputWay()
    def taskHandler = new ExportTaskHandler(outputWay)
    def task

    void setup() {

        task = new ExportTask()
        task.setTaskId(UUID.randomUUID().toString())
        task.setTaskType(TestBizType.TEST_BIZ_TYPE.getType())
        task.setTaskDate(new Date())
        task.setExecuteParam("{}")
    }

    def "直接执行导出方法并上传到文件服务器"() {

        given:
        ExportDataGetter dataGetter = new TestBaseExportGetter();

        and:
        exportTaskProps.getNfsPathPrefix() >> "/export/excel"

        when:
        ExecuteResult result = taskHandler.executeForDataGetter(dataGetter, task)

        then:
        result.success
    }
}
