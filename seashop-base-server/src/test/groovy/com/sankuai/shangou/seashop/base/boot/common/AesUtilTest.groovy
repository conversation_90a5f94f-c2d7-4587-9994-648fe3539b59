package com.sankuai.shangou.seashop.base.boot.common

import com.sankuai.shangou.seashop.base.boot.utils.AesUtil
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

@Title("AES加解密工具类测试")
@Subject(AesUtil)
class AesUtilTest extends Specification {

    def decryptKey = "ae125efkk4454eeff444ferfkny6oxi8"
    def ciphertext = "NI/z9VLrtd0IOZY1xCvqTg=="
    def plaintext = "张三"

    def "加密"() {
        when:
        def currentCipherTest = AesUtil.encrypt(plaintext, decryptKey)

        then:
        ciphertext == currentCipherTest
    }

    def "解密"() {
        when:
        def currentPlainTest = AesUtil.decrypt(ciphertext, decryptKey)

        then:
        plaintext == currentPlainTest
    }
}
