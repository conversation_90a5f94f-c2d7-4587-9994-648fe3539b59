package com.sankuai.shangou.seashop.base.boot.util

import com.sankuai.shangou.seashop.base.InterfaceImpl
import com.sankuai.shangou.seashop.base.ShareObj
import com.sankuai.shangou.seashop.base.utils.ReflectUtil
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

@Title("反射工具类测试")
@Subject(ReflectUtil)
class ReflectUtilTest extends Specification {

    def "获取接口的泛型类型"() {
        given:
        def impl = new InterfaceImpl();

        when:
        Class clz = ReflectUtil.getInterfaceGenericClass(impl, 0, 0)

        then:
        clz == ShareObj.class
    }
}
