package com.sankuai.shangou.seashop.base.export

import com.sankuai.shangou.seashop.base.export.config.ExportAutoConfiguration
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

@Title("导出测试")
@Subject(ExportAutoConfiguration)
class ExportAutoConfigurationTest extends Specification {

    /*def "测试 fileOutputWay 方法"() {
        given: "一个配置类实例"
        def config = new ExportAutoConfiguration()

        and: "一个mock的ExportTaskProps对象"
        def exportTaskProps = Mock(ExportTaskProps)

        and: "模拟环境属性，使得exportTaskHandler被创建"
        EnvironmentTestUtils.addEnvironment(mockEnvironment, "task.export.enable=true")

        when: "调用fileOutputWay方法"
        def fileOutputWay = config.fileOutputWay()

        then: "返回一个新的UploadOutputWay实例"
        fileOutputWay instanceof UploadOutputWay

        and: "当exportTaskHandler被调用时，使用mock的ExportTaskProps"
        def exportTaskHandler = config.exportTaskHandler(exportTaskProps)

        then: "exportTaskHandler使用mock的FileWriteAndOutputWay实例"
        exportTaskHandler.fileWriteAndOutputWay == fileOutputWay
    }*/
}
