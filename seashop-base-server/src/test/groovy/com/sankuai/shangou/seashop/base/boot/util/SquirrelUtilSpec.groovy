package com.sankuai.shangou.seashop.base.boot.util


import com.sankuai.shangou.seashop.base.utils.SquirrelUtil
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Subject

import javax.annotation.Resource
/**
 * <AUTHOR>
 * @date 2024/03/02 14:58
 */
@SpringBootTest
@ContextConfiguration(classes = com.sankuai.shangou.seashop.base.StartApp.class)
@Subject(SquirrelUtil)
class SquirrelUtilSpec extends Specification {

    @Resource
    SquirrelUtil squirrelUtil

    def "setting and getting a non-expiring cache should work"() {
        given:
        def key = "testKey"
        def value = "testValue"

        when:
        squirrelUtil.set(key, value)
        def result = squirrelUtil.get(key)

        then:
        result == value
    }

    def "setting and getting an expiring cache should work"() {
        given:
        def key = "testKey"
        def value = "testValue"
        def expireInSeconds = 5

        when:
        squirrelUtil.set(key, value, expireInSeconds)
        sleep(expireInSeconds * 1000) // 等待缓存过期
        def result = squirrelUtil.get(key)

        then:
        result == null
    }

    def "deleting a key should work"() {
        given:
        def key = "testKey"
        def value = "testValue"

        when:
        squirrelUtil.set(key, value)
        squirrelUtil.deleteKey(key)
        def result = squirrelUtil.get(key)

        then:
        result == null
    }

    def "setting and getting an expiring cache with setnx should work"() {
        given:
        def key = "testKey"
        def value = "testValue"
        def expireInSeconds = 5

        when:
        def success = squirrelUtil.setnx(key, value, expireInSeconds)
        sleep(expireInSeconds * 1000) // 等待缓存过期
        def result = squirrelUtil.get(key)

        then:
        success == true
        result == null
    }

    def "incrementing a key should work"() {
        given:
        def key = "testKey"

        when:
        def success = squirrelUtil.incr(key)

        then:
        success == true
    }

    def "comparing and deleting a key should work"() {
        given:
        def key = "testKey"
        def value = "testValue"

        when:
        squirrelUtil.set(key, value)
        def success = squirrelUtil.compareAndDelete(key, value)

        then:
        success == true
        squirrelUtil.get(key) == null
    }
}
