package com.sankuai.shangou.seashop.base.boot.common

import com.sankuai.shangou.seashop.base.boot.utils.DateUtil
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

import java.time.LocalDateTime

@Title("日期工具类测试")
@Subject(DateUtil)
class DateUtilTest extends Specification {

    def dateStr = "2021-08-01 12:00:00"
    def dateLong = 1627790400000L
    def date = new Date(dateLong)
    def localDateTime = LocalDateTime.of(2021, 8, 1, 12, 0, 0)

    def "默认格式化成yyyy-MM-dd HH:mm:ss"() {
        when:
        def formatDateStr = DateUtil.format(date)

        then:
        formatDateStr == dateStr
    }

    def "指定格式格式化"() {
        when:
        def formatDateStr = DateUtil.format(date, DateUtil.DEFAULT_PATTERN)

        then:
        formatDateStr == dateStr
    }

    def "格式化成yyyyMMddHH"() {
        when:
        def formatDateStr = DateUtil.formatHour(date)

        then:
        formatDateStr == "2021080112"
    }

    def "date转换成LocalDateTime"() {
        when:
        def formatDateStr = DateUtil.convertDate(date)

        then:
        formatDateStr == localDateTime
    }
}
