package com.sankuai.shangou.seashop.base.boot.common

import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

@Title("数字工具类测试")
@Subject(NumberUtil)
class NumberUtilTest extends Specification {

    def ba = new BigDecimal("1.2")
    def bb = new BigDecimal("2")
    def ic = 3
    def ld = 4L
    def mulab = new BigDecimal("2.4")
    def mulac = new BigDecimal("3.6")
    def mulad = new BigDecimal("4.8")

    def "BigDecimal与BigDecimal相乘"() {
        when:
        def result = NumberUtil.multiply(ba, bb)

        then:
        mulab == result
    }

    def "BigDecimal与int相乘"() {
        when:
        def result = NumberUtil.multiply(ba, ic)

        then:
        mulac == result
    }

    def "BigDecimal与long相乘"() {
        when:
        def result = NumberUtil.multiply(ba, ld)

        then:
        mulad == result
    }

    def "如果为null返回默认值0"() {
        when:
        def result = NumberUtil.nullToZero(null)

        then:
        result == BigDecimal.ZERO
    }
}
