package com.sankuai.shangou.seashop.base.boot.common


import spock.lang.Narrative
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

import java.util.function.BiConsumer
import java.util.function.Consumer
import java.util.function.Function

@Title("批次查询工具测试类") // 测试的标题
@Narrative("""
    1. 批次查询工具测试类
    2. 批次查询工具测试类
""") // 可选：测试的描述，可以是多行文本
@Subject(BatchQueryHelper) // 可选：测试的主题，用于指定被测试的类是哪一个
class BatchQueryHelperTest extends Specification {

    BatchQueryHelper.TestParam testParam
    def setParamConsumer
    def condFunction
    def queryFunction
    def invokeConsumer
    def idList

    def setupSpec() {
        //TODO: 设置每个测试类的环境
    }

    // 设置每个测试方法的环境，每个测试方法执行一次
    void setup() {
        idList = Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L)
        testParam = new BatchQueryHelper.TestParam();
        testParam.name = "hello"
        testParam.idList = idList

        setParamConsumer = { param, idList -> param.setIdList(idList) } as BiConsumer<BatchQueryHelper.TestParam, List<Long>>;
        condFunction = { param -> param.getIdList() } as Function<BatchQueryHelper.TestParam, List<Long>>;
        queryFunction = { param -> BatchQueryHelper.printMethod(param) } as Function<BatchQueryHelper.TestParam, List<String>>;
        invokeConsumer = { idList -> BatchQueryHelper.testInvoke(idList) } as Consumer<List<Long>>;
    }

    def "使用默认批次执行批次调用"() {

        when:
        List<String> resultList = BatchQueryHelper.batchQuery(testParam, setParamConsumer, condFunction, queryFunction)

        then:
        resultList.size() == idList.size()
    }

    def "使用自定义批次执行批次调用"() {
        when:
        List<String> resultList = BatchQueryHelper.batchQuery(testParam, 2, setParamConsumer, condFunction, queryFunction)

        then:
        resultList.size() == idList.size()
    }

    def "简单的批次调用接口"() {

        when:
        BatchQueryHelper.batchInvoke(testParam.getIdList(), 2, invokeConsumer)

        // 测试方法为void，且是批次调用consumer方法，没有能验证的数据，能执行即可
        then:
        assert true
    }
}
