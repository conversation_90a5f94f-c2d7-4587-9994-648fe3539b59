package com.sankuai.shangou.seashop.base.boot.response

import com.sankuai.shangou.seashop.base.ShareObj
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

import java.util.function.Function

@Title("thrift工具类测试")
@Subject(ThriftResponseHelper)
class ThriftResponseHelperTest extends Specification {

    def "responseInvoke"() {
        given:
        def req = new ShareObj("hello")
        def respFunction = { param -> param.getName() + " world"  } as Function<ShareObj, ShareObj>;

        when:
        def resp = ThriftResponseHelper.responseInvoke("test", req, respFunction)

        then:
        with(resp) {
            resp.getCode() == 0
            resp.getData() != null
        }
    }

    def "executeThriftCall"() {
        given:
        def resp = new ShareObj("hello")
        def supp = { ResultDto.newWithData(resp) } as ThrowingSupplier<ResultDto<ShareObj>>;

        when:
        def obj = ThriftResponseHelper.executeThriftCall(supp)

        then:
        obj.getName() == "hello"
    }

}
