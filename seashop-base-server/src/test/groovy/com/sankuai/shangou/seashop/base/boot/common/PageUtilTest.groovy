package com.sankuai.shangou.seashop.base.boot.common

import com.sankuai.shangou.seashop.base.boot.utils.PageUtil
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

@Title("分页页码工具类测试")
@Subject(PageUtil)
class PageUtilTest extends Specification {

    def pageNo1 = 1;
    def pageSize = 8;
    def pageNo2 = 2;
    def totalCountInt = 20;
    def totalCountLong = 20L;
    def totalPage = 3;

    def "根据页码和每页大小计算当前页开始索引位置"() {
        when:
        def startIndex1 = PageUtil.getStart(pageNo2, pageSize)

        then:
        pageSize == startIndex1
    }

    def "根据页码和每页大小计算当前页结束索引位置"() {
        when:
        def endIndex1 = PageUtil.getEnd(pageNo1, pageSize)

        then:
        pageSize == endIndex1
    }

    def "获取总页数int"() {
        when:
        def totalPage1 = PageUtil.totalPage(totalCountInt, pageSize)

        then:
        totalPage == totalPage1
    }

    def "获取总页数long"() {
        when:
        def totalPage1 = PageUtil.totalPage(totalCountLong, pageSize)

        then:
        totalPage == totalPage1
    }
}
