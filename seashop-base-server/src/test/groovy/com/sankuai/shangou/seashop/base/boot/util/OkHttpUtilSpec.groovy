import com.sankuai.shangou.seashop.base.utils.OkHttpUtil
import okhttp3.Request
import spock.lang.Specification
import spock.lang.Subject

/**
 * <AUTHOR>
 * @date 2024/3/11 17:00
 * @version 1.0
 */
@Subject(OkHttpUtil)
class OkHttpUtilSpec  extends Specification {

    def "从OkHttpUtil里面取出Okhttp实例"() {
        given:
        def client = OkHttpUtil.getInstance()
        def request = new Request.Builder().url("https://www.baidu.com").build()

        when:
        def response = client.newCall(request).execute()

        then:
        response.code() == 200

        // 关闭响应
        response.close()
    }

}