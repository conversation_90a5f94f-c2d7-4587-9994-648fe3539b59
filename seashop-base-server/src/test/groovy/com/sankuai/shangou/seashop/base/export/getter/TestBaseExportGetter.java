package com.sankuai.shangou.seashop.base.export.getter;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.TestBizType;
import com.sankuai.shangou.seashop.base.export.handler.AbstractBaseDataGetter;
import com.sankuai.shangou.seashop.base.export.handler.SingleWrapperDataGetter;
import com.sankuai.shangou.seashop.base.export.model.BaseExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.DataContext;
import com.sankuai.shangou.seashop.base.export.model.XOrderProductExcelVO;
import com.sankuai.shangou.seashop.base.export.wrapper.OrderProductDataWrapper;

import java.util.Collections;

/**
 * <AUTHOR>
 */
public class TestBaseExportGetter extends AbstractBaseDataGetter<BasePageReq>
        implements SingleWrapperDataGetter<BasePageReq> {


    private XOrderProductExcelVO repository = new XOrderProductExcelVO();

    @Override
    public Integer getModule() {
        return TestBizType.TEST_BIZ_TYPE.getType();
    }

    @Override
    public String getFileName() {
        return TestBizType.TEST_BIZ_TYPE.getName();
    }

    @Override
    public DataContext selectData(BasePageReq param) {
        DataContext context = new DataContext();
        BaseExportWrapper<XOrderProductExcelVO, BasePageReq> wrapper1 = new OrderProductDataWrapper(repository);

        context.setSheetDataList(Collections.singletonList(wrapper1));
        return context;
    }
}
