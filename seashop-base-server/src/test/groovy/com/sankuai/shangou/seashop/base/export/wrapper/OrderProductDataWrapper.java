package com.sankuai.shangou.seashop.base.export.wrapper;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.write.handler.WriteHandler;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.export.model.PageExportWrapper;
import com.sankuai.shangou.seashop.base.export.model.XOrderProductExcelVO;
import com.sankuai.shangou.seashop.base.export.writeHandler.ValueFormatWriteHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Slf4j
public class OrderProductDataWrapper extends PageExportWrapper<XOrderProductExcelVO, BasePageReq> {

    private XOrderProductExcelVO repository;
    private List<XOrderProductExcelVO> dataList;

    public OrderProductDataWrapper(XOrderProductExcelVO repository) {
        this.repository = repository;
    }

    public OrderProductDataWrapper(List<XOrderProductExcelVO> dataList) {
        this.dataList = dataList;
    }

    @Override
    public List<XOrderProductExcelVO> getPageList(BasePageReq param) {
        int from = (param.getPageNo() - 1) * param.getPageSize();
        int to = param.getPageSize() * param.getPageNo();
        log.info("【数据导出】数组取值, 当前页码={}, 当前每页数据大小={}", from, to);
        List<XOrderProductExcelVO> pageList = repository.pageList(from, to);
        if (CollUtil.isEmpty(dataList)) {
            dataList = pageList;
        } else {
            dataList.addAll(pageList);
        }
        return pageList;
    }

    public List<XOrderProductExcelVO> getExistsList() {
        return dataList;
    }

    @Override
    public List<WriteHandler> getWriteHandlerList() {
        return Collections.singletonList(new ValueFormatWriteHandler(XOrderProductExcelVO.class, null));
    }
}
