package com.sankuai.shangou.seashop.base.boot.common

import com.fasterxml.jackson.core.type.TypeReference
import com.sankuai.shangou.seashop.base.GenericObj
import com.sankuai.shangou.seashop.base.ShareObj
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

import java.util.function.BiConsumer

@Title("JSON工具类测试")
@Subject(JsonUtil)
class JsonUtilTest extends Specification {

    def name = "张三"
    def shareObjStr = "{\"name\":\"张三\"}"
    def addNodeObjStr = "{\"name\":\"张三\",\"age\":18}"
    def shareObjListStr = "[{\"name\":\"张三\"}]"
    def shareObj = new ShareObj(name: name)
    def nestObjStr = "{\"value\":\"world\",\"shareObj\":{\"name\":\"张三\"}}"
    def genericObjStr = "{\"data\":{\"name\":\"张三\"}}"

    def "GetObjectMapper"() {

        expect:
        JsonUtil.getObjectMapper() != null
    }

    def "ToJsonString"() {
        expect:
        shareObjStr == JsonUtil.toJsonString(shareObj)
    }

    def "ToJsonStringWithMapper"() {

        given:
        def mapper = JsonUtil.getObjectMapper()

        expect:
        shareObjStr == JsonUtil.toJsonString(shareObj, mapper)
    }

    def "ParseStringToObject"() {

        when:
        def shareObj = JsonUtil.parseObject(shareObjStr, ShareObj.class)

        then:
        name == shareObj.getName()
    }

    def "ParseInputStreamToObject"() {
        when:
        def is = new ByteArrayInputStream(shareObjStr.getBytes("UTF-8"))

        and:
        def shareObj = JsonUtil.parseObject(is, ShareObj.class)

        then:
        name == shareObj.getName()

    }

    def "ParseStringToObjectWithMapper"() {
        when:
        def mapper = JsonUtil.getObjectMapper()

        and:
        def shareObj = JsonUtil.parseObject(shareObjStr, ShareObj.class, mapper)

        then:
        name == shareObj.getName()
    }

    def "GetAsString"() {
        when:
        def value = JsonUtil.getAsString(shareObjStr, "name")

        then:
        name == value
    }

    def "GetAsObject"() {
        when:
        def obj = JsonUtil.getAsObject(nestObjStr, "shareObj", ShareObj.class)

        then:
        name == obj.getName()
    }

    def "ParseObjectWithTypeReference"() {
        when:
        def current = JsonUtil.parseObject(genericObjStr, new TypeReference<GenericObj<ShareObj>>() {})

        then:
        current.getData().getName() == name
    }

    def "TestParseObject3"() {
        when:
        def typeReference = new TypeReference<GenericObj<ShareObj>>() {}
        def type = typeReference.getType()

        and:
        def current = JsonUtil.parseObject(genericObjStr, type)

        then:
        current.getData().getName() == name
    }

    def "ParseArray"() {
        when:
        def list = JsonUtil.parseArray(shareObjListStr, ShareObj.class)

        then:
        name == list.get(0).getName()

    }

    def "ParseInputStreamToArray"() {
        when:
        def is = new ByteArrayInputStream(shareObjListStr.getBytes("UTF-8"))

        and:
        def list = JsonUtil.parseArray(is, ShareObj.class)

        then:
        name == list.get(0).getName()
    }

    def "BeanToMap"() {
        when:
        def map = JsonUtil.beanToMap(shareObj)

        then:
        name == map.get("name")
    }

    def "GetAsJsonObject"() {
        when:
        def node = JsonUtil.getAsJsonObject(nestObjStr, "shareObj")

        then:
        node.get("name").asText() == name
    }

    def "AddNode"() {
        when:
        def newStr = JsonUtil.add(shareObjStr, "age", 18)

        then:
        newStr == addNodeObjStr
    }

    def "Format"() {
        when:
        def newStr = JsonUtil.format(addNodeObjStr)

        then:
        println newStr
        assert true
    }

    def "IsJson"() {
        expect:
        JsonUtil.isJson(shareObjStr)
    }

    def "Copy"() {
        when:
        def newObj = JsonUtil.copy(shareObj, ShareObj.class)

        then:
        name == newObj.getName()
    }

    def "CopyWithTypeReference"() {
        when:
        def genericObj = JsonUtil.parseObject(genericObjStr, new TypeReference<GenericObj<ShareObj>>() {})
        def current = JsonUtil.copy(genericObj, new TypeReference<GenericObj<ShareObj>>() {})

        then:
        current.getData().getName() == name
    }

    def "CopyWithIgnoreProperties"() {
        when:
        def newObj = JsonUtil.copy(shareObj, ShareObj.class, "name")

        then:
        assert newObj.getName() == null
    }

    def "CopyList"() {
        when:
        def list = [shareObj]

        and:
        def newList = JsonUtil.copyList(list, ShareObj.class)

        then:
        name == newList.get(0).getName()
    }

    def "CopyListWithConsumer"() {
        when:
        def list = [shareObj]

        and:
        def consumer = { oldV, newV -> newV.setName("李四") } as BiConsumer<ShareObj, ShareObj>

        and:
        def newList = JsonUtil.copyList(list, ShareObj.class, consumer)

        then:
        "李四" == newList.get(0).getName()
    }
}
