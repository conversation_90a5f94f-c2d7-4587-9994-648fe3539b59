package com.sankuai.shangou.seashop.base.boot.util


import com.sankuai.shangou.seashop.base.utils.TransactionHelper
import com.sankuai.shangou.seashop.base.utils.VoidSupplier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Subject

import java.util.function.Supplier
/**
 * <AUTHOR>
 * @date 2024/03/02 15:05
 */
@SpringBootTest
@ContextConfiguration(classes = com.sankuai.shangou.seashop.base.StartApp.class)
@Subject(TransactionHelper)
class TransactionHelperSpec extends Specification {

    def "doInTransaction with VoidSupplier should commit successfully"() {
        given:
        VoidSupplier supplier = { println "Inside transaction" }

        when:
        TransactionHelper.doInTransaction(supplier)

        then:
        1 == 1
    }

    def "doInTransaction with VoidSupplier should rollback on exception"() {
        given:
        VoidSupplier supplier = { throw new RuntimeException("Simulating exception") }

        when:
        def exceptionThrown = false
        try {
            TransactionHelper.doInTransaction(supplier)
        } catch (Exception e) {
            exceptionThrown = true
        }

        then:
        exceptionThrown
    }

    def "doInTransaction with Supplier should commit successfully"() {
        given:
        Supplier supplier = { "Result" }

        when:
        def result = TransactionHelper.doInTransaction(supplier)

        then:
        result == "Result"
    }

    def "doInTransaction with Supplier should rollback on exception"() {
        given:
        Supplier supplier = { throw new RuntimeException("Simulating exception") }

        when:
        def exceptionThrown = false
        try {
            TransactionHelper.doInTransaction(supplier)
        } catch (Exception e) {
            exceptionThrown = true
        }

        then:
        exceptionThrown
    }
}
