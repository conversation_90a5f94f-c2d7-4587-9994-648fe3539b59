package com.sankuai.shangou.seashop.base.dao.core.mapper

import cn.hutool.core.date.DateUtil
import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest
import com.sankuai.shangou.seashop.base.dao.core.domain.SendMessageRecord
import org.springframework.test.annotation.Rollback
import org.springframework.test.context.ActiveProfiles
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

import javax.annotation.Resource

/**
 *
 *
 * <AUTHOR>
 */
@Title("SendMessageRecordMapper测试")
@Subject(SendMessageRecordMapper.class)

@ActiveProfiles("unittest")
@MybatisPlusTest
@Rollback(false)
class SendMessageRecordMapperSpec extends Specification {

    void setup() {
    }

    void cleanup(){
    }

    @Resource
    private SendMessageRecordMapper sendMessageRecordMapper;

    def "insert and delete"() {
        expect:
        SendMessageRecord sendMessageRecord = new SendMessageRecord();
        sendMessageRecord.setId(111111L);
        sendMessageRecord.setContentType(1);
        sendMessageRecord.setSendContent("test");
        sendMessageRecord.setSendState(1);
        sendMessageRecord.setToUserLabel("test");
        sendMessageRecord.setMessageType(1);
        sendMessageRecord.setSendTime(DateUtil.date());
        sendMessageRecordMapper.insert(sendMessageRecord)>=1;
        sendMessageRecordMapper.deleteById(111111L)>=1;
    }
}
