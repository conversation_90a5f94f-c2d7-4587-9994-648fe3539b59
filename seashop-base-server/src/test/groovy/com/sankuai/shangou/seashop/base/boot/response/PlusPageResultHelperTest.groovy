package com.sankuai.shangou.seashop.base.boot.response

import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.sankuai.shangou.seashop.base.ShareObj
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Title

import java.util.function.BiConsumer
import java.util.function.Consumer
import java.util.function.Function

@Title("MybatisPlus分页工具类测试")
@Subject(PlusPageResultHelper)
class PlusPageResultHelperTest extends Specification {

    def req = new BasePageReq();

    def "defaultEmpty"() {
        when:
        def resp = PlusPageResultHelper.defaultEmpty(req)

        then:
        resp.getPageSize() == 10
    }

    def "transfer"() {
        given:
        Page<ShareObj> page = new Page<>(req.getPageNo(), req.getPageSize())

        when:
        def resp = PlusPageResultHelper.transfer(page, ShareObj.class)

        then:
        resp.getPageSize() == req.getPageSize()
    }

    def "transferWithConsumer"() {
        given:
        Page<ShareObj> page = new Page<>(req.getPageNo(), req.getPageSize())
        def con = {it -> it.getName() + "1"} as Consumer<ShareObj>

        when:
        def resp = PlusPageResultHelper.transfer(page, ShareObj.class, con)

        then:
        resp.getPageSize() == req.getPageSize()
    }

    def "transferWithBiConsumer"() {
        given:
        Page<ShareObj> page = new Page<>(req.getPageNo(), req.getPageSize())
        def con = {from, to -> to.setName(from.getName() + " world")} as BiConsumer<ShareObj, ShareObj>

        when:
        def resp = PlusPageResultHelper.transfer(page, ShareObj.class, con)

        then:
        resp.getPageSize() == req.getPageSize()
    }

    def "transferWithFunction"() {
        given:
        Page<ShareObj> page = new Page<>(req.getPageNo(), req.getPageSize())
        def func = {from -> from.getName() + " world"} as Function<ShareObj, String>

        when:
        def resp = PlusPageResultHelper.transfer(page, func)

        then:
        resp.getPageSize() == req.getPageSize()
    }

}
