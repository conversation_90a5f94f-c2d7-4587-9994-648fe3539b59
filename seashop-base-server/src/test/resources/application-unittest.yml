spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=FALSE;DB_CLOSE_ON_EXIT=FALSE;MODE=MYSQL
    username: sa
    password:
  sql:
    init:
      mode: always
      schema-locations:
        - classpath:db/schema.sql
      data-locations:
        - classpath:db/data.sql
      encoding: UTF-8


mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sankuai.shangou.seashop.base.dao
  global-config:
    db-config:
      id-type: auto
      #驼峰下划线转换
      table-underline: true
    banner: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    lazy-loading-enabled: true
    #    default-statement-timeout: 3
    use-generated-keys: true

#缓存
squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_qa
venus:
  hostName: http://p0.inf.test.sankuai.com/seashopimagetest/
  bucket: seashopimagetest
  clientId: 8scckx46q4cjn4kq000000000054eb54
s3plus:
  enabled: false
  hostName: http://msstest.vip.sankuai.com/seashop-test/
  bucketName: seashop-test
#邮件
email:
  channelId: 514
#微信小程序第三方接口
wechat:
  getTokenUrl: https://api.weixin.qq.com/cgi-bin/token
  addTemplate: https://api.weixin.qq.com/wxaapi/newtmpl/addtemplate
  removeTemplate: https://api.weixin.qq.com/wxaapi/newtmpl/deltemplate
  sendSubscribeMsg: https://api.weixin.qq.com/cgi-bin/message/subscribe/send
  tokenExpireTime: 7000
express:
  bizType: 47

#rocketmq:
#  name-server: **************:9876
#  producer:
#    group: seashop_operation_log_consumer

logging:
  level:
    org.apache.rocketmq: debug
    com.sankuai.shangou.seashop.base: debug
    com.baomidou.mybatisplus: debug