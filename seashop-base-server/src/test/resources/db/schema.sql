drop table if exists base_send_message_record;

CREATE TABLE `base_send_message_record`
(
    `id`            BIGINT(20) NOT NULL AUTO_INCREMENT,
    `message_type`  INT(11) NOT NULL COMMENT '消息类别',
    `content_type`  INT(11) NOT NULL COMMENT '内容类型',
    `send_content`  VARCHAR(600) NOT NULL COMMENT '发送内容' ,
    `to_user_label` VARCHAR(200) NULL DEFAULT NULL COMMENT '发送对象' ,
    `send_state`    INT(11) NOT NULL COMMENT '发送状态',
    `send_time`     DATETIME     NOT NULL COMMENT '发送时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARSET=utf8mb4 COMMENT='发送消息记录表';
