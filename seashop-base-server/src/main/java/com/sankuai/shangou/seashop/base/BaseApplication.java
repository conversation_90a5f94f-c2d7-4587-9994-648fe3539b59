package com.sankuai.shangou.seashop.base;


import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;


//@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@SpringBootApplication(scanBasePackages = "com.sankuai.shangou.seashop")
@MapperScan(basePackages = {"com.sankuai.shangou.seashop.**.mapper"})
@EnableTransactionManagement
@EnableAsync
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {
        "com.sankuai.shangou.seashop",
        "com.hishop.himall.report.api.service"
})
@EnableScheduling
public class BaseApplication {
    private static final Logger log = LoggerFactory.getLogger(BaseApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(BaseApplication.class, args);
        log.info("服务启动成功！");
    }
}
