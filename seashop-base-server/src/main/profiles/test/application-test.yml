#日志配置
logging:
  level.com.sankuai.shangou.seashop.user.account.mapper: debug # 打印mybatis的sql日志
  level.com.sankuai.shangou.seashop.user.shop.mapper: debug # 打印mybatis的sql日志

zebra:
  #  jdbcRef: waimaistoremanagement_shangou_sgb2b_seashop_user_test # TODO 替换为自己test环境的jdbcRef
  jdbcRef: shangousgb2bseashop_shangou_sgb2b_seashop_user_test # 迁移库

#分布式锁cerberus
cerberus:
  lock:
    env: offline
    category: sg-seashop-cache

# venus 配置
venus:
  enabled: true
  bucket: seashopimagetest
  clientId: 8scckx46q4cjn4kq000000000054eb54
  hostName: http://p0.inf.test.sankuai.com/seashopimagetest/
  env: test

# s3配置
s3plus:
  hostName: http://msstest.vip.sankuai.com
  bucketName: seashop-test
  downloadUrlHeader: https://msstest.vip.sankuai.com/seashop-test/

# 电子签章
esign:
  appId: 9f093d4f8c

#缓存
squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_qa

# ES
eagle:
  clusterName: shangou_shandiancang_default
  #访问集群的密钥，配置在kms中
  access-key-kms-key: eagleAccessKey

mt:
  company:
    companyName: 北京三快在线科技有限公司
    regCode: 91110108562144110X
    contactMobile: 13720092740
    certifierMobile: 13720092740
    certifierName: 张斌
    certifierIdentity: 412723198501121298

font:
  path: https://msstest.sankuai.com/seashop-test/FZLTXHJW.TTF
  cu_path: https://msstest.sankuai.com/seashop-test/FZLTZHUNHJW.TTF

weiXin:
  envVersion: trial

es:
  index:
    shop: shop_qianyi_index