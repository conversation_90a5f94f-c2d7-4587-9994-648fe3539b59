#日志配置
logging:
  level.com.sankuai.shangou.seashop.user.account.mapper: debug # 打印mybatis的sql日志
  level.com.sankuai.shangou.seashop.user.shop.mapper: debug # 打印mybatis的sql日志

zebra:
  #  jdbcRef: waimaistoremanagement_shangou_sgb2b_seashop_user_test # TODO 替换为自己test环境的jdbcRef
  jdbcRef: sgb2b_shangou_sgb2b_seashop_user_product # 迁移库

#分布式锁cerberus
cerberus:
  lock:
    env: online
    category: sg-seashop-cache

# venus 配置
venus:
  enabled: true
  hostName: https://p0.meituan.net/seashopimage/
  bucket: seashopimage
  clientId: 6bcc7rdvvkxp7wn40000000000e78ae2
  env: prod
s3plus:
  hostName: https://s3plus-bj02.sankuai.com
  bucketName: seashop-product
  downloadUrlHeader: https://s3plus-bj02.sankuai.com/seashop-product/
# 电子签章
esign:
  appId: 47665e1dde

#缓存
squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_product

# ES
eagle:
  clusterName: shangou_eaglenode-es-shandiancang_default
  #访问集群的密钥，配置在kms中
  access-key-kms-key: eagleAccessKey

mt:
  company:
    companyName: 北京三快在线科技有限公司
    regCode: 91110108562144110X
    contactMobile: 13720092740
    certifierMobile: 13720092740
    certifierName: 张斌
    certifierIdentity: 412723198501121298

font:
  path: https://msstest.sankuai.com/seashop-test/FZLTXHJW.TTF
  cu_path: https://msstest.sankuai.com/seashop-test/FZLTZHUNHJW.TTF

weiXin:
  envVersion: trial