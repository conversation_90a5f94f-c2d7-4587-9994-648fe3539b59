# web服务端口号
server:
  port: 8080

spring:
  application:
    name: himall-base
  profiles:
    active: chengpei_local
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  redis:
    database: 1
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:124.71.221.117:8848}
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:hishop}
      config:
        namespace: ${spring.profiles.active}
        group: 1.0.0
        protocol: http
      discovery:
        namespace: ${spring.profiles.active}
        group: 1.0.0
        protocol: http
  config:
    import:
      - optional:nacos:himall-common.yml
      - optional:nacos:${spring.application.name}.yml
mybatis:
  type-aliases-package: com.sankuai.shangou.seashop.base.dao.core.mapper
  mapper-locations: classpath:mapper/*.xml
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
