/*
 Navicat Premium Dump SQL

 Source Server         : 易事-测试
 Source Server Type    : MySQL
 Source Server Version : 50743 (5.7.43-231000-log)
 Source Host           : ************:3306
 Source Schema         : himall_order_bbc

 Target Server Type    : MySQL
 Target Server Version : 50743 (5.7.43-231000-log)
 File Encoding         : 65001

 Date: 10/06/2025 10:40:20
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for finance_account
-- ----------------------------
DROP TABLE IF EXISTS `finance_account`;
CREATE TABLE `finance_account`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `account_date` datetime NOT NULL COMMENT '出账日期',
  `start_date` datetime NOT NULL COMMENT '开始时间',
  `end_date` datetime NOT NULL COMMENT '结束时间',
  `status` int(11) NOT NULL COMMENT '枚举 0未结账，1已结账',
  `product_actual_paid_amount` decimal(18, 2) NOT NULL COMMENT '商品实付总额',
  `freight_amount` decimal(18, 2) NOT NULL COMMENT '运费',
  `commission_amount` decimal(18, 2) NOT NULL COMMENT '佣金',
  `refund_commission_amount` decimal(18, 2) NOT NULL COMMENT '退还佣金',
  `refund_amount` decimal(18, 2) NOT NULL COMMENT '退款金额',
  `advance_payment_amount` decimal(18, 2) NOT NULL COMMENT '预付款总额',
  `period_settlement` decimal(18, 2) NOT NULL COMMENT '本期应结',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `payment_channel` int(1) NULL DEFAULT 1 COMMENT '支付渠道 1汇付天下',
  `channel_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '渠道手续费',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 790 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '财务结算记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_account_detail
-- ----------------------------
DROP TABLE IF EXISTS `finance_account_detail`;
CREATE TABLE `finance_account_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL COMMENT '结算记录外键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `date` datetime NOT NULL COMMENT '完成日期',
  `order_date` datetime NOT NULL COMMENT '订单下单日期',
  `pay_date` datetime NOT NULL COMMENT '付款日期',
  `order_finish_date` datetime NOT NULL COMMENT '订单完成日期',
  `order_type` int(11) NOT NULL COMMENT '枚举 完成订单1，退订单0',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `order_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  `product_actual_paid_amount` decimal(18, 2) NOT NULL COMMENT '商品实付总额',
  `freight_amount` decimal(18, 2) NOT NULL COMMENT '运费金额',
  `tax_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '税费',
  `commission_amount` decimal(18, 2) NOT NULL COMMENT '佣金',
  `refund_total_amount` decimal(18, 2) NOT NULL COMMENT '退款金额',
  `refund_commis_amount` decimal(18, 2) NOT NULL COMMENT '退还佣金',
  `order_refunds_dates` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退单的日期集合以;分隔',
  `settlement_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '结算金额',
  `payment_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付类型名称',
  `discount_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台优惠券抵扣金额',
  `discount_amount_return` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台优惠券退还金额',
  `payment_channel` int(1) NOT NULL DEFAULT 1 COMMENT '支付渠道。1：汇付天下；2：钱袋宝',
  `payment_type` tinyint(4) NOT NULL COMMENT '支付方式。1：支付宝扫码；2：支付宝H5；3：微信小程序；4：微信H5；5：企业网银；6：个人网银',
  `channel_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '渠道手续费',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `deposit_refund_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '保证金退款金额(冗余。退款的单据有值，此时默认为0)',
  `biz_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '业务类型。1：订单支付成功；2：退款成功',
  `refund_settle_status` tinyint(4) NOT NULL DEFAULT -1 COMMENT '退款时结算状态(钱袋宝)。1：待分账；3：已分账；5：已结算',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_account_id`(`account_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '财务结算明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_cash_deposit
-- ----------------------------
DROP TABLE IF EXISTS `finance_cash_deposit`;
CREATE TABLE `finance_cash_deposit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint(20) NOT NULL COMMENT 'Shop表外键',
  `current_balance` decimal(20, 2) NOT NULL DEFAULT 0.00 COMMENT '可用金额',
  `total_balance` decimal(20, 2) NOT NULL DEFAULT 0.00 COMMENT '已缴纳金额',
  `date` datetime NOT NULL COMMENT '最后一次缴纳时间',
  `enable_labels` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否显示标志，只有保证金欠费该字段才有用，默认显示',
  `alert_value` int(11) NULL DEFAULT 0 COMMENT '警戒值',
  `iz_sub_order` bit(1) NULL DEFAULT b'0' COMMENT '是否不够订单退款',
  `send_time` datetime NULL DEFAULT NULL COMMENT '提醒发送时间',
  `send_migrate_flag` tinyint(1) NULL DEFAULT 0 COMMENT '迁移： 0：不弹框    1：弹框  ',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 720 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '保证金表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_cash_deposit_detail
-- ----------------------------
DROP TABLE IF EXISTS `finance_cash_deposit_detail`;
CREATE TABLE `finance_cash_deposit_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cash_deposit_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '保证金表id',
  `add_date` datetime NOT NULL COMMENT '时间',
  `balance` decimal(20, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `operator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `recharge_way` int(11) NULL DEFAULT NULL COMMENT '充值类型（银联、支付宝之类的）',
  `operator_type` int(11) NOT NULL DEFAULT 1 COMMENT '类型；1，付款；2，扣款；3，退款   4:订单退款',
  `channel_order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道支付单号',
  `channel_type` tinyint(1) NULL DEFAULT 1 COMMENT '1: 汇付  2:钱袋宝',
  `platform_deduction` decimal(20, 2) NOT NULL DEFAULT 0.00 COMMENT '平台扣款金额',
  `forzen_amount` decimal(20, 2) NOT NULL DEFAULT 0.00 COMMENT '冻结金额',
  `refund_amount` decimal(20, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `trade_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易流水号',
  `channel_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道ID',
  `deduction_type` int(11) NOT NULL DEFAULT 0 COMMENT '扣款类型，1：罚款；2：代收代付；',
  `deduction_fee` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '扣款手续费',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_deposit_id_type`(`cash_deposit_id`, `operator_type`, `channel_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1093 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '保证金明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_cash_deposit_detail_order_refund
-- ----------------------------
DROP TABLE IF EXISTS `finance_cash_deposit_detail_order_refund`;
CREATE TABLE `finance_cash_deposit_detail_order_refund`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '门店id',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id',
  `order_refund_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单退款id',
  `cash_deposit_id` bigint(20) NOT NULL COMMENT '保证金id',
  `deposit_detail_id` bigint(20) NULL DEFAULT NULL COMMENT '保证金明细id',
  `refund_amount` decimal(20, 0) NULL DEFAULT 0 COMMENT '退款金额',
  `refund_status` tinyint(1) NULL DEFAULT 0 COMMENT '0：无效的  1：有效的',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单售后退款保证金明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_cash_deposit_pay
-- ----------------------------
DROP TABLE IF EXISTS `finance_cash_deposit_pay`;
CREATE TABLE `finance_cash_deposit_pay`  (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pay_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保证金订单ID',
  `adapay_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '汇付ID, 钱袋宝ID',
  `pay_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '1: 汇付 2:钱袋宝',
  `pay_date` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `pay_status` int(1) NOT NULL DEFAULT 0 COMMENT '支付状态  0: 未支付, 1: 支付成功, 2: 支付失败',
  `payment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2599 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '保证金支付表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_cash_deposit_refund
-- ----------------------------
DROP TABLE IF EXISTS `finance_cash_deposit_refund`;
CREATE TABLE `finance_cash_deposit_refund`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cash_deposit_detail_id` bigint(20) NOT NULL,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态 0，待审核；1，已通过；2，已拒绝；3，退款处理中  4：退款失败  5：重试',
  `bond` decimal(18, 2) NOT NULL COMMENT '保证金',
  `deduction` decimal(18, 2) NOT NULL COMMENT '扣除金额',
  `refund` decimal(18, 2) NOT NULL COMMENT '退款金额',
  `apply_date` datetime NOT NULL COMMENT '申请时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `refund_order_id` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付撤销处理ID',
  `execute_type` tinyint(1) NULL DEFAULT NULL COMMENT '1: 钱袋包转账退款  2: 钱袋包普通退款  3:超期退款',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 316 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '保证金退款表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_finance
-- ----------------------------
DROP TABLE IF EXISTS `finance_finance`;
CREATE TABLE `finance_finance`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `pay_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付单号，保证金订单号和支付单号一致',
  `pay_channel` tinyint(4) NOT NULL DEFAULT 1 COMMENT '支付渠道。1：汇付；2：钱袋宝',
  `adapay_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '汇付支付流水号',
  `type` int(11) NOT NULL COMMENT '交易类型 [Description(\"支付\")]Pay = 1,[Description(\"退款\")]Refund =  2,[Description(\"扣款\")]Deduction = 3',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '会员ID',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员名称',
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交易流水号',
  `total_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `refund_type` int(11) NULL DEFAULT NULL COMMENT '退款类型,1:订单完成前退款，2：订单完成后退款',
  `freight` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '运费',
  `product_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '原价',
  `discount_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '针对该订单的优惠金额',
  `plat_discount_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台优惠券抵扣金额',
  `full_discount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满额减金额',
  `money_off` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满减活动优惠金额',
  `integral_discount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '积分优惠金额',
  `service_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '汇付手续费',
  `settlement_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '供应商结算金额',
  `commission_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金',
  `deduction_type` int(11) NOT NULL DEFAULT 0 COMMENT '扣款类型，1：罚款；2：代收代付；',
  `service_fee` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '扣款手续费',
  `order_refund_id` bigint(20) NULL DEFAULT NULL COMMENT '退款Id',
  `actual_pay_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '订单实付金额',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '财务中间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_finance_item
-- ----------------------------
DROP TABLE IF EXISTS `finance_finance_item`;
CREATE TABLE `finance_finance_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `finance_id` bigint(20) NOT NULL COMMENT '中间表ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `sku` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU',
  `quantity` bigint(20) NOT NULL COMMENT '购买数量/退货数量',
  `original_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '原价',
  `sale_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '售价',
  `total_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '实际应付金额',
  `discount_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `full_discount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满额减平摊到订单项的金额',
  `money_off` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满减活动平摊到订单项金额',
  `coupon_discount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券抵扣金额',
  `plat_coupon_discount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台优惠券抵扣金额',
  `commis_rate` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '抽佣比例',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '财务中间项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_pending_settlement_order
-- ----------------------------
DROP TABLE IF EXISTS `finance_pending_settlement_order`;
CREATE TABLE `finance_pending_settlement_order`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `order_type` int(11) NULL DEFAULT NULL COMMENT '订单类型',
  `order_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  `products_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '商品实付金额',
  `freight_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '运费',
  `tax_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '税费',
  `plat_commission` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台佣金',
  `refund_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `refund_date` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `plat_commission_return` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台佣金退还',
  `settlement_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '结算金额',
  `pay_date` datetime NULL DEFAULT NULL COMMENT '付款日期',
  `order_finish_time` datetime NULL DEFAULT NULL COMMENT '订单完成时间',
  `payment_type` int(11) NOT NULL COMMENT '支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银，',
  `payment_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `discount_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台优惠券抵扣金额',
  `discount_amount_return` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台优惠券退还金额',
  `payment_channel` int(1) NULL DEFAULT 1 COMMENT '支付渠道 1汇付天下',
  `channel_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '渠道手续费',
  `settelement_remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结算备注',
  `self_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否自营店结算订单，0代表否，1代表是',
  `delete_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '待结算订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_pending_settlement_order_qdb
-- ----------------------------
DROP TABLE IF EXISTS `finance_pending_settlement_order_qdb`;
CREATE TABLE `finance_pending_settlement_order_qdb`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '店铺名称',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `biz_type` tinyint(4) NOT NULL COMMENT '业务类型。1：订单支付成功；2：退款成功',
  `biz_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务单号。订单号或者退款单号',
  `event_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '业务发生金额',
  `plat_commission` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台佣金',
  `settlement_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '结算金额',
  `payment_source` tinyint(4) NOT NULL COMMENT '支付来源。1：闪电仓；2：牵牛花',
  `payment_type` tinyint(4) NOT NULL COMMENT '支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银，',
  `payment_channel` tinyint(4) NOT NULL DEFAULT 2 COMMENT '支付渠道。2：钱袋宝',
  `channel_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '渠道手续费。钱袋宝目前为0',
  `deposit_refund_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '保证金退款金额(冗余。退款的单据有值，此时默认为0)',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '结算备注',
  `event_time` datetime NOT NULL COMMENT '事件发生时间(如果是后续补偿保存的与创建时间不一致)',
  `pay_date` datetime NULL DEFAULT NULL COMMENT '订单付款时间',
  `order_finish_time` datetime NULL DEFAULT NULL COMMENT '订单完成时间',
  `settle_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '结算状态。1：待分账；2：分帐中；3：已分账；4：结算中；5：已结算',
  `sale_return_time_out` mediumint(9) NULL DEFAULT NULL COMMENT '订单最大的允许售后期限(单位天)(订单完成时写入)',
  `self_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否自营店铺。0代表否，1代表是',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除。0：否；1：是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `order_date` datetime NOT NULL COMMENT '下单时间',
  `split_time` datetime NULL DEFAULT NULL COMMENT '分账时间',
  `settle_time` datetime NULL DEFAULT NULL COMMENT '结算时间',
  `pay_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付服务ID',
  `pay_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付平台(汇付/钱袋宝)支付流水号',
  `pay_batch_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付批次号',
  `settle_after_split_days` mediumint(9) NULL DEFAULT NULL COMMENT '分帐后多少天结算(订单完成时写入)',
  `settle_type` tinyint(4) NULL DEFAULT NULL COMMENT '结算时限类型(订单完成时写入)。1：订单完成后；2：售后维权期后',
  `settle_period` mediumint(9) NULL DEFAULT NULL COMMENT '结算时限(单位天)(订单完成时写入)',
  `refund_settle_status` tinyint(4) NOT NULL DEFAULT -1 COMMENT '退款时结算状态。1：待分账；3：已分账；5：已结算',
  `settle_plat_commission` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台待结佣金',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '待结算订单表(钱袋宝)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_plat_account
-- ----------------------------
DROP TABLE IF EXISTS `finance_plat_account`;
CREATE TABLE `finance_plat_account`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `pending_settlement` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '待结算',
  `settled` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '已结算',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台资金表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_qdb_settle_error_log
-- ----------------------------
DROP TABLE IF EXISTS `finance_qdb_settle_error_log`;
CREATE TABLE `finance_qdb_settle_error_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `biz_type` tinyint(4) NOT NULL COMMENT '业务类型。1：分账；2：结算；',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `pending_id` bigint(20) NOT NULL COMMENT '待结算表ID',
  `error_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '错误描述',
  `remark` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标识。0：否；1：是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '钱袋宝结算错误日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_settle_record
-- ----------------------------
DROP TABLE IF EXISTS `finance_settle_record`;
CREATE TABLE `finance_settle_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `trace_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跟踪流水号',
  `record_type` tinyint(4) NOT NULL COMMENT '记录类型。10：分账；20：佣金结算；21：货款结算',
  `amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '业务发生金额(分账/结算金额)',
  `handle_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '处理状态。0：待处理；2：处理成功；3：处理失败',
  `external_serial_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部流水号',
  `error_desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败描述(记录异常码和异常描述)',
  `related_biz_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联的业务编码，冗余用途',
  `merchant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '支付商户ID',
  `settlement_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '结算ID',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  `retry_flag` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否需要重试(两种类型的结算都失败时不需要重试)。1：是；0：否',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除。0：否；1：是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  INDEX `id`(`id`) USING BTREE,
  INDEX `idx_trace_no`(`trace_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '结算记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_settle_record_pending_rel
-- ----------------------------
DROP TABLE IF EXISTS `finance_settle_record_pending_rel`;
CREATE TABLE `finance_settle_record_pending_rel`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主表流水号',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `pending_settlement_id` bigint(20) NOT NULL COMMENT '待结算表ID',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `biz_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '业务类型(冗余)。10：分账；20：佣金结算；21：货款结算',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除。0：否；1：是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '明细金额，目前分账记录会设置',
  INDEX `id`(`id`) USING BTREE,
  INDEX `idx_trace_no`(`trace_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '结算记录与待结算关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_settlement_config
-- ----------------------------
DROP TABLE IF EXISTS `finance_settlement_config`;
CREATE TABLE `finance_settlement_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `settlement_interval` int(10) NOT NULL DEFAULT 1 COMMENT '当前结算周期(天)',
  `settlement_fee_rate` decimal(10, 5) NOT NULL DEFAULT 0.00000 COMMENT '结算手续费率（%）',
  `wx_fee_rate` decimal(10, 5) NOT NULL DEFAULT 0.00000 COMMENT '微信结算手续费率（%）',
  `qdb_fee_rate` decimal(10, 5) NULL DEFAULT 0.00000 COMMENT '钱袋宝手续比例',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `settlement_time` int(10) NOT NULL DEFAULT 0 COMMENT '钱袋宝 分账x天后结算',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '结算配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_settlement_event_record
-- ----------------------------
DROP TABLE IF EXISTS `finance_settlement_event_record`;
CREATE TABLE `finance_settlement_event_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `pedding_settlement_id` bigint(20) NOT NULL COMMENT '待结算表关联ID',
  `trace_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跟踪流水号',
  `record_type` tinyint(4) NOT NULL COMMENT '记录类型。1：分账；2：佣金；3：结算金额',
  `amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '业务发生金额',
  `handle_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '处理状态。0：待处理；2：处理成功；3：处理失败',
  `error_desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败描述(记录异常码和异常描述)',
  `settle_period_start_time` datetime NULL DEFAULT NULL COMMENT '结算周期开始时间',
  `settle_period_end_time` datetime NULL DEFAULT NULL COMMENT '结算周期结束时间',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除。0：否；1：是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '结算事件记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_settlement_record
-- ----------------------------
DROP TABLE IF EXISTS `finance_settlement_record`;
CREATE TABLE `finance_settlement_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `pedding_settlement_id` bigint(20) NOT NULL COMMENT '待结算表关联ID',
  `trace_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跟踪流水号',
  `record_type` tinyint(4) NOT NULL COMMENT '记录类型。1：分账；2：佣金；3：结算金额',
  `amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '业务发生金额',
  `handle_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '平台佣金',
  `settlement_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '处理状态。0：待处理；2：处理成功；3：处理失败',
  `error_desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败描述(记录异常码和异常描述)',
  `settle_period_start_time` datetime NULL DEFAULT NULL COMMENT '结算周期开始时间',
  `settle_period_end_time` datetime NULL DEFAULT NULL COMMENT '结算周期结束时间',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除。0：否；1：是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '待结算订单表(钱袋宝)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_shop_account
-- ----------------------------
DROP TABLE IF EXISTS `finance_shop_account`;
CREATE TABLE `finance_shop_account`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺Id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `pending_settlement` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '待结算',
  `settled` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '已结算',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `settle_account_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '汇付天下结算账户ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_shop_id`(`shop_id`) USING BTREE COMMENT '店铺ID为唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 737 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺资金表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for finance_shop_account_item
-- ----------------------------
DROP TABLE IF EXISTS `finance_shop_account_item`;
CREATE TABLE `finance_shop_account_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交易流水号',
  `account_id` bigint(20) NOT NULL COMMENT '关联资金编号',
  `amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `trade_type` int(11) NOT NULL DEFAULT 0 COMMENT '交易类型',
  `income_flag` bit(1) NOT NULL COMMENT '是否收入',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易备注',
  `detail_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详情ID',
  `settlement_cycle` int(11) NOT NULL COMMENT '结算周期(以天为单位)(冗余字段)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺资金流水表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `order_status` int(11) NOT NULL COMMENT '订单状态(1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中)',
  `order_date` datetime NOT NULL COMMENT '订单创建日期',
  `close_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关闭原因',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `seller_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商电话',
  `seller_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商发货地址',
  `seller_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商说明',
  `seller_remark_flag` int(11) NULL DEFAULT NULL COMMENT '供应商说明标识',
  `user_id` bigint(20) NOT NULL COMMENT '商家id',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `user_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家留言',
  `ship_to` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货人',
  `cell_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人电话',
  `top_region_id` int(11) NOT NULL COMMENT '收货人地址省份id',
  `region_id` int(11) NOT NULL COMMENT '收货人区域id',
  `region_full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '全名的收货地址',
  `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '地址',
  `receive_longitude` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '收货地址坐标',
  `receive_latitude` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '收货地址坐标',
  `freight` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '运费，如果有修改，就是修改后的运费',
  `backup_freight` decimal(8, 2) NULL DEFAULT NULL COMMENT '手动修改运费后备份的原运费，只有第一次修改运费时才备份',
  `shipping_date` datetime NULL DEFAULT NULL COMMENT '发货日期',
  `is_printed` tinyint(1) NOT NULL COMMENT '是否打印快递单',
  `payment_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款类型名称',
  `payment_type` int(11) NOT NULL COMMENT '支付方式(1:线上支付)',
  `gateway_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付接口返回的id',
  `pay_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款注释',
  `pay_date` datetime NULL DEFAULT NULL COMMENT '付款日期',
  `tax` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '发票税钱',
  `finish_date` datetime NULL DEFAULT NULL COMMENT '完成订单日期',
  `product_total_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '商品总金额',
  `refund_total_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `commis_total_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金总金额',
  `refund_commis_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '退还佣金总金额',
  `settlement_charge` decimal(8, 4) NULL DEFAULT 0.0000 COMMENT '结算手续费比率',
  `active_type` int(11) NOT NULL DEFAULT 0 COMMENT '活动类型',
  `platform` int(11) NOT NULL DEFAULT 0 COMMENT '来自哪个终端的订单',
  `coupon_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券抵扣金额',
  `order_type` int(11) NOT NULL DEFAULT 0 COMMENT '订单类型。0:正常购,1:组合购,2:限时购',
  `order_remarks` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注(买家留言)',
  `last_modify_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  `delivery_type` int(11) NOT NULL DEFAULT '0' COMMENT '发货类型(0:快递配送)',
  `total_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '订单实付金额',
  `actual_pay_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '订单实收金额(订单实付-退款金额)',
  `discount_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '折扣金额',
  `money_off_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满减活动优惠金额',
  `money_off_condition` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满足了多少金额，才参与的满减活动（如果有叠加，则是叠加后的满足金额）',
  `coupon_id` bigint(20) NOT NULL COMMENT '使用的优惠券id',
  `is_send` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否发送过短信',
  `coupon_type` int(11) NULL DEFAULT NULL COMMENT '优惠券类型（0代表商家券，1代表商家红包）',
  `order_source` int(11) NOT NULL DEFAULT 0 COMMENT '订单来源 0表示本系统，1表示牵牛花',
  `source_order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '牵牛花订单号',
  `push_erp_type` int(11) NOT NULL DEFAULT 0 COMMENT '推送erp类型',
  `push_erp_state` int(11) NOT NULL DEFAULT 0 COMMENT '推送erp结果 0未推送 1推送成功 2推送失败',
  `push_erp_msg` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送erp错误信息',
  `receive_delay` int(11) NOT NULL DEFAULT 0 COMMENT '收货延迟天数',
  `payment` int(11) NULL DEFAULT NULL COMMENT '支付方式。1：支付宝扫码；3：微信小程序；5：企业网银；6：个人网银',
  `pay_bank_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '汇付支付网银编码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `main_order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主订单号。用于区分同一批次提交的订单，取批次中第一个订单号',
  `has_commented` tinyint(4) NOT NULL DEFAULT 0 COMMENT '订单是否已评价。0：否；1：是',
  `store_id` bigint(20) NULL DEFAULT NULL COMMENT '门店ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user`(`user_id`, `order_date`) USING BTREE,
  INDEX `idx_shop`(`shop_id`, `order_date`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE COMMENT '订单ID索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_comment
-- ----------------------------
DROP TABLE IF EXISTS `order_comment`;
CREATE TABLE `order_comment`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `comment_date` datetime NOT NULL COMMENT '评价日期',
  `pack_mark` int(11) NOT NULL COMMENT '包装评分',
  `delivery_mark` int(11) NOT NULL COMMENT '物流评分',
  `service_mark` int(11) NOT NULL COMMENT '服务评分',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单评价表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_complaint
-- ----------------------------
DROP TABLE IF EXISTS `order_complaint`;
CREATE TABLE `order_complaint`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id',
  `status` int(11) NOT NULL COMMENT '审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束)',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `shop_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺联系方式',
  `user_id` bigint(20) NOT NULL COMMENT '商家id',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `user_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家联系方式',
  `complaint_date` datetime NOT NULL COMMENT '投诉日期',
  `complaint_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '投诉原因',
  `seller_reply` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺反馈信息',
  `plat_remark` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '投诉备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2304122048188255 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单投诉表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_exception_order
-- ----------------------------
DROP TABLE IF EXISTS `order_exception_order`;
CREATE TABLE `order_exception_order`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `batch_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付批次号，多笔订单同时支付时批次号相同',
  `pay_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付渠道对应的唯一标识',
  `pay_time` datetime NOT NULL COMMENT '支付时间',
  `pay_amount` decimal(18, 2) NOT NULL COMMENT '支付金额',
  `error_type` smallint(6) NOT NULL COMMENT '异常类型，1，重复支付；2，超时关闭；',
  `refund_time` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `refund_status` smallint(6) NOT NULL DEFAULT 0 COMMENT '退款状态。0:待退款；1:退款中；2:退款完成；3:退款失败；',
  `refund_fail_reason` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款失败原因',
  `refund_batch_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款批次号',
  `refund_manager` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款操作员',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '异常订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_invoice
-- ----------------------------
DROP TABLE IF EXISTS `order_invoice`;
CREATE TABLE `order_invoice`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '订单编号',
  `invoice_type` int(11) NOT NULL DEFAULT 0 COMMENT '发票类型（1:普通发票、2:电子发票、3:增值税发票）',
  `invoice_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票抬头',
  `invoice_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税号',
  `invoice_context` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票内容(发票明细、商品类别)',
  `register_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '注册地址',
  `register_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '注册电话',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行帐号',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收票人姓名',
  `cell_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收票人手机号',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收票人邮箱',
  `region_id` int(11) NOT NULL DEFAULT 0 COMMENT '收票人地址区域id',
  `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收票人详细地址',
  `vat_invoice_day` int(11) NOT NULL DEFAULT 0 COMMENT '订单完成后多少天开具增值税发票',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单发票信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_item
-- ----------------------------
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id，对应order表的order_id',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'skuid',
  `sku` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sku表sku字段',
  `quantity` bigint(20) NOT NULL DEFAULT 0 COMMENT '购买数量',
  `return_quantity` bigint(20) NOT NULL DEFAULT 0 COMMENT '退货数量',
  `cost_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `sale_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '销售价',
  `discount_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `real_total_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '实际应付金额=final_sale_price-discount_amount-change_amount',
  `refund_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '退款价格',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `color` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sku颜色',
  `size` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sku尺寸',
  `version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sku版本',
  `thumbnails_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缩略图',
  `commis_rate` decimal(18, 4) NOT NULL DEFAULT 0.0000 COMMENT '分佣比例',
  `enabled_refund_amount` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '可退金额',
  `is_limit_buy` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为限时购商品',
  `coupon_discount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券抵扣金额',
  `full_discount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满额减平摊到订单项的金额',
  `money_off` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满减活动分配金额',
  `active_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '折扣营销活动id',
  `flash_sale_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '限时购活动id',
  `sku_auto_id` bigint(20) NOT NULL DEFAULT 0 COMMENT 'sku表auto_id字段',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `apply_refund_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '已申请售后数量。发货前售后该值为0，发货后不管是仅退款还是退货退款都有值',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_mq_error_data
-- ----------------------------
DROP TABLE IF EXISTS `order_mq_error_data`;
CREATE TABLE `order_mq_error_data`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `biz_type` tinyint(4) NOT NULL COMMENT '业务类型。1：订单状态变更；2：支付回调；3：售后状态变更；4：售后回调',
  `biz_type_desc` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务类型描述',
  `biz_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编号，业务层面控制业务类型下编号唯一',
  `message_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'MQ消息ID',
  `trace_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'MQ处理时的traceId，用于排查',
  `error_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '异常信息',
  `data_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'MQ消息内容。注意长度',
  `handle_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '处理状态。1：待处理；2：处理中；3：已处理；4：已取消；5：处理失败',
  `re_exe_error_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '重新执行时的异常内容。handle_status=5 时可能有值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 168 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单服务中MQ消息处理异常的数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `order_operation_log`;
CREATE TABLE `order_operation_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id',
  `operator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作者',
  `operate_date` datetime NOT NULL COMMENT '操作日期',
  `operate_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 551588 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单操作日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_pay_record
-- ----------------------------
DROP TABLE IF EXISTS `order_pay_record`;
CREATE TABLE `order_pay_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `batch_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付批次单号。多笔订单一起支付时batch_no一致，数据迁移时对应之前的payId',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号。与 order 表 order_id 一致',
  `pay_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付渠道的唯一标识。目前时汇付的支付ID',
  `pay_channel` tinyint(4) NOT NULL COMMENT '支付渠道。1：汇付天下支付。目前固定为1',
  `pay_method` tinyint(4) NOT NULL COMMENT '支付方式。1：支付宝扫码；2：支付宝H5；3：微信小程序；4：微信H5；5：企业网银；6：个人网银',
  `pay_status` tinyint(4) NOT NULL COMMENT '支付状态。0：关闭；1：支付中；2：支付成功；3：支付失败',
  `out_trans_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部交易单号，比如支付宝的支付流水号',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间，支付结果回调设置',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注。比如支付失败时可能的提示信息',
  `order_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '订单需要支付的金额',
  `pay_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付金额。可能在订单需付金额基础上还有渠道优惠',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1780167513524154815 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单支付记录表，用于记录订单与支付单之间的映射' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_pending_settlement_forzen
-- ----------------------------
DROP TABLE IF EXISTS `order_pending_settlement_forzen`;
CREATE TABLE `order_pending_settlement_forzen`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `order_refund_id` bigint(20) NOT NULL COMMENT '退款id',
  `forzen_amount` decimal(20, 2) NOT NULL COMMENT '冻结金额',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除标识，解冻就删除。0：否，1：是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_forzen_refund_order_shop_id`(`order_refund_id`, `order_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商待结算金额冻结表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_pending_settlement_order
-- ----------------------------
DROP TABLE IF EXISTS `order_pending_settlement_order`;
CREATE TABLE `order_pending_settlement_order`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号，对应order表的order_id',
  `order_type` int(11) NULL DEFAULT NULL COMMENT '订单类型',
  `order_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  `products_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '商品实付金额',
  `freight_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '运费',
  `tax_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '税费',
  `plat_commission` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台佣金',
  `refund_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `refund_date` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `plat_commission_return` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台佣金退还',
  `settlement_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '结算金额',
  `order_finsh_time` datetime NULL DEFAULT NULL COMMENT '订单完成时间',
  `payment_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `adpay_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '汇付手续费',
  `settelement_remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结算备注',
  `is_self` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否自营店结算订单(0代表否，1代表是)',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除标识。0：否，1：是',
  `settle_time` datetime NULL DEFAULT NULL COMMENT '结算时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '待结算订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_product_comment
-- ----------------------------
DROP TABLE IF EXISTS `order_product_comment`;
CREATE TABLE `order_product_comment`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_comment_id` bigint(20) NOT NULL COMMENT '商品评论id(美团id组件生成, 与其他表的关联使用该字段)',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id, 对应order.order_id',
  `sub_order_id` bigint(20) NOT NULL COMMENT '订单详细id，对应order_item.id',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `spec1_alias` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格1别名',
  `spec2_alias` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格2别名',
  `spec3_alias` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格3别名',
  `spec1_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格1名称',
  `spec2_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格2名称',
  `spec3_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格3名称',
  `thumbnails_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '缩略图',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
  `user_id` bigint(20) NOT NULL COMMENT '商家id',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家名称',
  `email` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家email',
  `user_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商家手机号',
  `review_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评价内容',
  `review_date` datetime NOT NULL COMMENT '评价日期',
  `review_mark` int(11) NOT NULL COMMENT '评分',
  `reply_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回复内容',
  `reply_date` datetime NULL DEFAULT NULL COMMENT '回复日期',
  `has_image` bit(1) NULL DEFAULT b'0' COMMENT '是否回复了图片',
  `append_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '追加内容',
  `append_date` datetime NULL DEFAULT NULL COMMENT '追加时间',
  `append_has_image` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否追加了图片',
  `reply_append_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '追加评论回复',
  `reply_append_date` datetime NULL DEFAULT NULL COMMENT '追加评论回复时间',
  `has_hidden` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否隐藏(前端不显示)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品ID_规格1ID_规格2ID_规格3ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除,1是0否',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_product_comment_id`(`product_comment_id`) USING BTREE,
  INDEX `idx_order`(`sub_order_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`, `review_mark`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单商品评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_product_comment_image
-- ----------------------------
DROP TABLE IF EXISTS `order_product_comment_image`;
CREATE TABLE `order_product_comment_image`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '评论图片',
  `product_comment_id` bigint(20) NOT NULL COMMENT '商品评论id(order_product_comment.product_comment_id)',
  `comment_type` int(11) NOT NULL COMMENT '评论类型（首次评论/追加评论）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_comment`(`product_comment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单商品评论图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_product_verification
-- ----------------------------
DROP TABLE IF EXISTS `order_product_verification`;
CREATE TABLE `order_product_verification`  (
  `id` bigint(20) NOT NULL COMMENT '自增主键ID',
  `verification_type` int(5) NOT NULL COMMENT '核销码类型（1:虚拟商品；2:自提订单）',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `product_id` bigint(20) NULL DEFAULT NULL COMMENT '商品ID',
  `virtual_product_snapshot_id` bigint(20) NULL DEFAULT NULL COMMENT '虚拟商品快照ID',
  `shop_id` bigint(20) NULL DEFAULT NULL COMMENT '店铺ID',
  `store_id` bigint(20) NULL DEFAULT NULL COMMENT '门店ID',
  `cell_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货人手机号',
  `verification_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '核销码（8位）',
  `multi_verification_no` int(5) NULL DEFAULT NULL COMMENT '多核销码编号（数字及排序）',
  `verify_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '核销状态（1:待核销；2:已核销；3:已退款）',
  `verify_user` bigint(20) NULL DEFAULT NULL COMMENT '核销人',
  `verify_time` datetime NULL DEFAULT NULL COMMENT '核销时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_shopId_code`(`shop_id`, `verification_code`) USING BTREE COMMENT '店铺ID+核销码 唯一',
  INDEX `idx_orderId_productId`(`order_id`, `product_id`) USING BTREE COMMENT '订单ID、商品ID',
  INDEX `idx_shopId_storeId`(`shop_id`, `store_id`) USING BTREE COMMENT '店铺ID、门店ID',
  INDEX `idx_cell_phone`(`cell_phone`) USING BTREE COMMENT '收货人手机号'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单商品核销表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_promotion_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `order_promotion_snapshot`;
CREATE TABLE `order_promotion_snapshot`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号。与 order 表 order_id 一致',
  `biz_type` tinyint(4) NOT NULL COMMENT '业务类型。1：订单营销；2：商品营销',
  `product_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '商品ID。bizType=2时有值，否则默认0',
  `sku_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'skuId，下单维度是sku，bizType=2时有值，否则默认空',
  `promotion_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '满足的营销活动内容，异构数据，用json字符串保存',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 371 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单涉及的营销快照数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_refund
-- ----------------------------
DROP TABLE IF EXISTS `order_refund`;
CREATE TABLE `order_refund`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id',
  `source_refund_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '三方售后单编号',
  `order_item_id` bigint(20) NOT NULL COMMENT '订单详情id',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `user_id` bigint(20) NOT NULL COMMENT '商家id',
  `applicant` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '申请内容',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_cell_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `apply_date` datetime NOT NULL COMMENT '申请时间',
  `amount` decimal(18, 2) NOT NULL COMMENT '金额',
  `reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款原因',
  `reason_detail` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款详情',
  `seller_audit_status` int(11) NOT NULL COMMENT '供应商审核状态(1:待供应商审核,2:待买家寄货,3:待供应商收货,4:供应商拒绝,5:供应商通过审核)',
  `seller_audit_date` datetime NOT NULL COMMENT '供应商审核时间',
  `seller_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商注释',
  `manager_confirm_status` int(11) NOT NULL COMMENT '平台审核状态(6:待平台确认,7:退款成功,8:平台驳回)',
  `manager_confirm_date` datetime NOT NULL COMMENT '平台审核时间',
  `manager_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台注释',
  `is_return` bit(1) NOT NULL COMMENT '是否需要退货',
  `express_company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司名称',
  `express_company_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司编码',
  `ship_order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `refund_mode` int(11) NOT NULL COMMENT '退款方式(1:订单退款,2:货品退款,3:退货退款)',
  `refund_pay_status` int(11) NOT NULL DEFAULT 2 COMMENT '退款支付状态',
  `refund_pay_type` int(11) NOT NULL COMMENT '退款支付类型',
  `buyer_deliver_date` datetime NULL DEFAULT NULL COMMENT '买家发货时间',
  `seller_confirm_arrival_date` datetime NULL DEFAULT NULL COMMENT '卖家确认到货时间',
  `refund_batch_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款批次号',
  `refund_post_time` datetime NULL DEFAULT NULL COMMENT '退款异步提交时间',
  `apply_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '申请售后数量，发货后才会设置。记录的是申请的数量，比如仅退款为了支持多次售后，也需要输入数量',
  `return_quantity` bigint(20) NOT NULL DEFAULT 0 COMMENT '退货数量，记录的是实际退货的数量',
  `return_plat_commission` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台佣金退还',
  `apply_number` int(11) NOT NULL COMMENT '申请次数',
  `cert_pic1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证图片1',
  `cert_pic2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证图片2',
  `cert_pic3` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证图片3',
  `is_all_return` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否订单全部退货。发货后的状态才可能为true',
  `return_freight` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '退运费',
  `last_modify_time` datetime NULL DEFAULT NULL COMMENT '最后修改时间',
  `is_cancel` bit(1) NULL DEFAULT b'0' COMMENT '是否取消申请',
  `cancel_date` datetime NULL DEFAULT NULL COMMENT '取消申请时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '综合状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；6：待平台确认；7：退款成功；8：平台驳回；9：退款中；-1：买家取消',
  `reapply_origin_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '重新申请对应的原始ID。0代表非重新申请的原始数据',
  `is_delete` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除。0：否；1：是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order`(`order_id`) USING BTREE,
  INDEX `idx_source_refund_id`(`source_refund_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 716280008293421115 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单退款主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_refund_item
-- ----------------------------
DROP TABLE IF EXISTS `order_refund_item`;
CREATE TABLE `order_refund_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `refund_id` bigint(20) NOT NULL COMMENT '售后单ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id',
  `order_item_id` bigint(20) NOT NULL COMMENT '订单详情id',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `user_id` bigint(20) NOT NULL COMMENT '商家id',
  `apply_amount` decimal(18, 2) NOT NULL COMMENT '申请金额',
  `return_amount` decimal(18, 2) NOT NULL COMMENT '实际退款金额(初始值跟申请金额一致)',
  `apply_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '申请售后数量',
  `return_quantity` bigint(20) NOT NULL DEFAULT 0 COMMENT '退货数量(初始值跟申请数量一致)',
  `return_plat_commission` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '平台佣金退还',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_delete` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除。0：否；1：是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_refund_id`(`refund_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `order_item_id`(`order_item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单退款明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_refund_log
-- ----------------------------
DROP TABLE IF EXISTS `order_refund_log`;
CREATE TABLE `order_refund_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `refund_id` bigint(20) NOT NULL COMMENT '售后编号',
  `operator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作者',
  `operate_date` datetime NOT NULL COMMENT '操作日期',
  `operate_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作内容',
  `apply_number` int(11) NOT NULL COMMENT '申请次数',
  `step` smallint(6) NOT NULL COMMENT '退款步聚(枚举:common_model.enum.order_refund_step)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注(买家留言/商家留言/商家拒绝原因/平台退款备注)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_refund`(`refund_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 97745 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单售后日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_refund_record
-- ----------------------------
DROP TABLE IF EXISTS `order_refund_record`;
CREATE TABLE `order_refund_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `relate_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联id;订单默认关联orderId表',
  `refund_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `pay_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付时支付单号',
  `pay_channel_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付时渠道订单号',
  `refund_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款订单号',
  `refund_type` int(2) NOT NULL DEFAULT 1 COMMENT '退款类型 1:超付退款',
  `business_type` int(2) NOT NULL DEFAULT 1 COMMENT '业务类型 1：订单,4：保证金',
  `refund_status` int(2) NOT NULL DEFAULT 0 COMMENT '退款状态 0: 退款中,1: 退款成功,2: 退款失败',
  `err_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '异常描述',
  `operator_id` bigint(20) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人账号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_virtual_product_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `order_virtual_product_snapshot`;
CREATE TABLE `order_virtual_product_snapshot`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `validity_period_type` tinyint(1) NOT NULL COMMENT '有效期类型（1:长期有效；2:天数内；3:时间段）',
  `validity_period_days` int(11) NULL DEFAULT NULL COMMENT '有效期天数',
  `validity_period_start_time` datetime NULL DEFAULT NULL COMMENT '有效期开始时间(订单创建时间)',
  `validity_period_end_time` datetime NOT NULL COMMENT '有效期结束时间',
  `whether_multi_verification_code` bit(1) NULL DEFAULT NULL COMMENT '是否开启多核销码',
  `virtual_product_usage_intro` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '虚拟商品使用说明',
  `enable_reserved_info` bit(1) NULL DEFAULT NULL COMMENT '是否开启预留信息',
  `reserved_info_setting` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留信息表设置（json）',
  `reserved_info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户所填写的预留信息（json）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_orderId_productId`(`order_id`, `product_id`) USING BTREE COMMENT '订单id、商品id'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单虚拟商品快照表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_way_bill
-- ----------------------------
DROP TABLE IF EXISTS `order_way_bill`;
CREATE TABLE `order_way_bill`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `express_company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递公司名称',
  `express_company_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司编码',
  `ship_order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物流单号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 479733 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单快递单号信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pay_channel_config
-- ----------------------------
DROP TABLE IF EXISTS `pay_channel_config`;
CREATE TABLE `pay_channel_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `payment_channel` int(11) NULL DEFAULT 1 COMMENT '支付渠道 1：汇付天下',
  `config_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置名字',
  `config_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置Key值',
  `config_value` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置Value值',
  `config_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `channel_key_u_index`(`payment_channel`, `config_key`) USING BTREE COMMENT '渠道和Key组成唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付渠道配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pay_exchange_log
-- ----------------------------
DROP TABLE IF EXISTS `pay_exchange_log`;
CREATE TABLE `pay_exchange_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联ID',
  `type` int(1) NOT NULL DEFAULT 0 COMMENT '1:创建汇付支付单，2汇付支付成功回调',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '方法',
  `param` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接口请求入参',
  `result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接口返回返参',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1185 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '汇付交互表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pay_order_pay
-- ----------------------------
DROP TABLE IF EXISTS `pay_order_pay`;
CREATE TABLE `pay_order_pay`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pay_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付流水ID',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源订单ID',
  `pay_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `pay_state` int(1) NOT NULL DEFAULT 0 COMMENT '支付状态  0: 未支付, 1: 支付成功, 2: 支付失败',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `payment_channel` int(11) NOT NULL DEFAULT 1 COMMENT '支付渠道 1：汇付天下',
  `payment_type` int(11) NOT NULL COMMENT '支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银，',
  `business_type` int(11) NOT NULL DEFAULT 1 COMMENT '业务类型 1：订单,4：保证金',
  `bank_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '银行编码',
  `channel_confirm_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道确认ID(分账返回ID)',
  `channel_pay_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道支付ID',
  `channel_pay_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 656007 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单支付记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pay_qdb_integration_record
-- ----------------------------
DROP TABLE IF EXISTS `pay_qdb_integration_record`;
CREATE TABLE `pay_qdb_integration_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `biz_type` tinyint(4) NOT NULL COMMENT '业务类型。20：佣金结算；21：结算；22：退款佣金退回',
  `biz_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编码。唯一识别本次对接的业务码，内部通过bizNo获取结果',
  `param_json` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求参数json串',
  `invoke_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '接口请求状态。1：处理中；2：请求成功；3：请求失败',
  `invoke_result_data` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接口请求返回结果',
  `invoke_result_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接口请求结果描述',
  `invoke_timestamp` bigint(20) NOT NULL COMMENT '接口请求时间戳。时间戳可以计算耗时',
  `invoke_response_timestamp` bigint(20) NULL DEFAULT NULL COMMENT '接口请求响应时间戳。时间戳可以计算耗时',
  `biz_status` tinyint(4) NOT NULL DEFAULT -1 COMMENT '业务状态。-1：无意义默认值；1：成功；2：失败',
  `notify_data` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回调结果数据',
  `notify_time` datetime NULL DEFAULT NULL COMMENT '回调时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标识。0：否；1：是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `refund_mq_param` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '退佣成功发更改订单状态mq参数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_biz_type_and_no`(`biz_type`, `biz_no`, `biz_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '钱袋宝对接记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pay_reverse_order
-- ----------------------------
DROP TABLE IF EXISTS `pay_reverse_order`;
CREATE TABLE `pay_reverse_order`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `channel_pay_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道支付ID',
  `reverse_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款流水ID',
  `reverse_amount` decimal(18, 2) NOT NULL COMMENT '退款金额',
  `reverse_state` int(11) NOT NULL DEFAULT 0 COMMENT '退款状态 0: 退款中,1: 退款成功,2: 退款失败',
  `reverse_time` datetime NULL DEFAULT NULL COMMENT '退款时间',
  `channel_refund_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道退款ID',
  `channel_refund_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误描述',
  `reverse_type` int(2) NOT NULL DEFAULT 0 COMMENT '退款类型 0：未结算退款；1：已结算退款',
  `business_type` int(5) NOT NULL DEFAULT 1 COMMENT '业务类型 1：订单,4：保证金',
  `business_status_type` int(2) NOT NULL DEFAULT 1 COMMENT '业务状态类型：1：正常订单退款；2：异常订单退款；3：订单补偿退款（支付金额大于订单金额）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49344 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pay_settle_commission_for_qdb
-- ----------------------------
DROP TABLE IF EXISTS `pay_settle_commission_for_qdb`;
CREATE TABLE `pay_settle_commission_for_qdb`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_refund_id` bigint(20) NOT NULL COMMENT '订单退款ID',
  `send_param` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接口请求入参',
  `send_result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接口请求返参',
  `send_param_return_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口请求返参编码',
  `notify_param` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '接口回调入参',
  `notify_param_return_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口回调入参编码',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '接口状态。1：正常；2：需要补偿',
  `num` tinyint(4) NULL DEFAULT 0 COMMENT '补偿次数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台佣金动账表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for send_message_record_coupon
-- ----------------------------
DROP TABLE IF EXISTS `send_message_record_coupon`;
CREATE TABLE `send_message_record_coupon`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `coupon_id` bigint(20) NOT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3276 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发送优惠券详细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for send_message_record_coupon_sn
-- ----------------------------
DROP TABLE IF EXISTS `send_message_record_coupon_sn`;
CREATE TABLE `send_message_record_coupon_sn`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `coupon_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发送优惠券记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for third_notice
-- ----------------------------
DROP TABLE IF EXISTS `third_notice`;
CREATE TABLE `third_notice`  (
  `id` bigint(20) NOT NULL COMMENT '通知id',
  `biz_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编号 可以为订单id/productId/售后单id',
  `notice_type` tinyint(2) NOT NULL COMMENT '通知类型 0-9:订单通知 10-19:商品通知 20-29:售后单通知',
  `notice_source` tinyint(2) NOT NULL COMMENT '通知来源 0: 牵牛花 1：旺店通 2：聚水潭 3：网店管家 4：吉客云 5: 易久批',
  `notice_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '通知唯一编码, 消息体 + 时间戳生成, 用来保证某一时间段内同一消息不重复处理',
  `notice_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '通知内容',
  `handle_status` tinyint(2) NULL DEFAULT 2 COMMENT '处理状态 0-待处理 1-处理中 2-处理成功 3-处理失败',
  `handle_result` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '处理结果',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_notice_code`(`notice_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三方推送通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for third_push_event
-- ----------------------------
DROP TABLE IF EXISTS `third_push_event`;
CREATE TABLE `third_push_event`  (
  `id` bigint(20) NOT NULL COMMENT '数据ID',
  `send_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '推送编号 可以为订单id或者 productId',
  `event_type` tinyint(2) NOT NULL COMMENT '事件类型 0-9:订单事件 10-19:商品事件 20-29:退货单',
  `send_target` tinyint(2) NOT NULL COMMENT '发送目标 1：旺店通 2：聚水潭 3：网店管家 4：吉客云',
  `send_state` tinyint(1) NOT NULL COMMENT '推送状态 0:待推送 1：推送成功 2：推送失败，3：推送中',
  `event_body` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息body',
  `send_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送返回的错误消息',
  `valid` tinyint(1) NOT NULL DEFAULT 1 COMMENT '数据核验 0/1 默认1',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三方推送事件表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
