/*
 Navicat Premium Dump SQL

 Source Server         : 易事-测试
 Source Server Type    : MySQL
 Source Server Version : 50743 (5.7.43-231000-log)
 Source Host           : ************:3306
 Source Schema         : himall_report_bbc

 Target Server Type    : MySQL
 Target Server Version : 50743 (5.7.43-231000-log)
 File Encoding         : 65001

 Date: 10/06/2025 10:40:40
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for histore_statisticswechatportrait
-- ----------------------------
DROP TABLE IF EXISTS `histore_statisticswechatportrait`;
CREATE TABLE `histore_statisticswechatportrait`  (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Range` int(11) NOT NULL COMMENT '时间维度',
  `Date` date NOT NULL COMMENT '日期',
  `Group` int(11) NOT NULL COMMENT '分组',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '键/描述',
  `Value` int(11) NOT NULL COMMENT '值',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_Date`(`Range`, `Date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3465 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for histore_statisticswechatvisit
-- ----------------------------
DROP TABLE IF EXISTS `histore_statisticswechatvisit`;
CREATE TABLE `histore_statisticswechatvisit`  (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Range` int(11) NOT NULL COMMENT '时间维度',
  `Date` datetime NOT NULL COMMENT '日期',
  `Visitors` int(11) NOT NULL COMMENT '总用户数',
  `SessionCount` int(11) NOT NULL COMMENT '小程序打开次数',
  `VisitPV` int(11) NOT NULL COMMENT '访问次数',
  `VisitUV` int(11) NOT NULL COMMENT '访问人数',
  `VisitUVNew` int(11) NOT NULL COMMENT '新用户数',
  `StayTimeUV` decimal(10, 3) NOT NULL COMMENT '人均停留时长',
  `StayTimeSession` decimal(10, 3) NOT NULL COMMENT '次均停留时长',
  `VisitDepth` decimal(10, 3) NOT NULL COMMENT '平均访问深度',
  `SharePV` int(11) NOT NULL COMMENT '转发次数',
  `ShareUV` int(11) NOT NULL COMMENT '转发人数',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_custom
-- ----------------------------
DROP TABLE IF EXISTS `report_custom`;
CREATE TABLE `report_custom`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `platform` int(255) NOT NULL COMMENT '平台',
  `groups` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组',
  `fields` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段',
  `dimension` int(10) NOT NULL COMMENT '统计纬度',
  `shops` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '门店范围',
  `products` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品范围',
  `range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间维度',
  `automatic` bit(1) NOT NULL COMMENT '自动更新',
  `times` int(4) NOT NULL COMMENT '自动更新天数',
  `start_date` date NULL DEFAULT NULL COMMENT '开始时间',
  `end_date` date NULL DEFAULT NULL COMMENT '截止时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '统计报表-配置单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_custom_record
-- ----------------------------
DROP TABLE IF EXISTS `report_custom_record`;
CREATE TABLE `report_custom_record`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `custom_id` bigint(11) NOT NULL COMMENT '自定义报表ID',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人名称',
  `dimension` int(10) NOT NULL COMMENT '维度',
  `platform` int(10) NULL DEFAULT NULL COMMENT '平台',
  `automatic` bit(1) NULL DEFAULT NULL COMMENT '自动更新',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报表名称',
  `range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间维度',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `status` int(4) NOT NULL COMMENT '状态',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件地址',
  `failure_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失败原因',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_product_trade
-- ----------------------------
DROP TABLE IF EXISTS `report_product_trade`;
CREATE TABLE `report_product_trade`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间维度',
  `date` date NOT NULL COMMENT '日期',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID',
  `platform` int(11) NOT NULL DEFAULT 0 COMMENT '来源',
  `category_first` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '一级类目名称',
  `category_second` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二级类目名称',
  `cart_users` int(10) NOT NULL DEFAULT 0 COMMENT '加购人数',
  `cart_quantity` int(10) NOT NULL DEFAULT 0 COMMENT '加购件数',
  `follow_users` int(10) NOT NULL DEFAULT 0 COMMENT '关注人数',
  `order_users` int(10) NOT NULL DEFAULT 0 COMMENT '下单人数',
  `order_orders` int(10) NOT NULL DEFAULT 0 COMMENT '下单笔数',
  `order_quantity` int(10) NOT NULL DEFAULT 0 COMMENT '下单件数',
  `order_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '下单金额',
  `payment_users` int(10) NOT NULL DEFAULT 0 COMMENT '支付人数',
  `payment_orders` int(10) NOT NULL DEFAULT 0 COMMENT '支付订单数',
  `payment_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '支付金额',
  `payment_quantity` int(10) NOT NULL DEFAULT 0 COMMENT '支付件数',
  `apply_orders` int(10) NOT NULL DEFAULT 0 COMMENT '申请订单数',
  `apply_users` int(10) NOT NULL DEFAULT 0 COMMENT '申请用户数',
  `refund_orders` int(10) NOT NULL DEFAULT 0 COMMENT '退款订单数',
  `refund_users` int(10) NOT NULL DEFAULT 0 COMMENT '退款人数',
  `refund_quantity` int(10) NOT NULL DEFAULT 0 COMMENT '退款件数',
  `refund_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '退款金额',
  `visits_users` int(10) NOT NULL DEFAULT 0 COMMENT '访客数',
  `visits_count` int(10) NOT NULL DEFAULT 0 COMMENT '访问量',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_date`(`range`, `date`, `shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 122840 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品交易统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_shop_trade
-- ----------------------------
DROP TABLE IF EXISTS `report_shop_trade`;
CREATE TABLE `report_shop_trade`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间维度',
  `date` date NOT NULL COMMENT '日期',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `province_id` int(10) NOT NULL DEFAULT 0 COMMENT '省份',
  `platform` int(4) NOT NULL DEFAULT 0 COMMENT '平台渠道',
  `user_total` int(10) NOT NULL DEFAULT 0 COMMENT '累计用户数',
  `user_new` int(10) NOT NULL DEFAULT 0 COMMENT '新增用户数',
  `cart_users` int(10) NOT NULL DEFAULT 0 COMMENT '加购会员数',
  `user_coupons` int(10) NOT NULL DEFAULT 0 COMMENT '领券人数',
  `follow_users` int(10) NOT NULL DEFAULT 0 COMMENT '关注人数',
  `order_orders` int(10) NOT NULL DEFAULT 0 COMMENT '下单订单数',
  `order_users` int(10) NOT NULL DEFAULT 0 COMMENT '下单人数',
  `order_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '下单金额',
  `payment_users` int(10) NOT NULL DEFAULT 0 COMMENT '支付人数',
  `payment_quantity` int(10) NOT NULL DEFAULT 0 COMMENT '支付件数',
  `payment_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '支付金额',
  `payment_product_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '支付商品金额',
  `payment_orders` int(10) NOT NULL DEFAULT 0 COMMENT '支付订单数',
  `payment_new_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '新客支付金额',
  `payment_new_users` int(10) NOT NULL DEFAULT 0 COMMENT '新客支付人数',
  `payment_new_orders` int(10) NOT NULL DEFAULT 0 COMMENT '新客支付订单数',
  `refund_amount` decimal(18, 3) NOT NULL DEFAULT 0.000 COMMENT '成功退款金额',
  `refund_orders` int(10) NOT NULL DEFAULT 0 COMMENT '成功退款订单数',
  `refund_quantity` int(10) NOT NULL DEFAULT 0 COMMENT '成功退款件数',
  `package_delivery` int(10) NOT NULL DEFAULT 0 COMMENT '发货包裹数',
  `package_delivery_second` bigint(10) NOT NULL DEFAULT 0 COMMENT '累计发货时长(秒)',
  `package_finish` int(10) NOT NULL DEFAULT 0 COMMENT '完成包裹数',
  `package_finish_second` bigint(10) NOT NULL DEFAULT 0 COMMENT '累计签收时长(秒)',
  `visits_users` int(10) NOT NULL DEFAULT 0 COMMENT '访客数',
  `visits_count` int(10) NOT NULL DEFAULT 0 COMMENT '访问量',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_date`(`range`, `date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4596 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '门店交易统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_cart
-- ----------------------------
DROP TABLE IF EXISTS `report_source_cart`;
CREATE TABLE `report_source_cart`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `platform` int(4) NULL DEFAULT 0 COMMENT '来源平台',
  `quantity` int(11) NULL DEFAULT NULL COMMENT '数量',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IX_Time`(`create_time`, `product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1317137 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '基础数据源表-加购数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_coupon
-- ----------------------------
DROP TABLE IF EXISTS `report_source_coupon`;
CREATE TABLE `report_source_coupon`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `record_id` bigint(20) NOT NULL COMMENT '优惠券记录',
  `receive_time` datetime NOT NULL COMMENT '领取时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_time`(`receive_time`, `shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 677599 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_follow_product
-- ----------------------------
DROP TABLE IF EXISTS `report_source_follow_product`;
CREATE TABLE `report_source_follow_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '关注会员',
  `product_id` bigint(20) NOT NULL COMMENT '关注商品',
  `follow_time` datetime NOT NULL COMMENT '关注时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_time`(`follow_time`, `product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 146 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '基础数据源-关注记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_follow_shop
-- ----------------------------
DROP TABLE IF EXISTS `report_source_follow_shop`;
CREATE TABLE `report_source_follow_shop`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '关注会员',
  `shop_id` bigint(20) NOT NULL COMMENT '关注门店',
  `follow_time` datetime NOT NULL COMMENT '关注时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_time`(`follow_time`, `shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '基础数据源-关注记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_order
-- ----------------------------
DROP TABLE IF EXISTS `report_source_order`;
CREATE TABLE `report_source_order`  (
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID',
  `order_time` datetime NOT NULL COMMENT '下单时间',
  `payment_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `delivery_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
  `finish_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `payment_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '支付金额',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `province_id` bigint(20) NULL DEFAULT NULL COMMENT '下单省份',
  PRIMARY KEY (`order_id`) USING BTREE,
  UNIQUE INDEX `idx_order`(`order_id`) USING BTREE,
  INDEX `idx_order_time`(`order_time`) USING BTREE,
  INDEX `idx_payment_time`(`payment_time`) USING BTREE,
  INDEX `idx_finish_time`(`finish_time`, `shop_id`) USING BTREE,
  INDEX `idx_delivery_time`(`delivery_time`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '源数据表-订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_order_bill
-- ----------------------------
DROP TABLE IF EXISTS `report_source_order_bill`;
CREATE TABLE `report_source_order_bill`  (
  `bill_id` bigint(20) NOT NULL,
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
  `express_company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递公司名称',
  `express_company_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司编码',
  `ship_order_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物流单号',
  `deliver_time` datetime NOT NULL COMMENT '发货时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`bill_id`) USING BTREE,
  INDEX `idx_deliver_time`(`deliver_time`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_order_item
-- ----------------------------
DROP TABLE IF EXISTS `report_source_order_item`;
CREATE TABLE `report_source_order_item`  (
  `order_item_id` bigint(20) NOT NULL COMMENT '订单项ID',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `platform` int(4) NOT NULL COMMENT '平台',
  `order_time` datetime NOT NULL COMMENT '下单时间',
  `payment_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `price` decimal(18, 2) NOT NULL COMMENT '单价',
  `quantity` int(10) NOT NULL COMMENT '购买数量',
  `discount` decimal(18, 2) NOT NULL COMMENT '优惠金额',
  `amount` decimal(18, 2) NOT NULL COMMENT '商品金额',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_item_id`) USING BTREE,
  INDEX `idx_order_time`(`order_time`) USING BTREE,
  INDEX `idx_payment_time`(`payment_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_product
-- ----------------------------
DROP TABLE IF EXISTS `report_source_product`;
CREATE TABLE `report_source_product`  (
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_spu` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品SPU',
  `thumbnail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品缩略图',
  `category_first` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一级分类名称',
  `category_second` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二级分类名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`product_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '基础源数据-商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_refund
-- ----------------------------
DROP TABLE IF EXISTS `report_source_refund`;
CREATE TABLE `report_source_refund`  (
  `refund_id` bigint(20) NOT NULL COMMENT '售后单ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `order_item_id` bigint(20) NOT NULL COMMENT '订单项ID',
  `shop_id` int(11) NOT NULL COMMENT '门店ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `platform` int(4) NOT NULL COMMENT '来源渠道',
  `sku_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格ID',
  `quantity` int(11) NOT NULL COMMENT '退货数量',
  `amount` decimal(18, 2) NOT NULL COMMENT '退款金额',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `completion_time` datetime NOT NULL COMMENT '完成时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(4) NULL DEFAULT NULL,
  PRIMARY KEY (`refund_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '基础源数据表-售后表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_region
-- ----------------------------
DROP TABLE IF EXISTS `report_source_region`;
CREATE TABLE `report_source_region`  (
  `id` int(11) NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `parent_id` int(11) NOT NULL COMMENT '上级ID',
  `level` int(10) NOT NULL COMMENT '等级',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_shop
-- ----------------------------
DROP TABLE IF EXISTS `report_source_shop`;
CREATE TABLE `report_source_shop`  (
  `shop_id` bigint(11) NOT NULL COMMENT '门店ID',
  `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店名称',
  `province_id` int(10) NOT NULL COMMENT '省份',
  `city` int(10) NOT NULL COMMENT '城市',
  `opening_time` datetime NOT NULL COMMENT '开业时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_user
-- ----------------------------
DROP TABLE IF EXISTS `report_source_user`;
CREATE TABLE `report_source_user`  (
  `user_id` bigint(20) NOT NULL,
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `province_id` int(10) NULL DEFAULT NULL COMMENT '所属省份',
  `registration_time` datetime NULL DEFAULT NULL COMMENT '注册时间',
  `first_payment_time` datetime NULL DEFAULT NULL COMMENT '首次消费时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `IX_province`(`province_id`) USING BTREE,
  INDEX `ix_registration_time`(`registration_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_source_visit
-- ----------------------------
DROP TABLE IF EXISTS `report_source_visit`;
CREATE TABLE `report_source_visit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `visitor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '访客标识',
  `shop_id` bigint(11) NULL DEFAULT NULL COMMENT '门店ID',
  `product_id` bigint(11) NULL DEFAULT NULL COMMENT '商品ID',
  `platform` int(255) NOT NULL COMMENT '来源平台',
  `page` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '访问页面',
  `create_time` datetime NOT NULL COMMENT '访问时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_time_shop`(`create_time`, `shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_user_trade
-- ----------------------------
DROP TABLE IF EXISTS `report_user_trade`;
CREATE TABLE `report_user_trade`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '维度',
  `date` date NOT NULL COMMENT '日期',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID',
  `order_orders` int(11) NOT NULL DEFAULT 0 COMMENT '下单订单数',
  `payment_orders` int(11) NOT NULL DEFAULT 0 COMMENT '支付订单数',
  `payment_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '支付商品件数',
  `payment_order_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '订单支付金额',
  `payment_product_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '商品支付金额',
  `refund_orders` int(11) NOT NULL DEFAULT 0 COMMENT '成功退款订单数',
  `refund_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '成功退款金额',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IX_Member`(`range`, `date`, `user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5946 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户交易统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_wechat_portrait
-- ----------------------------
DROP TABLE IF EXISTS `report_wechat_portrait`;
CREATE TABLE `report_wechat_portrait`  (
  `id` bigint(20) NOT NULL,
  `range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间纬度',
  `date` date NOT NULL COMMENT '日期',
  `group` int(4) NOT NULL COMMENT '分组',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段名',
  `value` int(10) NOT NULL COMMENT '值',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信小程序统计-用户画像' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for report_wechat_visit
-- ----------------------------
DROP TABLE IF EXISTS `report_wechat_visit`;
CREATE TABLE `report_wechat_visit`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间维度',
  `date` datetime NOT NULL COMMENT '日期',
  `visitors` int(11) NOT NULL COMMENT '总用户数',
  `session_count` int(11) NOT NULL COMMENT '小程序打开次数',
  `visit_pv` int(11) NOT NULL COMMENT '访问次数',
  `visit_uv` int(11) NOT NULL COMMENT '访问人数',
  `visit_uv_new` int(11) NOT NULL COMMENT '新用户数',
  `stay_time_uv` decimal(10, 3) NOT NULL COMMENT '人均停留时长',
  `stay_time_session` decimal(10, 3) NOT NULL COMMENT '次均停留时长',
  `visit_depth` decimal(10, 3) NOT NULL COMMENT '平均访问深度',
  `share_pv` int(11) NOT NULL COMMENT '转发次数',
  `share_uv` int(11) NOT NULL COMMENT '转发人数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信小程序统计-访问统计' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
