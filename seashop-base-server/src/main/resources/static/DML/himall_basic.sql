/*
 Navicat Premium Dump SQL

 Source Server         : 易事-测试
 Source Server Type    : MySQL
 Source Server Version : 50743 (5.7.43-231000-log)
 Source Host           : ************:3306
 Source Schema         : himall_basic_bbc

 Target Server Type    : MySQL
 Target Server Version : 50743 (5.7.43-231000-log)
 File Encoding         : 65001

 Date: 10/06/2025 10:32:08
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for base_agreement
-- ----------------------------
DROP TABLE IF EXISTS `base_agreement`;
CREATE TABLE `base_agreement`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `agreement_type` int(4) NOT NULL COMMENT '协议类型 枚举 agreement_type：0买家注册协议，1卖家入驻协议 3 隐私协议',
  `agreement_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '协议内容',
  `last_update_time` datetime NOT NULL COMMENT '最后修改日期',
  `status` int(11) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_STATUS`(`status`) USING BTREE,
  INDEX `IDX_TYPE`(`agreement_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_article
-- ----------------------------
DROP TABLE IF EXISTS `base_article`;
CREATE TABLE `base_article`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `category_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '文章分类ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文章标题',
  `icon_url` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文档内容',
  `add_date` datetime NULL DEFAULT NULL,
  `display_sequence` bigint(20) NOT NULL,
  `seo_title` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'SEO标题',
  `seo_description` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'SEO说明',
  `seo_keywords` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'SEO关键字',
  `is_release` tinyint(1) NOT NULL COMMENT '是否显示',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK_ArticleCategory_Article`(`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 152 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_article_category
-- ----------------------------
DROP TABLE IF EXISTS `base_article_category`;
CREATE TABLE `base_article_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `parent_category_id` bigint(20) NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文章类型名称',
  `display_sequence` bigint(20) NULL DEFAULT NULL COMMENT '显示顺序',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否为默认',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_banks
-- ----------------------------
DROP TABLE IF EXISTS `base_banks`;
CREATE TABLE `base_banks`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `bank_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '银行编码',
  `bank_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '银行名称',
  `bank_id` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 54 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_custom_form
-- ----------------------------
DROP TABLE IF EXISTS `base_custom_form`;
CREATE TABLE `base_custom_form`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_date` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_custom_form_field
-- ----------------------------
DROP TABLE IF EXISTS `base_custom_form_field`;
CREATE TABLE `base_custom_form_field`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `form_id` bigint(20) NOT NULL COMMENT '自定义表单ID',
  `field_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段名称',
  `type` int(11) NOT NULL COMMENT '类型',
  `format` int(11) NULL DEFAULT NULL COMMENT '格式',
  `option` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '选项',
  `is_required` tinyint(1) NOT NULL COMMENT '是否必填',
  `display_sequence` bigint(20) NULL DEFAULT 1 COMMENT '排序',
  `added_date` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_form_id`(`form_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 512 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_data_dictionary
-- ----------------------------
DROP TABLE IF EXISTS `base_data_dictionary`;
CREATE TABLE `base_data_dictionary`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_express_company
-- ----------------------------
DROP TABLE IF EXISTS `base_express_company`;
CREATE TABLE `base_express_company`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递名称',
  `taobao_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '淘宝编号',
  `kuaidi100_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递100对应物流编号',
  `kuaidiniao_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递鸟物流公司编号',
  `width` int(11) NOT NULL COMMENT '快递面单宽度',
  `height` int(11) NOT NULL COMMENT '快递面单高度',
  `logo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司logo',
  `background_image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司面单背景图片',
  `status` int(11) NOT NULL COMMENT '快递公司状态（0：正常，1：删除）',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `wangdiantong_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '旺店通code',
  `jushuitan_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚水潭code',
  `boluopai_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菠萝派code',
  `meituan_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '美团编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 107 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '快递公司设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_member_open_id
-- ----------------------------
DROP TABLE IF EXISTS `base_member_open_id`;
CREATE TABLE `base_member_open_id`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `open_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信OpenID',
  `union_open_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开发平台Openid',
  `union_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开发平台Unionid',
  `service_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '插件名称（Himall.Plugin.OAuth.WeiXin）',
  `app_id_type` int(255) NOT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 66295 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '登录小程序的openId' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_message_notice_setting
-- ----------------------------
DROP TABLE IF EXISTS `base_message_notice_setting`;
CREATE TABLE `base_message_notice_setting`  (
  `message_type` int(255) NOT NULL,
  `message_type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `emaill_notice` bit(1) NULL DEFAULT NULL,
  `sms_notice` bit(1) NULL DEFAULT NULL,
  `wx_notice` bit(1) NULL DEFAULT NULL,
  `template_id` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`message_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_mobile_foot_menu
-- ----------------------------
DROP TABLE IF EXISTS `base_mobile_foot_menu`;
CREATE TABLE `base_mobile_foot_menu`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '导航名称',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接地址',
  `url_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接名称',
  `menu_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示图片',
  `menu_icon_sel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '未选中显示图片',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '菜单类型（1代表微信、2代表小程序）',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺Id(0默认是平台)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 109 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '底部导航栏' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_module_product
-- ----------------------------
DROP TABLE IF EXISTS `base_module_product`;
CREATE TABLE `base_module_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `module_id` bigint(20) NOT NULL COMMENT '模块ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `display_sequence` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK_Product_ModuleProduct`(`product_id`) USING BTREE,
  INDEX `FK_TopicModule_ModuleProduct`(`module_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22787 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `base_operation_log`;
CREATE TABLE `base_operation_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `module_id` int(11) NOT NULL COMMENT '业务板块id',
  `module_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务板块名称',
  `operation_type` int(11) NOT NULL COMMENT '操作类型',
  `operation_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作类型名称',
  `operation_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `operation_user_id` bigint(20) NULL DEFAULT NULL COMMENT '操作人id',
  `operation_user_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人账号',
  `operation_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人名称',
  `operation_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作数据详情',
  `shop_id` bigint(20) NULL DEFAULT NULL COMMENT '店铺ID',
  `action_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 303105 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_photo_space
-- ----------------------------
DROP TABLE IF EXISTS `base_photo_space`;
CREATE TABLE `base_photo_space`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `photo_category_id` bigint(20) NOT NULL COMMENT '图片分组ID',
  `shop_id` bigint(20) NULL DEFAULT NULL COMMENT '店铺ID',
  `photo_name` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片名称',
  `photo_path` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片路径',
  `file_size` bigint(20) NULL DEFAULT NULL COMMENT '图片大小',
  `upload_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '图片上传时间',
  `lastUpdate_time` datetime NULL DEFAULT NULL COMMENT '图片最后更新时间',
  `rc_status` int(11) NULL DEFAULT 0 COMMENT '0待审，1已审核，2.已拒绝',
  `rc_opdateTime` datetime NULL DEFAULT NULL COMMENT '风控最后更新时间',
  `rc_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风控不通过原因',
  `risk_id` bigint(20) NULL DEFAULT 0 COMMENT '风控Id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id_category_id`(`shop_id`, `photo_category_id`, `rc_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1096740 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_photo_space_category
-- ----------------------------
DROP TABLE IF EXISTS `base_photo_space_category`;
CREATE TABLE `base_photo_space_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NULL DEFAULT NULL COMMENT '店铺ID',
  `photo_space_catrgory_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片空间分类名称',
  `displayS_sequence` bigint(20) NULL DEFAULT NULL COMMENT '显示顺序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 483 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_platform_task_info
-- ----------------------------
DROP TABLE IF EXISTS `base_platform_task_info`;
CREATE TABLE `base_platform_task_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `biz_type` int(11) NOT NULL DEFAULT 0 COMMENT '业务类型。1：导出任务',
  `task_type` int(11) NOT NULL DEFAULT 0 COMMENT '任务类型。具体的业务指定，需要唯一，最好具有一定的规则',
  `task_name` varchar(251) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `task_status` int(11) NOT NULL DEFAULT 0 COMMENT '任务状态：10-准备就绪(初始状态)，20-执行中，30-执行成功，40-执行失败',
  `begin_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `cost` int(11) NULL DEFAULT NULL COMMENT '任务执行耗时，单位毫秒',
  `total_num` bigint(8) NOT NULL DEFAULT 0 COMMENT '总记录数。多sheet导出的是所有sheet的总数',
  `success_num` bigint(8) NOT NULL DEFAULT 0 COMMENT '成功数',
  `failed_num` bigint(8) NOT NULL DEFAULT 0 COMMENT '失败数',
  `execute_param` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务执行参数',
  `execute_result` varchar(10240) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务执行结果。如果执行失败内容为部分异常内容',
  `file_path` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径，相对路径',
  `operator_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '操作人id',
  `operator_account` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作人账号',
  `retry_times` tinyint(2) NULL DEFAULT 0 COMMENT '重试次数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `env` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '环境。主要是区分uat和prd',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_operator_id_ctime`(`operator_id`, `create_time`) USING BTREE,
  INDEX `idx_task_status_ctime`(`task_status`, `create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1811471462627115161 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_region
-- ----------------------------
DROP TABLE IF EXISTS `base_region`;
CREATE TABLE `base_region`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '美团区域编号',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域名称',
  `short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域简称',
  `status` int(11) NULL DEFAULT NULL COMMENT '状态(0:正常,9删除)',
  `parent_id` bigint(20) NULL DEFAULT NULL,
  `region_level` int(11) NULL DEFAULT NULL,
  `left` int(11) NULL DEFAULT NULL COMMENT '左值',
  `right` int(11) NULL DEFAULT NULL COMMENT '右值',
  `custom` bit(1) NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `short_name`(`short_name`) USING BTREE,
  INDEX `IDX_ParentId`(`parent_id`) USING BTREE,
  INDEX `IDX_left`(`left`) USING BTREE,
  INDEX `IDX_right`(`right`) USING BTREE,
  INDEX `IDX_custom`(`custom`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 43164 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_risk
-- ----------------------------
DROP TABLE IF EXISTS `base_risk`;
CREATE TABLE `base_risk`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '风控Id',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺Id',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户Id',
  `client_type` int(11) NOT NULL DEFAULT 0 COMMENT '1.用户，2.供应商，3.平台',
  `page_type` int(11) NOT NULL DEFAULT 0 COMMENT '页面类型,2:店铺，3：微店，4：商品详情，5：个人中心',
  `type` int(11) NOT NULL COMMENT '对海商送审类型，1：文字异步机审，2：文字异步机审+人审，3：图片异步机审送审，4：图片异步机审+人审，5：视频异步机审+人审(是否入审，入参选择)',
  `biz_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '内容单元标志,区分位置，默认位置：0',
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `visit_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前访问页面',
  `business_type` int(11) NOT NULL COMMENT '1:文本，2：图片，3：视频',
  `status` int(11) NOT NULL COMMENT '处理结果：1待处理，2已通过，3已拒绝,4服务异常',
  `content_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '送审的json内容，无送审为null',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `batch_sid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `err_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '异常提示',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_risk_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 73081 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_risk_content
-- ----------------------------
DROP TABLE IF EXISTS `base_risk_content`;
CREATE TABLE `base_risk_content`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '风控项目Id',
  `risk_id` bigint(20) NOT NULL COMMENT '风控Id',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺Id',
  `business_type` int(11) NOT NULL COMMENT '1:文本，2：图片，3：视频',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片或视频路径',
  `status` int(11) NOT NULL COMMENT '审核状态（0：待审核，1：通过，2：拒绝）',
  `txt` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文本内容',
  `biz_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '内容单元标志,区分位置，默认位置：0',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `verify_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核不通过的提示文字',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_risk_id`(`risk_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100003061415 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_risk_response
-- ----------------------------
DROP TABLE IF EXISTS `base_risk_response`;
CREATE TABLE `base_risk_response`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '风控接口响应Id',
  `template_id` int(11) NOT NULL COMMENT '入参的模板Id参数',
  `type` int(11) NOT NULL COMMENT '对海商送审类型，1：文字异步机审，2：文字异步机审+人审，3：图片异步机审送审，4：图片异步机审+人审，5：视频异步机审+人审(是否入审，入参选择)',
  `trans_id` bigint(20) NOT NULL COMMENT '本次请求的唯一Id，与入参一致（RiskId）',
  `biz_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '内容单元标志,区分位置，默认位置：0',
  `code` int(11) NOT NULL COMMENT '服务是否正常，0正常，其他异常',
  `msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '如果异常则是异常消息提示',
  `risk_result_code` int(11) NOT NULL COMMENT '风控决策码，0：通过，其他拦截',
  `prompt` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提示文案，一般用于提供给前端用户的文案提示',
  `extra` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包含风控返回的跟业务方约定的其他信息字段，Map<String,Object>',
  `date_time` datetime NOT NULL COMMENT '结果产出时间',
  `biz_data` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务方透传字段',
  `business_type` int(11) NOT NULL COMMENT '1:文本，2：图片，3：视频',
  `audit_freq` int(11) NOT NULL COMMENT '1:业务送审结果，2：审核修正结果',
  `audit_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '拦截原因和明细，json内容',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '1：待平台自动任务处理，2：已处理，4：处理异常',
  `err_num` int(11) NOT NULL DEFAULT 0 COMMENT '处理错误次数，大于50次不用处理',
  `err_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消费异常提示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11025 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_seller_task_info
-- ----------------------------
DROP TABLE IF EXISTS `base_seller_task_info`;
CREATE TABLE `base_seller_task_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `biz_type` int(11) NOT NULL DEFAULT 0 COMMENT '业务类型。1：导出任务',
  `task_type` int(11) NOT NULL DEFAULT 0 COMMENT '任务类型。具体的业务指定，需要唯一，最好具有一定的规则',
  `task_name` varchar(251) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `task_status` int(11) NOT NULL DEFAULT 0 COMMENT '任务状态：10-准备就绪(初始状态)，20-执行中，30-执行成功，40-执行失败',
  `begin_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `cost` int(11) NULL DEFAULT NULL COMMENT '任务执行耗时，单位毫秒',
  `total_num` bigint(8) NOT NULL DEFAULT 0 COMMENT '总记录数。多sheet导出的是所有sheet的总数',
  `success_num` bigint(8) NOT NULL DEFAULT 0 COMMENT '成功数',
  `failed_num` bigint(8) NOT NULL DEFAULT 0 COMMENT '失败数',
  `execute_param` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务执行参数',
  `execute_result` varchar(10240) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务执行结果。如果执行失败内容为部分异常内容',
  `file_path` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径，相对路径',
  `operator_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '操作人id',
  `operator_account` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作人账号',
  `retry_times` tinyint(2) NULL DEFAULT 0 COMMENT '重试次数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `env` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '环境。主要是区分uat和prd',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_operator_id_ctime`(`operator_id`, `create_time`) USING BTREE,
  INDEX `idx_task_status_ctime`(`task_status`, `create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1811587475754549444 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_send_message_record
-- ----------------------------
DROP TABLE IF EXISTS `base_send_message_record`;
CREATE TABLE `base_send_message_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_type` int(11) NOT NULL COMMENT '消息类别',
  `content_type` int(11) NOT NULL COMMENT '内容类型',
  `send_content` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发送内容',
  `to_user_label` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送对象',
  `send_state` int(11) NOT NULL COMMENT '发送状态',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19379 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_send_message_record_coupon
-- ----------------------------
DROP TABLE IF EXISTS `base_send_message_record_coupon`;
CREATE TABLE `base_send_message_record_coupon`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `coupon_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_reference_message`(`message_id`) USING BTREE,
  INDEX `fk_reference_message_coupon`(`coupon_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3259 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发送优惠券详细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_settled
-- ----------------------------
DROP TABLE IF EXISTS `base_settled`;
CREATE TABLE `base_settled`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `business_type` int(11) NOT NULL COMMENT '商家类型 0、仅企业可入驻；1、仅个人可入驻；2、企业和个人均可',
  `settlement_account_type` int(11) NOT NULL COMMENT '商家结算类型 0、仅银行账户；1、仅微信账户；2、银行账户及微信账户均可',
  `trial_days` int(11) NOT NULL COMMENT '试用天数',
  `is_city` int(11) NOT NULL COMMENT '地址必填 0、非必填；1、必填',
  `is_people_number` int(11) NOT NULL COMMENT '人数必填 0、非必填；1、必填',
  `is_address` int(11) NOT NULL COMMENT '详细地址必填 0、非必填；1、必填',
  `is_business_license_code` int(11) NOT NULL COMMENT '营业执照号必填 0、非必填；1、必填',
  `is_business_scope` int(11) NOT NULL COMMENT '经营范围必填 0、非必填；1、必填',
  `is_business_license` int(11) NOT NULL COMMENT '营业执照必填 0、非必填；1、必填',
  `is_agency_code` int(11) NOT NULL COMMENT '机构代码必填 0、非必填；1、必填',
  `is_agency_code_license` int(11) NOT NULL COMMENT '机构代码证必填 0、非必填；1、必填',
  `is_taxpayer_to_prove` int(11) NOT NULL COMMENT '纳税人证明必填 0、非必填；1、必填',
  `company_verification_type` int(11) NOT NULL COMMENT '验证类型 0、验证手机；1、验证邮箱；2、均需验证',
  `is_s_name` int(11) NOT NULL COMMENT '个人姓名必填 0、非必填；1、必填',
  `is_s_city` int(11) NOT NULL COMMENT '个人地址必填 0、非必填；1、必填',
  `is_s_address` int(11) NOT NULL COMMENT '个人详细地址必填 0、非必填；1、必填',
  `is_sid_card` int(11) NOT NULL COMMENT '个人身份证必填 0、非必填；1、必填',
  `is_sid_card_url` int(11) NOT NULL COMMENT '个人身份证上传 0、非必填；1、必填',
  `self_verification_type` int(11) NOT NULL COMMENT '个人验证类型 0、验证手机；1、验证邮箱；2、均需验证',
  `custom_form_Json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单Id（企业）',
  `personal_custom_form_Json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义表单Id（个人）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入驻设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_site_setting
-- ----------------------------
DROP TABLE IF EXISTS `base_site_setting`;
CREATE TABLE `base_site_setting`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 301 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_slide_ad
-- ----------------------------
DROP TABLE IF EXISTS `base_slide_ad`;
CREATE TABLE `base_slide_ad`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺ID，0：平台轮播图',
  `image_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片保存URL',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片跳转URL',
  `display_sequence` bigint(20) NOT NULL COMMENT '排序',
  `type_id` int(11) NOT NULL DEFAULT 0 COMMENT '类型',
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `rc_status` int(11) NOT NULL DEFAULT 0 COMMENT '0待审，1已审核，2.已拒绝',
  `rc_update_time` datetime NULL DEFAULT NULL COMMENT '风控最后更新时间',
  `rc_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风控不通过原因',
  `risk_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '风控Id',
  `del_flag` bit(1) NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3610 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '滚动广告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_template_page
-- ----------------------------
DROP TABLE IF EXISTS `base_template_page`;
CREATE TABLE `base_template_page`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL,
  `v_shop_id` bigint(20) NOT NULL,
  `type` int(11) NOT NULL,
  `client` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `l_modules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `p_modules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `j_page` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `add_time` datetime NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`shop_id`, `v_shop_id`, `type`, `client`) USING BTREE,
  UNIQUE INDEX `IX_PageID`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 798 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_topic
-- ----------------------------
DROP TABLE IF EXISTS `base_topic`;
CREATE TABLE `base_topic`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专题名称',
  `front_cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `top_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `background_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `plat_form` int(11) NOT NULL DEFAULT 0 COMMENT '使用终端',
  `tags` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `is_recommend` tinyint(1) UNSIGNED ZEROFILL NULL DEFAULT NULL COMMENT '是否推荐',
  `self_defineText` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义热点',
  `home` tinyint(1) NULL DEFAULT 0,
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 486 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_topic_module
-- ----------------------------
DROP TABLE IF EXISTS `base_topic_module`;
CREATE TABLE `base_topic_module`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `topic_id` bigint(20) NOT NULL COMMENT '专题ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专题名称',
  `title_align` int(11) NOT NULL COMMENT '标题位置 0、left；1、center ；2、right',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK_Topic_TopicModule`(`topic_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1123 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_weixin_msg_template
-- ----------------------------
DROP TABLE IF EXISTS `base_weixin_msg_template`;
CREATE TABLE `base_weixin_msg_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_type` int(11) NOT NULL COMMENT '消息类别',
  `template_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息模板编号',
  `template_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息模板ID',
  `open_flag` tinyint(1) NOT NULL COMMENT '是否启用',
  `user_in_wx_applet` tinyint(4) UNSIGNED ZEROFILL NOT NULL DEFAULT 0000 COMMENT '是否小程序微信通知',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模版标题',
  `description` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '场景说明',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_wx_applet_form_data
-- ----------------------------
DROP TABLE IF EXISTS `base_wx_applet_form_data`;
CREATE TABLE `base_wx_applet_form_data`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `event_id` bigint(20) NOT NULL COMMENT '事件ID',
  `event_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件值',
  `form_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件的表单ID',
  `event_time` datetime NOT NULL COMMENT '事件时间',
  `expire_time` datetime NOT NULL COMMENT 'FormId过期时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 614004 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for base_wx_menu
-- ----------------------------
DROP TABLE IF EXISTS `base_wx_menu`;
CREATE TABLE `base_wx_menu`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `link_type` int(255) NOT NULL COMMENT '链接类型',
  `link_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '链接值',
  `parent_id` int(11) NOT NULL COMMENT '上级ID',
  `whether_custom` tinyint(4) NOT NULL COMMENT '是否自定义菜单',
  `create_time` datetime NOT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信公众号菜单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for refund_reason
-- ----------------------------
DROP TABLE IF EXISTS `refund_reason`;
CREATE TABLE `refund_reason`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `after_sales_text` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后原因',
  `sequence` int(11) NOT NULL DEFAULT 100 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '售后原因' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_user
-- ----------------------------
DROP TABLE IF EXISTS `test_user`;
CREATE TABLE `test_user`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `age` int(11) NULL DEFAULT NULL COMMENT '年龄',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_business_category
-- ----------------------------
DROP TABLE IF EXISTS `user_business_category`;
CREATE TABLE `user_business_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类申请ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `commission_rate` decimal(8, 2) NOT NULL COMMENT '分佣比例',
  `bond` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '保证金',
  `whether_frozen` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否冻结',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_category_businesscategory`(`category_id`) USING BTREE,
  INDEX `fk_shopid_businesscategory`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3004 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商经营分类信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_business_category_apply
-- ----------------------------
DROP TABLE IF EXISTS `user_business_category_apply`;
CREATE TABLE `user_business_category_apply`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `apply_date` datetime NOT NULL COMMENT '申请日期',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `audited_status` int(11) NOT NULL COMMENT '审核状态 0-待审核 1-已审核 2-审核拒绝',
  `audited_date` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `refuse_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `agreement_status` int(11) NOT NULL DEFAULT 0 COMMENT '签署协议状态,待签署=0,已签署=1',
  `shop_agreement_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '签署协议的对应的协议Id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id_agreement_status_audited_status`(`shop_id`, `agreement_status`, `audited_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 432 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商申请经营分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_business_category_apply_detail
-- ----------------------------
DROP TABLE IF EXISTS `user_business_category_apply_detail`;
CREATE TABLE `user_business_category_apply_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `commission_rate` decimal(8, 2) NOT NULL COMMENT '分佣比例',
  `category_id` bigint(20) NOT NULL COMMENT '类目ID',
  `apply_id` bigint(20) NOT NULL COMMENT '申请Id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fr_bussinesscate_apply`(`apply_id`) USING BTREE,
  INDEX `fr_bussinesscate_apply_cid`(`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 719 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商申请经营分类明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_business_category_form
-- ----------------------------
DROP TABLE IF EXISTS `user_business_category_form`;
CREATE TABLE `user_business_category_form`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL,
  `category_id` bigint(20) NOT NULL,
  `form_id` bigint(20) NOT NULL DEFAULT 0,
  `form_data` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 349 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '经营分类自定义表单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_customer_service
-- ----------------------------
DROP TABLE IF EXISTS `user_customer_service`;
CREATE TABLE `user_customer_service`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL,
  `tool` int(11) NOT NULL COMMENT '工具类型（qq、旺旺）',
  `type` int(11) NOT NULL,
  `name` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '客服名称',
  `account_code` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通信账号',
  `terminal_type` int(11) NOT NULL DEFAULT 0 COMMENT '终端类型',
  `server_status` int(11) NOT NULL DEFAULT 1 COMMENT '客服状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 135 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_exclusive_member
-- ----------------------------
DROP TABLE IF EXISTS `user_exclusive_member`;
CREATE TABLE `user_exclusive_member`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '供应商Id',
  `user_id` bigint(20) NOT NULL COMMENT '商家Id',
  `add_date` datetime NOT NULL COMMENT '添加时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_shop_id`(`shop_id`) USING BTREE,
  INDEX `index_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 120 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专属商家列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_favorite
-- ----------------------------
DROP TABLE IF EXISTS `user_favorite`;
CREATE TABLE `user_favorite`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `product_id` bigint(20) NOT NULL,
  `tags` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类标签',
  `date` datetime NOT NULL COMMENT '收藏日期',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_member_favorite`(`user_id`) USING BTREE,
  INDEX `fk_product_favorite`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 149 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '收藏商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_favorite_shop
-- ----------------------------
DROP TABLE IF EXISTS `user_favorite_shop`;
CREATE TABLE `user_favorite_shop`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `tags` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类标签',
  `date` datetime NOT NULL COMMENT '收藏日期',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_member_favorite`(`user_id`) USING BTREE,
  INDEX `fk_shop_favorite`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 139 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '收藏店铺表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_freight_area_content
-- ----------------------------
DROP TABLE IF EXISTS `user_freight_area_content`;
CREATE TABLE `user_freight_area_content`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `freight_template_id` bigint(20) NOT NULL COMMENT '运费模板ID',
  `area_content` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地区选择',
  `first_unit` int(11) NOT NULL COMMENT '首笔单元计量',
  `first_unit_monry` float NOT NULL COMMENT '首笔单元费用',
  `accumulation_unit` int(11) NOT NULL COMMENT '递增单元计量',
  `accumulation_unit_money` float NOT NULL COMMENT '递增单元费用',
  `whether_default` tinyint(4) NOT NULL COMMENT '是否为默认',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_freighttemalate_freight_area_content`(`freight_template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1491 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板区域信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_freight_area_detail
-- ----------------------------
DROP TABLE IF EXISTS `user_freight_area_detail`;
CREATE TABLE `user_freight_area_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `freight_template_id` bigint(20) NOT NULL COMMENT '运费模板ID',
  `freight_area_id` bigint(20) NOT NULL COMMENT '模板地区Id',
  `province_id` int(11) NOT NULL COMMENT '省份ID',
  `city_id` int(11) NOT NULL COMMENT '城市ID',
  `county_id` int(11) NOT NULL COMMENT '区ID',
  `town_ids` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '乡镇的ID用逗号隔开',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1401 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板详情' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_freight_template
-- ----------------------------
DROP TABLE IF EXISTS `user_freight_template`;
CREATE TABLE `user_freight_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运费模板名称',
  `source_address` int(11) NOT NULL COMMENT '宝贝发货地',
  `send_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送时间',
  `whether_free` int(11) NOT NULL COMMENT '是否商家负责运费',
  `valuation_method` int(11) NOT NULL COMMENT '定价方法(按体积、重量计算）',
  `shipping_method` int(11) NULL DEFAULT NULL COMMENT '运送类型（物流、快递）',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `non_sales_area_hide` bit(1) NOT NULL DEFAULT b'0' COMMENT '非销售区域是否隐藏',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `non_sales_area_product_hide` bit(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_non_sales_area_hide`(`non_sales_area_hide`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2394 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_invoice_title
-- ----------------------------
DROP TABLE IF EXISTS `user_invoice_title`;
CREATE TABLE `user_invoice_title`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `invoice_type` int(11) NOT NULL DEFAULT 1 COMMENT '发票类型（1:普通发票、2:电子发票、3:增值税发票）',
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '抬头名称',
  `code` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '税号',
  `invoice_context` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '发票明细',
  `register_address` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '注册地址',
  `register_phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '注册电话',
  `bank_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '银行帐号',
  `real_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收票人姓名',
  `cell_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收票人手机号',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收票人邮箱',
  `region_id` int(11) NOT NULL DEFAULT 0 COMMENT '收票人地址区域ID',
  `address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收票人详细地址',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否默认',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 355 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_label
-- ----------------------------
DROP TABLE IF EXISTS `user_label`;
CREATE TABLE `user_label`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `label_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 59 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家标签信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_manager
-- ----------------------------
DROP TABLE IF EXISTS `user_manager`;
CREATE TABLE `user_manager`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `sub_account` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否子账号',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `password_salt` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码加盐',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `real_name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实名称',
  `cellphone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `encryption_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'SHA-256' COMMENT '加密方式',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_user` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建人',
  `update_user` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新人',
  `ep_account_id` int(11) NULL DEFAULT NULL COMMENT 'ep账号ID',
  `whether_transfer` bit(1) NULL DEFAULT b'1',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_name`(`user_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 836 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '管理员信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_member
-- ----------------------------
DROP TABLE IF EXISTS `user_member`;
CREATE TABLE `user_member` (
           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
           `user_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
           `password` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
           `password_salt` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码加盐',
           `nick` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '昵称',
           `sex` int(11) NOT NULL DEFAULT '0' COMMENT '性别',
           `email` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮件',
           `top_region_id` int(11) DEFAULT NULL COMMENT '省份id',
           `region_id` int(11) DEFAULT NULL COMMENT '省市区id',
           `real_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '真实姓名',
           `cell_phone` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话',
           `qq` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'qq(删除)',
           `address` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '街道地址(删除)',
           `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否禁用',
           `last_login_date` datetime NOT NULL COMMENT '最后登录日期',
           `order_number` int(11) NOT NULL DEFAULT '0' COMMENT '下单次数',
           `total_amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '总消费金额（不排除退款）',
           `expenditure` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '总消费金额（不排除退款）',
           `points` int(11) NOT NULL DEFAULT '0' COMMENT '积分',
           `photo` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
           `parent_seller_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '商家父账号id',
           `remark` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
           `pay_pwd` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付密码',
           `pay_pwd_salt` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付密码加密字符',
           `invite_user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '邀请人',
           `birth_day` date DEFAULT NULL COMMENT '会员生日',
           `occupation` varchar(15) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职业',
           `net_amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '净消费金额（排除退款）',
           `last_consumption_time` datetime DEFAULT NULL COMMENT '最后消费时间',
           `platform` int(11) NOT NULL DEFAULT '0' COMMENT '用户来源终端',
           `encryption_mode` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'sha-256' COMMENT '加密方式',
           `whether_notice_join` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否不提示入驻为供应商',
           `whether_log_out` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否注销用户',
           `log_out_time` datetime DEFAULT NULL COMMENT '注销用户的时间',
           `register_source` int(11) NOT NULL DEFAULT '0' COMMENT '注册来源 0表示本系统，1表示牵牛花',
           `open_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信openid',
           `union_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信unionId',
           `wxmp_open_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信公众号openid',
           `wxmp_union_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信公众号unionId',
           `ep_account_id` int(11) DEFAULT NULL COMMENT 'ep账户id',
           `whether_transfer` bit(1) NOT NULL DEFAULT b'1',
           `create_time` datetime NOT NULL COMMENT '创建时间',
           `update_time` datetime NOT NULL COMMENT '修改时间',
           `province_id` bigint(20) DEFAULT NULL,
           `first_consumption_time` datetime DEFAULT NULL,
           PRIMARY KEY (`id`) USING BTREE,
           UNIQUE KEY `ix_user_name` (`user_name`) USING BTREE,
           KEY `ix_email` (`email`) USING BTREE,
           KEY `ix_cell_phone` (`cell_phone`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='商家信息表';
-- ----------------------------
-- Table structure for user_member_buy_category
-- ----------------------------
DROP TABLE IF EXISTS `user_member_buy_category`;
CREATE TABLE `user_member_buy_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '会员ID',
  `category_id` bigint(20) NOT NULL COMMENT '类别ID',
  `orders_count` int(11) NOT NULL DEFAULT 0 COMMENT '购买次数',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家购买力的分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_member_contact
-- ----------------------------
DROP TABLE IF EXISTS `user_member_contact`;
CREATE TABLE `user_member_contact`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_type` int(11) NOT NULL COMMENT '用户类型(0 Email 1 SMS)',
  `service_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '插件名称',
  `contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系号码',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 233 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家联系方式' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_member_label
-- ----------------------------
DROP TABLE IF EXISTS `user_member_label`;
CREATE TABLE `user_member_label`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `mem_id` bigint(20) NOT NULL COMMENT '会员ID',
  `label_id` bigint(20) NOT NULL COMMENT '标签Id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 206 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家标签信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_member_logout_record
-- ----------------------------
DROP TABLE IF EXISTS `user_member_logout_record`;
CREATE TABLE `user_member_logout_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录名称',
  `sex` int(11) NOT NULL DEFAULT 0 COMMENT '性别',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮件',
  `cell_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
  `qq` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'QQ',
  `birth_day` date NULL DEFAULT NULL COMMENT '会员生日',
  `occupation` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职业',
  `logout_time` datetime NOT NULL COMMENT '注销用户的时间',
  `member_id` bigint(20) NOT NULL COMMENT '商家Id',
  `open_id_infos` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备份信任登录信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家注销记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_order_setting
-- ----------------------------
DROP TABLE IF EXISTS `user_order_setting`;
CREATE TABLE `user_order_setting`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `purchase_min_valid_type` int(11) NULL DEFAULT NULL COMMENT '起购量校验方式：1：起购数量和起购金额同时满足 2：起购数量和起购金额满足其一',
  `purchase_min_quantity` int(11) NULL DEFAULT NULL COMMENT '起购数量',
  `purchase_min_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '起购金额',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_user` bigint(20) NOT NULL COMMENT '创建人',
  `update_user` bigint(20) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺订单设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_privilege
-- ----------------------------
DROP TABLE IF EXISTS `user_privilege`;
CREATE TABLE `user_privilege`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'url链接',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父类权限ID，祖辈则为0',
  `whether_delete` bit(1) NULL DEFAULT b'0' COMMENT '是否删除',
  `display_sequence` int(11) NULL DEFAULT NULL COMMENT '排序',
  `platform_id` tinyint(4) NOT NULL COMMENT '权限平台，0平台，1供应商，2商家',
  `power_type` tinyint(4) NULL DEFAULT NULL COMMENT '权限类型，0菜单，1接口',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `actived_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单选中图标',
  `action` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '动作',
  `shop_type` int(11) NOT NULL DEFAULT 0 COMMENT '店铺类型',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `izTab` tinyint(4) NULL DEFAULT NULL,
  PRIMARY KEY (`id`, `platform_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13287 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_privilege_copy1
-- ----------------------------
DROP TABLE IF EXISTS `user_privilege_copy1`;
CREATE TABLE `user_privilege_copy1`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'url链接',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '父类权限ID，祖辈则为0',
  `whether_delete` bit(1) NULL DEFAULT b'0' COMMENT '是否删除',
  `display_sequence` int(11) NULL DEFAULT NULL COMMENT '排序',
  `platform_id` tinyint(4) NOT NULL COMMENT '权限平台，0平台，1供应商，2商家',
  `power_type` tinyint(4) NULL DEFAULT NULL COMMENT '权限类型，0菜单，1接口',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `actived_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单选中图标',
  `action` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '动作',
  `shop_type` int(11) NOT NULL DEFAULT 0 COMMENT '店铺类型',
  PRIMARY KEY (`id`, `platform_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9014 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_restricted_area
-- ----------------------------
DROP TABLE IF EXISTS `user_restricted_area`;
CREATE TABLE `user_restricted_area`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) NOT NULL COMMENT '运费模板ID',
  `region_id` int(11) NOT NULL COMMENT '地区ID',
  `region_path` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地区全路径',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 147 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板区域限制表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `role_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '说明',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 152 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_role_privilege
-- ----------------------------
DROP TABLE IF EXISTS `user_role_privilege`;
CREATE TABLE `user_role_privilege`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `privilege_id` int(11) NOT NULL COMMENT '权限ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_role_role_privilege`(`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2578 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色权限信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shipping_address
-- ----------------------------
DROP TABLE IF EXISTS `user_shipping_address`;
CREATE TABLE `user_shipping_address`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `region_id` int(11) NOT NULL COMMENT '区域ID',
  `ship_to` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货人',
  `address` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货地址',
  `address_detail` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货人电话',
  `whether_default` bit(1) NOT NULL COMMENT '是否为默认',
  `whether_quick` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为轻松购地址',
  `longitude` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '经度',
  `latitude` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '纬度',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_member_shipping_address`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 205 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '收货地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shipping_free_group
-- ----------------------------
DROP TABLE IF EXISTS `user_shipping_free_group`;
CREATE TABLE `user_shipping_free_group`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) NOT NULL COMMENT '运费模版ID',
  `condition_type` int(11) NOT NULL COMMENT '包邮条件类型',
  `condition_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '包邮条件值',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 470 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费包邮' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shipping_free_region
-- ----------------------------
DROP TABLE IF EXISTS `user_shipping_free_region`;
CREATE TABLE `user_shipping_free_region`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) NOT NULL,
  `group_id` bigint(20) NOT NULL,
  `region_id` int(11) NOT NULL,
  `region_path` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地区全路径',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 864 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费包邮区域' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop
-- ----------------------------
DROP TABLE IF EXISTS `user_shop`;
CREATE TABLE `user_shop`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `grade_id` bigint(20) NOT NULL COMMENT '店铺等级',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `logo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '店铺LOGO路径',
  `sub_domains` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留子域名，未使用',
  `theme` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留主题，未使用',
  `whether_self` bit(1) NOT NULL COMMENT '是否是官方自营店',
  `shop_status` int(11) NOT NULL COMMENT '店铺状态',
  `refuse_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核拒绝原因',
  `adapay_status` int(11) NULL DEFAULT NULL COMMENT '汇付操作状态',
  `adapay_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '汇付创建失败原因',
  `plate_status` int(11) NULL DEFAULT NULL COMMENT '平台审核状态',
  `create_date` datetime NOT NULL COMMENT '店铺创建日期',
  `contacts_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `contacts_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contacts_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系Email',
  `general_taxpayer_phot` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一般纳税人证明',
  `bank_account_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行开户名',
  `bank_account_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司银行账号',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户银行支行名称',
  `bank_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支行联行号',
  `bank_region_id` int(11) NOT NULL COMMENT '开户银行所在地',
  `bank_photo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '开户银行许可证照',
  `bank_type` int(11) NOT NULL DEFAULT 1 COMMENT '开户银行类型（1对公，2对私）',
  `tax_registration_certificate` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税务登记证',
  `taxpayer_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税务登记证号',
  `tax_registration_certificate_photo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纳税人识别号',
  `pay_photo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '支付凭证',
  `pay_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付注释',
  `sender_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家发货人名称',
  `sender_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家发货人地址',
  `sender_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家发货人电话',
  `freight` decimal(18, 2) NOT NULL COMMENT '运费',
  `free_freight` decimal(18, 2) NOT NULL COMMENT '多少钱开始免运费',
  `amount_freight_condition` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '店铺运费-单笔订单实付金额门槛',
  `amount_freight` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '店铺单笔订单实付金额满足条件后的运费',
  `quantity_freight_condition` int(11) NOT NULL DEFAULT 0 COMMENT '店铺运费-单笔订单商品数量门槛',
  `quantity_freight` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '店铺单笔订单商品数量满足条件后的运费',
  `money_off_condition` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单笔订单满减金额门槛',
  `money_off_fee` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单笔订单满减金额',
  `money_off_overlay` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否叠加优惠',
  `stage` int(11) NOT NULL DEFAULT 0 COMMENT '注册步骤',
  `sender_region_id` int(11) NOT NULL DEFAULT 0 COMMENT '商家发货人省市区',
  `product_cert` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品证书',
  `other_cert` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '其他证书',
  `business_type` int(11) NOT NULL DEFAULT 0 COMMENT '0、企业；1、个人',
  `id_card` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `id_cardurl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '身份证URL',
  `id_cardurl2` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '身份证照片URL2',
  `auto_allot_order` bit(1) NOT NULL COMMENT '商家是否开启自动分配订单',
  `whether_auto_print` bit(1) NOT NULL DEFAULT b'0' COMMENT '商家是否开启自动打印',
  `print_count` int(11) NOT NULL DEFAULT 0 COMMENT '打印张数',
  `whether_open_top_image_ad` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启头部图片广告',
  `whether_open_hi_chat` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否注册了海商客服平台',
  `whether_pay_bond` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否缴纳保证金',
  `whether_agreement` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否签署协议',
  `whether_sms_tips` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已发送短信提醒',
  `autograph_img` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签名照',
  `id_card_expire_type` int(11) NOT NULL DEFAULT 1 COMMENT '有效期类型：1永久有效，2时间段',
  `id_card_start_date` datetime NULL DEFAULT NULL COMMENT '份证有效期开始日期',
  `id_card_end_date` datetime NULL DEFAULT NULL COMMENT '身份证有效期开始日期',
  `licence_cert_addr` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执照地址',
  `currency_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '币种',
  `introduct` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务商简介',
  `form_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义数据',
  `whether_open_exclusive_member` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启专属商家',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `order_pay_iz_send_sms` bit(1) NULL DEFAULT b'1' COMMENT '订单支付是否短信通知',
  `shop_type` int(1) NULL DEFAULT 1 COMMENT '店铺类型 1商城店铺 2牵牛花店铺 3易久批',
  `id_card_date` date NULL DEFAULT NULL,
  `serial_number` int(11) NULL DEFAULT NULL COMMENT '前端排序顺序',
  `whether_supply` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要补充资料',
  `whether_arrear` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否欠费保证金',
  `user_id` int(11) NULL DEFAULT NULL,
  `app_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `app_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_registed` tinyint(1) NULL DEFAULT NULL,
  `is_enable` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_shop_is_self`(`whether_self`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000057 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_banner
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_banner`;
CREATE TABLE `user_shop_banner`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '导航名称',
  `position` int(11) NOT NULL COMMENT '导航显示位置',
  `display_sequence` bigint(20) NOT NULL COMMENT '定位',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跳转URL',
  `platform` int(11) NOT NULL DEFAULT 0 COMMENT '显示在哪个终端',
  `url_type` int(11) NOT NULL DEFAULT 0,
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '开启或者关闭',
  `enable_delete` int(11) NOT NULL DEFAULT 1 COMMENT '能否删除',
  `rc_status` int(11) NOT NULL DEFAULT 0 COMMENT '0待审，1已审核，2.已拒绝',
  `rc_update_time` datetime NULL DEFAULT NULL COMMENT '风控最后更新时间',
  `rc_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风控不通过原因',
  `risk_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '风控Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 121 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '导航条设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_erp
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_erp`;
CREATE TABLE `user_shop_erp`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `erp_type` int(11) NOT NULL COMMENT '开启erp类型 0未开启 1旺店通 2聚水潭 3网店管家 4吉客云',
  `jst_status` bit(1) NOT NULL DEFAULT b'0' COMMENT '聚水潭授权 0未授权 1已授权',
  `jst_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚水潭授权url',
  `jst_url_create_time` datetime NULL DEFAULT NULL COMMENT '聚水潭授权url创建时间',
  `jst_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚水潭授权码',
  `jst_code_get_time` datetime NULL DEFAULT NULL COMMENT '聚水潭授权码获得时间',
  `jst_access_token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚水潭访问令牌',
  `jst_access_token_expires` int(11) NOT NULL DEFAULT 0 COMMENT '聚水潭访问令牌多少秒后过期',
  `jst_refresh_token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚水潭更新令牌',
  `jst_token_get_time` datetime NULL DEFAULT NULL COMMENT '聚水潭令牌获取时间',
  `jst_shop_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚水潭店铺编号',
  `jst_co_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聚水潭公司编号',
  `blp_token` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菠萝派供应商token',
  `wdt_token` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '旺店通访问令牌',
  `whether_send_sms` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否发送短信',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_JST_EXPIRE`(`erp_type`, `jst_token_get_time`, `jst_access_token_expires`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'erp管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_ext
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_ext`;
CREATE TABLE `user_shop_ext`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `company_region_id` int(11) NOT NULL COMMENT '公司省市区',
  `company_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司地址',
  `company_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司电话',
  `company_employee_count` int(11) NOT NULL COMMENT '公司员工数量',
  `company_registered_capital` decimal(18, 2) NOT NULL COMMENT '公司注册资金',
  `business_license_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业执照号',
  `business_license_number_photo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '营业执照',
  `business_license_region_id` int(11) NOT NULL COMMENT '营业执照所在地',
  `business_license_start` datetime NULL DEFAULT NULL COMMENT '营业执照有效期开始',
  `business_license_end` datetime NULL DEFAULT NULL COMMENT '营业执照有效期',
  `business_sphere` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '法定经营范围',
  `organization_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组织机构代码',
  `organization_code_photo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组织机构执照',
  `tax_registration_certificate` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税务登记证',
  `taxpayer_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税务登记证号',
  `tax_registration_certificate_photo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纳税人识别号',
  `legal_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '法人代表',
  `company_founding_date` datetime NULL DEFAULT NULL COMMENT '公司成立日期',
  `company_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司类型',
  `licence_cert_addr` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执照地址',
  `finance_chief` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '财务负责人',
  `finance_chief_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '财务负责人联系方式',
  `introduct` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务商简介',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4203 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺补充信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_footer
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_footer`;
CREATE TABLE `user_shop_footer`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL,
  `footer` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 127 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '页面设置-页脚编辑表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_free_shipping_area
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_free_shipping_area`;
CREATE TABLE `user_shop_free_shipping_area`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '供应商ID',
  `region_id` int(11) NOT NULL COMMENT '地区ID',
  `region_path` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地区全路径',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 780 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '包邮地区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_home_module
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_home_module`;
CREATE TABLE `user_shop_home_module`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '楼层名称',
  `enable_flag` tinyint(1) NOT NULL COMMENT '是否启用',
  `display_sequence` int(11) NOT NULL COMMENT '排序',
  `url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼层链接',
  `rc_status` int(11) NOT NULL DEFAULT 0 COMMENT '0待审，1已审核，2.已拒绝',
  `rc_update_time` datetime NULL DEFAULT NULL COMMENT '风控最后更新时间',
  `rc_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风控不通过原因',
  `risk_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '风控Id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 128 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '页面设置-楼层表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_home_module_product
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_home_module_product`;
CREATE TABLE `user_shop_home_module_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `home_module_id` bigint(20) NOT NULL,
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `display_sequence` int(11) NOT NULL COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 122 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '页面设置-楼层商品区域' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_home_module_top_img
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_home_module_top_img`;
CREATE TABLE `user_shop_home_module_top_img`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `img_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `home_module_id` bigint(20) NOT NULL,
  `display_sequence` int(11) NOT NULL,
  `rc_status` int(11) NOT NULL DEFAULT 0 COMMENT '0待审，1已审核，2.已拒绝',
  `rc_update_time` datetime NULL DEFAULT NULL COMMENT '风控最后更新时间',
  `rc_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风控不通过原因',
  `risk_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '风控Id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 127 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '页面设置-楼层轮播图' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_image_ad
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_image_ad`;
CREATE TABLE `user_shop_image_ad`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL,
  `image_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片的存放URL',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片的调整地址',
  `transverse_ad_flag` tinyint(1) NOT NULL COMMENT '是否是横向长广告',
  `type_id` int(11) NOT NULL DEFAULT 0 COMMENT '微信头像',
  `rc_status` int(11) NOT NULL DEFAULT 0 COMMENT '0待审，1已审核，2.已拒绝',
  `rc_update_time` datetime NULL DEFAULT NULL COMMENT '风控最后更新时间',
  `rc_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风控不通过原因',
  `risk_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '风控Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 162 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '顶部广告栏' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_invoice_config
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_invoice_config`;
CREATE TABLE `user_shop_invoice_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `whether_invoice` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否提供发票',
  `whether_plain_invoice` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否提供普通发票',
  `whether_electronic_invoice` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否提供电子发票',
  `plain_invoice_rate` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '普通发票税率',
  `whether_vat_invoice` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否提供增值税发票',
  `vat_invoice_day` int(11) NOT NULL DEFAULT 0 COMMENT '订单完成后多少天开具增值税发票',
  `vat_invoice_rate` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '增值税税率',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 123 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发票设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_need_sign
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_need_sign`;
CREATE TABLE `user_shop_need_sign`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '商家id',
  `whether_need_sign` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否需要重签',
  `sign_time` datetime NULL DEFAULT NULL COMMENT '签约时间',
  `whether_see` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已经弹窗查看',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商重签合同记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_open_api_setting
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_open_api_setting`;
CREATE TABLE `user_shop_open_api_setting`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺编号',
  `app_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'app_key',
  `app_secret` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'app_secret',
  `add_date` datetime NOT NULL COMMENT '增加时间',
  `last_edit_date` datetime NOT NULL COMMENT '最后重置时间',
  `whether_enable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启',
  `whether_registered` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已注册',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_open_api_setting_copy1
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_open_api_setting_copy1`;
CREATE TABLE `user_shop_open_api_setting_copy1`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺编号',
  `app_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'app_key',
  `app_secret` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'app_secret',
  `add_date` datetime NOT NULL COMMENT '增加时间',
  `last_edit_date` datetime NOT NULL COMMENT '最后重置时间',
  `whether_enable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启',
  `whether_registered` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已注册',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_risk
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_risk`;
CREATE TABLE `user_shop_risk`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '风控Id',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺Id',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户Id',
  `client_type` int(11) NOT NULL DEFAULT 0 COMMENT '1.用户，2.供应商，3.平台',
  `page_type` int(11) NOT NULL DEFAULT 0 COMMENT '页面类型,2:店铺，3：微店，4：商品详情，5：个人中心',
  `type` int(11) NOT NULL COMMENT '对海商送审类型，1：文字异步机审，2：文字异步机审+人审，3：图片异步机审送审，4：图片异步机审+人审，5：视频异步机审+人审(是否入审，入参选择)',
  `biz_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '内容单元标志,区分位置，默认位置：0',
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `visit_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前访问页面',
  `business_type` int(11) NOT NULL COMMENT '1:文本，2：图片，3：视频',
  `status` int(11) NOT NULL COMMENT '处理结果：1待处理，2已通过，3已拒绝,4服务异常',
  `content_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '送审的json内容，无送审为null',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `batch_sid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `err_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '异常提示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 312 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '风控信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_shipper
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_shipper`;
CREATE TABLE `user_shop_shipper`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '商家编号',
  `default_send_goods_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认发货地址',
  `default_get_goods_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认收货地址',
  `default_verification_flag` tinyint(1) NULL DEFAULT 0 COMMENT '默认核销地址',
  `shipper_tag` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发货点名称',
  `shipper_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发货人',
  `region_id` int(11) NOT NULL DEFAULT 0 COMMENT '区域ID',
  `address` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '具体街道信息',
  `tel_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `zip_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `wx_open_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信OpenID用于发信息到微信给发货人',
  `longitude` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '经度',
  `latitude` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '纬度',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商发/退货地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_slide_ad
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_slide_ad`;
CREATE TABLE `user_shop_slide_ad`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID，0：平台轮播图  ',
  `image_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片保存URL',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片跳转URL',
  `display_sequence` bigint(20) NOT NULL,
  `type_id` int(11) NOT NULL DEFAULT 0,
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `rc_status` int(11) NOT NULL DEFAULT 0 COMMENT '0待审，1已审核，2.已拒绝',
  `rc_update_time` datetime NULL DEFAULT NULL COMMENT '风控最后更新时间',
  `rc_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风控不通过原因',
  `risk_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '风控Id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 134 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '页面设置-轮播图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shop_store
-- ----------------------------
DROP TABLE IF EXISTS `user_shop_store`;
CREATE TABLE `user_shop_store`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `store_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店名称',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '市',
  `region` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区',
  `street` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '街道',
  `detailed_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `longitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '纬度',
  `store_contact` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人',
  `store_contact_number` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `associated_members` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联会员（英文\",\"逗号分割）',
  `business_hours` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业时间',
  `store_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态（0:冻结；1:正常）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE COMMENT '店铺id',
  INDEX `idx_store_name`(`store_name`) USING BTREE COMMENT '门店名称',
  INDEX `idx_province_city_region_street`(`province`, `city`, `region`, `street`) USING BTREE COMMENT '省市区街道'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户店铺门店表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for xxl_job_group
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_group`;
CREATE TABLE `xxl_job_group`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行器AppName',
  `title` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行器名称',
  `address_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '执行器地址类型：0=自动注册、1=手动录入',
  `address_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '执行器地址列表，多地址逗号分隔',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_info
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_info`;
CREATE TABLE `xxl_job_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
  `job_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `add_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `author` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `alarm_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报警邮件',
  `schedule_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
  `schedule_conf` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调度配置，值含义取决于调度类型',
  `misfire_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
  `executor_route_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器路由策略',
  `executor_handler` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务handler',
  `executor_param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务参数',
  `executor_block_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '阻塞处理策略',
  `executor_timeout` int(11) NOT NULL DEFAULT 0 COMMENT '任务执行超时时间，单位秒',
  `executor_fail_retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `glue_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'GLUE类型',
  `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'GLUE源代码',
  `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'GLUE备注',
  `glue_updatetime` datetime NULL DEFAULT NULL COMMENT 'GLUE更新时间',
  `child_jobid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
  `trigger_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '调度状态：0-停止，1-运行',
  `trigger_last_time` bigint(13) NOT NULL DEFAULT 0 COMMENT '上次调度时间',
  `trigger_next_time` bigint(13) NOT NULL DEFAULT 0 COMMENT '下次调度时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_lock
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_lock`;
CREATE TABLE `xxl_job_lock`  (
  `lock_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '锁名称',
  PRIMARY KEY (`lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_log
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log`;
CREATE TABLE `xxl_job_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
  `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
  `executor_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器地址，本次执行的地址',
  `executor_handler` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务handler',
  `executor_param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务参数',
  `executor_sharding_param` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务分片参数，格式如 1/2',
  `executor_fail_retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `trigger_time` datetime NULL DEFAULT NULL COMMENT '调度-时间',
  `trigger_code` int(11) NOT NULL COMMENT '调度-结果',
  `trigger_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '调度-日志',
  `handle_time` datetime NULL DEFAULT NULL COMMENT '执行-时间',
  `handle_code` int(11) NOT NULL COMMENT '执行-状态',
  `handle_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '执行-日志',
  `alarm_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `I_trigger_time`(`trigger_time`) USING BTREE,
  INDEX `I_handle_code`(`handle_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_log_report
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log_report`;
CREATE TABLE `xxl_job_log_report`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trigger_day` datetime NULL DEFAULT NULL COMMENT '调度-时间',
  `running_count` int(11) NOT NULL DEFAULT 0 COMMENT '运行中-日志数量',
  `suc_count` int(11) NOT NULL DEFAULT 0 COMMENT '执行成功-日志数量',
  `fail_count` int(11) NOT NULL DEFAULT 0 COMMENT '执行失败-日志数量',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `i_trigger_day`(`trigger_day`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 181 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_logglue
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_logglue`;
CREATE TABLE `xxl_job_logglue`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
  `glue_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'GLUE类型',
  `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'GLUE源代码',
  `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'GLUE备注',
  `add_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_registry
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_registry`;
CREATE TABLE `xxl_job_registry`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `registry_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `registry_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `registry_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_g_k_v`(`registry_group`, `registry_key`, `registry_value`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 800 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for xxl_job_user
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_user`;
CREATE TABLE `xxl_job_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账号',
  `password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `role` tinyint(4) NOT NULL COMMENT '角色：0-普通用户、1-管理员',
  `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `i_username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
