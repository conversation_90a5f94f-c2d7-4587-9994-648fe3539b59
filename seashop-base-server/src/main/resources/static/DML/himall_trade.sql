/*
 Navicat Premium Dump SQL

 Source Server         : 易事-测试
 Source Server Type    : MySQL
 Source Server Version : 50743 (5.7.43-231000-log)
 Source Host           : ************:3306
 Source Schema         : himall_trade_bbc

 Target Server Type    : MySQL
 Target Server Version : 50743 (5.7.43-231000-log)
 File Encoding         : 65001

 Date: 10/06/2025 10:40:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for advance
-- ----------------------------
DROP TABLE IF EXISTS `advance`;
CREATE TABLE `advance`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '首页广告设置',
  `is_enable` bit(1) NOT NULL COMMENT '是否开启弹窗广告',
  `img` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '广告位图片',
  `link` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片外联链接',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `is_replay` bit(1) NOT NULL COMMENT '是否重复播放',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '首页广告设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for attribute
-- ----------------------------
DROP TABLE IF EXISTS `attribute`;
CREATE TABLE `attribute`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `spec_template_id` bigint(20) NOT NULL COMMENT '规格模板ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `display_sequence` bigint(20) NOT NULL DEFAULT 0 COMMENT '排序',
  `whether_must` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为必选',
  `whether_multi` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否可多选',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_spec_template_id`(`spec_template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '属性表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for attribute_value
-- ----------------------------
DROP TABLE IF EXISTS `attribute_value`;
CREATE TABLE `attribute_value`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `attribute_id` bigint(20) NOT NULL COMMENT '属性ID',
  `value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性值',
  `display_sequence` bigint(20) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_attribute_id`(`attribute_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 192 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '属性值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for brand
-- ----------------------------
DROP TABLE IF EXISTS `brand`;
CREATE TABLE `brand`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `display_sequence` bigint(20) NOT NULL DEFAULT 0 COMMENT '顺序',
  `logo` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'LOGO',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '品牌简介',
  `meta_title` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO',
  `meta_description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO相关',
  `meta_keywords` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO相关',
  `whether_recommend` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否推荐',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for brand_apply
-- ----------------------------
DROP TABLE IF EXISTS `brand_apply`;
CREATE TABLE `brand_apply`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `shop_id` bigint(20) NOT NULL COMMENT '商家ID',
  `brand_id` bigint(20) NOT NULL COMMENT '品牌ID',
  `brand_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '品牌名称',
  `logo` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '品牌Logo',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '描述',
  `auth_certificate` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '品牌授权证书',
  `apply_mode` int(11) NOT NULL COMMENT '申请类型 1-平台已有品牌 2-新品牌',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `audit_status` int(11) NOT NULL COMMENT '审核状态 0-未审核 1-审核通过 2-审核拒绝',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `plat_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '平台备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_brand_name`(`brand_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 124 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类目名称',
  `icon` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '类目图标',
  `display_sequence` bigint(20) NOT NULL DEFAULT 0 COMMENT '排序',
  `parent_category_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '上级类目id',
  `depth` int(11) NOT NULL COMMENT '类目的深度',
  `path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类目的路径（以|分离）',
  `has_children` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否有子类目',
  `spec_template_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '规格模板Id',
  `commission_rate` decimal(8, 2) NOT NULL COMMENT '分佣比例',
  `meta_title` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO',
  `meta_description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO 相关',
  `meta_keywords` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO 相关',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `whether_show` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否显示',
  `custom_form_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '自定义表单Id',
  `whether_special` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否特殊',
  `default_status` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否默认 0-否 1-是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_spec_template_id`(`spec_template_id`) USING BTREE,
  INDEX `idx_parent_category_id`(`parent_category_id`) USING BTREE,
  INDEX `idx_path`(`path`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2592 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台类目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for category_cash_deposit
-- ----------------------------
DROP TABLE IF EXISTS `category_cash_deposit`;
CREATE TABLE `category_cash_deposit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `category_id` bigint(20) NULL DEFAULT NULL COMMENT '类名id',
  `need_pay_cash_deposit` decimal(10, 2) NULL DEFAULT NULL COMMENT '需要缴纳保证金',
  `enable_no_reason_return` bit(1) NULL DEFAULT b'1' COMMENT '允许七天无理由退货',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_uq_category_id`(`category_id`) USING BTREE COMMENT '类型唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '类目需缴纳保证金信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for collocation
-- ----------------------------
DROP TABLE IF EXISTS `collocation`;
CREATE TABLE `collocation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID自增',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组合购标题',
  `start_time` datetime NOT NULL COMMENT '开始日期',
  `end_time` datetime NOT NULL COMMENT '结束日期',
  `short_desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组合描述',
  `shop_id` bigint(20) NOT NULL COMMENT '组合购店铺ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int(11) NULL DEFAULT 1 COMMENT '状态：-1已失效,0未开始,1进行中,2已结束',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 72 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组合购信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for collocation_product
-- ----------------------------
DROP TABLE IF EXISTS `collocation_product`;
CREATE TABLE `collocation_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID自增',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `collo_id` bigint(20) NOT NULL COMMENT '组合购ID',
  `main_flag` bit(1) NOT NULL COMMENT '是否主商品,1是0否',
  `display_sequence` int(11) NOT NULL COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_COLLOPROD_PRODUCT_ID`(`product_id`) USING BTREE,
  INDEX `IDX_COLLOPROD_COLLO_ID`(`collo_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 341 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组合购商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for collocation_sku
-- ----------------------------
DROP TABLE IF EXISTS `collocation_sku`;
CREATE TABLE `collocation_sku`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID自增',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品SkuId',
  `collo_product_id` bigint(20) NOT NULL COMMENT '组合商品表ID',
  `price` decimal(18, 2) NOT NULL COMMENT '组合购价格',
  `sku_pirce` decimal(18, 2) NOT NULL COMMENT '原始价格',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_COLLOSKU_PRODUCT_ID`(`product_id`) USING BTREE,
  INDEX `IDX_COLLOSKU_SKU_ID`(`sku_id`) USING BTREE,
  INDEX `IDX_COLLOSKU_COLLO_PRODUCT_ID`(`collo_product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 479 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组合购商品SKU表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for coupon
-- ----------------------------
DROP TABLE IF EXISTS `coupon`;
CREATE TABLE `coupon`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL,
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
  `price` decimal(18, 0) NOT NULL COMMENT '价格',
  `per_max` int(11) NOT NULL COMMENT '最大可领取张数',
  `order_amount` decimal(18, 0) NOT NULL COMMENT '订单金额（满足多少钱才能使用）',
  `num` int(11) NOT NULL COMMENT '发行张数',
  `receive_num` int(11) NOT NULL DEFAULT 0 COMMENT '已领用张数',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `coupon_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '优惠券名称',
  `receive_type` int(11) NOT NULL DEFAULT 0 COMMENT '领取方式 0 店铺首页 1 积分兑换 2 主动发放',
  `use_area` int(11) NOT NULL DEFAULT 0 COMMENT '使用范围：0=全场通用，1=部分商品可用',
  `status` int(11) NULL DEFAULT 1 COMMENT '状态：-1已失效,0未开始,1进行中,2已结束',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 814 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for coupon_product
-- ----------------------------
DROP TABLE IF EXISTS `coupon_product`;
CREATE TABLE `coupon_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `coupon_id` bigint(20) NOT NULL,
  `product_id` bigint(20) NOT NULL,
  `del_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_coupon_id_product_id`(`coupon_id`, `product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26116 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券商品关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for coupon_record
-- ----------------------------
DROP TABLE IF EXISTS `coupon_record`;
CREATE TABLE `coupon_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `coupon_id` bigint(20) NOT NULL COMMENT '优惠券id',
  `coupon_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '优惠券的SN标示',
  `coupon_time` datetime NOT NULL COMMENT '领取时间',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `used_time` datetime NULL DEFAULT NULL COMMENT '使用时间',
  `order_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用的订单ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `coupon_status` int(11) NOT NULL COMMENT '优惠券状态：0未使用;1已使用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `un_idx_user_id_coupon_id`(`user_id`, `coupon_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13733 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for coupon_setting
-- ----------------------------
DROP TABLE IF EXISTS `coupon_setting`;
CREATE TABLE `coupon_setting`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `plat_form` int(11) NOT NULL COMMENT '优惠券的发行平台',
  `coupon_id` bigint(20) NOT NULL,
  `display` int(11) NOT NULL DEFAULT 1 COMMENT '是否显示',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_coupon_id`(`coupon_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1559 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for discount_active
-- ----------------------------
DROP TABLE IF EXISTS `discount_active`;
CREATE TABLE `discount_active`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺编号',
  `active_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `iz_all_product` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否全部商品',
  `active_status` int(11) NOT NULL DEFAULT 0 COMMENT '活动状态：0正常;',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 559 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '折扣活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for discount_active_product
-- ----------------------------
DROP TABLE IF EXISTS `discount_active_product`;
CREATE TABLE `discount_active_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `active_id` bigint(20) NOT NULL COMMENT '活动编号',
  `product_id` bigint(20) NOT NULL COMMENT '产品编号 -1表示所有商品',
  `del_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_active_id_product_id`(`active_id`, `product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50226 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '折扣活动商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for discount_active_rule
-- ----------------------------
DROP TABLE IF EXISTS `discount_active_rule`;
CREATE TABLE `discount_active_rule`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `active_id` bigint(20) NOT NULL COMMENT '活动编号',
  `quota` decimal(18, 2) NOT NULL COMMENT '条件（满多少）',
  `discount` decimal(18, 2) NOT NULL COMMENT '优惠（减多少）',
  `del_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_active_id`(`active_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2414 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '折扣活动规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for documents
-- ----------------------------
DROP TABLE IF EXISTS `documents`;
CREATE TABLE `documents`  (
  `document_identifier` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `text_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `author` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `doc_version` int(11) NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exclusive_price
-- ----------------------------
DROP TABLE IF EXISTS `exclusive_price`;
CREATE TABLE `exclusive_price`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `shop_id` bigint(20) NOT NULL COMMENT '供应商Id',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `product_count` int(11) NOT NULL COMMENT '商品数量',
  `member_count` int(11) NOT NULL COMMENT '会员数量',
  `status` int(11) NULL DEFAULT 1 COMMENT '状态：-1已失效,0未开始,1进行中,2已结束',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 232 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专享价格信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exclusive_price_product
-- ----------------------------
DROP TABLE IF EXISTS `exclusive_price_product`;
CREATE TABLE `exclusive_price_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `active_id` bigint(20) NOT NULL COMMENT '活动ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品Id',
  `sku_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格ID',
  `price` decimal(18, 2) NOT NULL COMMENT '专享价',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `del_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_active_id`(`active_id`) USING BTREE,
  INDEX `idx_product_id_member_id`(`product_id`, `member_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 290285 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专享价格商品关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flash_sale
-- ----------------------------
DROP TABLE IF EXISTS `flash_sale`;
CREATE TABLE `flash_sale`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `product_id` bigint(20) NOT NULL COMMENT '产品ID',
  `status` int(11) NOT NULL COMMENT '状态：1待审核,2进行中,3未通过,4已结束,5已取消,6未开始',
  `begin_date` datetime NOT NULL COMMENT '活动开始日期',
  `end_date` datetime NOT NULL COMMENT '活动结束日期',
  `limit_type` int(2) NOT NULL DEFAULT 1 COMMENT '限购类型:1商品;2规格',
  `limit_count` int(11) NOT NULL COMMENT '限购数量',
  `sale_count` int(11) NOT NULL DEFAULT 0 COMMENT '仅仅只计算在限时购里的销售数',
  `category_id` bigint(20) NOT NULL COMMENT '活动id',
  `category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动分类：默认是限时购分类',
  `url_path` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跳转路径',
  `min_price` decimal(18, 2) NOT NULL COMMENT '最小价格（多规格价格可能不一样）',
  `front_flag` bit(1) NOT NULL DEFAULT b'1' COMMENT '前端是否显示',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 600 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flash_sale_category
-- ----------------------------
DROP TABLE IF EXISTS `flash_sale_category`;
CREATE TABLE `flash_sale_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动分类：默认是限时购分类',
  `del_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时活动分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flash_sale_config
-- ----------------------------
DROP TABLE IF EXISTS `flash_sale_config`;
CREATE TABLE `flash_sale_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺ID（平台的ID默认=0）',
  `preheat` int(11) NOT NULL DEFAULT 0 COMMENT '预热时间（店铺配置）',
  `normal_purchase_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否允许正常购买（店铺配置）',
  `need_audit_flag` bit(1) NULL DEFAULT b'0' COMMENT '是否需要审核（平台配置）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时购配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flash_sale_consume_record
-- ----------------------------
DROP TABLE IF EXISTS `flash_sale_consume_record`;
CREATE TABLE `flash_sale_consume_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `flash_sale_id` bigint(20) NOT NULL COMMENT '限时购ID',
  `flash_sale_detail_id` bigint(20) NOT NULL COMMENT '限时购明细ID',
  `member_id` bigint(20) NOT NULL COMMENT '用户ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'skuId',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `consume_num` int(10) NOT NULL DEFAULT 0 COMMENT '核销数量',
  `consume_flag` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否核销:1核销;0撤销',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_order_id`(`order_id`) USING BTREE,
  INDEX `idx_flash_sale_id`(`flash_sale_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2676992 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时购核销记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flash_sale_detail
-- ----------------------------
DROP TABLE IF EXISTS `flash_sale_detail`;
CREATE TABLE `flash_sale_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `flash_sale_id` bigint(20) NOT NULL COMMENT '对应FlashSale表主键',
  `product_id` bigint(20) NOT NULL,
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格id',
  `price` decimal(18, 2) NOT NULL COMMENT '限时购时金额',
  `total_count` int(11) NOT NULL DEFAULT 0 COMMENT '活动库存',
  `limit_count` int(11) NOT NULL DEFAULT 0 COMMENT '限购数量',
  `del_flag` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_flash_id`(`flash_sale_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1343 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时活动明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for flash_sale_stock_return
-- ----------------------------
DROP TABLE IF EXISTS `flash_sale_stock_return`;
CREATE TABLE `flash_sale_stock_return`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `flash_sale_id` bigint(20) NOT NULL COMMENT '限时购ID',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'skuId',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `relation_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '关联ID（售后ID,用于做幂等）',
  `stock_return_num` int(10) NOT NULL COMMENT '库存归还数量',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_flash_sale_id`(`flash_sale_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2535450 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时购库存归还表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for full_reduction
-- ----------------------------
DROP TABLE IF EXISTS `full_reduction`;
CREATE TABLE `full_reduction`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺编号',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '店铺名称',
  `active_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
  `money_off_condition` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单笔订单满减金额门槛',
  `money_off_fee` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单笔订单满减金额',
  `money_off_over_lay` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否叠加优惠',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `active_status` int(11) NOT NULL DEFAULT 0 COMMENT '活动状态：0正常;',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 144 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '满减活动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for himall_order_pay
-- ----------------------------
DROP TABLE IF EXISTS `himall_order_pay`;
CREATE TABLE `himall_order_pay`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pay_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付流水ID',
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源订单ID',
  `pay_amount` decimal(18, 2) NOT NULL COMMENT '支付金额',
  `pay_state` bit(1) NOT NULL DEFAULT b'0' COMMENT '支付状态：0未支付；1支付成功；2支付失败',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `payment_channel` int(11) NOT NULL DEFAULT 1 COMMENT '支付渠道 1：汇付天下',
  `payment_type` int(11) NOT NULL COMMENT '支付方式 1：微信，2：支付宝，3：个人网银，4：企业网银',
  `business_type` int(11) NOT NULL DEFAULT 1 COMMENT '业务类型 1：订单,4：保证金',
  `channel_pay_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道支付ID',
  `channel_pay_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单支付记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for lwstest_order
-- ----------------------------
DROP TABLE IF EXISTS `lwstest_order`;
CREATE TABLE `lwstest_order`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `order_status` int(11) NOT NULL COMMENT '订单状态(1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中)',
  `order_date` datetime NOT NULL COMMENT '订单创建日期',
  `close_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关闭原因',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `seller_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商电话',
  `seller_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商发货地址',
  `seller_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商说明',
  `seller_remark_flag` int(11) NULL DEFAULT NULL COMMENT '供应商说明标识',
  `user_id` bigint(20) NOT NULL COMMENT '商家id',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `user_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家留言',
  `ship_to` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货人',
  `cell_phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人电话',
  `top_region_id` int(11) NOT NULL COMMENT '收货人地址省份id',
  `region_id` int(11) NOT NULL COMMENT '收货人区域id',
  `region_full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '全名的收货地址',
  `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '地址',
  `receive_longitude` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '收货地址坐标',
  `receive_latitude` decimal(18, 6) NOT NULL DEFAULT 0.000000 COMMENT '收货地址坐标',
  `freight` decimal(8, 2) NOT NULL COMMENT '运费，如果有修改，就是修改后的运费',
  `backup_freight` decimal(8, 2) NULL DEFAULT NULL COMMENT '手动修改运费后备份的原运费，只有第一次修改运费时才备份',
  `shipping_date` datetime NULL DEFAULT NULL COMMENT '发货日期',
  `is_printed` tinyint(1) NOT NULL COMMENT '是否打印快递单',
  `payment_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款类型名称',
  `payment_type` int(11) NOT NULL COMMENT '支付方式(1:线上支付)',
  `gateway_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付接口返回的id',
  `pay_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款注释',
  `pay_date` datetime NULL DEFAULT NULL COMMENT '付款日期',
  `tax` decimal(8, 2) NOT NULL COMMENT '发票税钱',
  `finish_date` datetime NULL DEFAULT NULL COMMENT '完成订单日期',
  `product_total_amount` decimal(18, 2) NOT NULL COMMENT '商品总金额',
  `refund_total_amount` decimal(18, 2) NOT NULL COMMENT '退款金额',
  `commis_total_amount` decimal(18, 2) NOT NULL COMMENT '佣金总金额',
  `refund_commis_amount` decimal(18, 2) NOT NULL COMMENT '退还佣金总金额',
  `settlement_charge` decimal(8, 4) NULL DEFAULT NULL COMMENT '结算手续费比率',
  `active_type` int(11) NOT NULL DEFAULT 0 COMMENT '活动类型',
  `platform` int(11) NOT NULL DEFAULT 0 COMMENT '来自哪个终端的订单',
  `coupon_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券抵扣金额',
  `order_type` int(11) NOT NULL DEFAULT 0 COMMENT '订单类型。0:正常购,1:组合购,2:限时购',
  `order_remarks` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注(买家留言)',
  `last_modify_time` datetime NOT NULL COMMENT '最后操作时间',
  `delivery_type` int(11) NOT NULL COMMENT '发货类型(0:快递配送)',
  `total_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '订单实付金额',
  `actual_pay_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '订单实收金额(订单实付-退款金额)',
  `discount_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '折扣金额',
  `money_off_amount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满减活动优惠金额',
  `money_off_condition` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '满足了多少金额，才参与的满减活动（如果有叠加，则是叠加后的满足金额）',
  `coupon_id` bigint(20) NOT NULL COMMENT '使用的优惠券id',
  `is_send` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否发送过短信',
  `coupon_type` int(11) NULL DEFAULT NULL COMMENT '优惠券类型（0代表商家券，1代表商家红包）',
  `order_source` int(11) NOT NULL DEFAULT 0 COMMENT '订单来源 0表示本系统，1表示牵牛花',
  `source_order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '牵牛花订单号',
  `push_erp_type` int(11) NOT NULL DEFAULT 0 COMMENT '推送erp类型',
  `push_erp_state` int(11) NOT NULL DEFAULT 0 COMMENT '推送erp结果 0未推送 1推送成功 2推送失败',
  `push_erp_msg` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送erp错误信息',
  `receive_delay` int(11) NOT NULL DEFAULT 0 COMMENT '收货延迟天数',
  `payment` int(11) NULL DEFAULT NULL COMMENT '支付方式。1：支付宝扫码；3：微信小程序；5：企业网银；6：个人网银',
  `pay_bank_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '汇付支付网银编码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `main_order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主订单号。用于区分同一批次提交的订单，取批次中第一个订单号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user`(`user_id`, `order_date`) USING BTREE,
  INDEX `idx_shop`(`shop_id`, `order_date`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE COMMENT '订单ID索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品唯一表示',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `category_id` bigint(20) NOT NULL COMMENT '类目ID',
  `category_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类目路径',
  `brand_id` bigint(20) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商品编号',
  `short_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '广告词',
  `sale_status` int(11) NOT NULL COMMENT '销售状态 1-销售中 2-仓库中 3-草稿箱',
  `audit_status` int(11) NOT NULL COMMENT '审核状态 1-待审核 2-销售中 3-未通过 4-违规下架',
  `added_date` datetime NOT NULL COMMENT '添加日期',
  `display_sequence` bigint(20) NOT NULL DEFAULT 0 COMMENT '显示顺序',
  `market_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '市场价',
  `min_sale_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '最小销售价',
  `has_sku` bit(1) NOT NULL COMMENT '是否有SKU',
  `sale_counts` bigint(20) NOT NULL DEFAULT 0 COMMENT '销售量',
  `freight_template_id` bigint(20) NULL DEFAULT NULL COMMENT '运费模板ID',
  `weight` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '重量',
  `volume` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '体积',
  `quantity` int(11) NULL DEFAULT 0 COMMENT '数量',
  `measure_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '计量单位',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `delete_version` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除版本号 为0表示未删除',
  `max_buy_count` int(11) NOT NULL DEFAULT 0 COMMENT '最大购买数',
  `whether_open_ladder` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启阶梯价格',
  `spec1_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '颜色别名',
  `spec2_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '尺码别名',
  `spec3_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '版本别名',
  `spec_name_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规格名称id的集合, 逗号隔开',
  `shop_display_sequence` int(11) NOT NULL DEFAULT 0 COMMENT '商家商品序号',
  `virtual_sale_counts` bigint(20) NOT NULL DEFAULT 0 COMMENT '虚拟销量',
  `image_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商品主图',
  `video_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商品主图视频',
  `submit_audit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交审核时间',
  `check_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '商品审核时间',
  `multiple_count` int(11) NOT NULL DEFAULT 1 COMMENT '倍数起购量',
  `whether_new_product` bit(1) NULL DEFAULT b'0' COMMENT '是否新商品',
  `source` tinyint(2) NOT NULL DEFAULT 1 COMMENT '来源 1-商城 2-牵牛花 3-易久批',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  `product_type` tinyint(1) NULL DEFAULT 1 COMMENT '商品类型（1:实物商品；2:虚拟商品）',
  `validity_period_type` tinyint(1) NULL DEFAULT NULL COMMENT '有效期类型（1:长期有效；2:天数内；3:时间段）',
  `validity_period_days` int(11) NULL DEFAULT NULL COMMENT '有效期天数',
  `validity_period_start_time` datetime NULL DEFAULT NULL COMMENT '有效期开始时间',
  `validity_period_end_time` datetime NULL DEFAULT NULL COMMENT '有效期结束时间',
  `whether_multi_verification_code` bit(1) NULL DEFAULT NULL COMMENT '是否支持多核销码',
  `virtual_product_usage_intro` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '虚拟商品使用说明',
  `enable_reserved_info` bit(1) NULL DEFAULT NULL COMMENT '是否开启预留信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_product_id`(`product_id`) USING BTREE,
  UNIQUE INDEX `un_idx_product_code`(`product_code`, `shop_id`, `delete_version`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 417 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_attribute
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute`;
CREATE TABLE `product_attribute`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `attribute_id` bigint(20) NOT NULL COMMENT '属性ID',
  `value_id` bigint(20) NOT NULL COMMENT '属性值ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品属性关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_attribute_audit
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute_audit`;
CREATE TABLE `product_attribute_audit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `attribute_id` bigint(20) NOT NULL COMMENT '属性ID',
  `value_id` bigint(20) NOT NULL COMMENT '属性值ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 296 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品属性审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_audit
-- ----------------------------
DROP TABLE IF EXISTS `product_audit`;
CREATE TABLE `product_audit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品唯一标识(美团Id组件获取)',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `category_id` bigint(20) NOT NULL COMMENT '类目ID',
  `category_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类目路径',
  `brand_id` bigint(20) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商品编号',
  `short_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '广告词',
  `sale_status` int(11) NOT NULL COMMENT '销售状态 1-销售中 2-仓库中 3-草稿箱',
  `audit_status` int(11) NOT NULL COMMENT '审核状态 1-待审核 2-销售中 3-未通过 4-违规下架',
  `added_date` datetime NOT NULL COMMENT '添加日期',
  `display_sequence` bigint(20) NULL DEFAULT 0 COMMENT '显示顺序',
  `market_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '市场价',
  `min_sale_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '最小销售价',
  `has_sku` bit(1) NOT NULL COMMENT '是否有SKU',
  `sale_counts` bigint(20) NOT NULL DEFAULT 0 COMMENT '销售量',
  `freight_template_id` bigint(20) NULL DEFAULT NULL COMMENT '运费模板ID',
  `weight` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '重量',
  `volume` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '体积',
  `quantity` int(11) NULL DEFAULT 0 COMMENT '数量',
  `measure_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '计量单位',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `delete_version` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除版本号 为0表示未删除',
  `max_buy_count` int(11) NULL DEFAULT NULL COMMENT '最大购买数',
  `whether_open_ladder` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启阶梯价格',
  `spec1_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格1别名',
  `spec2_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格2别名',
  `spec3_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格3别名',
  `spec_name_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规格名称id的集合, 逗号隔开',
  `shop_display_sequence` int(11) NULL DEFAULT 0 COMMENT '商家商品序号',
  `virtual_sale_counts` bigint(20) NOT NULL DEFAULT 0 COMMENT '虚拟销量',
  `image_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商品主图',
  `video_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商品主图视频',
  `submit_audit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交审核时间',
  `check_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '商品审核时间',
  `multiple_count` int(11) NOT NULL DEFAULT 1 COMMENT '倍数起购量',
  `whether_new_product` bit(1) NULL DEFAULT b'0' COMMENT '是否新商品',
  `source` tinyint(2) NOT NULL DEFAULT 1 COMMENT '来源 1-商城 2-牵牛花 3-易久批',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1130 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_consultation
-- ----------------------------
DROP TABLE IF EXISTS `product_consultation`;
CREATE TABLE `product_consultation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '店铺名称',
  `user_id` bigint(20) NOT NULL COMMENT '咨询用户id',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户名称',
  `email` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '邮箱',
  `consultation_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '咨询内容',
  `consultation_date` datetime NOT NULL COMMENT '咨询时间',
  `reply_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '回复内容',
  `reply_date` datetime NULL DEFAULT NULL COMMENT '回复日期',
  `risk_status` int(11) NOT NULL DEFAULT 0 COMMENT '0待审，1已审核，2.已拒绝',
  `risk_update_time` datetime NULL DEFAULT NULL COMMENT '风控最后更新时间',
  `risk_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '风控不通过原因',
  `risk_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '风控Id',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品咨询表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_description
-- ----------------------------
DROP TABLE IF EXISTS `product_description`;
CREATE TABLE `product_description`  (
  `id` bigint(20) NOT NULL DEFAULT 0 COMMENT '主键',
  `product_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `audit_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核原因',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'PC端详情',
  `description_prefix_id` bigint(20) NULL DEFAULT NULL COMMENT '关联顶部版式',
  `description_suffix_id` bigint(20) NULL DEFAULT NULL COMMENT '关联底部版式',
  `meta_title` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO',
  `meta_description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO 相关',
  `meta_keywords` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO 相关',
  `mobile_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '移动端描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1762008218558537771 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_description_audit
-- ----------------------------
DROP TABLE IF EXISTS `product_description_audit`;
CREATE TABLE `product_description_audit`  (
  `id` bigint(20) NOT NULL DEFAULT 0 COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `audit_reason` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核原因',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详情',
  `description_prefix_id` bigint(20) NULL DEFAULT NULL COMMENT '关联顶部版式',
  `description_suffix_id` bigint(20) NULL DEFAULT NULL COMMENT '关联底部版式',
  `meta_title` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO',
  `meta_description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO相关',
  `meta_keywords` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SEO相关',
  `mobile_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '移动端描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品详情审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_description_template
-- ----------------------------
DROP TABLE IF EXISTS `product_description_template`;
CREATE TABLE `product_description_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '板式名称',
  `position` int(11) NOT NULL COMMENT '位置（1-上、2-下）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'PC端版式',
  `mobile_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '移动端版式',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品版式信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_image
-- ----------------------------
DROP TABLE IF EXISTS `product_image`;
CREATE TABLE `product_image`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `image_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
  `sequence` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1389 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_image_audit
-- ----------------------------
DROP TABLE IF EXISTS `product_image_audit`;
CREATE TABLE `product_image_audit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `image_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
  `sequence` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1419 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品图片审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_ladder_price
-- ----------------------------
DROP TABLE IF EXISTS `product_ladder_price`;
CREATE TABLE `product_ladder_price`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '阶梯价格ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `min_bath` int(11) NOT NULL COMMENT '最小批量',
  `max_bath` int(11) NOT NULL COMMENT '最大批量',
  `price` decimal(18, 2) NOT NULL COMMENT '价格',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 775 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品梯度价格表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_ladder_price_audit
-- ----------------------------
DROP TABLE IF EXISTS `product_ladder_price_audit`;
CREATE TABLE `product_ladder_price_audit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '阶梯价格ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `min_bath` int(11) NOT NULL COMMENT '最小批量',
  `max_bath` int(11) NOT NULL COMMENT '最大批量',
  `price` decimal(18, 2) NOT NULL COMMENT '价格',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 775 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品阶梯价审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_relation_product
-- ----------------------------
DROP TABLE IF EXISTS `product_relation_product`;
CREATE TABLE `product_relation_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `relation` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '推荐的商品id列表，以‘，’分隔',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '推荐商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for product_reserved_info
-- ----------------------------
DROP TABLE IF EXISTS `product_reserved_info`;
CREATE TABLE `product_reserved_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `field_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `field_type` int(5) NULL DEFAULT NULL COMMENT '字段类型（1:文本；2:手机号；3:日期；4:时间；5:图片；6:身份证；7:数字）',
  `iz_required` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否必填',
  `sort` int(5) NULL DEFAULT NULL COMMENT '排序',
  `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态（是否启用）',
  `iz_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_productId_status`(`product_id`, `status`) USING BTREE COMMENT '商品ID、是否启用'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品预留信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_shop_category
-- ----------------------------
DROP TABLE IF EXISTS `product_shop_category`;
CREATE TABLE `product_shop_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `shop_category_id` bigint(20) NOT NULL COMMENT '店铺分类id',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_shop_category_id`(`shop_category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1531 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品店铺分类关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for send_message_record_coupon
-- ----------------------------
DROP TABLE IF EXISTS `send_message_record_coupon`;
CREATE TABLE `send_message_record_coupon`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `coupon_id` bigint(20) NOT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3325 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发送优惠券详细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for send_message_record_coupon_sn
-- ----------------------------
DROP TABLE IF EXISTS `send_message_record_coupon_sn`;
CREATE TABLE `send_message_record_coupon_sn`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `coupon_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 179 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发送优惠券记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop_brand
-- ----------------------------
DROP TABLE IF EXISTS `shop_brand`;
CREATE TABLE `shop_brand`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `shop_id` bigint(20) NOT NULL COMMENT '商家Id',
  `brand_id` bigint(20) NOT NULL COMMENT '品牌Id',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 61 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商品牌表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop_category
-- ----------------------------
DROP TABLE IF EXISTS `shop_category`;
CREATE TABLE `shop_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `parent_category_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '上级分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分类名称',
  `display_sequence` bigint(20) NOT NULL DEFAULT 0 COMMENT '排序',
  `whether_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 188 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '店铺分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shop_spec_value
-- ----------------------------
DROP TABLE IF EXISTS `shop_spec_value`;
CREATE TABLE `shop_spec_value`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `value_id` bigint(20) NOT NULL COMMENT '规格值ID',
  `spec` int(11) NOT NULL COMMENT '规格 1-规格1 2-规格2 3-规格3',
  `spec_template_id` bigint(20) NOT NULL COMMENT '规格模板Id',
  `value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家的规格值',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_value_id`(`value_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家规格信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sku
-- ----------------------------
DROP TABLE IF EXISTS `sku`;
CREATE TABLE `sku`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键Id',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品ID_规格1ID_规格2ID_规格3ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `spec1_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格1',
  `spec2_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格2',
  `spec3_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格3',
  `spec_value_json` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '[]' COMMENT '规格json',
  `sku_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '货号',
  `cost_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `sale_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '销售价',
  `show_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '显示图片',
  `bar_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '条码',
  `bar_code_image_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '条码图片',
  `measure_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '计量单位',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `delete_version` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除版本号 为0表示未删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_id`(`sku_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1002024 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品SKU表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sku_audit
-- ----------------------------
DROP TABLE IF EXISTS `sku_audit`;
CREATE TABLE `sku_audit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键Id',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品ID_颜色规格ID_颜色规格ID_尺寸规格',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `spec1_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '颜色规格',
  `spec2_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '尺寸规格',
  `spec3_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '版本规格',
  `spec_value_json` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '[]' COMMENT '规格json',
  `sku_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'SKU',
  `cost_price` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `sale_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '销售价',
  `show_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '显示图片',
  `bar_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '条码',
  `bar_code_image_path` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '条码图片',
  `measure_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '计量单位',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `delete_version` bigint(20) NOT NULL DEFAULT 0 COMMENT '删除版本号 为0表示未删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_id`(`sku_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE,
  INDEX `idx_shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1002178 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品SKU审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sku_stock
-- ----------------------------
DROP TABLE IF EXISTS `sku_stock`;
CREATE TABLE `sku_stock`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品ID_规格1ID_规格2ID_规格3ID',
  `sku_auto_id` bigint(20) NOT NULL COMMENT 'sku自增id',
  `sku_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '货号',
  `stock` bigint(20) NOT NULL DEFAULT 0 COMMENT '库存',
  `safe_stock` bigint(20) NOT NULL DEFAULT 0 COMMENT '警戒库存',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_sku_id`(`sku_id`) USING BTREE,
  UNIQUE INDEX `un_idx_sku_auto_id`(`sku_auto_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1756 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格库存表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sku_stock_audit
-- ----------------------------
DROP TABLE IF EXISTS `sku_stock_audit`;
CREATE TABLE `sku_stock_audit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品ID_规格1ID_规格2ID_规格3ID',
  `sku_auto_id` bigint(20) NOT NULL COMMENT 'sku_audit表的自增id',
  `sku_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '货号',
  `stock` bigint(20) NOT NULL DEFAULT 0 COMMENT '库存',
  `safe_stock` bigint(20) NOT NULL DEFAULT 0 COMMENT '警戒库存',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_sku_id`(`sku_id`) USING BTREE,
  UNIQUE INDEX `un_idx_sku_auto_id`(`sku_auto_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2074 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格库存审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sku_stock_log
-- ----------------------------
DROP TABLE IF EXISTS `sku_stock_log`;
CREATE TABLE `sku_stock_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格ID',
  `sku_auto_id` bigint(20) NOT NULL COMMENT 'Sku表里的自增id',
  `before_stock` bigint(20) NOT NULL COMMENT '修改前库存',
  `stock` bigint(20) NOT NULL COMMENT '当前库存',
  `update_type` int(11) NOT NULL COMMENT '修改类型 1-编辑商品 2-批量调整库存 3-批量导入库存 4-生成订单 5-生成订单失败 6-牵牛花API生成订单 7-售后退回 8-订单关闭 9-聚水潭同步 10-牵牛花API关闭订单 11-旺店通同步 12-牵牛花API同步库存 13-网店管家/吉客云同步库存',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3795 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sku_stock_task
-- ----------------------------
DROP TABLE IF EXISTS `sku_stock_task`;
CREATE TABLE `sku_stock_task`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `update_type` tinyint(2) NULL DEFAULT NULL COMMENT '修改类型 1-编辑商品 2-批量调整库存 3-批量导入库存 4-生成订单 5-生成订单失败6-牵牛花API生成订单 7-售后退回 8-订单关闭 9-聚水潭同步 10-牵牛花API关闭订单 11-旺店通同步 12-牵牛花API同步库存 13-网店管家/吉客云同步库存',
  `update_way` tinyint(2) NOT NULL COMMENT '更新方式 1-调整库存(加减) 2-覆盖库存',
  `update_key` tinyint(2) NOT NULL COMMENT '更新key的类型 1-根据skuId更新 2-根据skuAutoId更新 3-根据skuCode更新',
  `biz_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务编码',
  `seq_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务序号',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idx_update_type_biz_code_seq_code`(`update_type`, `biz_code`, `seq_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1624 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存变更任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sku_stock_task_info
-- ----------------------------
DROP TABLE IF EXISTS `sku_stock_task_info`;
CREATE TABLE `sku_stock_task_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint(20) NOT NULL COMMENT '库存变更任务id',
  `status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '执行状态 0-待执行 1-执行中 2-执行成功 3-执行失败',
  `error_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `product_id` bigint(20) NULL DEFAULT 0 COMMENT '商品id',
  `sku_auto_id` bigint(20) NULL DEFAULT 0 COMMENT 'sku自增id',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品ID_规格1ID_规格2ID_规格3ID',
  `sku_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '货号',
  `stock` bigint(20) NOT NULL COMMENT '任务提交的库存, 变动库存/覆盖库存',
  `shop_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '店铺id, 根据skuCode更新必传',
  `before_stock` bigint(20) NULL DEFAULT NULL COMMENT '修改前的库存',
  `change_stock` bigint(20) NULL DEFAULT NULL COMMENT '增加/减少的库存',
  `after_stock` bigint(20) NULL DEFAULT NULL COMMENT '修改后的库存',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3378 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存变更任务明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for spec_name
-- ----------------------------
DROP TABLE IF EXISTS `spec_name`;
CREATE TABLE `spec_name`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `spec_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格名',
  `spec_alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格别名',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IX_Shop`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格名表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for spec_template
-- ----------------------------
DROP TABLE IF EXISTS `spec_template`;
CREATE TABLE `spec_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格模板名称',
  `whether_support_spec1` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否支持规格1',
  `whether_support_spec2` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否支持规格2',
  `whether_support_spec3` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否支持规格3',
  `whether_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已删除',
  `spec1_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格1别名',
  `spec2_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格2别名',
  `spec3_alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规格3别名',
  `default_status` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否默认 0-否 1-是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人Id',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 99 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规格模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for spec_value
-- ----------------------------
DROP TABLE IF EXISTS `spec_value`;
CREATE TABLE `spec_value`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(11) NOT NULL COMMENT '店铺ID',
  `name_id` bigint(11) NOT NULL COMMENT '规格名',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格值',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_shop`(`shop_id`) USING BTREE,
  INDEX `ix_name`(`name_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for trade_shopping_cart
-- ----------------------------
DROP TABLE IF EXISTS `trade_shopping_cart`;
CREATE TABLE `trade_shopping_cart`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '商家id',
  `product_id` bigint(20) NOT NULL COMMENT '商品id',
  `sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'skuid',
  `quantity` bigint(20) NOT NULL COMMENT '购买数量',
  `add_time` datetime NOT NULL COMMENT '添加时间',
  `whether_select` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否选中',
  `platform` int(4) NULL DEFAULT 0 COMMENT '来源平台',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1317191 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '购物车表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for ztd_bill_data
-- ----------------------------
DROP TABLE IF EXISTS `ztd_bill_data`;
CREATE TABLE `ztd_bill_data`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `unique_number` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账单唯一ID',
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `shop_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `business_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务类型',
  `business_account_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务识别号',
  `record_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务名称',
  `deliver_date` date NOT NULL COMMENT '账单日期',
  `event_date` date NOT NULL COMMENT '事件日期',
  `amount` decimal(20, 2) NOT NULL COMMENT '账单金额',
  `payment_type` tinyint(4) NULL DEFAULT NULL COMMENT '支付类型,1-汇付',
  `payment_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付单号',
  `out_payment_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三方支付流水号',
  `out_payment_account` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付账户',
  `currency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '币种',
  `entered_amount` decimal(20, 2) NOT NULL COMMENT '原币种账单金额',
  `source_finance_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始数据来源，如finance表中的ID',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '账单类型',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `valid` tinyint(4) NOT NULL DEFAULT 1 COMMENT '数据记录是否有效，1-有效，-1-无效',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_uq_number`(`unique_number`, `business_type`, `record_name`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `ztd_bill_data_deliver_date_valid_index`(`deliver_date`, `valid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '中途岛传账记录表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
