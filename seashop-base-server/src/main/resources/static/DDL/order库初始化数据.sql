
-- 汇付
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (1, 1, '汇付支付开关', 'izOpen', 'true', '是否开启汇付支付', '2023-11-20 10:02:02', '2024-06-20 15:11:17');
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (2, 1, 'Adapay App_ID', 'appId', 'app_39b69a1c-d9ad-4e30-bb29-d80833b86885', '汇付天下中的AppId', '2023-11-20 10:05:11', '2024-12-13 15:59:51');
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (3, 1, 'mock 模式API_KEY', 'apiMockKey', 'api_test_000fc218-bd56-4ea8-9609-8439687ad2bc', '汇付天下中的ApiMockKey', '2023-11-20 10:05:39', '2023-12-25 10:17:06');
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (4, 1, 'prod 模式API_KEY', 'apiKey', 'api_live_54054387-f98b-4f13-ad20-b6ebcbd07da4', '汇付天下中的ApiKey', '2023-11-20 10:06:15', '2023-11-20 10:37:10');
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (5, 1, 'Adapay RSA 公钥', 'rsaProductKey', 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB', '汇付天下中的AdapayRSAKey', '2023-11-20 10:06:31', '2023-11-20 10:18:32');
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (6, 1, '商户 RSA 私钥', 'rsaPrivateKey', 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIYS3BfoZ2+g0eY21IZwvllVccKEcSwzrJUwLnWJ8vFvSM5mU5aPkMkhO1m5GR2aDJEz16hBuEK1ezzP73R5Sh6Qne2kXHWgCO6vKRWJ/PeAm8xBKddHKO3Xdg9MW1tVQoHA2pPV1ru2KOXT93YWOIKUlWTFpnelpgrIZLjZME2XAgMBAAECgYATy30LWpjK9meHIdlG8CZqch8VpRBAgnCcpjx1xiREWTXao2j79b5es7VbjeSTZkcsuQbCJNHbp4fGdrzX6YBzw/xR2gdnE/A4lRfcgvJ62wFBIbq4MZYevBmBX5Ng2AuxbdEbJ87Kd+zpFFHb6x0aB4aJjUSx5EpQaoiRuhBRIQJBAMWcgi44hvJQNnx9fVZZWs0ZyUDgf9hTVUEcJ+LyaUeNOXcsrP13NJ98PMDtSyhiVEsAmwugZDc2uWRbcbYUyBMCQQCtsE5m+UEH6ALg2lQUVyfuxORWb2OYXSeA3JBJN9RvzBo2WfQnzJO3Vs6BV2qcvqgUcKv+b4DwLPHODPmZyxztAkBvADwL1IrQ4AfLI/5cm7KqlPp8a97EWAMCoNsy2vISVBzceYbulaBEmdfSkzhthdZNjxiIjl7cuOuomMkl+0RrAkEAgEpEbszWmtdlIN5C0k9aAIPPwIRABS9xWT4RGPOy5uzTw6eHrsntpbLpjyGZbrNohMiAUcvcagpYhICS8GTVNQJBAMWPBEHATBX/Ka/5d/WC7SmIZ96d24NDJQDwlo0ufbZAp791HGoujq/w+SAySoQfA3VnVQyIkaqQlKou7Q5MS5A=', '汇付天下中的PrivateKey', '2023-11-20 10:06:50', '2024-11-19 15:29:06');
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (7, 1, 'DeviceId', 'deviceId', 'hishop1905', '汇付天下中的DeviceId', '2023-11-20 10:14:24', '2023-11-20 10:14:24');
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (8, 1, '调试模式', 'izDebug', 'true', 'debug 模式，开启后有详细的日志', '2023-11-20 20:00:27', '2024-06-20 15:11:26');
INSERT INTO `pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (9, 1, '环境设置', 'prodMode', 'true', 'prodMode 模式，默认为生产模式，false可以使用mock模式', '2023-11-20 20:02:03', '2024-06-20 15:11:28');

-- 财务手续费
INSERT INTO `finance_settlement_config` (`id`, `settlement_interval`, `settlement_fee_rate`, `wx_fee_rate`, `qdb_fee_rate`, `create_time`, `update_time`, `settlement_time`) VALUES (1, 1, 0.25000, 0.65000, 0.00000, '2024-03-25 10:34:18', '2024-03-25 10:34:18', 0);
