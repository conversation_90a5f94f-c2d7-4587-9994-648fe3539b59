INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (1, 1, '汇付支付开关', 'izOpen', 'true', '是否开启汇付支付', '2023-11-20 10:02:02', '2024-06-20 15:11:17');
INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (2, 1, 'Adapay App_ID', 'appId', '', '汇付天下中的AppId', '2023-11-20 10:05:11', '2024-12-13 15:59:51');
INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (3, 1, 'mock 模式API_KEY', 'apiMockKey', '', '汇付天下中的ApiMockKey', '2023-11-20 10:05:39', '2023-12-25 10:17:06');
INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (4, 1, 'prod 模式API_KEY', 'apiKey', '', '汇付天下中的ApiKey', '2023-11-20 10:06:15', '2023-11-20 10:37:10');
INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (5, 1, 'Adapay RSA 公钥', 'rsaProductKey', '', '汇付天下中的AdapayRSAKey', '2023-11-20 10:06:31', '2023-11-20 10:18:32');
INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (6, 1, '商户 RSA 私钥', 'rsaPrivateKey', '', '汇付天下中的PrivateKey', '2023-11-20 10:06:50', '2024-11-19 15:29:06');
INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (7, 1, 'DeviceId', 'deviceId', '', '汇付天下中的DeviceId', '2023-11-20 10:14:24', '2023-11-20 10:14:24');
INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (8, 1, '调试模式', 'izDebug', '', 'debug 模式，开启后有详细的日志', '2023-11-20 20:00:27', '2024-06-20 15:11:26');
INSERT INTO `himall_order_bbc`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (9, 1, '环境设置', 'prodMode', '', 'prodMode 模式，默认为生产模式，false可以使用mock模式', '2023-11-20 20:02:03', '2024-06-20 15:11:28');