INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (11, 2, '是否开启微信支付', 'izOpen', 'true', '是否开启微信支付', '2024-09-03 10:28:44', '2024-09-10 17:16:56');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (12, 2, '是否开启服务商模式', 'serviceMode', '', '是否开启服务商模式', '2024-09-02 08:41:15', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (13, 2, '服务商商户号', 'serviceMchId', '', '服务商商户号', '2024-09-02 08:42:26', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (14, 2, '服务商appId', 'serviceAppId', '', '服务商appId', '2024-09-02 08:42:51', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (15, 2, 'apiV3 秘钥值', 'apiV3Key', '', 'apiV3 秘钥值', '2024-09-02 08:43:28', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (16, 2, '服务商p12证书地址', 'serviceP12KeyPath', '', '服务商p12证书地址', '2024-09-02 08:46:25', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (17, 2, '商户号', 'mchId', '', '商户号', '2024-09-02 08:52:21', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (18, 2, 'p12证书地址', 'p12KeyPath', '', 'p12证书地址', '2024-09-02 08:53:55', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (19, 2, '是否开启小程序支付', 'enableApplet', '', '是否开启小程序支付', '2024-09-02 08:54:36', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (20, 2, '小程序支付应用Id', 'miniProgramAppId', '', '小程序支付应用Id', '2024-09-02 09:00:22', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (21, 2, '是否开启H5/微商城支付', 'enableH5', '', '是否开启H5/微商城支付', '2024-09-02 08:55:45', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (22, 2, '公众号支付应用Id', 'officialAccountAppId', '', '公众号支付应用Id', '2024-09-02 09:01:40', '2025-06-25 15:03:19');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (23, 2, '是否开启Native支付', 'enableNative', '', '是否开启Native支付', '2024-09-02 08:56:48', '2025-06-25 15:03:20');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (24, 2, 'Native支付应用Id', 'nativeAppId', '', 'Native支付应用Id', '2024-09-02 09:02:19', '2025-06-25 15:03:20');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (25, 2, '是否开启App支付', 'enableApp', '', '是否开启App支付', '2024-09-05 08:58:39', '2025-06-25 15:03:20');
INSERT INTO `himall_order`.`pay_channel_config` (`id`, `payment_channel`, `config_name`, `config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES (26, 2, 'App支付应用AppId', 'applicationAppId', '', 'App支付应用AppId', '2024-09-05 08:59:23', '2025-06-25 15:03:20');
