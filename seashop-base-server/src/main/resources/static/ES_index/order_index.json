{"mappings": {"properties": {"actualPayAmount": {"type": "double"}, "cellPhone": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "finishDate": {"type": "long"}, "flashSaleId": {"type": "long"}, "gatewayOrderId": {"type": "keyword"}, "hasCommented": {"type": "integer"}, "invoiceType": {"type": "integer"}, "orderDate": {"type": "long"}, "orderId": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "orderInvoice": {"properties": {"address": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "bankName": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "bankNo": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "cellPhone": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "email": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "invoiceCode": {"type": "keyword"}, "invoiceContext": {"type": "keyword"}, "invoiceTitle": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "invoiceType": {"type": "long"}, "realName": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "regionId": {"type": "long"}, "registerAddress": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "registerPhone": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "vatInvoiceDay": {"type": "long"}}}, "orderItems": {"type": "nested", "properties": {"flashSaleId": {"type": "long"}, "id": {"type": "long"}, "itemId": {"type": "long"}, "orderDate": {"type": "long"}, "orderId": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "orderStatus": {"type": "integer"}, "productId": {"type": "long"}, "brandId": {"type": "long"}, "brandName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word", "search_analyzer": "ik_smart"}, "productName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word", "search_analyzer": "ik_smart"}, "quantity": {"type": "long"}, "realTotalPrice": {"type": "double"}, "shopId": {"type": "long"}, "skuAutoId": {"type": "long"}, "skuId": {"type": "keyword"}, "userId": {"type": "long"}, "oeCode": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "brandCode": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "adaptableCar": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word", "search_analyzer": "ik_smart"}, "partSpec": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word", "search_analyzer": "ik_smart"}, "userName": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}}}, "orderSource": {"type": "integer"}, "orderStatus": {"type": "integer"}, "payChannel": {"type": "long"}, "payDate": {"type": "long"}, "payId": {"type": "keyword"}, "payMethod": {"type": "integer"}, "payNo": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "payment": {"type": "long"}, "paymentType": {"type": "integer"}, "platform": {"type": "integer"}, "productCount": {"type": "integer"}, "productTotalAmount": {"type": "double"}, "quantity": {"type": "long"}, "shopId": {"type": "long"}, "shopName": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "totalAmount": {"type": "double"}, "tradeNo": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "userId": {"type": "long"}, "userName": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}, "userPhone": {"type": "keyword", "fields": {"text": {"type": "text"}, "wildcard": {"type": "wildcard"}}}}}, "settings": {"number_of_shards": "1", "number_of_replicas": "1"}}