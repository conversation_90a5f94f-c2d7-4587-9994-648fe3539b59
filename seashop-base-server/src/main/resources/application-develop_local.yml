server:
  port: 8082
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
  elasticsearch:
    uris: http://**************:9200
  mvc:
    static-path-pattern: /static/**
  web:
    resources:
      static-locations: classpath:/static/
  freemarker:
    charset: UTF-8
    suffix: .ftl
    template-loader-path: classpath:/templates/
    request-context-attribute: request
    settings:
      number_format: 0.##########

# xxl:
#   job:
#     server:
#       #演示环境，不需要xxljob，这里配置错误地址
#       #addresses: https://histore.cce.35hiw.com/xxl-job-server
#       addresses: https://localhost:18081/xxl-job-server
#       accessToken: 'default_token'
#     executor:
#       appname: 'sss'

#邮件
email:
  channelId: 514
#微信小程序第三方接口
wechat:
  getTokenUrl: https://api.weixin.qq.com/cgi-bin/token
  addTemplate: https://api.weixin.qq.com/wxaapi/newtmpl/addtemplate
  removeTemplate: https://api.weixin.qq.com/wxaapi/newtmpl/deltemplate
  sendSubscribeMsg: https://api.weixin.qq.com/cgi-bin/message/subscribe/send
  tokenExpireTime: 7000
express:
  bizType: 47


logging:
  level:
    org.apache.rocketmq: debug
    com.sankuai.shangou.seashop.base: debug
    com.baomidou.mybatisplus: info

mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sankuai.shangou.seashop.base.dao
  global-config:
    banner: true
  check-config-location: true

pagehelper:
  helperDialect: mysql
  reasonable: true
  defaultCount: true
hishop:
  storage:
    storage-type: OBS
    bucket-name: himall-test
    endpoint: https://obs.cn-south-1.myhuaweicloud.com
    access-key: UEDFC3T2O7J1RXQQIAVO
    secret-key: FzRPLhtiitbYO3MTrXxArFLUT3KBIkIdmw9MIKEL
    domain: https://himall-obs.35hiw.com
    base-path: /${spring.profiles.active}/rs/himall-base
    rest: true
  logistics:
    enabled: true
    app-key: 10000742
    secret-key: 9c85962d5fc8900fa48cc4109bbde1c7
  sms:
    dev: true
    allow-mobiles:
      - 15116451260
#电子面单相关配置 begin
current:
  domain:
    url: http://localhost:8080
cerberus:
  lock:
    env: offline
    category: sg-seashop-cache
#cache
squirrel:
  category: sg-seashop-cache
  # Squirrel
  clusterName: redis-sg-common_qa
venus:
  hostName: http://p0.inf.test.sankuai.com/seashopimagetest/
  bucket: seashopimagetest
  clientId: 8scckx46q4cjn4kq000000000054eb54
s3plus:
  enabled: false
  hostName: https://himall-storage-1259069382.cos.ap-nanjing.myqcloud.com/web/Storage
  bucketName: seashop-test
  downloadUrlHeader: https://msstest.vip.sankuai.com/seashop-test/seashop-test/

wx:
  appKey: wx5a538cdb4b7b286d
  appSecret: 8517959f6e6dc5537995ae4a643d3649

password:
  black:
    list: 123456,123456789,000000,111111,123123,5201314,666666,123321,1314520,1234567890,888888,1234567,654321,12345678,520520,7758521,112233,147258,123654,987654321,88888888,147258369,666888,5211314,521521,zxcvbnm,999999,222222,123123123,1314521,201314,woaini,789456,555555,qwertyuiop,100200,168168,qwerty,258369,456789,110110,789456123,159357,123789,123456a,121212,456123,987654,111222,1111111111,7758258,00000000,admin,administrator,333333,1111111,369369,888999,asdfgh,11111111,woaini1314,258258,0123456789,369258,aaaaaa,778899,0000000000,0000000,159753,abc123,585858,asdfghjkl,321654,211314,584520,abcdefg,777777,0123456,a123456789,a123456,123654789,abc123456,336699,abcdef,518518,888666,708904,135246,12345678910,147369,110119,qq123456,789789,251314,555666,111111111,123000,zxcvbn,qazwsx,123456abc,!@#$%^\u0026*,000000,00000000,000000000,0000000000,0000000000000000,00000000000000000000,000000a,00001111,00112233,00123456,007007007,01010101,01020304,01234567,012345678,0123456789,05962514787,0987654321,100200,100200300,10101010,10203040,1029384756,110110,110110110,110119120,110120,110120119,110120130,11110000,111111,11111111,111111111,1111111111,11111111111,1111111111111111,11111111111111111111,11111111a,1111111a,111111a,111111aa,111111qq,11112222,1111aaaa,1111qqqq,111222333,112112112,112233,11223344,1122334455,112233445566,11235813,1123581321,119119119,120120120,121121121,12121212,1212121212,12131415,1223334444,123000,12300000,12301230,123123,12312300,12312312,123123123,123123123123,123123456,123123a,123123aa,123123qq,123321,12332100,123321123,123321123321,1233211234567,123321aa,12341234,12344321,1234512345,123454321,1234554321,123456,123456.,123456..,12345600,123456000,12345612,123456123,123456123456,123456456,123456654321,12345678,123456780,123456789,123456789.,123456789..,1234567890,12345678900,1234567890123,1234567891,12345678910,123456789123,1234567891234567,1234567899,123456789a,123456789abc,123456789q,123456789qq,12345678A@#,12345678a,12345679,123456798,1234567a,1234567b,12345687,123456987,123456a,123456aa,123456aaa,123456ab,123456abc,123456abcd,123456as,123456asd,123456q,123456qaz,123456qq,123456qw,123456qwe,123456zx,12345abcde,12345qwert,12348765,1234abcd,1234asdf,1234qwer,123654,12365478,123654789,12369874,123698745,123789456,123abc,123qwe,123qwe123,123qweasd,12qwaszx,13131313,13141314,1314520,13145200,13145201314520,1314520520,1314521,13572468,135792468,1357924680,147147147,147258,147258369,1472583690,147852369,14789632,147896325,159159159,159357,159753,168168168,16897168,19491001,19821010,19830209,19841001,19841010,19841012,19841015,19841018,19841020,19841023,19841024,19841025,19841026,19841028,19850603,19851010,19851019,19851212,19861010,19861012,19861015,19861016,19861018,19861020,19861026,19861028,19861111,19861123,19861210,19861212,19861215,19870623,19871010,19871011,19871020,19871021,19871023,19871024,19871025,19871028,19871125,19871212,19871987,19881010,19881011,19881020,19881028,19881120,19881128,19881212,19881220,19890306,19890309,1A2B3C4D,1QAZ2WSX,1a2b3c4d,1q1q1q1q,1q2w3e,1q2w3e4r,1q2w3e4r5t,1qaz1qaz,1qaz2wsx,1qaz2wsx3edc,1qazxsw2,20080808,20082008,20092009,20102010,21212121,22222222,222222222,23232323,23456789,25251325,25257758,258258258,299792458,3.1415926,31415926,314159265,3141592653,3141592654,321321321,321654987,33333333,333333333,369258147,369369369,369852147,44444444,45612300,456123789,456456456,456789123,456852,518518518,5201314,52013140,5201314123,52013141314,5201314520,52013145201314,5201314a,520520,520520520,5211314,521521,521521521,55555555,5555555555,55667788,56565656,584131420,584131421,5841314520,5841314521,584520,5845201314,5845211314,58585858,666666,66666666,666666666,6666666666,66668888,66778899,74108520,741852963,753951,7708801314520,7758258,7758521,77585210,77585211,7758521521,77585217758521,77777777,77889900,78787878,789456123,7894561230,789654123,789789789,84131421,87654321,88771024,88888888,888888888,8888888888,88889999,88982866,89898989,911911911,963852741,987456321,98765432,987654321,9876543210,98989898,99998888,99999999,999999999,A123456789,AAAAAAAA,ASDFGHJKL,Aa123456,P@ssw0rd,QAZ123,QAZWSXEDC,QWERTYUIOP,a000000,a0000000,a00000000,a111111,a1111111,a11111111,a123123,a123123123,a123321,a123456,a1234567,a12345678,a12345678123456,a123456789,a123456a,a1b2c3,a1b2c3d4,a1s2d3f4,a5201314,aa123123,aa123456,aa123456789,aaa123,aaa123123,aaa123456,aaaa1111,aaaaaa,aaaaaaaa,aaaaaaaaa,aaaaaaaaaa,ab123456,abc123,abc12345,abc123456,abc12345678,abc123456789,abcd123,abcd1234,abcd123456,abcde12345,abcdefgh,admin123,administrator,aini1314,aptx4869,as1234,as123456,asasasas,asd123,asd123123,asd12345,asd123456,asdasd,asdasd123,asdasdasd,asdf1234,asdf123456,asdfasdf,asdfghjk,asdfghjkl,asdfjkl;,buzhidao,caonima,caonima123,cccccccc,chenchen,code8925,computer,csdn.net,csdncsdn,dddddddd,dearbook,dgdg7234322,dongdong,ds760206,ffffffff,gggggggg,goodluck,google250,hahahaha,hello123,helloworld,hhhhhhhh,hyjzstx8,iloveyou,imissyou,internet,jingjing,jjjjjjjj,justdoit,kingcom5,kkkkkkkk,lb851210,li123456,liangliang,lilylily,liu123456,llllllll,longlong,love1314,lovelove,meiyoumima,miaomiao,microsoft,mingming,mm123456,mmmmmmmm,ms0083jxj,newhappy,nicholas,nihao123,p@ssw0rd,passw0rd,password,pppppppp,q1111111,q123456,q1234567,q12345678,q123456789,q1w2e3,q1w2e3r4,q1w2e3r4t5,qaz123456,qazqazqaz,qazwsx123,qazwsxedc,qazxswedc,qq000000,qq111111,qq123123,qq123456,qq123456789,qq1314520,qq5201314,qqq11111,qqqq1111,qqqqqqqq,qqqqqqqqq,qqqqqqqqqq,qw123456,qwe123,qwe123123,qwe12345,qwe123456,qweasd123,qweasdzxc,qweqweqwe,qwer1234,qwerasdf,qwert12345,qwerty,qwerty123,qwertyui,qwertyuiop,qwqwqwqw,s123456,shanghai,shanshan,ssssssss,sunshine,superman,testtest,tiantian,tzwadmin123,w123456,w1234567,w12345678,w123456789,wang123,wang123456,wangchao,wangjian,wangjing,wangpeng,wangwang,wangyang,wiii2dsE,woailaopo,woaini,woaini123,woaini1314,woaini1314520,woaini520,woaini521,woainima,woainiwoaini,woaiwojia,wobuzhidao,wocaonima,wodemima,wojiushiwo,worinima,woshishui,www123456,wwwwwwww,xiaofeng,xiaolong,xiaoqiang,xiaoxiao,xiazhili,xingxing,xxxxxxxx,yangyang,yuanyuan,yyyyyyyy,z123456,z1234567,z12345678,z123456789,z3255500,zaq12wsx,zhang123,zhanghao,zhangjian,zhangjie,zhangjing,zhanglei,zhangwei,zhimakaimen,zx123456,zxc123,zxc123456,zxcv1234,zxcvbnm,zxcvbnm123,zxczxczxc,zz123456,zzzzzzzz


#end
font:
  path: https://himall-test.obs.cn-south-1.myhuaweicloud.com/test/rs/font/FZLTXHJW.TTF
  cu_path: https://himall-test.obs.cn-south-1.myhuaweicloud.com/test/rs/font/FZLTZHUNHJW.TTF

himall:
  #电子面单begin
  waybill:
    appkey: 51ff1d71326d44d089c37708a7b0f7a1
    appsecret: 44d4d257a32e4dd39c692f085393cedd
  current:
    domain:
      url: https://histore.cce.35hiw.com
  erp:
    url: http://hierp.kuaidiantong.cn/api/commercialtenantregister
    domain: http://hierp.kuaidiantong.cn
  email:
    white-code: 123456
  sms:
    white-code: 123456
    real:
      send: true
    templates:
      0:
        title: 测试
        sms-channel: NOTICE
        content: 测试
      2007552:
        title: 验证码
        sms-channel: NOTICE
        content: 您本次的验证码为：{code}，有效期为两分钟，请勿泄露。
      2007553:
        title: 供应商入驻-审核通过通知
        sms-channel: NOTICE
        content: 恭喜您，已经成功入驻{siteName}。
      2007587:
        title: 供应商入驻-审核失败通知
        sms-channel: NOTICE
        content: 您好，您的入驻申请被拒绝，请您登录{siteName}PC供应商后台查看原因，修改资料后重新提交。
      2007554:
        title: 缴纳保证金通知
        sms-channel: NOTICE
        content: 您好，您尚未缴纳保证金，请您登录{siteName}PC供应商后台及时完成缴纳哦。
      2009179:
        title: 签署并缴纳保证金
        sms-channel: NOTICE
        content: 您好，您尚未签署合同及缴纳保证金，请您登录及时完成哦。（操作路径：供应商中心-店铺-合同管理，供应商中心-店铺-保证金管理）
      2007555:
        title: 签署合同通知
        sms-channel: NOTICE
        content: 您好，您尚未签署合同，请您登录{siteName}PC供应商后台及时完成签署哦。
      2007556:
        title: 续签合同通知
        sms-channel: NOTICE
        content: 您好，您有合同需要续签，请您登录{siteName}PC供应商后台及时完成续签哦。
      2007557:
        title: 订单-商家待付款通知
        sms-channel: NOTICE
        content: 尊敬的客户：｛userName｝，您在{siteName}还有订单未成功付款，尽快支付别让好货错过。
      2007558:
        title: 订单-商家订单支付通知
        sms-channel: NOTICE
        content: 尊敬的客户：｛userName｝，您在{siteName}的订单｛orderId｝已支付成功，我们会尽快为您发货。
      2007559:
        title: 订单-供应商发货提醒通知（定时任务）
        sms-channel: NOTICE
        content: 您好，贵店铺｛shopName｝在{siteName}有｛count｝笔订单付款已超过｛hours｝小时没发货，请务必保证及时发出。
      2009070:
        title: 订单-供应商发货提醒通知
        sms-channel: NOTICE
        content: 您好，贵店铺{shopName}在{siteName}有订单{orderId}已付款，请及时发货。
      2007583:
        title: 订单-商家订单发货通知
        sms-channel: NOTICE
        content: 尊敬的客户：{userName}，您在{siteName}的订单{orderId}已发货，请注意查收。
      2007560:
        title: 订单-商家申请退款通知
        sms-channel: NOTICE
        content: 尊敬的客户：{userName}，您在{siteName}的退款申请正在受理中，请至个人中心查看。
      2007561:
        title: 订单-商家退款成功通知
        sms-channel: NOTICE
        content: 尊敬的客户：{userName}，您在{siteName}的订单已经完成退款，请留意查收。
      2007562:
        title: 订单-商家退款失败通知（供应商拒绝+平台驳回）
        sms-channel: NOTICE
        content: 尊敬的客户：{userName}，您在{siteName}的退款申请被拒绝，请知悉。
      2007563:
        title: 订单-商家退货申请通知
        sms-channel: NOTICE
        content: 尊敬的客户：{userName}，您在{siteName}的退货申请正在受理中，请至个人中心查看。
      2007564:
        title: 订单-供应商同意退货通知
        sms-channel: NOTICE
        content: 尊敬的客户：{userName}，您在{siteName}的退货已审核通过，请及时发货。
      2009071:
        title: 订单-商家退货失败通知（供应商拒绝+平台驳回）
        sms-channel: NOTICE
        content: 尊敬的客户：{userName}，您在{siteName}的退货申请被拒绝，请知悉。
      2007565:
        title: 订单-供应商售后通知
        sms-channel: NOTICE
        content: 您在{siteName}的店铺有订单申请了售后，订单号：{orderId}，请及时处理！
      2007969:
        title: 聚水潭-授权到期通知
        sms-channel: NOTICE
        content: 您好，因聚水潭要求，您在{siteName}的授权即将到期，请于{expirationTime}前，前往供应商后台-店铺-ERP管理，打开授权链接，登录聚水潭账号进行授权。
      2010238:
        title: 保证金预警提醒
        sms-channel: NOTICE
        content: 您的{siteName}保证金余额不足，为保障您后续经营，请及时充值，路径:供应商后台-店铺-保证金管理(http://dpurl.cn/58eceP8z)
      2010239:
        title: 重新入网提醒
        sms-channel: NOTICE
        content: 您在{siteName}的资质信息发生变更，请前往卖家中心重新开通商户号。
      2010589:
        title: 保证金从汇付转移到钱袋宝通知供应商
        sms-channel: NOTICE
        content: 您好，您的{siteName}保证金已从汇付账户转移到美团钱袋宝账户，请知悉！
amap:
  jd:
    # 高德key
    appKey: 76d5200d966952d223f0ea562a4badb9

#本地联调，调用自己服务用http://localhost:端口，调用test远程服务用https://himall.cce.35hiw.com
#himall-base：8082
#himall-order: 8083
#himall-report ：8085
#himall-gw：8081
#himall-trade：8084
himall-order:
  dev:
    url: http://himall.cce.35hiw.com
himall-base:
  dev:
    url: http://himall.cce.35hiw.com
himall-report:
  dev:
    url: http://127.0.0.1:8085
himall-trade:
  dev:
    url: http://himall.cce.35hiw.com