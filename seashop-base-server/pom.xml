<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-base</artifactId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>seashop-base-server</artifactId>
    <version>${base.version}</version>
    <packaging>jar</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-user-account</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-user-shop</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hishop-xxl-job-client-boot-starter</artifactId>
                    <groupId>com.hishop.starter</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-user-auth</artifactId>
        </dependency>


        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>himall-web-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>hishop-s3-spring-boot-starter</artifactId>
        </dependency>

        <!-- 单元测试相关 start -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter-test</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.hishop.starter</groupId>-->
<!--            <artifactId>xxl-job-server-spring-boot-starter</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 单元测试相关 end -->
    </dependencies>

    <build>
        <finalName>himall-base</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.0</version>
            </plugin>
        </plugins>

    </build>

</project>
