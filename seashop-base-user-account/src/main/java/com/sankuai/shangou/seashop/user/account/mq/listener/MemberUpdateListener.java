package com.sankuai.shangou.seashop.user.account.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.account.mq.model.UpdateMemberMsg;
import com.sankuai.shangou.seashop.user.account.service.MemberService;
import com.sankuai.shangou.seashop.user.common.constant.MafkaConst;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品信息更新监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConst.TOPIC_MEMBER_UPDATE+ "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.TOPIC_MEMBER_UPDATE_GROUP+ "_${spring.profiles.active}")
public class MemberUpdateListener implements RocketMQListener<MessageExt> {

    @Resource
    private MemberService memberService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【用户变更】消息内容为: {}", body);
        try {
//            String body = (String) mafkaMessage.getBody();
            UpdateMemberMsg updateMemberMsg = JsonUtil.parseObject(body, UpdateMemberMsg.class);
            //先写死稍后移到loin
            //如果是appKey不是自己的，就不处理
            memberService.updateMember(updateMemberMsg);
            log.info("【mafka消费】【用户变更】消息处理完成内容为: {}", body);
        }
        catch (Exception e) {
            log.info("【mafka消费】【用户变更】消息处理异常: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
