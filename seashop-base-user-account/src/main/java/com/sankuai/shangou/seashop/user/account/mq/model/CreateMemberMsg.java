package com.sankuai.shangou.seashop.user.account.mq.model;

import lombok.Data;

/**
 * @description:
 * @author: LXH
 **/
@Data
public class CreateMemberMsg {
    /**
     * 会员id
     */
    private Integer id;
    /**
     * 设备id
     */
    private Integer dpId;
    /**
     * 登录名
     */
    private String login;
    /**
     * 用户名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邀请码
     */
    private String interCode;
    /**
     * partType
     */
    private Integer partType;
    /**
     * partKey
     */
    private String partKey;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 会员来源bg
     */
    private Integer bgSource;
    /**
     * 会员来源app_key
     */
    private String baSource;
    /**
     * 唯一标识
     */
    private String uuid;
    /**
     * 来源IP
     */
    private String ip;
    /**
     * 来源平台
     */
    private Integer platform;
}
