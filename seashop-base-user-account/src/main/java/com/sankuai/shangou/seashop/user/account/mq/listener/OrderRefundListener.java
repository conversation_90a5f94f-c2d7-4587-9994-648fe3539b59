package com.sankuai.shangou.seashop.user.account.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.account.mq.model.OrderRefundMsg;
import com.sankuai.shangou.seashop.user.account.service.MemberService;
import com.sankuai.shangou.seashop.user.common.config.MafkaConst;

import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: LXH
 **/
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_ORDER_REFUND,
//        group = MafkaConst.GROUP_ORDER_REFUND)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_REFUND+ "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_REFUND+ "_${spring.profiles.active}")
public class OrderRefundListener implements RocketMQListener<MessageExt> {
    @Resource
    private MemberService memberService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【订单状态变更-售后ES修改】消息内容为: {}", body);
//        String body = (String) mafkaMessage.getBody();
        try {
            OrderRefundMsg changeMsg = JsonUtil.parseObject(body, OrderRefundMsg.class);
            memberService.updateRefundOrderStatus(changeMsg);
        }
        catch (Exception e) {
            log.error("【mafka消费】【订单状态变更-售后ES修改】异常: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
