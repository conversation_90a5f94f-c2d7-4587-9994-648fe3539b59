package com.sankuai.shangou.seashop.user.account.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.account.mq.model.CreateMemberMsg;
import com.sankuai.shangou.seashop.user.account.service.MemberService;
import com.sankuai.shangou.seashop.user.common.constant.MafkaConst;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户信息更新监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConst.TOPIC_MEMBER_CREATE+ "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.TOPIC_MEMBER_CREATE_GROUP+ "_${spring.profiles.active}")
public class MemberCreateListener implements RocketMQListener<MessageExt> {
    @Resource
    private MemberService memberService;
    @Value("$KMS{open.sso.app.key}")
    private String appKey;

    @Override
    public void onMessage(MessageExt message) {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("【mafka消费】【用户创建】消息内容为: {}", body);
//            String body = (String) mafkaMessage.getBody();
            CreateMemberMsg createMemberMsg = JsonUtil.parseObject(body, CreateMemberMsg.class);
            if (createMemberMsg == null) {
                log.error("【mafka消费】【商家注册】消息内容为空");
                return;
            }
            //先写死稍后移到loin
            if (!"com.sankuai.sgshopcrm.fe.himall".equals(createMemberMsg.getBaSource())) {
                return;
            }
            //如果是appKey不是自己的，就不处理
            memberService.insert(createMemberMsg);
            log.info("【mafka消费】【用户创建】消息处理完成内容为: {}", body);
            return;
        }
        catch (Exception e) {
            log.error("consumer message error", e);
            throw new RuntimeException(e);
        }
    }
}
