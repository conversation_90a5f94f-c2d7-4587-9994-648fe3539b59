package com.hishop.himall.report.common.remote;

import com.hishop.himall.report.common.remote.bo.AppSiteSettingBo;
import com.hishop.starter.util.model.ResultInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@FeignClient(name = "himall-base", contextId = "SiteSettingQueryThriftService", url = "${himall.url:https://himall.cce.35hiw.com}", path = "/himall-base/siteSetting")
public interface SiteSettingQueryClient {
    /**
     * 获取app配置
     *
     * @return
     */
    @RequestMapping("/getAppSettings")
    ResultInfo<AppSiteSettingBo> getAppSettings();
}
