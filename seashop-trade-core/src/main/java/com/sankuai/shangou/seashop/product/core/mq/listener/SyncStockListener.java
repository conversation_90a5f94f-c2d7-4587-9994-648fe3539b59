package com.sankuai.shangou.seashop.product.core.mq.listener;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.mq.model.SyncStockMessage;
import com.sankuai.shangou.seashop.product.core.service.SkuStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/20 17:17
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConstant.DEFAULT_NAMESPACE,
//        topic = MafkaConstant.TOPIC_SYNC_STOCK,
//        group = MafkaConstant.GROUP_SYNC_STOCK)
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_SYNC_STOCK + "_${spring.profiles.active}", consumerGroup = MafkaConstant.GROUP_SYNC_STOCK + "_${spring.profiles.active}")
public class SyncStockListener implements RocketMQListener<MessageExt> {

    @Resource
    private SkuStockService skuStockService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isEmpty(body)) {
                return;
            }
            log.info("【mafka消费】【执行覆盖库存】消息内容为: {}", body);

            if (JSONUtil.isTypeJSONObject(body)) {
                SyncStockMessage syncStockMessage = JsonUtil.parseObject(body, SyncStockMessage.class);
                skuStockService.executeAsyncStock(syncStockMessage.getId());
            } else {
                // 解析消息体数组，使用org.json.JSONArray
                List<JSONObject> items = JSONUtil.toList(body, JSONObject.class);
                items.forEach(item -> {
                    // 从payload字段中提取真正的SyncStockMessage数据
                    String payload = item.getStr("payload");
                    if (StringUtils.isNotEmpty(payload)) {
                        SyncStockMessage syncStockMessage = JsonUtil.parseObject(payload, SyncStockMessage.class);
                        skuStockService.executeAsyncStock(syncStockMessage.getId());
                    }
                });
            }
            log.info("【mafka消费】【执行覆盖库存】消息处理完成: {}", body);
        }
        catch (Exception e) {
            log.error("【mafka消费】【执行覆盖库存】消息内容为: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
