package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import java.math.BigDecimal;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteReductionBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 满减处理器
 *
 * <AUTHOR>
 * @date 2023/12/25 9:32
 */
@Slf4j
@Component
public class FullReductionHandler extends AbsActivityHandler {
    @Override
    public void handle(DealActivityContext context) {
        ActivityContext activityContext = context.getActivityContext();
        if (activityContext.getFullReduction() == null) {
            return;
        }

        ProductActivityInfoBo activityInfo = context.getActivityInfo();
        activityInfo.setHasFullReduction(true);
        RemoteReductionBo fullReduction = activityContext.getFullReduction();
        activityInfo.setFullReductionRule(fullReduction);

        // 预估价
        BigDecimal estimatePrice = context.getEstimatePrice();

        // 如果预估到手价小于0, 则预估到手价为0
        context.setFullReductionEstimatePrice(getFullReductionEstimatePrice(fullReduction, estimatePrice));
    }

    /**
     * 进入该方法了, 前置条件已经判断达到了预估价的最低层级
     *
     * @param fullReduction 满减活动
     * @param estimatePrice 预估价
     * @return 预估到手价
     */
    private static BigDecimal getFullReductionEstimatePrice(RemoteReductionBo fullReduction, BigDecimal estimatePrice) {
        // 将预估价对 moneyOffCondition 取模, 计算达到了几层优惠
        int layer = 1;
        if (fullReduction.getMoneyOffCondition().compareTo(BigDecimal.ZERO) > 0) {
            layer = estimatePrice.divide(fullReduction.getMoneyOffCondition(), 0, BigDecimal.ROUND_DOWN).intValue();
        }

        // 如果没有叠加优惠的话, 并且达到了优惠层级, 则优惠层级恒为1
        if (!fullReduction.getMoneyOffOverLay() && layer > 1) {
            layer = 1;
        }
        // 计算满减金额 将优惠层级乘以满减金额
        BigDecimal fullReductionPrice = fullReduction.getMoneyOffFee().multiply(BigDecimal.valueOf(layer));
        // 计算预估到手价
        BigDecimal fullReductionEstimatePrice = estimatePrice.subtract(fullReductionPrice);
        return fullReductionEstimatePrice.compareTo(BigDecimal.ZERO) > 0 ? fullReductionEstimatePrice : BigDecimal.ZERO;
    }

    @Override
    public int order() {
        return 6;
    }
}
