package com.sankuai.shangou.seashop.trade.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.AddonActivityBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.AddonProductResultBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.EsCommentSummaryBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.EsTradeProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.ProductVisitBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.QueryAddonProductParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.QueryProductDetailBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchProductInShopBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchTradeProductRespBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchedTradeProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ShopAddonActivityParamBo;
import com.sankuai.shangou.seashop.trade.thrift.core.request.CalculateFreightReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ProductIdsReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.MallShopProductResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.CalculateFreightResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.QueryProductByIdListResp;

/**
 * 交易商品服务
 * <p>所谓交易商品是指，基于商品基础信息以及其他关联信息构建的，用于商家在商城搜索和下单的汇总的商品信息，
 * 名称用于区别于商品原始信息</p>
 *
 * <AUTHOR>
 */
public interface TradeProductService {

    /**
     * 构建交易商品
     *
     * @param productId 需要构建的商品ID
     * <AUTHOR>
     */
    void build(Long productId);

    /**
     * 搜索交易商品
     *
     * @param searchBo 搜索条件
     * <AUTHOR>
     */
    SearchTradeProductRespBo search(SearchProductBo searchBo);

    /**
     * 商品详情页的 本店搜索 。搜索结果域商品列表有差别，单独实现
     *
     * @param searchBo 查询入参
     * <AUTHOR>
     */
    BasePageResp<SearchedTradeProductBo> searchInShop(SearchProductInShopBo searchBo);

    /**
     * 更新商品收藏数
     *
     * @param productId 商品id
     */
    void updateProductCollectionCount(Long productId);

    /**
     * 更新商品访问量
     *
     * @param visitBo 添加访问数入参
     */
    void updateProductVisitCount(ProductVisitBo visitBo);

    /**
     * 更新商品评论汇总
     *
     * @param productId 商品id
     */
    void updateProductCommentSummary(Long productId);

    /**
     * 查询热销商品
     *
     * @param queryBo 查询入参
     * @return 热销商品列表
     */
    List<SearchedTradeProductBo> queryHotSaleProduct(SearchProductBo queryBo);

    /**
     * 查询商城店铺商品
     *
     * @param queryBo 查询入参
     * @return 商城店铺商品列表
     */
    MallShopProductResp queryMallShopProduct(SearchProductBo queryBo);

    /**
     * 查询热门关注
     *
     * @param queryBo 查询入参
     * @return 热门关注列表
     */
    List<SearchedTradeProductBo> queryHotAttentionProduct(SearchProductBo queryBo);

    /**
     * 查询猜你喜欢
     *
     * @param productId 商品id
     * @return 猜你喜欢列表
     */
    List<SearchedTradeProductBo> queryGuessYouLike(Long productId);

    /**
     * 搜索凑单商品
     *
     * @param queryParam 查询参数
     * @return 附加商品结果
     */
    AddonProductResultBo searchAddonProduct(QueryAddonProductParamBo queryParam);

    /**
     * 获取店铺的凑单活动列表
     *
     * @param param 店铺ID
     * @return 附加商品活动列表
     */
    List<AddonActivityBo> getShopAddonActivity(ShopAddonActivityParamBo param);

    /**
     * 查询商品信息
     *
     * @param queryBo 查询商品信息入参
     * @return 商品信息
     */
    ProductBaseInfoBo queryProductBaseInfo(QueryProductDetailBo queryBo);

    /**
     * 查询商品评论汇总
     *
     * @param productId 商品id
     * @return 商品评论汇总
     */
    EsCommentSummaryBo queryCommentSummary(Long productId);

    /**
     * 计算运费
     *
     * @param calculateFreightReq
     * @return 运费
     */
    CalculateFreightResp calculateFreight(CalculateFreightReq calculateFreightReq);

    /**
     * 根据商品id列表查询商品信息
     *
     * @param request
     * @return
     */
    List<QueryProductByIdListResp> queryProductByIdList(ProductIdsReq request);

    /**
     * 查询最新商品
     *
     * @param shopId 店铺id
     * @return 最新商品列表
     */
    List<SearchedTradeProductBo> queryNewestProduct(Long shopId);
}
