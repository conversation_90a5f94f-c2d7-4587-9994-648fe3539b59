package com.sankuai.shangou.seashop.product.core.service.excel.read;

import com.sankuai.shangou.seashop.base.eimport.BizType;

/**
 * <AUTHOR>
 * @date 2023/11/21 22:04
 */
public enum BizTypeEnum implements BizType {
    STOCK_IMPORT("STOCK_IMPORT", "库存导入"),
    PRICE_IMPORT("PRICE_IMPORT", "价格导入"),
    OFF_SHELF_IMPORT("OFF_SHELF_IMPORT", "导入下架"),
    VIOLATION_OFF_SALE_IMPORT("VIOLATION_OFF_SALE_IMPORT", "违规下架导入"),
    PRODUCT_IMPORT("PRODUCT_IMPORT", "商品导入"),
    PLATFORM_PRODUCT_IMPORT("PLATFORM_PRODUCT_IMPORT", "平台商品导入"),
    PRODUCT_UPDATE_IMPORT("PRODUCT_UPDATE_IMPORT", "商品更新导入"),
    ;


    private final String type;
    private final String desc;

    BizTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @Override
    public String type() {
        return null;
    }

    @Override
    public String desc() {
        return null;
    }
}
