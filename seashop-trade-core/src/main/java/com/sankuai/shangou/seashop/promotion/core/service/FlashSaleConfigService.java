package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.PlatFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.ShopFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.PlatFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopFlashSaleConfigResp;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
public interface FlashSaleConfigService {

    /**
     * 查询平台限时购配置
     *
     * @return
     */
    PlatFlashSaleConfigResp getPlatConfig();

    /**
     * 查询店铺限时购配置
     *
     * @param request
     * @return
     */
    ShopFlashSaleConfigResp getShopConfig(ShopIdReq request);

    /**
     * 修改平台限时购配置
     *
     * @param request
     */
    void updatePlatConfig(PlatFlashSaleConfigReq request);

    /**
     * 修改店铺限时购配置
     *
     * @param request
     */
    void updateShopConfig(ShopFlashSaleConfigReq request);
}
