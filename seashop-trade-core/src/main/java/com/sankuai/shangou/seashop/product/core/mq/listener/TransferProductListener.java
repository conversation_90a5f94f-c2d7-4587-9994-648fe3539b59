package com.sankuai.shangou.seashop.product.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.service.ShopCategoryService;
import com.sankuai.shangou.seashop.product.core.service.model.TransferProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/06 20:13
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConstant.DEFAULT_NAMESPACE,
//        topic = MafkaConstant.TOPIC_PRODUCT_TRANSFER,
//        group = MafkaConstant.GROUP_PRODUCT_TRANSFER)
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_PRODUCT_TRANSFER + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.GROUP_PRODUCT_TRANSFER + "_${spring.profiles.active}")
public class TransferProductListener implements RocketMQListener<MessageExt> {

    @Resource
    private ShopCategoryService shopCategoryService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
//            String bodyStr = body.toString();
            log.info("【mafka消费】【转移商品】消息内容为: {}", body);

            TransferProductBo transferProductBo = JsonUtil.parseObject(body, TransferProductBo.class);
            shopCategoryService.transferProduct(transferProductBo);
            log.info("【mafka消费】【转移商品】消息处理完成: {}", body);
        }
        catch (Exception e) {
            log.error("【mafka消费】【转移商品】消息处理完成: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
