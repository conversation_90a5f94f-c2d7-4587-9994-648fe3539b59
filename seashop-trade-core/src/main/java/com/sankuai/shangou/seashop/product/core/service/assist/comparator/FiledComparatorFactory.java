package com.sankuai.shangou.seashop.product.core.service.assist.comparator;

import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductField;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/02/22 10:22
 */
@Slf4j
public class FiledComparatorFactory {

    public static Boolean compare(@NonNull ProductField fieldAnno, Object newObj, Object oldObj) {
        Class<? extends AbsFieldComparator> comparator = fieldAnno.comparator();
        try {
            AbsFieldComparator absFieldComparator = comparator.newInstance();
            absFieldComparator.setFieldAnno(fieldAnno);
            return absFieldComparator.compareField(newObj, oldObj);
        } catch (InstantiationException | IllegalAccessException e) {
            log.error("比较字段异常", e);
            throw new SystemException("比较字段异常");
        }
    }
}
