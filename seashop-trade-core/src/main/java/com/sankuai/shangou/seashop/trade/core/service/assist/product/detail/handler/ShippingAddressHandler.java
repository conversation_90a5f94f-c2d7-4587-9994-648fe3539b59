package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.common.remote.UserShippingAddressRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 默认收货地址查询
 *
 * <AUTHOR>
 * @date 2023/12/23 15:47
 */
@Component
@Slf4j
public class ShippingAddressHandler extends AbsProductQueryHandler {

    @Resource
    private UserShippingAddressRemoteService userShippingAddressRemoteService;

    @Override
    public void handle(ProductBaseContext context) {
        log.info("查询默认收货地址, context: {}", context);
        if (context.getUserId() == null) {
            log.info("用户未登录, 不查询默认收货地址");
            return;
        }

        ProductBaseInfoBo product = context.getProduct();
        ShippingAddressBo shippingAddress = userShippingAddressRemoteService.getUserDefaultShippingAddress(context.getUserId());
        product.setDefaultShippingAddress(shippingAddress);
    }


    @Override
    public int order() {
        return 1;
    }
}
