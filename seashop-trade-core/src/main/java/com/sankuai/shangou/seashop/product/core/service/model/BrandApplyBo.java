package com.sankuai.shangou.seashop.product.core.service.model;

import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/07 15:15
 */
@Getter
@Setter
@Builder
public class BrandApplyBo {

    /**
     * 申请记录id
     */
    private Integer id;

    /**
     * 供应商id
     */
    private Long shopId;

    /**
     * 供应商名称
     */
    private String shopName;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌logo
     */
    private String logo;

    /**
     * 品牌简介
     */
    private String description;

    /**
     * 品牌授权书
     */
    private List<String> authCertificateList;

    /**
     * 申请类型
     */
    private BrandEnum.ApplyModeEnum applyMode;

    /**
     * 申请类型 1-平台已有品牌 2-供应商新增品牌
     */
    private Integer applyModeCode;

    /**
     * 申请类型描述
     */
    private String applyModeDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态
     */
    private BrandEnum.AuditStatusEnum auditStatus;

    /**
     * 审核状态 0-未审核 1-审核通过 2-审核拒绝
     */
    private Integer auditStatusCode;

    /**
     * 审核状态描述
     */
    private String auditStatusDesc;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 平台备注
     */
    private String platRemark;

}
