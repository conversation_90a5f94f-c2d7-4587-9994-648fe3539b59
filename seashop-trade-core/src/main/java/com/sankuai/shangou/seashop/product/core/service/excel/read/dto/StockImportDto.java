package com.sankuai.shangou.seashop.product.core.service.excel.read.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.base.eimport.anno.ExcelField;
import com.sankuai.shangou.seashop.base.eimport.regex.FieldRegexEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/21 22:08
 */
@Getter
@Setter
public class StockImportDto extends RowReadResult {

    @ExcelProperty(value = "*规格ID")
    @ExcelField(required = true, regexEnum = FieldRegexEnum.POSITIVE_INTEGER_REGEX)
    private String skuAutoId;

    @ExcelProperty(value = "*库存")
    @ExcelField(required = true, regexEnum = FieldRegexEnum.NON_NEGATIVE_INTEGER_REGEX)
    private String stock;

    @ExcelIgnore
    private Long productId;

    @ExcelIgnore
    private StringBuilder errBuilder = new StringBuilder();

}
