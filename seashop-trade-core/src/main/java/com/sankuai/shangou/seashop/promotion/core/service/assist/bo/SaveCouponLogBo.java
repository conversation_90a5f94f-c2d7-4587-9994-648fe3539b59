package com.sankuai.shangou.seashop.promotion.core.service.assist.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponSaveBo;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/7/003
 * @description:
 */
@Getter
@Setter
public class SaveCouponLogBo {

    @ExaminField(description = "优惠券ID")
    private Long id;

    @ExaminField(description = "面值(价格)")
    private BigDecimal price;

    @ExaminField(description = "最大可领取张数")
    private Integer perMax;

    @ExaminField(description = "订单金额（满足多少钱才能使用）")
    private BigDecimal orderAmount;

    @ExaminField(description = "发行张数")
    private Integer num;

    @ExaminField(description = "开始时间")
    private Date startTime;

    @ExaminField(description = "结束时间")
    private Date endTime;

    @ExaminField(description = "优惠券名称")
    @PrimaryField
    private String couponName;

    @ExaminField(description = "领取方式 0 店铺首页 1 积分兑换 2 主动发放")
    private Integer receiveType;

    @ExaminField(description = "使用范围：0=全场通用，1=部分商品可用")
    private Integer useArea;

    @ExaminField(description = "备注")
    private String remark;

    @ExaminField(description = "产品ID列表")
    private List<Long> productIdList;

    @ExaminField(description = "推广方式：0 平台；4 移动端(小程序)")
    private List<Integer> platForm;

    public static SaveCouponLogBo build(CouponSaveBo saveBo) {
        if (saveBo == null) {
            return null;
        }
        return JsonUtil.copy(saveBo, SaveCouponLogBo.class);
    }

    public static SaveCouponLogBo build(CouponResp resp) {
        if (resp == null) {
            return null;
        }
        return JsonUtil.copy(resp, SaveCouponLogBo.class);
    }
}