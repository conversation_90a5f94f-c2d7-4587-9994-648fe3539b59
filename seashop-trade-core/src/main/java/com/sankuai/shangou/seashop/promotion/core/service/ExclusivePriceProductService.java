package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductPageQryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceProductSimpleResp;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
public interface ExclusivePriceProductService {

    /**
     * 分页查询专项价商品
     *
     * @param request
     * @return
     */
    BasePageResp<ExclusivePriceProductDto> pageList(ExclusivePriceProductPageQryReq request);

    /**
     * 查询店铺下当前有效的专享价SKU
     *
     * @param shopId
     * @return
     */
    ExclusivePriceProductSimpleResp getShopValidExclusive(Long shopId);
}
