package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.query;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.LadderPriceAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 查询阶梯价审核处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:22
 */
@Component
@Slf4j
public class QueryLadderPriceAuditHandler extends AbsQueryProductAuditHandler {

    @Resource
    private LadderPriceAssist ladderPriceAssist;

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        Long productId = context.getProductId();

        // 查询阶梯价
        productBo.setLadderPriceList(ladderPriceAssist.getLadderPriceAuditBoList(productId));
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_LADDER_PRICE_AUDIT;
    }

}
