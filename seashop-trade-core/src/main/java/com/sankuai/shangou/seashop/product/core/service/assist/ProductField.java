package com.sankuai.shangou.seashop.product.core.service.assist;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.sankuai.shangou.seashop.product.core.service.assist.comparator.AbsFieldComparator;
import com.sankuai.shangou.seashop.product.core.service.assist.comparator.DefaultComparator;

/**
 * 用来标记bo 中需要入库的字段
 *
 * <AUTHOR>
 * @date 2023/11/15 16:04
 */
@Target({ElementType.FIELD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ProductField {

    /**
     * 是否需要审核
     */
    boolean needAudit();

    /**
     * 字段类型 默认转成字符后对比
     */
    Class type() default String.class;

    /**
     * 自定义比较器
     */
    Class<? extends AbsFieldComparator> comparator() default DefaultComparator.class;

}
