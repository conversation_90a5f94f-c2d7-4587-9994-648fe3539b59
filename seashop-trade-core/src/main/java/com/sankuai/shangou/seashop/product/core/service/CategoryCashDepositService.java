package com.sankuai.shangou.seashop.product.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.CategoryCashDepositListReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.CategoryCashDepositReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryDepositConfigBySkuReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryFirstCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositMapResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositResp;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
public interface CategoryCashDepositService {

    /**
     * 查询所有的保证金配置
     *
     * @return
     */
    List<CategoryCashDepositResp> queryAll();

    /**
     * 保存保证金配置
     *
     * @param req
     * @return
     */
    ResultDto<BaseResp> save(CategoryCashDepositListReq req);

    /**
     * 查询一级类目的保证金配置
     *
     * @param request
     * @return
     */
    List<CategoryCashDepositMapResp> queryByCategoryList(QueryFirstCategoryReq request);

    /**
     * 查询sku的保证金配置
     *
     * @param request 请求参数
     */
    List<SkuFitCategoryCashDepositResp> queryBySkuList(QueryDepositConfigBySkuReq request);

}
