package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.mq.model.ProductVisitMessage;
import com.sankuai.shangou.seashop.trade.core.mq.publisher.ProductVisitPublisher;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 更新访问数
 *
 * <AUTHOR>
 * @date 2024/03/05 14:28
 */
@Component
@Slf4j
public class UpdateVisitCountsHandler extends AbsProductQueryHandler {

    @Resource
    private ProductVisitPublisher productVisitPublisher;

    @Override
    public void handle(ProductBaseContext context) {
        ProductVisitMessage visitMessage = new ProductVisitMessage();
        visitMessage.setProductId(context.getProductId());
        visitMessage.setAddVisitCounts(1);
        // productVisitPublisher.sendMessage(visitMessage);
    }

    @Override
    public int order() {
        return 100;
    }
}
