package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.AbsActivityQueryHandler;

/**
 * <AUTHOR>
 * @date 2023/11/23 19:55
 */
@Component
public class ActivityHandlerContainer implements InitializingBean {

    @Resource
    private ApplicationContext applicationContext;

    private static Map<Class<? extends AbsActivityQueryHandler>, AbsActivityQueryHandler> HANDLER_MAP = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        Map<String, AbsActivityQueryHandler> beanMap = applicationContext.getBeansOfType(AbsActivityQueryHandler.class);
        beanMap.values().forEach(handler -> {
            HANDLER_MAP.put(handler.getClass(), handler);
        });
    }

    public static AbsActivityQueryHandler getHandler(Class<? extends AbsActivityQueryHandler> type) {
        if (type == null) {
            return null;
        }
        return HANDLER_MAP.get(type);
    }
}
