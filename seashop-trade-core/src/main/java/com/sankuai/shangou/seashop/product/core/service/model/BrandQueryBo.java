package com.sankuai.shangou.seashop.product.core.service.model;


import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/07 11:10
 */
@Getter
@Setter
@Builder
public class BrandQueryBo {

    /**
     * 品牌id
     */
    private Long id;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 排序字段
     */
    private List<FieldSortReq> sortList;

    /**
     * 是否推荐
     */
    private Boolean whetherRecommend;

    /**
     * 查询条数
     */
    private Integer limit;

}
