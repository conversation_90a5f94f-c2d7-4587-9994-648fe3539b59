package com.sankuai.shangou.seashop.product.core.service.model.product;

import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.ProductFieldDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/05 17:16
 */
@Setter
@Getter
public class ProductImportBo {

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 销售状态 1-销售中 2-仓库中
     */
    private Integer saleStatus;

    /**
     * 操作人ID
     */
    private Long operationUserId;

    /**
     * 操作店铺id
     */
    private Long operationShopId;

    /**
     * 关联列表
     */
    private List<ProductFieldDto> fields;

}
