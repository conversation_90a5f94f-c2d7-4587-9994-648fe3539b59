package com.sankuai.shangou.seashop.product.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.product.core.mq.model.SkuStockMessage;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import lombok.extern.slf4j.Slf4j;

/**
 * 库存更新监听器
 *
 * <AUTHOR>
 * @date 2023/11/29 15:44
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConstant.DEFAULT_NAMESPACE,
//        topic = MafkaConstant.TOPIC_SKU_STOCK_UPDATE,
//        group = MafkaConstant.GROUP_ES_SKU_STOCK_BUILD)
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_SKU_STOCK_UPDATE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.GROUP_ES_SKU_STOCK_BUILD + "_${spring.profiles.active}")
public class SkuStockUpdateListener implements RocketMQListener<MessageExt> {

    @Resource
    private ProductEsBuildService productEsBuildService;
    @Resource
    private ProductAssist productAssist;


    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
//            String bodyStr = (String) body;
            log.info("【mafka消费】【库存表变动】消息内容为: {}", body);
            DbTableDataChangeMessage<SkuStockMessage> messageWrapper = JsonUtil.parseObject(body, new TypeReference<DbTableDataChangeMessage<SkuStockMessage>>() {});
            if (messageWrapper.getData() == null) {
                return;
            }
            Long productId = messageWrapper.getData().getProductId();
            productEsBuildService.updateStockEs(productId);

            // 发送库存变动事件
            productAssist.sendProductChangeEvent(productId, ProductSourceEnum.MALL, ProductChangeType.SET_STOCK);

        } catch (Exception e) {
            log.error("【mafka消费】【库存表变动】消息内容为: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
