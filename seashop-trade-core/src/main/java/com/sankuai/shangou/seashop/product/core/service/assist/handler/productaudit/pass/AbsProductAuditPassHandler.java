package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.pass;

import java.util.Arrays;
import java.util.List;

import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;

/**
 * <AUTHOR>
 * @date 2023/11/24 16:12
 */
public abstract class AbsProductAuditPassHandler extends AbsProductHandler {
    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.PASS_PRODUCT_AUDIT);
    }
}
