package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ShopCategoryAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopCategory;
import com.sankuai.shangou.seashop.product.thrift.core.enums.result.ProductResultEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 校验店铺分类
 *
 * <AUTHOR>
 * @date 2024/03/04 11:41
 */
@Component
@Slf4j
public class CheckShopCategoryHandler extends AbsProductHandler {

    @Resource
    private ShopCategoryAssist shopCategoryAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】校验店铺分类属性【start】, context:{}", context);

        ProductBo saveProductBo = context.getSaveProductBo();
        List<Long> shopCategoryIdList = saveProductBo.getShopCategoryIdList();
        if (CollectionUtils.isEmpty(shopCategoryIdList)) {
            return;
        }

        Map<Long, ShopCategory> shopCategoryMap = shopCategoryAssist.getShopCategoryMap(shopCategoryIdList);
        AssertUtil.throwIfTrue(!shopCategoryMap.keySet().containsAll(shopCategoryIdList), ProductResultEnum.SHOP_CATEGORY_NOT_EXIST);
        saveProductBo.setShopCategoryNameList(shopCategoryMap.values().stream().map(ShopCategory::getName).collect(Collectors.toList()));
        log.info("【保存商品】校验店铺分类属性【end】, context:{}", context);
    }

    @Override
    public boolean support(ProductContext context) {
        return CollectionUtils.isNotEmpty(context.getSaveProductBo().getShopCategoryIdList());
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_SHOP_CATEGORY;
    }
}
