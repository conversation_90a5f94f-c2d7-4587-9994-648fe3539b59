package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductByIdReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.PromotionResultCodeEnum;
import com.sankuai.shangou.seashop.promotion.core.model.bo.DiscountActiveQueryBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.DiscountActiveRuleBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.DiscountActiveSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.DiscountActiveService;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.PromotionLogAssist;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActive;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActiveProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActiveRule;
import com.sankuai.shangou.seashop.promotion.dao.core.model.*;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.DiscountActiveProductRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.DiscountActiveRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.DiscountActiveRuleRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveRuleReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@Service
@Slf4j
public class DiscountActiveServiceImpl implements DiscountActiveService {

    @Resource
    private DiscountActiveRepository discountActiveRepository;
    @Resource
    private DiscountActiveRuleRepository discountActiveRuleRepository;
    @Resource
    private DiscountActiveProductRepository discountActiveProductRepository;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private PromotionLogAssist promotionLogAssist;

    @Override
//    @ExaminProcess(processModel = ExaminModelEnum.PROMOTION, processType = ExaProEnum.MODIFY, actionName = "保存/修改折扣活动信息", serviceMethod = "save", dto = DiscountActiveSaveBo.class, entity = DiscountActive.class)
    public void save(DiscountActiveSaveBo saveBo) {
        log.info("save-saveBo:{}", saveBo);

        BaseIdReq baseIdReq = new BaseIdReq();

        DiscountActive saveDiscountActive;
        DiscountActiveResp oldDiscountActiveResp;
        if (null != saveBo.getId()) {
            // 更新
            saveDiscountActive = discountActiveRepository.getById(saveBo.getId());
            if (null == saveDiscountActive) {
                throw new BusinessException(PromotionResultCodeEnum.DISCOUNT_ACTIVE_NOT_FIND.getCode(), PromotionResultCodeEnum.DISCOUNT_ACTIVE_NOT_FIND.getMsg());
            }
            // 已经结束的活动不能修改
            Date endTime = saveDiscountActive.getEndTime();
            if (endTime.before(new Date())) {
                throw new BusinessException(PromotionResultCodeEnum.DISCOUNT_ACTIVE_END.getCode(),
                        PromotionResultCodeEnum.DISCOUNT_ACTIVE_END.getMsg());
            }
            baseIdReq.setId(saveBo.getId());
            oldDiscountActiveResp = getById(baseIdReq);
        }
        else {
            oldDiscountActiveResp = null;
            saveDiscountActive = new DiscountActive();
        }
        BeanUtils.copyProperties(saveBo, saveDiscountActive, "id");
        List<Long> productIdList = saveBo.getProductIdList();
        final Boolean izAllProduct = CollectionUtils.isEmpty(productIdList) ? true : false;
        saveDiscountActive.setIzAllProduct(izAllProduct);

        List<Long> queryProductIdList = new ArrayList<>();
        if (!izAllProduct) {
            queryProductIdList.addAll(productIdList);
            // 需要加上全部商品
            queryProductIdList.add(PromotionConstant.ALL_PRODUCT);
        }

        // 判断是否有商品参加了活动（不论时段，只要是未过期的）
        DiscountActiveParamDto paramDto = DiscountActiveParamDto.builder()
//                .startTime(saveBo.getStartTime())
//                .endTime(saveBo.getEndTime())
                .notEqId(saveBo.getId())
                .shopId(saveBo.getShopId())
                .productIds(queryProductIdList).build();
        List<DiscountActiveDetailDto> discountActives = discountActiveRepository.selectProductInActiveDetail(paramDto);
        if (CollectionUtils.isNotEmpty(discountActives)) {
            List<Long> productIds = discountActives.stream().map(DiscountActiveDetailDto::getProductId).distinct().collect(Collectors.toList());
            QueryProductByIdReq queryProductByIdReq = new QueryProductByIdReq();
            queryProductByIdReq.setProductIds(productIds);
            ProductListResp productListResp = productRemoteService.queryProductById(queryProductByIdReq);
            List<ProductPageResp> productList = productListResp.getProductList();
            StringBuffer productName = new StringBuffer();
            if (CollUtil.isNotEmpty(productList)) {
                List<String> productNameList = productList.stream().map(ProductPageResp::getProductName).collect(Collectors.toList());
                productName.append("【");
                productName.append(String.join(",", productNameList));
                productName.append("】");
            }
            throw new BusinessException(PromotionResultCodeEnum.PRODUCT_HAS_OTHER_DISCOUNT_ACTIVE.getCode(), productName.toString() + PromotionResultCodeEnum.PRODUCT_HAS_OTHER_DISCOUNT_ACTIVE.getMsg());
        }

        TransactionHelper.doInTransaction(() -> {
            // 保存活动主体对象
            discountActiveRepository.saveOrUpdate(saveDiscountActive);

            /*
             * 保存规则
             */
            List<DiscountActiveRule> oldDiscountActiveRules = opDiscountActiveRules(saveBo, saveDiscountActive);
            discountActiveRuleRepository.saveOrUpdateBatch(oldDiscountActiveRules);

            /*
             * 保存商品
             */
            List<DiscountActiveProduct> oldDiscountActiveProducts = opDiscountActiveProducts(saveBo, saveDiscountActive, productIdList, izAllProduct);
            discountActiveProductRepository.saveOrUpdateBatch(oldDiscountActiveProducts);

            // 记录操作日志
            baseIdReq.setId(saveDiscountActive.getId());
            promotionLogAssist.recordDiscountActiveLog(saveBo.getOperationUserId(), saveBo.getOperationShopId(),
                    oldDiscountActiveResp, getById(baseIdReq));
        });
    }

    @NotNull
    private List<DiscountActiveProduct> opDiscountActiveProducts(DiscountActiveSaveBo saveBo, DiscountActive saveDiscountActive, List<Long> productIdList, Boolean izAllProduct) {
        List<DiscountActiveProduct> oldDiscountActiveProducts = new ArrayList<>();
        if (null != saveBo.getId()) {
            // 更新的话，先查询老的商品
            oldDiscountActiveProducts = discountActiveProductRepository.listByActiveId(saveBo.getId());
        }
        List<Long> productIds = new ArrayList<>();
        if (izAllProduct) {
            productIds.add(PromotionConstant.ALL_PRODUCT);

        }
        else {
            productIds = productIdList;
        }
        int oldProductSize = oldDiscountActiveProducts.size();
        int newProductSize = productIds.size();
        // 判断两个列表的大小
        if (oldProductSize > newProductSize) {
            // 删除oldDiscountActiveProducts多出来的数据
            for (int i = newProductSize; i < oldProductSize; i++) {
                DiscountActiveProduct discountActiveProduct = oldDiscountActiveProducts.get(i);
                discountActiveProduct.setDelFlag(Boolean.TRUE);
            }
            if (newProductSize > 0) {
                // 更新productIds多出来的数据
                for (int i = 0; i < newProductSize; i++) {
                    Long productId = productIds.get(i);
                    DiscountActiveProduct discountActiveProduct = oldDiscountActiveProducts.get(i);
                    discountActiveProduct.setActiveId(saveDiscountActive.getId());
                    discountActiveProduct.setProductId(productId);
                }
            }
        }
        else if (oldProductSize <= newProductSize) {
            // 新增productIds多出来的数据
            for (int i = oldProductSize; i < newProductSize; i++) {
                Long productId = productIds.get(i);
                DiscountActiveProduct newProduct = new DiscountActiveProduct();
                newProduct.setActiveId(saveDiscountActive.getId());
                newProduct.setProductId(productId);
                oldDiscountActiveProducts.add(newProduct);
            }
            if (oldProductSize > 0) {
                // 更新oldDiscountActiveProducts多出来的数据
                for (int i = 0; i < oldProductSize; i++) {
                    Long productId = productIds.get(i);
                    DiscountActiveProduct discountActiveProduct = oldDiscountActiveProducts.get(i);
                    discountActiveProduct.setActiveId(saveDiscountActive.getId());
                    discountActiveProduct.setProductId(productId);
                }
            }
        }
        return oldDiscountActiveProducts;
    }

    @NotNull
    private List<DiscountActiveRule> opDiscountActiveRules(DiscountActiveSaveBo saveBo, DiscountActive saveDiscountActive) {
        List<DiscountActiveRule> oldDiscountActiveRules = new ArrayList<>();
        if (null != saveBo.getId()) {
            // 更新的话，先查询老的规则
            oldDiscountActiveRules = discountActiveRuleRepository.listByActiveId(saveBo.getId());
        }
        List<DiscountActiveRuleBo> ruleBoList = saveBo.getRuleList();
        if (null == ruleBoList) {
            ruleBoList = new ArrayList<>();
        }

        int oldRuleSize = oldDiscountActiveRules.size();
        int newRuleSize = ruleBoList.size();

        // 判断两个列表的大小
        if (oldRuleSize > newRuleSize) {
            // 删除discountActiveRules多出来的数据
            for (int i = newRuleSize; i < oldRuleSize; i++) {
                DiscountActiveRule discountActiveRule = oldDiscountActiveRules.get(i);
                discountActiveRule.setDelFlag(Boolean.TRUE);
            }
            if (newRuleSize > 0) {
                // 更新ruleBoList多出来的数据
                for (int i = 0; i < newRuleSize; i++) {
                    DiscountActiveRuleBo ruleBo = ruleBoList.get(i);
                    DiscountActiveRule discountActiveRule = oldDiscountActiveRules.get(i);
                    discountActiveRule.setActiveId(saveDiscountActive.getId());
                    discountActiveRule.setQuota(ruleBo.getQuota());
                    discountActiveRule.setDiscount(ruleBo.getDiscount());
                }
            }
        }
        else if (oldRuleSize <= newRuleSize) {
            // 新增ruleBoList多出来的数据
            for (int i = oldRuleSize; i < newRuleSize; i++) {
                DiscountActiveRuleBo discountActiveRuleBo = ruleBoList.get(i);
                DiscountActiveRule newRule = new DiscountActiveRule();
                newRule.setActiveId(saveDiscountActive.getId());
                newRule.setQuota(discountActiveRuleBo.getQuota());
                newRule.setDiscount(discountActiveRuleBo.getDiscount());
                oldDiscountActiveRules.add(newRule);
            }
            if (oldRuleSize > 0) {
                // 更新oldDiscountActiveRules多出来的数据
                for (int i = 0; i < oldRuleSize; i++) {
                    DiscountActiveRuleBo ruleBo = ruleBoList.get(i);
                    DiscountActiveRule discountActiveRule = oldDiscountActiveRules.get(i);
                    discountActiveRule.setActiveId(saveDiscountActive.getId());
                    discountActiveRule.setQuota(ruleBo.getQuota());
                    discountActiveRule.setDiscount(ruleBo.getDiscount());
                }
            }
        }
        return oldDiscountActiveRules;
    }

    @Override
    public BasePageResp<DiscountActiveSimpleDto> pageList(DiscountActiveQueryBo queryBo) {
        BasePageResp<DiscountActiveSimpleDto> pageResp = discountActiveRepository.pageList(queryBo.buildPage(),
                JsonUtil.copy(queryBo, DiscountActiveParamDto.class));
        pageResp.getData().parallelStream().forEach(
                data -> {
                    if (data.getIzAllProduct()) {
                        data.setProductCount(-1);
                    }
                    else {
                        DiscountActiveProductParamDto paramDto = DiscountActiveProductParamDto.builder()
                                .activeId(data.getId()).build();
                        long l = discountActiveProductRepository.countByExample(paramDto);
                        data.setProductCount((int) l);
                    }
                    ActiveBaseDto.opStatus(data);
                }
        );
        return pageResp;
    }

    @Override
    public void endActive(BaseIdReq baseIdReq) {
        DiscountActive discountActive = discountActiveRepository.getById(baseIdReq.getId());
        if (null == discountActive) {
            throw new BusinessException(PromotionResultCodeEnum.DISCOUNT_ACTIVE_NOT_FIND.getCode(), PromotionResultCodeEnum.DISCOUNT_ACTIVE_NOT_FIND.getMsg());
        }
        Date now = new Date();
        Date endTime = discountActive.getEndTime();
        if (endTime.before(now)) {
            throw new BusinessException(PromotionResultCodeEnum.DISCOUNT_ACTIVE_END.getCode(),
                    PromotionResultCodeEnum.DISCOUNT_ACTIVE_END.getMsg());
        }
        discountActive.setEndTime(now);
        TransactionHelper.doInTransaction(() -> {
            discountActiveRepository.saveOrUpdate(discountActive);

            // 记录操作日志
            promotionLogAssist.recordEndDiscountActiveLog(baseIdReq);
        });
    }

    @Override
    public DiscountActiveResp getById(BaseIdReq idReq) {
        DiscountActive discountActive = discountActiveRepository.getById(idReq.getId());
        if (null == discountActive) {
            throw new BusinessException(PromotionResultCodeEnum.DISCOUNT_ACTIVE_NOT_FIND.getCode(), PromotionResultCodeEnum.DISCOUNT_ACTIVE_NOT_FIND.getMsg());
        }
        Long shopId = idReq.getOperationShopId();
        if (shopId != null && shopId > 0 && !discountActive.getShopId().equals(shopId)) {
            throw new BusinessException(PromotionResultCodeEnum.AUTHORITY_ERROR.getCode(), PromotionResultCodeEnum.AUTHORITY_ERROR.getMsg());
        }
        DiscountActiveResp discountActiveDto = JsonUtil.copy(discountActive, DiscountActiveResp.class);
        if (!discountActiveDto.getIzAllProduct()) {
            List<DiscountActiveProduct> discountActiveProducts = discountActiveProductRepository.listByActiveId(discountActiveDto.getId());
            if (CollUtil.isNotEmpty(discountActiveProducts)) {
                List<DiscountActiveProductDto> discountActiveProductDtoList = JsonUtil.copyList(discountActiveProducts, DiscountActiveProductDto.class);
                // 处理商品名称和价格
                List<Long> productIdList = discountActiveProductDtoList.stream().map(DiscountActiveProductDto::getProductId).collect(Collectors.toList());

                List<ProductPageResp> productList = new ArrayList<>();
                // 将商品ID列表按200个进行分组
                List<List<Long>> partition = Lists.partition(productIdList, PromotionConstant.MAX_LIKE_QUERY);
                for (List<Long> productIds : partition) {
                    QueryProductReq productReq = new QueryProductReq();
                    productReq.setProductIds(productIds);
                    productReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
                    productReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
                    BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(productReq);
                    if (null != productPage && CollUtil.isNotEmpty(productPage.getData())) {
                        productList.addAll(productPage.getData());
                    }
                }
                if (CollUtil.isNotEmpty(productList)) {
                    final List<ProductPageResp> productListFinal = productList;
                    discountActiveProductDtoList.parallelStream().forEach(
                            discountActiveProductDto -> {
                                productListFinal.stream().filter(productPageResp -> productPageResp.getProductId().equals(discountActiveProductDto.getProductId())).findFirst().ifPresent(
                                        productPageResp -> {
                                            discountActiveProductDto.setProductName(productPageResp.getProductName());
                                            discountActiveProductDto.setSalePrice(productPageResp.getMinSalePrice());
                                        }
                                );
                            }
                    );
                }

                discountActiveDto.setProductList(discountActiveProductDtoList);
            }
        }
        else {
            discountActiveDto.setProductList(Collections.emptyList());
        }
        List<DiscountActiveRule> discountActiveRules = discountActiveRuleRepository.listByActiveId(discountActiveDto.getId());
        discountActiveDto.setRuleList(JsonUtil.copyList(discountActiveRules, DiscountActiveRuleReq.class));
        return discountActiveDto;
    }

    @Override
    public DiscountActiveResp queryByProductId(ProductAndShopIdReq request) {

        DiscountActiveParamDto paramDto = DiscountActiveParamDto.builder()
                .shopId(request.getShopId())
                .productIds(Arrays.asList(request.getProductId())).build();
        List<DiscountActive> discountActives = discountActiveRepository.selectProductInActive(paramDto);
        if (CollUtil.isEmpty(discountActives)) {
            return null;
        }
        // 根据开始时间升序排序
        discountActives.sort(Comparator.comparing(DiscountActive::getStartTime));
        log.info("queryByProductId-discountActives:{}", discountActives);
        DiscountActive discountActive = discountActives.get(0);
        DiscountActiveResp discountActiveResp = JsonUtil.copy(discountActive, DiscountActiveResp.class);

        List<DiscountActiveRule> discountActiveRules = discountActiveRuleRepository.listByActiveId(discountActiveResp.getId());
        if (CollUtil.isNotEmpty(discountActiveRules)) {
            discountActiveResp.setRuleList(JsonUtil.copyList(discountActiveRules, DiscountActiveRuleReq.class));
        }

        return discountActiveResp;
    }

    @Override
    public BasePageResp<DiscountActiveProductDto> queryDiscountActiveProduct(QueryDiscountActiveProductReq req) {
        DiscountActive active = discountActiveRepository.getById(req.getActiveId());
        AssertUtil.throwIfNull(active, "活动不存在");
        if (req.getShopId() != null) {
            AssertUtil.throwIfTrue(!req.getShopId().equals(active.getShopId()), "无权操作");
        }

        Page<DiscountActiveProduct> result = PageHelper.startPage(req.getPageNo(), req.getPageSize());
        discountActiveProductRepository.listByActiveId(req.getActiveId());

        List<Long> productList = result.getResult().stream().map(DiscountActiveProduct::getProductId).collect(Collectors.toList());
        Map<Long, ProductBasicDto> productMap = productRemoteService.getProductBasicMap(productList);
        return PageResultHelper.transfer(result, DiscountActiveProductDto.class, (db, source) -> {
            ProductBasicDto product = productMap.get(source.getProductId());
            if (product != null) {
                source.setProductName(product.getProductName());
                source.setSalePrice(product.getMinSalePrice());
            }
        });
    }

    @Override
    public List<DiscountActiveResp> selectCurrentDiscountByProduct(Collection<Long> productIds) {
        // 查询当前全商品的活动
        DiscountActive discountActive = discountActiveRepository.selectCurrentEnableIzAllProduct();
        if (discountActive != null) {
            // 商品
            DiscountActiveResp discountActiveResp = JsonUtil.copy(discountActive, DiscountActiveResp.class);
            List<DiscountActiveProduct> discountActiveProducts = discountActiveProductRepository.listByActiveId(discountActive.getId());
            List<DiscountActiveProductDto> discountActiveProductDtoList = JsonUtil.copyList(discountActiveProducts, DiscountActiveProductDto.class);
            discountActiveResp.setProductList(discountActiveProductDtoList);
            // 规则
            List<DiscountActiveRule> discountActiveRules = discountActiveRuleRepository.listByActiveId(discountActiveResp.getId());
            if (CollUtil.isNotEmpty(discountActiveRules)) {
                discountActiveResp.setRuleList(JsonUtil.copyList(discountActiveRules, DiscountActiveRuleReq.class));
            }
            return Collections.singletonList(discountActiveResp);
        }
        // 查找指定商品参与的抵扣活动
        List<DiscountActiveProduct> discountActiveProducts = discountActiveRepository.selectCurrentByProductId(productIds);
        if (CollectionUtils.isEmpty(discountActiveProducts)) {
            return Collections.emptyList();
        }
        Map<Long, List<DiscountActiveProduct>> productMap = discountActiveProducts.stream()
                .collect(Collectors.groupingBy(DiscountActiveProduct::getActiveId));
        List<DiscountActive> discountActives = discountActiveRepository.listByIds(productMap.keySet());
        List<DiscountActiveRule> discountActiveRules = discountActiveRuleRepository.listByActiveIds(productMap.keySet());
        Map<Long, List<DiscountActiveRule>> groupRuleMap = discountActiveRules.stream().collect(Collectors.groupingBy(DiscountActiveRule::getActiveId));
        List<DiscountActiveResp> result = discountActives.stream().map(active -> {
            DiscountActiveResp resp = JsonUtil.copy(active, DiscountActiveResp.class);
            List<DiscountActiveRule> rules = groupRuleMap.get(active.getId());
            resp.setRuleList(JsonUtil.copyList(rules, DiscountActiveRuleReq.class));
            List<DiscountActiveProduct> products = productMap.get(active.getId());
            resp.setProductList(JsonUtil.copyList(products, DiscountActiveProductDto.class));
            return resp;
        }).collect(Collectors.toList());
        return result;
    }
}