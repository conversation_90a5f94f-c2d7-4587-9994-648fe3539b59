package com.sankuai.shangou.seashop.product.core.service.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImage;

/**
 * <AUTHOR>
 * @date 2023/11/18 16:44
 */
public class SaveProductConverter {

    public static ProductImage convertToProductImage(String img, Long productId, Integer sequence) {
        ProductImage productImage = new ProductImage();
        productImage.setProductId(productId);
        productImage.setImageUrl(img);
        productImage.setSequence(sequence);
        return productImage;
    }

    public static List<ProductImage> convertToProductImage(List<String> imgList, Long productId) {
        if (CollectionUtils.isEmpty(imgList)) {
            return Collections.emptyList();
        }

        List<ProductImage> productImageList = new ArrayList<>();
        for (int i = 0 ; i < imgList.size() ; i++) {
            productImageList.add(convertToProductImage(imgList.get(i), productId, i));
        }
        return productImageList;
    }

}
