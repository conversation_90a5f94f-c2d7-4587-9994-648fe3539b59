package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductSkuMergeDto;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.PromotionResultCodeEnum;
import com.sankuai.shangou.seashop.promotion.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.promotion.common.remote.ShopRemoteService2;
import com.sankuai.shangou.seashop.promotion.core.model.bo.ExclusivePriceProductBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.ExclusivePriceSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.ExclusivePriceService;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.PromotionLogAssist;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePrice;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePriceProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceExtDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceProductParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceSimpleDto;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.ExclusivePriceProductRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.ExclusivePriceRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.ActiveStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductUpdateReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndMemberIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceDetailResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceSimpleResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@Service
@Slf4j
public class ExclusivePriceServiceImpl implements ExclusivePriceService {

    @Resource
    private ExclusivePriceRepository exclusivePriceRepository;
    @Resource
    private ExclusivePriceProductRepository exclusivePriceProductRepository;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private MemberRemoteService memberRemoteService;
    @Resource
    private ShopRemoteService2 shopRemoteService2;
    @Resource
    private PromotionLogAssist promotionLogAssist;

    @Override
//    @ExaminProcess(processModel = ExaminModelEnum.PROMOTION, processType = ExaProEnum.MODIFY, serviceMethod = "", dto = ExclusivePriceSaveBo.class, entity = ExclusivePrice.class)
    public void save(ExclusivePriceSaveBo saveBo) {
        log.info("ExclusivePriceService-sava-saveBo:{}", saveBo);

        ExclusivePrice exclusivePrice;
        ExclusivePriceResp oldExclusivePriceResp;
        if (null != saveBo.getId()) {
            // 更新
            exclusivePrice = exclusivePriceRepository.getById(saveBo.getId());
            if (null == exclusivePrice) {
                throw new BusinessException(PromotionResultCodeEnum.EXCLUSIVE_PRICE_NOT_FIND.getCode(), PromotionResultCodeEnum.EXCLUSIVE_PRICE_NOT_FIND.getMsg());
            }
            oldExclusivePriceResp = getById(saveBo.getId());
        }
        else {
            exclusivePrice = new ExclusivePrice();
            oldExclusivePriceResp = null;
        }
        BeanUtils.copyProperties(saveBo, exclusivePrice, "id");

        List<ExclusivePriceProductBo> productList = saveBo.getProductList();
        Map<Long, List<Long>> shopProductErrMap = new HashMap<>();
        // 同一个商家同一个商品同一个时间段只能参与一个专享价活动
        Map<Long, List<ExclusivePriceProductBo>> listMap = productList.stream().collect(Collectors.groupingBy(ExclusivePriceProductBo::getMemberId));
        for (Map.Entry<Long, List<ExclusivePriceProductBo>> entry : listMap.entrySet()) {
            Long memberId = entry.getKey();
            List<ExclusivePriceProductBo> value = entry.getValue();
            List<Long> productIds = value.stream().map(ExclusivePriceProductBo::getProductId).collect(Collectors.toList());
            ExclusivePriceParamDto paramDto = ExclusivePriceParamDto.builder()
                    .startTime(saveBo.getStartTime())
                    .endTime(saveBo.getEndTime())
                    .notEqId(saveBo.getId())
                    .shopId(saveBo.getShopId())
                    .memberId(memberId)
                    .productIds(productIds).build();
            List<ExclusivePriceExtDto> exclusivePrices = exclusivePriceRepository.listExtByParams(paramDto);
            if (CollectionUtils.isNotEmpty(exclusivePrices) && exclusivePrices.size() > 0) {
                shopProductErrMap.put(memberId, exclusivePrices.stream().map(ExclusivePriceExtDto::getProductId).distinct().collect(Collectors.toList()));
            }
        }

        /*
            处理提示消息
         */
        if (CollUtil.isNotEmpty(shopProductErrMap)) {
            log.info("【专享价】商品已存在活动中，{}", shopProductErrMap);
            Set<Long> memberIds = shopProductErrMap.keySet();
            Set<Long> productSet = new HashSet<>();
            shopProductErrMap.forEach((k, v) -> {
                productSet.addAll(v);
            });

            List<MemberResp> memberList = memberRemoteService.queryMembers(memberIds.stream().collect(Collectors.toList()));
            Map<Long, MemberResp> memberMap = memberList.stream().collect(Collectors.toMap(MemberResp::getId, Function.identity(), (k1, k2) -> k1));

            List<ProductBasicDto> remoteProductList = productRemoteService.queryProductBase(productSet.stream().collect(Collectors.toList()));
            Map<Long, ProductBasicDto> productMap = remoteProductList.stream().collect(Collectors.toMap(ProductBasicDto::getProductId, Function.identity(), (k1, k2) -> k1));

            StringBuffer errSb = new StringBuffer();
            shopProductErrMap.forEach((memberId, productIds) -> {
                MemberResp member = memberMap.get(memberId);
                AssertUtil.throwIfNull(member, "用户不存在: " + memberId);

                List<String> productNames = new ArrayList<>();
                productIds = productIds.stream().distinct().collect(Collectors.toList());
                productIds.forEach(productId -> {
                    ProductBasicDto product = productMap.get(productId);
                    AssertUtil.throwIfNull(product, "商品不存在:" + productId);
                    productNames.add(product.getProductName());
                });

                errSb.append(String.format("商品【%s】,商家【%s】已存在;", String.join(StrUtil.COMMA, productNames), member.getUserName()));
            });

            throw new BusinessException(PromotionResultCodeEnum.EXCLUSIVE_PRICE_PRODUCT_IN_ACTIVE.getCode(), errSb.length() > 0 ? errSb.toString()
                    : PromotionResultCodeEnum.EXCLUSIVE_PRICE_PRODUCT_IN_ACTIVE.getMsg());
        }

        // 商品数量包含Sku的，不进行去重
        Integer productSize = productList.stream().map(ExclusivePriceProductBo::getProductId).collect(Collectors.toList()).size();
        Integer memberSize = productList.stream().map(ExclusivePriceProductBo::getMemberId).distinct().collect(Collectors.toList()).size();
        exclusivePrice.setMemberCount(memberSize);
        exclusivePrice.setProductCount(productSize);

        TransactionHelper.doInTransaction(() -> {

            // 保存活动主体对象
            exclusivePriceRepository.saveOrUpdate(exclusivePrice);

            /*
             * 保存商品
             */
            List<ExclusivePriceProduct> oldExclusivePriceProducts = opExclusivePriceProducts(saveBo, exclusivePrice, productList);
            exclusivePriceProductRepository.saveOrUpdateBatch(oldExclusivePriceProducts);

            // 记录操作日志
            promotionLogAssist.recordExclusivePriceLog(saveBo.getOperationUserId(), saveBo.getOperationShopId(),
                    oldExclusivePriceResp, getById(exclusivePrice.getId()));
        });
    }

    @NotNull
    private List<ExclusivePriceProduct> opExclusivePriceProducts(ExclusivePriceSaveBo saveBo, ExclusivePrice exclusivePrice, List<ExclusivePriceProductBo> productList) {
        List<ExclusivePriceProduct> oldExclusivePriceProducts = new ArrayList<>();
        if (null != saveBo.getId()) {
            oldExclusivePriceProducts = exclusivePriceProductRepository.listByActiveId(saveBo.getId());
        }
        List<ExclusivePriceProduct> exclusivePriceProducts = JsonUtil.copyList(productList, ExclusivePriceProduct.class);

        opExclusivePriceList(exclusivePrice, oldExclusivePriceProducts, exclusivePriceProducts);
        return oldExclusivePriceProducts;
    }

    @Override
    public void endActive(BaseIdReq baseIdReq) {
        ExclusivePrice exclusivePrice = exclusivePriceRepository.getById(baseIdReq.getId());
        if (null == exclusivePrice) {
            throw new BusinessException(PromotionResultCodeEnum.EXCLUSIVE_PRICE_NOT_FIND.getCode(), PromotionResultCodeEnum.EXCLUSIVE_PRICE_NOT_FIND.getMsg());
        }
        Date now = new Date();
        Date endTime = exclusivePrice.getEndTime();
        if (endTime.before(now)) {
            throw new BusinessException(PromotionResultCodeEnum.EXCLUSIVE_PRICE_END.getCode(),
                    PromotionResultCodeEnum.EXCLUSIVE_PRICE_END.getMsg());
        }
        exclusivePrice.setEndTime(now);
        exclusivePriceRepository.updateById(exclusivePrice);

        // 记录操作日志
        promotionLogAssist.recordEndExclusivePriceLog(baseIdReq);
    }

    @Override
    public void updateProduct(ExclusivePriceProductUpdateReq req) {
        ExclusivePrice exclusivePrice = exclusivePriceRepository.getById(req.getId());
        if (null == exclusivePrice) {
            throw new BusinessException(PromotionResultCodeEnum.EXCLUSIVE_PRICE_NOT_FIND.getCode(), PromotionResultCodeEnum.EXCLUSIVE_PRICE_NOT_FIND.getMsg());
        }
        if (!req.getShopId().equals(exclusivePrice.getShopId())) {
            // 越权
            throw new BusinessException(PromotionResultCodeEnum.AUTHORITY_ERROR.getCode(), PromotionResultCodeEnum.AUTHORITY_ERROR.getMsg());
        }
        ExclusivePriceResp oldExclusivePriceResp = getById(req.getId());

        List<ExclusivePriceProduct> oldExclusivePriceProducts = exclusivePriceProductRepository.listByActiveId(req.getId());
        List<ExclusivePriceProduct> exclusivePriceProducts = JsonUtil.copyList(req.getProductList(), ExclusivePriceProduct.class);

        opExclusivePriceList(exclusivePrice, oldExclusivePriceProducts, exclusivePriceProducts);
        exclusivePriceProductRepository.saveOrUpdateBatch(oldExclusivePriceProducts);

        // 记录操作日志
        promotionLogAssist.recordExclusivePriceLog(req.getOperationUserId(), req.getOperationShopId(),
                oldExclusivePriceResp, getById(req.getId()));
    }

    private static void opExclusivePriceList(ExclusivePrice exclusivePrice, List<ExclusivePriceProduct> oldExclusivePriceProducts, List<ExclusivePriceProduct> exclusivePriceProducts) {
        int oldProductSize = oldExclusivePriceProducts.size();
        int newProductSize = exclusivePriceProducts.size();
        if (oldProductSize > newProductSize) {
            for (int i = newProductSize; i < oldProductSize; i++) {
                ExclusivePriceProduct exclusivePriceProduct = oldExclusivePriceProducts.get(i);
                exclusivePriceProduct.setDelFlag(Boolean.TRUE);
            }
            if (newProductSize > 0) {
                for (int i = 0; i < newProductSize; i++) {
                    ExclusivePriceProduct newProduct = exclusivePriceProducts.get(i);
                    ExclusivePriceProduct oldProduct = oldExclusivePriceProducts.get(i);
                    oldProduct.setProductId(newProduct.getProductId());
                    oldProduct.setMemberId(newProduct.getMemberId());
                    oldProduct.setSkuId(newProduct.getSkuId());
                    oldProduct.setPrice(newProduct.getPrice());
                    oldProduct.setActiveId(exclusivePrice.getId());
                }
            }
        }
        else if (oldProductSize < newProductSize) {
            for (int i = oldProductSize; i < newProductSize; i++) {
                ExclusivePriceProduct exclusivePriceProduct = exclusivePriceProducts.get(i);
                exclusivePriceProduct.setActiveId(exclusivePrice.getId());
                // 新增，所以要把Id设置成空
                exclusivePriceProduct.setId(null);
                oldExclusivePriceProducts.add(exclusivePriceProduct);
            }
        }
    }

    @Override
    public BasePageResp<ExclusivePriceSimpleResp> pageList(ExclusivePriceQueryReq request) {
        if (request.getStartTime() != null) {
            request.setStartTime(DateUtil.beginOfDay(request.getStartTime()));
        }
        if (request.getEndTime() != null) {
            request.setEndTime(DateUtil.endOfDay(request.getEndTime()));
        }

        List<ShopSimpleResp> shopSimpleRespList = new ArrayList<>();

        // 处理店铺名称模糊查询
        String shopName = request.getShopName();
        if (StrUtil.isNotBlank(shopName)) {
            ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                    ShopSimpleQueryReq.builder()
                            .shopName(shopName).build()
            );
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            shopSimpleRespList = shopSimpleListResp.getList();
            if (shopSimpleRespList.size() > PromotionConstant.MAX_LIKE_QUERY) {
                throw new BusinessException(PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getCode(),
                        PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getMsg());
            }
        }

        ExclusivePriceParamDto exclusivePriceParamModel = JsonUtil.copy(request, ExclusivePriceParamDto.class);
        if (CollUtil.isNotEmpty(shopSimpleRespList)) {
            // 拿到过滤后的店铺id
            List<Long> shopIds = shopSimpleRespList.stream().map(ShopSimpleResp::getId).collect(Collectors.toList());
            exclusivePriceParamModel.setShopIdList(shopIds);
        }

        BasePageResp<ExclusivePriceSimpleDto> pageResp = exclusivePriceRepository.pageList(request.buildPage(),
                exclusivePriceParamModel);
        if (null == pageResp || CollUtil.isEmpty(pageResp.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }

        if (CollUtil.isEmpty(shopSimpleRespList)) {
            List<ExclusivePriceSimpleDto> modelList = pageResp.getData();
            List<Long> shopIds = modelList.stream().map(ExclusivePriceSimpleDto::getShopId).distinct().collect(Collectors.toList());
            ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                    ShopSimpleQueryReq.builder()
                            .shopIdList(shopIds).build()
            );
            shopSimpleRespList = shopSimpleListResp.getList();
        }

        final List<ShopSimpleResp> shopSimpleModelsFinal = shopSimpleRespList;

        Date now = new Date();
        return PageResultHelper.transfer(pageResp, ExclusivePriceSimpleResp.class, exclusivePriceSimple -> {

            if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                shopSimpleModelsFinal.stream().filter(s -> s.getId().equals(exclusivePriceSimple.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                    exclusivePriceSimple.setShopName(shopSimpleResp.getShopName());
                });
            }

            if (now.before(exclusivePriceSimple.getStartTime()) && now.before(exclusivePriceSimple.getEndTime())) {
                exclusivePriceSimple.setStatus(ActiveStatusEnum.NOT_START.getCode());
                exclusivePriceSimple.setStatusDesc(ActiveStatusEnum.NOT_START.getMsg());
            }
            else if (now.after(exclusivePriceSimple.getStartTime()) && now.before(exclusivePriceSimple.getEndTime())) {
                exclusivePriceSimple.setStatus(ActiveStatusEnum.START.getCode());
                exclusivePriceSimple.setStatusDesc(ActiveStatusEnum.START.getMsg());
            }
            else {
                exclusivePriceSimple.setStatus(ActiveStatusEnum.END.getCode());
                exclusivePriceSimple.setStatusDesc(ActiveStatusEnum.END.getMsg());
            }
        });
    }

    @Override
    public ExclusivePriceResp getById(Long id) {
        ExclusivePrice exclusivePrice = exclusivePriceRepository.getById(id);
        if (null == exclusivePrice) {
            throw new BusinessException(PromotionResultCodeEnum.EXCLUSIVE_PRICE_NOT_FIND.getCode(), PromotionResultCodeEnum.EXCLUSIVE_PRICE_NOT_FIND.getMsg());
        }
        // 详情最多返回200条明细
        exclusivePrice.setQueryLimit(200L);
        ExclusivePriceResp exclusivePriceDto = getExclusivePriceResp(exclusivePrice);

        return exclusivePriceDto;
    }

    @Override
    public ExclusivePriceResp queryByProductAndMemberId(ProductAndMemberIdReq request) {
        if (request.getMemberId() == null) {
            return null;
        }

        Date now = new Date();
        ExclusivePriceParamDto paramDto = ExclusivePriceParamDto.builder()
                .memberId(request.getMemberId())
                .productIds(Arrays.asList(request.getProductId()))
                .startTime(now)
                .endTime(now)
                .build();
        List<ExclusivePrice> exclusivePrices = exclusivePriceRepository.listByParams(paramDto);
        if (CollUtil.isEmpty(exclusivePrices)) {
            return null;
        }
        ExclusivePrice exclusivePrice = exclusivePrices.get(0);
        exclusivePrice.setProductId(request.getProductId());
        exclusivePrice.setMemberId(request.getMemberId());
        ExclusivePriceResp exclusivePriceResp = getExclusivePriceResp(exclusivePrice);
        return exclusivePriceResp;
    }

    @Override
    public BasePageResp<ExclusivePriceDetailResp> detailPageList(ExclusivePriceQueryReq request) {

        List<ShopSimpleResp> shopSimpleRespList = new ArrayList<>();

        // 处理店铺名称模糊查询
        String shopName = request.getShopName();
        if (StrUtil.isNotBlank(shopName)) {
            ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                    ShopSimpleQueryReq.builder()
                            .shopName(shopName).build()
            );
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            shopSimpleRespList = shopSimpleListResp.getList();
            if (shopSimpleRespList.size() > PromotionConstant.MAX_LIKE_QUERY) {
                throw new BusinessException(PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getCode(),
                        PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getMsg());
            }
        }

        ExclusivePriceParamDto exclusivePriceParamModel = JsonUtil.copy(request, ExclusivePriceParamDto.class);
        if (CollUtil.isNotEmpty(shopSimpleRespList)) {
            // 拿到过滤后的店铺id
            List<Long> shopIds = shopSimpleRespList.stream().map(ShopSimpleResp::getId).collect(Collectors.toList());
            exclusivePriceParamModel.setShopIdList(shopIds);
        }

        BasePageResp<ExclusivePriceSimpleDto> pageResp = exclusivePriceRepository.pageList(request.buildPage(),
                exclusivePriceParamModel);
        if (null == pageResp || CollUtil.isEmpty(pageResp.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }

        // 查询店铺信息
        List<ExclusivePriceSimpleDto> modelList = pageResp.getData();
        if (CollUtil.isEmpty(shopSimpleRespList)) {
            List<Long> shopIds = modelList.stream().map(ExclusivePriceSimpleDto::getShopId).distinct().collect(Collectors.toList());
            ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                    ShopSimpleQueryReq.builder()
                            .shopIdList(shopIds).build()
            );
            shopSimpleRespList = shopSimpleListResp.getList();
        }
        final List<ShopSimpleResp> shopSimpleModelsFinal = shopSimpleRespList;

        // 明细信息查询
        List<Long> idList = modelList.stream().map(ExclusivePriceSimpleDto::getId).collect(Collectors.toList());
        List<ExclusivePriceProduct> exclusivePriceProducts = exclusivePriceProductRepository.listByActiveIds(idList);
        Map<Long, List<ExclusivePriceProduct>> exclusivePriceProductMap =
                exclusivePriceProducts.stream().collect(Collectors.groupingBy(ExclusivePriceProduct::getActiveId));

        Set<String> skuIdList = new HashSet<>();
        Set<Long> memberIdList = new HashSet<>();
        exclusivePriceProductMap.forEach((k, v) -> {
            v.forEach(exclusivePriceProduct -> {
                skuIdList.add(exclusivePriceProduct.getSkuId());
                memberIdList.add(exclusivePriceProduct.getMemberId());
            });
        });

        // 查询商品信息
        List<ProductSkuMergeDto> productSkuList = productRemoteService.getProductSkuMergeList(skuIdList.stream().collect(Collectors.toList()));
        Map<String, ProductSkuMergeDto> productSkuMap = productSkuList.stream().collect(Collectors.toMap(ProductSkuMergeDto::getSkuId, Function.identity(), (k1, k2) -> k1));

        // 商家信息
        List<MemberResp> memberRespList = memberRemoteService.queryMembers(memberIdList.stream().distinct().collect(Collectors.toList()));
        Map<Long, MemberResp> userMap = memberRespList.stream().collect(Collectors.toMap(MemberResp::getId, Function.identity(), (k1, k2) -> k1));

        Map<Long, List<ExclusivePriceProductDto>> exclusivePriceProductDtoMap = new HashMap<>();
        exclusivePriceProductMap.forEach((k, v) -> {
            List<ExclusivePriceProductDto> exclusivePriceProductDtoList = JsonUtil.copyList(v, ExclusivePriceProductDto.class);
            exclusivePriceProductDtoList.forEach(exclusivePriceProductDto -> {
                ProductSkuMergeDto productSku = productSkuMap.get(exclusivePriceProductDto.getSkuId());
                if (productSku != null) {
                    exclusivePriceProductDto.setProductName(productSku.getProductName());
                    exclusivePriceProductDto.setSkuName(
                            Optional.ofNullable(productSku.getSpec1Value()).orElse("")
                                    + Optional.ofNullable(productSku.getSpec2Value()).orElse("")
                                    + Optional.ofNullable(productSku.getSpec3Value()).orElse("")
                    );
                    exclusivePriceProductDto.setMallPrice(productSku.getSalePrice());
                    exclusivePriceProductDto.setSkuAutoId(productSku.getSkuAutoId());
                }

                MemberResp user = userMap.get(exclusivePriceProductDto.getMemberId());
                if (user != null) {
                    exclusivePriceProductDto.setUserName(user.getUserName());
                }
            });

            exclusivePriceProductDtoMap.put(k, exclusivePriceProductDtoList);
        });

        return PageResultHelper.transfer(pageResp, ExclusivePriceDetailResp.class, exclusivePriceSimple -> {

            if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                shopSimpleModelsFinal.stream().filter(s -> s.getId().equals(exclusivePriceSimple.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                    exclusivePriceSimple.setShopName(shopSimpleResp.getShopName());
                });
            }
            exclusivePriceSimple.setProductList(exclusivePriceProductDtoMap.get(exclusivePriceSimple.getId()));
        });
    }

    @NotNull
    private ExclusivePriceResp getExclusivePriceResp(ExclusivePrice exclusivePrice) {
        ExclusivePriceResp exclusivePriceDto = JsonUtil.copy(exclusivePrice, ExclusivePriceResp.class);
        ExclusivePriceProductParamDto paramDto = ExclusivePriceProductParamDto.builder()
                .activeId(exclusivePrice.getId())
                .productId(exclusivePrice.getProductId())
                .memberId(exclusivePrice.getMemberId())
                .queryLimit(exclusivePrice.getQueryLimit())
                .build();
        List<ExclusivePriceProduct> exclusivePriceProducts = exclusivePriceProductRepository.listByActiveOrMemberId(paramDto);
        List<ExclusivePriceProductDto> productDtoList = JsonUtil.copyList(exclusivePriceProducts, ExclusivePriceProductDto.class);
        exclusivePriceDto.setProductList(productDtoList);

        if (CollUtil.isNotEmpty(productDtoList)) {
            // 商品名称和SKU规格名称处理、商城价格填充
            List<String> skuIdList = productDtoList.stream().map(ExclusivePriceProductDto::getSkuId).collect(Collectors.toList());
            List<ProductSkuMergeDto> productSkuList = productRemoteService.getProductSkuMergeList(skuIdList);
            if (CollUtil.isNotEmpty(productSkuList)) {
                productDtoList.parallelStream().forEach(
                        productDto -> {
                            productSkuList.stream().filter(productSku -> productSku.getSkuId().equals(productDto.getSkuId())).findFirst().ifPresent(
                                    productSku -> {
                                        productDto.setProductName(productSku.getProductName());
                                        productDto.setSkuName(productSku.getSkuName());
                                        productDto.setMallPrice(productSku.getSalePrice());
                                        productDto.setSkuAutoId(productSku.getSkuAutoId());
                                    }
                            );
                        }
                );
            }

            List<Long> memberIdList = productDtoList.stream().map(ExclusivePriceProductDto::getMemberId).distinct().collect(Collectors.toList());
            List<MemberResp> memberList = memberRemoteService.queryMembers(memberIdList);
            if (CollUtil.isNotEmpty(memberList)) {
                productDtoList.parallelStream().forEach(
                        productDto -> {
                            memberList.stream().filter(memberDto -> memberDto.getId().equals(productDto.getMemberId())).findFirst().ifPresent(
                                    memberDto -> {
                                        productDto.setUserName(memberDto.getUserName());
                                    }
                            );
                        }
                );
            }
        }
        return exclusivePriceDto;
    }
}
