package com.sankuai.shangou.seashop.product.core.service.excel.read.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.base.eimport.anno.ExcelField;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/22 18:26
 */
@Getter
@Setter
public class ProductOffSaleDto extends RowReadResult {

    @ExcelProperty(value = "*商品ID")
    @ExcelField(required = true, regex = ParameterConstant.PRODUCT_ID_CHECK_REGEX, regexMsg = "商品ID格式错误")
    private String productId;

}
