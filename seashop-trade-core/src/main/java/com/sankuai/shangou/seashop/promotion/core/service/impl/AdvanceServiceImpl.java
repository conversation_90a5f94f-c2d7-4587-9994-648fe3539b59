package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.model.bo.AdvanceSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.AdvanceService;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.PromotionLogAssist;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.Advance;
import com.sankuai.shangou.seashop.promotion.dao.core.model.AdvanceDto;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.AdvanceRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@Service
@Slf4j
public class AdvanceServiceImpl implements AdvanceService {

    @Resource
    private AdvanceRepository advanceRepository;
    @Resource
    private PromotionLogAssist promotionLogAssist;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AdvanceSaveBo saveBo) {
        AdvanceDto oldAdvanceModel = getOne();

        log.info("AdvanceServiceImpl-save-saveBo:{}", saveBo);
        Advance saveAdvance = new Advance();
        List<Advance> advanceList = advanceRepository.list();
        if (CollectionUtils.isNotEmpty(advanceList)) {
            saveAdvance = advanceList.get(0);
        }
        // 需要指定一些默认值
        else {
            Date now = new Date();
            saveBo.setStartTime(saveBo.getStartTime() == null ? DateUtil.beginOfYear(now) : saveBo.getStartTime());
            saveBo.setEndTime(saveBo.getEndTime() == null ? DateUtil.endOfYear(now) : saveBo.getEndTime());
            saveBo.setIsReplay(saveBo.getIsReplay() == null ? false : saveBo.getIsReplay());
            saveBo.setImg(saveBo.getImg() == null ? "" : saveBo.getImg());
        }
        BeanUtils.copyProperties(saveBo, saveAdvance, "id");
        advanceRepository.saveOrUpdate(saveAdvance);

        // 记录操作日志
        promotionLogAssist.recordSaveAdvanceLog(saveBo.getOperationUserId(), saveBo.getOperationShopId(), oldAdvanceModel, getOne());
    }

    @Override
    public AdvanceDto getOne() {
        Advance saveAdvance = new Advance();
        List<Advance> advanceList = advanceRepository.list();
        if (CollectionUtils.isNotEmpty(advanceList)) {
            saveAdvance = advanceList.get(0);
        }
        return JsonUtil.copy(saveAdvance, AdvanceDto.class);
    }
}
