package com.sankuai.shangou.seashop.product.core.service.assist;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.common.remote.user.model.RemoteShopBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopBrand;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopBrandRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.result.ProductResultEnum;

import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2023/12/13 16:19
 */
@Component
public class BrandAssist {

    @Resource
    private BrandRepository brandRepository;
    @Resource
    private ShopBrandRepository shopBrandRepository;
    @Resource
    private RemoteShopService remoteShopService;

    private Brand checkBrandAuthInner(Long brandId, Long shopId) {
        // 检验品牌
        Brand brand = brandRepository.getById(brandId);
        AssertUtil.throwIfTrue(brand == null || brand.getWhetherDelete(), ProductResultEnum.BRAND_NOT_EXIST);
        long count = shopBrandRepository.count(new LambdaQueryWrapper<ShopBrand>().eq(ShopBrand::getShopId, shopId).eq(ShopBrand::getBrandId, brand.getId()));
        AssertUtil.throwIfTrue(count == 0, ProductResultEnum.NO_BRAND_AUTH);
        return brand;
    }

    public Brand checkBrandAuth(Long brandId, @NonNull RemoteShopBo shop) {
        // 品牌自营店 无需检验
        if (shop.getWhetherSelf()) {
            Brand brand = brandRepository.getById(brandId);
            AssertUtil.throwIfTrue(brand == null || brand.getWhetherDelete(), ProductResultEnum.BRAND_NOT_EXIST);
            return brand;
        }

        return checkBrandAuthInner(brandId, shop.getId());
    }

    /**
     * 获取品牌名称
     *
     * @param brandId 品牌id
     * @return 品牌名称
     */
    public String getBrandName(Long brandId) {
        Brand brand = brandRepository.getById(brandId);
        return brand == null ? "" : brand.getName();
    }

}
