package com.sankuai.shangou.seashop.product.core.service.model.product;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/25 16:39
 */
@Setter
@Getter
public class ProductRichTextBo {

    /**
     * 详情富文本
     */
    private String description;

    /**
     * 顶部版式
     */
    private String descriptionPrefix;

    /**
     * 底部版式
     */
    private String descriptionSuffix;

    public static ProductRichTextBo ofEmpty() {
        ProductRichTextBo richText = new ProductRichTextBo();
        richText.setDescription(StrUtil.EMPTY);
        richText.setDescriptionPrefix(StrUtil.EMPTY);
        richText.setDescriptionSuffix(StrUtil.EMPTY);
        return richText;
    }

}
