package com.sankuai.shangou.seashop.product.core.service.assist.comparator;

import org.apache.commons.lang3.StringUtils;

import cn.hutool.core.util.StrUtil;

/**
 * 字符串类型比较器(自定义比较器)
 * 该比较器会将null 转成空字符串进行比较
 *
 * <AUTHOR>
 * @date 2024/02/22 10:12
 */
public class NullAsEmptyStringComparator extends AbsFieldComparator<String> {

    @Override
    public boolean compareField(String newObj, String oldObj) {
        return StringUtils.equals(StrUtil.nullToEmpty(newObj), StrUtil.nullToEmpty(oldObj));
    }
}
