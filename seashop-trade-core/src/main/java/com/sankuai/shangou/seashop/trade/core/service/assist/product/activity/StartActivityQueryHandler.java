package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.handler.FlashSaleQueryHandler;

import lombok.extern.slf4j.Slf4j;

/**
 * 获取查询启动处理器
 * 该处理器用来启动整个活动查询链, 没有具体的业务逻辑
 *
 * <AUTHOR>
 * @date 2023/12/23 10:11
 */
@Component
@Slf4j
public class StartActivityQueryHandler extends AbsActivityQueryHandler {

    @Override
    protected void query(ActivityContext context) {
        log.info("【活动查询】 start, context: {}", context);
    }

    @Override
    protected Class<? extends AbsActivityQueryHandler> nextHandler() {
        return FlashSaleQueryHandler.class;
    }

    @Override
    protected boolean support(ActivityContext context) {
        return true;
    }
}
