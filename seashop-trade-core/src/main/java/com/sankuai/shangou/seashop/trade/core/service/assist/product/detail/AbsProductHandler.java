package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductContext;

/**
 * 抽象商品查询处理器
 *
 * <AUTHOR>
 * @date 2023/12/23 11:38
 */
public abstract class AbsProductHandler<T extends ProductContext> {

    /**
     * 查询具体商品的方法
     *
     * @param context 商品上下文对象
     */
    public abstract void handle(T context);

    /**
     * 处理器类型
     */
    public abstract ProductHandlerType type();

    /**
     * 调用顺序 从小到大执行
     *
     * @return 顺序
     */
    public abstract int order();

}
