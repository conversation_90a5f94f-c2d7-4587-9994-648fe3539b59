package com.sankuai.shangou.seashop.product.core.task;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.core.service.SkuStockService;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockTaskInfo;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockTaskInfoRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateStatusEnum;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/03/20 17:10
 */
@Component
@Slf4j
public class SkuStockTask {

    @Resource
    private SkuStockService skuStockService;
    @Resource
    private SkuStockTaskInfoRepository skuStockTaskInfoRepository;

    /**
     * 库存补偿定时任务
     */
//    @Crane("stockTaskCompensate")
//    @XxlJob("stockTaskCompensate")
    public void stockTaskCompensate() {
        // 查询库存任务明细 超过5分钟未执行的任务
        List<Integer> status = Arrays.asList(StockUpdateStatusEnum.WAIT.getCode(), StockUpdateStatusEnum.EXECUTING.getCode());
        boolean isEnd = false;
        while (!isEnd) {
            List<SkuStockTaskInfo> taskInfoList = skuStockTaskInfoRepository.list(new LambdaQueryWrapper<SkuStockTaskInfo>()
                    .in(SkuStockTaskInfo::getStatus, status)
                    .le(SkuStockTaskInfo::getUpdateTime, DateUtil.date(new Date()).offset(DateField.MINUTE, -5))
                    .last("limit " + CommonConstant.UPDATE_LIMIT));
            taskInfoList.forEach(taskInfo -> {
                try {
                    skuStockService.executeAsyncStock(taskInfo.getId());
                }
                catch (Exception e) {
                    log.error("[库存补偿]定时任务异常, taskInfoId: {}", taskInfo.getId(), e);
                }
            });

            if (taskInfoList.size() < CommonConstant.UPDATE_LIMIT) {
                isEnd = true;
            }
        }
    }

}
