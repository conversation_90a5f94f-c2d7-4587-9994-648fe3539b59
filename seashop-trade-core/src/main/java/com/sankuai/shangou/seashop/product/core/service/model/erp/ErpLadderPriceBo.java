package com.sankuai.shangou.seashop.product.core.service.model.erp;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/06 14:57
 */
@Getter
@Setter
@Builder
public class ErpLadderPriceBo {

    /**
     * 阶梯价id
     */
    private Long id;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 最小批量
     */
    private Integer minBath;

    /**
     * 最大批量
     */
    private Integer maxBath;

    /**
     * 价格
     */
    private BigDecimal price;
}
