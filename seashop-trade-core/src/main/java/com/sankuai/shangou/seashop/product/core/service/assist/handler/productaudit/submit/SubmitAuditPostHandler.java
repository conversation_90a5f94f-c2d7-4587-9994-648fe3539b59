package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.submit;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.ProductAuditService;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 提交审核后置处理器
 *
 * <AUTHOR>
 * @date 2023/12/29 11:15
 */
@Component
@Slf4j
public class SubmitAuditPostHandler extends AbsSubmitProductAuditHandler {

    @Resource
    private ProductAuditRepository productAuditRepository;
    @Resource
    private ProductAuditService productAuditService;

    @Override
    protected void handle(ProductContext context) {
        log.info("【商品提交审核】后置处理器【start】, productId: {}", context.getProductId());

        // 尝试自动审核，平台自营店的可以直接过审
        /*if (context.isSkipRisk()) {
            log.info("【商品提交审核】后置处理器, 跳过风控, productId: {}", context.getProductId());
            ProductAudit productAudit = new ProductAudit();
            productAudit.setProductId(context.getProductId());
            productAuditRepository.updateByProductId(productAudit);

            productAuditService.tryAutoAudit(context.getProductId());
            return;
        }*/

        productAuditService.tryAutoAudit(context.getProductId());

        log.info("【商品提交审核】后置处理器【end】, productId: {}", context.getProductId());
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SUBMIT_PRODUCT_AUDIT_POST;
    }


}
