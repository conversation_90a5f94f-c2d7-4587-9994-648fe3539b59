package com.sankuai.shangou.seashop.product.core.service.assist;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteBusinessCategoryService;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.common.remote.user.model.RemoteShopBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.result.ProductResultEnum;

import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2023/12/13 16:21
 */
@Component
public class CategoryAssist {

    @Resource
    private CategoryRepository categoryRepository;
    @Resource
    private RemoteBusinessCategoryService remoteBusinessCategoryService;
    @Resource
    private RemoteShopService remoteShopService;

    private Category checkCategoryAuthInner(Long categoryId, Long shopId) {
        // 检验分类
        Category category = categoryRepository.getById(categoryId);
        AssertUtil.throwIfTrue(category == null || category.getWhetherDelete(), ProductResultEnum.CATEGORY_NOT_EXIST);

        List<Long> validCategoryIds = remoteBusinessCategoryService.getValidBusinessCategoryIds(shopId);
        AssertUtil.throwIfTrue(!validCategoryIds.contains(categoryId), ProductResultEnum.NO_CATEGORY_AUTH);
        return category;
    }

    /**
     * 校验类目权限
     *
     * @param categoryIds
     * @param shopId
     * @return true-有权限 false-无权限
     */
    public Boolean checkCategoryAuth(List<Long> categoryIds, Long shopId) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Boolean.TRUE;
        }

        // 官方自营店, 有所有类目的权限
        RemoteShopBo shop = remoteShopService.getByShopId(shopId);
        if (shop.getWhetherSelf()) {
            return Boolean.TRUE;
        }

        // 查询有没有没有权限的类目
        List<Long> validCategoryIds = remoteBusinessCategoryService.getValidBusinessCategoryIds(shopId);
        validCategoryIds.addAll(CommonConstant.WHITE_CATEGORY_IDS);
        long count = categoryIds.stream().filter(categoryId -> !validCategoryIds.contains(categoryId)).count();
        return count == 0;
    }

    public Category checkCategoryAuth(Long categoryId, @NonNull RemoteShopBo shopBo) {
        // 品牌自营店 无需检验
        if (shopBo.getWhetherSelf() || CommonConstant.WHITE_CATEGORY_IDS.contains(categoryId)) {
            Category category = categoryRepository.getById(categoryId);
            AssertUtil.throwIfTrue(category == null || category.getWhetherDelete(), ProductResultEnum.CATEGORY_NOT_EXIST);
            return category;
        }

        return checkCategoryAuthInner(categoryId, shopBo.getId());
    }

    public List<Long> getCategoryIds(String categoryPath) {
        if (StringUtils.isEmpty(categoryPath)) {
            return Collections.EMPTY_LIST;
        }
        return Arrays.stream(categoryPath.split(CommonConstant.CATEGORY_PATH_SPLIT)).map(Long::parseLong).collect(Collectors.toList());
    }

    public List<Long> getParentIds(String categoryPath) {
        List<Long> categoryIds = getCategoryIds(categoryPath);
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.EMPTY_LIST;
        }
        return categoryIds.subList(0, categoryIds.size() - 1);
    }
}
