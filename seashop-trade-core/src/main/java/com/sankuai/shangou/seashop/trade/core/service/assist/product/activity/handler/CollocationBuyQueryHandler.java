package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.handler;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.AbsActivityQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 组合购查询处理器
 * todo
 *
 * <AUTHOR>
 * @date 2023/12/23 10:30
 */
@Component
@Slf4j
public class CollocationBuyQueryHandler extends AbsActivityQueryHandler {

    @Resource
    private PromotionRemoteService promotionRemoteService;

    @Override
    protected void query(ActivityContext context) {
        log.info("查询组合购活动, context: {}", context);
        context.setCollectionBuy(promotionRemoteService.getCollectionBuy(context.getProductId(), context.getShopId()));
        log.info("查询组合购活动结果, context: {}, activity: {}", context, JsonUtil.toJsonString(context.getCollectionBuy()));
    }

    @Override
    protected Class<? extends AbsActivityQueryHandler> nextHandler() {
        return DiscountActiveQueryHandler.class;
    }

    /**
     * 组合购与专享价、限时购、折扣、阶梯价互斥
     * 商品详情页面需要同时展示限时购购买和原价购买, 所以限时购不影响组合购
     * 折扣活动和组合购虽然互斥, 但是参加哪种活动由用户选择, 所以需要查询
     *
     * @param context 活动上下文对象
     * @return true 支持，false 不支持
     */
    @Override
    protected boolean support(ActivityContext context) {
        return context.getExclusivePrice() == null
                // && context.getFlashSale() == null
                // && context.getDiscountActive() == null
                && CollectionUtils.isEmpty(context.getLadderPriceList());
    }
}
