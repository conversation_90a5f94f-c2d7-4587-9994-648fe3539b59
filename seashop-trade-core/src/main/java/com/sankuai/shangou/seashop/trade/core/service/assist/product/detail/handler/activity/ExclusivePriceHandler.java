package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.ExclusivePriceBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.ExclusivePriceProductBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductSkuBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 专享价活动处理器
 *
 * <AUTHOR>
 * @date 2023/12/25 9:25
 */
@Slf4j
@Component
public class ExclusivePriceHandler extends AbsActivityHandler {

    @Override
    public void handle(DealActivityContext context) {
        ActivityContext activityContext = context.getActivityContext();
        ExclusivePriceBo exclusivePrice = activityContext.getExclusivePrice();
        if (exclusivePrice == null || CollectionUtils.isEmpty(exclusivePrice.getProductList())) {
            return;
        }

        Map<String, ExclusivePriceProductBo> eclusivePriceMap = exclusivePrice.getProductList()
                .stream().collect(Collectors.toMap(ExclusivePriceProductBo::getSkuId, Function.identity(), (k1, k2) -> k2));
        List<ProductSkuBo> skuList = context.getSkuList();
        BigDecimal estimatePrice = context.getEstimatePrice();
        for (ProductSkuBo sku : skuList) {
            ExclusivePriceProductBo exclusivePriceProductBo = eclusivePriceMap.get(sku.getSkuId());
            if (exclusivePriceProductBo == null) {
                continue;
            }
            sku.setSalePrice(exclusivePriceProductBo.getPrice());
            sku.setExclusiveSku(Boolean.TRUE);
            estimatePrice = estimatePrice.min(exclusivePriceProductBo.getPrice());
        }

        context.getActivityInfo().setHasExclusivePrice(true);
        // context.setEstimatePrice(estimatePrice);
    }

    @Override
    public int order() {
        return 2;
    }
}
