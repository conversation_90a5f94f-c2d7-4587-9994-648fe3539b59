package com.sankuai.shangou.seashop.product.core.service.model;

import java.util.Date;
import java.util.List;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:53
 */
@Getter
@Setter
@Builder
public class ShopCategoryBo {

    /**
     * 店铺分类id
     */
    private Long id;

    /**
     * 店铺分类名称
     */
    private String name;

    /**
     * 上级店铺分类id
     */
    private Long parentCategoryId;

    /**
     * 排序字段
     */
    private Long displaySequence;

    /**
     * 是否显示
     */
    private Boolean whetherShow;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 子分类
     */
    private List<ShopCategoryBo> childList;

    /**
     * 创建时间
     */
    private Date createTime;
}
