package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.EsCommentSummaryBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 评价信息查询
 *
 * <AUTHOR>
 * @date 2023/12/23 15:50
 */
@Component
@Slf4j
public class CommentInfoHandler extends AbsProductQueryHandler {

    @Resource
    private TradeProductService tradeProductService;

    @Override
    public void handle(ProductBaseContext context) {
        ProductBaseInfoBo product = context.getProduct();
        log.info("查询评价信息, context: {}", context);
        EsCommentSummaryBo commentSummary = tradeProductService.queryCommentSummary(context.getProductId());
        product.setCommentSummary(commentSummary);
    }

    @Override
    public int order() {
        return 3;
    }
}
