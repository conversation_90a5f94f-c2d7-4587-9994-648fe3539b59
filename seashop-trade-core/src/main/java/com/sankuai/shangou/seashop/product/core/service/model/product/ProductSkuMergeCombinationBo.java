package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/03/20 14:25
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuMergeCombinationBo {

    /**
     * 有效的商品
     */
    private List<ProductSkuMergeBo> productSkuList;

    /**
     * 失效的商品(主要是针对skuId 已经被删除的商品)
     */
    private List<ProductSkuMergeBo> invalidSkuList;
}
