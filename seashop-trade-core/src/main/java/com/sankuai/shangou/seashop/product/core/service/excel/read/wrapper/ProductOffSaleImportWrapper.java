package com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductOffSaleDto;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/21 22:14
 */
@AllArgsConstructor
public class ProductOffSaleImportWrapper extends DataWrapper<ProductOffSaleDto> {

    private List<ProductOffSaleDto> dataList;

    @Override
    public Integer getModule() {
        return null;
    }

    @Override
    public String getFileName() {
        return "商品下架导入错误数据";
    }

    @Override
    public List<ProductOffSaleDto> getDataList() {
        return dataList;
    }
}
