package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.promotion.core.service.PromotionProductService;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSale;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CollocationProductRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.FlashSaleRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.CollocationFlashSaleProductReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2024/3/7/007
 * @description:
 */
@Service
@Slf4j
public class PromotionProductServiceImpl implements PromotionProductService {

    @Resource
    private FlashSaleRepository flashSaleRepository;

    @Resource
    private CollocationProductRepository collocationProductRepository;

    @Override
    public List<Long> collocationFlashSaleProductIdList(Long shopId) {
        Set<Long> prodcutIdSet = new HashSet<>();

        List<FlashSale> flashSaleList = flashSaleRepository.listEffectiveByShopId(shopId);
        if (CollUtil.isNotEmpty(flashSaleList)) {
            prodcutIdSet.addAll(flashSaleList.stream().map(FlashSale::getProductId).collect(Collectors.toList()));
        }

        List<CollocationProduct> collocationProductList = collocationProductRepository.listEffectiveByShopId(shopId);
        if (CollUtil.isNotEmpty(collocationProductList)) {
            collocationProductList = collocationProductList.stream().filter(c -> c.getMainFlag()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collocationProductList)) {
                prodcutIdSet.addAll(collocationProductList.stream().map(CollocationProduct::getProductId).collect(Collectors.toList()));
            }
        }

        return new ArrayList<>(prodcutIdSet);
    }

    @Override
    public List<Long> collocationFlashSaleProductIdIncludeSub(CollocationFlashSaleProductReq req) {
        Long shopId = req.getShopId();

        Set<Long> prodcutIdSet = new HashSet<>();
        List<FlashSale> flashSaleList = flashSaleRepository.listEffectiveByShopId(shopId);
        if (CollUtil.isNotEmpty(flashSaleList)) {
            prodcutIdSet.addAll(flashSaleList.stream().map(FlashSale::getProductId).collect(Collectors.toList()));
        }

        List<CollocationProduct> collocationProductList = collocationProductRepository.listEffectiveByShopId(shopId);
        if (req.getIncludeSubProduct() != null && !req.getIncludeSubProduct()) {
            collocationProductList = collocationProductList.stream().filter(c -> c.getMainFlag()).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(collocationProductList)) {
            prodcutIdSet.addAll(collocationProductList.stream().map(CollocationProduct::getProductId).collect(Collectors.toList()));
        }

        return new ArrayList<>(prodcutIdSet);
    }
}
