package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleConfigService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.PlatFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleConfigQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@RestController
@RequestMapping("/flashSaleConfig")
public class FlashSaleConfigQueryController implements FlashSaleConfigQueryFeign {

    @Resource
    private FlashSaleConfigService flashSaleConfigService;

    @GetMapping(value = "/getPlatConfig")
    @Override
    public ResultDto<PlatFlashSaleConfigResp> getPlatConfig() throws TException {
        return ThriftResponseHelper.responseInvoke("getPlatConfig", null, req -> {
            PlatFlashSaleConfigResp platConfig = flashSaleConfigService.getPlatConfig();
            return platConfig;
        });
    }

    @PostMapping(value = "/getShopConfig", consumes = "application/json")
    @Override
    public ResultDto<ShopFlashSaleConfigResp> getShopConfig(@RequestBody ShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getShopConfig", request, req -> {
            req.checkParameter();
            ShopFlashSaleConfigResp shopConfig = flashSaleConfigService.getShopConfig(req);
            return shopConfig;
        });
    }
}
