package com.sankuai.shangou.seashop.product.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.ProductChangeEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 暂时先消费商品事件构建ES, 后续有了binlog就可以删掉
 *
 * <AUTHOR>
 * @date 2024/07/18 9:25
 */
//@Profile("develop")
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_PRODUCT_CHANGE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.GROUP_PRODUCT_ES_BUILD + "_${spring.profiles.active}")
public class ProductEsBuildListener implements RocketMQListener<MessageExt> {

    @Resource
    private ProductEsBuildService productEsBuildService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【商品变动】消息内容为: {}", body);
        try {
            ProductChangeEvent productChangeEvent = JsonUtil.parseObject(body, ProductChangeEvent.class);
            productEsBuildService.buildProductEs(productChangeEvent.getProductId());
            productEsBuildService.buildProductAuditEs(productChangeEvent.getProductId());
        } catch (Exception e) {
            log.error("【mafka消费】【商品变动】消息内容为: body: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
