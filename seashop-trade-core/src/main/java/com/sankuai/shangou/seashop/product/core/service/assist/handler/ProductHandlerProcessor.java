package com.sankuai.shangou.seashop.product.core.service.assist.handler;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

/**
 * <AUTHOR>
 * @date 2023/11/23 20:02
 */
@Component
public class ProductHandlerProcessor {

    @Resource
    private ProductHandlerContainer productHandlerContainer;

    public void handle(ProductHandlerType type, ProductContext context) {
        List<AbsProductHandler> handlers = productHandlerContainer.getHandlers(type);
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(handlers), "没有找到对应的处理器");
        handlers.forEach(handler -> {
            if (handler.support(context)) {
                handler.handle(context);
            }
        });
    }

}
