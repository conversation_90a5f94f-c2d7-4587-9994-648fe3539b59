package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.LockConstant;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleService;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSale;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleAddReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleAuditReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleCancelConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleShowReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleStockReturnReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleUpdateReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCmdFeign;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@RestController
@RequestMapping("/flashSale")
public class FlashSaleCmdController implements FlashSaleCmdFeign {

    @Resource
    private FlashSaleService flashSaleService;

    @PostMapping(value = "/add", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> add(@RequestBody FlashSaleAddReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("add", request, req -> {
            req.valueInit();
            req.checkParameter();
            String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_SUBMIT_KEY, req.getShopId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                flashSaleService.add(req);
            });
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/update", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> update(@RequestBody FlashSaleUpdateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("update", request, req -> {
            req.valueInit();
            req.checkParameter();
            String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_SAVE_KEY, req.getId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                flashSaleService.update(req);
            });
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/audit", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> audit(@RequestBody FlashSaleAuditReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("audit", request, req -> {
            req.checkParameter();
            String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_SAVE_KEY, req.getId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                flashSaleService.audit(req);
            });
            return BaseResp.of();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PROMOTION,
        processType = ExaProEnum.MODIFY,
        dto = FlashSaleShowReq.class,
        entity = FlashSale.class,
        actionName = "前段显示限时购活动")
    @PostMapping(value = "/show", consumes = "application/json")
    public ResultDto<BaseResp> show(@RequestBody FlashSaleShowReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("show", request, req -> {
            req.checkParameter();
            String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_SAVE_KEY, req.getId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                flashSaleService.show(req);
            });
            return BaseResp.of();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PROMOTION,
        processType = ExaProEnum.MODIFY,
        dto = BaseIdReq.class,
        entity = FlashSale.class,
        actionName = "结束限时购活动")
    @PostMapping(value = "/endActive", consumes = "application/json")
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("endActive", request, req -> {
            req.checkParameter();
            String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_SAVE_KEY, req.getId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                flashSaleService.endActive(req);
            });
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/consume", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> consume(@RequestBody FlashSaleConsumeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("consume", request, req -> {
            req.checkParameter();

            flashSaleService.consume(req);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/cancelConsume", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> cancelConsume(@RequestBody FlashSaleCancelConsumeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("cancelConsume", request, req -> {
            req.checkParameter();

            flashSaleService.cancelConsume(req);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/stockReturn", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> stockReturn(@RequestBody FlashSaleStockReturnReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("stockReturn", request, req -> {
            req.checkParameter();

            flashSaleService.stockReturn(req);
            return BaseResp.of();
        });
    }
}
