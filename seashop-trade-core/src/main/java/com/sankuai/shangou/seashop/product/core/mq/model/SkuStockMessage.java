package com.sankuai.shangou.seashop.product.core.mq.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 库存变动消息
 */
@NoArgsConstructor
@Getter
@Setter
@ToString
public class SkuStockMessage implements Serializable {

    /**
     * 主键
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 商品ID
     */
    @JsonProperty("product_id")
    private Long productId;

    /**
     * 商品ID_规格1ID_规格2ID_规格3ID
     */
    @JsonProperty("sku_id")
    private String skuId;

    /**
     * sku自增id
     */
    @JsonProperty("sku_auto_id")
    private Long skuAutoId;

    /**
     * 库存
     */
    @JsonProperty("stock")
    private Long stock;

    /**
     * 警戒库存
     */
    @JsonProperty("safe_stock")
    private Long safeStock;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonProperty("update_time")
    private Date updateTime;

}
