package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.BrandApplyService;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyQueryBo;
import com.sankuai.shangou.seashop.product.thrift.core.BrandApplyQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.CheckBrandNameReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.BrandApplyDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.CheckBrandNameResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandApplyDto;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/brandApply")
public class BrandApplyQueryController implements BrandApplyQueryFeign {

    @Resource
    private BrandApplyService brandApplyService;

    @PostMapping(value = "/queryBrandApplyForPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<BrandApplyDto>> queryBrandApplyForPage(@RequestBody QueryBrandApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandApplyForPage", request, req -> {

            BasePageResp<BrandApplyBo> pageResp = brandApplyService.pageBrandApply(req.buildPage(), JsonUtil.copy(req, BrandApplyQueryBo.class));
            return PageResultHelper.transfer(pageResp, BrandApplyDto.class);
        });
    }

    @PostMapping(value = "/queryBrandApplyDetailForSeller", consumes = "application/json")
    @Override
    public ResultDto<BrandApplyDetailResp> queryBrandApplyDetailForSeller(@RequestBody QueryBrandApplyDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandApplyDetailForSeller", request, req -> {

            BrandApplyBo brandApplyBo = brandApplyService.queryBrandApplyDetailForSeller(req.getId(), req.getShopId());
            return BrandApplyDetailResp.builder().result(JsonUtil.copy(brandApplyBo, BrandApplyDto.class)).build();
        });
    }

    @PostMapping(value = "/queryBrandApplyDetailForPlatForm", consumes = "application/json")
    @Override
    public ResultDto<BrandApplyDetailResp> queryBrandApplyDetailForPlatForm(@RequestBody QueryBrandApplyDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandApplyDetailForPlatForm", request, req -> {

            BrandApplyBo brandApplyBo = brandApplyService.queryBrandApplyDetailForPlatForm(req.getId());
            return BrandApplyDetailResp.builder().result(JsonUtil.copy(brandApplyBo, BrandApplyDto.class)).build();
        });
    }

    @PostMapping(value = "/checkBrandName", consumes = "application/json")
    @Override
    public ResultDto<CheckBrandNameResp> checkBrandName(@RequestBody CheckBrandNameReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("checkBrandName", request, req -> {

            String tips = brandApplyService.checkBrandName(req.getBrandName());
            return CheckBrandNameResp.builder().tips(tips).build();
        });
    }
}
