package com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/05/11 13:43
 */
@Setter
@Getter
public class FlashSaleAuditLogBo {

    @PrimaryField(title = "限时购id")
    @ExaminField(description = "限时购id")
    private Long id;

    @ExaminField(description = "是否通过审核")
    private Boolean passFlag;

}
