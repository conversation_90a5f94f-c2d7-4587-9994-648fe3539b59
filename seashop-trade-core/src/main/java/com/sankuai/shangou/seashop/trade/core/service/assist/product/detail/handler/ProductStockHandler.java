package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductSkuBo;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.TradeProductStatusEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 处理库存
 *
 * <AUTHOR>
 * @date 2024/03/05 14:28
 */
@Component
@Slf4j
public class ProductStockHandler extends AbsProductQueryHandler {

    @Override
    public void handle(ProductBaseContext context) {
        ProductBaseInfoBo product = context.getProduct();
        List<ProductSkuBo> skuList = product.getSkuList();

        // 计算商品总库存
        product.setTotalStock(skuList.stream().map(ProductSkuBo::getStock).filter(Objects::nonNull).reduce(Long::sum).orElse(0L));
        product.setFlashSaleTotalStock(skuList.stream().map(ProductSkuBo::getFlashSaleStock).filter(Objects::nonNull).reduce(Long::sum).orElse(0L));

        // 重置一下商品的销售状态
        TradeProductStatusEnum tradeStatus = ProductStatusEnum.ON_SALE.getCode().equals(product.getStatus())
                ? TradeProductStatusEnum.ON_SALE : TradeProductStatusEnum.OFF_SALE;
        // 如果是销售中 并且没有库存了 设置为
        if (tradeStatus.equals(TradeProductStatusEnum.ON_SALE) && product.getTotalStock() <= 0 && product.getFlashSaleTotalStock() <= 0) {
            tradeStatus = TradeProductStatusEnum.SOLD_OUT;
        }
        product.setTradeStatus(tradeStatus.getValue());
    }

    @Override
    public int order() {
        return 99;
    }
}
