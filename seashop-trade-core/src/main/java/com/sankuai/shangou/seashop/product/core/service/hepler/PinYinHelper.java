package com.sankuai.shangou.seashop.product.core.service.hepler;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;

/**
 * <AUTHOR>
 * @date 2023/12/15 17:13
 */
public class PinYinHelper {

    /**
     * 获取所有的大写字母
     */
    public static final String[] ALL_UPPER_LETTER = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    /**
     * 非字母的标识
     */
    public static final String EXT_SYMBOL = "+";

    /**
     * 获取中文字符串的的首字母
     */
    public static String getFirstUpperChar(String chinese) {
        if (StringUtils.isEmpty(chinese)) {
            return StrUtil.EMPTY;
        }
        String pinyin = PinyinUtil.getPinyin(chinese);
        if (StringUtils.isEmpty(pinyin)) {
            return StrUtil.EMPTY;
        }
        return pinyin.substring(0, 1).toUpperCase();
    }

    /**
     * 获取中文字符串的的首字母表示(不是字母返回拓展符号)
     *
     * @param chinese 中文字符串
     * @return 首字母或者拓展符号
     */
    public static String getFirstUpperCharOrExtSymbol(String chinese) {
        String firstUpperChar = getFirstUpperChar(chinese);
        if (Arrays.stream(ALL_UPPER_LETTER).filter(letter -> letter.equals(firstUpperChar)).count() > 0) {
            return firstUpperChar;
        }
        return EXT_SYMBOL;
    }

    /**
     * 获取所有的大写字母
     */
    public static String[] getAllUpperLetter() {
        return ALL_UPPER_LETTER;
    }

    /**
     * 获取所有大写字母和拓展符号
     */
    public static String[] getAllUpperLetterAndExtSymbol() {
        String[] allUpperLetter = getAllUpperLetter();
        String[] allUpperLetterAndExtSymbol = Arrays.copyOf(allUpperLetter, allUpperLetter.length + 1);
        allUpperLetterAndExtSymbol[allUpperLetter.length] = EXT_SYMBOL;
        return allUpperLetterAndExtSymbol;
    }

}
