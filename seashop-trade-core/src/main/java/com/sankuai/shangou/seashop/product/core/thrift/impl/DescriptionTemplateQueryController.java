package com.sankuai.shangou.seashop.product.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.DescriptionTemplateService;
import com.sankuai.shangou.seashop.product.core.service.model.DescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.core.service.model.DescriptionTemplateQueryBo;
import com.sankuai.shangou.seashop.product.thrift.core.DescriptionTemplateQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.QueryDescriptionTemplateDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.QueryDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.DescriptionTemplateDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.DescriptionTemplateListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.dto.DescriptionTemplateDetailDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.dto.DescriptionTemplateDto;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:26
 */
@RestController
@RequestMapping("/descriptionTemplate")
public class DescriptionTemplateQueryController implements DescriptionTemplateQueryFeign {

    @Resource
    private DescriptionTemplateService descriptionTemplateService;

    @PostMapping(value = "/queryDescriptionTemplateForPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<DescriptionTemplateDto>> queryDescriptionTemplateForPage(@RequestBody QueryDescriptionTemplateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryDescriptionTemplateForPage", request, req -> {
            req.checkParameter();
            BasePageResp<DescriptionTemplateBo> pageResult = descriptionTemplateService.pageDescriptionTemplate(req.buildPage(), JsonUtil.copy(req, DescriptionTemplateQueryBo.class));
            return PageResultHelper.transfer(pageResult, DescriptionTemplateDto.class);
        });
    }

    @PostMapping(value = "/queryDescriptionTemplateDetail", consumes = "application/json")
    @Override
    public ResultDto<DescriptionTemplateDetailResp> queryDescriptionTemplateDetail(@RequestBody QueryDescriptionTemplateDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryDescriptionTemplateDetail", request, req -> {
            req.checkParameter();

            DescriptionTemplateBo descriptionTemplateBo = descriptionTemplateService.queryDescriptionTemplateDetail(req.getId(), req.getShopId());
            return DescriptionTemplateDetailResp.builder().result(JsonUtil.copy(descriptionTemplateBo, DescriptionTemplateDetailDto.class)).build();
        });
    }

    @PostMapping(value = "/queryDescriptionTemplateForList", consumes = "application/json")
    @Override
    public ResultDto<DescriptionTemplateListResp> queryDescriptionTemplateForList(@RequestBody QueryDescriptionTemplateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryDescriptionTemplateForList", request, req -> {

            req.checkParameter();
            List<DescriptionTemplateBo> templateList = descriptionTemplateService.listDescriptionTemplate(JsonUtil.copy(req, DescriptionTemplateQueryBo.class));
            return DescriptionTemplateListResp.builder().templateList(JsonUtil.copyList(templateList, DescriptionTemplateDto.class)).build();
        });
    }
}
