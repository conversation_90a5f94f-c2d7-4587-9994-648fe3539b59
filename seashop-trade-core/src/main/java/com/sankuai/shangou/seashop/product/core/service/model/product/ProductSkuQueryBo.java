package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.util.List;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/25 10:22
 */
@Setter
@Getter
@Builder
public class ProductSkuQueryBo {

    /**
     * skuId集合
     */
    private List<String> skuIdList;

    /**
     * 是否需要阶梯价
     */
    private boolean needLadderFlag;

    /**
     * sku自增id集合
     */
    private List<Long> skuAutoIds;

    /**
     * 规格编号
     */
    private List<String> skuCodes;

    /**
     * 商品id集合
     */
    private List<Long> productIds;

    /**
     * 是否过滤掉阶梯价商品
     */
    private boolean filterLadderPrice;

    /**
     * 店铺id
     */
    private Long shopId;

}
