package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.core.model.bo.FullReductionQueryBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.FullReductionSaveBo;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionSimpleDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionResp;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
public interface FullReductionService {

    /**
     * 保存满减活动
     *
     * @param saveBo
     */
    void save(FullReductionSaveBo saveBo);

    /**
     * 提前结束活动
     *
     * @param idReq
     */
    void endActive(BaseIdReq idReq);

    /**
     * 分页查询满减活动
     *
     * @param queryBo
     * @return
     */
    BasePageResp<FullReductionSimpleDto> pageList(FullReductionQueryBo queryBo);

    /**
     * 根据id查询满减活动
     *
     * @param id
     * @return
     */
    FullReductionDto getById(Long id, Long shopId);

    /**
     * 根据店铺id查询满减活动
     *
     * @param request
     * @return
     */
    FullReductionResp queryByShopId(ShopIdReq request);

    /**
     * 查询当前可用的满减
     *
     * @return
     */
    FullReductionResp currentEnableFullReduction();
}
