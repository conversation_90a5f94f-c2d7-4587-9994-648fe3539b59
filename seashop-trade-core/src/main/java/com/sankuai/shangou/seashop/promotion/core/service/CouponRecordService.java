package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponRecordQueryBo;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordSimpleDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderListResp;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
public interface CouponRecordService {

    /**
     * 分页查询优惠券领取记录
     *
     * @param queryBo
     * @return
     */
    BasePageResp<CouponRecordSimpleDto> pageList(CouponRecordQueryBo queryBo);

    /**
     * 核销优惠券
     *
     * @param request
     */
    void consume(CouponRecordConsumeReq request);

    /**
     * 取消核销优惠券
     *
     * @param request
     */
    void cancelConsume(CouponRecordCancelConsumeReq request);

    /**
     * 通过id或者sn码查询优惠券记录信息
     *
     * @param request
     * @return
     */
    CouponRecordSimpleListResp getByIdOrSn(CouponRecordIdOrSnReq request);

    /**
     * 通过id查询优惠券记录信息
     *
     * @param request
     * @return
     */
    CouponRecordSimpleResp getUserRecordById(CouponRecordIdReq request);

    /**
     * 通过订单查询可用优惠券记录信息
     *
     * @param request
     * @return
     */
    CouponRecordOrderListResp getRecordByOrder(PromotionRecordOrderQueryReq request);
}
