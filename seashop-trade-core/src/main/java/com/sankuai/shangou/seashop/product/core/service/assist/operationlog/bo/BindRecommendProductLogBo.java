package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 19:42
 */
@Getter
@Setter
public class BindRecommendProductLogBo {

    @ExaminField(description = "商品ID")
    @PrimaryField
    private Long productId;

    @ExaminField(description = "推荐商品ID")
    private String relation;

    public static BindRecommendProductLogBo build(Long productId, String relation) {
        BindRecommendProductLogBo bo = new BindRecommendProductLogBo();
        bo.setProductId(productId);
        bo.setRelation(relation);
        return bo;
    }

}
