package com.sankuai.shangou.seashop.product.core.mq.listener;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.common.remote.order.OrderQueryRemoteService;
import com.sankuai.shangou.seashop.product.core.mq.model.OrderMessage;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.core.service.assist.event.OrderEventFactory;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 监听订单变动事件
 *
 * <AUTHOR>
 * @date 2024/10/31 9:35
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_ORDER_CHANGE  + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.GROUP_ORDER_PRODUCT + "_${spring.profiles.active}")
public class OrderChangeListener implements RocketMQListener<String> {
    
    @Resource
    private OrderEventFactory orderEventFactory;

    @Override
    public void onMessage(String msg) {
        try {
            log.info("[订单变动} 收到订单变动事件, body: {}", msg);
            OrderMessage body = JsonUtil.parseObject(msg, OrderMessage.class);

            orderEventFactory.handle(body);
        } catch (Exception e) {
            log.error("[订单变动] 消费失败, body: {}", msg, e);
            throw e;
        }
    }
}
