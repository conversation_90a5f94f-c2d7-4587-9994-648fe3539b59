package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.ProductAuditService;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditCompareBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;
import com.sankuai.shangou.seashop.product.thrift.core.ProductAuditQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductAuditQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.QueryProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditPageResp;

/**
 * <AUTHOR>
 * @date 2023/11/17 15:01
 */
@RestController
@RequestMapping("/productAudit")
public class ProductAuditQueryController implements ProductAuditQueryFeign {

    @Resource
    private ProductAuditService productAuditService;

    @PostMapping(value = "/queryProductAudit",consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ProductAuditPageResp>> queryProductAudit(@RequestBody QueryProductAuditReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductAudit", request, req -> {
            BasePageResp<ProductPageBo> pageResult = productAuditService.pageProductAudit(req.buildPage(), JsonUtil.copy(req, ProductQueryBo.class));
            return PageResultHelper.transfer(pageResult, ProductAuditPageResp.class);
        });
    }

    @PostMapping(value = "/queryProductAuditDetail",consumes = "application/json")
    @Override
    public ResultDto<ProductAuditDetailResp> queryProductAuditDetail(@RequestBody ProductAuditQueryDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductDetail", request, req -> {
            req.checkParameter();

            ProductAuditCompareBo compareBo = productAuditService.queryProductAuditDetail(JsonUtil.copy(req, ProductAuditQueryBo.class));
            return JsonUtil.copy(compareBo, ProductAuditDetailResp.class);
        });
    }
}
