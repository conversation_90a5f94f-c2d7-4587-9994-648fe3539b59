package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.query;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.ProductExtAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品类目查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:08
 */
@Component
@Slf4j
public class QueryCategoryAuditHandler extends AbsQueryProductAuditHandler {

    @Resource
    private ProductExtAssist productExtAssist;

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        productExtAssist.fillCategoryInfo(productBo);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_CATEGORY_AUDIT;
    }
}
