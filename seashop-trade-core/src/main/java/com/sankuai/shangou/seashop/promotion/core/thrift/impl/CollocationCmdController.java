package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.promotion.core.service.CollocationService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.AddCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CancelCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.UpdateCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 11:20
 */
@RestController
@RequestMapping("/collocation")
public class CollocationCmdController implements CollocationCmdFeign {

    @Resource
    private CollocationService collocationService;

    @PostMapping(value = "/addCollocation", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addCollocation(@RequestBody AddCollocationReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("addCollocation", request, req -> {
            collocationService.addCollocation(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateCollocation", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateCollocation(@RequestBody UpdateCollocationReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("updateCollocation", request, req -> {
            collocationService.updateCollocation(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/cancelCollocation", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> cancelCollocation(@RequestBody CancelCollocationReq request) throws TException {
        AssertUtil.throwIfNull(request.getId(), "入参ID不能为空");
        return ThriftResponseHelper.responseInvoke("cancelCollocation", request, req -> {
            collocationService.cancelCollocation(req);
            return new BaseResp();
        });
    }
}
