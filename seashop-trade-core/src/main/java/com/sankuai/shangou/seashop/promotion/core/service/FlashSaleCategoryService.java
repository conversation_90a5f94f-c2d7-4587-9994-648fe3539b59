package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleCategoryAddReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleCategoryResp;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
public interface FlashSaleCategoryService {

    /**
     * 新增限时购分类
     *
     * @param request
     */
    void add(FlashSaleCategoryAddReq request);

    /**
     * 删除限时购分类
     *
     * @param request
     */
    void delete(BaseIdReq request);

    /**
     * 分页查询限时购分类列表
     *
     * @param request
     * @return
     */
    BasePageResp<FlashSaleCategoryResp> pageList(BasePageReq request);
}
