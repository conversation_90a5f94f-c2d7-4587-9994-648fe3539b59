package com.sankuai.shangou.seashop.product.core.service.excel.read.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23 16:50
 */
@Getter
@Setter
public class ProductImportNewDto extends RowReadResult {

    @ExcelProperty(index = 0)
    private String text0;

    @ExcelProperty(index = 1)
    private String text1;

    @ExcelProperty(index = 2)
    private String text2;

    @ExcelProperty(index = 3)
    private String text3;

    @ExcelProperty(index = 4)
    private String text4;

    @ExcelProperty(index = 5)
    private String text5;

    @ExcelProperty(index = 6)
    private String text6;

    @ExcelProperty(index = 7)
    private String text7;

    @ExcelProperty(index = 8)
    private String text8;

    @ExcelProperty(index = 9)
    private String text9;

    @ExcelProperty(index = 10)
    private String text10;

    @ExcelProperty(index = 11)
    private String text11;

    @ExcelProperty(index = 12)
    private String text12;

    @ExcelProperty(index = 13)
    private String text13;

    @ExcelProperty(index = 14)
    private String text14;

    @ExcelProperty(index = 15)
    private String text15;

    @ExcelProperty(index = 16)
    private String text16;

    @ExcelProperty(index = 17)
    private String text17;

    @ExcelProperty(index = 18)
    private String text18;

    @ExcelProperty(index = 19)
    private String text19;

    @ExcelProperty(index = 20)
    private String text20;

    @ExcelProperty(index = 21)
    private String text21;

    @ExcelProperty(index = 22)
    private String text22;

    @ExcelProperty(index = 23)
    private String text23;

    @ExcelProperty(index = 24)
    private String text24;

    @ExcelProperty(index = 25)
    private String text25;

    @ExcelProperty(index = 26)
    private String text26;

    @ExcelProperty(index = 27)
    private String text27;

    @ExcelProperty(index = 28)
    private String text28;

    @ExcelProperty(index = 29)
    private String text29;

    @ExcelProperty(index = 30)
    private String text30;

    @ExcelIgnore
    private StringBuilder errBuilder = new StringBuilder();

    @ExcelIgnore
    private Long shopId;

    @ExcelIgnore
    private ProductBo productBo;

    @ExcelIgnore
    private Boolean izUpdate;
}
