package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;

import lombok.Getter;
import lombok.Setter;

/**
 * 商品查询上下文对象
 *
 * <AUTHOR>
 * @date 2023/12/23 11:39
 */
@Setter
@Getter
public abstract class ProductContext {

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 当前用户ID
     */
    private Long userId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 组合购id(传了该id 表示从组合购进入详情，需要使用组合购的价格)
     */
    private Long collocationId;

    @Override
    public String toString() {
        return JsonUtil.toJsonString(this);
    }
}
