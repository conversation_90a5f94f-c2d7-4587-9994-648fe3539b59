package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.SkuUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.result.ProductResultEnum;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * sku检查处理器
 *
 * <AUTHOR>
 * @date 2023/11/15 19:41
 */
@Component
@Slf4j
public class CheckSkuHandler extends AbsProductHandler {

    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuAssist skuAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】校验sku【start】, context:{}", context);

        ProductBo saveProductBo = context.getSaveProductBo();
        List<ProductSkuBo> skuList = saveProductBo.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        // 检验skuCode 是否重复
        checkSkuCode(context);
        // 检查skuId 是否重复
        checkSkuId(context);
        // 如果是单规格商品 需要将商品的单位赋值给sku
        if (saveProductBo.getHasSku() != null && !saveProductBo.getHasSku()) {
            skuList.forEach(sku -> sku.setMeasureUnit(saveProductBo.getMeasureUnit()));
        }
        log.info("【保存商品】校验sku【end】, context:{}", context);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getSkuList() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_SKU;
    }

    /**
     * 检验skuCode 是否重复
     *
     * @param context 上下文对象
     */
    private void checkSkuCode(ProductContext context) {
        List<ProductSkuBo> skuList = context.getSaveProductBo().getSkuList();
        List<String> skuCodes = skuList.stream().map(ProductSkuBo::getSkuCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<String> duplicatesSkuCodes = findDuplicatesUsingStream(skuCodes);
        AssertUtil.throwIfTrue(CollectionUtils.isNotEmpty(duplicatesSkuCodes), ProductResultEnum.SKU_CODE_EXIST,
                StrUtil.join(StrUtil.COMMA, duplicatesSkuCodes));

        List<Sku> skus = skuRepository.listSkuBySkuCodesAndShopId(skuCodes, context.getShopId());
        Map<String, Sku> dbSkuMap = skus.stream().collect(Collectors.toMap(Sku::getSkuCode, Function.identity(), (o1, o2) -> o1));
        skuList.forEach(saveSku -> {
            if (StringUtils.isEmpty(saveSku.getSkuCode())) {
                return;
            }

            Sku dbSku = dbSkuMap.get(saveSku.getSkuCode());
            if (dbSku == null) {
                return;
            }

            // 此时skuCode 在db中已经存在 如果是创建商品直接抛异常
            AssertUtil.throwIfTrue(!context.isEditFlag(), ProductResultEnum.SKU_CODE_EXIST, saveSku.getSkuCode());

            // 编辑商品
            AssertUtil.throwIfTrue(!context.getProductId().equals(dbSku.getProductId()), ProductResultEnum.SKU_CODE_EXIST, saveSku.getSkuCode());
            if (SkuUpdateKeyEnum.SKU_ID.equals(context.getSkuUpdateKey()) && context.isPartSave()) {
                AssertUtil.throwIfTrue(!saveSku.getSkuId().equals(dbSku.getSkuId()), ProductResultEnum.SKU_CODE_EXIST, saveSku.getSkuCode());
            }
        });
    }

    /**
     * 检验skuId 是否重复
     *
     * @param context 上下文对象
     */
    private void checkSkuId(ProductContext context) {
        List<ProductSkuBo> skuList = context.getSaveProductBo().getSkuList();
        List<String> skuIds = skuList.stream().map(ProductSkuBo::getSkuId).collect(Collectors.toList());
        String duplicatesSkuNames = getDuplicatesSkuNames(skuList);
        AssertUtil.throwIfTrue(StringUtils.isNotEmpty(duplicatesSkuNames), ProductResultEnum.SKU_ID_EXIST, duplicatesSkuNames);

        List<Sku> skus = skuRepository.listSkuBySkuIds(skuIds);
        Map<String, Sku> skuMap = skus.stream().collect(Collectors.toMap(Sku::getSkuId, Function.identity(), (o1, o2) -> o1));
        skuList.forEach(saveSku -> {
            if (StringUtils.isEmpty(saveSku.getSkuId())) {
                return;
            }

            Sku dbSku = skuMap.get(saveSku.getSkuId());
            if (dbSku == null) {
                return;
            }

            String specValue = skuAssist.getSpecValue(dbSku);

            // 此时skuId 在db中已经存在 如果是创建商品直接抛异常
            AssertUtil.throwIfTrue(!context.isEditFlag(), ProductResultEnum.SKU_ID_EXIST, specValue);

            // 编辑商品
            AssertUtil.throwIfTrue(!context.getProductId().equals(dbSku.getProductId()), ProductResultEnum.SKU_ID_EXIST, specValue);
            if (SkuUpdateKeyEnum.SKU_CODE.equals(context.getSkuUpdateKey()) && context.isPartSave()) {
                AssertUtil.throwIfTrue(!saveSku.getSkuCode().equals(dbSku.getSkuCode()), ProductResultEnum.SKU_ID_EXIST, specValue);
            }
        });
    }

    private String getDuplicatesSkuNames(List<ProductSkuBo> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }

        List<String> skuIds = skuList.stream().map(ProductSkuBo::getSkuId).collect(Collectors.toList());
        List<String> duplicatesSkuIds = findDuplicatesUsingStream(skuIds);
        if (CollectionUtils.isNotEmpty(duplicatesSkuIds)) {
            List<String> skuNames = skuList.stream().filter(item -> duplicatesSkuIds.contains(item.getSkuId()))
                    .map(item -> skuAssist.getSkuName(item.getSpec1Value(), item.getSpec2Value(), item.getSpec3Value())).collect(Collectors.toList());
            return StrUtil.join(StrUtil.COMMA, skuNames);
        }
        return null;
    }

    /**
     * 查找重复元素
     *
     * @param list 待查找的集合
     * @return 重复元素列表
     */
    public List<String> findDuplicatesUsingStream(List<String> list) {
        return list.stream()
                .filter(i -> Collections.frequency(list, i) > 1)
                .distinct()
                .collect(Collectors.toList());
    }
}
