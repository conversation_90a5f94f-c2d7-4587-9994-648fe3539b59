package com.sankuai.shangou.seashop.product.core.service.impl;


import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.core.service.BrandApplyService;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.ProductLogAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.SaveBrandApplyLogBo;
import com.sankuai.shangou.seashop.product.core.service.converter.BrandApplyConverter;
import com.sankuai.shangou.seashop.product.core.service.hepler.StringHelper;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyAuditBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplySaveBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.dao.core.domain.BrandApply;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopBrand;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandApplyRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopBrandRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:09
 */
@Service
public class BrandApplyServiceImpl implements BrandApplyService {

    @Resource
    private BrandRepository brandRepository;
    @Resource
    private BrandApplyRepository brandApplyRepository;
    @Resource
    private ShopBrandRepository shopBrandRepository;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private ProductLogAssist productLogAssist;

    @Override
    public void saveBrandApply(BrandApplySaveBo brandApplySaveBo) {
        String lock = LockConstant.BRAND_APPLY_LOCK + brandApplySaveBo.getBrandId();
        Long shopId = brandApplySaveBo.getShopId();
        LockHelper.lock(lock, () -> {
            checkBrandApplyForSave(brandApplySaveBo);

            BrandApply dbBrandApply = null;
            if (brandApplySaveBo.getId() != null) {
                dbBrandApply = brandApplyRepository.getById(brandApplySaveBo.getId());
            }

            BrandApply brandApply = BrandApplyConverter.convert(brandApplySaveBo);

            brandApply.setShopId(shopId);
            brandApply.setAuditStatus(BrandEnum.AuditStatusEnum.UNAUDITED.getCode());
            brandApply.setApplyTime(new Date());
            brandApplyRepository.saveOrUpdate(brandApply);

            // 记录操作日志
            productLogAssist.recordBrandApplyLog(brandApplySaveBo.getOperationUserId(),
                    brandApplySaveBo.getOperationShopId(),
                    SaveBrandApplyLogBo.build(BrandApplyConverter.convertToBo(dbBrandApply)),
                    SaveBrandApplyLogBo.build(BrandApplyConverter.convertToBo(brandApply)));
        });
    }

    @Override
    public BasePageResp<BrandApplyBo> pageBrandApply(BasePageParam basePageParam, BrandApplyQueryBo queryBo) {
                // 如果模糊查询的店铺名称
                if (StringUtils.isNotBlank(queryBo.getShopName())) {
            List<Long> shopIds = remoteShopService.getShopIdsByName(queryBo.getShopName());
            if (CollectionUtils.isEmpty(shopIds)) {
                BasePageResp<BrandApplyBo> brandApplyBoBasePageResp = PageResultHelper.defaultEmpty(basePageParam);
                return brandApplyBoBasePageResp;
            }

            AssertUtil.throwIfTrue(shopIds.size() > CommonConstant.QUERY_LIMIT, "请输入更准确的店铺名称");
            queryBo.setShopIds(shopIds);
        }

        // 查询店铺名称
        Page<BrandApply> pageResult = PageHelper.startPage(basePageParam);
        List<BrandApply> brandApplyList = brandApplyRepository.list(commonBrandApplyWrapperBuilder(queryBo));
        List<Long> shopIds = brandApplyList.stream().map(BrandApply::getShopId).collect(Collectors.toList());
        Map<Long, String> shopNameMap = remoteShopService.getShopNameMap(shopIds);

        return PageResultHelper.transfer(pageResult, db -> {
            BrandApplyBo bo = BrandApplyConverter.convertToBo(db);
            bo.setShopName(shopNameMap.get(db.getShopId()));
            return bo;
        });
    }

    @Override
    public BrandApplyBo queryBrandApplyDetailForSeller(Long id, Long shopId) {
        BrandApply brandApply = brandApplyRepository.getById(id);
        AssertUtil.throwIfTrue(brandApply == null || brandApply.getWhetherDelete(), "申请记录不存在");
        if (shopId != null) {
            AssertUtil.throwIfTrue(!brandApply.getShopId().equals(shopId), "无权操作");
        }

        return convertBrandApplyBo(brandApply);
    }

    @Override
    public BrandApplyBo queryBrandApplyDetailForPlatForm(Long id) {
        BrandApply brandApply = brandApplyRepository.getById(id);
        AssertUtil.throwIfTrue(brandApply == null || brandApply.getWhetherDelete(), "申请记录不存在");

        return convertBrandApplyBo(brandApply);
    }

    private BrandApplyBo convertBrandApplyBo(BrandApply brandApply) {
        BrandApplyBo bo = BrandApplyConverter.convertToBo(brandApply);
        bo.setShopName(remoteShopService.getShopName(brandApply.getShopId()));

        if (BrandEnum.ApplyModeEnum.NEW_BRAND.equals(bo.getApplyMode())) {
           return bo;
        }

        // 如果是平台已有平台 需要用最新的品牌信息覆盖
        Brand brand = brandRepository.getById(bo.getBrandId());
        if (brand != null) {
            bo.setDescription(brand.getDescription());
            bo.setLogo(brand.getLogo());
        }
        return bo;
    }

    @Override
    public void auditBrandApply(BrandApplyAuditBo auditBo) {
        String lock = LockConstant.BRAND_APPLY_AUDIT_LOCK + auditBo.getId();
        LockHelper.lock(lock, () -> {
            BrandApply brandApply = checkBrandApplyForAudit(auditBo);

            // 修改审核状态
            BrandApply updBrandApply = new BrandApply();
            updBrandApply.setId(auditBo.getId());
            updBrandApply.setAuditStatus(auditBo.getPassFlag() ? BrandEnum.AuditStatusEnum.AUDITED.getCode() : BrandEnum.AuditStatusEnum.AUDIT_REFUSED.getCode());
            updBrandApply.setAuditTime(new Date());
            updBrandApply.setPlatRemark(auditBo.getRejectReason());
            updBrandApply.setShopId(brandApply.getShopId());
            // 如果是拒绝则无需后续操作
            if (!auditBo.getPassFlag()) {
                brandApplyRepository.updateById(updBrandApply);

                // 记录操作日志
                productLogAssist.recordAuditBrandApplyLog(auditBo);
                return;
            }

            updBrandApply.setBrandId(brandApply.getBrandId());
            // 如果是新品牌 则需要初始化到品牌表中
            if (CommonConstant.APPLY_NEW_BRAND_ID.equals(brandApply.getBrandId())) {
                Brand brand = new Brand();
                brand.setName(brandApply.getBrandName());
                brand.setLogo(brandApply.getLogo());
                brand.setDescription(brandApply.getDescription());
                brandRepository.save(brand);

                updBrandApply.setBrandId(brand.getId());
            }

            // 商家关联品牌
            ShopBrand shopBrand = new ShopBrand();
            shopBrand.setShopId(updBrandApply.getShopId());
            shopBrand.setBrandId(updBrandApply.getBrandId());
            shopBrandRepository.save(shopBrand);

            // 更新审核表
            brandApplyRepository.updateById(updBrandApply);

            // 记录操作日志
            productLogAssist.recordAuditBrandApplyLog(auditBo);
        });
    }

    @Override
    public void deleteBrandApply(Long id) {
        BrandApply brandApply = brandApplyRepository.getById(id);
        AssertUtil.throwIfTrue(brandApply == null || brandApply.getWhetherDelete(), "申请记录不存在");

        BrandApply updBrandApply = new BrandApply();
        updBrandApply.setId(id);
        updBrandApply.setWhetherDelete(Boolean.TRUE);
        brandApplyRepository.updateById(updBrandApply);
    }

    @Override
    public String checkBrandName(String brandName) {
        long count = brandRepository.count(new LambdaQueryWrapper<Brand>()
                .eq(Brand::getName, brandName).eq(Brand::getWhetherDelete, Boolean.FALSE));
        return count > 0 ? CommonConstant.BRAND_NAME_EXIST_TIPS : StrUtil.EMPTY;
    }

    @Override
    public List<BrandApplyBo> queryBrandApplyList(List<Long> brandIds) {
        return brandApplyRepository.list(new LambdaQueryWrapper<BrandApply>().in(BrandApply::getBrandId, brandIds)).stream()
                .map(BrandApplyConverter::convertToBo).collect(Collectors.toList());
    }

    /**
     * 构建品牌申请公共筛选参数
     *
     * @param queryBo 筛选参数
     * @return 筛选条件
     */
    private LambdaQueryWrapper<BrandApply> commonBrandApplyWrapperBuilder(BrandApplyQueryBo queryBo) {
        LambdaQueryWrapper<BrandApply> wrapper = new LambdaQueryWrapper<>();
        if (queryBo.getBrandId() != null) {
            wrapper.eq(BrandApply::getBrandId, queryBo.getBrandId());
        }
        if (!StringUtils.isEmpty(queryBo.getBrandName())) {
            wrapper.like(BrandApply::getBrandName, StringHelper.escapeSpecialChar(queryBo.getBrandName()));
        }
        if (queryBo.getAuditStatus() != null) {
            wrapper.eq(BrandApply::getAuditStatus, queryBo.getAuditStatus().getCode());
        }
        if (queryBo.getShopId() != null) {
            wrapper.eq(BrandApply::getShopId, queryBo.getShopId());
        }
        // 已经在调用方限制不超过200
        if (!CollectionUtils.isEmpty(queryBo.getShopIds())) {
            wrapper.in(BrandApply::getShopId, queryBo.getShopIds());
        }
        wrapper.eq(BrandApply::getWhetherDelete, Boolean.FALSE);
        if (!CollectionUtils.isEmpty(queryBo.getSortList())) {
            wrapper.last("order by " + MybatisUtil.getOrderSql(queryBo.getSortList()));
        }
        else {
            wrapper.orderByDesc(BrandApply::getApplyTime);
        }
        return wrapper;
    }

    /**
     * 校验品牌申请参数(如果是平台已有品牌则需要将品牌信息设置到申请参数中)
     * <p>
     * 校验规则如下:
     * 平台已有品牌:
     * 1.校验brandId 是否在数据库有效
     * 2.是否已经有该品牌的经营权限
     * 3.是否存在该品牌的申请记录
     * 供应商新增品牌:
     * 1.判断是否已经存在同名的品牌
     * 2.是否存在同名的申请记录
     *
     * @param brandApplyBo 品牌申请参数
     */
    private void checkBrandApplyForSave(BrandApplySaveBo brandApplyBo) {
        Long shopId = brandApplyBo.getShopId();
        boolean editFlag = brandApplyBo.getId() != null && brandApplyBo.getId() > 0;

        // 如果是编辑 校验申请类型不能修改
        if (editFlag) {
            BrandApply brandApply = brandApplyRepository.getById(brandApplyBo.getId());
            AssertUtil.throwIfTrue(brandApply == null || brandApply.getWhetherDelete(), "申请记录不存在");
            AssertUtil.throwIfTrue(!brandApply.getShopId().equals(shopId), "无权操作");
            AssertUtil.throwIfTrue(!BrandEnum.AuditStatusEnum.AUDIT_REFUSED.getCode().equals(brandApply.getAuditStatus()), "当前记录状态不允许编辑");
            AssertUtil.throwIfTrue(!brandApply.getApplyMode().equals(brandApplyBo.getApplyMode().getCode()), "申请类型不能修改");
        }

        // 平台已有品牌
        if (BrandEnum.ApplyModeEnum.EXISTING_BRAND.equals(brandApplyBo.getApplyMode())) {
            Brand brand = brandRepository.getById(brandApplyBo.getBrandId());
            AssertUtil.throwIfTrue(brand == null || brand.getWhetherDelete(), "品牌不存在");

            long count = shopBrandRepository.count(new LambdaQueryWrapper<ShopBrand>().eq(ShopBrand::getBrandId,
                    brandApplyBo.getBrandId()).eq(ShopBrand::getShopId, shopId));
            AssertUtil.throwIfTrue(count > 0, "您已经有该品牌的经营权限了");

            count = brandApplyRepository.count(new LambdaQueryWrapper<BrandApply>()
                    .eq(BrandApply::getBrandId, brandApplyBo.getBrandId())
                    .ne(editFlag, BrandApply::getId, brandApplyBo.getId())
                    .eq(BrandApply::getShopId, shopId).eq(BrandApply::getWhetherDelete, Boolean.FALSE));
            AssertUtil.throwIfTrue(count > 0, "该品牌申请已存在, 请选择其他品牌");

            // 将已有品牌的信息设置到申请参数中
            brandApplyBo.setBrandName(brand.getName());
            brandApplyBo.setLogo(brand.getLogo());
            brandApplyBo.setDescription(brand.getDescription());
        }
        // 供应商新增品牌
        else {
            long count = brandRepository.count(new LambdaQueryWrapper<Brand>()
                    .eq(Brand::getName, brandApplyBo.getBrandName()).eq(Brand::getWhetherDelete, Boolean.FALSE));
            AssertUtil.throwIfTrue(count > 0, "已存在相同品牌, 请重新输入");

            count = brandApplyRepository.count(new LambdaQueryWrapper<BrandApply>()
                    .eq(BrandApply::getBrandName, brandApplyBo.getBrandName())
                    .eq(BrandApply::getShopId, shopId)
                    .ne(editFlag, BrandApply::getId, brandApplyBo.getId())
                    .eq(BrandApply::getWhetherDelete, Boolean.FALSE));
            AssertUtil.throwIfTrue(count > 0, "该品牌申请已存在, 请填写其他品牌");

            brandApplyBo.setBrandId(CommonConstant.APPLY_NEW_BRAND_ID);
        }
    }

    /**
     * 校验品牌申请审核参数, 并返回品牌申请记录
     * 检验规则如下:
     * 1.校验id是否有效, 如果是审核拒绝则无需后续校验
     * 2.校验品牌申请是否已经审核
     * 3.判断申请类型
     * 3.1 如果是平台已有品牌
     * 3.1.1 校验品牌是否存在（审核过程中品牌可以已经被删除）
     * 3.2.2 检验商家是否已经有该品牌的经营权限
     * 3.2 如果是供应商新增品牌
     * 3.2.1 则校验品牌名称是否已经存在
     * (如果存在则表示审核过程中已经有其他人申请了同名品牌，并且审核已经被同意, 审核无法通过)
     *
     * @param auditBo 审核参数
     * @return 品牌申请记录
     */
    private BrandApply checkBrandApplyForAudit(BrandApplyAuditBo auditBo) {
        BrandApply brandApply = brandApplyRepository.getById(auditBo.getId());
        AssertUtil.throwIfTrue(brandApply == null || brandApply.getWhetherDelete(), "申请记录不存在");
        AssertUtil.throwIfTrue(!BrandEnum.AuditStatusEnum.UNAUDITED.getCode().equals(brandApply.getAuditStatus()), "申请记录已经审核过了");

        // 如果是拒绝 无需做后续检测
        if (!auditBo.getPassFlag()) {
            return brandApply;
        }

        // 平台已有品牌
        if (BrandEnum.ApplyModeEnum.EXISTING_BRAND.getCode().equals(brandApply.getApplyMode())) {
            Brand brand = brandRepository.getById(brandApply.getBrandId());
            AssertUtil.throwIfTrue(brand == null || brand.getWhetherDelete(), "申请品牌不存在或者已经被删除");

            // 在申请的时候已经校验过了, 这里重复校验一次, 非极端情况不会进入
            long count = shopBrandRepository.count(new LambdaQueryWrapper<ShopBrand>()
                    .eq(ShopBrand::getBrandId, brandApply.getBrandId()).eq(ShopBrand::getShopId, brandApply.getShopId()));
            AssertUtil.throwIfTrue(count > 0, "该商家已经有该品牌的经营权限了, 请拒绝该申请");
        }
        // 供应商新增品牌
        else {
            Brand brand = brandRepository.getOne(new LambdaQueryWrapper<Brand>()
                    .eq(Brand::getName, brandApply.getBrandName()).eq(Brand::getWhetherDelete, Boolean.FALSE));
            if (brand != null) {
                brandApply.setBrandId(brand.getId());
            }
        }
        return brandApply;
    }
}
