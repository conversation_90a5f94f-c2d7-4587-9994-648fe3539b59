package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.SkuStockService;
import com.sankuai.shangou.seashop.product.core.service.model.RollBackStockBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskInfoBo;
import com.sankuai.shangou.seashop.product.thrift.core.SkuStockCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.stock.RollBackStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.stock.UpdateStockReq;

/**
 * <AUTHOR>
 * @date 2023/12/18 15:49
 */
@RestController
@RequestMapping("/skuStock")
public class SkuStockCmdController implements SkuStockCmdFeign {

    @Resource
    private SkuStockService skuStockService;

    @PostMapping(value = "/syncChangeStock", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> syncChangeStock(@RequestBody UpdateStockReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("changeSkuStock", request, req -> {
            req.checkParameter();

            StockTaskBo updateBo = JsonUtil.copy(req, StockTaskBo.class);
            updateBo.setTaskInfoBoList(JsonUtil.copyList(req.getStockList(), StockTaskInfoBo.class));
            skuStockService.syncChangeStock(updateBo);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/aSyncChangeStock", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> aSyncChangeStock(@RequestBody UpdateStockReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("coverSkuStock", request, req -> {
            req.checkParameter();

            StockTaskBo updateBo = JsonUtil.copy(req, StockTaskBo.class);
            updateBo.setTaskInfoBoList(JsonUtil.copyList(req.getStockList(), StockTaskInfoBo.class));
            skuStockService.asyncChangeSkuStock(updateBo);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/rollBackSkuStock", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> rollBackSkuStock(@RequestBody RollBackStockReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("rollBackSkuStock", request, req -> {
            req.checkParameter();

            skuStockService.rollBackSkuStock(JsonUtil.copy(req, RollBackStockBo.class));
            return new BaseResp();
        });
    }
}
