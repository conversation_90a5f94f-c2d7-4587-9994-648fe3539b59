package com.sankuai.shangou.seashop.product.core.service.assist.listener.event;

import org.springframework.transaction.event.TransactionPhase;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.handler.AbstractHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.handler.SendCategoryChangeHandler;
import com.sankuai.shangou.seashop.product.thrift.core.event.category.CategoryChangeEvent;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/05/05 14:56
 */
@Setter
@Getter
public class SendCategoryChangeEvent extends AbstractTransactionEvent<CategoryChangeEvent> {
    @Override
    public Class<? extends AbstractHandler> getHandler() {
        return SendCategoryChangeHandler.class;
    }

    @Override
    public TransactionPhase getTransactionPhase() {
        return TransactionPhase.AFTER_COMMIT;
    }
}
