package com.sankuai.shangou.seashop.product.core.service.excel.read.handler;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.assist.LadderPriceAssist;
import com.sankuai.shangou.seashop.product.core.service.excel.read.BizTypeEnum;
import com.sankuai.shangou.seashop.product.core.service.excel.read.context.ProductImportAssist;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.PriceImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper.PriceImportWrapper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.SkuUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 价格导入处理器
 *
 * <AUTHOR>
 * @date 2023/11/21 22:08
 */
@Component
@Slf4j
public class PriceImportHandler extends ImportHandler<PriceImportDto> {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private LadderPriceAssist ladderPriceAssist;
    @Resource
    private ProductService productService;

    @Override
    public BizType bizType() {
        return BizTypeEnum.PRICE_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<PriceImportDto> importResult) {
        log.info("【价格导入】 参数校验");

        // 遍历出正确第一步检验通过的数据
        List<PriceImportDto> dataList = importResult.getSuccessDataList();

        // 校验价格导入
        checkPriceImport(dataList);
        buildErrMsg(dataList);
        // 构建阶梯价数组
        initLadderPriceList(importResult.getSuccessDataList());
    }

    private void checkPriceImport(List<PriceImportDto> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<Long> productIdList = dataList.stream().map(item -> Long.parseLong(item.getProductId())).collect(Collectors.toList());
        List<Long> skuAutoIdList = dataList.stream().filter(item -> StringUtils.isNotEmpty(item.getSkuAutoId())).map(item -> Long.parseLong(item.getSkuAutoId())).collect(Collectors.toList());
        Map<Long, Product> productMap = productRepository.getProductMap(productIdList);
        Map<Long, Sku> skuMap = skuRepository.getSkuMap(skuAutoIdList);
        // Map<Long, ProductAudit> productAuditMap = productAuditRepository.getProductAuditMap(productIdList);

        Long curShopId = ProductImportAssist.getShopIdOrThrow();
        dataList.forEach(item -> {
            Long productId = Long.parseLong(item.getProductId());
            Product product = productMap.get(productId);

            // 校验商品
            if (product == null || product.getWhetherDelete()) {
                item.getErrBuilder().append("商品ID不存在;");
                return;
            }
            if (!curShopId.equals(product.getShopId())) {
                item.getErrBuilder().append("该商品不属于当前店铺;");
            }

            // 校验sku
            if (StringUtils.isNotEmpty(item.getSkuAutoId())) {
                Sku sku = skuMap.get(Long.parseLong(item.getSkuAutoId()));
                if (sku == null) {
                    item.getErrBuilder().append("规格ID不存在;");
                    return;
                }

                if (!sku.getProductId().equals(Long.parseLong(item.getProductId()))) {
                    item.getErrBuilder().append("规格ID不属于该商品;");
                }

                if (!curShopId.equals(sku.getShopId())) {
                    item.getErrBuilder().append("该规格不属于当前店铺;");
                }

                item.setSkuId(sku.getSkuId());
                item.setShopId(sku.getShopId());
            }

            // 校验审核状态
            /*ProductAudit productAudit = productAuditMap.get(productId);
            if (productAudit != null) {
                if (ProductEnum.AuditStatusEnum.NOT_PASS.getCode().equals(productAudit.getAuditStatus())) {
                    item.getErrorBuilder().append(String.format("该商品被审核拒绝,不能做导入;"));
                }
                else {
                    item.getErrorBuilder().append(String.format("该商品正在审核中,不能做导入;"));
                }
            }*/
        });
    }

    @Override
    public void saveImportData(List<PriceImportDto> successList) {
        successList.forEach(product -> {
            ProductBo productBo = productBoBuild(product);
            try {
                productService.saveProduct(productBo, ProductSourceEnum.MALL, ProductChangeType.IMPORT_PRICE, true, true);
            }
            catch (Exception e) {
                product.setErrMsg(String.format("导入失败: 【%s】", e.getMessage()));
                log.error("商品价格更新导入失败: product: {}", product, e);
            }
        });
    }

    @Override
    public DataWrapper<PriceImportDto> wrapData(List<PriceImportDto> errList) {
        return new PriceImportWrapper(errList);
    }

    /**
     * 初始化阶梯价列表
     *
     * @param dataList
     */
    private void initLadderPriceList(List<PriceImportDto> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        dataList.forEach(item -> {
            List<ProductLadderPriceBo> ladderPriceList = new ArrayList<>();
            if (StringUtils.isNotEmpty(item.getMinBath1()) && StringUtils.isNotEmpty(item.getLadderPrice1())) {
                ProductLadderPriceBo bo = ProductLadderPriceBo.builder()
                        .minBath(Integer.parseInt(item.getMinBath1())).price(new BigDecimal(item.getLadderPrice1())).build();
                ladderPriceList.add(bo);
            }
            if (StringUtils.isNotEmpty(item.getMinBath2()) && StringUtils.isNotEmpty(item.getLadderPrice2())) {
                ProductLadderPriceBo bo = ProductLadderPriceBo.builder()
                        .minBath(Integer.parseInt(item.getMinBath2())).price(new BigDecimal(item.getLadderPrice2())).build();
                ladderPriceList.add(bo);
            }
            if (StringUtils.isNotEmpty(item.getMinBath3()) && StringUtils.isNotEmpty(item.getLadderPrice3())) {
                ProductLadderPriceBo bo = ProductLadderPriceBo.builder()
                        .minBath(Integer.parseInt(item.getMinBath3())).price(new BigDecimal(item.getLadderPrice3())).build();
                ladderPriceList.add(bo);
            }
            ladderPriceAssist.handleLadderPrice(ladderPriceList);
            item.setLadderPriceBoList(ladderPriceList);
        });
    }

    /**
     * 构建商品对象
     *
     * @param priceImport
     * @return
     */
    private ProductBo productBoBuild(PriceImportDto priceImport) {
        Long operationUserId = ProductImportAssist.getOperationUserId();
        Long operationShopId = ProductImportAssist.getOperationShopId();

        ProductBo product = JsonUtil.copy(priceImport, ProductBo.class);
        product.setWhetherOpenLadder(priceImport.isWhetherOpenLadderPriceFlag());
        product.setShopId(ProductImportAssist.getShopIdOrThrow());
        product.setOperationUserId(operationUserId);
        product.setOperationShopId(operationShopId);
        // 开启了阶梯价
        if (priceImport.isWhetherOpenLadderPriceFlag()) {
            product.setLadderPriceList(priceImport.getLadderPriceBoList());
            return product;
        }

        // 没有开启阶梯价 skuAutoId 必填
        ProductSkuBo sku = new ProductSkuBo();
        sku.setSkuAutoId(Long.parseLong(priceImport.getSkuAutoId()));
        sku.setSalePrice(BigDecimal.valueOf(Double.parseDouble(priceImport.getSalePrice())));
        sku.setSkuId(priceImport.getSkuId());
        product.setSkuUpdateKey(SkuUpdateKeyEnum.SKU_AUTO_ID);
        product.setSkuList(Arrays.asList(sku));
        return product;
    }

    /**
     * 构建错误信息
     *
     * @param productList 导入的数据
     */
    private void buildErrMsg(List<PriceImportDto> productList) {
        productList.forEach(product -> {
            String errMsg = StringUtils.defaultString(product.getErrMsg(), StrUtil.EMPTY);
            product.setErrMsg(errMsg + product.getErrBuilder().toString());
        });
    }

}
