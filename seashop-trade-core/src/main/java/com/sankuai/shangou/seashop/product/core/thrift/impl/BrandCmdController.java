package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.product.core.service.BrandService;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.thrift.core.BrandCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.CreateBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.DeleteBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.UpdateBrandReq;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@RestController
@RequestMapping("/brand")
public class BrandCmdController implements BrandCmdFeign {

    @Resource
    private BrandService brandService;

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = CreateBrandReq.class,
            entity = Brand.class,
            actionName = "创建品牌")
    @PostMapping(value = "/createBrand", consumes = "application/json")
    public ResultDto<BaseResp> createBrand(@RequestBody CreateBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createBrand", request, req -> {
            req.checkParameter();

            Brand brand = new Brand();
            brand.setName(req.getName());
            brand.setLogo(req.getLogo());
            brand.setDescription(StringUtils.defaultString(req.getDescription()));
            brandService.createBrand(brand);
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MODIFY,
            dto = UpdateBrandReq.class,
            entity = Brand.class,
            actionName = "更新品牌")
    @PostMapping(value = "/updateBrand", consumes = "application/json")
    public ResultDto<BaseResp> updateBrand(@RequestBody UpdateBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateBrand", request, req -> {
            req.checkParameter();

            Brand brand = new Brand();
            brand.setId(req.getId());
            brand.setName(req.getName());
            brand.setLogo(req.getLogo());
            brand.setDescription(StringUtils.defaultString(req.getDescription()));
            brandService.updateBrand(brand);
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MOVE,
            dto = DeleteBrandReq.class,
            entity = Brand.class,
            actionName = "删除品牌")
    @PostMapping(value = "/deleteBrand", consumes = "application/json")
    public ResultDto<BaseResp> deleteBrand(@RequestBody DeleteBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteBrand", request, req -> {
            req.checkParameter();

            brandService.deleteBrand(req.getId());
            return new BaseResp();
        });
    }
}
