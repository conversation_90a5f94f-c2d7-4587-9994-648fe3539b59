package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.BrandApplyService;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyAuditBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplySaveBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.thrift.core.BrandApplyCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.AuditBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.SaveBrandApplyReq;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@Tag(name = "品牌申请")
@RestController
@RequestMapping("/brandApply")
public class BrandApplyCmdController implements BrandApplyCmdFeign {

    @Resource
    private BrandApplyService brandApplyService;

    @PostMapping(value = "/createBrandApply",consumes = "application/json")
    @Override
    public ResultDto<BaseResp> createBrandApply(@RequestBody SaveBrandApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createBrandApply", request, req -> {
            req.checkParameter();

            brandApplyService.saveBrandApply(JsonUtil.copy(req, BrandApplySaveBo.class));
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateBrandApply",consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateBrandApply(@RequestBody SaveBrandApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateBrandApply", request, req -> {
            req.checkForEdit();

            brandApplyService.saveBrandApply(JsonUtil.copy(req, BrandApplySaveBo.class));
            return new BaseResp();
        });
    }

    @Operation(summary = "品牌申请审核")
    @PostMapping(value = "/auditBrandApply",consumes = "application/json")
    @Override
    public ResultDto<BaseResp> auditBrandApply(@RequestBody AuditBrandApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("auditBrandApply", request, req -> {
            req.checkParameter();

            brandApplyService.auditBrandApply(JsonUtil.copy(req, BrandApplyAuditBo.class));
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
        processType = ExaProEnum.MOVE,
        dto = BaseIdReq.class,
        entity = Brand.class,
        actionName = "删除品牌申请")
    @PostMapping(value = "/deleteBrandApply",consumes = "application/json")
    public ResultDto<BaseResp> deleteBrandApply(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteBrandApply", request, req -> {
            req.checkParameter();

            brandApplyService.deleteBrandApply(req.getId());
            return new BaseResp();
        });
    }
}
