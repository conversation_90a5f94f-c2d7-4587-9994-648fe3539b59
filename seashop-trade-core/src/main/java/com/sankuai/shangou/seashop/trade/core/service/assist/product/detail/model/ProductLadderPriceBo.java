package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * 阶梯价信息
 *
 * <AUTHOR>
 * @date 2023/12/22 16:56
 */
@Setter
@Getter
public class ProductLadderPriceBo {

    /**
     * 最小批量
     */
    private Integer minBath;

    /**
     * 最大批量
     */
    private Integer maxBath;

    /**
     * 价格
     */
    private BigDecimal price;

}
