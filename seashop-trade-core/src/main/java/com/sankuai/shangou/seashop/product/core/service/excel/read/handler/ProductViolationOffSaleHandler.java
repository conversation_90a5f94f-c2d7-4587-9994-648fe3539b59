package com.sankuai.shangou.seashop.product.core.service.excel.read.handler;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.excel.read.BizTypeEnum;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductViolationOffSaleDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper.ProductViolationOffSaleImportWrapper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductViolationOffSaleBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品违规下架处理器
 *
 * <AUTHOR>
 * @date 2023/11/22 18:25
 */
@Component
@Slf4j
public class ProductViolationOffSaleHandler extends ImportHandler<ProductViolationOffSaleDto> {

    @Resource
    private ProductService productService;
    @Resource
    private ProductRepository productRepository;

    @Override
    public BizType bizType() {
        return BizTypeEnum.VIOLATION_OFF_SALE_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<ProductViolationOffSaleDto> importResult) {
        log.info("【违规下架导入】 参数校验");

        // 遍历出正确第一步检验通过的数据
        List<ProductViolationOffSaleDto> dataList = importResult.getSuccessDataList();

        checkProductViolation(dataList);
        buildErrMsg(dataList);
    }

    @Override
    public void saveImportData(List<ProductViolationOffSaleDto> successList) {
        productService.batchViolationOffSale(JsonUtil.copyList(successList, ProductViolationOffSaleBo.class));
    }

    @Override
    public DataWrapper<ProductViolationOffSaleDto> wrapData(List<ProductViolationOffSaleDto> errList) {
        return new ProductViolationOffSaleImportWrapper(errList);
    }

    /**
     * 校验商品违规下架
     *
     * @param dataList
     */
    private void checkProductViolation(List<ProductViolationOffSaleDto> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<Long> productIdList = dataList.stream().map(item -> Long.parseLong(item.getProductId())).collect(Collectors.toList());
        Map<Long, Product> productMap = productRepository.getProductMap(productIdList);
        dataList.forEach(item -> {
            StringBuilder errorBuilder = item.getErrBuilder();
            Product product = productMap.get(Long.parseLong(item.getProductId()));
            if (product == null || product.getWhetherDelete()) {
                errorBuilder.append("商品不存在;");
            }
            // 如果不是销售中的商品, 无需违规下架
            /*else if (!ProductStatusEnum.ON_SALE.equals(ProductStatusHelper.getProductStatus(product.getSaleStatus(), product.getAuditStatus()))) {
                errorBuilder.append("非销售中商品, 无需违规下架;");
            }*/
        });
    }

    /**
     * 构建错误信息
     *
     * @param productList 导入的数据
     */
    private void buildErrMsg(List<ProductViolationOffSaleDto> productList) {
        productList.forEach(product -> {
            String errMsg = StringUtils.defaultString(product.getErrMsg(), StrUtil.EMPTY);
            product.setErrMsg(errMsg + product.getErrBuilder().toString());
        });
    }
}
