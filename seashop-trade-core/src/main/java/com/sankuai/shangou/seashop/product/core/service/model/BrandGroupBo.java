package com.sankuai.shangou.seashop.product.core.service.model;

import java.util.List;

import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/15 17:04
 */
@Setter
@Getter
@Builder
public class BrandGroupBo {

    /**
     * 分组名称
     */
    private String groupKey;

    /**
     * 品牌列表
     */
    private List<Brand> brandList;

    /**
     * 是否可选
     */
    private Boolean selectAble;
}
