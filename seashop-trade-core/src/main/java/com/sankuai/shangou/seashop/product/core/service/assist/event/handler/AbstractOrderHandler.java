package com.sankuai.shangou.seashop.product.core.service.assist.event.handler;

import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.product.core.mq.model.OrderMessage;

/**
 * <AUTHOR>
 * @date 2024/10/31 10:13
 */
public abstract class AbstractOrderHandler {

    public abstract void handle(OrderMessage event);

    public abstract OrderMessageEventEnum getEvent();

}
