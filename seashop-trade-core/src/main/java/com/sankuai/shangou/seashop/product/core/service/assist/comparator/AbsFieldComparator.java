package com.sankuai.shangou.seashop.product.core.service.assist.comparator;

import java.util.Comparator;

import com.sankuai.shangou.seashop.product.core.service.assist.ProductField;

import lombok.Getter;
import lombok.Setter;

/**
 * 抽象字段比较器
 *
 * <AUTHOR>
 * @date 2024/02/22 10:03
 */
@Setter
@Getter
public abstract class AbsFieldComparator<T> implements Comparator<T> {

    protected ProductField fieldAnno;

    public abstract boolean compareField(T newObj, T oldObj);

    @Override
    public int compare(T newObj, T oldObj) {
        return compareField(newObj, oldObj) ? 0 : 1;
    }

}
