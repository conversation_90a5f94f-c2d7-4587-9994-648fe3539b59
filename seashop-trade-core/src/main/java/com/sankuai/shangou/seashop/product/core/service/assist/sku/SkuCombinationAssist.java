package com.sankuai.shangou.seashop.product.core.service.assist.sku;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSpecEnum;

/**
 * <AUTHOR>
 * @date 2024/01/12 10:23
 */
@Component
public class SkuCombinationAssist {

    @Resource
    private SkuAssist skuAssist;

    public List<ProductSkuBo> generateCombinations(List<SpecCombinationBo> specs, Long productId) {
        return generateCombinations(specs, productId, 0);
    }

    // 生成SKU组合的递归方法
    private List<ProductSkuBo> generateCombinations(List<SpecCombinationBo> specs, Long productId, int curIndex) {
        if (CollectionUtils.isEmpty(specs)) {
            return Collections.EMPTY_LIST;
        }


        SpecCombinationBo curSpec = specs.get(0);
        List<SpecValueCombinationBo> curSpecValueList = curSpec.getSpecValueList();
        List<SpecCombinationBo> subSpecs = specs.size() > 1 ? specs.subList(1, specs.size()) : Collections.EMPTY_LIST;

        // 获取剩余规格的所有组合
        List<ProductSkuBo> subSkuList = generateCombinations(subSpecs, productId, curIndex + 1);
        if (CollectionUtils.isEmpty(subSkuList)) {
            return curSpecValueList.stream().map(curSpecValue -> {
                ProductSkuBo subSku = new ProductSkuBo();
                initSku(subSku, curSpecValue, curSpec);
                return subSku;
            }).collect(Collectors.toList());
        }

        List<ProductSkuBo> skuList = new ArrayList<>();
        // 对当前规格中的每一个选项，与子规格的所有组合进行笛卡尔积操作
        for (SpecValueCombinationBo curSpecValue : curSpecValueList) {
            for (ProductSkuBo subSku : subSkuList) {
                ProductSkuBo curSku = JsonUtil.copy(subSku, ProductSkuBo.class);
                initSku(curSku, curSpecValue, curSpec);
                skuList.add(curSku);
            }
        }

        // 如果是第一层递归, 组合sukId
        if (curIndex == 0) {
            for (ProductSkuBo sku : skuList) {
                sku.setSkuId(skuAssist.generateSkuId(productId, sku.getSpec1ValueId(), sku.getSpec2ValueId(), sku.getSpec3ValueId()));
            }
        }

        return skuList;
    }

    private void initSku(ProductSkuBo subSku, SpecValueCombinationBo curSpecValue, SpecCombinationBo curSpec) {
        if (ProductSpecEnum.SPEC_1.getCode().equals(curSpec.getSpec())) {
            subSku.setSpec1Value(curSpecValue.getValue());
            subSku.setSpec1ValueId(curSpecValue.getSpecValueId());
        }
        else if (ProductSpecEnum.SPEC_2.getCode().equals(curSpec.getSpec())) {
            subSku.setSpec2Value(curSpecValue.getValue());
            subSku.setSpec2ValueId(curSpecValue.getSpecValueId());
        }
        else if (ProductSpecEnum.SPEC_3.getCode().equals(curSpec.getSpec())) {
            subSku.setSpec3Value(curSpecValue.getValue());
            subSku.setSpec3ValueId(curSpecValue.getSpecValueId());
        }
    }

}
