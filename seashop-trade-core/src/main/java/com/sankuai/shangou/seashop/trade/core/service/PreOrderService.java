package com.sankuai.shangou.seashop.trade.core.service;

import com.sankuai.shangou.seashop.trade.core.service.model.*;

/**
 * <AUTHOR>
 */
public interface PreOrderService {

    /**
     * 获取订单提交token
     *
     * <AUTHOR>
     */
    String getSubmitToken(Long userId);

    /**
     * 提交订单
     * <p>点击提交按钮会先进行校验，校验的方向为：商品价格和数量规则是否发生变化；店铺订单总价是否发生变化。
     * 如果校验通过则生成订单，否则返回数据以及异常提示</p>
     *
     * @param orderBo 需要生成订单的数据
     * <AUTHOR>
     */
    PreviewOrderBo submitOrder(SubmitOrderBo orderBo);

    /**
     * 提交ERP订单
     *
     * @param orderBo 订单数据
     * @return 生成订单数据
     */
    PreviewOrderBo submitErpOrder(SubmitOrderBo orderBo);

    /**
     * 选择优惠券
     *
     * @param
     * <AUTHOR>
     */
    ChooseCouponRespBo chooseCoupon(ChooseCouponParamBo paramBo);

    /**
     * 选择收货地址
     *
     * @param
     * <AUTHOR>
     */
    PreviewOrderBo chooseShippingAddress(SubmitOrderBo orderBo);

    /**
     * 预下单
     *
     * @param orderBo 订单对象
     * @return 预下单订单
     */
    PreviewOrderBo previewOrder(SubmitOrderBo orderBo);

    /**
     * 修改预览订单中的商品数量
     *
     * @param paramBo 入参对象，包括整个店铺的数据
     * @return 店铺相关的数据
     */
    ShopProductListBo changePreviewSkuQuantity(PreviewChangeSkuQuantityParamBo paramBo);

}
