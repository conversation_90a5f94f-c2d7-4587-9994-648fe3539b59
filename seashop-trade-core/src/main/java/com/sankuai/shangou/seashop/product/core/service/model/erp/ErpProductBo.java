package com.sankuai.shangou.seashop.product.core.service.model.erp;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/07 9:10
 */
@Getter
@Setter
@Builder
public class ErpProductBo {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品编码(货号)
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * erp状态
     */
    private ProductStatusEnum productStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商品图片
     */
    private List<String> imageList;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 最小销售价(所有sku中的最低价格)
     */
    private BigDecimal minSalePrice;

    /**
     * 库存
     */
    private Long stock;

    /**
     * pc端详情地址(暂无)
     */
    private String webUrl;

    /**
     * h5端详情地址(暂无)
     */
    private String h5Url;

    /**
     * 商品单位
     */
    private String measureUnit;

    /**
     * 销量
     */
    private Long saleCounts;

    /**
     * 限购数
     */
    private Integer maxBuyCount;

    /**
     * 销售状态
     */
    private ProductEnum.SaleStatusEnum saleStatus;

    /**
     * 审核状态
     */
    private ProductEnum.AuditStatusEnum auditStatus;

    /**
     * 是否开启阶梯价
     */
    private Boolean whetherOpenLadder;

    /**
     * 阶梯价
     */
    private List<ErpLadderPriceBo> ladderPriceList;

    /**
     * 倍数起购量
     */
    private Integer multipleCount;

    /**
     * 商品规格
     */
    private List<ErpSkuBo> skuList;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 来源 1-商城 2-牵牛花 3-易久批
     */
    private Integer source;

}
