package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 16:58
 */
@Getter
@Setter
public class UpdateSafeStockItemLogBo {

    @PrimaryField
    @ExaminField(description = "规格id")
    private Long skuAutoId;

    @ExaminField(description = "安全库存")
    private Long safeStock;

}
