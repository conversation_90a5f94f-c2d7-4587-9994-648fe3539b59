package com.sankuai.shangou.seashop.promotion.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@Configuration
public class ServiceConfig {

    /**
     * TODO 待修改
     *
     * @return
     */
    @Bean
    public ExecutorService promotionActiveQueryPool() {
        return Executors.newFixedThreadPool(4, r -> new Thread(r, "promotionActiveQueryPool"));
    }

}
