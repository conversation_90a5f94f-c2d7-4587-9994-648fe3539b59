package com.sankuai.shangou.seashop.product.core.thrift.impl;


import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.BrandApplyService;
import com.sankuai.shangou.seashop.product.core.service.BrandService;
import com.sankuai.shangou.seashop.product.core.service.model.BatchQueryBranchParamBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandGroupBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.thrift.core.BrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.BatchQueryBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryNotApplyBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryRecommendBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.BrandListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.NotApplyBrandGroupResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.NotApplyBrandResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandGroupDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 */
@Slf4j
@RestController
@RequestMapping("/brand")
public class BrandQueryController implements BrandQueryFeign {

    @Resource
    private BrandService brandService;

    @Resource
    private BrandApplyService brandApplyService;

    @PostMapping(value = "/queryBrandForPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<BrandDto>> queryBrandForPage(@RequestBody QueryBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandForPage", request, req -> {
            BasePageResp<Brand> pageResp = brandService.pageBrand(req.buildPage(), JsonUtil.copy(req, BrandQueryBo.class));
            Map<Long, BrandApplyBo> map = new HashMap();
            if (pageResp.getTotalCount() > 0) {
                List<Long> brandIds = pageResp.getData().stream().map(Brand::getId).collect(Collectors.toList());
                List<BrandApplyBo> list = brandApplyService.queryBrandApplyList(brandIds);
                if (!CollectionUtils.isEmpty(list)) {
                    map = list.stream().collect(Collectors.toMap(BrandApplyBo::getBrandId, Function.identity(), (k1, k2) -> k2));
                }
            }
            Map<Long, BrandApplyBo> finalMap = map;
            return PageResultHelper.transfer(pageResp, brand -> {
                BrandDto brandDto = JsonUtil.copy(brand, BrandDto.class);
                BrandApplyBo brandApplyBo = finalMap.get(brand.getId());
                brandDto.setApplyMode(Objects.isNull(brandApplyBo) ? BrandEnum.ApplyModeEnum.EXISTING_BRAND.getDesc() : brandApplyBo.getApplyMode().getDesc());
                return brandDto;
            });
        });
    }

    @PostMapping(value = "/queryBrandDetail", consumes = "application/json")
    @Override
    public ResultDto<BrandDto> queryBrandDetail(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBrandDetail", request, req -> {

            Brand brand = brandService.queryBrandDetail(req.getId());
            return JsonUtil.copy(brand, BrandDto.class);
        });
    }

    @PostMapping(value = "/queryBrandList", consumes = "application/json")
    @Override
    public ResultDto<BrandListResp> queryBrandList(@RequestBody BatchQueryBrandReq request) throws TException {
        log.info("【品牌查询】批量查询品牌列表, 请求参数={}", request);
        return ThriftResponseHelper.responseInvoke("queryBrandList", request, req -> {
            // 参数基础校验
            req.checkParameter();

            // 参数对象转换
            BatchQueryBranchParamBo submitOrderBo = JsonUtil.copy(req, BatchQueryBranchParamBo.class);
            List<BrandDto> brandList = brandService.queryBrandList(submitOrderBo);
            return BrandListResp.builder().brandList(brandList).build();
        });
    }

    @PostMapping(value = "/queryNotApplyBrand", consumes = "application/json")
    @Override
    public ResultDto<NotApplyBrandResp> queryNotApplyBrand(@RequestBody QueryNotApplyBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryNotApplyBrand", request, req -> {

            List<Brand> brands = brandService.queryNotApplyBrand(req.getShopId());
            return NotApplyBrandResp.builder().brandList(JsonUtil.copyList(brands, BrandDto.class)).build();
        });
    }

    @PostMapping(value = "/queryNotApplyBrandGroup", consumes = "application/json")
    @Override
    public ResultDto<NotApplyBrandGroupResp> queryNotApplyBrandGroup(@RequestBody QueryNotApplyBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryNotApplyBrandGroup", request, req -> {

            List<BrandGroupBo> brandGroupBos = brandService.queryNotApplyBrandGroup(req.getShopId());
            return NotApplyBrandGroupResp.builder().groupList(JsonUtil.copyList(brandGroupBos, BrandGroupDto.class)).build();
        });
    }

    @PostMapping(value = "/queryRecommendBrand", consumes = "application/json")
    @Override
    public ResultDto<BrandListResp> queryRecommendBrand(@RequestBody QueryRecommendBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryRecommendBrand", request, req -> {

            List<Brand> brands = brandService.queryRecommendBrand(req.getCategoryId());
            return BrandListResp.builder().brandList(JsonUtil.copyList(brands, BrandDto.class)).build();
        });
    }

    @Override
    public ResultDto<List<Long>> queryBrandIdsByName(String brandName) {
        return ThriftResponseHelper.responseInvoke("queryBrandIdsByName", brandName, req -> this.brandService.queryBrandIdsByName(brandName));
    }
}
