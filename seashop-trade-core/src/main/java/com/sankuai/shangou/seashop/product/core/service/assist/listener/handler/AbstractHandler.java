package com.sankuai.shangou.seashop.product.core.service.assist.listener.handler;

import org.springframework.transaction.event.TransactionPhase;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.AbstractTransactionEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/25 13:59
 */
@Slf4j
public abstract class AbstractHandler<T extends AbstractTransactionEvent> {

    /**
     * 是否支持该处理器
     *
     * @param body
     * @param handlerPhase
     */
    public boolean support(T body, TransactionPhase handlerPhase) {
        if (!body.getTransactionPhase().equals(handlerPhase)) {
            log.warn("事件[{}]的处理器[{}]的事务阶段[{}]与事件的事务阶段[{}]不一致,不执行处理逻辑",
                    body.getClass().getSimpleName(), this.getClass().getSimpleName(),
                    handlerPhase, body.getTransactionPhase());
            return false;
        }

        return true;
    }

    /**
     * 具体 的执行逻辑
     *
     * @param body
     */
    public abstract void handle(T body);

}
