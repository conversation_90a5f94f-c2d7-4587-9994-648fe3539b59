package com.sankuai.shangou.seashop.product.core.service.converter;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPrice;

/**
 * <AUTHOR>
 * @date 2023/11/16 16:58
 */
public class LadderPriceConverter {

    public static ProductLadderPriceBo convertToBo(ProductLadderPrice ladderPrice) {
        return Optional.ofNullable(ladderPrice).map(entity -> {
            ProductLadderPriceBo bo = JsonUtil.copy(entity, ProductLadderPriceBo.class);
            bo.setLadderPriceId(entity.getId());
            return bo;
        }).orElse(null);
    }

    public static List<ProductLadderPriceBo> convertToBoList(List<ProductLadderPrice> ladderPriceList) {
        return Optional.ofNullable(ladderPriceList).map(list -> list.stream().map(LadderPriceConverter::convertToBo).collect(Collectors.toList())).orElse(null);
    }
}
