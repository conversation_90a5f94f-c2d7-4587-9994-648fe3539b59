package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 16:56
 */
@Getter
@Setter
public class UpdateSafeStockLogBo {

    @ExaminField(description = "规格集合", isChildField = true, entityClassName = "com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateSafeStockItemLogBo")
    private List<UpdateSafeStockItemLogBo> skuList;

    public static UpdateSafeStockLogBo build(List<SkuStock> skuStockList) {
        UpdateSafeStockLogBo bo = new UpdateSafeStockLogBo();
        bo.setSkuList(JsonUtil.copyList(skuStockList, UpdateSafeStockItemLogBo.class));
        return bo;
    }

    public static UpdateSafeStockLogBo build(List<SkuStock> skuStockList, Long safeStock) {
        UpdateSafeStockLogBo bo = new UpdateSafeStockLogBo();
        if (CollectionUtils.isEmpty(skuStockList)) {
            return bo;
        }

        List<UpdateSafeStockItemLogBo> skuList = JsonUtil.copyList(skuStockList, UpdateSafeStockItemLogBo.class);
        skuList.forEach(sku -> sku.setSafeStock(safeStock));
        bo.setSkuList(skuList);
        return bo;
    }

}
