package com.sankuai.shangou.seashop.promotion.core.service.assist.sort.enums;

import com.sankuai.shangou.seashop.promotion.core.service.assist.sort.SortFieldConverter;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/03/09 11:32
 */
@Getter
public enum SaleFlashSortEnum implements SortFieldConverter<SaleFlashSortEnum> {

    BEGIN_DATE("startTime", "begin_date"),

    END_DATE("endTime", "end_date"),

    LIMIT_COUNT("limitCount", "limit_count"),

    SALE_COUNT("saleCount", "sale_count"),

    ;

    private String source;

    private String target;

    SaleFlashSortEnum(String source, String target) {
        this.source = source;
        this.target = target;
    }


    @Override
    public Map<String, String> getMapping() {
        Map<String, String> mapping = new HashMap<>();
        SaleFlashSortEnum[] values = SaleFlashSortEnum.values();
        for (SaleFlashSortEnum value : values) {
            mapping.put(value.source, value.target);
        }
        return mapping;
    }
}
