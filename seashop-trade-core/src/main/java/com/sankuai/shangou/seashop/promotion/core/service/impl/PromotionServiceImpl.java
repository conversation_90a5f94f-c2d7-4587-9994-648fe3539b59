package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.promotion.common.enums.ActiveStatusEnum;
import com.sankuai.shangou.seashop.promotion.core.model.bo.QueryShopUserPromotionBo;
import com.sankuai.shangou.seashop.promotion.core.service.*;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.*;
import com.sankuai.shangou.seashop.promotion.dao.core.model.*;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.FlashSaleStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndMemberIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryProductPromotionReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.MallCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.OrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductPromotionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopReductionOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopUserPromotionResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/16/016
 * @description:
 */
@Service
@Slf4j
public class PromotionServiceImpl implements PromotionService {

//    @ThreadPool(poolName = "promotion_active_query_pool")
    @Resource
    private ExecutorService executorService;

    @Resource
    private DiscountActiveRepository discountActiveRepository;
    @Resource
    private DiscountActiveProductRepository discountActiveProductRepository;
    @Resource
    private DiscountActiveRuleRepository discountActiveRuleRepository;
    @Resource
    private FullReductionRepository fullReductionRepository;
    @Resource
    private ExclusivePriceRepository exclusivePriceRepository;
    @Resource
    private ExclusivePriceProductRepository exclusivePriceProductRepository;
    @Resource
    private FlashSaleRepository flashSaleRepository;
    @Resource
    private CouponRepository couponRepository;
    @Resource
    private CollocationRepository collocationRepository;
    @Resource
    private CollocationService collocationService;
    @Resource
    private CouponService couponService;
    @Resource
    private DiscountActiveService discountActiveService;
    @Resource
    private FlashSaleService flashSaleService;
    @Resource
    private ExclusivePriceService exclusivePriceService;
    @Resource
    private FullReductionService fullReductionService;
    @Resource
    private ProductRemoteService productRemoteService;

    @Override
    public ShopUserPromotionResp queryPromotionData(QueryShopUserPromotionBo queryShopUserPromotionBo) {

        Date now = new Date();
        List<Long> shopIdList = queryShopUserPromotionBo.getShopIdList();

        List<ShopUserPromotionDto> shopUserPromotionList = new ArrayList<>();
        for (Long shopId : shopIdList) {
            ShopUserPromotionDto shopUserPromotionDto = new ShopUserPromotionDto();
            shopUserPromotionDto.setShopId(shopId);
            shopUserPromotionList.add(shopUserPromotionDto);
        }

        // 折扣活动处理
        CompletableFuture future1 = CompletableFuture.runAsync(
                () -> discountActiveOp(now, shopIdList, shopUserPromotionList), executorService);
        // 满减活动处理
        CompletableFuture future2 = CompletableFuture.runAsync(
                () -> fullReductionOp(now, shopIdList, shopUserPromotionList), executorService);
        // 专享价活动处理
        CompletableFuture future3 = CompletableFuture.runAsync(
                () -> exclusivePriceOp(queryShopUserPromotionBo, now, shopUserPromotionList), executorService);

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(future1, future2, future3);

        // 等待所有查询完成
        combinedFuture.join();

        ShopUserPromotionResp shopUserPromotionResp = new ShopUserPromotionResp();
        shopUserPromotionResp.setUserId(queryShopUserPromotionBo.getUserId());
        shopUserPromotionResp.setShopUserPromotionList(shopUserPromotionList);

        return shopUserPromotionResp;
    }

    @Override
    public ShopReductionOrderListResp queryShopPromotionByOrder(PromotionRecordOrderQueryReq request) {
        ShopReductionOrderListResp resp = new ShopReductionOrderListResp();
        Date now = DateUtil.date();
        // userId + shopIds
        List<Long> shopIdList = request.getOrderList().stream().map(OrderQueryReq::getShopId).collect(Collectors.toList());
        List<FullReduction> fullReductions = fullReductionRepository.listByTimeAndShopIdList(
                FullReductionParamDto.builder()
                        .shopIdList(shopIdList)
                        .betweenTime(now).build());
        Map<Long, BigDecimal> orderAmountMap = request.getOrderList().stream().collect(Collectors.groupingBy(OrderQueryReq::getShopId,
                Collectors.reducing(BigDecimal.ZERO, order -> order.getProductList().stream().map(ProductQueryReq::getProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add), BigDecimal::add)));
        // 过滤不满足条件的满减活动
        List<ShopOrderReductionDto> reductionDtos = Optional.ofNullable(fullReductions)
                .orElse(Collections.emptyList()).stream()
                .filter(fullReduction -> {
                    //  满减活动是否满足条件
                    BigDecimal orderAmount = orderAmountMap.get(fullReduction.getShopId());
                    if (ActiveStatusEnum.START.getCode().equals(fullReduction.getActiveStatus()) || orderAmount == null) {
                        return false;
                    }
                    return fullReduction.getMoneyOffCondition().compareTo(orderAmount) <= 0;
                })
                .map(fullReduction -> {
                    ShopOrderReductionDto shopReductionDto = new ShopOrderReductionDto();
                    shopReductionDto.setShopId(fullReduction.getShopId());
                    shopReductionDto.setActiveId(fullReduction.getId());
                    shopReductionDto.setActiveName(fullReduction.getActiveName());
                    shopReductionDto.setMoneyOffCondition(fullReduction.getMoneyOffCondition());
                    shopReductionDto.setMoneyOffFee(fullReduction.getMoneyOffFee());
                    BigDecimal orderAmount = orderAmountMap.get(fullReduction.getShopId());
                    int overLayNum = calcMoneyOffOverLayNum(fullReduction, orderAmount);
                    shopReductionDto.setMoneyOffOverLayNum(overLayNum);
                    shopReductionDto.setMoneyOffTotalFee(fullReduction.getMoneyOffFee().multiply(BigDecimal.valueOf(overLayNum).setScale(2, BigDecimal.ROUND_HALF_UP)));
                    return shopReductionDto;
                }).collect(Collectors.toList());
        resp.setList(reductionDtos);
        return resp;
    }

    private int calcMoneyOffOverLayNum(FullReduction fullReduction, BigDecimal orderAmount) {
        boolean overlay = fullReduction.getMoneyOffOverLay() != null && fullReduction.getMoneyOffOverLay();
        if (!overlay) {
            return 1;
        }
        int overLayNum = 0;
        BigDecimal resultAmount = orderAmount;
        while (resultAmount.compareTo(fullReduction.getMoneyOffCondition()) >= 0) {
            resultAmount = resultAmount.subtract(fullReduction.getMoneyOffFee());
            overLayNum++;
        }
        return overLayNum;
    }


    @Override
    public void offSaleAllPromotion(BaseIdReq request) {
        /*
         查询所有的活动（正在进行和未开始的全部结束掉）
         */

        // 满减活动
        List<FullReduction> fullReductionList = fullReductionRepository.listEffectiveByShopId(request.getId());
        // 限时购活动
        List<FlashSale> flashSaleList = flashSaleRepository.listEffectiveByShopId(request.getId());
        // 专享价活动
        List<ExclusivePrice> exclusivePriceList = exclusivePriceRepository.listEffectiveByShopId(request.getId());
        // 折扣活动
        List<DiscountActive> discountActiveList = discountActiveRepository.listEffectiveByShopId(request.getId());
        // 优惠券活动
        List<Coupon> couponList = couponRepository.listEffectiveByShopId(request.getId());
        // 组合购活动
        CollocationDto collocationModel = new CollocationDto();
        collocationModel.setShopId(request.getId());
        List<Collocation> collocationList = collocationRepository.queryCollocationList(collocationModel);
        final Date finalNow = new Date();
        if (CollUtil.isNotEmpty(fullReductionList)) {
            fullReductionList.parallelStream().forEach(
                    fullReduction -> {
                        if (finalNow.before(fullReduction.getEndTime())) {
                            fullReduction.setEndTime(finalNow);
                            fullReduction.setActiveStatus(ActiveStatusEnum.END.getCode());
                        }
                    }
            );
        }
        if (CollUtil.isNotEmpty(flashSaleList)) {
            flashSaleList.parallelStream().forEach(
                    flashSale -> {
                        if (finalNow.before(flashSale.getEndDate())) {
                            flashSale.setEndDate(finalNow);
                            flashSale.setStatus(FlashSaleStatusEnum.ENDED.getStatus());
                        }
                    }
            );
        }
        if (CollUtil.isNotEmpty(exclusivePriceList)) {
            exclusivePriceList.parallelStream().forEach(
                    exclusivePrice -> {
                        if (finalNow.before(exclusivePrice.getEndTime())) {
                            exclusivePrice.setEndTime(finalNow);
                            exclusivePrice.setStatus(ActiveStatusEnum.END.getCode());
                        }
                    }
            );
        }
        if (CollUtil.isNotEmpty(discountActiveList)) {
            discountActiveList.parallelStream().forEach(
                    discountActive -> {
                        if (finalNow.before(discountActive.getEndTime())) {
                            discountActive.setEndTime(finalNow);
                            discountActive.setActiveStatus(ActiveStatusEnum.END.getCode());
                        }
                    }
            );
        }
        if (CollUtil.isNotEmpty(couponList)) {
            couponList.parallelStream().forEach(
                    coupon -> {
                        if (finalNow.before(coupon.getEndTime())) {
                            coupon.setEndTime(finalNow);
                            coupon.setStatus(ActiveStatusEnum.END.getCode());
                        }
                    }
            );
        }
        if (CollUtil.isNotEmpty(collocationList)) {
            collocationList.parallelStream().forEach(
                    collocation -> {
                        if (finalNow.before(collocation.getEndTime())) {
                            collocation.setEndTime(finalNow);
                            collocation.setStatus(ActiveStatusEnum.END.getCode());
                        }
                    }
            );
        }
        TransactionHelper.doInTransaction(() -> {
            if (CollUtil.isNotEmpty(fullReductionList)) {
                fullReductionRepository.updateBatchById(fullReductionList);
            }
            if (CollUtil.isNotEmpty(flashSaleList)) {
                flashSaleRepository.updateBatchById(flashSaleList);
            }
            if (CollUtil.isNotEmpty(exclusivePriceList)) {
                exclusivePriceRepository.updateBatchById(exclusivePriceList);
            }
            if (CollUtil.isNotEmpty(discountActiveList)) {
                discountActiveRepository.updateBatchById(discountActiveList);
            }
            if (CollUtil.isNotEmpty(couponList)) {
                couponRepository.updateBatchById(couponList);
            }
            if (CollUtil.isNotEmpty(collocationList)) {
                collocationRepository.updateBatchById(collocationList);
            }
        });
    }

    @Override
    public ProductPromotionResp queryProductPromotion(QueryProductPromotionReq req) {
        ProductPromotionResp promotionResp = new ProductPromotionResp();
        // 如果没传shopId 自己去获取
        if (req.getShopId() == null) {
            ProductBasicDto product = productRemoteService.getByProductId(req.getProductId());
            AssertUtil.throwIfNull(product, "商品不存在");
            req.setShopId(product.getShopId());
        }

        Long startTime = System.currentTimeMillis();

        // 查询组合购
        CompletableFuture collocationFuture = CompletableFuture.runAsync(
                () -> promotionResp.setMallCollocationResp(collocationService.queryMallCollocationList(JsonUtil.copy(req, MallCollocationReq.class))), executorService);

        // 查询优惠券
        CompletableFuture couponFuture = CompletableFuture.runAsync(
                () -> promotionResp.setCouponSimpleListResp(couponService.queryByProductId(JsonUtil.copy(req, ProductAndShopIdReq.class))), executorService);

        // 折扣活动
        CompletableFuture discountFuture = CompletableFuture.runAsync(
                () -> promotionResp.setDiscountActiveResp(discountActiveService.queryByProductId(JsonUtil.copy(req, ProductAndShopIdReq.class))), executorService);

        // 查询专享价
        CompletableFuture exclusiveFuture = CompletableFuture.runAsync(
                () -> promotionResp.setExclusivePriceResp(exclusivePriceService.queryByProductAndMemberId(JsonUtil.copy(req, ProductAndMemberIdReq.class))), executorService);

        // 查询限时购
        CompletableFuture flashSaleFutureFuture = CompletableFuture.runAsync(
                () -> promotionResp.setFlashSaleResp(flashSaleService.queryByProductId(JsonUtil.copy(req, ProductAndShopIdReq.class))), executorService);

        // 查询满减
        CompletableFuture fullReduceFuture = CompletableFuture.runAsync(
                () -> promotionResp.setFullReductionResp(fullReductionService.queryByShopId(JsonUtil.copy(req, ShopIdReq.class))), executorService);

        // 等待所有查询完成
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(collocationFuture, couponFuture, discountFuture, exclusiveFuture, flashSaleFutureFuture, fullReduceFuture);
        combinedFuture.join();

        log.info("查询商品促销耗时：{}ms", System.currentTimeMillis() - startTime);
        return promotionResp;
    }

    /**
     * 专享价活动查询和数据处理
     *
     * @param queryShopUserPromotionBo
     * @param now
     * @param shopUserPromotionList
     */
    private void exclusivePriceOp(QueryShopUserPromotionBo queryShopUserPromotionBo, Date now, List<ShopUserPromotionDto> shopUserPromotionList) {
        if (queryShopUserPromotionBo.getUserId() == null) {
            return;
        }

        List<ExclusivePrice> exclusivePrices = exclusivePriceRepository.listByParams(
                ExclusivePriceParamDto.builder()
                        .memberId(queryShopUserPromotionBo.getUserId())
                        .shopIdList(queryShopUserPromotionBo.getShopIdList())
                        .betweenTime(now).build());
        if (CollectionUtils.isNotEmpty(exclusivePrices)) {
            for (ExclusivePrice exclusivePrice : exclusivePrices) {

                ShopExclusivePriceDto shopExclusivePriceDto = new ShopExclusivePriceDto();
                shopExclusivePriceDto.setId(exclusivePrice.getId());
                shopExclusivePriceDto.setName(exclusivePrice.getName());

                List<ExclusivePriceProduct> exclusivePriceProducts = exclusivePriceProductRepository.listByActiveOrMemberId(
                        ExclusivePriceProductParamDto.builder()
                                .activeId(exclusivePrice.getId())
                                .memberId(queryShopUserPromotionBo.getUserId()).build()
                );
                if (CollectionUtils.isNotEmpty(exclusivePriceProducts)) {
                    shopExclusivePriceDto.setProductList(JsonUtil.copyList(exclusivePriceProducts, ShopExclusivePriceProductDto.class));
                }

                ShopUserPromotionDto shopUserPromotionDto = shopUserPromotionList.stream()
                        .filter(s -> s.getShopId().equals(exclusivePrice.getShopId())).collect(Collectors.toList()).get(0);

                List<ShopExclusivePriceDto> shopExclusivePriceList = shopUserPromotionDto.getShopExclusivePriceList();
                if (null == shopExclusivePriceList) {
                    shopExclusivePriceList = new ArrayList<>();
                }
                shopExclusivePriceList.add(shopExclusivePriceDto);

                shopUserPromotionDto.setShopExclusivePriceList(shopExclusivePriceList);
            }
        }
    }

    /**
     * 满减活动查询和数据处理
     *
     * @param now
     * @param shopIdList
     * @param shopUserPromotionList
     */
    private void fullReductionOp(Date now, List<Long> shopIdList, List<ShopUserPromotionDto> shopUserPromotionList) {
        // 查询当前时间段内的活动
        List<FullReduction> fullReductions = fullReductionRepository.listByTimeAndShopIdList(
                FullReductionParamDto.builder()
                        .shopIdList(shopIdList)
                        .betweenTime(now).build());
        if (CollectionUtils.isNotEmpty(fullReductions)) {
            for (FullReduction fullReduction : fullReductions) {
                ShopReductionDto shopReductionDto = new ShopReductionDto();
                shopReductionDto.setActiveId(fullReduction.getId());
                shopReductionDto.setActiveName(fullReduction.getActiveName());
                shopReductionDto.setMoneyOffCondition(fullReduction.getMoneyOffCondition());
                shopReductionDto.setMoneyOffFee(fullReduction.getMoneyOffFee());
                shopReductionDto.setMoneyOffOverLay(fullReduction.getMoneyOffOverLay());
                shopReductionDto.setStartTime(fullReduction.getStartTime());
                shopReductionDto.setEndTime(fullReduction.getEndTime());
                ShopUserPromotionDto shopUserPromotionDto = shopUserPromotionList.stream()
                        .filter(s -> s.getShopId().equals(fullReduction.getShopId())).collect(Collectors.toList()).get(0);
                shopUserPromotionDto.setShopReduction(shopReductionDto);
            }
        }
    }

    /**
     * 折扣活动查询和数据处理
     *
     * @param now
     * @param shopIdList
     * @param shopUserPromotionList
     */
    private void discountActiveOp(Date now, List<Long> shopIdList, List<ShopUserPromotionDto> shopUserPromotionList) {
        // 查询当前时间段内的活动
        List<DiscountActive> discountActives = discountActiveRepository.listByExample(
                DiscountActiveParamDto.builder()
                        .shopIdList(shopIdList)
                        .betweenTime(now).build());
        if (CollectionUtils.isNotEmpty(discountActives)) {
            for (DiscountActive discountActive : discountActives) {

                ShopDiscountDto shopDiscountDto = new ShopDiscountDto();
                shopDiscountDto.setDiscountActId(discountActive.getId());
                shopDiscountDto.setDiscountActName(discountActive.getActiveName());
                shopDiscountDto.setIzAllProduct(discountActive.getIzAllProduct());
                shopDiscountDto.setStartTime(discountActive.getStartTime());
                shopDiscountDto.setEndTime(discountActive.getEndTime());
                if (!discountActive.getIzAllProduct()) {
                    // 非全部商品，需要查询商品信息
                    List<DiscountActiveProduct> discountActiveProducts = discountActiveProductRepository.listByActiveId(discountActive.getId());
                    if (CollectionUtils.isNotEmpty(discountActiveProducts)) {
                        shopDiscountDto.setProductIdList(discountActiveProducts.stream().map(DiscountActiveProduct::getProductId).collect(Collectors.toList()));
                    }
                }
                // 折扣规则查询
                List<DiscountActiveRule> discountActiveRules = discountActiveRuleRepository.listByActiveId(discountActive.getId());
                if (CollectionUtils.isNotEmpty(discountActiveRules)) {
                    shopDiscountDto.setRuleList(JsonUtil.copyList(discountActiveRules, DiscountActiveRuleReq.class));
                }

                ShopUserPromotionDto shopUserPromotionDto = shopUserPromotionList.stream()
                        .filter(s -> s.getShopId().equals(discountActive.getShopId())).collect(Collectors.toList()).get(0);
                List<ShopDiscountDto> shopDiscountList = shopUserPromotionDto.getShopDiscountList();
                if (null == shopDiscountList) {
                    shopDiscountList = new ArrayList<>();
                }
                shopDiscountList.add(shopDiscountDto);

                shopUserPromotionDto.setShopDiscountList(shopDiscountList);
            }
        }
    }
}
