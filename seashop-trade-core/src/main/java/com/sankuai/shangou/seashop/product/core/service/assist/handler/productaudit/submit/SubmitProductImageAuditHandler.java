package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.submit;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.converter.SaveProductConverter;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImage;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImageAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageAuditRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 提交商品图片审核
 *
 * <AUTHOR>
 * @date 2023/11/17 13:40
 */
@Component
@Slf4j
public class SubmitProductImageAuditHandler extends AbsSubmitProductAuditHandler {

    @Resource
    private ProductImageAuditRepository productImageAuditRepository;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        log.info("【商品提交审核】保存商品图片审核记录【start】, productId: {}", productId);

        ProductBo auditProductBo = context.getAuditProductBo();
        List<String> newImgList = auditProductBo.getImageList();

        List<ProductImage> productImageList = SaveProductConverter.convertToProductImage(newImgList, productId);
        if (!CollectionUtils.isEmpty(productImageList)) {
            productImageAuditRepository.saveBatch(JsonUtil.copyList(productImageList, ProductImageAudit.class));
        }

        log.info("【商品提交审核】保存商品图片审核记录【end】, productId: {}", productId);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SUBMIT_PRODUCT_IMAGE_AUDIT;
    }


}
