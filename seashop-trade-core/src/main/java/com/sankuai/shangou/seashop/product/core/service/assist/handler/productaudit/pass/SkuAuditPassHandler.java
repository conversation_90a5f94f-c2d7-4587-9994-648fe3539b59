package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.pass;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.hepler.CompareHelper;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/24 16:13
 */
@Component
@Slf4j
public class SkuAuditPassHandler extends AbsProductAuditPassHandler {

    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuAuditRepository skuAuditRepository;
    @Resource
    private SkuStockAuditRepository skuStockAuditRepository;
    @Resource
    private SkuStockRepository skuStockRepository;

    @Override
    protected void handle(ProductContext context) {
        log.info("【商品审核通过】保存商品sku==开始, context={}", context);

        Long productId = context.getProductId();
        List<Sku> dbSkuList = skuRepository.list(new LambdaQueryWrapper<Sku>().eq(Sku::getProductId, productId));
        List<SkuAudit> skuAuditList = skuAuditRepository.list(new LambdaQueryWrapper<SkuAudit>().eq(SkuAudit::getProductId, productId));
        List<Sku> newSkuList = JsonUtil.copyList(skuAuditList, Sku.class);
        CompareHelper.CompareResult<Sku> compareResult = CompareHelper.compare(newSkuList, dbSkuList, sku -> sku.getSkuId(),
            (newSku, oldSku) -> false, (newSku, oldSku) -> newSku.setId(oldSku.getId()));

        // 更新数据库
        addSkuList(compareResult.getAddList());

        updSkuList(compareResult.getUpdateList());

        delSkuList(compareResult.getDeleteList());

        log.info("【商品审核通过】保存商品sku==结束, context={}", context);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.PASS_SKU_AUDIT;
    }

    private void addSkuList(List<Sku> addList) {
        if (CollectionUtils.isEmpty(addList)) {
            return;
        }

        Long productId = addList.get(0).getProductId();
        // 获取审核的库存信息
        List<SkuStockAudit> skuStockAuditList = skuStockAuditRepository.list(new LambdaQueryWrapper<SkuStockAudit>()
            .eq(SkuStockAudit::getProductId, productId));

        List<String> addSkuIds = addList.stream().map(Sku::getSkuId).collect(Collectors.toList());
        addList.forEach(sku -> sku.setId(null));
        skuRepository.saveBatch(addList);

        Map<String, Sku> addMap = addList.stream().collect(Collectors.toMap(Sku::getSkuId, Function.identity(), (k1, k2) -> k2));
        List<SkuStockAudit> addSkuStockList = skuStockAuditList.stream().filter(item -> addSkuIds.contains(item.getSkuId())).collect(Collectors.toList());
        addSkuStockList.forEach(skuStock -> {
            skuStock.setSkuAutoId(addMap.get(skuStock.getSkuId()).getId());
            skuStock.setId(null);
        });
        skuStockRepository.saveBatch(JsonUtil.copyList(addSkuStockList, SkuStock.class));
    }

    private void updSkuList(List<Sku> updList) {
        if (CollectionUtils.isEmpty(updList)) {
            return;
        }

        skuRepository.updateBatchById(updList);
    }

    private void delSkuList(List<Sku> delList) {
        if (CollectionUtils.isEmpty(delList)) {
            return;
        }

        List<String> skuIds = delList.stream().map(Sku::getSkuId).collect(Collectors.toList());
        skuRepository.removeBySkuIds(skuIds);
        skuStockRepository.removeBySkuIds(skuIds);
    }



}
