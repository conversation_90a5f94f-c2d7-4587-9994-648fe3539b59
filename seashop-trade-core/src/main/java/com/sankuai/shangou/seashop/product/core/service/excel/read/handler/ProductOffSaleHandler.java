package com.sankuai.shangou.seashop.product.core.service.excel.read.handler;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.excel.read.BizTypeEnum;
import com.sankuai.shangou.seashop.product.core.service.excel.read.context.ProductImportAssist;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductOffSaleDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper.ProductOffSaleImportWrapper;
import com.sankuai.shangou.seashop.product.core.service.hepler.ProductStatusHelper;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductOnOffSaleReq;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品下架导入处理器
 *
 * <AUTHOR>
 * @date 2023/11/22 18:25
 */
@Component
@Slf4j
public class ProductOffSaleHandler extends ImportHandler<ProductOffSaleDto> {

    @Resource
    private ProductService productService;
    @Resource
    private ProductRepository productRepository;

    @Override
    public BizType bizType() {
        return BizTypeEnum.OFF_SHELF_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<ProductOffSaleDto> importResult) {
        log.info("【下架导入】 参数校验");

        // 遍历出正确第一步检验通过的数据
        List<ProductOffSaleDto> dataList = importResult.getSuccessDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<Long> productIdList = dataList.stream().map(item -> Long.parseLong(item.getProductId())).collect(Collectors.toList());
        Map<Long, Product> productMap = productRepository.getProductMap(productIdList);
        Long curShopId = ProductImportAssist.getShopIdOrThrow();

        dataList.forEach(item -> {
            StringBuilder errorBuilder = new StringBuilder(StringUtils.defaultString(item.getErrMsg(), StringUtils.EMPTY));
            Product product = productMap.get(Long.parseLong(item.getProductId()));

            if (product == null || product.getWhetherDelete()) {
                errorBuilder.append("商品不存在;");
            }
            else if (!product.getShopId().equals(curShopId)) {
                errorBuilder.append("该商品不属于当前店铺;");
            }
            else if (!ProductStatusEnum.ON_SALE.equals(ProductStatusHelper.getProductStatus(product.getSaleStatus(), product.getAuditStatus()))) {
                errorBuilder.append("非销售中商品, 无需下架;");
            }

            item.setErrMsg(errorBuilder.toString());
        });
    }

    @Override
    public void saveImportData(List<ProductOffSaleDto> successList) {
        Long operationUserId = ProductImportAssist.getOperationUserId();
        Long operationShopId = ProductImportAssist.getOperationShopId();

        List<Long> productIdList = successList.stream().map(item -> Long.parseLong(item.getProductId())).collect(Collectors.toList());
        Long curShopId = ProductImportAssist.getShopIdOrThrow();

        ProductOnOffSaleReq param = new ProductOnOffSaleReq();
        param.setProductIdList(productIdList);
        param.setShopId(curShopId);
        param.setOnSale(false);
        param.setFromImport(true);
        param.setOperationShopId(operationShopId);
        param.setOperationUserId(operationUserId);
        productService.batchOnOffSaleProduct(param);
    }

    @Override
    public DataWrapper<ProductOffSaleDto> wrapData(List<ProductOffSaleDto> errList) {
        return new ProductOffSaleImportWrapper(errList);
    }
}
