package com.sankuai.shangou.seashop.promotion.core.service.impl;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.promotion.common.constant.SquirrelConst;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleConfigService;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleConfig;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.FlashSaleConfigRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.PlatFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.ShopFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.PlatFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopFlashSaleConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Service
@Slf4j
public class FlashSaleConfigServiceImpl implements FlashSaleConfigService {

    @Resource
    private FlashSaleConfigRepository flashSaleConfigRepository;

    @Resource
    private SquirrelUtil squirrelUtil;

    @Override
    public PlatFlashSaleConfigResp getPlatConfig() {
        Object obj = squirrelUtil.get(SquirrelConst.FLASH_SALE_CONFIG_KEY + PromotionConstant.PLAT_ID);
        if (obj != null) {
            return JsonUtil.copy(obj, PlatFlashSaleConfigResp.class);
        }

        FlashSaleConfig flashSaleConfig = flashSaleConfigRepository.selectByShopId(PromotionConstant.PLAT_ID);
        if (null == flashSaleConfig) {
            flashSaleConfig = new FlashSaleConfig();
            flashSaleConfig.setShopId(PromotionConstant.PLAT_ID);
            flashSaleConfig.setNeedAuditFlag(Boolean.FALSE);
            flashSaleConfigRepository.save(flashSaleConfig);
        }
        squirrelUtil.set(SquirrelConst.FLASH_SALE_CONFIG_KEY + PromotionConstant.PLAT_ID, flashSaleConfig);

        return JsonUtil.copy(flashSaleConfig, PlatFlashSaleConfigResp.class);
    }

    @Override
    public ShopFlashSaleConfigResp getShopConfig(ShopIdReq request) {
        Object obj = squirrelUtil.get(SquirrelConst.FLASH_SALE_CONFIG_KEY + request.getShopId());
        if (obj != null) {
            return JsonUtil.copy(obj, ShopFlashSaleConfigResp.class);
        }

        FlashSaleConfig flashSaleConfig = flashSaleConfigRepository.selectByShopId(request.getShopId());
        if (null == flashSaleConfig) {
            flashSaleConfig = new FlashSaleConfig();
            flashSaleConfig.setShopId(request.getShopId());
            // 默认为0天
            flashSaleConfig.setPreheat(PromotionConstant.DEFAULT_LIMIT_TIME_BUY_PREHEAT);
            // 默认不可以购买
            flashSaleConfig.setNormalPurchaseFlag(Boolean.FALSE);
            flashSaleConfigRepository.save(flashSaleConfig);
        }
        squirrelUtil.set(SquirrelConst.FLASH_SALE_CONFIG_KEY + request.getShopId(), flashSaleConfig);

        return JsonUtil.copy(flashSaleConfig, ShopFlashSaleConfigResp.class);
    }

    @Override
    public void updatePlatConfig(PlatFlashSaleConfigReq request) {
        FlashSaleConfig flashSaleConfig = flashSaleConfigRepository.selectByShopId(PromotionConstant.PLAT_ID);
        if (null == flashSaleConfig) {
            flashSaleConfig = new FlashSaleConfig();
            flashSaleConfig.setShopId(PromotionConstant.PLAT_ID);
        }
        flashSaleConfig.setNeedAuditFlag(request.getNeedAuditFlag());

        flashSaleConfigRepository.saveOrUpdate(flashSaleConfig);
        squirrelUtil.deleteKey(SquirrelConst.FLASH_SALE_CONFIG_KEY + PromotionConstant.PLAT_ID);
    }

    @Override
    public void updateShopConfig(ShopFlashSaleConfigReq request) {
        FlashSaleConfig flashSaleConfig = flashSaleConfigRepository.selectByShopId(request.getShopId());
        if (null == flashSaleConfig) {
            flashSaleConfig = new FlashSaleConfig();
        }
        flashSaleConfig.setShopId(request.getShopId());
        flashSaleConfig.setPreheat(request.getPreheat());
        flashSaleConfig.setNormalPurchaseFlag(request.getNormalPurchaseFlag());

        flashSaleConfigRepository.saveOrUpdate(flashSaleConfig);
        squirrelUtil.deleteKey(SquirrelConst.FLASH_SALE_CONFIG_KEY + request.getShopId());
    }
}
