package com.sankuai.shangou.seashop.product.core.mq.listener;

import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.wdt.WdtGoodsApiReq;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.wdt.WdtGoodsDto;
import com.sankuai.shangou.seashop.erp.thrift.channel.ErpWdtProductCmdThriftService;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.SellerUpdateReceiverReq;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.ProductChangeEvent;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopErpQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpResp;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 暂时先消费商品事件构建ES, 后续有了binlog就可以删掉
 *
 * <AUTHOR>
 * @date 2024/07/18 9:25
 */
//@Profile("develop")
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_PRODUCT_CHANGE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.GROUP_PRODUCT_WDT_REQUEST + "_${spring.profiles.active}")
public class ProductWdtBuildListener implements RocketMQListener<MessageExt> {


    @Resource
    private ShopErpQueryFeign shopErpQueryFeign;

    @Resource
    private ProductService productService;

    @Resource
    private ErpWdtProductCmdThriftService erpWdtProductCmdThriftService;
    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【同步旺店通erp】消息内容为: {}", body);
        try {
            ProductChangeEvent productChangeEvent = JsonUtil.parseObject(body, ProductChangeEvent.class);

            QueryShopErpReq req = new QueryShopErpReq();
            req.setShopId(productChangeEvent.getShopId());
            QueryShopErpResp queryShopErpResp = ThriftResponseHelper.executeThriftCall(() -> shopErpQueryFeign.queryShopErp(req));
            if (queryShopErpResp == null) {
                log.info("【mafka消费】【同步旺店通erp】消息内容为: body: {}, 找不到erp配置", body);
                return;
            }
            if (StringUtil.isNullOrEmpty(queryShopErpResp.getWdtAppKey())) {
                log.info("【mafka消费】【同步旺店通erp】消息内容为: body: {}, 店铺没有配置erp", body);
                return;
            }
            ProductBo productBo = productService.queryProductDetail(productChangeEvent.getProductId(), productChangeEvent.getShopId());
            if (productBo == null) {
                log.info("【mafka消费】【同步旺店通erp】消息内容为: body: {}, 找不到商品", body);
                return;
            }

            WdtGoodsApiReq wdtGoodsApiReq = buildWdtGoodsApiReq(productBo, queryShopErpResp);
            //同步平台商品
            ThriftResponseHelper.executeThriftCall(() -> erpWdtProductCmdThriftService.createActiveProduct(wdtGoodsApiReq));

        } catch (Exception e) {
            log.error("【mafka消费】【同步旺店通erp】消息内容为: body: {}", body, e);
            throw new RuntimeException(e);
        }
    }

    private WdtGoodsApiReq buildWdtGoodsApiReq(ProductBo productBo, QueryShopErpResp shopErpResp) {
        WdtGoodsApiReq wdtGoodsApiReq = new WdtGoodsApiReq();
        wdtGoodsApiReq.setPlatformId(127);//固定 127为自研商城
        wdtGoodsApiReq.setShopNo(shopErpResp.getWdtShopNo());
        wdtGoodsApiReq.setAppKey(shopErpResp.getWdtAppKey());
        wdtGoodsApiReq.setAppSecret(shopErpResp.getWdtAppSecret());
        wdtGoodsApiReq.setSid(shopErpResp.getWdtSid());
        wdtGoodsApiReq.setBaseUrl("https://sandbox.wangdian.cn/openapi2");

        List<WdtGoodsDto> goodsList = new ArrayList<>();
        productBo.getSkuList().forEach(t -> {
            WdtGoodsDto wdtGoodsDto = new WdtGoodsDto();
            wdtGoodsDto.setGoodsId(productBo.getProductId().toString());
            wdtGoodsDto.setGoodsNo(productBo.getProductCode());
            wdtGoodsDto.setGoodsName(productBo.getProductName());
            wdtGoodsDto.setPrice(t.getSalePrice().doubleValue());
            wdtGoodsDto.setPicUrl(t.getShowPic());
            wdtGoodsDto.setSpecCode(t.getSkuCode());
            wdtGoodsDto.setSpecNo(t.getSkuCode());
            wdtGoodsDto.setStockNum(t.getStock());
            wdtGoodsDto.setSpecId(t.getSkuId());
            wdtGoodsDto.setSpecName(t.getSpec1Value() + ";" + t.getSpec2Value() + ";" + t.getSpec3Value());
            wdtGoodsDto.setStatus(productBo.getStatus().equals(1) ? 1 : 2);
            goodsList.add(wdtGoodsDto);
        });
        wdtGoodsApiReq.setGoodsList(goodsList);
        return wdtGoodsApiReq;
    }
}
