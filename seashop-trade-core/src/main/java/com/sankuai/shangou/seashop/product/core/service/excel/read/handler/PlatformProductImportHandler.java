package com.sankuai.shangou.seashop.product.core.service.excel.read.handler;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteFreightAreaService;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.assist.BizCodeAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.sku.SkuCombinationAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.sku.SpecCombinationBo;
import com.sankuai.shangou.seashop.product.core.service.excel.read.BizTypeEnum;
import com.sankuai.shangou.seashop.product.core.service.excel.read.context.ProductImportAssist;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.PlatformProductImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper.PlatformProductImportWrapper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.domain.SpecName;
import com.sankuai.shangou.seashop.product.dao.core.domain.SpecValue;
import com.sankuai.shangou.seashop.product.dao.core.model.SpecValueDto;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopBrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopCategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SpecNameRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SpecValueRepository;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.dto.SpecDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.TemplateValuationMethod;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;
import com.sankuai.shangou.seashop.product.thrift.core.helper.ParameterHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品导入处理器
 *
 * <AUTHOR>
 * @date 2023/11/21 22:08
 */
@Component
@Slf4j
@SuppressWarnings("all")
public class PlatformProductImportHandler extends ImportHandler<PlatformProductImportDto> {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private CategoryRepository categoryRepository;
    @Resource
    private BrandRepository brandRepository;
    @Resource
    private BizCodeAssist bizCodeAssist;
    @Resource
    private ShopBrandRepository shopBrandRepository;
    @Resource
    private ShopCategoryRepository shopCategoryRepository;
    @Resource
    private SkuAssist skuAssist;
    @Resource
    private ProductService productService;
    @Resource
    private SkuCombinationAssist skuCombinationAssist;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private ProductAssist productAssist;
    @Resource
    private RemoteFreightAreaService remoteFreightAreaService;
    @Resource
    private SpecNameRepository specNameRepository;
    @Resource
    private SpecValueRepository specValueRepository;


    @Override
    public BizType bizType() {
        return BizTypeEnum.PLATFORM_PRODUCT_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<PlatformProductImportDto> importResult) {
        // 获取数据格式校验通过的数据
        List<PlatformProductImportDto> productList = importResult.getSuccessDataList();

        checkShop(productList);
        checkProductCode(productList);
        checkProduct(productList);
        checkProductImage(productList);
        checkCategory(productList);
        checkBrand(productList);
        checkFreightTemplate(productList);
        checkShopCategory(productList);
        checkStock(productList);
        checkPrice(productList);
        genProductCode(productList);
        checkSku(productList);
        buildErrMsg(productList);
    }

    @Override
    public void saveImportData(List<PlatformProductImportDto> successList) {
        successList.forEach(product -> {
            ProductBo productBo = productBoBuild(product);
            try {
                productService.saveProduct(productBo, ProductSourceEnum.MALL, ProductChangeType.IMPORT_CREATE, false, false);
            }
            catch (Exception e) {
                product.setErrMsg(String.format("导入失败: 【%s】", e.getMessage()));
                log.error("商品更新导入失败: product: {}", product, e);
            }
        });
    }

    @Override
    public DataWrapper<PlatformProductImportDto> wrapData(List<PlatformProductImportDto> errList) {
        return new PlatformProductImportWrapper(errList);
    }

    /**
     * 校验店铺信息
     *
     * @param productList 导入的数据
     */
    private void checkShop(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        // 首先校验一下shopId 的格式
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getShopId())) {
                return;
            }

            if (!NumberUtil.isLong(product.getShopId())) {
                product.getErrBuilder().append("店铺id格式不正确");
                return;
            }

            product.setShopIdLong(Long.parseLong(product.getShopId()));
        });

        // 校验店铺和店铺id是否存在
        List<Long> shopIds = productList.stream()
                .map(PlatformProductImportDto::getShopIdLong).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
        List<String> shopNames = productList.stream()
                .map(PlatformProductImportDto::getShopName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        List<ShopSimpleResp> shopList = remoteShopService.listShopByIds(shopIds);
        Map<Long, ShopSimpleResp> shopIdNameMap = shopList.stream().collect(Collectors.toMap(ShopSimpleResp::getId, Function.identity(), (k1, k2) -> k2));
        shopList = remoteShopService.listShopByNames(shopNames);
        Map<String, ShopSimpleResp> shopNameIdMap = shopList.stream().collect(Collectors.toMap(ShopSimpleResp::getShopName, Function.identity(), (k1, k2) -> k2));

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getShopId()) && StringUtils.isEmpty(product.getShopName())) {
                product.getErrBuilder().append("店铺id和店铺名称不能同时为空;");
                return;
            }

            if (product.getShopIdLong() != null) {
                ShopSimpleResp shop = shopIdNameMap.get(product.getShopIdLong());
                if (shop == null) {
                    product.getErrBuilder().append("店铺不存在;");
                    return;
                }
                product.setShopName(shop.getShopName());
                product.setWhetherSelf(shop.getWhetherSelf());
                return;
            }

            if (StringUtils.isNotEmpty(product.getShopName())) {
                String shopName = product.getShopName();
                ShopSimpleResp shop = shopNameIdMap.get(shopName);
                if (shop == null) {
                    product.getErrBuilder().append("店铺不存在;");
                    return;
                }
                product.setShopId(String.valueOf(shop.getId()));
                product.setShopIdLong(shop.getId());
                product.setWhetherSelf(shop.getWhetherSelf());
            }
        });
    }

    /**
     * 校验货号是否存在
     *
     * @param productList 导入的数据
     */
    private void checkProductCode(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            // 如果货号不为空 则检验货号格式
            if (StringUtils.isNotEmpty(product.getProductCode())) {
                if (product.getShopIdLong() == null) {
                    return;
                }

                Boolean flag = productRepository.existProductCode(product.getProductCode(), product.getShopIdLong());
                if (flag) {
                    product.getErrBuilder().append("商品货号已存在;");
                    return;
                }

                if (product.getProductCode().length() > ParameterConstant.PRODUCT_CODE_LENGTH) {
                    product.getErrBuilder().append("商品货号长度不能超过" + ParameterConstant.PRODUCT_CODE_LENGTH + "个字符;");
                }

                if (!ParameterHelper.checkProductCode(product.getProductCode())) {
                    product.getErrBuilder().append("商品货号格式不正确(只能为数字、字母或者-);");
                }
                return;
            }
        });
    }

    /**
     * 自动生成货号
     *
     * @param productList
     */
    private void genProductCode(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<PlatformProductImportDto> noProductCodeList = productList.stream()
                .filter(item -> StringUtils.isEmpty(item.getProductCode()) && item.getErrBuilder().length() == 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noProductCodeList)) {
            return;
        }

        List<String> productCodes = bizCodeAssist.getProductCodes(noProductCodeList.size());
        for (int i = 0; i < noProductCodeList.size(); i++) {
            PlatformProductImportDto product = noProductCodeList.get(i);
            product.setProductCode(productCodes.get(i));
        }
    }

    /**
     * 校验商品信息
     *
     * @param productList 导入的数据
     */
    private void checkProduct(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getProductName())) {
                product.getErrBuilder().append("商品名称不能为空;");
            }

            if (StringUtils.isNotEmpty(product.getProductName()) && product.getProductName().length() > ParameterConstant.PRODUCT_NAME_LENGTH) {
                product.getErrBuilder().append("商品名称长度不能超过" + ParameterConstant.PRODUCT_NAME_LENGTH + "个字符;");
            }

            // 广告词长度限制200
            if (StringUtils.isNotEmpty(product.getShortDescription()) && product.getShortDescription().length() > ParameterConstant.PRODUCT_SHORT_DESCRIPTION_LIMIT_LENGTH) {
                product.getErrBuilder().append("广告词长度不能超过" + ParameterConstant.PRODUCT_SHORT_DESCRIPTION_LIMIT_LENGTH + "个字符;");
            }

            // 计量单位不能为空
            if (StringUtils.isEmpty(product.getMeasureUnit())) {
                product.getErrBuilder().append("计量单位不能为空;");
            }

            // 计量单位里面不能带数字
            if (StringUtils.isNotEmpty(product.getMeasureUnit()) && !ParameterHelper.checkMeasureUnit(product.getMeasureUnit())) {
                product.getErrBuilder().append("计量单位格式不正确(不能包含数字);");
            }
        });
    }

    private void checkProductImage(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        Map<String, String> remoteImgMapping = ProductImportAssist.getRemoteImgMapping();
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getImagePath())) {
                return;
            }

            List<String> localImageList = Arrays.asList(product.getImagePath().split(StrUtil.COMMA));
            List<String> imageList = new ArrayList<>();
            // 将本地图片转换为
            localImageList.forEach(localImage -> {
                String remoteImage = remoteImgMapping.get(localImage);
                if (StringUtils.isEmpty(remoteImage)) {
                    product.getErrBuilder().append(String.format("[%s]图片不存在;", localImage));
                    return;
                }

                imageList.add(remoteImage);
            });

            if (product.getErrBuilder().length() == 0) {
                product.setImageList(imageList);
                product.setImagePath(CollectionUtils.isNotEmpty(imageList) ? imageList.get(0) : null);
            }
        });
    }

    /**
     * 校验商品分类
     *
     * @param productList 导入的数据
     */
    private void checkCategory(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        checkFirstCategory(productList);
        checkSecondCategory(productList);
        checkThirdCategory(productList);
    }

    /**
     * 校验一级分类
     *
     * @param productList 导入的数据
     */
    private void checkFirstCategory(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> firstCategoryNames = productList.stream()
                .map(PlatformProductImportDto::getFirstCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<Category> categoryList = categoryRepository.listCategoryByPidAndName(CommonConstant.DEFAULT_PARENT_ID, firstCategoryNames);
        Map<String, Category> categoryMap = categoryList.stream().collect(Collectors.toMap(Category::getName, Function.identity(), (k1, k2) -> k2));
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getFirstCategoryName())) {
                product.getErrBuilder().append("一级分类不能为空;");
                return;
            }

            Category category = categoryMap.get(product.getFirstCategoryName());
            if (category == null) {
                product.getErrBuilder().append("一级分类不存在;");
                return;
            }

            product.setFirstCategoryId(category.getId());
        });
    }

    /**
     * 校验二级分类
     *
     * @param productList 导入的数据
     */
    private void checkSecondCategory(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getSecondCategoryName())) {
                product.getErrBuilder().append("二级分类不能为空;");
                return;
            }

            if (product.getFirstCategoryId() == null) {
                return;
            }

            Category category = categoryRepository.getCategoryByPidAndName(product.getFirstCategoryId(), product.getSecondCategoryName());
            if (category == null) {
                product.getErrBuilder().append("二级分类不存在;");
                return;
            }

            product.setSecondCategoryId(category.getId());
        });
    }

    /**
     * 校验三级分类
     *
     * @param productList 导入的数据
     */
    private void checkThirdCategory(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getThirdCategoryName())) {
                product.getErrBuilder().append("三级分类不能为空;");
                return;
            }

            if (product.getSecondCategoryId() == null) {
                return;
            }

            Category category = categoryRepository.getCategoryByPidAndName(product.getSecondCategoryId(), product.getThirdCategoryName());
            if (category == null) {
                product.getErrBuilder().append("三级分类不存在;");
                return;
            }

            product.setThirdCategoryId(category.getId());
            product.setCategoryPath(category.getPath());
        });
    }

    /**
     * 校验品牌
     *
     * @param productList 导入的数据
     */
    private void checkBrand(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> brandNames = productList.stream()
                .map(PlatformProductImportDto::getBrandName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        List<Brand> brandList = brandRepository.getByBrandNames(brandNames);
        Map<String, Brand> brandMap = brandList.stream().collect(Collectors.toMap(Brand::getName, Function.identity(), (k1, k2) -> k2));
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getBrandName())) {
                product.getErrBuilder().append("品牌不能为空;");
                return;
            }

            Brand brand = brandMap.get(product.getBrandName());
            if (brand == null) {
                product.getErrBuilder().append("品牌不存在;");
                return;
            }

            if (product.getShopIdLong() == null) {
                return;
            }

            // 如果不是品牌自营店 校验一下品牌权限
            if (product.getWhetherSelf() == null || !product.getWhetherSelf()) {
                Boolean existFlag = shopBrandRepository.existShopBrand(brand.getId(), product.getShopIdLong());
                if (!existFlag) {
                    product.getErrBuilder().append("该供应商没有该品牌的经营权限;");
                }
            }

            product.setBrandId(brand.getId());
        });
    }

    /**
     * 校验店铺品牌
     *
     * @param productList 导入的数据
     */
    private void checkShopCategory(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getShopCategoryName())) {
                product.getErrBuilder().append("店铺分类不能为空;");
                return;
            }

            if (product.getShopIdLong() == null) {
                return;
            }

            ShopCategory shopCategory = shopCategoryRepository.getShopCategory(product.getShopCategoryName(), product.getShopIdLong());
            if (shopCategory == null) {
                product.getErrBuilder().append("店铺分类不存在;");
                return;
            }

            product.setShopCategoryId(shopCategory.getId());
        });
    }

    /**
     * 校验运费模板
     *
     * @param productList 导入的数据
     */
    private void checkFreightTemplate(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getFreightTemplateName())) {
                product.getErrBuilder().append("运费模板不能为空;");
                return;
            }

            if (product.getShopIdLong() == null) {
                return;
            }

            // 重量和体积只能为数字
            if (StringUtils.isNotEmpty(product.getWeight()) && !NumberUtil.isNumber(product.getWeight())) {
                product.getErrBuilder().append("重量格式不正确(只能为数字);");
                return;
            }

            if (StringUtils.isNotEmpty(product.getVolume()) && !NumberUtil.isNumber(product.getVolume())) {
                product.getErrBuilder().append("体积格式不正确(只能为数字);");
                return;
            }

            QueryFreightTemplateDto freightTemplate = remoteFreightAreaService.queryFreightTemplate(product.getShopIdLong(), product.getFreightTemplateName());
            if (freightTemplate == null) {
                product.getErrBuilder().append("运费模板不存在;");
                return;
            }

            TemplateValuationMethod valuationMethod = TemplateValuationMethod.getByCode(freightTemplate.getValuationMethod());
            switch (valuationMethod) {
                case WEIGHT:
                    // 如果是根据重量计费 则重量必填
                    if (StringUtils.isEmpty(product.getWeight())) {
                        product.getErrBuilder().append("重量不能为空;");
                    }
                    product.setVolume(null);
                    break;
                case VOLUME:
                    // 体积不能为空
                    if (StringUtils.isEmpty(product.getVolume())) {
                        product.getErrBuilder().append("体积不能为空;");
                    }
                    product.setWeight(null);
                    break;
                default:
                    product.setWeight(null);
                    product.setVolume(null);
                    break;
            }

            product.setFreightTemplateId(freightTemplate.getId());
        });
    }

    /**
     * 校验sku
     *
     * @param productList 导入的数据
     */
    private void checkSku(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getSpecs())) {
                product.setHasSku(false);
                product.setSkuList(Arrays.asList(buildSingleSpec(product)));
                return;
            }

            Long shopId = product.getShopIdLong();

            // 拆分后的规格map
            Map<String, List<String>> specMap = skuAssist.splitSpecStr(product.getSpecs());
            if (MapUtils.isEmpty(specMap)) {
                product.getErrBuilder().append("规格不能为空;");
                return;
            }

            List<ProductSkuBo> skuList = new ArrayList<>();
            List<ProductSkuBo> tempSkuList = new ArrayList<>();
            Set<String> specNames = specMap.keySet();
            int level = 0;
            for (String specName : specNames) {
                level++;
                tempSkuList = new ArrayList<>(skuList);
                skuList.clear();

                List<String> specValues = specMap.get(specName);
                if (CollectionUtils.isEmpty(specValues)) {
                    product.getErrBuilder().append("规格值不能为空;");
                    return;
                }

                SpecName specNameEntity = specNameRepository.getByName(shopId, specName);
                if (specNameEntity == null) {
                    product.getErrBuilder().append(String.format("[%s]规格不存在", specName));
                    continue;
                }

                // 查询规格值
                SpecValueDto valueParam = new SpecValueDto();
                valueParam.setShopId(shopId);
                valueParam.setNameId(specNameEntity.getId());
                valueParam.setValues(specValues);
                List<SpecValue> specValueEntityList = specValueRepository.getByCondition(valueParam);
                Map<String, SpecValue> specValueEntityMap = specValueEntityList.stream()
                        .collect(Collectors.toMap(SpecValue::getValue, Function.identity(), (k1, k2) -> k1));

                for (int i = 0; i < specValues.size(); i++) {
                    String specValue = specValues.get(i);
                    SpecValue specValueEntity = specValueEntityMap.get(specValue);
                    if (specValueEntity == null) {
                        product.getErrBuilder().append(String.format("[%s]规格值不存在", specValue));
                        continue;
                    }

                    SpecDto specDto = new SpecDto();
                    specDto.setNameId(specNameEntity.getId());
                    specDto.setSpecName(specNameEntity.getSpecName());
                    specDto.setSpecAlias(specNameEntity.getSpecAlias());
                    specDto.setValueId(specValueEntity.getId());
                    specDto.setSpecValue(specValueEntity.getValue());

                    // 如果是第一层规格, 直接往skuList 里面加即可
                    if (level == 1) {
                        ProductSkuBo sku = new ProductSkuBo();
                        sku.setSpecList(Arrays.asList(specDto));
                        // 第一层的时候 需要加入售价和库存
                        if (NumberUtil.isNumber(product.getSalePrice())) {
                            sku.setSalePrice(BigDecimal.valueOf(Double.parseDouble(product.getSalePrice())));
                        }
                        if (NumberUtil.isLong(product.getStock())) {
                            sku.setStock(Long.parseLong(product.getStock()));
                        }
                        skuList.add(sku);
                    }
                    // 规格层数大于1, 需要将规格值组合
                    else {
                        for (int j = 0; j < tempSkuList.size(); j++) {
                            ProductSkuBo sku = JsonUtil.parseObject(JsonUtil.toJsonString(tempSkuList.get(j)), ProductSkuBo.class);
                            sku.getSpecList().add(specDto);
                            skuList.add(sku);
                        }
                    }
                }
            }


            // 指定货号
            for (int i = 0; i < skuList.size(); i++) {
                ProductSkuBo sku = skuList.get(i);
                sku.setSkuCode(product.getProductCode() + CommonConstant.SKU_CODE_SPLIT + i);
            }

            product.setSkuList(skuList);
            product.setHasSku(true);
        });
    }


    /**
     * 校验库存
     *
     * @param productList
     */
    private void checkStock(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        productList.forEach(product -> {
            if (product.getStock() == null) {
                product.getErrBuilder().append("库存不能为空;");
                return;
            }

            if (!NumberUtil.isLong(product.getStock())) {
                product.getErrBuilder().append("库存格式异常, 请输入正整数");
                return;
            }

            if (!ParameterHelper.checkStock(Long.parseLong(product.getStock()))) {
                product.getErrBuilder().append(String.format("库存取值范围为%s-%s;", ParameterConstant.MIN_STOCK, ParameterConstant.MAX_STOCK));
            }
        });
    }

    /**
     * 校验价格
     *
     * @param productList
     */
    private void checkPrice(List<PlatformProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getSalePrice())) {
                product.getErrBuilder().append("商城价不能为空;");
            }

            if (StringUtils.isNotEmpty(product.getSalePrice())) {
                // 商城价格
                if (!ParameterHelper.checkAmount(product.getSalePrice())) {
                    product.getErrBuilder().append(String.format("商城价范围为%s-%s, 且最多保留两位小数", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
                }

                // 商城价范围
                else if (!ParameterHelper.checkPrice(BigDecimal.valueOf(Double.parseDouble(product.getSalePrice())))) {
                    product.getErrBuilder().append(String.format("商城价范围为%s-%s;", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
                }
            }

            if (StringUtils.isNotEmpty(product.getMarketPrice())) {
                if (!ParameterHelper.checkAmount(product.getMarketPrice())) {
                    product.getErrBuilder().append(String.format("市场价格式不正确(最多保留两位小数的正数);"));
                }

                // 市场价范围
                else if (!ParameterHelper.checkMarketPrice(BigDecimal.valueOf(Double.parseDouble(product.getMarketPrice())))) {
                    product.getErrBuilder().append(String.format("市场价范围为%s-%s;", ParameterConstant.MIN_MARKET_PRICE, ParameterConstant.MAX_MARKET_PRICE));
                }
            }
        });
    }

    /**
     * 构建保存商品参数
     *
     * @param platformProductImportDto 导入的商品数据
     * @return 商品参数
     */
    private ProductBo productBoBuild(PlatformProductImportDto platformProductImportDto) {
        Long operationUserId = ProductImportAssist.getOperationUserId();
        Long operationShopId = ProductImportAssist.getOperationShopId();

        ProductBo product = JsonUtil.copy(platformProductImportDto, ProductBo.class);
        product.setCategoryId(platformProductImportDto.getThirdCategoryId());
        product.setMobileDescription(platformProductImportDto.getDescription());
        if (platformProductImportDto.getShopCategoryId() != null) {
            product.setShopCategoryIdList(Arrays.asList(platformProductImportDto.getShopCategoryId()));
        }
        // 导入指定销售状态
        product.setSaleStatus(ProductImportAssist.getSaleStatus());
        product.setAuditStatus(ProductEnum.AuditStatusEnum.ON_SALE.getCode());
        // 指定为了草稿, 则可以跳过审核状态直接生效, 同时手动指定了商品的状态
        product.setDraftFlag(Boolean.TRUE);
        product.setOperationUserId(operationUserId);
        product.setOperationShopId(operationShopId);
        return product;
    }


    /**
     * 递归构建sku
     *
     * @param specList 规格参数
     */
    private List<ProductSkuBo> buildSku(List<SpecCombinationBo> specList) {
        return skuCombinationAssist.generateCombinations(specList, 0l);
    }

    /**
     * 构建单规格
     */
    private ProductSkuBo buildSingleSpec(PlatformProductImportDto product) {
        ProductSkuBo sku = JsonUtil.copy(product, ProductSkuBo.class);
        sku.setSkuId(String.format(CommonConstant.SINGLE_SKU_ID_FORMAT, 0));
        sku.setSkuCode(product.getProductCode());
        return sku;
    }

    /**
     * 补齐sku数据
     */
    private void appendSkuList(PlatformProductImportDto product, List<ProductSkuBo> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        for (int i = 0; i < skuList.size(); i++) {
            ProductSkuBo sku = skuList.get(i);
            sku.setMeasureUnit(product.getMeasureUnit());
            sku.setStock(StringUtils.isEmpty(product.getStock()) ? 0 : Long.parseLong(product.getStock()));
            sku.setSalePrice(BigDecimal.valueOf(Double.parseDouble(product.getSalePrice())));
            sku.setSkuCode(product.getProductCode() + CommonConstant.SKU_CODE_SPLIT + i);
        }
    }

    /**
     * 构建错误信息
     *
     * @param productList 导入的数据
     */
    private void buildErrMsg(List<PlatformProductImportDto> productList) {
        productList.forEach(product -> {
            String errMsg = StringUtils.defaultString(product.getErrMsg(), StrUtil.EMPTY);
            product.setErrMsg(errMsg + product.getErrBuilder().toString());
        });
    }

}
