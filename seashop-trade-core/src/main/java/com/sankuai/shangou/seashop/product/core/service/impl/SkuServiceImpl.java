package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.product.core.service.SkuService;
import com.sankuai.shangou.seashop.product.core.service.model.SkuQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;

/**
 * <AUTHOR>
 * @date 2023/12/21 9:26
 */
@Service
@Slf4j
public class SkuServiceImpl implements SkuService {

    @Resource
    private SkuRepository skuRepository;

    @Override
    public List<Sku> querySkuList(ProductSkuQueryReq queryBo) {
        return skuRepository.list(commonSkuQueryWrapperBuilder(queryBo));
    }

    /**
     * 通用查询条件构建 (此方法里面的in 均在入参处校验过不超过200)
     *
     * @param queryBo
     * @return
     */
    private LambdaQueryWrapper<Sku> commonSkuQueryWrapperBuilder(ProductSkuQueryReq queryBo) {
        // ProductIds 不为空 查询
        LambdaQueryWrapper<Sku> wrapper = new LambdaQueryWrapper<>();
        if (queryBo.getShopId() != null) {
            wrapper.eq(Sku::getShopId, queryBo.getShopId());
        }
        if (CollectionUtils.isNotEmpty(queryBo.getProductIds())) {
            return wrapper.in(Sku::getProductId, queryBo.getProductIds());
        }
        // 优先 skuAutoId  其次 skuCode  再次skuId
        if (CollectionUtils.isNotEmpty(queryBo.getSkuAutoIds())) {
            return wrapper.in(Sku::getId, queryBo.getSkuAutoIds());
        }
        if (CollectionUtils.isNotEmpty(queryBo.getSkuCodes())) {
            return wrapper.in(Sku::getSkuCode, queryBo.getSkuCodes());
        }
        return wrapper.in(Sku::getSkuId, queryBo.getSkuIdList());
    }
}
