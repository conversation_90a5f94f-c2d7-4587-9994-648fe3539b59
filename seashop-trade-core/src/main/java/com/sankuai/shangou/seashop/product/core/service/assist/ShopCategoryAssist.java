package com.sankuai.shangou.seashop.product.core.service.assist;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.dao.core.domain.ShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopCategoryRepository;

/**
 * <AUTHOR>
 * @date 2024/03/04 11:48
 */
@Component
public class ShopCategoryAssist {

    @Resource
    private ShopCategoryRepository shopCategoryRepository;

    public Map<Long, ShopCategory> getShopCategoryMap(List<Long> shopCategoryIds) {
        List<ShopCategory> shopCategories = shopCategoryRepository.listShopCategory(shopCategoryIds);
        return shopCategories.stream().collect(Collectors.toMap(ShopCategory::getId, Function.identity(), (k1, k2) -> k1));
    }

    public List<Long> getChildShopCategoryIds(Long shopCategoryId) {
        if (shopCategoryId == null) {
            return Collections.EMPTY_LIST;
        }
        return shopCategoryRepository.getChildCategoryIds(shopCategoryId);
    }

    public List<Long> getChildShopCategoryIdsAndSelf(Long shopCategoryId) {
        if (shopCategoryId == null) {
            return Collections.EMPTY_LIST;
        }

        List<Long> childIds = new ArrayList<>();
        childIds.add(shopCategoryId);
        childIds.addAll(getChildShopCategoryIds(shopCategoryId));
        return childIds;
    }
}
