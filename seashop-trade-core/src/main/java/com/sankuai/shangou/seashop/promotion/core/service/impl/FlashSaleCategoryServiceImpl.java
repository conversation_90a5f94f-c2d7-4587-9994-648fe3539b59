package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.PromotionResultCodeEnum;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleCategoryService;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSale;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleCategory;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.FlashSaleCategoryRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.FlashSaleRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleCategoryAddReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleCategoryResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Service
@Slf4j
public class FlashSaleCategoryServiceImpl implements FlashSaleCategoryService {

    @Resource
    private FlashSaleCategoryRepository flashSaleCategoryRepository;
    @Resource
    private FlashSaleRepository flashSaleRepository;

    @Override
    public void add(FlashSaleCategoryAddReq request) {
        // 校验名字的唯一性
        int countByName = flashSaleCategoryRepository.countByName(request.getCategoryName());
        if (countByName > 0) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_CATEGORY_NAME_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_CATEGORY_NAME_EXIST.getMsg());
        }

        // 校验数理
        long count = flashSaleCategoryRepository.count();
        if (count >= PromotionConstant.LIMIT_TIME_BUY_CATEGORY_MAX_NUM) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_CATEGORY_MAX.getCode(),
                    String.format(PromotionResultCodeEnum.FLASH_SALE_CATEGORY_MAX.getMsg(), PromotionConstant.LIMIT_TIME_BUY_CATEGORY_MAX_NUM));
        }
        FlashSaleCategory flashSaleCategory = new FlashSaleCategory();
        flashSaleCategory.setCategoryName(request.getCategoryName());
        flashSaleCategoryRepository.save(flashSaleCategory);
    }

    @Override
    public void delete(BaseIdReq request) {
        FlashSaleCategory flashSaleCategory = flashSaleCategoryRepository.getById(request.getId());
        if (null == flashSaleCategory || flashSaleCategory.getDelFlag().equals(Boolean.TRUE)) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_CATEGORY_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_CATEGORY_NOT_EXIST.getMsg());
        }

        // 判断限时购活动是否有引用
        List<FlashSale> flashSaleList = flashSaleRepository.listEffectiveByCategoryId(request.getId());
        if(CollUtil.isNotEmpty(flashSaleList)){
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_CATEGORY_USED.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_CATEGORY_USED.getMsg());
        }

        flashSaleCategory.setDelFlag(Boolean.TRUE);
        flashSaleCategoryRepository.updateById(flashSaleCategory);
    }

    @Override
    public BasePageResp<FlashSaleCategoryResp> pageList(BasePageReq request) {
        Page<FlashSaleCategory> flashSaleCategories = flashSaleCategoryRepository.pageList(request.buildPage());
        return PageResultHelper.transfer(flashSaleCategories, FlashSaleCategoryResp.class);
    }
}
