package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.query;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品审核查询店铺分类处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:22
 */
@Component
@Slf4j
public class QueryShopCategoryAuditHandler extends AbsQueryProductAuditHandler {

    @Resource
    private ProductAssist productAssist;

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        Long productId = context.getProductId();

        // 查询店铺分类
        productBo.setShopCategoryIdList(productAssist.getShopCategoryIds(productId));
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_SHOP_CATEGORY_AUDIT;
    }

}
