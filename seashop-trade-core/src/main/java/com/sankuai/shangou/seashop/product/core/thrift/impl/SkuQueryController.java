package com.sankuai.shangou.seashop.product.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.product.core.service.SkuQueryService;
import com.sankuai.shangou.seashop.product.thrift.core.SkuQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 14:58
 */

@RestController
@RequestMapping("/sku")
public class SkuQueryController implements SkuQueryFeign {

    @Resource
    private SkuQueryService skuQueryService;

    @PostMapping(value = "/querySkuList", consumes = "application/json")
    @Override
    public ResultDto<SkuListResp> querySkuList(@RequestBody SkuQueryReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("querySkuList", request, req -> {

            List<SkuQueryResp> skuList = skuQueryService.querySkuList(req);
            return SkuListResp.builder().skuList(skuList).build();
        });
    }
}
