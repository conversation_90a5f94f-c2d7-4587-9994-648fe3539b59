package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleCategoryService;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleCategoryResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCategoryQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@RestController
@RequestMapping("/flashSaleCategory")
public class FlashSaleCategoryQueryController implements FlashSaleCategoryQueryFeign {

    @Resource
    private FlashSaleCategoryService flashSaleCategoryService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<FlashSaleCategoryResp>> pageList(@RequestBody BasePageReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            BasePageResp<FlashSaleCategoryResp> pageList = flashSaleCategoryService.pageList(req);
            return pageList;
        });
    }
}
