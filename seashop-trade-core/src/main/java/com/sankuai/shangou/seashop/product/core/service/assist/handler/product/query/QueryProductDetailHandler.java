package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescription;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 商品详情查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:08
 */
@Component
@Slf4j
public class QueryProductDetailHandler extends AbsProductHandler {

    @Resource
    private ProductDescriptionRepository productDescriptionRepository;
    @Resource
    private ProductAssist productAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        ProductBo productBo = context.getOldProductBo();

        // 查询详情
        ProductDescription description = productDescriptionRepository.getProductDescriptionByProductId(productId);
        AssertUtil.throwIfNull(description, "商品详情不存在");
        productBo.setDescriptionPrefixId(productAssist.getValidDescriptionTemplateId(description.getDescriptionPrefixId()));
        productBo.setDescriptionSuffixId(productAssist.getValidDescriptionTemplateId(description.getDescriptionSuffixId()));
        productBo.setDescription(description.getDescription());
        productBo.setMobileDescription(description.getMobileDescription());
        if (StringUtils.isNotBlank(description.getDescriptionPics())) {
            productBo.setDescriptionPicList(Arrays.asList(description.getDescriptionPics().split(",")));
        }
        context.setOldProductBo(productBo);
    }


    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_PRODUCT_DETAIL;
    }

}
