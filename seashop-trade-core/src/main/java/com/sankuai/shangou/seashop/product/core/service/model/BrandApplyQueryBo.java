package com.sankuai.shangou.seashop.product.core.service.model;


import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/07 11:10
 */
@Getter
@Setter
@Builder
public class BrandApplyQueryBo {

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 审核状态
     */
    private BrandEnum.AuditStatusEnum auditStatus;

    /**
     * 供应商id
     */
    private Long shopId;

    /**
     * 供应商名称
     */
    private String shopName;

    /**
     * 供应商id的集合
     */
    private List<Long> shopIds;

    /**
     * 排序字段
     */
    private List<FieldSortReq> sortList;

}
