package com.sankuai.shangou.seashop.product.core.service.excel.read.dto;

import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.base.eimport.anno.ExcelField;
import com.sankuai.shangou.seashop.base.eimport.regex.FieldRegexEnum;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/22 10:40
 */
@Getter
@Setter
public class PriceImportDto extends RowReadResult {

    @ExcelProperty(value = "*商品ID")
    @ExcelField(required = true, regex = ParameterConstant.PRODUCT_ID_CHECK_REGEX, regexMsg = "商品ID格式错误")
    private String productId;

    @ExcelProperty(value = "规格ID")
    @ExcelField(regexEnum = FieldRegexEnum.POSITIVE_INTEGER_REGEX)
    private String skuAutoId;

    @ExcelProperty(value = "商城价")
    @ExcelField(regexEnum = FieldRegexEnum.AMOUNT_REGEX)
    private String salePrice;

    @ExcelProperty(value = "*是否开启阶梯价")
    @ExcelField(required = true, regexEnum = FieldRegexEnum.YES_OR_NO_REGEX)
    private String whetherOpenLadderPrice;

    @ExcelProperty(value = "1起购量")
    @ExcelField(regexEnum = FieldRegexEnum.POSITIVE_INTEGER_REGEX)
    private String minBath1;

    @ExcelProperty(value = "1阶梯价")
    @ExcelField(regexEnum = FieldRegexEnum.AMOUNT_REGEX)
    private String ladderPrice1;

    @ExcelProperty(value = "2起购量")
    @ExcelField(regexEnum = FieldRegexEnum.POSITIVE_INTEGER_REGEX)
    private String minBath2;

    @ExcelProperty(value = "2阶梯价")
    @ExcelField(regexEnum = FieldRegexEnum.AMOUNT_REGEX)
    private String ladderPrice2;

    @ExcelProperty(value = "3起购量")
    @ExcelField(regexEnum = FieldRegexEnum.POSITIVE_INTEGER_REGEX)
    private String minBath3;

    @ExcelProperty(value = "3阶梯价")
    @ExcelField(regexEnum = FieldRegexEnum.AMOUNT_REGEX)
    private String ladderPrice3;

    @ExcelIgnore
    private boolean whetherOpenLadderPriceFlag;

    @ExcelIgnore
    private List<ProductLadderPriceBo> ladderPriceBoList;

    @ExcelIgnore
    private String skuId;

    @ExcelIgnore
    private Long shopId;

    @ExcelIgnore
    private StringBuilder errBuilder = new StringBuilder();
}
