package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.model.bo.FullReductionQueryBo;
import com.sankuai.shangou.seashop.promotion.core.service.FullReductionService;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionSimpleDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryFullReductionDetailReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FullReductionQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@RestController
@RequestMapping("/fullReduction")
public class FullReductionQueryController implements FullReductionQueryFeign {

    @Resource
    private FullReductionService fullReductionService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<FullReductionSimpleResp>> pageList(@RequestBody FullReductionQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            FullReductionQueryBo queryBo = JsonUtil.copy(req, FullReductionQueryBo.class);
            BasePageResp<FullReductionSimpleDto> couponListDtoBasePageResp = fullReductionService.pageList(queryBo);
            return PageResultHelper.transfer(couponListDtoBasePageResp, FullReductionSimpleResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @Override
    public ResultDto<FullReductionResp> getById(@RequestBody QueryFullReductionDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            FullReductionDto dto = fullReductionService.getById(req.getId(), req.getShopId());
            return JsonUtil.copy(dto, FullReductionResp.class);
        });
    }

    @PostMapping(value = "/queryByShopId", consumes = "application/json")
    @Override
    public ResultDto<FullReductionResp> queryByShopId(@RequestBody ShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryByShopId", request, req -> {
            req.checkParameter();
            FullReductionResp dto = fullReductionService.queryByShopId(req);
            return dto;
        });
    }
}
