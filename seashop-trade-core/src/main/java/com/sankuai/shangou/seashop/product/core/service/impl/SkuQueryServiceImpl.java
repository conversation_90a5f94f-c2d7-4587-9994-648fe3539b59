package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.SkuQueryService;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 15:01
 */
@Service
@Slf4j
public class SkuQueryServiceImpl implements SkuQueryService {

    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuAssist skuAssist;

    @Override
    public List<SkuQueryResp> querySkuList(SkuQueryReq request) {
        List<Sku> skuList = skuRepository.list(commonQueryBuild(request));
        List<Long> skuAutoIds = skuList.stream().map(Sku::getId).collect(Collectors.toList());
        Map<Long, Long> stockMap = skuAssist.getStockMap(skuAutoIds);
        return JsonUtil.copyList(skuList, SkuQueryResp.class, (db, res) -> {
            res.setSpecName(skuAssist.getSpecValue(db));
            res.setStock(stockMap.get(db.getId()));
        });
    }

    private LambdaQueryWrapper<Sku> commonQueryBuild(SkuQueryReq request) {
        // 已经在入参处校验 in 不超过200
        LambdaQueryWrapper<Sku> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollectionUtils.isNotEmpty(request.getSkuIdList()), Sku::getSkuId, request.getSkuIdList());
        wrapper.in(CollectionUtils.isNotEmpty(request.getProductIds()), Sku::getProductId, request.getProductIds());
        return wrapper;
    }

}
