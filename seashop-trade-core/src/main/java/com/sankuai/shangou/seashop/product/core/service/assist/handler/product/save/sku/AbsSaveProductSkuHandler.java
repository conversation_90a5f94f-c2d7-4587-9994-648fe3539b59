package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save.sku;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductFieldHelper;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.SyncStockAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save.AbsSaveProductHandler;
import com.sankuai.shangou.seashop.product.core.service.converter.SkuConverter;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockRepository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

/**
 * 抽象sku保存处理器
 *
 * <AUTHOR>
 * @date 2023/11/15 18:28
 */
public abstract class AbsSaveProductSkuHandler<T> extends AbsSaveProductHandler {

    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuStockRepository skuStockRepository;
    @Resource
    private SyncStockAssist syncStockAssist;
    @Resource
    private SkuAssist skuAssist;

    /**
     * 创建sku
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void create(ProductContext context) {
        ProductBo saveProductBo = context.getSaveProductBo();
        ProductBo auditProductBo = context.getAuditProductBo();
        List<ProductSkuBo> saveSkuList = saveProductBo.getSkuList();
        auditProductBo.setSkuList(JsonUtil.copyList(saveSkuList, ProductSkuBo.class));

        List<Sku> skuList = SkuConverter.convertToSku(saveSkuList, context.getProductId(), context.getShopId());
        skuRepository.saveBatch(skuList);

        Map<String, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getSkuId, Function.identity(), (k1, k2) -> k2));

        // 保存库存信息
        List<SkuStock> skuStockList = JsonUtil.copyList(saveSkuList, SkuStock.class);
        skuStockList.forEach(skuStock -> {
            Sku sku = skuMap.get(skuStock.getSkuId());
            skuStock.setProductId(sku.getProductId());
            skuStock.setSkuId(sku.getSkuId());
            skuStock.setSkuAutoId(sku.getId());
            skuStock.setShopId(context.getShopId());
        });
        skuStockRepository.saveBatch(skuStockList);
        skuStockList = skuStockList.stream().filter(skuStock -> skuStock.getStock() != null).collect(Collectors.toList());
        syncStockAssist.coverStockForEditProduct(context.getShopId(), skuStockList);
    }

    /**
     * 执行更新方法前置逻辑(计算本次提交的sku信息和数据库sku信息的差异, 获取需要新增/编辑/删除的数据)
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void beforeUpdate(ProductContext context) {
        ProductBo saveProductBo = context.getSaveProductBo();
        ProductBo oldProductBo = context.getOldProductBo();

        // 前面的流程已经判断 这两个值不会为空
        List<ProductSkuBo> newSkuList = saveProductBo.getSkuList();
        List<ProductSkuBo> oldSkuList = oldProductBo.getSkuList();

        // 对比数据
        Map<T, ProductSkuBo> oldSkuMap = oldSkuList.stream().collect(Collectors.toMap(sku -> getUniqueKey(sku), Function.identity(), (k1, k2) -> k2));

        Map<Object, Map<Boolean, Map<String, Object>>> skuCompareMap = new HashMap<>();
        // 找到uniqueKey 相同的值进行对比
        List<ProductSkuBo> addSkuList = new ArrayList<>();
        List<ProductSkuBo> updateSkuList = new ArrayList<>();

        for (ProductSkuBo newSku : newSkuList) {
            T uniqueKey = getUniqueKey(newSku);
            ProductSkuBo oldSku = oldSkuMap.get(uniqueKey);

            if (oldSku == null) {
                addSkuList.add(newSku);
            }
            else {
                newSku.setSkuAutoId(oldSku.getSkuAutoId());
                newSku.setSkuStockId(oldSku.getSkuStockId());
                updateSkuList.add(newSku);
            }

            // 部分更新的场景忽略null值
            Map<Boolean, Map<String, Object>> fieldMap = ProductFieldHelper.getFieldMap(newSku, oldSku, context.isPartSave());
            skuCompareMap.put(uniqueKey, fieldMap);
        }
        context.setSkuCompareMap(skuCompareMap);

        // 计算出需要删除的sku
        List<T> existUniqueKeys = newSkuList.stream().map(sku -> getUniqueKey(sku)).collect(Collectors.toList());
        List<ProductSkuBo> deleteSkuList =
                oldSkuList.stream().filter(item -> !existUniqueKeys.contains(getUniqueKey(item))).collect(Collectors.toList());
        context.setCreateSkuList(addSkuList);
        context.setUpdateSkuList(updateSkuList);
        context.setDeleteSkuList(deleteSkuList);
    }

    /**
     * 更新sku
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void update(ProductContext context) {
        ProductBo oldProductBo = context.getOldProductBo();
        List<ProductSkuBo> oldSkuList = oldProductBo.getSkuList();
        Map<T, ProductSkuBo> oldSkuMap = oldSkuList.stream().collect(Collectors.toMap(sku -> getUniqueKey(sku), Function.identity(), (k1, k2) -> k2));

        // 把无需审核的字段直接更新到数据库
        Map<Object, Map<Boolean, Map<String, Object>>> skuCompareMap = context.getSkuCompareMap();

        List<Sku> updSkuList = new ArrayList<>();
        List<SkuStock> updSkuStockList = new ArrayList<>();
        Boolean needAudit = Boolean.FALSE;
        Set<Object> uniqueKeys = skuCompareMap.keySet();
        for (Object uniqueKey : uniqueKeys) {
            Map<Boolean, Map<String, Object>> fieldMap = skuCompareMap.get(uniqueKey);
            ProductSkuBo oldSku = oldSkuMap.get(uniqueKey);
            Map<String, Object> auditFieldMap = fieldMap.get(Boolean.TRUE);
            Map<String, Object> noAuditFieldMap = fieldMap.get(Boolean.FALSE);

            if (!noAuditFieldMap.isEmpty() && oldSku != null) {
                Sku updSku = JsonUtil.copy(noAuditFieldMap, Sku.class);
                updSku.setId(oldSku.getSkuAutoId());
                updSku.setUpdateTime(new Date());
                updSkuList.add(updSku);

                SkuStock updSkuStock = JsonUtil.copy(noAuditFieldMap, SkuStock.class);
                updSkuStock.setSkuId(oldSku.getSkuId());
                updSkuStockList.add(updSkuStock);
            }

            if (!auditFieldMap.isEmpty()) {
                needAudit = Boolean.TRUE;
            }
        }

        // 批量更新需要直接生效的数据
        if (!updSkuList.isEmpty()) {
            skuRepository.updateBatchById(updSkuList);
            // 保存安全库存
            List<SkuStock> updSafeStockList = updSkuStockList.stream().filter(skuStock -> skuStock.getSafeStock() != null).map(item -> {
                SkuStock skuStock = new SkuStock();
                skuStock.setSkuId(item.getSkuId());
                skuStock.setSafeStock(item.getSafeStock());
                return skuStock;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updSafeStockList)) {
                skuStockRepository.updateBatchBySkuId(updSafeStockList);
            }

            List<SkuStock> updateStockLst = updSkuStockList.stream().filter(skuStock -> skuStock.getStock() != null).collect(Collectors.toList());
            syncStockAssist.coverStockForEditProduct(context.getShopId(), updateStockLst);
        }
        if (needAudit) {
            context.setNeedAudit(Boolean.TRUE);
        }
    }

    /**
     * 保存到草稿
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void updateDraft(ProductContext context) {
        // 执行新增
        createDraftSku(context);

        // 执行编辑
        updateDraftSku(context);

        // 执行删除
        deleteDraftSku(context);
    }

    /**
     * 执行更新商品后置逻辑(对于部分更新方法, 需要填充旧的商品数据, 用于审核)
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void postUpdate(ProductContext context) {
        if (!context.isPartSave() || context.isDraftFlag()) {
            return;
        }

        ProductBo saveProductBo = context.getSaveProductBo();
        ProductBo oldProductBo = context.getOldProductBo();
        ProductBo auditProductBo = context.getAuditProductBo();
        if (oldProductBo == null) {
            return;
        }

        // 如果部分保存, 如果newSkuList没有值, 则表示没有修改规格
        List<ProductSkuBo> newSkuList = saveProductBo.getSkuList();
        List<ProductSkuBo> oldSkuList = oldProductBo.getSkuList();
        if (newSkuList == null) {
            auditProductBo.setSkuList(JsonUtil.copyList(oldSkuList, ProductSkuBo.class));
            return;
        }

        List<ProductSkuBo> auditSkuList = JsonUtil.copyList(oldSkuList, ProductSkuBo.class);
        List<T> existUniqueKeys = auditSkuList.stream().map(sku -> getUniqueKey(sku)).collect(Collectors.toList());
        List<ProductSkuBo> addSkuList = newSkuList.stream().filter(sku -> !existUniqueKeys.contains(getUniqueKey(sku))).collect(Collectors.toList());
        auditSkuList.addAll(addSkuList);

        Map<T, ProductSkuBo> newSkuMap = newSkuList.stream().collect(Collectors.toMap(sku -> getUniqueKey(sku), Function.identity(), (k1, k2) -> k2));
        CopyOptions copyOptions = CopyOptions.create().setIgnoreNullValue(true);
        auditSkuList.forEach(auditSku -> {
            ProductSkuBo newSku = newSkuMap.get(getUniqueKey(auditSku));
            if (newSku != null) {
                BeanUtil.copyProperties(newSku, auditSku, copyOptions);
            }
        });

        // 如果sku数量不一致, 则需要审核
        if (auditSkuList.size() != oldSkuList.size()) {
            context.setNeedAudit(Boolean.TRUE);
        }

        auditProductBo.setSkuList(auditSkuList);
    }


    protected void createDraftSku(ProductContext context) {
        List<ProductSkuBo> skuList = context.getCreateSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        List<Sku> addSkus = SkuConverter.convertToSku(skuList, context.getProductId(), context.getShopId());
        skuRepository.saveBatch(addSkus);
        Map<String, Sku> skuMap = addSkus.stream().collect(Collectors.toMap(Sku::getSkuId, Function.identity(), (k1, k2) -> k2));

        List<SkuStock> skuStockList = JsonUtil.copyList(skuList, SkuStock.class, (source, target) -> {
            target.setSkuAutoId(skuMap.get(source.getSkuId()).getId());
            target.setProductId(context.getProductId());
            target.setShopId(context.getShopId());
        });
        skuStockRepository.saveBatch(skuStockList);
    }

    protected void updateDraftSku(ProductContext context) {
        List<ProductSkuBo> skuList = context.getUpdateSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        List<Sku> updSkus = JsonUtil.copyList(skuList, Sku.class, (source, target) -> {
            target.setId(source.getSkuAutoId());
        });
        skuRepository.updateBatchById(updSkus);

        List<SkuStock> skuStockList = JsonUtil.copyList(skuList, SkuStock.class, (source, target) -> {
            target.setId(source.getSkuStockId());
        });
        skuStockRepository.updateBatchById(skuStockList);
    }

    protected void deleteDraftSku(ProductContext context) {
        List<ProductSkuBo> skuList = context.getDeleteSkuList();
        if (context.isPartSave() || CollectionUtils.isEmpty(skuList)) {
            return;
        }
        List<String> skuIds = skuList.stream().map(ProductSkuBo::getSkuId).collect(Collectors.toList());
        skuRepository.removeBySkuIds(skuIds);
        skuStockRepository.removeBySkuIds(skuIds);
    }

    /**
     * 获取唯一key
     *
     * @param sku 数据
     * @return 唯一key
     */
    public abstract T getUniqueKey(ProductSkuBo sku);

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SAVE_SKU;
    }

}
