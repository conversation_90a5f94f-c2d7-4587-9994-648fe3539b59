package com.sankuai.shangou.seashop.product.core.service;

import com.sankuai.shangou.seashop.product.core.service.model.UpdateVisitCountBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductEsBo;

/**
 * <AUTHOR>
 * @date 2023/11/06 11:57
 */
public interface ProductEsBuildService {

    /**
     * 查询商品信息(用于ES 构建)
     *
     * @param productId 商品id
     * @return 商品信息
     */
    ProductEsBo queryProductForEsBuild(Long productId);

    /**
     * 构建商品ES库存状态
     *
     * @param productId 商品id
     */
    void updateStockEs(Long productId);

    /**
     * 查询商品审核信息(用于ES 构建)
     *
     * @param productId 商品id
     * @return 商品审核信息
     */
    ProductEsBo queryProductAuditForEsBuild(Long productId);

    /**
     * 构建商品审核ES
     *
     * @param productId 商品id
     */
    void buildProductAuditEs(Long productId);

    /**
     * 构建商品es
     *
     * @param productId 商品id
     */
    void buildProductEs(Long productId);

    /**
     * 构建商品收藏es
     *
     * @param productId 商品id
     */
    void buildProductFavoriteEs(Long productId);

    /**
     * 构建商品评论es
     *
     * @param productId 商品id
     */
    void buildProductCommentEs(Long productId);

    /**
     * 构建商品销量es
     *
     * @param productId 商品id
     */
    void buildProductSalesEs(Long productId);

    /**
     * 构建商品评论es
     *
     * @param orderId 订单id
     */
    void buildProductCommentEsByOrderId(String orderId);
}
