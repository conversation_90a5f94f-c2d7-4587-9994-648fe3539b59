package com.sankuai.shangou.seashop.product.core.service.assist.handler;

import java.util.List;
import java.util.Map;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.remote.user.model.RemoteShopBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.SkuUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.SaveProductResp;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/15 14:26
 */
@Getter
@Setter
@Builder
public class ProductContext {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 本次提交的记录
     */
    private ProductBo saveProductBo;

    /**
     * 数据库中的记录
     */
    private ProductBo oldProductBo;

    /**
     * 商品提审记录
     */
    private ProductBo auditProductBo;

    /**
     * 是否是编辑
     */
    private boolean editFlag;

    /**
     * 是否需要审核
     */
    private boolean needAudit;

    /**
     * 当前商家id
     */
    private Long shopId;

    /**
     * 是否保存到草稿
     */
    private boolean draftFlag;

    /**
     * 商品审核信息
     */
    private ProductAudit productAudit;

    /**
     * 商品来源
     */
    private ProductSourceEnum changeSource;

    /**
     * 是否是部分保存
     */
    private boolean partSave;

    /**
     * 商品表字段对比结果
     */
    private Map<Boolean, Map<String, Object>> productCompareMap;

    /**
     * sku表字段对比结果
     */
    private Map<Object, Map<Boolean, Map<String, Object>>> skuCompareMap;

    /**
     * sku更新key
     */
    private SkuUpdateKeyEnum skuUpdateKey;

    /**
     * 保存商品返回值
     */
    private SaveProductResp saveProductResp;

    /**
     * 新增的sku集合
     */
    private List<ProductSkuBo> createSkuList;

    /**
     * 编辑的sku集合
     */
    private List<ProductSkuBo> updateSkuList;

    /**
     * 删除的sku集合
     */
    private List<ProductSkuBo> deleteSkuList;

    /**
     * 是否跳过风控流程(为 true时风控直接通过)
     */
    private boolean skipRisk;

    /**
     * 变更类型
     */
    private ProductChangeType changeType;

    /**
     * 店铺信息
     */
    private RemoteShopBo currentShop;

    /**
     * 旧的商品信息
     */
    private Product dbProduct;

    @Override
    public String toString() {
        return JsonUtil.toJsonString(this);
    }
}
