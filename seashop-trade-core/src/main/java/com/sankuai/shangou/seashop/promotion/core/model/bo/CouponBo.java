package com.sankuai.shangou.seashop.promotion.core.model.bo;//package com.sankuai.shangou.seashop.promotion.core.model.bo;
//
//import lombok.*;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * @author: lhx
// * @date: 2023/11/9/009
// * @description:
// */
//@TypeDoc(description = "优惠券活动响应体")
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
//@Builder
//@ToString
//public class CouponBo {
//
//    //主键ID
//    private Long id;
//
//    //店铺ID
//    private Long shopId;
//
//    //店铺名称
//    private String shopName;
//
//    //面值(价格)
//    private Long price;
//
//    //最大可领取张数
//    private Integer perMax;
//
//    //订单金额（满足多少钱才能使用）
//    private Long orderAmount;
//
//    //发行张数
//    private Integer num;
//
//    //开始时间
//    private Date startTime;
//
//    //结束时间
//    private Date endTime;
//
//    //优惠券名称
//    private String couponName;
//
//    //领取方式 0 店铺首页 1 积分兑换 2 主动发放
//    private Integer receiveType;
//
//    //使用范围：0=全场通用，1=部分商品可用
//    private Integer useArea;
//
//    //备注
//    private String remark;
//
//    //产品ID列表
//    private List<Long> productIdList;
//
//    //推广方式：0 平台；4 移动端(小程序)
//    private List<Integer> platForm;
//}
