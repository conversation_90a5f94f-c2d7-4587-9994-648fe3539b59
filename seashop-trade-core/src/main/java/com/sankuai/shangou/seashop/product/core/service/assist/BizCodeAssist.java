package com.sankuai.shangou.seashop.product.core.service.assist;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LeafKeyConstant;

/**
 * <AUTHOR>
 * @date 2023/12/06 13:55
 */
@Component
public class BizCodeAssist {

    @Resource
    private LeafService leafService;

    /**
     * 获取商品id
     *
     * @return 商品id
     */
    public Long getProductId() {
        return leafService.generateNo(LeafKeyConstant.PRODUCT_ID_LEAF_KEY);
    }

    /**
     * 获取商品编码
     *
     * @return 商品编码
     */
    public String getProductCode() {
        String productCode = String.valueOf(leafService.generateNoBySnowFlake(LeafKeyConstant.PRODUCT_CODE_LEAF_KEY));
        // 截取后8位
        return productCode.substring(productCode.length() - CommonConstant.PRODUCT_CODE_LENGTH);
    }

    public List<String> getProductCodes(int size) {
        // 按照100 分组 分批生成
        int groupSize = 100;
        int groupCount = size / groupSize;
        int remainCount = size % groupSize;
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < groupCount; i++) {
            ids.addAll(leafService.batchGenerateNoBySnowFlake(LeafKeyConstant.PRODUCT_CODE_LEAF_KEY, groupSize));
        }
        if (remainCount > 0) {
            ids.addAll(leafService.batchGenerateNoBySnowFlake(LeafKeyConstant.PRODUCT_CODE_LEAF_KEY, remainCount));
        }
        return ids.stream().map(id -> String.valueOf(id).substring(id.toString().length() - CommonConstant.PRODUCT_CODE_LENGTH)).collect(Collectors.toList());
    }

    /**
     * 获取删除版本号
     *
     * @return 删除版本号
     */
    public Long getDeleteVersion() {
        return leafService.generateNoBySnowFlake(LeafKeyConstant.DELETE_VERSION_LEAF_KEY);
    }

}
