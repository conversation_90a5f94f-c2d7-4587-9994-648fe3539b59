package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.ProductAuditService;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.thrift.core.ProductAuditCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.ProductAuditReq;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:05
 */
@RestController
@RequestMapping("/productAudit")
public class ProductAuditCmdController implements ProductAuditCmdFeign {

    @Resource
    private ProductAuditService productAuditService;

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductAuditReq.class,
            entity = Product.class,
            actionName = "批量审核商品")
    @PostMapping(value = "/batchAuditProduct", consumes = "application/json")
    public ResultDto<BaseResp> batchAuditProduct(@RequestBody ProductAuditReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchAuditProduct", request, req -> {
            req.checkParameter();

            productAuditService.batchAuditProduct(JsonUtil.copy(req, ProductAuditBo.class));
            return new BaseResp();
        });
    }
}
