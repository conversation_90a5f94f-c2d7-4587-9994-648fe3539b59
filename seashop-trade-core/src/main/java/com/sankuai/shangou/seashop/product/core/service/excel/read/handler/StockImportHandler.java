package com.sankuai.shangou.seashop.product.core.service.excel.read.handler;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.product.core.service.SkuStockService;
import com.sankuai.shangou.seashop.product.core.service.excel.read.BizTypeEnum;
import com.sankuai.shangou.seashop.product.core.service.excel.read.context.ProductImportAssist;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.StockImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper.StockImportWrapper;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskInfoBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateWayEnum;
import com.sankuai.shangou.seashop.product.thrift.core.helper.ParameterHelper;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 库存导入处理器
 *
 * <AUTHOR>
 * @date 2023/11/21 22:08
 */
@Component
@Slf4j
public class StockImportHandler extends ImportHandler<StockImportDto> {

    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuStockService skuStockService;


    @Override
    public BizType bizType() {
        return BizTypeEnum.STOCK_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<StockImportDto> importResult) {
        log.info("【库存导入】 参数校验");

        // 遍历出正确第一步检验通过的数据
        List<StockImportDto> dataList = importResult.getSuccessDataList();

        checkStock(dataList);
        buildErrMsg(dataList);
    }

    @Override
    public void saveImportData(List<StockImportDto> successList) {
        log.info("【库存导入】 保存数据");
        StockTaskBo taskBo = new StockTaskBo();
        Long shopId = ProductImportAssist.getShopIdOrThrow();
        taskBo.setBizCode(IdUtil.fastUUID());
        taskBo.setUpdateType(StockUpdateTypeEnum.EXCEL_IMPORT_UPDATE);
        taskBo.setUpdateWay(StockUpdateWayEnum.COVER);
        taskBo.setUpdateKey(StockUpdateKeyEnum.SKU_AUTO_ID);
        List<StockTaskInfoBo> taskInfoBoList = JsonUtil.copyList(successList, StockTaskInfoBo.class, (source, target) -> target.setShopId(shopId));
        taskBo.setTaskInfoBoList(taskInfoBoList);
        skuStockService.asyncChangeSkuStock(taskBo);
    }

    @Override
    public DataWrapper<StockImportDto> wrapData(List<StockImportDto> errList) {
        return new StockImportWrapper(errList);
    }

    /**
     * 校验库存导入参数
     *
     * @param dataList
     */
    private void checkStock(List<StockImportDto> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        List<Long> skuAutoIds = dataList.stream().map(item -> Long.parseLong(item.getSkuAutoId())).collect(Collectors.toList());
        Map<Long, Sku> skuMap = skuRepository.getSkuMap(skuAutoIds);
        Long curShopId = ProductImportAssist.getShopIdOrThrow();

        dataList.forEach(item -> {
            Sku sku = skuMap.get(Long.parseLong(item.getSkuAutoId()));
            if (sku == null) {
                item.getErrBuilder().append("规格ID不存在;");
                return;
            }
            if (!curShopId.equals(sku.getShopId())) {
                item.getErrBuilder().append("该规格不属于当前店铺;");
                return;
            }

            if (!ParameterHelper.checkStock(Long.parseLong(item.getStock()))) {
                item.getErrBuilder().append(String.format("库存取值范围为%s-%s;", ParameterConstant.MIN_STOCK, ParameterConstant.MAX_STOCK));
            }
            item.setProductId(sku.getProductId());
        });
    }

    /**
     * 构建错误信息
     *
     * @param productList 导入的数据
     */
    private void buildErrMsg(List<StockImportDto> productList) {
        productList.forEach(product -> {
            String errMsg = StringUtils.defaultString(product.getErrMsg(), StrUtil.EMPTY);
            product.setErrMsg(errMsg + product.getErrBuilder().toString());
        });
    }
}
