package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.handler;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.AbsActivityQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 限时购活动处理器
 *
 * <AUTHOR>
 * @date 2023/12/23 9:42
 */
@Component
@Slf4j
public class FlashSaleQueryHandler extends AbsActivityQueryHandler {

    @Resource
    private PromotionRemoteService promotionRemoteService;

    @Override
    protected void query(ActivityContext context) {
        log.info("查询限时购活动, context: {}", context);
        RemoteFlashSaleBo flashSale = promotionRemoteService.getFlashSale(context.getProductId(), context.getShopId());
        context.setFlashSale(flashSale);
        log.info("查询限时购活动结果, context: {}, activity: {}", context, JsonUtil.toJsonString(context.getFlashSale()));
    }

    @Override
    protected Class<? extends AbsActivityQueryHandler> nextHandler() {
        return ExclusivePriceQueryHandler.class;
    }

    /**
     * 限时购优先级最高, 所以无论如何都会执行
     *
     * @param context 活动上下文对象
     * @return true 支持，false 不支持
     */
    @Override
    protected boolean support(ActivityContext context) {
        return true;
    }
}
