package com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog;


import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.promotion.core.service.assist.bo.SaveCouponLogBo;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.bo.EndFullReduceLogBo;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.bo.FlashSaleAuditLogBo;
import com.sankuai.shangou.seashop.promotion.dao.core.model.AdvanceDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.SendCouponCmdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/04/11 17:18
 */
@Component
@Slf4j
public class PromotionLogAssist {

    @Resource
    private BaseLogAssist baseLogAssist;

    /**
     * 记录修改优惠券
     */
    public void recordCouponLog(Long userId,
                                Long shopId,
                                SaveCouponLogBo oldCouponBo,
                                SaveCouponLogBo newCouponBo) {
        try {
            boolean isEdit = oldCouponBo != null;

            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, isEdit ? ExaProEnum.MODIFY : ExaProEnum.INSERT,
                isEdit ? "修改优惠券" : "新增优惠券",
                userId, shopId, dealSaveCouponLog(oldCouponBo), dealSaveCouponLog(newCouponBo));
        }
        catch (Exception e) {
            log.warn("记录优惠券日志失败", e);
        }
    }

    private SaveCouponLogBo dealSaveCouponLog(SaveCouponLogBo logBo) {
        if (logBo == null) {
            return null;
        }
        if (logBo.getUseArea() == 0) {
            logBo.setProductIdList(null);
        }
        return logBo;
    }

    public void recordFlashSaleLog(Long userId,
                                   Long shopId,
                                   FlashSaleResp oldFlashSale,
                                   FlashSaleResp newFlashSale) {
        try {
            boolean isEdit = oldFlashSale != null;

            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, isEdit ? ExaProEnum.MODIFY : ExaProEnum.INSERT,
                isEdit ? "修改限时抢购" : "新增限时抢购",
                userId, shopId, oldFlashSale, newFlashSale);

        }
        catch (Exception e) {
            log.warn("记录限时抢购日志失败", e);
        }
    }

    public void recordSendCouponLog(Long userId,
                                    Long shopId,
                                    SendCouponCmdReq sendCouponCmdReq) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY,
                "发放优惠券",
                userId, shopId, null, sendCouponCmdReq);
        }
        catch (Exception e) {
            log.warn("记录发放优惠券日志失败", e);
        }
    }

    public void recordEndCouponLog(BaseIdReq baseIdReq) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY,
                "结束优惠券活动",
                baseIdReq.getOperationUserId(), baseIdReq.getOperationShopId(), null, baseIdReq);
        }
        catch (Exception e) {
            log.warn("记录结束优惠券活动日志失败", e);
        }
    }

    public void recordDiscountActiveLog(Long userId,
                                        Long shopId,
                                        DiscountActiveResp oldDiscountActive,
                                        DiscountActiveResp newDiscountActive) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY,
                oldDiscountActive == null ? "新增折扣活动" : "修改折扣活动",
                userId, shopId, oldDiscountActive, newDiscountActive);
        }
        catch (Exception e) {
            log.warn("记录优惠活动日志失败", e);
        }
    }

    public void recordEndDiscountActiveLog(BaseIdReq baseIdReq) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY,
                "结束折扣活动",
                baseIdReq.getOperationUserId(), baseIdReq.getOperationShopId(), null, baseIdReq);
        }
        catch (Exception e) {
            log.warn("记录结束折扣活动日志失败", e);
        }
    }

    public void recordExclusivePriceLog(Long userId,
                                        Long shopId,
                                        ExclusivePriceResp oldExclusivePrice,
                                        ExclusivePriceResp newExclusivePrice) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY,
                oldExclusivePrice == null ? "新增专享价活动" : "修改专享价活动",
                userId, shopId, oldExclusivePrice, newExclusivePrice);
        }
        catch (Exception e) {
            log.warn("记录专享价活动失败", e);
        }
    }

    public void recordEndExclusivePriceLog(BaseIdReq baseIdReq) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY,
                "结束专享价活动",
                baseIdReq.getOperationUserId(), baseIdReq.getOperationShopId(), null, baseIdReq);
        }
        catch (Exception e) {
            log.warn("记录结束专享价活动日志失败", e);
        }
    }

    public void recordSaveAdvanceLog(Long userId,
                                     Long shopId,
                                     AdvanceDto oldAdvanceModel,
                                     AdvanceDto newAdvanceModel) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, oldAdvanceModel == null ? ExaProEnum.INSERT : ExaProEnum.MODIFY,
                oldAdvanceModel == null ? "新增弹窗广告" : "编辑弹窗广告",
                userId, shopId, oldAdvanceModel, newAdvanceModel);
        }
        catch (Exception e) {
            log.warn("记录保存弹框广告日志失败", e);
        }
    }

    public void recordEndFullReductionLog(Long userId,
                                          Long shopId,
                                          EndFullReduceLogBo endFullReduceLogBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY, "结束满减活动",
                userId, shopId, null, endFullReduceLogBo);
        }
        catch (Exception e) {
            log.warn("记录结束满减活动失败", e);
        }
    }

    public void recordSaveFullReductionLog(Long operationUserId,
                                           Long operationShopId,
                                           FullReductionDto oldFullReduction,
                                           FullReductionDto newFullReduction) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, oldFullReduction == null ? ExaProEnum.INSERT : ExaProEnum.MODIFY,
                oldFullReduction == null ? "新增满减活动" : "编辑满减活动",
                operationUserId, operationShopId, oldFullReduction, newFullReduction);
        }
        catch (Exception e) {
            log.warn("记录保存满减活动日志失败", e);
        }
    }

    public void recordAuditFlashSaleLog(Long operationUserId,
                                        Long operationShopId,
                                        FlashSaleAuditLogBo flashSaleAuditReq) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY, "审核限时购",
                operationUserId, operationShopId, null, flashSaleAuditReq);
        }
        catch (Exception e) {
            log.warn("记录审核限时购日志失败", e);
        }
    }
}
