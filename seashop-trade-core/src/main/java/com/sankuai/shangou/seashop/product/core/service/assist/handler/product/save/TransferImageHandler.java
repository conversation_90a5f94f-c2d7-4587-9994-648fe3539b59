package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.net.URI;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.remote.base.RemoteSiteSettingService;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.hepler.VenusHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 转移图片处理器(主要针对易久批保存图片时保存的是外部图片，该处理器用于将外部图片转移为内部图片)
 *
 * <AUTHOR>
 * @date 2024/02/20 9:10
 */
@Component
@Slf4j
public class TransferImageHandler extends AbsProductHandler {

    @Resource
    private VenusHelper venusHelper;
    @Resource
    private RemoteSiteSettingService remoteSiteSettingService;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】转移图片处理器【start】, context:{}", context);

        ProductBo saveProductBo = context.getSaveProductBo();
        setDefaultImage(context);
        transferProductImage(saveProductBo);
        transferSkuImage(saveProductBo);

        log.info("【保存商品】转移图片处理器【end】, context:{}", context);
    }

    private void setDefaultImage(ProductContext context) {
        if (context.isPartSave()) {
            return;
        }

        ProductBo productBo = context.getSaveProductBo();
        List<String> imageList = productBo.getImageList();
        if (CollectionUtils.isEmpty(imageList)) {
            String image = remoteSiteSettingService.getSetting(CommonConstant.DEFAULT_IMAGE_SETTING_KEY, CommonConstant.DEFAULT_IMAGE);
            productBo.setImageList(Arrays.asList(image));
        }
    }

    /**
     * 转移商品图片
     *
     * @param productBo
     */
    private void transferProductImage(ProductBo productBo) {
        List<String> imageList = productBo.getImageList();
        if (CollectionUtils.isEmpty(imageList)) {
            return;
        }

        List<String> transferImageList = getSupportTransferImageList(productBo.getImageList());
        Map<String, String> imageMap = venusHelper.uploadRemoteImages(transferImageList);

        for (int i = 0 ; i < imageList.size() ; i++) {
            String image = imageList.get(i);
            imageList.set(i, imageMap.getOrDefault(image, image));
        }
        productBo.setImagePath(imageList.get(0));
    }

    /**
     * 转移规格图片
     *
     * @param productBo
     */
    private void transferSkuImage(ProductBo productBo) {
        List<ProductSkuBo> skuList = productBo.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        List<String> imageList = skuList.stream().map(ProductSkuBo::getShowPic).filter(this::supportTransfer).collect(Collectors.toList());
        imageList = getSupportTransferImageList(imageList);

        Map<String, String> imageMap = venusHelper.uploadRemoteImages(imageList);
        skuList.forEach(sku -> {
            String showPic = imageMap.getOrDefault(sku.getShowPic(), sku.getShowPic());
            sku.setShowPic(showPic);
        });
    }

    private List<String> getSupportTransferImageList(List<String> imageList) {
        if (CollectionUtils.isEmpty(imageList)) {
            return Collections.EMPTY_LIST;
        }

        return imageList.stream().filter(this::supportTransfer).collect(Collectors.toList());
    }


    private boolean supportTransfer(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }

        return URI.create(url).getHost() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.TRANSFER_IMAGE;
    }
}
