package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.submit;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.converter.SkuConverter;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockAuditRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 提交sku审核
 *
 * <AUTHOR>
 * @date 2023/11/17 13:40
 */
@Component
@Slf4j
public class SubmitSkuAuditHandler extends AbsSubmitProductAuditHandler {

    @Resource
    private SkuAuditRepository skuAuditRepository;
    @Resource
    private SkuStockAuditRepository skuStockAuditRepository;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        log.info("【商品提交审核】保存商品sku审核记录【start】, productId: {}", productId);

        ProductBo auditProductBo = context.getAuditProductBo();
        List<ProductSkuBo> saveSkuList = auditProductBo.getSkuList();

        List<Sku> skuList = SkuConverter.convertToSku(saveSkuList, context.getProductId(), context.getShopId());
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        List<SkuAudit> skuAuditList = JsonUtil.copyList(skuList, SkuAudit.class);
        skuAuditRepository.saveBatch(skuAuditList);

        Map<String, SkuAudit> skuMap = skuAuditList.stream().collect(Collectors.toMap(SkuAudit::getSkuId, Function.identity(), (k1, k2) -> k2));
        // 保存库存信息
        List<SkuStockAudit> skuStockList = JsonUtil.copyList(saveSkuList, SkuStockAudit.class);
        skuStockList.forEach(skuStock -> {
            SkuAudit sku = skuMap.get(skuStock.getSkuId());
            skuStock.setProductId(sku.getProductId());
            skuStock.setSkuId(sku.getSkuId());
            skuStock.setSkuAutoId(sku.getId());
            skuStock.setShopId(context.getShopId());
        });
        skuStockAuditRepository.saveBatch(skuStockList);

        log.info("【商品提交审核】保存商品sku审核记录【end】, productId: {}", productId);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SUBMIT_SKU_AUDIT;
    }

}
