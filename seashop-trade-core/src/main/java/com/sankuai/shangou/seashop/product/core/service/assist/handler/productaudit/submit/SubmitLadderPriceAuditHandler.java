package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.submit;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPrice;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPriceAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductLadderPriceAuditRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 提交阶梯价审核
 *
 * <AUTHOR>
 * @date 2023/11/17 13:40
 */
@Component
@Slf4j
@Order(5)
public class SubmitLadderPriceAuditHandler extends AbsSubmitProductAuditHandler {

    @Resource
    private ProductLadderPriceAuditRepository productLadderPriceAuditRepository;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        log.info("【商品提交审核】保存阶梯价审核记录【start】, productId: {}", productId);

        ProductBo auditProductBo = context.getAuditProductBo();
        List<ProductLadderPriceBo> newLadderPriceList = auditProductBo.getLadderPriceList();
        if (auditProductBo.getWhetherOpenLadder()!= null && auditProductBo.getWhetherOpenLadder() && !CollectionUtils.isEmpty(newLadderPriceList)) {
            List<ProductLadderPrice> ladderPriceList = JsonUtil.copyList(newLadderPriceList, ProductLadderPrice.class);
            ladderPriceList.forEach(ladderPrice -> ladderPrice.setProductId(context.getProductId()));
            productLadderPriceAuditRepository.saveBatch(JsonUtil.copyList(ladderPriceList, ProductLadderPriceAudit.class));
        }

        log.info("【商品提交审核】保存阶梯价审核记录【end】, productId: {}", productId);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SUBMIT_LADDER_PRICE_AUDIT;
    }


}
