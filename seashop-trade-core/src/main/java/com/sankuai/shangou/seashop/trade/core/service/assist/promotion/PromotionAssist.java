package com.sankuai.shangou.seashop.trade.core.service.assist.promotion;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountRuleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteExclusivePriceBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteReductionBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopExclusivePriceBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopUserPromotionBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.DataProcessorAssist;
import com.sankuai.shangou.seashop.trade.core.service.model.AddonProductContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.AddonSummaryBo;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.AddonProductPromotionTabEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PromotionAssist {

    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private DataProcessorAssist dataProcessorAssist;

    public Map<Long, BigDecimal> flatMinExclusivePrice(RemoteShopUserPromotionBo shopPromotion) {
        if (shopPromotion == null) {
            return Collections.emptyMap();
        }
        List<RemoteShopExclusivePriceBo> exclusiveList = shopPromotion.getShopExclusivePriceList();
        if (CollUtil.isEmpty(exclusiveList)) {
            return Collections.emptyMap();
        }
        // 平铺店铺商品的专享价，并转换成map结构，key为productId，value为价格，多sku的，取价格的最小值
        Map<Long, BigDecimal> minExclusivePriceMap = new HashMap<>(exclusiveList.size());
        // 双重遍历，多个活动，每个活动多个商品
        for (RemoteShopExclusivePriceBo exclusivePriceDto : exclusiveList) {
            List<RemoteExclusivePriceBo> productList = exclusivePriceDto.getProductList();
            if (CollUtil.isEmpty(productList)) {
                continue;
            }
            for (RemoteExclusivePriceBo productExclusivePriceDto : productList) {
                Long productId = productExclusivePriceDto.getProductId();
                BigDecimal exclusivePrice = productExclusivePriceDto.getPrice();

                if (minExclusivePriceMap.containsKey(productId)) {
                    BigDecimal oldPrice = minExclusivePriceMap.get(productId);
                    if (exclusivePrice.compareTo(oldPrice) < 0) {
                        minExclusivePriceMap.put(productId, exclusivePrice);
                    }
                } else {
                    minExclusivePriceMap.put(productId, exclusivePrice);
                }
            }
        }
        return minExclusivePriceMap;
    }


    public Map<Long, RemoteDiscountRuleBo> findProductDiscount(RemoteShopUserPromotionBo shopPromotion, BigDecimal productAmount) {
        if (shopPromotion == null) {
            return Collections.emptyMap();
        }
        List<RemoteDiscountBo> shopDiscountList = shopPromotion.getShopDiscountList();
        // 如果没有折扣活动，直接返回
        if (CollUtil.isEmpty(shopDiscountList)) {
            return Collections.emptyMap();
        }
        Map<Long, RemoteDiscountRuleBo> productDiscountMap = new HashMap<>();
        // 理论上如果有活动适应于所有商品，则就只会有一个活动
        RemoteDiscountBo firstDiscount;
        if (shopDiscountList.size() == 1 && (firstDiscount = shopDiscountList.get(0)).getIzAllProduct()) {
            List<RemoteDiscountRuleBo> ruleList = firstDiscount.getRuleList();
            // 找到满足条件的折扣最大值
            RemoteDiscountRuleBo matchRule = ruleList.stream()
                .sorted(Comparator.comparing(RemoteDiscountRuleBo::getQuota).reversed())
                .filter(rule -> productAmount.compareTo(rule.getQuota()) >= 0)
                .findFirst()
                .map(rule -> {
                    RemoteDiscountRuleBo discountBo = new RemoteDiscountRuleBo();
                    discountBo.setId(rule.getId());
                    discountBo.setQuota(rule.getQuota());
                    discountBo.setDiscount(rule.getDiscount());
                    return discountBo;
                })
                .orElse(null);
            productDiscountMap.put(CommonConst.ALL_PRODUCT_ID, matchRule);
            return productDiscountMap;
        }
        // 多个折扣活动之间的商品不会重复，可以直接遍历折扣活动
        shopDiscountList.forEach(dis -> {
            List<Long> productIdList = dis.getProductIdList();
            RemoteDiscountRuleBo matchRule = dis.getRuleList().stream()
                .sorted(Comparator.comparing(RemoteDiscountRuleBo::getQuota).reversed())
                .filter(rule -> productAmount.compareTo(rule.getQuota()) >= 0)
                .findFirst()
                .map(rule -> {
                    RemoteDiscountRuleBo discountBo = new RemoteDiscountRuleBo();
                    discountBo.setId(rule.getId());
                    discountBo.setQuota(rule.getQuota());
                    discountBo.setDiscount(rule.getDiscount());
                    return discountBo;
                })
                .orElse(null);
            productIdList.forEach(productId -> productDiscountMap.put(productId, matchRule));
        });
        return productDiscountMap;
    }


    public AddonSummaryBo buildAddonSummary(Integer tab, AddonProductContext context) {
        // 获取店铺营销
        RemoteShopUserPromotionBo shopPromotion = getShopPromotion(context.getShopId(), context.getUserId());
        if (AddonProductPromotionTabEnum.REDUCTION.getCode().equals(tab)) {
            AssertUtil.throwIfNull(shopPromotion.getShopReduction(), "店铺没有匹配的满减活动");
            return buildReductionSummary(context, shopPromotion.getShopReduction());
        }
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(shopPromotion.getShopDiscountList()), "店铺没有匹配的折扣活动");
        RemoteDiscountBo shopDiscount = findTheDiscount(context.getActivityId(), shopPromotion.getShopDiscountList());
        return buildDiscountSummary(context, shopDiscount);
    }


    private AddonSummaryBo buildReductionSummary(AddonProductContext context, RemoteReductionBo shopReduction) {
        int selectedProductCount = context.getSelectedProductCount();
        BigDecimal selectedProductAmount = cn.hutool.core.util.NumberUtil.nullToZero(context.getSelectedProductAmount());
        // 满减的活动中，专享价不计算，所以总额不一定是勾选的商品的总金额
        BigDecimal shopAmount = dataProcessorAssist.calculateSkuTotalAmountBesideExclusive(context.getProductList());
        boolean isOverlay = Boolean.TRUE.equals(shopReduction.getMoneyOffOverLay());
        // 对于满减，不需要指定商品，所以根据购物车勾选情况计算
        AddonSummaryBo temp = new AddonSummaryBo();
        temp.setSelectedInActiveCount(selectedProductCount);
        temp.setSelectedProductAmount(selectedProductAmount);
        temp.setActivityDesc(shopReduction.getActiveName());

        long deadline = DateUtil.between(new Date(), shopReduction.getEndTime(), DateUnit.MS);
        String formatDiff = DateUtil.formatBetween(deadline, BetweenFormatter.Level.MINUTE);
        temp.setDeadlineDesc(formatDiff);
        temp.setDeadline(deadline);

        BigDecimal condition = shopReduction.getMoneyOffCondition();
        BigDecimal moneyOffFee = shopReduction.getMoneyOffFee();
        // 商品金额比满减门槛小，不管是不是叠加，提示第一层满减，提示 差xx元可减xx
        if (shopAmount.compareTo(condition) < 0) {
            log.info("商品金额比满减门槛小，不管是不是叠加，提示第一层满减，提示 差xx元可减xx");
            BigDecimal diff = condition.subtract(shopAmount);
            String desc = String.format(CommonConst.ADDON_REDUCTION_DESC_DEFAULT, diff, moneyOffFee);
            temp.setMatchPromotionDesc(desc);
        } else if (!isOverlay) {
            log.info("不叠加，也只需要判断第一层满减");
            // 不叠加，也只需要判断第一层满减
            // 商品金额比满减门槛大，提示 已购满X元，已减X元，再凑X元可减X元
            String desc = String.format(CommonConst.ADDON_REDUCTION_DESC_NEXT, condition, moneyOffFee);
            temp.setMatchPromotionDesc(desc);
            temp.setSelectedProductAmount(shopAmount.subtract(moneyOffFee));
        } else {
            log.info("满减叠加计算");
            // 计算当前金额满足的叠加次数
            int reductionCount = shopAmount.divideToIntegralValue(condition).intValue();
            BigDecimal haveBuyAmount = cn.hutool.core.util.NumberUtil.mul(condition, reductionCount);
            BigDecimal haveReductionAmount = cn.hutool.core.util.NumberUtil.mul(moneyOffFee, reductionCount);
            // 当前金额距下一个满减条件的差额
            BigDecimal diff = cn.hutool.core.util.NumberUtil.mul(condition, (reductionCount + 1)).subtract(shopAmount).setScale(2, RoundingMode.HALF_UP);
            // 下一个满减的优惠金额
            BigDecimal nextRed = cn.hutool.core.util.NumberUtil.mul(moneyOffFee, reductionCount + 1);
            String desc = String.format(CommonConst.ADDON_REDUCTION_DESC_NEXT_OVERLAY,
                haveBuyAmount, haveReductionAmount, diff, nextRed);
            temp.setMatchPromotionDesc(desc);
            temp.setSelectedProductAmount(shopAmount.subtract(haveReductionAmount));
        }
        return temp;
    }

    private AddonSummaryBo buildDiscountSummary(AddonProductContext context, RemoteDiscountBo shopDiscount) {
        log.info("【凑单商品列表】折扣活动为: {}", JsonUtil.toJsonString(shopDiscount));
        // 获取满足条件的最大折扣(数值最小)
        DiscountRuleHolder ruleHolder = findMaxDiscountRule(context, shopDiscount);
        log.info("【凑单商品列表】折扣活动最大折扣为: {}", JsonUtil.toJsonString(ruleHolder));
        // 构建汇总信息
        AddonSummaryBo summary = buildAddonMaxDiscountSummary(context, shopDiscount, ruleHolder);
        log.info("【凑单商品列表】折扣汇总信息为: {}", JsonUtil.toJsonString(summary));
        return summary;
    }

    private DiscountRuleHolder findMaxDiscountRule(AddonProductContext context, RemoteDiscountBo shopDiscount) {
        // 活动中，专享价不计算，所以总额不一定是勾选的商品的总金额
        List<ShopProductBo> shopSkuList = context.getProductList();
        BigDecimal totalAmount = dataProcessorAssist.calculateSkuTotalAmountBesideExclusive(shopSkuList);
        // 下标遍历，因为需要判断下一个层级
        DiscountRuleHolder ruleHolder = null;
        List<RemoteDiscountRuleBo> ruleList = shopDiscount.getRuleList();
        // 根据折扣排序，取门槛最高的，理论上门槛越高折扣力度越大(数值越小)
        ruleList.sort(Comparator.comparing(RemoteDiscountRuleBo::getQuota).reversed());
        log.info("【凑单商品列表】折扣活动排序后为: {}", JsonUtil.toJsonString(ruleList));
        // 显示重新设置，buildSummary使用
        shopDiscount.setRuleList(ruleList);
        // 是否所有商品适用
        if (Boolean.TRUE.equals(shopDiscount.getIzAllProduct())) {
            // 找到满足条件的折扣最大值
            ruleHolder = findMaxDiscountRule(ruleList, totalAmount);
            ruleHolder.setMatchProductAmount(totalAmount);
            ruleHolder.setMatchProductCount(context.getSelectedProductCount());
        } else {
            // 如果是针对部分商品的，则需要根据折扣的商品情况，分别汇总店铺商品的金额，再判断折扣
            // 折扣是到商品维度的，同一个商品ID可以有多个sku
            Map<Long, List<ShopProductBo>> shopSkuMap = null;
            if (CollUtil.isNotEmpty(shopSkuList)) {
                shopSkuMap = shopSkuList.stream()
                    .filter(sku -> Boolean.TRUE.equals(sku.getWhetherSelected()))
                    // 排除专享价商品
                    .filter(sku -> !Boolean.TRUE.equals(sku.getWhetherExclusive()))
                    .collect(Collectors.groupingBy(ShopProductBo::getProductId));
            } else {
                shopSkuMap = Collections.emptyMap();
            }
            List<Long> productIdList = shopDiscount.getProductIdList();
            // 计算当前折扣活动适用商品的总金额
            /*BigDecimal productTotalAmount = productIdList.stream()
                    .map(shopSkuMap::get)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .map(ShopProductBo::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);*/
            int matchProductCount = 0;
            BigDecimal productTotalAmount = BigDecimal.ZERO;
            for (Long pid : productIdList) {
                List<ShopProductBo> pList = shopSkuMap.get(pid);
                if (CollUtil.isEmpty(pList)) {
                    continue;
                }
                for (ShopProductBo p : pList) {
                    productTotalAmount = productTotalAmount.add(p.getTotalAmount());
                    matchProductCount = matchProductCount + 1;
                }
            }
            log.info("【凑单商品列表】折扣活动适用商品总金额为: {}", productTotalAmount);
            // 找到满足条件的折扣最大值
            ruleHolder = findMaxDiscountRule(ruleList, productTotalAmount);
            ruleHolder.setProductIdList(productIdList);
            ruleHolder.setMatchProductAmount(productTotalAmount);
            ruleHolder.setMatchProductCount(matchProductCount);
        }
        return ruleHolder;
    }

    private DiscountRuleHolder findMaxDiscountRule(List<RemoteDiscountRuleBo> ruleList, BigDecimal productTotalAmount) {
        for (int i = 0; i < ruleList.size(); i++) {
            RemoteDiscountRuleBo rule = ruleList.get(i);
            if (productTotalAmount.compareTo(rule.getQuota()) >= 0) {
                return new DiscountRuleHolder(rule, i);
            }
        }
        // 如果没有任何一个匹配，说明没有达到折扣规则，取最后要给，即最小折扣，用于后续提示
        return new DiscountRuleHolder(ruleList.get(ruleList.size() - 1), -1);
    }

    private AddonSummaryBo buildAddonMaxDiscountSummary(AddonProductContext context, RemoteDiscountBo shopDiscount,
                                                        DiscountRuleHolder ruleHolder) {
        int selectedProductCount = ruleHolder.getMatchProductCount();
        // 显示当前选择的折扣活动匹配的商品的总金额，会排除专享价商品
        BigDecimal selectedProductAmount = ruleHolder.getMatchProductAmount();

        AddonSummaryBo temp = new AddonSummaryBo();
        temp.setSelectedInActiveCount(selectedProductCount);
        temp.setActivityDesc(shopDiscount.getDiscountActName());
        long deadline = DateUtil.between(new Date(), shopDiscount.getEndTime(), DateUnit.MS);
        String formatDiff = DateUtil.formatBetween(deadline, BetweenFormatter.Level.MINUTE);
        temp.setDeadlineDesc(formatDiff);
        temp.setDeadline(deadline);
        temp.setSelectedProductAmount(selectedProductAmount);

        RemoteDiscountRuleBo matchRule = null;
        List<RemoteDiscountRuleBo> ruleList = shopDiscount.getRuleList();
        // 已购满X元，享X折优惠，再凑X元可享X元优惠，立减X元
        if (ruleHolder != null && (matchRule = ruleHolder.getMatchRule()) != null) {
            BigDecimal quota = matchRule.getQuota();
            int matchIndex = ruleHolder.getMatchIndex();
            // matchIndex 说明没有达到折扣起点
            if (matchIndex == -1) {
                BigDecimal diff = quota.subtract(selectedProductAmount);
                BigDecimal discount = cn.hutool.core.util.NumberUtil.div(matchRule.getDiscount(), CommonConst.TEN, 2);
                BigDecimal discountAmount = quota.subtract(cn.hutool.core.util.NumberUtil.mul(discount, quota)).setScale(2, RoundingMode.HALF_UP);
                String desc = String.format(CommonConst.ADDON_DISCOUNT_DESC_DEFAULT, diff, matchRule.getDiscount(), discountAmount);
                temp.setMatchPromotionDesc(desc);
            }
            else if (matchIndex == 0) {
                // 如果是第一层，代表是最大折扣了，不需要提示再凑多少元
                String desc = String.format(CommonConst.ADDON_DISCOUNT_DESC_LAST, quota, matchRule.getDiscount());
                temp.setMatchPromotionDesc(desc);

                // 满足了折扣，需要重新根据折扣计算总金额
                reCalculateSelectedAmountForAddonList(context, ruleHolder, temp);
            } else {
                // 如果不是最后一层，需要提示再凑多少元
                RemoteDiscountRuleBo nextRule = ruleList.get(matchIndex - 1);
                BigDecimal nextQuota = nextRule.getQuota();
                BigDecimal nextQuotaDiff = nextQuota.subtract(selectedProductAmount);
                BigDecimal discount = cn.hutool.core.util.NumberUtil.div(nextRule.getDiscount(), CommonConst.TEN, 2);
                BigDecimal nextDiscountAmount = cn.hutool.core.util.NumberUtil.sub(nextRule.getQuota(), cn.hutool.core.util.NumberUtil.mul(nextRule.getQuota(), discount)).setScale(2, RoundingMode.HALF_UP);
                String desc = String.format(CommonConst.ADDON_DISCOUNT_DESC_NEXT,
                    quota, matchRule.getDiscount(), nextQuotaDiff, nextRule.getDiscount(), nextDiscountAmount);
                temp.setMatchPromotionDesc(desc);

                // 满足了折扣，需要重新根据折扣计算总金额
                reCalculateSelectedAmountForAddonList(context, ruleHolder, temp);
            }
        }
        return temp;
    }

    private void reCalculateSelectedAmountForAddonList(AddonProductContext context, DiscountRuleHolder ruleHolder, AddonSummaryBo summary) {
        // 处理并计算折扣价格
        List<ShopProductBo> shopSkuList = context.getProductList();
        RemoteDiscountRuleBo matchRule = ruleHolder.getMatchRule();

        BigDecimal totalAmount = BigDecimal.ZERO;
        if (CollUtil.isEmpty(shopSkuList)) {
            summary.setSelectedProductAmount(totalAmount);
            return;
        }
        // 重置商品的实际售价
        BigDecimal discount = cn.hutool.core.util.NumberUtil.div(matchRule.getDiscount(), CommonConst.TEN, 2);
        // ID没有，说明是全部商品适用
        if (CollUtil.isEmpty(ruleHolder.getProductIdList())) {
            log.info("凑单全商品适用，重新计算折扣价格");
            for (ShopProductBo sku : shopSkuList) {
                if (!Boolean.TRUE.equals(sku.getWhetherSelected())) {
                    continue;
                }
                BigDecimal discountSalePrice = cn.hutool.core.util.NumberUtil.mul(sku.getFinalSalePrice(), discount).setScale(2, RoundingMode.HALF_UP);
                sku.setFinalSalePrice(discountSalePrice);
                sku.setDiscountSalePrice(discountSalePrice);
                BigDecimal amount = NumberUtil.mul(discountSalePrice, sku.getQuantity()).setScale(2, RoundingMode.HALF_UP);
                totalAmount = totalAmount.add(amount);
            }
        } else {
            Map<Long, Long> discountProductIdMap = ruleHolder.getProductIdList().stream()
                .collect(Collectors.toMap(id -> id, id -> id, (oldV, newV) -> newV));
            for (ShopProductBo sku : shopSkuList) {
                if (!Boolean.TRUE.equals(sku.getWhetherSelected())) {
                    continue;
                }
                // 只有满足当前折扣活动的商品才需要重新计算金额，否则过滤
                if (!discountProductIdMap.containsKey(sku.getProductId())) {
                    continue;
                }
                BigDecimal discountSalePrice = cn.hutool.core.util.NumberUtil.mul(sku.getFinalSalePrice(), discount).setScale(2, RoundingMode.HALF_UP);
                sku.setFinalSalePrice(discountSalePrice);
                sku.setDiscountSalePrice(discountSalePrice);
                BigDecimal amount = NumberUtil.mul(discountSalePrice, sku.getQuantity()).setScale(2, RoundingMode.HALF_UP);
                totalAmount = totalAmount.add(amount);
            }
        }
        // 重置总金额
        summary.setSelectedProductAmount(totalAmount);
    }

    static class DiscountRuleHolder {
        private RemoteDiscountRuleBo matchRule;
        private int matchIndex;
        private List<Long> productIdList;
        /**
         * 符合折扣活动的商品总金额。去除专享价，如果是部分商品适用，则是部分商品的总金额
         */
        private BigDecimal matchProductAmount;
        private Integer matchProductCount;

        public DiscountRuleHolder(RemoteDiscountRuleBo matchRule, int matchIndex) {
            this.matchRule = matchRule;
            this.matchIndex = matchIndex;
        }
        public DiscountRuleHolder(RemoteDiscountRuleBo matchRule, int matchIndex, List<Long> productIdList) {
            this.matchRule = matchRule;
            this.matchIndex = matchIndex;
            this.productIdList = productIdList;
        }

        public RemoteDiscountRuleBo getMatchRule() {
            return matchRule;
        }

        public void setMatchRule(RemoteDiscountRuleBo matchRule) {
            this.matchRule = matchRule;
        }

        public int getMatchIndex() {
            return matchIndex;
        }

        public void setMatchIndex(int matchIndex) {
            this.matchIndex = matchIndex;
        }

        public List<Long> getProductIdList() {
            return productIdList;
        }

        public void setProductIdList(List<Long> productIdList) {
            this.productIdList = productIdList;
        }

        public BigDecimal getMatchProductAmount() {
            return matchProductAmount;
        }

        public void setMatchProductAmount(BigDecimal matchProductAmount) {
            this.matchProductAmount = matchProductAmount;
        }

        public Integer getMatchProductCount() {
            return matchProductCount;
        }

        public void setMatchProductCount(Integer matchProductCount) {
            this.matchProductCount = matchProductCount;
        }
    }

    private RemoteShopUserPromotionBo getShopPromotion(Long shopId, Long userId) {
        List<RemoteShopUserPromotionBo> promotionList = promotionRemoteService.queryValidShopUserPromotion(Collections.singletonList(shopId), userId);
        Map<Long, RemoteShopUserPromotionBo> shopPromotionMap = promotionList.stream()
                .collect(Collectors.toMap(RemoteShopUserPromotionBo::getShopId, Function.identity(),(o1,
                                                                                                     o2)->o2));
        return shopPromotionMap.get(shopId);
    }

    private RemoteDiscountBo findTheDiscount(Long activityId, List<RemoteDiscountBo> shopDiscountList) {
        return shopDiscountList.stream()
                .filter(dis -> dis.getDiscountActId().equals(activityId))
                .findFirst()
                .orElseThrow(() -> new BusinessException("店铺折扣活动不存在"));

    }

}
