package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.handler;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.AbsActivityQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 阶梯价查询处理器
 * 阶梯价由商品详情中返回, 无需再次查询
 *
 * <AUTHOR>
 * @date 2023/12/23 10:26
 */
@Component
@Slf4j
public class LadderPriceQueryHandler extends AbsActivityQueryHandler {


    @Override
    protected void query(ActivityContext context) {
        log.info("查询阶梯价活动, context: {}", context);
    }

    @Override
    protected Class<? extends AbsActivityQueryHandler> nextHandler() {
        return CollocationBuyQueryHandler.class;
    }

    /**
     * 阶梯价与专享价、限时购、组合购互斥
     * 商品详情页面需要同时展示限时购购买和原价购买, 所以限时购不影响阶梯价
     *
     * @param context 活动上下文对象
     * @return true 支持，false 不支持
     */
    @Override
    protected boolean support(ActivityContext context) {
        return context.getExclusivePrice() == null
                // && context.getFlashSale() == null
                && context.getCollectionBuy() == null;
    }
}
