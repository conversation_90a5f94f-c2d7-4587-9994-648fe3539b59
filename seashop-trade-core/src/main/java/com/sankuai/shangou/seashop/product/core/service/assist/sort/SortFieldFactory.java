package com.sankuai.shangou.seashop.product.core.service.assist.sort;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;

/**
 * <AUTHOR>
 * @date 2024/03/09 13:36
 */
public class SortFieldFactory {

    public static <E extends Enum<E> & SortFieldConverter<E>> void dealSortField(Class<E> enumClass,
                                                                                 List<FieldSortReq> fieldList) {
        if (CollectionUtils.isEmpty(fieldList)) {
            return;
        }

        Map<String, String> enumMap = getEnumMap(enumClass);
        for (int i = fieldList.size() - 1; i >= 0; i--) {
            FieldSortReq fieldSortReq = fieldList.get(i);
            String target = enumMap.get(fieldSortReq.getSort());
            if (target == null) {
                fieldList.remove(i);
            } else {
                fieldSortReq.setSort(target);
            }
        }
    }

    private static <E extends Enum<E> & SortFieldConverter<E>> Map<String, String> getEnumMap(Class<E> enumClass) {
        E[] enumConstants = enumClass.getEnumConstants();
        if (enumConstants == null || enumConstants.length == 0) {
            throw new IllegalArgumentException("Invalid enum class provided");
        }
        return enumConstants[0].getMapping();
    }

}
