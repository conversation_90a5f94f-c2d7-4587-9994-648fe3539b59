package com.sankuai.shangou.seashop.product.core.mq.publisher;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.ProductChangeEvent;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/03 14:29
 */
@Service
@Slf4j
public class ProductChangePublisher {

    @Resource
    private DefaultRocketMq defaultRocketMq;

    public void sendMessage(List<ProductChangeEvent> msgList) {
        log.info("商品变动事件消息: data：{}", JsonUtil.toJsonString(msgList));
        try {
            List<String> formatMsgList = msgList.stream().map(msg -> JsonUtil.toJsonString(msg)).collect(Collectors.toList());
            // ProducerResult producerResult = producerProcessor.sendMessages(formatMsgList);
            for (String msg : formatMsgList) {
//                producerProcessor.sendDelayMessage(msg, CommonConstant.PRODUCT_EVENT_PUSH_DELAY_TIME);
                defaultRocketMq.syncSend(MafkaConstant.TOPIC_PRODUCT_CHANGE,msg);
            }
            log.info("商品变动事件消息: result：{}", JSONUtil.toJsonStr(msgList));
        }
        catch (Exception e) {
            log.error("商品变动事件消息: data：{}", JsonUtil.toJsonString(msgList), e);
        }
    }

}
