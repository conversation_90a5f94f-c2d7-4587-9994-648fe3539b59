package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.common.constant.CacheConstant;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductService;
import com.sankuai.shangou.seashop.product.core.service.BrandService;
import com.sankuai.shangou.seashop.product.core.service.hepler.PinYinHelper;
import com.sankuai.shangou.seashop.product.core.service.hepler.StringHelper;
import com.sankuai.shangou.seashop.product.core.service.model.BatchQueryBranchParamBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandCacheBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandGroupBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.dao.core.domain.BrandApply;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandApplyRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopBrandRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandDto;

import cn.hutool.core.util.ZipUtil;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:09
 */
@Service
public class BrandServiceImpl implements BrandService {

    @Resource
    private BrandRepository brandRepository;
    @Resource
    private BrandApplyRepository brandApplyRepository;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private EsProductService esProductService;
    @Resource
    private CategoryRepository categoryRepository;
    @Resource
    private ShopBrandRepository shopBrandRepository;
    @Resource
    private SquirrelUtil squirrelUtil;


    @Override
    public void createBrand(Brand brand) {
        String lock = LockConstant.SAVE_BRAND_LOCK + brand.getName();
        LockHelper.lock(lock, () -> {
            long count = brandRepository.count(new LambdaQueryWrapper<Brand>()
                    .eq(Brand::getName, brand.getName()).eq(Brand::getWhetherDelete, Boolean.FALSE));
            AssertUtil.throwIfTrue(count > 0, "已存在相同品牌");

            brandRepository.save(brand);

            // 清空缓存
            removeBrandCache();
        });
    }

    @Override
    public void updateBrand(Brand brand) {
        String lock = LockConstant.SAVE_BRAND_LOCK + brand.getName();
        LockHelper.lock(lock, () -> {
            Brand dbBrand = brandRepository.getById(brand.getId());
            AssertUtil.throwIfTrue(dbBrand == null || dbBrand.getWhetherDelete(), "品牌不存在");

            long count = brandRepository.count(new LambdaQueryWrapper<Brand>().ne(Brand::getId, brand.getId())
                    .eq(Brand::getName, brand.getName()).eq(Brand::getWhetherDelete, Boolean.FALSE));
            AssertUtil.throwIfTrue(count > 0, "已存在相同品牌");

            brandRepository.updateById(brand);

            // 更新品牌申请表的记录
            BrandApply apply = new BrandApply();
            apply.setBrandName(brand.getName());
            apply.setLogo(brand.getLogo());
            apply.setDescription(brand.getDescription());
            brandApplyRepository.update(apply, new LambdaQueryWrapper<BrandApply>().eq(BrandApply::getBrandId, brand.getId()));

            // 清空缓存
            removeBrandCache();
        });
    }

    @Override
    public void deleteBrand(Long id) {
        long count = productRepository.count(new LambdaQueryWrapper<Product>().eq(Product::getBrandId, id).eq(Product::getWhetherDelete,
            Boolean.FALSE));
        AssertUtil.throwIfTrue(count > 0, "品牌下有关联商品，不能删除");

        Brand brand = brandRepository.getById(id);
        AssertUtil.throwIfTrue(brand == null || brand.getWhetherDelete(), "品牌不存在");

        TransactionHelper.doInTransaction(() -> {
            brandRepository.logicDeleteById(id);

            // 删除名称相同的申请记录
            brandApplyRepository.logicDeleteByBrandName(brand.getName());

            // 清空缓存
            removeBrandCache();
        });
    }

    @Override
    public BasePageResp<Brand> pageBrand(BasePageParam page, BrandQueryBo brandQueryBo) {
        Page<Brand> pageResult = PageHelper.startPage(page);
        brandRepository.list(commonBrandWrapperBuilder(brandQueryBo));
        return PageResultHelper.transfer(pageResult, Brand.class);
    }

    @Override
    public List<BrandDto> queryBrandList(BatchQueryBranchParamBo paramBo) {
        List<Long> idList = paramBo.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.EMPTY_LIST;
        }

        if (paramBo.getUseCache() != null && paramBo.getUseCache()) {
            List<BrandCacheBo> cacheList = listBrandCache().stream()
                    .filter(brand -> idList.contains(brand.getId())).collect(Collectors.toList());
            return JsonUtil.copyList(cacheList, BrandDto.class);
        }

        List<Brand> brandList = brandRepository.getByIdList(paramBo.getIdList());
        return JsonUtil.copyList(brandList, BrandDto.class);
    }

    @Override
    public List<Brand> queryNotApplyBrand(Long shopId) {
        List<Integer> auditStatusList = Arrays.asList(BrandEnum.AuditStatusEnum.UNAUDITED.getCode());
        List<Long> brandIds = brandApplyRepository.getBrandIdsByShopId(shopId, auditStatusList);
        brandIds.addAll(shopBrandRepository.listBrandIdsByShopId(shopId));
        if (CollectionUtils.isEmpty(brandIds)) {
            return brandRepository.list(new LambdaQueryWrapper<Brand>().eq(Brand::getWhetherDelete, Boolean.FALSE));
        }

        return MybatisUtil.queryBatch(ids -> brandRepository
                .list(new LambdaQueryWrapper<Brand>().notIn(Brand::getId, ids).eq(Brand::getWhetherDelete, Boolean.FALSE)), brandIds);
    }

    @Override
    public List<BrandGroupBo> queryNotApplyBrandGroup(Long shopId) {
        List<Brand> brands = queryNotApplyBrand(shopId);
        return buildBrandGroup(brands);
    }

    @Override
    public Brand queryBrandDetail(Long id) {
        Brand brand = brandRepository.getById(id);
        AssertUtil.throwIfTrue(brand == null || brand.getWhetherDelete(), "品牌不存在");
        return brand;
    }

    @Override
    public List<Brand> queryRecommendBrand(Long categoryId) {
        if (categoryId == null) {
            return Collections.EMPTY_LIST;
        }

        Category category = categoryRepository.getById(categoryId);
        if (category == null) {
            return Collections.EMPTY_LIST;
        }

        List<Long> brandIds = esProductService.getBrandIds(category.getPath());
        if (CollectionUtils.isEmpty(brandIds)) {
            return Collections.EMPTY_LIST;
        }

        List<Brand> brandList = brandRepository.getByIdList(brandIds);
        brandList.sort(Comparator.comparing(Brand::getDisplaySequence));
        return brandList;
    }

    @Override
    public List<Long> queryBrandIdsByName(String brandName) {
        List<Brand> brands = brandRepository.list(new LambdaQueryWrapper<Brand>().like(Brand::getName, brandName).eq(Brand::getWhetherDelete, Boolean.FALSE));
        if (CollectionUtils.isEmpty(brands)) {
            return Collections.EMPTY_LIST;
        }
        return brands.stream().map(Brand::getId).collect(Collectors.toList());
    }

    @Override
    public List<BrandCacheBo> listBrandCache() {
        byte[] arr = (byte[]) squirrelUtil.get(CacheConstant.ALL_BRAND_CACHE);
        if (arr == null) {
            List<Brand> brandList = brandRepository.list(new LambdaQueryWrapper<Brand>().eq(Brand::getWhetherDelete, false));
            List<BrandCacheBo> cacheList = JsonUtil.copyList(brandList, BrandCacheBo.class);
            arr = ZipUtil.gzip(JsonUtil.toJsonString(cacheList), CommonConstant.DEFAULT_CHARSET);
            squirrelUtil.set(CacheConstant.ALL_BRAND_CACHE, arr, CacheConstant.BRAND_CACHE_TIME);
            return cacheList;
        }
        return JsonUtil.parseArray(ZipUtil.unGzip(arr, CommonConstant.DEFAULT_CHARSET), BrandCacheBo.class);
    }

    @Override
    public void removeBrandCache() {
        squirrelUtil.deleteKey(CacheConstant.ALL_BRAND_CACHE);
    }

    @Override
    public Brand queryByName(String name) {
        Brand brand = brandRepository.getOne(new LambdaQueryWrapper<Brand>().eq(Brand::getName, name).last("limit 1"));
        return brand;
    }

    /**
     * 构建品牌公共筛选参数
     *
     * @param brandQueryBo 筛选参数
     * @return 筛选条件
     */
    private LambdaQueryWrapper<Brand> commonBrandWrapperBuilder(BrandQueryBo brandQueryBo) {
        LambdaQueryWrapper<Brand> wrapper = new LambdaQueryWrapper<>();
        if (brandQueryBo.getId() != null) {
            wrapper.eq(Brand::getId, brandQueryBo.getId());
        }
        if (!StringUtils.isEmpty(brandQueryBo.getName())) {
            wrapper.like(Brand::getName, StringHelper.escapeSpecialChar(brandQueryBo.getName()));
        }
        if (brandQueryBo.getWhetherRecommend() != null) {
            wrapper.eq(Brand::getWhetherRecommend, brandQueryBo.getWhetherRecommend());
        }

        wrapper.eq(Brand::getWhetherDelete, Boolean.FALSE);
        if (!CollectionUtils.isEmpty(brandQueryBo.getSortList())) {
            String orderSql = MybatisUtil.getOrderSql(brandQueryBo.getSortList());
            wrapper.last("order by " + orderSql);
        }
        else {
            wrapper.orderByDesc(Brand::getId);
        }

        if (brandQueryBo.getLimit() != null) {
            wrapper.last(CommonConstant.LIMIT + brandQueryBo.getLimit());
        }
        return wrapper;
    }

    /**
     * 构建品牌分组
     *
     * @param brands 品牌列表
     * @return 品牌分组
     */
    private List<BrandGroupBo> buildBrandGroup(List<Brand> brands) {
        List<BrandGroupBo> groupList = new ArrayList<>();
        Map<String, List<Brand>> brandMap = groupBrandByFirstPinyin(brands);

        String[] allUpperLetter = PinYinHelper.getAllUpperLetterAndExtSymbol();
        for (String key : allUpperLetter) {
            List<Brand> subBrands = brandMap.getOrDefault(key, new ArrayList<>());
            BrandGroupBo group = BrandGroupBo.builder()
                    .groupKey(key)
                    .brandList(subBrands)
                    .selectAble(subBrands.size() > 0).build();
            groupList.add(group);
        }
        return groupList;
    }

    /**
     * 根据首拼将brand 进行分组
     *
     * @param brands 品牌列表
     * @return 分组后的品牌列表
     */
    private Map<String, List<Brand>> groupBrandByFirstPinyin(List<Brand> brands) {
                        if (CollectionUtils.isEmpty(brands)) {
                            return MapUtils.EMPTY_MAP;
                        }

                        Map<String, List<Brand>> groupMap = new HashMap<>();
                        brands.forEach(brand -> {
            String firstUpperChar = PinYinHelper.getFirstUpperCharOrExtSymbol(brand.getName());
            List<Brand> groupList = groupMap.getOrDefault(firstUpperChar, new ArrayList<>());
            groupList.add(brand);
            groupMap.put(firstUpperChar, groupList);
        });
        return groupMap;
    }


}
