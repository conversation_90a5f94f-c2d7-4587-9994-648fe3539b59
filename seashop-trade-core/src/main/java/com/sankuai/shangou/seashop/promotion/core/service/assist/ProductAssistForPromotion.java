package com.sankuai.shangou.seashop.promotion.core.service.assist;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.core.remote.SkuRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/04/18 15:17
 */
@Component
@Slf4j
public class ProductAssistForPromotion {

    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private SkuRemoteService skuRemoteService;

    public void checkOnSaleStatus(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }

        Map<Long, ProductBasicDto> productMap = productRemoteService.getProductBasicMap(productIds);
        productIds.forEach(productId -> {
            ProductBasicDto productBasicDto = productMap.get(productId);
            AssertUtil.throwIfTrue(productBasicDto == null || productBasicDto.getWhetherDelete(), "商品不存在或已删除, 商品id:" + productId);
            if (!productBasicDto.getSaleStatus().equals(ProductEnum.SaleStatusEnum.ON_SALE.getCode())
                || !productBasicDto.getAuditStatus().equals(ProductEnum.AuditStatusEnum.ON_SALE.getCode())) {
                throw new BusinessException("商品不在销售状态:" + productBasicDto.getProductName());
            }
        });
    }

    public void checkSkuStock(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        List<SkuQueryResp> skuList = skuRemoteService.querySkuList(skuIds);
        Map<String, SkuQueryResp> skuMap = skuList.stream().collect(Collectors.toMap(SkuQueryResp::getSkuId, Function.identity(), (a, b) -> a));
        skuIds.forEach(skuId -> {
            SkuQueryResp sku = skuMap.get(skuId);
            AssertUtil.throwIfTrue(sku == null || sku.getWhetherDelete(), "商品不存在或已删除, 商品id:" + skuId);
            if (sku.getStock() <= 0) {
                throw new BusinessException("商品库存不足:" + sku.getSpecName());
            }
        });
    }
}
