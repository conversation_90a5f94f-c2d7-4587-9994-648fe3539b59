package com.sankuai.shangou.seashop.product.core.service.assist.sort.enums;

import java.util.HashMap;
import java.util.Map;

import com.sankuai.shangou.seashop.product.core.service.assist.sort.SortFieldConverter;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/03/09 11:32
 */
@Getter
public enum ProductSortEnum implements SortFieldConverter<ProductSortEnum> {

    ADDED_TIME("addedTime", "addedTime"),

    ADDED_DATE("addedDate", "addedTime"),

    VIRTUAL_SALE_COUNTS("virtualSaleCounts", "virtualSaleCounts"),

    MIN_SALE_PRICE("minSalePrice", "minSalePrice"),

    CHECK_TIME("checkTime", "onsaleTime"),

    SALE_COUNTS("saleCounts", "saleCounts"),

    DISPLAY_SEQUENCE("displaySequence", "displaySequence"),

    SHOP_DISPLAY_SEQUENCE("shopDisplaySequence", "shopDisplaySequence")

    ;

    private String source;

    private String target;

    ProductSortEnum(String source, String target) {
        this.source = source;
        this.target = target;
    }


    @Override
    public Map<String, String> getMapping() {
        Map<String, String> mapping = new HashMap<>();
        ProductSortEnum[] values = ProductSortEnum.values();
        for (ProductSortEnum value : values) {
            mapping.put(value.source, value.target);
        }
        return mapping;
    }
}
