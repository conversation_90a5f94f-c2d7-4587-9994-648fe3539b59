package com.sankuai.shangou.seashop.product.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.CategoryCashDepositService;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryCashDepositQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryDepositConfigBySkuReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryFirstCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositMapListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositMapResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@RestController
@RequestMapping("/categoryCashDeposit")
@Slf4j
public class CategoryCashDepositQueryController implements CategoryCashDepositQueryFeign {

    @Resource
    private CategoryCashDepositService categoryCashDepositService;

    @GetMapping("/queryAll")
    @Override
    public ResultDto<CategoryCashDepositListResp> queryAll() throws TException {
        List<CategoryCashDepositResp> categoryCashDepositRespList = categoryCashDepositService.queryAll();
        CategoryCashDepositListResp categoryCashDepositListResp = new CategoryCashDepositListResp();
        categoryCashDepositListResp.setList(categoryCashDepositRespList);
        return ResultDto.newWithData(categoryCashDepositListResp);
    }

    @PostMapping(value = "/queryByCategoryList", consumes = "application/json")
    @Override
    public ResultDto<CategoryCashDepositMapListResp> queryByCategoryList(@RequestBody QueryFirstCategoryReq request) throws TException {
        request.checkParameter();
        List<CategoryCashDepositMapResp> list = categoryCashDepositService.queryByCategoryList(request);
        CategoryCashDepositMapListResp categoryCashDepositMapListResp = new CategoryCashDepositMapListResp();
        categoryCashDepositMapListResp.setList(list);
        return ResultDto.newWithData(categoryCashDepositMapListResp);
    }

    @PostMapping(value = "/queryBySkuList", consumes = "application/json")
    @Override
    public ResultDto<SkuFitCategoryCashDepositListResp> queryBySkuList(@RequestBody QueryDepositConfigBySkuReq request) throws TException {
        log.info("【保证金配置查询】查询sku保证金配置, 请求参数={}", JsonUtil.toJsonString(request));
        request.checkParameter();
        List<SkuFitCategoryCashDepositResp> list = categoryCashDepositService.queryBySkuList(request);
        SkuFitCategoryCashDepositListResp categoryCashDepositMapListResp = new SkuFitCategoryCashDepositListResp();
        categoryCashDepositMapListResp.setSkuCateDepositList(list);
        return ResultDto.newWithData(categoryCashDepositMapListResp);
    }
}
