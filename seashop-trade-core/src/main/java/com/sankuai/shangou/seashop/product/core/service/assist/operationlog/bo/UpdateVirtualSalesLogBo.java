package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 19:55
 */
@Getter
@Setter
public class UpdateVirtualSalesLogBo {

    @ExaminField(description = "商品列表", isChildField = true, entityClassName = "com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateVirtualSalesItemLogBo")
    private List<UpdateVirtualSalesItemLogBo> productList;

    public static UpdateVirtualSalesLogBo build(List<Product> productList) {
        UpdateVirtualSalesLogBo bo = new UpdateVirtualSalesLogBo();
        bo.setProductList(JsonUtil.copyList(productList, UpdateVirtualSalesItemLogBo.class));
        return bo;
    }

}
