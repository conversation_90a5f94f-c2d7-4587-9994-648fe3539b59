package com.sankuai.shangou.seashop.product.core.service.model.erp;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/07 9:27
 */
@Getter
@Setter
@Builder
public class ErpSkuBo {

    /**
     * skuId
     */
    private String skuId;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 库存
     */
    private Long stock;

    /**
     * 规格属性(暂无)
     */
    private String specValue;

    /**
     * 规格图片url
     */
    private String showPic;
    /**
     * 自增id
     */
    private Long skuAutoId;
    /**
     * sku 单位
     */
    private String measureUnit;

}
