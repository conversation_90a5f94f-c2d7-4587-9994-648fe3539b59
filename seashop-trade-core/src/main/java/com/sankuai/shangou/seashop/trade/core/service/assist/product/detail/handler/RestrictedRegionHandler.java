package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.remote.FreightTemplateService;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.RestrictedRegionBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.FreightTemplateBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 默认收货地址查询
 *
 * <AUTHOR>
 * @date 2023/12/23 15:47
 */
@Component
@Slf4j
public class RestrictedRegionHandler extends AbsProductQueryHandler {
    @Resource
    private FreightTemplateService freightTemplateService;

    @Override
    public void handle(ProductBaseContext context) {
        log.info("查询受限区域信息, context: {}", context);

        ProductBaseInfoBo product = context.getProduct();
        if (product.getFreightTemplateId() == null) {
            return;
        }

        // 赋值运费模板信息
        RestrictedRegionBo restrictedRegionBo = freightTemplateService.queryRestrictedRegion(product.getFreightTemplateId());
        FreightTemplateBo freightTemplate = JsonUtil.copy(restrictedRegionBo, FreightTemplateBo.class);
        freightTemplate.setFreightTemplateId(product.getFreightTemplateId());
        product.setFreightTemplateInfo(freightTemplate);

        ShippingAddressBo shippingAddress = product.getDefaultShippingAddress();
        // 判断是否在限购区域
        List<Long> restrictedRegionIds = restrictedRegionBo.getRestrictedRegionIds();
        if (shippingAddress != null  && CollectionUtils.isNotEmpty(restrictedRegionIds)) {
            String regionPath = shippingAddress.getRegionPath();
            if (StringUtils.isEmpty(regionPath)) {
                return;
            }

            List<Long> regionIds = Arrays.asList(regionPath.split(StrUtil.COMMA)).stream().map(item -> Long.parseLong(item)).collect(Collectors.toList());
            freightTemplate.setInRestrictedRegion(restrictedRegionIds.stream().filter(item -> regionIds.contains(item)).count() > 0);
        }
    }


    @Override
    public int order() {
        return 2;
    }
}
