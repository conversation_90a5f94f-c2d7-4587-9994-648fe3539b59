package com.sankuai.shangou.seashop.product.core.mq.publisher;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.AbstractTransactionEvent;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.service.model.TransferProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/06 20:06
 */
@Service
@Slf4j
public class TransferProductPublisher {

//    @MafkaProducer(namespace = MafkaConstant.DEFAULT_NAMESPACE, topic = MafkaConstant.TOPIC_PRODUCT_TRANSFER)
//    private IProducerProcessor producerProcessor;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    @Resource
    private ApplicationContext applicationContext;

    public void publish(AbstractTransactionEvent event) {
        applicationContext.publishEvent(event);
    }

    /**
     * 发送转移商品消息
     *
     * @param transferBo 转移商品消息
     */
    public void sendMessage(TransferProductBo transferBo) {
        String msg = JsonUtil.toJsonString(transferBo);
        log.info("商品转移发送消息-data：{}", msg);
        try {
            defaultRocketMq.syncSend(MafkaConstant.TOPIC_PRODUCT_TRANSFER, msg);
        }
        catch (Exception e) {
            log.error("商品转移发送消息-data：{}", msg, e);
            throw new SystemException("转移商品失败");
        }
    }

}
