package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.model.bo.ExclusivePriceSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.ExclusivePriceService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductUpdateReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@RestController
@RequestMapping("/exclusivePriceProduct")
public class ExclusivePriceCmdController implements ExclusivePriceCmdFeign {

    @Resource
    private ExclusivePriceService exclusivePriceService;

    @PostMapping(value = "/save", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> save(@RequestBody ExclusivePriceSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            req.checkParameter();
            ExclusivePriceSaveBo saveBo = JsonUtil.copy(req, ExclusivePriceSaveBo.class);
            exclusivePriceService.save(saveBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/endActive", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("endActive", request, req -> {
            req.checkParameter();
            exclusivePriceService.endActive(req);
            return null;
        });
    }

    @PostMapping(value = "/updateProduct", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateProduct(@RequestBody ExclusivePriceProductUpdateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateProduct", request, req -> {
            req.checkParameter();
            exclusivePriceService.updateProduct(req);
            return BaseResp.of();
        });
    }
}
