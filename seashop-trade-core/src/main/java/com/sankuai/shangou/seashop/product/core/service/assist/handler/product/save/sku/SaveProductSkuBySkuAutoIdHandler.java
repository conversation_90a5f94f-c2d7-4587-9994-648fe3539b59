package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save.sku;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.thrift.core.enums.SkuUpdateKeyEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 根据skuCode 保存sku 信息(主要用于ERP推送)
 *
 * <AUTHOR>
 * @date 2023/11/15 18:28
 */
@Component
@Slf4j
public class SaveProductSkuBySkuAutoIdHandler extends AbsSaveProductSkuHandler<Long> {


    @Override
    public boolean support(ProductContext context) {
        return CollectionUtils.isNotEmpty(context.getSaveProductBo().getSkuList()) && SkuUpdateKeyEnum.SKU_AUTO_ID.equals(context.getSkuUpdateKey());
    }

    @Override
    public String getHandlerName() {
        return "保存商品sku信息(by skuAutoId)";
    }

    @Override
    public Long getUniqueKey(ProductSkuBo sku) {
        return sku.getSkuAutoId();
    }
}
