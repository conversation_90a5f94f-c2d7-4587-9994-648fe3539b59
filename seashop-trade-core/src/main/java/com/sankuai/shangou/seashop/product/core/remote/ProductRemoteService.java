package com.sankuai.shangou.seashop.product.core.remote;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.*;
import com.sankuai.shangou.seashop.product.core.service.model.BatchQueryBranchParamBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.BatchQueryBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryTreeReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductSkuMergeQueryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductSkuMergeDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductRemoteService {

    @Resource
    private ProductService productService;
    @Resource
    private ProductEsBuildService productEsBuildService;
    @Resource
    private BrandService brandService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private SkuQueryService skuQueryService;
    @Resource
    private ShopCategoryService shopCategoryService;

    /**
     * 以SKU为维度查询商品和SKU信息
     *
     * @param skuIdList skuId列表
     * <AUTHOR>
     */
    public List<RemoteProductSkuBo> queryProductSku(List<String> skuIdList) {

        //ProductSkuQueryReq queryReq = ProductSkuQueryReq.builder()
        //    .skuIdList(skuIdList)
        //    .needLadderFlag(Boolean.TRUE)
        //    .build();
        //log.info("【商品查询】查询商品SKU信息, req={}", JsonUtil.toJsonString(queryReq));
        //ProductSkuMergeQueryResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> productQueryThriftService.queryProductSkuMerge(queryReq)
        //);
        //log.info("【商品查询】查询商品SKU信息, resp={}", JsonUtil.toJsonString(resp));
        //return JsonUtil.copyList(resp.getProductSkuList(), RemoteProductSkuBo.class);

        //服务合并改造begin
        ProductSkuQueryReq queryReq = ProductSkuQueryReq.builder()
                .skuIdList(skuIdList)
                .needLadderFlag(Boolean.TRUE)
                .build();
        queryReq.checkParameter();
        log.info("【商品查询】查询商品SKU信息, req={}", JsonUtil.toJsonString(queryReq));
        ProductSkuMergeCombinationBo combBo = productService.queryProductSkuMerge(JsonUtil.copy(queryReq, ProductSkuQueryBo.class));
        log.info("【商品查询】查询商品SKU信息, resp={}", JsonUtil.toJsonString(combBo));
        List<RemoteProductSkuBo> skuList = JsonUtil.copyList(combBo.getProductSkuList(), RemoteProductSkuBo.class);
        return skuList;
        //new end
    }

    public List<RemoteProductSkuBo> queryProductSkuBySkuCodes(Long shopId, List<String> skuCodes) {

        //ProductSkuQueryReq queryReq = ProductSkuQueryReq.builder()
        //    .shopId(shopId)
        //    .skuCodes(skuCodes)
        //    .needLadderFlag(Boolean.TRUE)
        //    .build();
        //log.info("【商品查询】查询商品SKU信息, skuIdList={}", JsonUtil.toJsonString(queryReq));
        //ProductSkuMergeQueryResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> productQueryThriftService.queryProductSkuMerge(queryReq)
        //);
        //log.info("【商品查询】查询商品SKU信息, resp={}", JsonUtil.toJsonString(resp));
        //return JsonUtil.copyList(resp.getProductSkuList(), RemoteProductSkuBo.class);

        ProductSkuQueryReq queryReq = ProductSkuQueryReq.builder()
                .shopId(shopId)
                .skuCodes(skuCodes)
                .needLadderFlag(Boolean.TRUE)
                .build();
        log.info("【商品查询】查询商品SKU信息, skuIdList={}", JsonUtil.toJsonString(queryReq));
        queryReq.checkParameter();
        ProductSkuMergeCombinationBo combBo = productService.queryProductSkuMerge(JsonUtil.copy(queryReq, ProductSkuQueryBo.class));
        log.info("【商品查询】查询商品SKU信息, resp={}", JsonUtil.toJsonString(combBo));
        return JsonUtil.copyList(combBo.getProductSkuList(), RemoteProductSkuBo.class);
    }

    /**
     * 查询商品详情，用于ES构建商品索引时使用，不包含SKU信息，只包含商品基本信息和商品属性信息
     *
     * @param productId 商品ID
     * @return 商品详情
     */
    public ProductDetailBo queryProductDetail(Long productId) {
        //log.info("【商品查询】查询商品详情, productId={}", productId);
        //QueryProductEsReq request = QueryProductEsReq.builder()
        //    .productId(productId)
        //    .build();
        //ProductEsResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> productQueryThriftService.queryProductForEsBuild(request)
        //);
        //log.info("【商品查询】查询商品详情, resp={}", JsonUtil.toJsonString(resp));
        //return JsonUtil.copy(resp.getProduct(), ProductDetailBo.class);

        log.info("【商品查询】查询商品详情, productId={}", productId);
        QueryProductEsReq request = QueryProductEsReq.builder()
                .productId(productId)
                .build();
        request.checkParameter();
        ProductEsBo productBo = productEsBuildService.queryProductForEsBuild(request.getProductId());
        log.info("【商品查询】查询商品详情, resp={}", JsonUtil.toJsonString(productBo));
        return JsonUtil.copy(productBo, ProductDetailBo.class);
    }

    public List<BrandBo> getBrandList(List<Long> brandIdList) {
        //log.info("【商品查询】查询品牌信息, brandIdList={}", JsonUtil.toJsonString(brandIdList));
        //BatchQueryBrandReq request = new BatchQueryBrandReq(brandIdList,true);
        //BrandListResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> brandQueryThriftService.queryBrandList(request)
        //);
        //log.info("【商品查询】查询品牌信息, brandList={}", JsonUtil.toJsonString(resp.getBrandList()));
        //return JsonUtil.copyList(resp.getBrandList(), BrandBo.class);

        log.info("【商品查询】查询品牌信息, brandIdList={}", JsonUtil.toJsonString(brandIdList));
        BatchQueryBrandReq request = new BatchQueryBrandReq(brandIdList,true);
        request.checkParameter();
        // 参数对象转换
        BatchQueryBranchParamBo submitOrderBo = JsonUtil.copy(request, BatchQueryBranchParamBo.class);
        List<BrandDto> brandList = brandService.queryBrandList(submitOrderBo);
        log.info("【商品查询】查询品牌信息, brandList={}", JsonUtil.toJsonString(brandList));
        return JsonUtil.copyList(brandList, BrandBo.class);
    }

    /**
     * 获取推荐商品id的集合
     *
     * @param productId 商品id
     * @return 推荐商品id的集合
     */
    public List<Long> queryRecommendProductIds(Long productId) {
        //log.info("【商品查询】查询推荐商品id的集合, productId={}", productId);
        //BaseIdReq req = new BaseIdReq();
        //req.setId(productId);
        //
        //RecommendProductIdsResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> productQueryThriftService.queryRecommendProductIds(req)
        //);
        //log.info("【商品查询】查询推荐商品id的集合, resp={}", JsonUtil.toJsonString(resp));
        //return resp.getProductIds();

        log.info("【商品查询】查询推荐商品id的集合, productId={}", productId);
        BaseIdReq req = new BaseIdReq();
        req.setId(productId);
        req.checkParameter();
        List<Long> productIds = productService.queryRecommendProductIds(req.getId());
        log.info("【商品查询】查询推荐商品id的集合, resp={}", JsonUtil.toJsonString(productIds));
        return productIds;
    }

    /**
     * 根据商品ID查询商品详情
     *
     * @param productId 商品ID
     * @return 商品详情
     */
    public ProductDetailDto getProductDetail(Long productId) {
        //ProductQueryDetailReq req = new ProductQueryDetailReq();
        //req.setProductId(productId);
        //ProductDetailResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> productQueryThriftService.queryProductDetail(req)
        //);
        //return resp.getResult();

        ProductQueryDetailReq req = new ProductQueryDetailReq();
        req.setProductId(productId);
        req.checkParameter();
        ProductBo productBo = productService.queryProductDetail(req.getProductId(), req.getShopId());
        return JsonUtil.copy(productBo, ProductDetailDto.class);
    }

    /**
     * 查询商品阶梯价格
     *
     * @param productIdList 商品ID列表
     * @return 商品阶梯价格
     */
    public List<RemoteLadderPriceBo> queryProductLadderPrice(List<Long> productIdList) {
        //QueryLadderPriceReq req = new QueryLadderPriceReq();
        //req.setProductIds(productIdList);
        //log.info("【商品查询】查询商品阶梯价格, req={}", JsonUtil.toJsonString(req));
        //ProductLadderPriceResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> productQueryThriftService.getLadderPriceBoList(req)
        //);
        //log.info("【商品查询】查询商品阶梯价格, resp={}", JsonUtil.toJsonString(resp));
        //if (resp == null || CollUtil.isEmpty(resp.getLadderPriceList())) {
        //    return Collections.emptyList();
        //}
        //return JsonUtil.copyList(resp.getLadderPriceList(), RemoteLadderPriceBo.class);

        QueryLadderPriceReq req = new QueryLadderPriceReq();
        req.setProductIds(productIdList);
        log.info("【商品查询】查询商品阶梯价格, req={}", JsonUtil.toJsonString(req));
        req.checkParameter();
        List<LadderPriceDto> ladderPriceBoList = productService.getLadderPriceBoList(req.getProductIds());
        log.info("【商品查询】查询商品阶梯价格, resp={}", JsonUtil.toJsonString(ladderPriceBoList));
        if (CollUtil.isEmpty(ladderPriceBoList)) {
            return Collections.emptyList();
        }
        return JsonUtil.copyList(ladderPriceBoList, RemoteLadderPriceBo.class);
    }

    /**
     * 查询商品分类树，根据第三级ID获取整个分类树
     *
     * @param cateIdList 分类ID列表
     * @return 商品分类树
     */
    public List<CateLevel1Bo> getCategoryTree(List<Long> cateIdList) {
        //QueryCategoryTreeReq req = new QueryCategoryTreeReq();
        //req.setIds(cateIdList);
        //log.info("【商品查询】查询分类树, req={}", JsonUtil.toJsonString(req));
        //CategoryTreeResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> categoryQueryThriftService.queryCategoryTreeWithParent(req)
        //);
        //log.info("【商品查询】查询分类树, resp={}", resp);
        //if (resp == null || StrUtil.isBlank(resp.getResult())) {
        //    return null;
        //}
        //return JsonUtil.parseArray(resp.getResult(), CateLevel1Bo.class);

        QueryCategoryTreeReq req = new QueryCategoryTreeReq();
        req.setIds(cateIdList);
        log.info("【商品查询】查询分类树, req={}", JsonUtil.toJsonString(req));
        List<CategoryBo> categoryList = categoryService.queryCategoryTreeWithParent(JsonUtil.copy(req, CategoryQueryBo.class));
        log.info("【商品查询】查询分类树, resp={}", categoryList);
        if (CollUtil.isEmpty(categoryList)) {
            return Collections.emptyList();
        }
        return JsonUtil.copyList(categoryList, CateLevel1Bo.class);
    }

    public List<RemoteSkuBo> getProductSku(List<Long> productIdList) {
        //SkuQueryReq req = new SkuQueryReq();
        //req.setProductIds(productIdList);
        //log.info("【商品查询】查询商品SKU信息, req={}", JsonUtil.toJsonString(req));
        //SkuListResp resp = ThriftResponseHelper.executeThriftCall(
        //    () -> skuQueryThriftService.querySkuList(req)
        //);
        //log.info("【商品查询】查询商品SKU信息, resp={}", JsonUtil.toJsonString(resp));
        //if (CollUtil.isEmpty(resp.getSkuList())) {
        //    return null;
        //}
        //return JsonUtil.copyList(resp.getSkuList(), RemoteSkuBo.class);

        SkuQueryReq req = new SkuQueryReq();
        req.setProductIds(productIdList);
        log.info("【商品查询】查询商品SKU信息, req={}", JsonUtil.toJsonString(req));
        List<SkuQueryResp> skuList = skuQueryService.querySkuList(req);
        log.info("【商品查询】查询商品SKU信息, resp={}", JsonUtil.toJsonString(skuList));
        if (CollUtil.isEmpty(skuList)) {
            return null;
        }
        return JsonUtil.copyList(skuList, RemoteSkuBo.class);

    }

    public Map<Long, List<RemoteSkuBo>> getProductSkuGroupByProductId(List<Long> productIdList) {
        List<RemoteSkuBo> skuList = this.getProductSku(productIdList);
        if (CollUtil.isEmpty(skuList)) {
            return null;
        }
        return skuList.stream().collect(Collectors.groupingBy(RemoteSkuBo::getProductId));
    }


    //给promotion营销调用的方法***********************************************************************
    /**
     * 分页查询商品列表
     *
     * @param request
     * @return
     */
    public BasePageResp<ProductPageResp> queryProduct(QueryProductReq request) {
        //return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProduct(request));
        //服务合并改造，调用本地方法
        request.checkParameter();
        BasePageParam pageParam = request.buildPage();
        pageParam.setScrollId(request.getScrollId());
        pageParam.setUseScroll(request.getUseScroll());
        pageParam.setKeepAliveMinutes(request.getKeepAliveMinutes());
        BasePageResp<ProductPageBo> pageResult = productService.pageProduct(pageParam, JsonUtil.copy(request, ProductQueryBo.class));
        return PageResultHelper.transfer(pageResult, ProductPageResp.class);
    }


    /**
     * 查询商品sku
     *
     * @param request
     * @return
     */
    public ProductSkuMergeQueryResp queryProductSkuMerge(ProductSkuQueryReq request) {
        //return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductSkuMerge(request));
        //服务合并改造，调用本地方法
        request.checkParameter();
        ProductSkuMergeCombinationBo combBo = productService.queryProductSkuMerge(JsonUtil.copy(request, ProductSkuQueryBo.class));
        return JsonUtil.copy(combBo, ProductSkuMergeQueryResp.class);
    }

    /**
     * 根据skuId 查询商品sku
     *
     * @param skuIds 商品skuId
     * @return 商品sku
     */
    public List<ProductSkuMergeDto> getProductSkuMergeList(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        skuIds = skuIds.stream().distinct().collect(Collectors.toList());
        List<List<String>> subSkuIdArr = Lists.partition(skuIds, PromotionConstant.MAX_QUERY_LIMIT);
        List<ProductSkuMergeDto> skuList = new ArrayList<>();
        subSkuIdArr.forEach(ids -> {
            ProductSkuQueryReq req = new ProductSkuQueryReq();
            req.setSkuIdList(ids);
            ProductSkuMergeQueryResp resp = queryProductSkuMerge(req);
            if (resp != null && CollectionUtils.isNotEmpty(resp.getProductSkuList())) {
                skuList.addAll(resp.getProductSkuList());
            }
        });
        return skuList;
    }

    /**
     * 根据商品id查询商品
     *
     * @param request
     * @return
     */
    public ProductListResp queryProductById(QueryProductByIdReq request) {
        //return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductById(request));
        //服务合并改造，调用本地方法
        request.checkParameter();
        List<ProductPageBo> productList = productService.queryProductById(request.getProductIds(), request.getShopId());
        return ProductListResp.builder().productList(JsonUtil.copyList(productList, ProductPageResp.class)).build();
    }

    /**
     * 根据商品ID集合查询阶梯价
     *
     * @param productIdList
     * @return
     */
    public List<LadderPriceDto> getLadderPriceBoList(List<Long> productIdList) {
        //QueryLadderPriceReq request = new QueryLadderPriceReq();
        //request.setProductIds(productIdList);
        //ProductLadderPriceResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.getLadderPriceBoList(request));
        //return resp.getLadderPriceList();
        //服务合并改造，调用本地方法
        QueryLadderPriceReq request = new QueryLadderPriceReq();
        request.setProductIds(productIdList);
        request.checkParameter();
        List<LadderPriceDto> ladderPriceBoList = productService.getLadderPriceBoList(request.getProductIds());
        return ladderPriceBoList;
    }

    /**
     * 根据商品ID集合从数据库查询商品基础信息（供组合购使用）
     *
     * @param request
     * @return
     */
    public List<ProductBasicDto> queryProductBase(QueryProductBasicReq request) {
        //ProductBasicResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductBasic(request));
        //return resp.getProductList();
        //服务合并改造，调用本地方法
        request.checkParameter();
        List<ProductBasicDto> productList = productService.queryProductBasic(request);
        return productList;
    }

    public List<ProductBasicDto> queryProductBase(List<Long> productIds) {
        //if (CollectionUtils.isEmpty(productIds)) {
        //    return Collections.emptyList();
        //}
        //
        //List<ProductBasicDto> productList = new ArrayList<>();
        //List<List<Long>> subProductIds = Lists.partition(productIds, PromotionConstant.MAX_QUERY_LIMIT);
        //subProductIds.forEach(ids -> {
        //    QueryProductBasicReq request = new QueryProductBasicReq();
        //    request.setProductIds(ids);
        //    ProductBasicResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductBasic(request));
        //    if (resp != null && CollectionUtils.isNotEmpty(resp.getProductList())) {
        //        productList.addAll(resp.getProductList());
        //    }
        //});
        //return productList;
        //服务合并改造，调用本地方法
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        List<ProductBasicDto> productList = new ArrayList<>();
        List<List<Long>> subProductIds = Lists.partition(productIds, PromotionConstant.MAX_QUERY_LIMIT);
        subProductIds.forEach(ids -> {
            QueryProductBasicReq request = new QueryProductBasicReq();
            request.setProductIds(ids);
            request.checkParameter();
            List<ProductBasicDto> respProductList = productService.queryProductBasic(request);
            if (CollectionUtils.isNotEmpty(respProductList)) {
                productList.addAll(respProductList);
            }
        });
        return productList;
    }

    public ProductBasicDto getByProductId(Long productId) {
        List<ProductBasicDto> productList = queryProductBase(Arrays.asList(productId));
        return CollectionUtils.isEmpty(productList) ? null : productList.get(0);
    }

    public List<ProductPageResp> queryProductByIds(QueryProductByIdsReq req) {
        //return ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductByIds(req));
        //服务合并改造，调用本地方法
        req.checkParameter();
        return productService.queryProductByIds(req);
    }

    public Map<Long, ProductBasicDto> getProductBasicMap(List<Long> productIdList) {
        List<ProductBasicDto> productList = queryProductBase(productIdList);

        return productList.stream().collect(Collectors.toMap(ProductBasicDto::getProductId, Function.identity(), (k, v) -> v));
    }

    public Map<Long, ProductPageResp> getProductMap(List<Long> productIdList) {
        //if (CollectionUtils.isEmpty(productIdList)) {
        //    return Collections.emptyMap();
        //}
        //
        //productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        //List<List<Long>> subProductIds = Lists.partition(productIdList, PromotionConstant.MAX_QUERY_LIMIT);
        //List<ProductPageResp> productList = new ArrayList<>();
        //
        //subProductIds.forEach(subIds -> {
        //    QueryProductByIdReq request = new QueryProductByIdReq();
        //    request.setProductIds(subIds);
        //    ProductListResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryThriftService.queryProductById(request));
        //    if (resp != null && CollectionUtils.isNotEmpty(resp.getProductList())) {
        //        productList.addAll(resp.getProductList());
        //    }
        //});
        //
        //return productList.stream().collect(Collectors.toMap(ProductPageResp::getProductId, Function.identity(), (k, v) -> v));
        //服务合并改造，调用本地方法
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.emptyMap();
        }
        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        List<List<Long>> subProductIds = Lists.partition(productIdList, PromotionConstant.MAX_QUERY_LIMIT);
        List<ProductPageResp> productList = new ArrayList<>();

        subProductIds.forEach(subIds -> {
            QueryProductByIdReq request = new QueryProductByIdReq();
            request.setProductIds(subIds);
            request.checkParameter();
            List<ProductPageBo> respProductList = productService.queryProductById(request.getProductIds(), request.getShopId());
            ProductListResp resp = ProductListResp.builder().productList(JsonUtil.copyList(respProductList, ProductPageResp.class)).build();
            if (resp != null && CollectionUtils.isNotEmpty(resp.getProductList())) {
                productList.addAll(resp.getProductList());
            }
        });

        return productList.stream().collect(Collectors.toMap(ProductPageResp::getProductId, Function.identity(), (k, v) -> v));
    }

    public SkuListResp querySkuList(SkuQueryReq request) {
        //return ThriftResponseHelper.executeThriftCall(() -> skuQueryThriftService.querySkuList(request));
        //服务合并改造，调用本地方法
        request.checkParameter();
        List<SkuQueryResp> skuList = skuQueryService.querySkuList(request);
        return SkuListResp.builder().skuList(skuList).build();
    }

    /**
     * 以SKU为维度查询商品和SKU信息
     *
     * @param skuIdList skuId列表
     * <AUTHOR>
     */
    public Map<String, RemoteProductSkuBo> queryProductSkuToMap(List<String> skuIdList) {
        List<RemoteProductSkuBo> list = parallelQueryProductSku(skuIdList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream()
            .collect(Collectors.toMap(RemoteProductSkuBo::getSkuId, Function.identity(), (o1, o2) -> o2));
    }

    /**
     * 以SKU为维度查询商品和SKU信息（并行查询）
     *
     * @param skuIdList skuId列表
     * <AUTHOR>
     */
    public List<RemoteProductSkuBo> parallelQueryProductSku(List<String> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return Collections.emptyList();
        }

        // 存放查询的规格信息
        List<RemoteProductSkuBo> respList = Collections.synchronizedList(new ArrayList<>());
        // 200 一个分组
        List<List<String>> subSkuIdList = Lists.partition(skuIdList, CommonConst.PAGE_ALL_PAGE_SIZE);

        subSkuIdList.parallelStream().forEach(skuIds -> {
            ProductSkuQueryBo queryReq = ProductSkuQueryBo.builder()
                .skuIdList(skuIds)
                .needLadderFlag(Boolean.TRUE)
                .build();

            ProductSkuMergeCombinationBo resp = productService.queryProductSkuMerge(queryReq);
            log.debug("【商品查询】查询商品SKU信息, resp={}", JsonUtil.toJsonString(resp));
            if (resp == null) {
                return;
            }
            // 目前接口入参是skuId，可能之前是但规格，后面改成多规格了，购物车传入的skuId查不到数据，但是要显示商品名称，所以通过invalid返回
            List<ProductSkuMergeBo> normalList = resp.getProductSkuList();
            List<ProductSkuMergeBo> invalidList = resp.getInvalidSkuList();
            if (CollUtil.isEmpty(normalList)) {
                normalList = new ArrayList<>(queryReq.getSkuIdList().size());
            }
            if (CollUtil.isNotEmpty(invalidList)) {
                invalidList.forEach(data -> {
                    data.setWhetherDelete(true);
                    // 防止报错的默认值
                    data.setSalePrice(BigDecimal.ZERO);
                });
                normalList.addAll(invalidList);
            }
            respList.addAll(JsonUtil.copyList(normalList, RemoteProductSkuBo.class));
        });

        return respList;
    }

    public CategoryBo getCategoryById(Long cateId) {
        return categoryService.getCategoryById(cateId);
    }

    /**
     * 根据店铺分类ID获取自己以及其所有子级
     *
     * @param shopCateId 店铺分类ID
     */
    public List<Long> getSubAndSelfShopCateById(Long shopCateId) {
        return shopCategoryService.getSubShopCategoryIds(shopCateId);
    }
}
