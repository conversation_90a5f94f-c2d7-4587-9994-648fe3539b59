package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import java.math.BigDecimal;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerProcessor;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.ActivityQueryProcessor;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.ProductHandlerType;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.TradeProductHandlerProcessor;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/23 15:52
 */
@Component
@Slf4j
public class ProductActivityHandler extends AbsProductQueryHandler {

    @Resource
    private ActivityQueryProcessor activityQueryProcessor;
    @Resource
    private TradeProductHandlerProcessor tradeProductHandlerProcessor;

    @Override
    public void handle(ProductBaseContext context) {
        ActivityContext activityContext = JsonUtil.copy(context, ActivityContext.class);
        ProductBaseInfoBo product = context.getProduct();
        // 阶梯价为商品详情返回 所以无需再次查询
        activityContext.setLadderPriceList(product.getLadderPriceList());
        activityQueryProcessor.query(activityContext);

        // 处理活动信息
        DealActivityContext dealActivityContext = JsonUtil.copy(activityContext, DealActivityContext.class);
        dealActivityContext.setSkuList(product.getSkuList());
        dealActivityContext.setWhetherOpenLadder(product.isWhetherOpenLadder());
        dealActivityContext.setActivityContext(activityContext);
        dealActivityContext.setEstimatePrice(product.getEstimatePrice());
        dealActivityContext.setCollocationId(context.getCollocationId());
        BigDecimal orgEstimatePrice = product.getEstimatePrice();
        tradeProductHandlerProcessor.handle(ProductHandlerType.DEAL_ACTIVITY, dealActivityContext);
        product.setActivityInfo(dealActivityContext.getActivityInfo());
        product.setEstimatePrice(dealActivityContext.getEstimatePrice());
        product.setSalePriceRange(dealActivityContext.getSalePriceRange());
        product.setMinSalePrice(dealActivityContext.getMinSalePrice());
        product.setMaxSalePrice(dealActivityContext.getMaxSalePrice());

        // 如果预估到手价没有变化 则前端不显示
        if (orgEstimatePrice.compareTo(product.getEstimatePrice()) == 0) {
            product.setEstimatePrice(null);
        }
    }

    @Override
    public int order() {
        return 5;
    }
}
