package com.sankuai.shangou.seashop.product.core.service.model.product;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品sku 合并信息
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuMergeBo {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目ids
     */
    private List<Long> categoryIds;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目路径
     */
    private String categoryPath;

    /**
     * 类目全路径
     */
    private String fullCategoryName;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    private ProductEnum.SaleStatusEnum saleStatus;

    /**
     * 审核状态 1-待审核 2-销售中 3-未通过 4-违规下架
     */
    private ProductEnum.AuditStatusEnum auditStatus;

    /**
     * 添加时间
     */
    private Date addedDate;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 最小销售价
     */
    private BigDecimal minSalePrice;

    /**
     * 是否有sku
     */
    private Boolean hasSku;

    /**
     * 运费模板ID
     */
    private Long freightTemplateId;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 最大购买数
     */
    private Integer maxBuyCount;

    /**
     * 是否开启阶梯价
     */
    private Boolean whetherOpenLadder;

    /**
     * 规格1 别名
     */
    private String spec1Alias;

    /**
     * 规格2 别名
     */
    private String spec2Alias;

    /**
     * 规格3 别名
     */
    private String spec3Alias;

    /**
     * 商品主图
     */
    private String imagePath;

    /**
     * 倍数起购量
     */
    private Integer multipleCount;

    /**
     * 是否删除
     */
    private Boolean whetherDelete;

    /**
     * sku自增id
     */
    private Long skuAutoId;

    /**
     * sku 拼接id 商品ID_规格1ID_规格2ID_规格3ID
     */
    private String skuId;

    /**
     * 规格1
     */
    private String spec1Value;

    /**
     * 规格2
     */
    private String spec2Value;

    /**
     * 规格3
     */
    private String spec3Value;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 安全库存
     */
    private Long safeStock;

    /**
     * 库存
     */
    private Long stock;

    /**
     * 阶梯价信息
     */
    private List<ProductLadderPriceBo> ladderPriceList;

    /**
     * spec 名称
     */
    private String specName;

    /**
     * 规格货号
     */
    private String skuCode;

    /**
     * 合并数量(用于给前端处理合并单元格)
     */
    private long mergeCount;

    /**
     * 计量单位
     */
    private String measureUnit;

    /**
     * 规格名称
     */
    private String skuName;
    /**
     * OE号
     */
    private String oeCode;
    /**
     * 品牌号
     */
    private String brandCode;
    /**
     * 适用车型
     */
    private String adaptableCar;
    /**
     * 零件规格
     */
    private String partSpec;
}
