package com.sankuai.shangou.seashop.product.core.mq.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 库存变动消息
 */
@NoArgsConstructor
@Getter
@Setter
@ToString
public class MigrateImageMessage implements Serializable {

    /**
     * 迁移类型 1 商品 2 商品审核
     */
    @JsonProperty("type")
    private Integer type;

    /**
     * 商品ID
     */
    @JsonProperty("product_id")
    private List<Long> productId;
}
