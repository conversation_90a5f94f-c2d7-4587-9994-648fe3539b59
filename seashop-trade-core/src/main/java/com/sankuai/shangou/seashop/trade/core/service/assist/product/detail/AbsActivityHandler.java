package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;

/**
 * 抽象活动处理器
 *
 * <AUTHOR>
 * @date 2023/12/25 9:54
 */
public abstract class AbsActivityHandler extends AbsProductHandler<DealActivityContext> {

    /**
     * 处理器类型
     */
    public ProductHandlerType type() {
        return ProductHandlerType.DEAL_ACTIVITY;
    }

}
