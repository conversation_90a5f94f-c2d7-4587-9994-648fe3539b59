package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.ImportResult;
import com.sankuai.shangou.seashop.product.core.service.ProductImportService;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.model.BindDescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductImportBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuUpdatePriceBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductUpdateVirtualSaleCountsBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.AddSaleCountReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.BindDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.BindFreightTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.BindRecommendProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductDeleteReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductImportReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductOnOffSaleReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuUpdatePriceReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateSafeStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateSequenceReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductUpdateVirtualSaleCountsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductViolationReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.SaveProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.SaveProductResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/14 11:45
 */
@RestController
@RequestMapping("/product")
public class ProductCmdController implements ProductCmdFeign {

    @Resource
    private ProductService productService;
    @Resource
    private ProductImportService productImportService;
    @Resource
    private StorageClient storageClient;

    @Resource
    private ProductRepository productRepository;
    @Resource
    private ProductAuditRepository productAuditRepository;

    @Resource
    private ProductEsBuildService productEsBuildService;
    @PostMapping(value = "/createProduct", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> createProduct(@RequestBody SaveProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createProduct", request, req -> {
            req.checkParameter(Boolean.FALSE);

            ProductBo productBo = JsonUtil.copy(req, ProductBo.class);
            ProductSourceEnum changeSource = ProductSourceEnum.getByCode(req.getChangeSource());
            productService.saveProduct(productBo, changeSource, ProductChangeType.CREATE, Boolean.FALSE, Boolean.FALSE);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateProduct", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateProduct(@RequestBody SaveProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateProduct", request, req -> {
            req.checkParameter(Boolean.FALSE);

            ProductBo productBo = JsonUtil.copy(req, ProductBo.class);
            ProductSourceEnum changeSource = ProductSourceEnum.getByCode(req.getChangeSource());
            productService.saveProduct(productBo, changeSource, ProductChangeType.UPDATE, Boolean.FALSE, Boolean.FALSE);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/partSaveProduct", consumes = "application/json")
    @Override
    public ResultDto<SaveProductResp> partSaveProduct(@RequestBody SaveProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("partSaveProduct", request, req -> {
            req.checkParameter(Boolean.TRUE);

            ProductBo productBo = JsonUtil.copy(req, ProductBo.class);
            ProductSourceEnum changeSource = ProductSourceEnum.getByCode(req.getChangeSource());
            ProductChangeType changeType = req.getProductId() != null && req.getProductId() > 0 ? ProductChangeType.UPDATE : ProductChangeType.CREATE;
            SaveProductResp productResp = productService.saveProduct(productBo, changeSource, changeType, Boolean.TRUE, Boolean.FALSE);
            return productResp;
        });
    }

    @PostMapping(value = "/batchOnOffSaleProduct", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchOnOffSaleProduct(@RequestBody ProductOnOffSaleReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchOnOffSaleProduct", request, req -> {
            req.checkParameter();

            productService.batchOnOffSaleProduct(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchDeleteProduct", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchDeleteProduct(@RequestBody ProductDeleteReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchDeleteProduct", request, req -> {
            req.checkParameter();

            productService.batchDeleteProduct(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchSaveProductSequence", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchSaveProductSequence(@RequestBody ProductUpdateSequenceReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchSaveProductSequence", request, req -> {
            req.checkParameter();

            productService.batchSaveProductSequence(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchSaveProductShopSequence", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchSaveProductShopSequence(@RequestBody ProductUpdateSequenceReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchSaveProductShopSequence", request, req -> {
            req.checkParameter();

            productService.batchSaveProductShopSequence(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchBindDescriptionTemplate", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchBindDescriptionTemplate(@RequestBody BindDescriptionTemplateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchBindDescriptionTemplate", request, req -> {
            req.checkParameter();

            productService.batchBindDescriptionTemplate(JsonUtil.copy(req, BindDescriptionTemplateBo.class));
            return new BaseResp();
        });
    }

    @PostMapping(value = "/bindRecommendProduct", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> bindRecommendProduct(@RequestBody BindRecommendProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("bindRecommendProduct", request, req -> {
            req.checkParameter();

            productService.bindRecommendProduct(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchBindFreightTemplate", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchBindFreightTemplate(@RequestBody BindFreightTemplateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("bindFreightTemplate", request, req -> {
            req.checkParameter();

            productService.batchBindFreightTemplate(req);
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductSkuUpdatePriceReq.class,
            entity = Product.class,
            actionName = "批量改价")
    @PostMapping(value = "/batchUpdateProductPrice", consumes = "application/json")
    public ResultDto<BaseResp> batchUpdateProductPrice(@RequestBody ProductSkuUpdatePriceReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchUpdateProductPrice", request, req -> {
            req.checkParameter();

            productService.batchUpdateProductPrice(JsonUtil.copyList(req.getSkuList(), ProductSkuUpdatePriceBo.class), req.getShopId());
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchUpdateSafeStock", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchUpdateSafeStock(@RequestBody ProductUpdateSafeStockReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchUpdateSafeStock", request, req -> {
            req.checkParameter();

            productService.batchUpdateSafeStock(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchUpdateStock", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchUpdateStock(@RequestBody ProductUpdateStockReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchUpdateStock", request, req -> {
            req.checkParameter();

            productService.batchUpdateStock(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchUpdateVirtualSales", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchUpdateVirtualSales(@RequestBody ProductUpdateVirtualSaleCountsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchUpdateVirtualSales", request, req -> {
            req.checkParameter();

            productService.batchUpdateVirtualSales(JsonUtil.copy(req, ProductUpdateVirtualSaleCountsBo.class));
            return new BaseResp();
        });
    }

    @PostMapping(value = "/batchViolationOffSale", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> batchViolationOffSale(@RequestBody ProductViolationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("batchViolationOffSale", request, req -> {
            req.checkParameter();

            productService.batchViolationOffSale(req);
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductImportReq.class,
            entity = Product.class,
            actionName = "导入库存")
    @PostMapping(value = "/importStock", consumes = "application/json")
    public ResultDto<BaseImportResp> importStock(@RequestBody ProductImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("importStock", request, req -> {
            req.checkParameter();

            ImportResult importResult = productImportService.importStock(JsonUtil.copy(req, ProductImportBo.class));
            return JsonUtil.copy(importResult, BaseImportResp.class);
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductImportReq.class,
            entity = Product.class,
            actionName = "导入下架")
    @PostMapping(value = "/importOffSale", consumes = "application/json")
    public ResultDto<BaseImportResp> importOffSale(@RequestBody ProductImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("importOffSale", request, req -> {
            req.checkParameter();

            ImportResult importResult = productImportService.importOffSale(JsonUtil.copy(req, ProductImportBo.class));
            return JsonUtil.copy(importResult, BaseImportResp.class);
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductImportReq.class,
            entity = Product.class,
            actionName = "导入违规下架")
    @PostMapping(value = "/importViolationOffSale", consumes = "application/json")
    public ResultDto<BaseImportResp> importViolationOffSale(@RequestBody ProductImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("importViolationOffSale", request, req -> {
            req.checkParameter();

            ImportResult importResult = productImportService.importViolationOffSale(JsonUtil.copy(req, ProductImportBo.class));
            return JsonUtil.copy(importResult, BaseImportResp.class);
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductImportReq.class,
            entity = Product.class,
            actionName = "供应商导入商品")
    @PostMapping(value = "/importProduct", consumes = "application/json")
    public ResultDto<BaseImportResp> importProduct(@RequestBody ProductImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("importProduct", request, req -> {
            req.checkParameter();

            ImportResult importResult = productImportService.importProduct(JsonUtil.copy(req, ProductImportBo.class));
            importResult.setFilePath(storageClient.formatUrl(importResult.getFilePath()));
            return JsonUtil.copy(importResult, BaseImportResp.class);
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductImportReq.class,
            entity = Product.class,
            actionName = "平台导入商品")
    @PostMapping(value = "/platformImportProduct", consumes = "application/json")
    public ResultDto<BaseImportResp> platformImportProduct(@RequestBody ProductImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("platformImportProduct", request, req -> {
            req.checkParameter();

            ImportResult importResult = productImportService.platformImportProduct(JsonUtil.copy(req, ProductImportBo.class));
            return JsonUtil.copy(importResult, BaseImportResp.class);
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductImportReq.class,
            entity = Product.class,
            actionName = "导入价格")
    @PostMapping(value = "/importPrice", consumes = "application/json")
    public ResultDto<BaseImportResp> importPrice(@RequestBody ProductImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("importPrice", request, req -> {
            req.checkParameter();

            ImportResult importResult = productImportService.importPrice(JsonUtil.copy(req, ProductImportBo.class));
            return JsonUtil.copy(importResult, BaseImportResp.class);
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = ProductImportReq.class,
            entity = Product.class,
            actionName = "导入商品更新")
    @PostMapping(value = "/importProductUpdate", consumes = "application/json")
    public ResultDto<BaseImportResp> importProductUpdate(@RequestBody ProductImportReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("importProductUpdate", request, req -> {
            req.checkParameter();

            ImportResult importResult = productImportService.importProductUpdate(JsonUtil.copy(req, ProductImportBo.class));
            return JsonUtil.copy(importResult, BaseImportResp.class);
        });
    }

    @PostMapping(value = "/offSaleAllProduct", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> offSaleAllProduct(@RequestBody BaseIdReq shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("offSaleAllProduct", shopId, req -> {
            req.checkParameter();

            productService.offSaleAllProduct(req.getId());
            return new BaseResp();
        });
    }

    @PostMapping(value = "/addSales", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addSales(@RequestBody AddSaleCountReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("addSales", request, req -> {
            req.checkParameter();

            productService.addSales(req);
            return new BaseResp();
        });
    }

    @Override
    public ResultDto<BaseResp> initProductES() throws TException {
        return ThriftResponseHelper.responseInvoke("addSales", null, req -> {
            LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>() ;
            queryWrapper.eq(Product::getWhetherDelete,false);
            List<Product> list = productRepository.list(queryWrapper);
            for (Product product : list) {
                productEsBuildService.buildProductEs(product.getProductId());
            }
            return new BaseResp();
        });

    }

    @Override
    public ResultDto<BaseResp> initProductAuditES() throws TException {
        return ThriftResponseHelper.responseInvoke("addSales", null, req -> {
            List<ProductAudit> list = productAuditRepository.list();
            for (ProductAudit product : list) {
                productEsBuildService.buildProductAuditEs(product.getProductId());
            }
            return new BaseResp();
        });
    }
}