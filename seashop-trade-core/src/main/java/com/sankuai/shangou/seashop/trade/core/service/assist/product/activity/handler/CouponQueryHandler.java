package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.handler;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.AbsActivityQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 优惠券查询处理器
 *
 * <AUTHOR>
 * @date 2023/12/23 10:36
 */
@Component
@Slf4j
public class CouponQueryHandler extends AbsActivityQueryHandler {

    @Resource
    private PromotionRemoteService promotionRemoteService;

    @Override
    protected void query(ActivityContext context) {
        log.info("查询优惠券活动, context: {}", context);
        context.setCouponList(promotionRemoteService.getCouponList(context.getProductId(), context.getShopId(), context.getUserId()));
        log.info("查询优惠券活动结果, context: {}, activity: {}", context, JsonUtil.toJsonString(context.getCouponList()));
    }

    @Override
    protected Class<? extends AbsActivityQueryHandler> nextHandler() {
        return null;
    }

    /**
     * 优惠券与专享价、满减互斥
     * 优惠券和满减虽然互斥, 但是参加哪种活动由客户选择, 所以也需要查询
     *
     * @param context 活动上下文对象
     * @return true 支持，false 不支持
     */
    @Override
    protected boolean support(ActivityContext context) {
        return context.getExclusivePrice() == null;
        // && context.getFullReduction() == null;
    }
}
