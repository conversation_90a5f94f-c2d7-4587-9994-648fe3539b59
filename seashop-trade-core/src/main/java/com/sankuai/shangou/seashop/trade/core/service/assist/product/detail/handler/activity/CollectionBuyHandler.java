package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 组合购活动处理器
 *
 * <AUTHOR>
 * @date 2023/12/25 9:29
 */
@Slf4j
@Component
public class CollectionBuyHandler extends AbsActivityHandler {

    @Override
    public void handle(DealActivityContext context) {
        ActivityContext activityContext = context.getActivityContext();
        if (CollectionUtils.isEmpty(activityContext.getCollectionBuy())) {
            return;
        }

        List<RemoteCollocationBo> collectionBuyList = activityContext.getCollectionBuy();
        ProductActivityInfoBo activityInfo = context.getActivityInfo();
        activityInfo.setHasCollectionBuy(true);
        activityInfo.setCollectionBuyList(collectionBuyList);
    }

    @Override
    public int order() {
        return 4;
    }
}
