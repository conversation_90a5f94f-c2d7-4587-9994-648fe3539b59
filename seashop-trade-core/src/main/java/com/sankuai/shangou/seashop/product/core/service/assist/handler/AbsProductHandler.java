package com.sankuai.shangou.seashop.product.core.service.assist.handler;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23 19:06
 */
public abstract class AbsProductHandler {

    /**
     * 处理器类型
     *
     * @return 处理器类型
     */
    protected abstract List<ProductHandlerType> types();

    /**
     * 处理器处理逻辑
     *
     * @param context 上下文
     */
    protected abstract void handle(ProductContext context);

    /**
     * 是否支持
     *
     * @param context 上下文
     * @return 是否支持
     */
    protected boolean support(ProductContext context) {
        return true;
    }

    /**
     * 处理器配置
     *
     * @return 处理器配置
     */
    protected abstract HandlerConfigEnum config();

}
