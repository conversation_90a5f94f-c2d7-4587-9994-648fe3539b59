package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.core.service.SkuStockService;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventBuilder;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendSyncStockEvent;
import com.sankuai.shangou.seashop.product.core.service.converter.SkuStockChangeTaskConverter;
import com.sankuai.shangou.seashop.product.core.service.model.RollBackStockBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskInfoBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockTask;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockTaskInfo;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockLogRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockTaskInfoRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockTaskRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateWayEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.result.ProductResultEnum;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/18 15:50
 */
@Service
@Slf4j
public class SkuStockServiceImpl implements SkuStockService {

    @Resource
    private SkuStockTaskRepository skuStockTaskRepository;
    @Resource
    private SkuStockTaskInfoRepository skuStockTaskInfoRepository;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private SkuAssist skuAssist;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuStockRepository skuStockRepository;
    @Resource
    private TransactionEventPublisher transactionEventPublisher;
    @Resource
    private SkuStockLogRepository skuStockLogRepository;

    private static String LOCK_KEY_FORMAT = "%s_%s_%s";
    private static String ROLL_BACK_STOCK_PREFIX = "rollback_";

    @Override
    public void syncChangeStock(StockTaskBo taskBo) {
        // 同步更新, 不能超过50条
        AssertUtil.throwIfTrue(taskBo.getTaskInfoBoList().size() > CommonConstant.SYNC_STOCK_LIMIT, "同步更新库存数不能超过50");

        // 先获取批次号的锁 防止重复提交任务
        String lockKey = getLockKey(taskBo.getUpdateType(), taskBo.getBizCode(), taskBo.getSeqCode());
        LockKey taskLock = getTaskLockKey(lockKey);
        distributedLockService.tryLock(taskLock, () -> {
            // 初始化taskBo
            if (initTaskBo(taskBo)) {
                return;
            }

            // 转换关联id 将调用方传过来的key 统一转换为skuId
            List<StockTaskInfoBo> taskInfoList = taskBo.getTaskInfoBoList();
            transRelateId(taskBo.getUpdateKey(), taskInfoList);
            // 提取taskInfoList 中不为空的errorReason, 并且将errorReason 逗号拼接起来
            String errorReason = taskInfoList.stream().filter(item -> StrUtil.isNotBlank(item.getErrorReason())).map(StockTaskInfoBo::getErrorReason).collect(Collectors.joining(StrUtil.COMMA));
            AssertUtil.throwIfTrue(StrUtil.isNotBlank(errorReason), errorReason);

            SkuStockTask task = SkuStockChangeTaskConverter.taskBoConvertToTask(taskBo);

            // 修改库存
            TransactionHelper.doInTransaction(() -> {
                // 保存库存变动任务
                skuStockTaskRepository.save(task);
                // 关联taskId
                taskInfoList.forEach(taskInfo -> {
                    taskInfo.setTaskId(task.getId());
                    taskInfo.setId(null);
                });

                // 实际扣减库存
                actualChangeStock(task, taskInfoList);

                // 记录库存变动明细
                skuStockTaskInfoRepository.saveBatch(SkuStockChangeTaskConverter.taskInfoBoConvertToTaskInfo(taskInfoList));
            });

        }, LockConstant.SYNC_STOCK_TIMEOUT, TimeUnit.SECONDS);
    }

    @Override
    public void asyncChangeSkuStock(StockTaskBo taskBo) {
        // 先获取批次号的锁 防止重复提交任务
        String lockKey = getLockKey(taskBo.getUpdateType(), taskBo.getBizCode(), taskBo.getSeqCode());
        LockKey taskLock = getTaskLockKey(lockKey);
        distributedLockService.tryLock(taskLock, () -> {
            // 初始化taskBo
            if (initTaskBo(taskBo)) {
                return;
            }

            List<StockTaskInfoBo> taskInfoList = taskBo.getTaskInfoBoList();
            transRelateId(taskBo.getUpdateKey(), taskInfoList);
            SkuStockTask task = SkuStockChangeTaskConverter.taskBoConvertToTask(taskBo);

            // 记录任务, 覆盖库存逻辑由mq执行
            TransactionHelper.doInTransaction(() -> {
                skuStockTaskRepository.save(task);
                List<SkuStockTaskInfo> dbTaskInfoList = SkuStockChangeTaskConverter.taskInfoBoConvertToTaskInfo(taskInfoList);
                // 关联taskId
                dbTaskInfoList.forEach(taskInfo -> {
                    taskInfo.setTaskId(task.getId());
                    taskInfo.setId(null);
                });
                skuStockTaskInfoRepository.saveBatch(dbTaskInfoList);

                // 发送事件消息, 在整个事务提交后执行
                List<StockTaskInfoBo> eventBody = JsonUtil.copyList(dbTaskInfoList, StockTaskInfoBo.class);
                transactionEventPublisher.publish(TransactionEventBuilder.of(SendSyncStockEvent.class, eventBody));
            });

        }, LockConstant.SYNC_STOCK_TIMEOUT, TimeUnit.SECONDS);
    }

    @Override
    public void executeAsyncStock(Long taskInfoId) {
        LockKey taskLockKey = LockKey.builder()
            .catTag(LockConstant.STOCK_TASK_SCENE + taskInfoId)
            .lockName(LockConstant.STOCK_TASK_LOCK + taskInfoId)
            .build();
        // 先锁定任务id 防止任务重复执行
        distributedLockService.tryLock(taskLockKey, () -> {

            SkuStockTaskInfo taskInfo = skuStockTaskInfoRepository.getByIdForceMaster(taskInfoId);
            if (taskInfo == null) {
                log.error("[同步库存]任务明细不存在, taskInfoId:{}", taskInfoId);
                return;
            }

            if (taskInfo.getStatus().equals(StockUpdateStatusEnum.SUCCESS.getCode())) {
                log.warn("子任务已经处理完成, taskInfo:{}", taskInfo);
                return;
            }

            StockTaskInfoBo taskInfoBo = JsonUtil.copy(taskInfo, StockTaskInfoBo.class);
            SkuStockTask task = skuStockTaskRepository.getByIdForceMaster(taskInfoBo.getTaskId());
            if (task == null) {
                log.error("[同步库存]任务不存在, taskId:{}", taskInfoBo.getTaskId());
                return;
            }

            TransactionHelper.doInTransaction(() -> {
                try {
                    // 执行实际同步库存的方法
                    actualChangeStock(task, Arrays.asList(taskInfoBo));
                    // 更新任务状态
                    taskInfoBo.setStatus(StockUpdateStatusEnum.SUCCESS.getCode());
                }
                catch (Exception e) {
                    taskInfoBo.setStatus(StockUpdateStatusEnum.FAIL.getCode());
                    taskInfoBo.setErrorReason(e.getMessage().substring(0, 200));
                    log.error("[同步库存]子任务执行失败, taskInfo:{}", taskInfoBo, e);
                    // 如果不是业务异常 则抛出去让mq 重试
                    AssertUtil.throwIfTrue(!(e instanceof BusinessException), "更新库存失败");
                }
                finally {
                    skuStockTaskInfoRepository.updateBatchById(SkuStockChangeTaskConverter.taskInfoBoConvertToTaskInfo(Arrays.asList(taskInfoBo)));
                }
            });
        });
    }

    @Override
    public void rollBackSkuStock(RollBackStockBo rollBackBo) {
        StockUpdateTypeEnum orgUpdateType = rollBackBo.getOrgUpdateType();
        List<String> orgBizCodes = rollBackBo.getOrgBizCodes();
        StockUpdateTypeEnum curUpdateType = StockUpdateTypeEnum.getByCode(orgUpdateType.getRollBackCode());
        AssertUtil.throwIfNull(curUpdateType, "该类型不支持回滚");

        // 此处没有加锁, 分布式锁批量获取不能超过50, 但是此处有可能超过50, 使用了数据库的唯一索引兜底, 保证不重复回滚
        TransactionHelper.doInTransaction(() -> {
            orgBizCodes.forEach(orgBizCode -> {
                String rollBackBizCode = ROLL_BACK_STOCK_PREFIX + orgBizCode;
                int count = skuStockTaskRepository.countByUpdateTypeAndBizCode(curUpdateType.getCode(), rollBackBizCode);
                if (count > 0) {
                    log.info("库存回滚任务已提交, 无需重复提交, rollBackBo:{}", rollBackBo);
                    return;
                }

                // 暂时不支持覆盖库存的回滚
                List<SkuStockTask> orgTaskList = skuStockTaskRepository.listStockTask(orgUpdateType.getCode(), orgBizCode);
                long coverCount = orgTaskList.stream().filter(item -> StockUpdateWayEnum.COVER.getCode().equals(item.getUpdateWay())).count();
                AssertUtil.throwIfTrue(coverCount > 0, "该任务下存在覆盖库存的子任务, 无法回滚");

                // 查询任务明细
                List<Long> taskIds = orgTaskList.stream().map(SkuStockTask::getId).collect(Collectors.toList());
                List<SkuStockTaskInfo> orgTaskInfoList = skuStockTaskInfoRepository.listByTaskIds(taskIds);
                Map<Long, List<SkuStockTaskInfo>> orgTaskMap = orgTaskInfoList.stream().collect(Collectors.groupingBy(SkuStockTaskInfo::getTaskId, Collectors.toList()));

                // 计算出回滚库存的任务
                TransactionHelper.doInTransaction(() -> {
                    orgTaskList.forEach(orgTask -> {
                        // 异步回退库存
                        StockTaskBo rollBackTaskBo = new StockTaskBo();
                        rollBackTaskBo.setUpdateType(curUpdateType);
                        rollBackTaskBo.setUpdateKey(StockUpdateKeyEnum.getByCode(orgTask.getUpdateKey()));
                        rollBackTaskBo.setUpdateWay(StockUpdateWayEnum.getByCode(orgTask.getUpdateWay()));
                        rollBackTaskBo.setBizCode(rollBackBizCode);
                        rollBackTaskBo.setSeqCode(orgTask.getSeqCode());
                        rollBackTaskBo.setTaskInfoBoList(rollBackTaskInfoBuilder(orgTaskMap.get(orgTask.getId())));
                        asyncChangeSkuStock(rollBackTaskBo);
                    });
                });
            });
        });
    }

    /**
     * 实际扣减库存的方法
     *
     * @param task         扣减库存任务
     * @param taskInfoList 扣减库存明细
     */
    private void actualChangeStock(SkuStockTask task, List<StockTaskInfoBo> taskInfoList) {
        if (CollectionUtils.isEmpty(taskInfoList) || task == null) {
            log.warn("[sync stock] task or taskInfo is null");
            return;
        }

        // 获取skuIds 并且进行排序
        List<String> skuIds = taskInfoList.stream().map(StockTaskInfoBo::getSkuId).sorted().collect(Collectors.toList());

        // 修改库存
        TransactionHelper.doInTransaction(() -> {
            // 计算库存变动
            List<SkuStock> skuStockList = skuStockRepository.listBySkuIdsForUpdate(skuIds);
            Map<String, Long> stockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getSkuId, SkuStock::getStock, (k1, k2) -> k1));

            taskInfoList.forEach(taskInfoBo -> {
                if (StockUpdateWayEnum.CHANGE.getCode().equals(task.getUpdateWay())) {
                    // 增减库存
                    calculateStockForChange(taskInfoBo, stockMap.get(taskInfoBo.getSkuId()));
                    skuStockList.add(skuStockBuilder(taskInfoBo.getSkuId(), taskInfoBo.getAfterStock()));
                }
                else {
                    // 覆盖库存
                    calculateStockForCover(taskInfoBo, stockMap.get(taskInfoBo.getSkuId()));
                    skuStockList.add(skuStockBuilder(taskInfoBo.getSkuId(), taskInfoBo.getAfterStock()));
                }
            });

            // todo 该表和 task_info 表重叠 后续可以考虑移除
            skuStockLogRepository.saveBatch(SkuStockChangeTaskConverter.taskInfoBoConvertToStockLog(task.getUpdateWay(), taskInfoList));
            skuStockRepository.updateBatchBySkuId(skuStockList);
        });
    }

    /**
     * 回滚库存任务构建
     *
     * @param sourceTaskInfoList 原库存任务
     * @return 回滚库存任务
     */
    private List<StockTaskInfoBo> rollBackTaskInfoBuilder(List<SkuStockTaskInfo> sourceTaskInfoList) {
        return sourceTaskInfoList.stream().map(item -> {
            StockTaskInfoBo taskInfoBo = new StockTaskInfoBo();
            taskInfoBo.setSkuId(item.getSkuId());
            taskInfoBo.setSkuAutoId(item.getSkuAutoId());
            taskInfoBo.setSkuCode(item.getSkuCode());
            taskInfoBo.setStock(item.getStock() * -1);
            taskInfoBo.setShopId(item.getShopId());
            return taskInfoBo;
        }).collect(Collectors.toList());
    }

    /**
     * 计算库存(增减库存)
     *
     * @param taskInfoBo  库存变动记录
     * @param beforeStock 修改前的库存
     */
    private void calculateStockForChange(StockTaskInfoBo taskInfoBo, Long beforeStock) {
        beforeStock = beforeStock == null ? 0 : beforeStock;
        Long skuAutoId = taskInfoBo.getSkuAutoId();
        Long stock = taskInfoBo.getStock();
        Long afterStock = beforeStock + stock;
        if (afterStock < 0) {
            String skuName = skuAssist.getSkuName(taskInfoBo.getSkuAutoId());
            log.info("库存不足, skuAutoId:{}, skuName:{}, beforeStock:{}, stock:{}, afterStock:{}", skuAutoId, skuName,
                beforeStock, stock, afterStock);
            throw new BusinessException(ProductResultEnum.STOCK_NOT_ENOUGH.getCode(), String.format(ProductResultEnum.STOCK_NOT_ENOUGH.getMsg(), skuName));
        }
        taskInfoBo.setBeforeStock(beforeStock);
        taskInfoBo.setChangeStock(stock);
        taskInfoBo.setAfterStock(afterStock);
        taskInfoBo.setStatus(StockUpdateStatusEnum.SUCCESS.getCode());
    }

    /**
     * 计算库存(覆盖库存)
     *
     * @param taskInfoBo  库存变动记录
     * @param beforeStock 变动前的库存
     */
    private void calculateStockForCover(StockTaskInfoBo taskInfoBo, Long beforeStock) {
        beforeStock = beforeStock == null ? 0 : beforeStock;
        Long stock = taskInfoBo.getStock();
        Long afterStock = stock;
        Long changeStock = afterStock - beforeStock;
        taskInfoBo.setBeforeStock(beforeStock);
        taskInfoBo.setChangeStock(changeStock);
        taskInfoBo.setAfterStock(afterStock);
        taskInfoBo.setStatus(StockUpdateStatusEnum.SUCCESS.getCode());
        log.info("更新库存： skuAutoId:{}, beforeStock:{}, stock:{}, afterStock:{}", taskInfoBo.getSkuAutoId(), beforeStock, stock, afterStock);
    }


    /**
     * 获取任务锁
     *
     * @param lockKey 锁key
     * @return 锁
     */
    private LockKey getTaskLockKey(String lockKey) {
        return new LockKey(LockConstant.STOCK_SYNC_SCENE + lockKey, LockConstant.STOCK_SYNC_LOCK + lockKey);
    }

    /**
     * 初始化任务bo(生成taskId和subTaskId)
     *
     * @param taskBo 任务bo
     * @return 是否终止流程
     */
    private boolean initTaskBo(StockTaskBo taskBo) {
        if (taskBo == null) {
            return true;
        }

        // 如果db中已经有该库存任务 无需新增
        SkuStockTask dbTask = skuStockTaskRepository.getStockTask(taskBo.getUpdateType().getCode(), taskBo.getBizCode(),
            ObjectUtils.defaultIfNull(taskBo.getSeqCode(), StrUtil.EMPTY));
        if (dbTask != null) {
            log.warn("该记录已提交, 无需重复提交, taskBo:{}", taskBo);
            return true;
        }

        return false;
    }

    /**
     * 转换关联id
     * 不管是使用什么字段更新库存, 都需要转换成skuAutoId进行更新
     *
     * @param updateKey      更新key
     * @param taskInfoBoList 任务明细集合
     */
    private void transRelateId(StockUpdateKeyEnum updateKey, List<StockTaskInfoBo> taskInfoBoList) {
        if (CollectionUtils.isEmpty(taskInfoBoList)) {
            return;
        }

        if (CollectionUtils.isEmpty(taskInfoBoList)) {
            return;
        }

        // 调用方传过来的是skuAutoId
        if (StockUpdateKeyEnum.SKU_AUTO_ID.equals(updateKey)) {
            transBySkuAutoId(taskInfoBoList);
            return;
        }

        // 调用方传过来的是skuId
        if (StockUpdateKeyEnum.SKU_ID.equals(updateKey)) {
            transBySkuId(taskInfoBoList);
            return;
        }

        // 调用方传过来的是skuCode
        transBySkuCode(taskInfoBoList);
    }

    /**
     * 根据skuAutoId转换
     *
     * @param taskInfoList 任务详情列表
     */
    private void transBySkuAutoId(List<StockTaskInfoBo> taskInfoList) {
        List<Long> skuAutoIds = taskInfoList.stream().map(StockTaskInfoBo::getSkuAutoId).collect(Collectors.toList());
        List<Sku> skus = skuRepository.listSkuByIds(skuAutoIds);
        Map<Long, Sku> skuMap = skus.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (k1, k2) -> k2));
        taskInfoList.forEach(taskInfo -> {
            Sku sku = skuMap.get(taskInfo.getSkuAutoId());
            if (sku != null) {
                taskInfo.setSkuId(sku.getSkuId());
                taskInfo.setSkuCode(sku.getSkuCode());
                taskInfo.setProductId(sku.getProductId());
                return;
            }

            taskInfo.setStatus(StockUpdateStatusEnum.FAIL.getCode());
            taskInfo.setErrorReason(String.format("skuAutoId不存在【%s】", taskInfo.getSkuAutoId()));
        });
    }

    /**
     * 通过skuId转换
     *
     * @param taskInfoList 任务详情列表
     */
    private void transBySkuId(List<StockTaskInfoBo> taskInfoList) {
        List<String> skuIds = taskInfoList.stream().map(StockTaskInfoBo::getSkuId).collect(Collectors.toList());
        List<Sku> skus = skuRepository.listSkuBySkuIds(skuIds);
        Map<String, Sku> skuMap = skus.stream().collect(Collectors.toMap(Sku::getSkuId, Function.identity(), (k1, k2) -> k2));
        taskInfoList.forEach(taskInfo -> {
            Sku sku = skuMap.get(taskInfo.getSkuId());
            if (sku != null) {
                taskInfo.setSkuAutoId(sku.getId());
                taskInfo.setSkuCode(sku.getSkuCode());
                taskInfo.setProductId(sku.getProductId());
                return;
            }

            taskInfo.setStatus(StockUpdateStatusEnum.FAIL.getCode());
            taskInfo.setErrorReason(String.format("skuId不存在【%s】", taskInfo.getSkuId()));
        });
    }

    /**
     * 通过skuCode转换
     *
     * @param taskInfoList 任务详情列表
     */
    private void transBySkuCode(List<StockTaskInfoBo> taskInfoList) {
        taskInfoList.forEach(taskInfo -> {
            Sku sku = skuRepository.getOne(new LambdaQueryWrapper<Sku>().eq(Sku::getSkuCode, taskInfo.getSkuCode()).eq(Sku::getShopId, taskInfo.getShopId()));
            if (sku != null) {
                taskInfo.setSkuAutoId(sku.getId());
                taskInfo.setSkuId(sku.getSkuId());
                taskInfo.setProductId(sku.getProductId());
                return;
            }

            taskInfo.setStatus(StockUpdateStatusEnum.FAIL.getCode());
            taskInfo.setErrorReason(String.format("skuCode不存在【%s】", taskInfo.getSkuCode()));
        });
    }

    /**
     * 获取锁key
     *
     * @param updateType 更新类型
     * @param bizCode    业务编码
     * @return 锁key
     */
    private String getLockKey(StockUpdateTypeEnum updateType, String bizCode, String seqCode) {
        return String.format(LOCK_KEY_FORMAT, updateType, bizCode, StrUtil.nullToDefault(seqCode, StrUtil.EMPTY));
    }

    /**
     * 构建skuStock
     *
     * @param skuId 规格id
     * @param stock 库存
     * @return skuStock
     */
    private SkuStock skuStockBuilder(String skuId, Long stock) {
        SkuStock skuStock = new SkuStock();
        skuStock.setSkuId(skuId);
        skuStock.setStock(stock);
        return skuStock;
    }
}
