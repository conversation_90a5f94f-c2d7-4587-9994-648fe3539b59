package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplySaveBo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/12 11:03
 */
@Getter
@Setter
public class SaveBrandApplyLogBo {

    @ExaminField(description = "申请记录id")
    private Long id;

    @ExaminField(description = "申请类型")
    private String applyModeDesc;

    @ExaminField(description = "申请品牌")
    private String brandName;

    @ExaminField(description = "品牌logo")
    private String logo;

    @ExaminField(description = "描述")
    private String description;

    @ExaminField(description = "授权证书")
    private List<String> authCertificateList;

    @ExaminField(description = "备注")
    private String remark;

    public static SaveBrandApplyLogBo build(BrandApplySaveBo saveBo) {
        if(saveBo == null) {
            return null;
        }

        SaveBrandApplyLogBo bo = JsonUtil.copy(saveBo, SaveBrandApplyLogBo.class);
        if (saveBo.getApplyMode() != null) {
            bo.setApplyModeDesc(saveBo.getApplyMode().name());
        }
        return bo;
    }

    public static SaveBrandApplyLogBo build(BrandApplyBo applyBo) {
        if (applyBo == null) {
            return null;
        }

        return JsonUtil.copy(applyBo, SaveBrandApplyLogBo.class);
    }

}
