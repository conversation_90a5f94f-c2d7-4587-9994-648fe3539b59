package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.core.model.bo.DiscountActiveQueryBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.DiscountActiveSaveBo;
import com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveSimpleDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;

import java.util.Collection;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
public interface DiscountActiveService {

    /**
     * 保存活动
     *
     * @param saveBo
     */
    void save(DiscountActiveSaveBo saveBo);

    /**
     * 分页查询活动
     *
     * @param queryBo
     * @return
     */
    BasePageResp<DiscountActiveSimpleDto> pageList(DiscountActiveQueryBo queryBo);

    /**
     * 结束活动
     *
     * @param baseIdReq
     */
    void endActive(BaseIdReq baseIdReq);

    /**
     * 根据id查询活动
     *
     * @param idReq
     * @return
     */
    DiscountActiveResp getById(BaseIdReq idReq);

    /**
     * 根据商品id查询活动
     *
     * @param request
     * @return
     */
    DiscountActiveResp queryByProductId(ProductAndShopIdReq request);

    /**
     * 分页查询折扣商品
     *
     * @param req 搜索入参
     * @return 商品集合
     */
    BasePageResp<DiscountActiveProductDto> queryDiscountActiveProduct(QueryDiscountActiveProductReq req);

        /**
     * 根据指定商品Ids查询当前时间运行中的折扣活动
     *
     * @return
     */
    List<DiscountActiveResp> selectCurrentDiscountByProduct(Collection<Long> productIds);
}
