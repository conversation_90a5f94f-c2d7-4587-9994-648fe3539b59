package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class FullReductionQueryBo extends BasePageReq {

    //主键ID
    private Long id;

    //店铺ID
    private Long shopId;

    //店铺名称
    private String shopName;

    //满减活动名称
    private String activeName;

    //店铺ID列表
    private List<Long> shopIdList;

    //状态 0-未开始 1-进行中 2-已结束
    private Integer status;

    //开始时间
    private Date startTime;

    //结束时间
    private Date endTime;

}
