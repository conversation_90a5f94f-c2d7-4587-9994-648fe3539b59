package com.sankuai.shangou.seashop.product.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.product.core.mq.model.ProductMessage;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品信息更新监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConstant.DEFAULT_NAMESPACE,
//        topic = MafkaConstant.TOPIC_PRODUCT_AUDIT_UPDATE,
//        group = MafkaConstant.GROUP_ES_PRODUCT_AUDIT_BUILD)
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_PRODUCT_AUDIT_UPDATE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.GROUP_ES_PRODUCT_AUDIT_BUILD + "_${spring.profiles.active}")
public class ProductAuditUpdateListener implements RocketMQListener<MessageExt> {

    @Resource
    private ProductEsBuildService productEsBuildService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            log.info("【mafka消费】【商品审核表变动】消息内容为: {}", body);
            DbTableDataChangeMessage<ProductMessage> messageWrapper = JsonUtil.parseObject(body,
                new TypeReference<DbTableDataChangeMessage<ProductMessage>>() {
            });
            if (messageWrapper.getData() == null) {
                return;
            }
            productEsBuildService.buildProductAuditEs(messageWrapper.getData().getProductId());
        }
        catch (Exception e) {
            log.warn("【mafka消费】【商品审核表变动】消息内容为: body: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
