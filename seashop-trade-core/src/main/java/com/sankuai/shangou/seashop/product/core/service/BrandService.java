package com.sankuai.shangou.seashop.product.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.product.core.service.model.BatchQueryBranchParamBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandCacheBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandGroupBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandDto;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:08
 */
public interface BrandService {

    /**
     * 创建品牌
     *
     * @param brand 品牌
     */
    void createBrand(Brand brand);

    /**
     * 更新品牌
     *
     * @param brand 品牌
     */
    void updateBrand(Brand brand);

    /**
     * 删除品牌
     *
     * @param id 品牌id
     */
    void deleteBrand(Long id);

    /**
     * 分页查询品牌
     *
     * @param page         分页参数
     * @param brandQueryBo 筛选条件
     * @return 分页对象
     */
    BasePageResp<Brand> pageBrand(BasePageParam page, BrandQueryBo brandQueryBo);

    /**
     * 批量查询品牌列表
     *
     * @param paramBo 参数
     * @return 品牌列表
     */
    List<BrandDto> queryBrandList(BatchQueryBranchParamBo paramBo);

    /**
     * 查询没有申请记录的品牌列表
     *
     * @param shopId 供应商id
     * @return 品牌列表
     */
    List<Brand> queryNotApplyBrand(Long shopId);

    /**
     * 查询没有申请记录的品牌分组
     *
     * @param shopId 供应商id
     * @return 品牌分组
     */
    List<BrandGroupBo> queryNotApplyBrandGroup(Long shopId);

    /**
     * 查询品牌详情
     *
     * @param id 品牌id
     * @return 品牌详情
     */
    Brand queryBrandDetail(Long id);

    /**
     * 查询推荐品牌
     *
     * @param categoryId 类目id
     * @return 推荐品牌列表
     */
    List<Brand> queryRecommendBrand(Long categoryId);

    /**
     * 根据品牌名称查询品牌id
     *
     * @param brandName 品牌名称
     * @return 品牌id列表
     */
    List<Long> queryBrandIdsByName(String brandName);

    /**
     * 查询品牌列表(读取缓存)
     *
     * @return 品牌列表
     */
    List<BrandCacheBo> listBrandCache();

    /**
     * 清理品牌缓存
     */
    void removeBrandCache();

    /**
     * 按名称查询品牌
     * @param name
     * @return
     */
    Brand queryByName(String name);
}
