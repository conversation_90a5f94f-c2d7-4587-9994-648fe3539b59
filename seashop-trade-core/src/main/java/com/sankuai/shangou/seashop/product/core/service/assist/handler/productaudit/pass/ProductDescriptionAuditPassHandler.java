package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.pass;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescription;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/24 16:13
 */
@Component
@Slf4j
public class ProductDescriptionAuditPassHandler extends AbsProductAuditPassHandler {

    @Resource
    private ProductDescriptionAuditRepository productDescriptionAuditRepository;
    @Resource
    private ProductDescriptionRepository productDescriptionRepository;

    @Override
    protected void handle(ProductContext context) {
        log.info("【商品审核通过】保存商品详情数据==开始, context={}", context);

        Long productId = context.getProductId();
        ProductDescriptionAudit descriptionAudit = productDescriptionAuditRepository.getProductDescriptionByProductId(productId);
        AssertUtil.throwIfNull(descriptionAudit, "未找到审核详情记录");
        ProductDescription productDescription = JsonUtil.copy(descriptionAudit, ProductDescription.class);
        productDescriptionRepository.updateByProductId(productDescription);

        log.info("【商品审核通过】保存商品详情数据==结束, context={}", context);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.PASS_PRODUCT_DESCRIPTION_AUDIT;
    }

}
