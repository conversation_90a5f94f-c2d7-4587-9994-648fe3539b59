package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.handler;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.AbsActivityQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 专属价查询处理器
 *
 * <AUTHOR>
 * @date 2023/12/23 10:00
 */
@Component
@Slf4j
public class ExclusivePriceQueryHandler extends AbsActivityQueryHandler {

    @Resource
    private PromotionRemoteService promotionRemoteService;

    @Override
    protected void query(ActivityContext context) {
        log.info("查询专属价活动, context: {}", context);
        if (context.getUserId() == null) {
            return;
        }
        context.setExclusivePrice(promotionRemoteService.getExclusivePrice(context.getProductId(), context.getUserId()));
        log.info("查询专属价活动结果, context: {}, activity: {}", context, JsonUtil.toJsonString(context.getExclusivePrice()));
    }

    @Override
    protected Class<? extends AbsActivityQueryHandler> nextHandler() {
        return LadderPriceQueryHandler.class;
    }

    /**
     * 专享价和所有的活动均互斥, 但是其优先级只低于限时购
     * 所以只有没有参加限时购活动时, 才参加专享价活动
     * 但是由于限时购的页面存在原价购买的按钮, 所以限时购是否参加不会影响其他活动
     *
     * @param context 活动上下文对象
     * @return true 支持，false 不支持
     */
    @Override
    protected boolean support(ActivityContext context) {
        return true;
    }
}
