package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.submit;

import java.util.Arrays;
import java.util.List;

import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;

/**
 * 抽象提交审核处理器
 *
 * <AUTHOR>
 * @date 2023/11/23 19:44
 */
public abstract class AbsSubmitProductAuditHandler extends AbsProductHandler {
    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.SUBMIT_PRODUCT_AUDIT);
    }
}
