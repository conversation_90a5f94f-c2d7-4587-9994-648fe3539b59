package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.common.remote.user.RemoteFreightAreaService;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;

import lombok.extern.slf4j.Slf4j;

/**
 * 查询运费模板处理器
 *
 * <AUTHOR>
 * @date 2024/04/08 10:26
 */
@Component
@Slf4j
public class QueryFreightTemplateHandler extends AbsProductHandler {

    @Resource
    private RemoteFreightAreaService remoteFreightAreaService;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        ProductBo oldProductBo = context.getOldProductBo();
        Long freightTemplateId = oldProductBo.getFreightTemplateId();
        if (freightTemplateId == null || freightTemplateId == 0) {
            return;
        }

        QueryFreightTemplateDto template = remoteFreightAreaService.queryTplByTemplateId(freightTemplateId);
        if (template != null) {
            oldProductBo.setFreightTemplateName(template.getName());
            oldProductBo.setFreightTemplateIdMethod(template.getValuationMethod());
        }
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_BRAND;
    }
}
