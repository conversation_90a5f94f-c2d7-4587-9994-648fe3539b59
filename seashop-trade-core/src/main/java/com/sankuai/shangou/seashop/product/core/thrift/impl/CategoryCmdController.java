package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.CategoryService;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.SaveCategoryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.BindCustomFormReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.SaveCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.UpdateCategoryParamReq;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:22
 */
@RestController
@RequestMapping("/category")
public class CategoryCmdController implements CategoryCmdFeign {

    @Resource
    private CategoryService categoryService;

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = SaveCategoryReq.class,
            entity = Category.class,
            actionName = "新增类目")
    @PostMapping(value = "/createCategory", consumes = "application/json")
    public ResultDto<BaseResp> createCategory(@RequestBody SaveCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createCategory", request, req -> {
            req.checkParameter();

            categoryService.saveCategory(JsonUtil.copy(req, SaveCategoryBo.class));
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MODIFY,
            dto = SaveCategoryReq.class,
            entity = Category.class,
            actionName = "编辑类目")
    @PostMapping(value = "/updateCategory", consumes = "application/json")
    public ResultDto<BaseResp> updateCategory(@RequestBody SaveCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategory", request, req -> {
            req.checkForEdit();

            categoryService.saveCategory(JsonUtil.copy(req, SaveCategoryBo.class));
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MODIFY,
            dto = UpdateCategoryParamReq.class,
            entity = Category.class,
            actionName = "更新类目属性")
    @PostMapping(value = "/updateCategoryParam", consumes = "application/json")
    public ResultDto<BaseResp> updateCategoryParam(@RequestBody UpdateCategoryParamReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateCategoryParam", request, req -> {
            req.checkParameter();

            categoryService.updateCategoryParam(JsonUtil.copy(req, SaveCategoryBo.class));
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MOVE,
            dto = BaseIdReq.class,
            entity = Category.class,
            actionName = "删除类目")
    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    public ResultDto<BaseResp> deleteCategory(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteCategory", request, req -> {
            req.checkParameter();

            categoryService.deleteCategory(request.getId());
            return new BaseResp();
        });
    }

    @Override
    /*@ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MODIFY,
            dto = BindCustomFormReq.class,
            entity = Category.class,
            actionName = "类目关联自定义表单")*/
    @PostMapping(value = "/bindCustomForm", consumes = "application/json")
    public ResultDto<BaseResp> bindCustomForm(@RequestBody BindCustomFormReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("bindCustomForm", request, req -> {
            req.checkParameter();

            categoryService.bindCustomForm(JsonUtil.copy(req, CategoryBo.class));
            return new BaseResp();
        });
    }
}
