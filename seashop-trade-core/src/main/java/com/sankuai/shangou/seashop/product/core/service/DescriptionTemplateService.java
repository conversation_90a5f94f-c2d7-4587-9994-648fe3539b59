package com.sankuai.shangou.seashop.product.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.product.core.service.model.DescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.core.service.model.DescriptionTemplateQueryBo;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:28
 */
public interface DescriptionTemplateService {

    /**
     * 保存版式
     *
     * @param descriptionTemplateBo 版式
     */
    void saveDescriptionTemplate(DescriptionTemplateBo descriptionTemplateBo);

    /**
     * 分页查询版式
     *
     * @param pageParam 分页参数
     * @param queryBo   查询条件
     * @return 版式列表
     */
    BasePageResp<DescriptionTemplateBo> pageDescriptionTemplate(BasePageParam pageParam, DescriptionTemplateQueryBo queryBo);

    /**
     * 查询版式详情
     *
     * @param id     版式id
     * @param shopId 店铺id
     * @return 版式详情
     */
    DescriptionTemplateBo queryDescriptionTemplateDetail(Long id, Long shopId);

    /**
     * 删除版式
     *
     * @param id     版式id
     * @param shopId 店铺id
     */
    void deleteDescriptionTemplate(Long id, Long shopId);

    /**
     * 查询版式列表
     *
     * @param queryBo 查询参数
     * @return 版式列表
     */
    List<DescriptionTemplateBo> listDescriptionTemplate(DescriptionTemplateQueryBo queryBo);
}
