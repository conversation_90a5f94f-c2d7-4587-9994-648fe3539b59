package com.sankuai.shangou.seashop.trade.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.trade.thrift.core.request.finance.ApiCashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositRefundResp;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
public interface ApiCashDepositRefundService {

    /**
     * 通过条件查询保证金退款明细信息
     *
     * @param request
     * @return
     */
    BasePageResp<ApiCashDepositRefundResp> refundList(ApiCashDepositRefundQueryReq request);

    /**
     * 通过ID查询审核信息
     *
     * @param id
     * @return
     */
    ApiCashDepositRefundDetailResp refundDetail(Long id);
}
