package com.sankuai.shangou.seashop.product.core.service;

import com.sankuai.shangou.seashop.base.eimport.ImportResult;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductImportBo;

/**
 * <AUTHOR>
 * @date 2023/12/05 18:05
 */
public interface ProductImportService {

    /**
     * 商品库存导入
     *
     * @param importBo 导入参数
     * @return 导入结果
     */
    ImportResult importStock(ProductImportBo importBo);

    /**
     * 商品下架导入
     *
     * @param importBo 导入参数
     * @return 导入结果
     */
    ImportResult importOffSale(ProductImportBo importBo);

    /**
     * 商品违规下架导入
     *
     * @param importBo 导入参数
     * @return 导入结果
     */
    ImportResult importViolationOffSale(ProductImportBo importBo);

    /**
     * 导入商品数据
     *
     * @param importBo 导入参数
     * @return 导入结果
     */
    ImportResult importProduct(ProductImportBo importBo);

    /**
     * 导入价格
     *
     * @param importBo 导入参数
     * @return 导入结果
     */
    ImportResult importPrice(ProductImportBo importBo);

    /**
     * 导入商品更新
     *
     * @param importBo 导入参数
     * @return 导入结果
     */
    ImportResult importProductUpdate(ProductImportBo importBo);

    /**
     * 平台导入商品
     *
     * @param importBo 导入参数
     * @return 导入结果
     */
    ImportResult platformImportProduct(ProductImportBo importBo);
}
