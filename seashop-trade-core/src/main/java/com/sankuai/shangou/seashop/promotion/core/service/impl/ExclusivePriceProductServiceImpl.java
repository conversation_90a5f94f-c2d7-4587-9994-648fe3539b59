package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductSkuMergeDto;
import com.sankuai.shangou.seashop.promotion.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.promotion.core.service.ExclusivePriceProductService;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePrice;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePriceProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceProductParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.ExclusivePriceProductRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.ExclusivePriceRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductPageQryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceProductSimpleResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@Service
@Slf4j
public class ExclusivePriceProductServiceImpl implements ExclusivePriceProductService {

    @Resource
    private ExclusivePriceProductRepository exclusivePriceProductRepository;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private MemberRemoteService memberRemoteService;
    @Resource
    private ExclusivePriceRepository exclusivePriceRepository;

    @Override
    public BasePageResp<ExclusivePriceProductDto> pageList(ExclusivePriceProductPageQryReq request) {

        Page<ExclusivePriceProduct> exclusivePriceProductPage = exclusivePriceProductRepository.pageByActiveId(request.buildPage(), JsonUtil.copy(request, ExclusivePriceProductParamDto.class));
        if (null == exclusivePriceProductPage || CollUtil.isEmpty(exclusivePriceProductPage.getResult())) {
            return PageResultHelper.defaultEmpty(request.buildPage());
        }

        List<String> skuIdList = exclusivePriceProductPage.getResult().stream().map(ExclusivePriceProduct::getSkuId).collect(Collectors.toList());
        List<ProductSkuMergeDto> productSkuList = productRemoteService.getProductSkuMergeList(skuIdList);

        List<Long> memberIds = exclusivePriceProductPage.getResult().stream().map(ExclusivePriceProduct::getMemberId).collect(Collectors.toList());
        List<MemberResp> memberList = memberRemoteService.queryMembers(memberIds);
        Map<Long, MemberResp> memberMap = memberList.stream().collect(Collectors.toMap(MemberResp::getId, Function.identity(), (k1, k2) -> k1));

        return PageResultHelper.transfer(exclusivePriceProductPage, ExclusivePriceProductDto.class, exclusivePriceProductDto -> {
            if (null != productSkuList) {
                productSkuList.stream().filter(productSkuMergeDto -> productSkuMergeDto.getSkuId().equals(exclusivePriceProductDto.getSkuId())).findFirst().ifPresent(productSkuMergeDto -> {
                    exclusivePriceProductDto.setProductName(productSkuMergeDto.getProductName());
                    exclusivePriceProductDto.setSkuName(productSkuMergeDto.getSkuName());
                    exclusivePriceProductDto.setMallPrice(productSkuMergeDto.getSalePrice());
                    exclusivePriceProductDto.setSkuAutoId(productSkuMergeDto.getSkuAutoId());
                });
            }

            MemberResp member = memberMap.get(exclusivePriceProductDto.getMemberId());
            if (member != null) {
                exclusivePriceProductDto.setUserName(member.getUserName());
            }
        });
    }

    @Override
    public ExclusivePriceProductSimpleResp getShopValidExclusive(Long shopId) {
        ExclusivePriceProductSimpleResp resp = new ExclusivePriceProductSimpleResp();
        // 获取当前有效的专享价活动
        List<ExclusivePrice> exclusivePriceList = exclusivePriceRepository.getValidByShopId(shopId);
        log.info("shopId:{},exclusivePriceList:{}", shopId, JsonUtil.toJsonString(exclusivePriceList));
        if(CollUtil.isEmpty(exclusivePriceList)) {
            return resp;
        }
        List<Long> activityIdList = exclusivePriceList.stream().map(ExclusivePrice::getId).collect(Collectors.toList());
        // 获取专享价活动对象的商品
        List<ExclusivePriceProduct> productList = exclusivePriceProductRepository.listByActiveIds(activityIdList);
        if (CollUtil.isEmpty(productList)) {
            return resp;
        }
        List<ExclusivePriceProductDto> resultList = JsonUtil.copyList(productList, ExclusivePriceProductDto.class);
        resp.setResultList(resultList);
        return resp;
    }
}
