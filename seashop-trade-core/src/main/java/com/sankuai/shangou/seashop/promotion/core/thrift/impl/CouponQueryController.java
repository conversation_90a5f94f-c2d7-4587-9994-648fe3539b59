package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.CouponService;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductAvailableQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.ProductAvailableQueryResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@RestController
@RequestMapping("/coupon")
public class CouponQueryController implements CouponQueryFeign {

    @Resource
    private CouponService couponService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<CouponSimpleResp>> pageList(@RequestBody CouponQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            return couponService.pageList(req);
        });
    }

    @GetMapping(value = "/flagHasCoupons")
    @Override
    public ResultDto<Boolean> flagHasCoupons(@RequestParam Long shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("flagHasCoupons", shopId, req -> {
            return couponService.flagHasCoupons(req);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @Override
    public ResultDto<CouponResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            return couponService.getById(req.getId());
        });
    }

    @GetMapping(value = "/queryAvailableCouponCountByUser")
    @Override
    public ResultDto<Integer> queryAvailableCouponCountByUser(@RequestParam Long userId) throws TException {
        if (userId == null) {
            return ResultDto.newWithData(null);
        }
        return ThriftResponseHelper.responseInvoke("queryAvailableCouponCountByUser", userId, req -> couponService.queryAvailableCouponCountByUser(req));
    }

    @PostMapping(value = "/queryByProductId", consumes = "application/json")
    @Override
    public ResultDto<CouponSimpleListResp> queryByProductId(@RequestBody ProductAndShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryByProductId", request, req -> {
            req.checkParameter();
            return couponService.queryByProductId(req);
        });
    }

    @PostMapping(value = "/getByIdList", consumes = "application/json")
    @Override
    public ResultDto<CouponSimpleListResp> getByIdList(@RequestBody BaseBatchIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryByProductId", request, req -> {
            req.checkParameter();
            return couponService.getByIdList(request);
        });
    }

    @PostMapping(value = "/queryCouponProductPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<CouponProductDto>> queryCouponProductPage(@RequestBody CouponProductQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCouponProductPage", request, req -> {
            req.checkParameter();

            return couponService.queryCouponProductPage(req);
        });
    }

    @PostMapping(value = "/getByMessageId", consumes = "application/json")
    @Override
    public ResultDto<CouponSimpleListResp> getByMessageId(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getByMessageId", request, req -> {
            req.checkParameter();
            return couponService.getByMessageId(request);
        });
    }

    @PostMapping(value = "/calculateUsageRate", consumes = "application/json")
    @Override
    public ResultDto<String> calculateUsageRate(@RequestBody BaseIdReq msgId) throws TException{
        return ThriftResponseHelper.responseInvoke("calculateUsageRate",msgId,req->{
            return couponService.calculateUsageRate(req);
        });
    }

    @PostMapping(value = "/getProductAvailableCouponList", consumes = "application/json")
    @Override
    public ResultDto<ProductAvailableQueryResp> getProductAvailableCouponList(@RequestBody ProductAvailableQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getProductAvailableCouponList", request, req -> {
            req.checkParameter();
            return couponService.getProductAvailableCouponList(req);
        });
    }

    @GetMapping(value = "/queryRelateProductIds")
    @Override
    public ResultDto<List<Long>> queryRelateProductIds(@RequestParam Long couponId) throws TException {
        return ThriftResponseHelper.responseInvoke("queryRelateProductIds", couponId, req -> {

            return couponService.queryRelateProductIds(req);
        });
    }
}
