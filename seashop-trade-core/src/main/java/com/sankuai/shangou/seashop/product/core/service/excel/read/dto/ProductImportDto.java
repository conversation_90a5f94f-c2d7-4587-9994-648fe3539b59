package com.sankuai.shangou.seashop.product.core.service.excel.read.dto;

import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/23 16:50
 */
@Getter
@Setter
public class ProductImportDto extends RowReadResult {

    @ExcelProperty(value = "商品货号")
    private String productCode;

    @ExcelProperty(value = "*商品名称")
    // @ExcelField(required = true)
    private String productName;

    @ExcelProperty(value = "商品详情")
    private String description;

    @ExcelProperty(value = "*平台一级分类")
    // @ExcelField(required = true)
    private String firstCategoryName;

    @ExcelIgnore
    private Long firstCategoryId;

    @ExcelProperty(value = "*平台二级分类")
    // @ExcelField(required = true)
    private String secondCategoryName;

    @ExcelIgnore
    private Long secondCategoryId;

    @ExcelProperty(value = "*平台三级分类")
    // @ExcelField(required = true)
    private String thirdCategoryName;

    @ExcelIgnore
    private Long thirdCategoryId;

    @ExcelIgnore
    private String categoryPath;

    @ExcelProperty(value = "广告词")
    private String shortDescription;

    @ExcelProperty(value = "*品牌")
    // @ExcelField(required = true)
    private String brandName;

    @ExcelIgnore
    private Long brandId;

    @ExcelProperty(value = "市场价")
    // @ExcelField(regexEnum = FieldRegexEnum.AMOUNT_REGEX)
    private String marketPrice;

    @ExcelProperty(value = "*店铺分类")
    // @ExcelField(required = true)
    private String shopCategoryName;

    @ExcelIgnore
    private Long shopCategoryId;

    @ExcelProperty(value = "*计量单位")
    // @ExcelField(required = true)
    private String measureUnit;

    @ExcelProperty(value = "商品主图")
    private String imagePath;

    @ExcelProperty(value = "规格")
    private String specs;

    @ExcelProperty(value = "*库存")
    // @ExcelField(regexEnum = FieldRegexEnum.NON_NEGATIVE_INTEGER_REGEX)
    private String stock;

    @ExcelProperty(value = "*商城价")
    // @ExcelField(required = true, regexEnum = FieldRegexEnum.AMOUNT_REGEX)
    private String salePrice;

    @ExcelProperty(value = "*运费模板")
    // @ExcelField(required = true)
    private String freightTemplateName;

    @ExcelIgnore
    private Long freightTemplateId;

    @ExcelIgnore
    private Long shopId;

    @ExcelIgnore
    private Boolean hasSku;

    @ExcelIgnore
    private List<ProductSkuBo> skuList;

    @ExcelIgnore
    private StringBuilder errBuilder = new StringBuilder();

    @ExcelIgnore
    private List<String> imageList;

    @ExcelIgnore
    private String spec1Alias;

    @ExcelIgnore
    private String spec2Alias;

    @ExcelIgnore
    private String spec3Alias;

    @ExcelProperty(value = "重量")
    private String weight;

    @ExcelProperty(value = "体积")
    private String volume;


}
