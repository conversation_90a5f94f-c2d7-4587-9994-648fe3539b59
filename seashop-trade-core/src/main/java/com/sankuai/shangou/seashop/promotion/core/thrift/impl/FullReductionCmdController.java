package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.LockConstant;
import com.sankuai.shangou.seashop.promotion.core.model.bo.FullReductionSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.FullReductionService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FullReductionCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@RestController
@RequestMapping("/fullReduction")
public class FullReductionCmdController implements FullReductionCmdFeign {

    @Resource
    private FullReductionService fullReductionService;

    @Override
    /*@ExaminProcess(processModel = ExaminModelEnum.PROMOTION,
            processType = ExaProEnum.MODIFY,
            dto = FullReductionSaveReq.class,
            entity = FullReduction.class,
            actionName = "保存满减活动")*/
    @PostMapping(value = "/save", consumes = "application/json")
    public ResultDto<BaseResp> save(@RequestBody FullReductionSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            req.checkParameter();
            FullReductionSaveBo saveBo = JsonUtil.copy(req, FullReductionSaveBo.class);
            String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_SUBMIT_KEY, saveBo.getShopId());
            LockHelper.lock(lockKey, () -> {
                fullReductionService.save(saveBo);
            });
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/endActive", consumes = "application/json")
    @Override
    /*@ExaminProcess(processModel = ExaminModelEnum.PROMOTION,
            processType = ExaProEnum.MODIFY,
            dto = BaseIdReq.class,
            entity = FullReduction.class,
            actionName = "结束满减活动")*/
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("endActive", request, req -> {
            req.checkParameter();
            fullReductionService.endActive(req);
            return BaseResp.of();
        });
    }
}
