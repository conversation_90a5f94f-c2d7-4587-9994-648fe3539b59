package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.hepler.SkuIdHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.dto.SpecDto;
import com.sankuai.shangou.seashop.product.thrift.core.helper.SkuHelper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/07/16 15:26
 */
@Component
@Slf4j
public class CheckSpecHandler extends AbsProductHandler {
    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】校验规格【start】, context:{}", context);
        ProductBo productBo = context.getSaveProductBo();
        List<ProductSkuBo> skuList = productBo.getSkuList();
        AssertUtil.throwIfTrue(CollUtil.isEmpty(skuList), "请至少设置一个sku信息");

        // 单规格商品 只能有一个规格信息
        if (productBo.getHasSku() != null && !productBo.getHasSku()) {
            AssertUtil.throwIfTrue(skuList.size() != 1, "无规格商品只能设置一个sku信息");
            resetProductSpec(productBo);
            skuList.forEach(sku -> {
                sku.setSpecList(Collections.emptyList());
                resetSkuSpec(sku);
            });
        }

        // 校验并且初始化规格信息 todo 最好可以校验一下每个sku的specList 规则是否一致
        skuList.forEach(sku -> {
            checkAndInitSpec(productBo, sku);

            // 重置一下skuId 第一位是productId 后面是规格值Id
            SkuIdHelper.SkuIdDto skuIdDto = SkuIdHelper.getSkuIdDto(sku.getSkuId());
            sku.setSkuId(SkuHelper.generateSkuId(context.getProductId(),
                Arrays.asList(skuIdDto.getSpec1ValueId(), skuIdDto.getSpec2ValueId(), skuIdDto.getSpec3ValueId())));
        });
        log.info("【保存商品】校验规格【end】, context:{}", context);
    }

    private void checkAndInitSpec(ProductBo product, ProductSkuBo sku) {
        List<SpecDto> specList = sku.getSpecList();
        if (CollUtil.isEmpty(specList)) {
            return;
        }

        resetProductSpec(product);
        resetSkuSpec(sku);
        int size = NumberUtil.min(specList.size(), ParameterConstant.MAX_SPEC_SIZE);
        List<Long> specValueIds = new ArrayList<>();
        List<Long> specNameIds = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            SpecDto spec = specList.get(i);
            AssertUtil.throwIfNull(spec.getNameId(), "规格Id不能为空");
            AssertUtil.throwIfTrue(StrUtil.isEmpty(spec.getSpecName()), "规格名称不能为空");
            AssertUtil.throwIfTrue(StrUtil.isEmpty(spec.getSpecAlias()), "规格别名不能为空");
            AssertUtil.throwIfTrue(StrUtil.isEmpty(spec.getSpecValue()), "规格值不能为空");
            AssertUtil.throwIfNull(spec.getValueId(), "规格值Id不能为空");

            if (i == 0) {
                sku.setSpec1ValueId(spec.getValueId());
                sku.setSpec1Value(spec.getSpecValue());
                product.setSpec1Alias(spec.getSpecAlias());
            } else if (i == 1) {
                sku.setSpec2ValueId(spec.getValueId());
                sku.setSpec2Value(spec.getSpecValue());
                product.setSpec2Alias(spec.getSpecAlias());
            } else if (i == 2) {
                sku.setSpec3ValueId(spec.getValueId());
                sku.setSpec3Value(spec.getSpecValue());
                product.setSpec3Alias(spec.getSpecAlias());
            }

            if (spec.getValueId() != null) {
                specValueIds.add(spec.getValueId());
            }

            if (spec.getNameId() != null) {
                specNameIds.add(spec.getNameId());
            }
        }

        sku.setSkuId(SkuHelper.generateSkuId(product.getProductId(), specValueIds));
        sku.setSpecValueJson(JSONUtil.toJsonStr(specList));
        product.setSpecNameIds(StrUtil.join(StrUtil.COMMA, specNameIds));
    }

    private void resetProductSpec(ProductBo product) {
        product.setSpec1Alias(StrUtil.EMPTY);
        product.setSpec2Alias(StrUtil.EMPTY);
        product.setSpec3Alias(StrUtil.EMPTY);
        product.setSpecNameIds(StrUtil.EMPTY);
    }

    private void resetSkuSpec(ProductSkuBo sku) {
        sku.setSpec1ValueId(ParameterConstant.DEFAULT_SPEC_VALUE_ID);
        sku.setSpec2ValueId(ParameterConstant.DEFAULT_SPEC_VALUE_ID);
        sku.setSpec3ValueId(ParameterConstant.DEFAULT_SPEC_VALUE_ID);
        sku.setSpec1Value(StrUtil.EMPTY);
        sku.setSpec2Value(StrUtil.EMPTY);
        sku.setSpec3Value(StrUtil.EMPTY);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getSkuList() != null;
    }


    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_SPEC;
    }
}
