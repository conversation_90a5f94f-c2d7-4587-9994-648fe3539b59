package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/7/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class CouponSaveBo extends BaseParamReq {

    //主键ID
    @PrimaryField
    private Long id;

    //店铺ID
    private Long shopId;

    //店铺名称
    private String shopName;

    //面值(价格)
    @ExaminField
    private BigDecimal price;

    //最大可领取张数
    @ExaminField
    private Integer perMax;

    //订单金额（满足多少钱才能使用）
    @ExaminField
    private BigDecimal orderAmount;

    //发行张数
    @ExaminField
    private Integer num;

    //开始时间
    @ExaminField
    private Date startTime;

    //结束时间
    @ExaminField
    private Date endTime;

    //优惠券名称
    @ExaminField
    private String couponName;

    //领取方式 0 店铺首页 1 积分兑换 2 主动发放
    @ExaminField
    private Integer receiveType;

    //使用范围：0=全场通用，1=部分商品可用
    @ExaminField
    private Integer useArea;

    //备注
    private String remark;

    //产品ID列表
    private List<Long> productIdList;

    //推广方式：0 平台；4 移动端(小程序)
    private List<Integer> platForm;
}