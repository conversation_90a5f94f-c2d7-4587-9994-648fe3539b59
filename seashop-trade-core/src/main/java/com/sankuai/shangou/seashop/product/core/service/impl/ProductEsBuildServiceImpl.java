package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributeLock;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentSummaryResp;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductCommentModel;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductModel;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductAuditService;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductService;
import com.sankuai.shangou.seashop.product.common.remote.order.OrderQueryRemoteService;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAuditAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.converter.EsProductConverter;
import com.sankuai.shangou.seashop.product.core.service.converter.ProductConverter;
import com.sankuai.shangou.seashop.product.core.service.model.UpdateVisitCountBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductEsBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockRepository;
import com.sankuai.shangou.seashop.trade.common.remote.UserFavoriteRemoteService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/12 10:00
 */
@Service
@Slf4j
public class ProductEsBuildServiceImpl implements ProductEsBuildService {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private ProductImageRepository productImageRepository;
    @Resource
    private ProductAssist productAssist;
    @Resource
    private ProductAuditAssist productAuditAssist;
    @Resource
    private ProductAuditRepository productAuditRepository;
    @Resource
    private ProductImageAuditRepository productImageAuditRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuAuditRepository skuAuditRepository;
    @Resource
    private EsProductService esProductService;
    @Resource
    private EsProductAuditService esProductAuditService;
    @Resource
    private SkuStockRepository skuStockRepository;
    @Resource
    private UserFavoriteRemoteService userFavoriteRemoteService;
    @Resource
    private OrderQueryRemoteService orderQueryRemoteService;

    @Override
    public ProductEsBo queryProductForEsBuild(Long productId) {
        Product product = productRepository.getByProductId(productId);
        if (product == null) {
            return null;
        }

        ProductEsBo bo = ProductConverter.convertToEsBo(product);
        // 查询商品图片
        bo.setImgList(productImageRepository.listImagesByProductId(product.getProductId()));
        // 查询店铺分类id
        bo.setShopCategoryIds(productAssist.getShopCategoryIds(productId));
        // 查询审核原因
        bo.setAuditReason(productAssist.getAuditReason(product));

        List<Sku> skuList = skuRepository.listByProductIds(Collections.singletonList(productId));
        bo.setSkuAutoIds(skuList.stream().map(Sku::getId).collect(Collectors.toList()));
        bo.setSkuCodes(skuList.stream().map(Sku::getSkuCode).filter(Objects::nonNull).collect(Collectors.toList()));
        bo.setSkuIds(skuList.stream().map(Sku::getSkuId).filter(Objects::nonNull).collect(Collectors.toList()));
        bo.setMaxSalePrice(productAssist.calculateMaxSalePrice(productId));
        bo.setTotalStock(skuStockRepository.getTotalStock(productId));
        // 规格名称的集合
        if (StrUtil.isNotEmpty(product.getSpecNameIds())) {
            bo.setSpecNameIds(StrUtil.split(product.getSpecNameIds(), StrUtil.COMMA).stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        return bo;
    }

    @Override
    @DistributeLock(keyPattern = LockConstant.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConstant.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    public void updateStockEs(Long productId) {
        Product product = productRepository.getByProductId(productId);
        if (product == null || product.getWhetherDelete()) {
            return;
        }

        esProductService.partUpdate(buildEsProductStockModel(productId));
    }

    @Override
    public ProductEsBo queryProductAuditForEsBuild(Long productId) {
        ProductAudit productAudit = productAuditRepository.getByProductId(productId);
        if (productAudit == null) {
            return null;
        }

        ProductEsBo bo = ProductConverter.convertToEsBo(productAudit);
        // 查询商品图片
        bo.setImgList(productImageAuditRepository.listImagesByProductId(productId));
        // 查询店铺分类id
        bo.setShopCategoryIds(productAssist.getShopCategoryIds(productId));
        // 查询审核原因
        bo.setAuditReason(productAuditAssist.getAuditReason(productAudit));

        List<SkuAudit> skuList = skuAuditRepository.listByProductIds(Arrays.asList(productId));
        bo.setSkuAutoIds(skuList.stream().map(SkuAudit::getId).collect(Collectors.toList()));
        bo.setSkuCodes(skuList.stream().map(SkuAudit::getSkuCode).collect(Collectors.toList()));
        bo.setSkuIds(skuList.stream().map(SkuAudit::getSkuId).collect(Collectors.toList()));
        bo.setMaxSalePrice(productAuditAssist.calculateMaxSalePrice(productId));
        return bo;
    }

    @Override
    @DistributeLock(keyPattern = LockConstant.LOCK_ES_PRODUCT_AUDIT_UPDATE_PATTERN, scenes =
            LockConstant.SCENE_ES_PRODUCT_AUDIT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    public void buildProductAuditEs(Long productId) {
        // 更新审核表信息
        ProductEsBo productEsBo = SpringUtil.getBean(ProductEsBuildService.class).queryProductAuditForEsBuild(productId);
        if (productEsBo != null && !productEsBo.getWhetherDelete()) {
            EsProductModel model = EsProductConverter.boToEsModel(productEsBo);
            esProductAuditService.partUpdate(model);
        } else {
            esProductAuditService.deleteById(String.valueOf(productId));
        }
    }

    @Override
    @DistributeLock(keyPattern = LockConstant.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConstant.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    public void buildProductEs(Long productId) {
        log.info("buildProductEs productId: {}, start", productId);
        // 更新审核表信息
        ProductEsBo productEsBo = SpringUtil.getBean(ProductEsBuildService.class).queryProductForEsBuild(productId);
        if (productEsBo != null && !productEsBo.getWhetherDelete()) {
            EsProductModel model = EsProductConverter.boToEsModel(productEsBo);

            // 是否达到低于警戒库存
            EsProductModel stockModel = buildEsProductStockModel(productId);
            model.setWhetherBelowSafeStock(stockModel.getWhetherBelowSafeStock());
            model.setTotalStock(stockModel.getTotalStock());

            // 收藏数
            Integer favoriteCount = userFavoriteRemoteService.getFavoriteCount(productId);
            model.setFavoriteCount(favoriteCount);

            // 评价汇总
            ProductCommentSummaryResp commentSummary = orderQueryRemoteService.getProductCommentSummary(productId);
            model.setCommentSummary(JsonUtil.copy(commentSummary, EsProductCommentModel.class));
            log.info("buildProductEs productId: {}, commentSummary: {}", productId, commentSummary);

            esProductService.partUpdate(model);
        } else {
            esProductService.deleteById(String.valueOf(productId));
        }
        log.info("buildProductEs productId: {}, end", productId);
    }

    @Override
    @DistributeLock(keyPattern = LockConstant.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConstant.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    public void buildProductFavoriteEs(Long productId) {
        log.info("[商品ES] 更新收藏数 productId: {}, start", productId);
        EsProductModel product = esProductService.getById(String.valueOf(productId));
        if (product == null) {
            log.warn("[商品ES] 更新收藏数, 未找到商品, productId: {}", productId);
            return;
        }

        EsProductModel model = new EsProductModel();
        model.setProductId(product.getProductId());
        Integer favoriteCount = userFavoriteRemoteService.getFavoriteCount(productId);
        model.setFavoriteCount(favoriteCount);
        esProductService.partUpdate(model);
        log.info("[商品ES] 更新收藏数 productId: {}, end", productId);
    }

    @Override
    @DistributeLock(keyPattern = LockConstant.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConstant.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    public void buildProductCommentEs(Long productId) {
        log.info("[商品ES] 更新评论数 productId: {}, start", productId);
        EsProductModel product = esProductService.getById(String.valueOf(productId));
        if (product == null) {
            log.warn("[商品ES] 更新评论数, 未找到商品, productId: {}", productId);
            return;
        }

        EsProductModel model = new EsProductModel();
        model.setProductId(productId);
        // 评价汇总
        ProductCommentSummaryResp commentSummary = orderQueryRemoteService.getProductCommentSummary(productId);
        model.setCommentSummary(JsonUtil.copy(commentSummary, EsProductCommentModel.class));
        esProductService.partUpdate(model);
        log.info("[商品ES] 更新评论数 productId: {}, end", productId);
    }

    @Override
    @DistributeLock(keyPattern = LockConstant.LOCK_ES_PRODUCT_UPDATE_PATTERN, scenes =
            LockConstant.SCENE_ES_PRODUCT_UPDATE,
            waitLock = true,
            keyValues = {"{0}"})
    public void buildProductSalesEs(Long productId) {
        log.info("[商品ES] 更新销量数 productId: {}, start", productId);
        EsProductModel product = esProductService.getById(String.valueOf(productId));
        if (product == null) {
            log.warn("[商品ES] 更新销量数, 未找到商品, productId: {}", productId);
            return;
        }

        Product dbProduct = productRepository.getByProductId(productId);
        if (dbProduct == null) {
            log.warn("[商品ES] 更新销量数, db未找到商品, productId: {}", productId);
            return;
        }

        EsProductModel model = new EsProductModel();
        model.setProductId(productId);
        model.setSaleCounts(dbProduct.getSaleCounts());
        model.setTotalSaleCounts(ObjectUtil.defaultIfNull(product.getVirtualSaleCounts(), 0L) + dbProduct.getSaleCounts());

        esProductService.partUpdate(model);
        log.info("[商品ES] 更新销量数 productId: {}, end", productId);
    }

    /**
     * 这个方法已经在订单事件统一加了锁，这里可以不加锁
     *
     * @param orderId 订单id
     */
    @Override
    public void buildProductCommentEsByOrderId(String orderId) {
        log.info("[商品ES] 更新评论数, orderId: {}", orderId);
        OrderInfoDto order = orderQueryRemoteService.getOrder(orderId);
        if (order == null || CollUtil.isEmpty(order.getItemList())) {
            log.warn("[商品ES] 更新评论数, 订单不存在, orderId: {}", orderId);
            return;
        }

        order.getItemList().forEach(item -> {
            ProductEsBuildService service = SpringUtil.getBean(ProductEsBuildService.class);
            service.buildProductCommentEs(Long.parseLong(item.getProductId()));
        });
    }

    private EsProductModel buildEsProductStockModel(Long productId) {
        List<SkuStock> stockList = skuStockRepository.listByProductIds(Arrays.asList(productId));
        stockList = Optional.ofNullable(stockList).orElse(Collections.emptyList());

        // 提取低于安全库存的数量
        long count = stockList.stream().filter(item -> item.getStock() <= item.getSafeStock()).count();
        // 提取总库存
        Long totalStock = stockList.stream().mapToLong(SkuStock::getStock).sum();

        EsProductModel model = new EsProductModel();
        model.setProductId(productId);
        model.setWhetherBelowSafeStock(count > 0);
        model.setTotalStock(totalStock);
        return model;
    }
}
