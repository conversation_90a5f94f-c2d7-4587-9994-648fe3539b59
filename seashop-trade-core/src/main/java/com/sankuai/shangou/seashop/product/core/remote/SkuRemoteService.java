package com.sankuai.shangou.seashop.product.core.remote;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.product.core.service.SkuQueryService;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 15:11
 */
@Service
public class SkuRemoteService {

    @Resource
    private SkuQueryService skuQueryService;

    public List<SkuQueryResp> querySkuList(SkuQueryReq request) {
        //SkuListResp skuListResp = ThriftResponseHelper.executeThriftCall(() -> skuQueryThriftService.querySkuList(request));
        //return skuListResp.getSkuList();
        //服务合并后，直接调用本地方法
        request.checkParameter();
        List<SkuQueryResp> skuList = skuQueryService.querySkuList(request);
        return skuList;
    }

    public List<SkuQueryResp> querySkuList(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        skuIds = skuIds.stream().distinct().collect(Collectors.toList());
        List<SkuQueryResp> skuList = new ArrayList<>();
        List<List<String>> subSkuIds = Lists.partition(skuIds, PromotionConstant.MAX_QUERY_LIMIT);
        for (List<String> subList : subSkuIds) {
            SkuQueryReq request = new SkuQueryReq();
            request.setSkuIdList(subList);
            List<SkuQueryResp> subSkuList = querySkuList(request);
            if (CollectionUtils.isNotEmpty(subSkuList)) {
                skuList.addAll(subSkuList);
            }
        }
        return skuList;
    }
}
