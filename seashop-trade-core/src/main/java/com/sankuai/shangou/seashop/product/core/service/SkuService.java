package com.sankuai.shangou.seashop.product.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.product.core.service.model.SkuQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;

/**
 * <AUTHOR>
 * @date 2023/12/21 9:25
 */
public interface SkuService {

    /**
     * 查询sku集合
     *
     * @param queryBo 查询入参
     * @return sku集合
     */
    List<Sku> querySkuList(ProductSkuQueryReq queryBo);

}
