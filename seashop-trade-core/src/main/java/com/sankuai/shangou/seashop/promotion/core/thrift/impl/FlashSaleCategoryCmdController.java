package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleCategoryService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleCategoryAddReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCategoryCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@RestController
@RequestMapping("/flashSaleCategory")
public class FlashSaleCategoryCmdController implements FlashSaleCategoryCmdFeign {

    @Resource
    private FlashSaleCategoryService flashSaleCategoryService;

    @PostMapping(value = "/add", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> add(@RequestBody FlashSaleCategoryAddReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("add", request, req -> {
            req.checkParameter();
            flashSaleCategoryService.add(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/delete", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> delete(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("delete", request, req -> {
            req.checkParameter();
            flashSaleCategoryService.delete(req);
            return new BaseResp();
        });
    }
}
