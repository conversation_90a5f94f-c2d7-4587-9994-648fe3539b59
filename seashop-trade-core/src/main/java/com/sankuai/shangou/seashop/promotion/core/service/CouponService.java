package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponReceiveBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponSaveBo;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.SendCouponCmdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductAvailableQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.ProductAvailableQueryResp;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
public interface CouponService {

    /**
     * 保存优惠券
     *
     * @param saveBo
     */
    void save(CouponSaveBo saveBo);

    /**
     * 提前结束活动
     *
     * @param baseIdReq
     */
    void endActive(BaseIdReq baseIdReq);

    /**
     * 分页查询优惠券
     *
     * @param couponQueryReq
     * @return
     */
    BasePageResp<CouponSimpleResp> pageList(CouponQueryReq couponQueryReq);

    /**
     * 判断店铺是否有可用的优惠券
     *
     * @param shopId
     * @return
     */
    Boolean flagHasCoupons(Long shopId);

    /**
     * 根据id查询优惠券
     *
     * @param id
     * @return
     */
    CouponResp getById(Long id);

    /**
     * 领取优惠券
     *
     * @param receiveBo
     */
    void receiveCoupon(CouponReceiveBo receiveBo);

    /**
     * 通过用户ID查询可用的优惠券数量
     *
     * @param userId
     * @return
     */
    Integer queryAvailableCouponCountByUser(Long userId);

    /**
     * 发送优惠券
     *
     * @param request
     */
    void sendCoupon(SendCouponCmdReq request);

    /**
     * 通过产品ID查询优惠券
     *
     * @param request
     * @return
     */
    CouponSimpleListResp queryByProductId(ProductAndShopIdReq request);

    /**
     * 通过id列表查询优惠券
     *
     * @param request
     * @return
     */
    CouponSimpleListResp getByIdList(BaseBatchIdReq request);

    //    计算使用率
    String calculateUsageRate(BaseIdReq msgId);

    CouponSimpleListResp getByMessageId(BaseIdReq msgId);

    /**
     * 查询优惠券商品列表
     *
     * @param req 查询条件
     * @return 优惠券商品列表
     */
    BasePageResp<CouponProductDto> queryCouponProductPage(CouponProductQueryReq req);

    /**
     * 查询可用优惠券商品列表
     *
     * @param request 查询条件
     * @return 可用优惠券商品列表
     */
    ProductAvailableQueryResp getProductAvailableCouponList(ProductAvailableQueryReq request);

    /**
     * 查询优惠券关联的商品ID
     *
     * @param couponId 优惠券ID
     * @return 商品ID列表
     */
    List<Long> queryRelateProductIds(Long couponId);
}
