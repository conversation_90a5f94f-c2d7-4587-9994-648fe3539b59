package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.model.bo.DiscountActiveQueryBo;
import com.sankuai.shangou.seashop.promotion.core.service.DiscountActiveService;
import com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveSimpleDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.DiscountActiveQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.DiscountActiveQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: liuhaox
 * @date: 2023/11/7/007
 * @description:
 */
@RestController
@RequestMapping("/discountActive")
public class DiscountActiveQueryController implements DiscountActiveQueryFeign {

    @Resource
    private DiscountActiveService discountActiveService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<DiscountActiveSimpleResp>> pageList(@RequestBody DiscountActiveQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            DiscountActiveQueryBo saveBo = JsonUtil.copy(req, DiscountActiveQueryBo.class);
            BasePageResp<DiscountActiveSimpleDto> pageResp = discountActiveService.pageList(saveBo);
            return PageResultHelper.transfer(pageResp, DiscountActiveSimpleResp.class);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @Override
    public ResultDto<DiscountActiveResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            return discountActiveService.getById(req);
        });
    }

    @PostMapping(value = "/queryByProductId", consumes = "application/json")
    @Override
    public ResultDto<DiscountActiveResp> queryByProductId(@RequestBody ProductAndShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryByProductId", request, req -> {
            req.checkParameter();
            return discountActiveService.queryByProductId(req);
        });
    }

    @PostMapping(value = "/queryDiscountActiveProduct", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<DiscountActiveProductDto>> queryDiscountActiveProduct(@RequestBody QueryDiscountActiveProductReq request) {
        return ThriftResponseHelper.responseInvoke("queryDiscountActiveProduct", request, req -> {
            req.checkParameter();

            return discountActiveService.queryDiscountActiveProduct(req);
        });
    }
}
