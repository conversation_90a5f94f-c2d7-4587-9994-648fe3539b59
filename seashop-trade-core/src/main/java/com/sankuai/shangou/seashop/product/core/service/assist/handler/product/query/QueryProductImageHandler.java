package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品图片查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:08
 */
@Component
@Slf4j
public class QueryProductImageHandler extends AbsProductHandler {

    @Resource
    private ProductAssist productAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        Long productId = context.getProductId();

        // 查询商品图片
        productBo.setImageList(productAssist.getProductImgList(productId));
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_PRODUCT_IMAGE;
    }

}
