package com.sankuai.shangou.seashop.product.core.service.model;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:12
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryQueryBo {

    /**
     * 最大深度
     */
    private Integer maxDepth;

    /**
     * 类目id集合
     */
    private List<Long> ids;

    /**
     * 类目名称模糊搜索
     */
    private String nameLike;

    /**
     * 深度
     */
    private Integer depth;

    /**
     * 父类目id
     */
    private Long parentId;

    /**
     * 是否显示
     */
    private Boolean whetherShow;

    /**
     * 是否过滤出没有下级的类目
     */
    private Boolean filterNoChildren;

    /**
     * 展示状态 0-全部 1-展示开启 2-展示关闭
     */
    private Integer showStatus;

    /**
     * 是否有子类目
     */
    private Boolean hasChildren;

    /**
     * 自定义表单id
     */
    private List<Long> customerFormIds;

    /**
     * 递归获取上级类目id
     */
    private Long recursiveParentId;

    /**
     * 排除没有销售商品的类目
     */
    private Boolean excludeNoSale;

}
