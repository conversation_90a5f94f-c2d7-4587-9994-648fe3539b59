package com.sankuai.shangou.seashop.product.core.service.excel.read.listener;

import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;

import com.sankuai.shangou.seashop.base.eimport.DefaultReadListener;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.PriceImportDto;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.helper.ParameterHelper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/21 22:10
 */
@Slf4j
public class PriceImportListener extends DefaultReadListener<PriceImportDto> {

    /**
     * 入库前检验数据
     * <p>
     * 1.如果没有开启阶梯价, 则规格id和商城价必填
     * 2.如果开启了阶梯价, 1起购量和1阶梯价必填
     * 2.1 如果填了2起购量, 则2阶梯价必填
     * 2.2 如果填了2起购量, 2起购量必须大于1起购量
     * 2.3 如果填了3起购量, 则3阶梯价必填
     * 2.4 如果填了3起购量, 3起购量必须大于2起购量
     *
     * @param data 单行数据
     * @return 错误信息
     */
    @Override
    protected String checkData(PriceImportDto data) {
        StringBuilder errMsgBuilder = data.getErrBuilder();
        boolean whetherOpenLadderPrice = CommonConstant.YES_STR.equals(data.getWhetherOpenLadderPrice());
        data.setWhetherOpenLadderPriceFlag(whetherOpenLadderPrice);

        // 未开启阶梯价
        if (!whetherOpenLadderPrice) {
            if (StringUtils.isEmpty(data.getSkuAutoId())) {
                errMsgBuilder.append("规格ID必填;");
            }
            if (StringUtils.isEmpty(data.getSalePrice())) {
                errMsgBuilder.append("商城价必填;");
            } else  if (!ParameterHelper.checkPrice(BigDecimal.valueOf(Double.parseDouble(data.getSalePrice())))) {
                errMsgBuilder.append(String.format("商城价范围为%s-%s;", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
            }
            return errMsgBuilder.toString();
        }

        // 开启了阶梯价
        if (StringUtils.isEmpty(data.getMinBath1())) {
            errMsgBuilder.append("1起购量必填;");
        }
        if (StringUtils.isEmpty(data.getLadderPrice1())) {
            errMsgBuilder.append("1阶梯价必填;");
        } else if (!ParameterHelper.checkPrice(BigDecimal.valueOf(Double.parseDouble(data.getLadderPrice1())))) {
            errMsgBuilder.append(String.format("1阶梯价范围为%s-%s;", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
        }
        if (StringUtils.isNotEmpty(data.getMinBath2())) {
            if (StringUtils.isEmpty(data.getLadderPrice2())) {
                errMsgBuilder.append("2阶梯价必填;");
            } else if (!ParameterHelper.checkPrice(BigDecimal.valueOf(Double.parseDouble(data.getLadderPrice2())))) {
                errMsgBuilder.append(String.format("2阶梯价范围为%s-%s;", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
            }
            if (StringUtils.isNotEmpty(data.getMinBath1()) && Integer.parseInt(data.getMinBath2()) <= Integer.parseInt(data.getMinBath1())) {
                errMsgBuilder.append("2起购量必须大于1起购量;");
            }
        }
        if (StringUtils.isNotEmpty(data.getMinBath3())) {
            if (StringUtils.isEmpty(data.getMinBath2())) {
                errMsgBuilder.append("2起购量必填;");
            }
            if (StringUtils.isEmpty(data.getLadderPrice3())) {
                errMsgBuilder.append("3阶梯价必填;");
            } else if (!ParameterHelper.checkPrice(BigDecimal.valueOf(Double.parseDouble(data.getLadderPrice3())))) {
                errMsgBuilder.append(String.format("3阶梯价范围为%s-%s;", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
            }
            if (StringUtils.isNotEmpty(data.getMinBath2()) && Integer.parseInt(data.getMinBath3()) <= Integer.parseInt(data.getMinBath2())) {
                errMsgBuilder.append("3起购量必须大于2起购量;");
            }
        }
        return errMsgBuilder.toString();
    }
}
