package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.ExclusivePriceBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCouponSimpleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteReductionBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductLadderPriceBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 活动上下文对象
 *
 * com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryThriftService#queryByProductId #优惠券
 * com.sankuai.shangou.seashop.promotion.thrift.core.service.DiscountActiveQueryThriftService#queryByProductId #折扣活动
 * com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryThriftService#queryByProductId #限时购
 * com.sankuai.shangou.seashop.promotion.thrift.core.service.FullReductionQueryThriftService#queryByShopId #店铺满减
 * com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceQueryThriftService#queryByProductAndMemberId #专享价
 *
 * <AUTHOR>
 * @date 2023/12/23 9:35
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityContext {

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 限时购活动
     */
    private RemoteFlashSaleBo flashSale;

    /**
     * 专享价活动
     */
    private ExclusivePriceBo exclusivePrice;

    /**
     * 阶梯价
     */
    private List<ProductLadderPriceBo> ladderPriceList;

    /**
     * 组合购活动
     */
    private List<RemoteCollocationBo> collectionBuy;

    /**
     * 折扣活动
     */
    private RemoteDiscountBo discountActive;

    /**
     * 满减
     */
    private RemoteReductionBo fullReduction;

    /**
     * 优惠券
     */
    private List<RemoteCouponSimpleBo> couponList;

    @Override
    public String toString() {
        return "ActivityContext{" +
                "productId=" + productId +
                ", shopId=" + shopId +
                ", userId=" + userId +
                '}';
    }
}
