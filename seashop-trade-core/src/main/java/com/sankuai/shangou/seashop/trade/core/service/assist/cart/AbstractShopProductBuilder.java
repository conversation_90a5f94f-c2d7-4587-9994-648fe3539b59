package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.UseAreaEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.constant.MessageConst;
import com.sankuai.shangou.seashop.trade.common.enums.OrderPromotionType;
import com.sankuai.shangou.seashop.trade.common.enums.TradeOrderValidResultEnum;
import com.sankuai.shangou.seashop.trade.common.enums.TradeResultCode;
import com.sankuai.shangou.seashop.trade.common.exception.ChangeQuantityException;
import com.sankuai.shangou.seashop.trade.common.remote.FreightRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.Order2TradeRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountRuleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteReductionBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopUserPromotionBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.CalculateFreightBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.CalculateFreightProductBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.CalculateFreightShopBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.ShopFreightBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.model.InvoiceBo;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.PreviewOrderSummaryBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.AddonDescBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ProductMatchDiscountBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ProductPromotionBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.PromotionBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ShopAndProductPromotionBo;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.AddonProductPromotionTabEnum;
import com.sankuai.shangou.seashop.user.thrift.account.enums.InvoiceTypeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractShopProductBuilder<H extends BaseBuildDataHolder> implements ShopProductBuilder {

    @Resource
    private FreightRemoteService freightRemoteService;
    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    protected DataProcessorAssist dataProcessorAssist;
    @Resource
    private Order2TradeRemoteService orderRemoteService;


    @Override
    public BuildResult build(BuildContext context) {
        StopWatch stopWatch = new StopWatch("店铺商品构建");
        // 根据业务场景构建店铺信息和店铺下的商品列表，数据设置到不同业务的临时数据存储对象中
        stopWatch.start("初始化店铺和商品数据");
        H dataHolder = buildShopAndProductList(context);
        stopWatch.stop();
        if (dataHolder == null) {
            log.info("店铺商品构建为空耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
            return null;
        }
        // 处理营销
        stopWatch.start("处理营销");
        processPromotion(context, dataHolder);
        stopWatch.stop();
        // 其他扩展处理，比如运费、税费、优惠券
        stopWatch.start("扩展处理运费税费优惠券");
        processExpand(context, dataHolder);
        stopWatch.stop();
        // 构建返回对象
        stopWatch.start("组装返回结果");
        BuildResult result = assembleResult(dataHolder);
        stopWatch.stop();
        log.info("店铺商品构建耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return result;
    }


    /**********************start:抽象方法定义******************************/

    /**
     * 根据业务场景构建店铺信息和店铺下的商品列表，数据设置到不同业务的临时数据存储对象中
     *
     * @param context 构建信息参数上下文
     *                H
     * <AUTHOR>
     */
    protected abstract H buildShopAndProductList(BuildContext context);


    /**
     * 构建店铺商品列表，包括基础优惠价格，折扣价格，以及折扣金额的计算
     *
     * @param context    构建信息参数上下文
     * @param dataHolder 数据临时存储对象
     * <AUTHOR>
     */
    protected abstract List<ShopProductListBo> buildShopProduct(BuildContext context, H dataHolder);

    /**
     * 处理其他扩展数据，比如预览订单页的满减，运费等等
     *
     * @param context    构建信息参数上下文
     * @param dataHolder 数据临时存储对象
     *                   void
     * <AUTHOR>
     */
    protected abstract void processExpand(BuildContext context, H dataHolder);

    /**
     * 基本的返回结果之外，额外扩展设置返回结果
     *
     * @param result     公共的构建结果
     * @param dataHolder 业务构建过程中的临时存储对象
     *                   void
     * <AUTHOR>
     */
    protected abstract void extendResult(BuildResult result, H dataHolder);


    //***************************************************************


    /**
     * 处理营销，包括折扣和满减，折扣必须先处理
     * <p>专享价和折扣的计算也放一起了，不想多次遍历</p>
     * 优惠券没有在这里一起处理，一是只有提交订单才会有优惠券，二是优惠券可能会使用页面传入的数据，所以直接由子类自己处理
     *
     * @param context    构建上下文
     * @param dataHolder 数据存储对象
     * <AUTHOR>
     */
    protected void processPromotion(BuildContext context, H dataHolder) {
        List<ShopProductListBo> shopProductList = dataHolder.getShopProductList();
        if (CollUtil.isEmpty(shopProductList)) {
            return;
        }
        Map<Long/*shopId*/, RemoteShopUserPromotionBo> shopPromotionMap = dataHolder.getShopPromotionMap();
        if (MapUtil.isEmpty(shopPromotionMap)) {
            return;
        }
        for (ShopProductListBo shopOrder : shopProductList) {
            processPromotionForShop(shopOrder, context, shopPromotionMap);
        }

    }

    private void processPromotionForShop(ShopProductListBo shopOrder,
                                         BuildContext context,
                                         Map<Long/*shopId*/, RemoteShopUserPromotionBo> shopPromotionMap) {
        RemoteShopUserPromotionBo shopPromotion = shopPromotionMap.get(shopOrder.getShop().getShopId());
        if (shopPromotion == null) {
            return;
        }
        ShoppingCartShopBo shop = shopOrder.getShop();
        OrderAdditionalBo additional = shopOrder.getAdditional();
        List<ShopProductBo> shopSkuList = shopOrder.getProductList();
        StopWatch stopWatch = new StopWatch("按店铺计算营销");
        // 优先，处理专享价和阶梯价，虽然阶梯价不是属于营销，但是专享价是，而价格有优先级，放到一起处理
        stopWatch.start("处理价格");
        if (context.needReCalPrice()) {
            calculatePriceAndAmountForShop(context, shop, shopSkuList, shopPromotionMap.get(shop.getShopId()));
        }
        stopWatch.stop();
        // 处理折扣，一定要先处理折扣，因为其他营销是在折扣的基础上计算的
        stopWatch.start("处理折扣");
        if (context.needDiscount()) {
            calculateDiscountAndResetRealPriceAndAmount(shop, shopSkuList, shopPromotion.getShopDiscountList(), additional);
        }
        stopWatch.stop();
        // 处理满减，context的满减是业务场景是否需要；店铺维度的是当前店铺是否需要
        stopWatch.start("处理满减");
        if (context.needReduction() && shop.isNeedReduction()) {
            dealReduction(shopOrder, shopPromotion.getShopReduction());
        }
        stopWatch.stop();
        log.info("按店铺计算营销耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
    }


    /**
     * 组装构建结果
     *
     * @param dataHolder 数据存储对象
     * <AUTHOR>
     */
    private BuildResult assembleResult(H dataHolder) {
        List<ShopProductListBo> shopProductList = dataHolder.getShopProductList();
        // 返回前端空数组
        if (shopProductList == null){
            shopProductList = Collections.emptyList();
        }

        PreviewOrderSummaryBo summary = new PreviewOrderSummaryBo();
        // 汇总金额
        BigDecimal totalSelectedAmount = BigDecimal.ZERO;
        BigDecimal totalProductAmount = BigDecimal.ZERO;
        BigDecimal totalDiscountAmount = BigDecimal.ZERO;
        BigDecimal totalCouponAmount = BigDecimal.ZERO;
        BigDecimal totalFreightAmount = BigDecimal.ZERO;
        BigDecimal totalTaxAmount = BigDecimal.ZERO;
        BigDecimal totalReductionAmount = BigDecimal.ZERO;
        // 如果有效的购物车没有数据，选中为false，否则先设置为true，后续任意一个没有选中，设置为false
        boolean whetherAllSelect = !CollUtil.isEmpty(shopProductList);
        long totalSkuQuantity = 0L;
        for (ShopProductListBo shopProduct : shopProductList) {
            ShoppingCartShopBo shopBo = shopProduct.getShop();
            OrderAdditionalBo additional = shopProduct.getAdditional();

            // 此时再累计店铺实际金额
            totalSelectedAmount = totalSelectedAmount.add(shopBo.getSelectedTotalAmount());

            totalProductAmount = totalProductAmount.add(shopBo.getProductTotalAmount());
            totalDiscountAmount = totalDiscountAmount.add(NumberUtil.nullToZero(additional.getDiscountAmount()));
            totalCouponAmount = totalCouponAmount.add(NumberUtil.nullToZero(additional.getCouponAmount()));
            totalFreightAmount = totalFreightAmount.add(NumberUtil.nullToZero(additional.getFreightAmount()));
            totalTaxAmount = totalTaxAmount.add(NumberUtil.nullToZero(additional.getTaxAmount()));
            totalReductionAmount = totalReductionAmount.add(NumberUtil.nullToZero(additional.getReductionAmount()));

            List<ShopProductBo> productList = shopProduct.getProductList();
            totalSkuQuantity += productList.stream()
                    .filter(sku -> Boolean.TRUE.equals(sku.getWhetherSelected()))
                    .map(ShopProductBo::getQuantity)
                    .reduce(0L, Long::sum);

            if (!Boolean.TRUE.equals(shopBo.getWhetherSelected())) {
                whetherAllSelect = false;
            }
        }
        BigDecimal totalPayAmount = totalSelectedAmount.add(totalFreightAmount).add(totalTaxAmount);
        summary.setTotalPayAmount(totalPayAmount);
        summary.setTotalProductAmount(totalProductAmount);
        summary.setTotalDiscountAmount(totalDiscountAmount);
        summary.setTotalCouponAmount(totalCouponAmount);
        summary.setTotalFreightAmount(totalFreightAmount);
        summary.setTotalTaxAmount(totalTaxAmount);
        summary.setTotalSkuQuantity(totalSkuQuantity);
        summary.setTotalReductionAmount(totalReductionAmount);
        summary.setWhetherAllSelected(whetherAllSelect);

        // 构建基本的返回结果
        BuildResult result = BuildResult.builder()
                .shopProductList(shopProductList)
                .totalAmount(totalPayAmount)
                .summary(summary)
                .build();
        // 扩展设置返回结果
        extendResult(result, dataHolder);
        return result;
    }

    /**
     * 计算店铺总金额
     * <p>店铺总金额 = 选择的金额 + 运费 + 税费</p>
     * <AUTHOR>
     */
    protected BigDecimal calculateTotalAmount(ShoppingCartShopBo shopBo, OrderAdditionalBo additional) {
        // selectedTotalAmount 金额包括了折扣后的金额和满减/优惠券后的金额
        BigDecimal selectedTotalAmount = shopBo.getSelectedTotalAmount();
        BigDecimal freightAmount = NumberUtil.nullToZero(additional.getFreightAmount());
        BigDecimal taxAmount = NumberUtil.nullToZero(additional.getTaxAmount());
        // 店铺总金额 = 选择的金额(计算满减和优惠券后的) + 运费 + 税费
        log.info("计算店铺总金额, shopId={}, 选择的金额={}, 运费={}, 税费={}", shopBo.getShopId(), selectedTotalAmount, freightAmount, taxAmount);
        return selectedTotalAmount.add(freightAmount).add(taxAmount);
    }

    /**
     * 计算并设置运费
     *
     * @param defaultShippingAddress 收货地址
     * @param dataHolder 数据持有对象
     * <AUTHOR>
     */
    protected void calculateAndSetFreight(ShippingAddressBo defaultShippingAddress, BaseBuildDataHolder dataHolder) {
        long begin = System.currentTimeMillis();
        this.calculateAndSetFreight(defaultShippingAddress, dataHolder, false);
        long end = System.currentTimeMillis();
        log.info("计算运费耗时：{}", end - begin);
    }

    /**
     * 计算并设置运费
     * <p>这个标识的作用是：第一次进入订单预览页和后续其他操作都需要计算运费，但是第一次进入页面时，
     * 如果收货地址在禁售区域也应该显示数据，允许用户调整收货地址。所以第一次进入预览页这个标识设置为true，此时不报错而是忽略禁售区域的商品
     * 提交订单和其他接口要计算运费时，这个标识设置为false或者不设置，此时在禁售区域会抛出异常</p>
     * @param defaultShippingAddress 收货地址
     * @param dataHolder 数据持有对象
     * @param ignoreForbiddenArea 是否忽略禁售区域商品
     * <AUTHOR>
     */
    protected void calculateAndSetFreight(ShippingAddressBo defaultShippingAddress, BaseBuildDataHolder dataHolder, Boolean ignoreForbiddenArea) {
        if (defaultShippingAddress == null) {
            return;
        }
        // 计算运费
        List<ShopFreightBo> shopFreightList = calculateFreight(defaultShippingAddress, dataHolder, ignoreForbiddenArea);
        // 设置运费
        setShopFreight(dataHolder, shopFreightList);
    }

    private List<ShopFreightBo> calculateFreight(ShippingAddressBo defaultShippingAddress, BaseBuildDataHolder dataHolder, Boolean ignoreForbiddenArea) {
        CalculateFreightBo param = new CalculateFreightBo();
        param.setRegionId(defaultShippingAddress.getRegionId());
        param.setRegionPath(defaultShippingAddress.getRegionPath());
        param.setIgnoreForbiddenArea(ignoreForbiddenArea);
        List<CalculateFreightShopBo> calShopProductList = new ArrayList<>();
        CalculateFreightShopBo calShop = null;
        for (ShopProductListBo shop : dataHolder.getShopProductList()) {
            calShop = new CalculateFreightShopBo();
            calShop.setShopId(shop.getShop().getShopId());
            calShop.setTotalAmount(shop.getShop().getSelectedTotalAmount());
            List<CalculateFreightProductBo> prodList = new ArrayList<>();
            CalculateFreightProductBo calProd = null;
            for (ShopProductBo prod : shop.getProductList()) {
                calProd = new CalculateFreightProductBo();
                calProd.setProductId(prod.getProductId());
                calProd.setSkuId(prod.getSkuId());
                calProd.setTemplateId(prod.getFreightTemplateId());
                calProd.setWeight(prod.getWeight());
                calProd.setVolume(prod.getVolume());
                calProd.setProductAmount(prod.getTotalAmount());
                calProd.setBuyCount(prod.getQuantity());
                calProd.setProductName(prod.getProductName());
                prodList.add(calProd);
            }
            calShop.setProductList(prodList);
            calShopProductList.add(calShop);
        }
        param.setShopList(calShopProductList);
        return freightRemoteService.calculateFreight(param);
    }

    private void setShopFreight(BaseBuildDataHolder dataHolder, List<ShopFreightBo> shopFreightList) {
        Map<Long, ShopFreightBo> shopFreightMap = shopFreightList.stream()
                .collect(Collectors.toMap(ShopFreightBo::getShopId, Function.identity(), (o1,
                                                                                          o2) -> o2));
        for (ShopProductListBo shop : dataHolder.getShopProductList()) {
            ShopFreightBo shopFreight = shopFreightMap.get(shop.getShop().getShopId());
            if (shopFreight != null) {
                shop.getAdditional().setFreightAmount(shopFreight.getFreight());
            }
        }
    }

    protected void calculateTax(BaseBuildDataHolder dataHolder) {
        List<ShopProductListBo> shopProductList = dataHolder.getShopProductList();
        if (CollUtil.isEmpty(shopProductList)) {
            return;
        }
        List<Long> shopIdList = shopProductList.stream()
                .map(shop -> shop.getShop().getShopId())
                .collect(Collectors.toList());
        StopWatch stopWatch = new StopWatch("计算税费");
        stopWatch.start("查询店铺税费配置");
        Map<Long, QueryShopInvoiceResp> shopInvoiceMap = shopRemoteService.queryShopInvoiceSettingMap(shopIdList);
        stopWatch.stop();
        stopWatch.start("执行税费计算");
        for (ShopProductListBo shopOrder : shopProductList) {
            ShoppingCartShopBo shop = shopOrder.getShop();
            log.info("计算税费, shopId={}", shop.getShopId());
            OrderAdditionalBo additional = shopOrder.getAdditional();
            InvoiceBo invoice = null;
            if (additional == null) {
                continue;
            }
            // 先默认设置税费为0，重新计算
            additional.setTaxAmount(BigDecimal.ZERO);
            if ((invoice = additional.getInvoice()) == null) {
                continue;
            }
            if (invoice.getInvoiceType() == null){
                continue;
            }
            QueryShopInvoiceResp shopInvoice = shopInvoiceMap.get(shopOrder.getShop().getShopId());
            if (shopInvoice == null) {
                continue;
            }
            // 普通发票和电子发票，税费=商品支付金额（不包括运费）*税率
            // 增值税发票，税费=商品支付金额（不包括运费）*税率
            BigDecimal taxRate = BigDecimal.ZERO;
            BigDecimal beTaxAmount = BigDecimal.ZERO;
            if (InvoiceTypeEnum.VAT.getValue() == invoice.getInvoiceType()) {
                taxRate = shopInvoice.getVatInvoiceRate();
                beTaxAmount = shopOrder.getShop().getSelectedTotalAmount();

            }
            else if (InvoiceTypeEnum.NORMAL.getValue() == invoice.getInvoiceType() ||
                    InvoiceTypeEnum.ELECTRONIC.getValue() == invoice.getInvoiceType()) {
                taxRate = shopInvoice.getPlainInvoiceRate();
                beTaxAmount = shopOrder.getShop().getSelectedTotalAmount();
            }
            else {
                throw new InvalidParamException("发票类型不正确");
            }
            // 税率百分比
            taxRate = NumberUtil.div(taxRate, CommonConst.HUNDRED, 4);
            log.info("计算税费, shopId={}, 发票类型={}， 税率={}, 待计算税费金额={}", shop.getShopId(), invoice.getInvoiceType(), taxRate, beTaxAmount);
            BigDecimal taxAmount = NumberUtil.mul(beTaxAmount, taxRate).setScale(2, RoundingMode.HALF_UP);

            additional.setTaxRate(taxRate);
            additional.setTaxAmount(taxAmount);
        }
        stopWatch.stop();
        log.info("计算税费耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
    }

    /**
     * 处理满减，此时已经时订单维度了
     *
     * @param shopOrder     店铺商品列表
     * @param shopReduction 店铺的满减信息
     */
    protected void dealReduction(ShopProductListBo shopOrder, RemoteReductionBo shopReduction) {
        if (shopReduction == null) {
            return;
        }
        dataProcessorAssist.calculateAndBuildReductionAmount(shopOrder, shopReduction);
    }



    /**
     * 获取并设置店铺当前有效的营销活动，包括折扣、满减、优惠券
     *
     * @param context    构建行下文
     * @param dataHolder 数据存储器
     * <AUTHOR>
     */
    protected void appendShopPromotion(BuildContext context, BaseBuildDataHolder dataHolder) {
        Long userId = context.getUserId();

        Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap = dataHolder.getShopGroupedMap();
        List<Long> shopIdList = shopGroupedMap.keySet().stream()
                .map(ShoppingCartShopBo::getShopId)
                .collect(Collectors.toList());
        List<RemoteShopUserPromotionBo> promotionList = promotionRemoteService.queryValidShopUserPromotion(shopIdList, userId);
        Map<Long, RemoteShopUserPromotionBo> shopPromotionMap = promotionList.stream()
                .collect(Collectors.toMap(RemoteShopUserPromotionBo::getShopId, Function.identity(), (o1, o2) -> o2));
        dataHolder.setShopPromotionMap(shopPromotionMap);

        // 没有营销活动, 可以直接返回
        if (CollUtil.isEmpty(promotionList)) {
            return;
        }

        // 提取当前店铺下单的商品
        List<Long> productIds = shopGroupedMap.values().stream()
                .filter(productList -> CollUtil.isNotEmpty(productList))
                .flatMap(productList -> productList.stream().map(ShopProductBo::getProductId))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(productIds)) {
            return;
        }

        // 循环所有的营销活动，过滤掉不符合条件的满减
        promotionList.forEach(promotion -> {
            List<RemoteDiscountBo> shopDiscountList = promotion.getShopDiscountList();
            if (CollUtil.isEmpty(shopDiscountList)) {
                return;
            }

            shopDiscountList = shopDiscountList.stream().filter(dis -> dis.getIzAllProduct()
                            || Optional.ofNullable(dis.getProductIdList()).orElse(Collections.emptyList()).stream().anyMatch(productIds::contains))
                    .collect(Collectors.toList());
            promotion.setShopDiscountList(shopDiscountList);
        });
    }





    /**
     * 根据折扣情况计算并设置商品的实际售价，以及店铺的总金额
     *
     * @param shop             店铺信息，此时的店铺信息是包含了营销对象的
     * @param shopSkuList      店铺的商品信息
     * @param shopDiscountList 店铺下的商品折扣
     * <AUTHOR>
     */
    protected void calculateDiscountAndResetRealPriceAndAmount(ShoppingCartShopBo shop,
                                                               List<ShopProductBo> shopSkuList,
                                                               List<RemoteDiscountBo> shopDiscountList,
                                                               OrderAdditionalBo additional) {
        if (CollUtil.isEmpty(shopDiscountList) || CollUtil.isEmpty(shopSkuList)) {
            return;
        }
        Map<Long, ProductMatchDiscountBo> productDiscountMap = findSuitableProductDiscount(shop, shopSkuList, shopDiscountList);
        resetRealPriceForDiscountAndSumTotalAmount(shop, shopSkuList, productDiscountMap, additional);
    }

    protected Map<Long, ProductMatchDiscountBo> findSuitableProductDiscount(ShoppingCartShopBo shop,
                                                                            List<ShopProductBo> shopSkuList,
                                                                            List<RemoteDiscountBo> shopDiscountList) {
        // 如果没有折扣活动，返回null，特殊含义，后续逻辑不需要继续判断处理折扣了
        if (CollectionUtils.isEmpty(shopDiscountList)) {
            return null;
        }
        Map<Long, ProductMatchDiscountBo> productDiscountMap = new HashMap<>();
        // 经过前面专享价和阶梯价后的最新的店铺总金额，购物车是根据勾选的情况，其他业务都是店铺所有商品
        // 专享价商品不参与折扣，所以需要重新计算总金额，而不是取 shop.getSelectedTotalAmount()
        BigDecimal totalAmount = dataProcessorAssist.calculateSkuTotalAmountBesideExclusive(shopSkuList);

        boolean containAllProduct = shopDiscountList.stream().anyMatch(dis -> Boolean.TRUE.equals(dis.getIzAllProduct()));
        if (containAllProduct && shopDiscountList.size() > 1) {
            // 留个途径知道问题，目前逻辑不会报错，也允许继续执行
            log.error("存在多个折扣活动适应所有商品，请检查！");
        }
        // 理论上如果有活动适应于所有商品，则就只会有一个活动
        RemoteDiscountBo firstDiscount;
        if (shopDiscountList.size() == 1 && (firstDiscount = shopDiscountList.get(0)).getIzAllProduct()) {
            List<RemoteDiscountRuleBo> ruleList = firstDiscount.getRuleList();
            // 根据折扣排序，取门槛最高的
            ruleList.sort(Comparator.comparing(RemoteDiscountRuleBo::getQuota).reversed());
            // 找到满足条件的折扣最大值
            ProductMatchDiscountBo matchRule = null;
            int matchIndex = -1;
            for (int i = 0; i < ruleList.size(); i++) {
                RemoteDiscountRuleBo rule = ruleList.get(i);
                if (totalAmount.compareTo(rule.getQuota()) >= 0) {
                    matchRule = new ProductMatchDiscountBo();
                    matchRule.setId(rule.getId());
                    matchRule.setQuota(rule.getQuota());
                    matchRule.setDiscount(rule.getDiscount());
                    matchRule.setActivityId(firstDiscount.getDiscountActId());
                    matchRule.setActivityName(firstDiscount.getDiscountActName());
                    matchRule.setIzAllProduct(true);
                    matchRule.setProductTotalAmount(totalAmount);

                    matchIndex = i;
                    break;
                }
            }
            // 填充凑单数据
            appendAddonPromotionDesc(shop, matchRule, matchIndex, ruleList, totalAmount);

            productDiscountMap.put(CommonConst.ALL_PRODUCT_ID, matchRule);

            return productDiscountMap;
        }
        // 如果是针对部分商品的，则需要根据折扣的商品情况，分别汇总店铺商品的金额，再判断折扣
        // 折扣是到商品维度的，同一个商品ID可以有多个sku
        Map<Long, List<ShopProductBo>> shopSkuMap = shopSkuList.stream()
                .filter(sku -> Boolean.TRUE.equals(sku.getWhetherSelected()))
                // 排除专享价商品
                .filter(sku -> !Boolean.TRUE.equals(sku.getWhetherExclusive()))
                .collect(Collectors.groupingBy(ShopProductBo::getProductId));
        // 多个折扣活动之间的商品不会重复，可以直接遍历折扣活动，把商品拿出来汇总
        shopDiscountList.forEach(dis -> {
            List<Long> productIdList = dis.getProductIdList();
            // 计算当前折扣活动适用商品的总金额
            BigDecimal productTotalAmount = productIdList.stream()
                    .map(shopSkuMap::get)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .map(ShopProductBo::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            List<RemoteDiscountRuleBo> ruleList = dis.getRuleList();
            // 根据折扣排序，取门槛最高的
            ruleList.sort(Comparator.comparing(RemoteDiscountRuleBo::getQuota).reversed());
            // 遍历每一个折扣，每一个折扣活动都需要返回
            int matchIndex = -1;
            ProductMatchDiscountBo matchRule = null;
            for (int i = 0; i < ruleList.size(); i++) {
                RemoteDiscountRuleBo rule = ruleList.get(i);
                if (productTotalAmount.compareTo(rule.getQuota()) >= 0) {
                    matchRule = new ProductMatchDiscountBo();
                    matchRule.setId(rule.getId());
                    matchRule.setQuota(rule.getQuota());
                    matchRule.setDiscount(rule.getDiscount());
                    matchRule.setActivityId(dis.getDiscountActId());
                    matchRule.setActivityName(dis.getDiscountActName());
                    matchRule.setIzAllProduct(false);
                    matchRule.setProductIdList(dis.getProductIdList());
                    matchRule.setProductTotalAmount(productTotalAmount);

                    matchIndex = i;
                    break;
                }
            }
            // 填充凑单数据
            appendAddonPromotionDesc(shop, matchRule, matchIndex, ruleList, productTotalAmount);
            // 设置商品满足的折扣，由于活动之间商品不会重复，所以不用担心会覆盖
            // 假如以后会有多个活动适用于同一个商品，这里设置的时候比较一下折扣最大值
            if (matchRule != null) {
                for (Long productId : productIdList) {
                    productDiscountMap.put(productId, matchRule);
                }
            }
        });
        return productDiscountMap;
    }

    /**
     * 根据折扣情况计算并设置商品的实际售价，以及店铺的总金额
     * <p>此时的商品折扣是已经计算出来的实际可以参与的，直接计算就行</p>
     *
     * @param shop               店铺信息，此时的店铺信息是包含了营销对象的
     * @param shopSkuList        店铺的商品信息
     * @param productDiscountMap 商品满足的折扣，已经是根据金额算出来的商品可以参与的折扣
     * <AUTHOR>
     */
    protected void resetRealPriceForDiscountAndSumTotalAmount(ShoppingCartShopBo shop,
                                                              List<ShopProductBo> shopSkuList,
                                                              Map<Long, ProductMatchDiscountBo> productDiscountMap,
                                                              OrderAdditionalBo additional) {
        if (MapUtils.isEmpty(productDiscountMap)) {
            return;
        }
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        for (ShopProductBo sku : shopSkuList) {
            ProductMatchDiscountBo productDiscount = getProductDiscount(sku, productDiscountMap);
            if (productDiscount == null) {
                // 没有折扣，使用原金额累计
                if (Boolean.TRUE.equals(sku.getWhetherSelected())) {
                    totalAmount = totalAmount.add(NumberUtil.nullToZero(sku.getTotalAmount()));
                }
                continue;
            }
            // 重置商品的实际售价
            BigDecimal discount = NumberUtil.div(productDiscount.getDiscount(), CommonConst.TEN, 4);
            // 折扣计算，四舍五入后，如果价格还小于0.01，则取0.01
            BigDecimal discountRealPrice = NumberUtil.mul(sku.getRealSalePrice(), discount).setScale(2, RoundingMode.HALF_UP);
            if (sku.getRealSalePrice().compareTo(BigDecimal.ZERO) > 0 && discountRealPrice.compareTo(CommonConst.DEFAULT_MIN_AMOUNT) < 0) {
                log.info("折扣后的价格小于0.01，skuId={}, productId={}, realSalePrice={}, discount={}, discountRealPrice={}",
                        sku.getSkuId(), sku.getProductId(), sku.getRealSalePrice(), discount, discountRealPrice);
                discountRealPrice = CommonConst.DEFAULT_MIN_AMOUNT;
            }
            sku.setFinalSalePrice(discountRealPrice);
            sku.setDiscountSalePrice(discountRealPrice);
            // 重新计算商品的总金额
            BigDecimal before = sku.getTotalAmount();
            BigDecimal after = NumberUtil.mul(sku.getFinalSalePrice(), sku.getQuantity());
            // 重置商品最终总金额
            sku.setTotalAmount(after);
            // 设置折扣活动ID
            sku.setDiscountActivityId(productDiscount.getActivityId());
            BigDecimal skuDiscountAmount = before.subtract(after);
            // 设置折扣均摊在sku上的金额
            sku.setSplitDiscountAmount(skuDiscountAmount);
            // 汇总商品总金额
            if (Boolean.TRUE.equals(sku.getWhetherSelected())) {
                totalAmount = totalAmount.add(sku.getTotalAmount());
                discountAmount = discountAmount.add(skuDiscountAmount);
            }
            PromotionBo promotionBo = PromotionBo.builder()
                    .promotionType(OrderPromotionType.DISCOUNT.name())
                    .promotionType(OrderPromotionType.DISCOUNT.getDesc())
                    .promotionId(productDiscount.getActivityId())
                    .promotionName(productDiscount.getActivityName())
                    .matchConditionDesc(String.format("id=%s, quota(折扣门槛)=%s, discount(折扣)=%s, izAllProduct(是否适用所有商品)=%s, productIdList(适用商品)=%s",
                            productDiscount.getId(), productDiscount.getQuota(), productDiscount.getDiscount(),
                            productDiscount.getIzAllProduct(), JsonUtil.toJsonString(productDiscount.getProductIdList())))
                    .promotionValueDesc(String.format("满足条件的所有商品的总金额=%s", productDiscount.getProductTotalAmount()))
                    .build();
            addProductPromotion(shop, sku.getProductId(), sku.getSkuId(), promotionBo);
        }
        // 只修改店铺的总金额，不修改商品的总金额
        shop.setSelectedTotalAmount(totalAmount);
        // 设置折扣金额
        additional.setDiscountAmount(discountAmount);
    }

    /**
     * 获取商品满足的折扣信息
     * <AUTHOR>
     * @param sku 商品sku
	 * @param productDiscountMap 当前店铺满足的折扣
     */
    protected ProductMatchDiscountBo getProductDiscount(ShopProductBo sku, Map<Long, ProductMatchDiscountBo> productDiscountMap) {
        // 专享价商品不参与折扣
        if (Boolean.TRUE.equals(sku.getWhetherExclusive())) {
            return null;
        }
        if (MapUtils.isEmpty(productDiscountMap)) {
            return null;
        }
        Long productId = sku.getProductId();
        boolean discountForAll = productDiscountMap.containsKey(CommonConst.ALL_PRODUCT_ID);
        if (discountForAll) {
            return productDiscountMap.get(CommonConst.ALL_PRODUCT_ID);
        }
        return productDiscountMap.get(productId);
    }

    /**
     * 按照店铺对商品进行分组
     *
     * @param validProductList 有效的商品列表
     * <AUTHOR>
     */
    protected Map<ShoppingCartShopBo, List<ShopProductBo>> groupByShop(List<ShopProductBo> validProductList) {
        if (CollUtil.isEmpty(validProductList)) {
            return null;
        }
        return validProductList.stream()
                .collect(Collectors.groupingBy(sku -> {
                    ShoppingCartShopBo shopBo = new ShoppingCartShopBo();
                    shopBo.setShopId(sku.getShopId());
                    shopBo.setShopName(sku.getShopName());
                    // 虽然初始设置在sku上，但同一个店铺的数据是一样的
                    shopBo.setWhetherShopOpenExclusiveMember(sku.getWhetherShopOpenExclusiveMember());
                    shopBo.setWhetherUserBelongExclusiveMember(sku.getWhetherUserBelongExclusiveMember());
                    return shopBo;
                }));
    }

    private void addProductPromotion(ShoppingCartShopBo shop, Long productId, String skuId, PromotionBo promotion) {
        ShopAndProductPromotionBo shopAndProductPromotion = shop.getShopAndProductPromotion();
        if (shopAndProductPromotion == null) {
            shopAndProductPromotion = new ShopAndProductPromotionBo();
            shop.setShopAndProductPromotion(shopAndProductPromotion);
        }
        Map<String/*skuId*/, ProductPromotionBo> productPromotionMap = shopAndProductPromotion.getProductPromotionMap();
        if (MapUtils.isEmpty(productPromotionMap)) {
            productPromotionMap = new HashMap<>();
            shopAndProductPromotion.setProductPromotionMap(productPromotionMap);
        }
        ProductPromotionBo productPromotion = productPromotionMap.get(skuId);
        if (productPromotion == null) {
            productPromotion = ProductPromotionBo.builder()
                    .productId(productId)
                    .skuId(skuId)
                    .promotionList(new ArrayList<>(3))
                    .build();
            productPromotionMap.put(skuId, productPromotion);
        }
        productPromotion.getPromotionList().add(promotion);
    }


    private void appendAddonPromotionDesc(ShoppingCartShopBo shop, ProductMatchDiscountBo matchRule,
                                          int matchIndex, List<RemoteDiscountRuleBo> sortedRuleList,
                                          BigDecimal matchedProductAmount) {
        // matchRule == null 说明没有达到折扣门槛的，需要提示凑单，取门槛最小的
        if (matchRule == null) {
            RemoteDiscountRuleBo rule = sortedRuleList.get(sortedRuleList.size() - 1);
            BigDecimal discount = NumberUtil.div(rule.getDiscount(), CommonConst.TEN, 2);
            BigDecimal discountAmount = NumberUtil.sub(rule.getQuota(), NumberUtil.mul(rule.getQuota(), discount)).setScale(2, RoundingMode.HALF_UP);
            String desc = String.format("订单满%s元可享%s折优惠，立减%s元", rule.getQuota(), rule.getDiscount(), discountAmount);
            shop.addPromotionDesc(new AddonDescBo(AddonProductPromotionTabEnum.DISCOUNT, desc));
            shop.setShowAddonBtn(true);
        } else if (matchIndex == 0) {
            // 前面是门槛降序的，所以判断是不是第一个，如果是第一个就没有更大的折扣门槛了，目前认为门槛和折扣是对应增长的
            String desc = String.format("已购满%s元，享%s折优惠", matchRule.getQuota(), matchRule.getDiscount());
            shop.addPromotionDesc(new AddonDescBo(AddonProductPromotionTabEnum.DISCOUNT, desc));
            shop.setShowAddonBtn(false);
        } else {
            // 此时是即有满足的折扣，也有下一层级的折扣
            // 前面是门槛降序的，所以判断是不是第一个，如果是第一个就没有更大的折扣门槛了，目前认为门槛和折扣是对应增长的
            RemoteDiscountRuleBo nextRule = sortedRuleList.get(matchIndex - 1);
            BigDecimal diff = nextRule.getQuota().subtract(matchedProductAmount);
            BigDecimal discount = NumberUtil.div(nextRule.getDiscount(), CommonConst.TEN, 2);
            BigDecimal nextDiscountAmount = NumberUtil.sub(nextRule.getQuota(), NumberUtil.mul(nextRule.getQuota(), discount)).setScale(2, RoundingMode.HALF_UP);
            String desc = String.format("已购满%s元，享%s折优惠，再凑%s元可享%s折优惠，立减%s元",
                    matchRule.getQuota(), matchRule.getDiscount(), diff, nextRule.getDiscount(), nextDiscountAmount);
            shop.addPromotionDesc(new AddonDescBo(AddonProductPromotionTabEnum.DISCOUNT, desc));
            shop.setShowAddonBtn(true);
        }
    }

    protected boolean validateShopOrderAmountAnyError(ShopProductListBo submitShopOrder, ShopProductListBo realtimeShopOrder) {
        // 计算当前规则下的店铺总金额
        OrderAdditionalBo submitAdditional = submitShopOrder.getAdditional();
        ShoppingCartShopBo submitShop = submitShopOrder.getShop();

        ShoppingCartShopBo currentShop = realtimeShopOrder.getShop();
        OrderAdditionalBo currentAdditional = realtimeShopOrder.getAdditional();

        BigDecimal submitTotalAmount = calculateTotalAmount(submitShop, submitAdditional);
        BigDecimal currentTotalAmount = calculateTotalAmount(currentShop, currentAdditional);
        log.info("校验店铺总金额, submitTotalAmount={}, currentTotalAmount={}", submitTotalAmount, currentTotalAmount);
        return submitTotalAmount.compareTo(currentTotalAmount) != 0;
    }


    /**
     * 比对店铺商品的价格和数量
     * <p>逻辑到此处，前面已经处理完满减和运费的计算了</p>
     * <AUTHOR>
     * @param originShopProductList 页面提交的数据
     * @param currentShopProductList 根据页面提交的，综合当前最新的数据，构建生成的最新的数据，这个数据对象计算了商品最新的单价和各种金额
     * boolean
     */
    protected TradeOrderValidResultEnum validatePriceAndQuantity(Long userId, List<ShopProductListBo> originShopProductList,
                                                                 List<ShopProductListBo> currentShopProductList) {
        Map<Long/*shopId*/, ShopProductListBo> currentShopProductListMap = currentShopProductList.stream()
                .collect(Collectors.toMap(k -> k.getShop().getShopId(), Function.identity(),(o1, o2)->o2));
        // 以页面提交的数据为基础，进行数据校验，并且最后将页面数据设置为返回的数据
        TradeOrderValidResultEnum anyError = null;
        // 遍历校验店铺数据
        for (ShopProductListBo osp : originShopProductList) {
            ShoppingCartShopBo submitShop = osp.getShop();
            // 每个店铺进行校验，以页面提交的为基础
            ShopProductListBo currentSp = currentShopProductListMap.get(submitShop.getShopId());
            // 如果店铺开启了专属，且当前用户不是专属商家，则不能提交订单
            ShoppingCartShopBo currentOrderShop = currentSp.getShop();
            if (Boolean.TRUE.equals(currentOrderShop.getWhetherShopOpenExclusiveMember()) &&
                    !Boolean.TRUE.equals(currentOrderShop.getWhetherUserBelongExclusiveMember())) {
                throw new BusinessException(String.format("当前商家不是店铺 %s 的专属商家，请移除店铺商品后重新提交", currentOrderShop.getShopName()));
            }
            TradeOrderValidResultEnum hasErr = validateAndSetMessageForShop(userId, osp, currentSp);
            if (TradeOrderValidResultEnum.anyError(hasErr)) {
                anyError = hasErr;
                break;
            }
        }
        return anyError;
    }

    protected TradeOrderValidResultEnum validateAndSetMessageForShop(Long userId, ShopProductListBo originShopProductList, ShopProductListBo currentShopProductList) {
        boolean anyError = false;
        // 先校验商品明细的价格金额等是否发生了变化，用户可以看到更具体的报错
        Map<String, ShopProductBo> currentShopProductListMap = currentShopProductList.getProductList().stream()
                .collect(Collectors.toMap(ShopProductBo::getSkuId, Function.identity(), (o1, o2) -> o2));
        // 先每个店铺都查
        Map<Long, Long> productBuyCountMap = this.getProductBuyCount(userId, currentShopProductList.getProductIdList());
        // 遍历店铺商品进行校验，以页面提交的为基础
        for (ShopProductBo origin : originShopProductList.getProductList()) {
            ShopProductBo current = currentShopProductListMap.get(origin.getSkuId());
            // 商品sku不存在，或被删除，或非销售中
            if (current == null || Boolean.TRUE.equals(current.getWhetherDeleted())) {
                log.info("商品sku不存在，或被删除，或非销售中");
                origin.setErrorMsg(MessageConst.PreOrder.PRODUCT_NOT_ON_SALE);
                anyError = true;
                continue;
            }
            if (!ProductEnum.SaleStatusEnum.ON_SALE.equals(current.getSaleStatus())) {
                log.info("商品未上架");
                origin.setErrorMsg(MessageConst.PreOrder.PRODUCT_NOT_ON_SALE);
                anyError = true;
                continue;
            }
            if (!ProductEnum.AuditStatusEnum.ON_SALE.equals(current.getAuditStatus())) {
                log.info("商品未审核通过");
                origin.setErrorMsg(MessageConst.PreOrder.PRODUCT_NOT_ON_SALE);
                anyError = true;
                continue;
            }
            // 页面提交的价格与最新的价格是否一致
            if (origin.getFinalSalePrice().compareTo(current.getFinalSalePrice()) != 0) {
                log.info("页面提交的价格与最新的价格不一致, pageSalePrice={}, currentSalePrice={}", origin.getRealSalePrice(), current.getRealSalePrice());
                origin.setErrorMsg(MessageConst.PreOrder.PRODUCT_OR_PRICE_UPDATED);
                anyError = true;
                continue;
            }
            // 页面提交的数量是否是否大于当前库存
            long quantity = origin.getQuantity();
            String unit = StrUtil.nullToEmpty(current.getMeasureUnit());
            if (current.getSkuStock() != null && quantity > current.getSkuStock()) {
                log.info("页面提交的数量是否是否大于当前库存, quantity={}, skuStock={}", quantity, current.getSkuStock());
                origin.setErrorMsg(String.format(MessageConst.PreOrder.LACK_OF_STOCK, current.getSkuStock(), unit));
                anyError = true;
                continue;
            }
            // 页面提交的数量是否小于最新的起购量
            if (current.getMinBuyCount() != null && quantity < current.getMinBuyCount()) {
                log.info("页面提交的数量是否小于最新的起购量, quantity={}, minBuyCount={}", quantity, current.getMinBuyCount());
                origin.setErrorMsg(String.format(MessageConst.PreOrder.LESS_THAN_MIN_BUY_COUNT, current.getMinBuyCount(), unit));
                anyError = true;
                continue;
            }
            // 页面提交的数量是否大于限购数
            long boughtCount = NumberUtil.nullToZero(productBuyCountMap.get(current.getProductId()));
            if (needCheckMaxBuyCount(current.getMaxBuyCount()) && quantity + boughtCount > current.getMaxBuyCount()) {
                long remain = current.getMaxBuyCount() - boughtCount;
                log.info("页面提交的数量是否大于限购数, quantity={}, maxBuyCount={}, bought={}, remain={}", quantity, current.getMaxBuyCount(), boughtCount, remain);
                if (remain < 0) {
                    remain = 0;
                }
                origin.setErrorMsg(String.format(MessageConst.PreOrder.OVER_MAX_BUY_COUNT,
                        current.getMaxBuyCount(), unit, boughtCount, unit, remain, unit));
                anyError = true;
                continue;
            }
            // 页面提交的数量是否满足倍数起购量
            if (current.getMultipleCount() != null && quantity % current.getMultipleCount() != 0) {
                log.info("页面提交的数量是否满足倍数起购量, quantity={}, multipleCount={}", quantity, current.getMultipleCount());
                origin.setErrorMsg(String.format(MessageConst.PreOrder.NOT_MATCH_MULTI_BUY_COUNT, current.getMultipleCount()));
                anyError = true;
            }
        }
        // 此时是商品维度的异常
        if (anyError) {
            return TradeOrderValidResultEnum.PRODUCT_FAIL;
        }
        // 再校验订单总金额，这里不用查数据，如果总金额不匹配，优先提示让刷新页面，大概率是营销活动，运费等发生了变化
        anyError = validateShopOrderAmountAnyError(originShopProductList, currentShopProductList);
        if (anyError) {
            return TradeOrderValidResultEnum.ORDER_AMOUNT_NOT_MATCH;
        }
        return TradeOrderValidResultEnum.SUCCESS;
    }

    protected void checkBuyQuantityForChange(Long changedQuantity, ShopProductBo sku, Map<Long, Long> productBuyCountMap) {
        String productName = getShortProductName(sku.getProductName());
        if (changedQuantity == null || changedQuantity <= 0) {
            throw new BusinessException(String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_GREAT_THAN_ZERO, productName));
        }
        if (!ProductEnum.SaleStatusEnum.ON_SALE.equals(sku.getSaleStatus())) {
            throw new BusinessException(String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_OFF_SALE, productName));
        }
        if (Boolean.TRUE.equals(sku.getWhetherDeleted())) {
            throw new BusinessException(String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_INVALID, productName));
        }
        long boughtCount = NumberUtil.nullToZero(productBuyCountMap.get(sku.getProductId()));

        String unit = StrUtil.nullToEmpty(sku.getMeasureUnit());
        // 默认错误码，需要前端根据返回的数据重置输入框数据，然后重新请求接口
        int errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL.getValue();
        // 没法完全根据规则计算一个一定符合的数字，所以目前返回的都是每个异常对应的数字
        // 比如 剩余可购买数只剩下1，但是最小起购量是2，此时不管是返回1还是2，下一步操作还是会报错
        if (changedQuantity < sku.getMinBuyCount()) {
            throw new ChangeQuantityException(errCode, productName,
                    String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_MIN_BUY_COUNT, sku.getMinBuyCount(), unit), (long)sku.getMinBuyCount());
        }
        // 库存与倍数起购量的校验，需要先校验库存，否则倍数里面计算错误数量时会不对
        if (sku.getSkuStock() < changedQuantity) {
            long errQ = (sku.getSkuStock() / sku.getMultipleCount()) * sku.getMultipleCount();
            // 需要变更的数量小于等于0时，前端不变更
            if (errQ <= 0) {
                errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL_HOLD.getValue();
                errQ = sku.getMinBuyCount();
            }
            throw new ChangeQuantityException(errCode, productName,
                    String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_LACK_STOCK, sku.getSkuStock(), unit), errQ);
        }
        if (changedQuantity % sku.getMultipleCount() != 0) {
            long b = changedQuantity / sku.getMultipleCount();
            // 给用户重置的数量优先向上取
            long errQ = (b + 1) * sku.getMultipleCount();
            // 因为前面已经校验了库存，所以此时 b * sku.getMultipleCount() 一定在库存范围内
            if (errQ > sku.getSkuStock()) {
                errQ = b * sku.getMultipleCount();
            }
            // 如果数量小于最小起购量，则前端还原不变化
            if (errQ < sku.getMinBuyCount()) {
                errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL_HOLD.getValue();
                errQ = sku.getMinBuyCount();
            }
            throw new ChangeQuantityException(errCode, productName,
                    String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_MULTIPLE, sku.getMultipleCount()), errQ);
        }
        if (needCheckMaxBuyCount(sku.getMaxBuyCount()) && changedQuantity + boughtCount > sku.getMaxBuyCount()) {
            long remain = sku.getMaxBuyCount() - boughtCount;
            log.info("页面提交的数量是否大于限购数, quantity={}, minBuyCount={}, maxBuyCount={}, stock={}, bought={}, remain={}",
                    changedQuantity, sku.getMinBuyCount(), sku.getMaxBuyCount(), sku.getSkuStock(), boughtCount, remain);
            if (remain < 0) {
                remain = 0;
            }
            long errQ = remain;
            // 如果剩余数量为0，或者数量小于最小起购量，或者剩余小于库存，则前端还原不变化，因为没法计算一个还能正常购买的值
            if (remain == 0 || remain < sku.getMinBuyCount() || sku.getSkuStock() < remain) {
                errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL_HOLD.getValue();
                // 此时页面无法判断如何操作，直接先返回用户输入的数值，用户自己决定如何调整
                errQ = changedQuantity;
            } else {
                long b = remain / sku.getMultipleCount();
                // 给用户重置的数量优先向上取
                errQ = b * sku.getMultipleCount();
                // 如果数量小于最小起购量，则前端还原不变化
                if (errQ < sku.getMinBuyCount()) {
                    errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL_HOLD.getValue();
                    errQ = sku.getMinBuyCount();
                }
            }
            throw new ChangeQuantityException(errCode, productName,
                    String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_MAX_BUY_COUNT,
                            sku.getMaxBuyCount(), unit, boughtCount, unit, remain, unit), errQ);
        }

    }

    protected String getShortProductName(String productName) {
        if (productName.length() <= CommonConst.CART_ERROR_MSG_PRODUCT_NAME_LENGTH) {
            return productName;
        }
        return productName.substring(0, CommonConst.CART_ERROR_MSG_PRODUCT_NAME_LENGTH) + CommonConst.CART_ERROR_MSG_PRODUCT_NAME_SHORT;
    }

    /**
     * 计算店铺商品sku的价格和总金额
     *
     * @param shop          店铺
     * @param shopSkuList   店铺的sku
     * @param shopPromotion 店铺的营销
     * <AUTHOR>
     */
    protected void calculatePriceAndAmountForShop(BuildContext context,
                                                  ShoppingCartShopBo shop,
                                                  List<ShopProductBo> shopSkuList,
                                                  RemoteShopUserPromotionBo shopPromotion) {
        dataProcessorAssist.calculatePriceAndAmountForShop(context, shop, shopSkuList, shopPromotion);
    }


    /**
     * 计算优惠券，子类按需调用
     *
     * @param context  上下文
     * @param dataHolder 数据
     * <AUTHOR>
     */
    protected void calculateCoupon(BuildContext context, H dataHolder) {
        // 处理优惠券
        long begin = System.currentTimeMillis();
        dataProcessorAssist.calculateCoupon(context, dataHolder);
        long end = System.currentTimeMillis();
        log.info("计算优惠券耗时:{}", end - begin);
    }


    /**
     * 拆分均摊营销优惠。
     * 折扣在折扣计算的逻辑中已经处理均摊了，这里主要是处理满减和优惠券
     * 满减是针对订单所有商品的， 优惠券可能针对所有商品，可能是部分商品，优惠券的商品范围在优惠金额的计算中已经处理了，设置了优惠券ID
     *
     * <AUTHOR>
     * void
     */
    protected void splitPromotionAmount(BuildContext context, H dataHolder) {
        // 遍历每个订单，计算每个订单的满减均摊和优惠券均摊
        dataProcessorAssist.splitPromotionAmount(dataHolder);
    }

    @FunctionalInterface
    public interface TriConsumer<T, U, V> {
        void accept(T t, U u, V v);
    }

    protected boolean needCheckMaxBuyCount(Integer maxBuyCount) {
        // null 是ERP同步的可能为null，本系统需求0代表不限制
        return maxBuyCount != null && maxBuyCount > 0;
    }

    /**
     * 填充额外的订单预览数据
     * <p>发票、保障标识、是否显示优惠券</p>
     *
     * @param dataHolder 数据存储器
     */
    protected void fillExtraOrderPreviewData(Long userId, H dataHolder) {
        // 先设置数据
        dataProcessorAssist.fillExtraOrderPreviewData(userId, dataHolder.getShopProductList());
        // 预览订单页面，如果没有满减，需要获取并设置优惠金额最大的优惠券，上面设置数据的逻辑中获取了优惠券
        for (ShopProductListBo shopOrder : dataHolder.getShopProductList()) {
            OrderAdditionalBo additional = shopOrder.getAdditional();
            // 处理设置默认面额最大的优惠券，fillExtraOrderPreviewData 是最后处理的，此时如果有满减，已经处理过了
            // 满减与优惠券互斥，默认有满减计算满减，没有满减的情况下，设置优惠券
            if (additional.getReductionActivityId() != null) {
                continue;
            }
            List<CouponRecordSimpleResp> validCouponList = shopOrder.getValidCouponList();
            if (CollUtil.isEmpty(validCouponList)) {
                continue;
            }
            resetAndReCalCoupon(shopOrder.getShop(), additional, validCouponList, shopOrder.getProductList());
        }
    }

    protected Map<Long, Long> getProductBuyCount(Long userId, List<Long> productIdList) {
        return orderRemoteService.getUserProductBuyCountMap(userId, productIdList);
    }

    protected BigDecimal getSkuSalePrice(RemoteProductSkuBo skuDto) {
        // sku的价格，开启阶梯价的，sku上的价格可以不设置，此时取最小价
        BigDecimal salePrice = skuDto.getSalePrice();
        // 如果价格设置为0，取多规格的最小价，如果还是0，则认为价格就是0，其他价格等后续逻辑处理
        // salePrice为空的，比如多规格的，salePrice可以不设置
        if (salePrice == null || BigDecimal.ZERO.compareTo(salePrice) >= 0) {
            salePrice = skuDto.getMinSalePrice();
        }
        if (salePrice == null) {
            throw new BusinessException(String.format("skuId=%s商品价格未设置，请检查", skuDto.getSkuId()));
        }
        return salePrice;
    }

    /**
     * 订单是否含有优惠券
     * 预览订单页面对于优惠券的处理，是在订单额外信息 OrderAdditionalBo 中设置
     * <AUTHOR>
     * @param additional
     * boolean
     */
    protected boolean hasCoupon(OrderAdditionalBo additional) {
        if (additional == null) {
            return false;
        }
        return additional.getCouponRecordId() != null && additional.getCouponRecordId() > 0;
    }
    
    
    protected void resetAndReCalCoupon(ShoppingCartShopBo shop, OrderAdditionalBo additional, List<CouponRecordSimpleResp> validCouponList, List<ShopProductBo> productList) {

        resetCouponInfo(shop, additional);

        // 设置的时候已经排序，面额最大的在前面
        // 优惠券有使用门槛，也可能会指定商品，所以判断的时候进行区分
        // 专享价商品不参与优惠券，所以需要重新计算总金额，而不是取 shop.getSelectedTotalAmount()
        BigDecimal shopAmount = dataProcessorAssist.calculateSkuTotalAmountBesideExclusive(productList);
        CouponRecordSimpleResp coupon = getMostMatchCoupon(shopAmount, validCouponList);
        log.info("重新计算优惠券，shopAmount={}, 当前优惠券为: {}", shopAmount, JsonUtil.toJsonString(coupon));
        if (coupon == null) {
            return;
        }
        reCalCoupon(coupon, shop, additional, shopAmount, productList);
    }

    protected void reCalCoupon(CouponRecordSimpleResp coupon, ShoppingCartShopBo shop, OrderAdditionalBo additional, BigDecimal shopAmount, List<ShopProductBo> productList) {
        BigDecimal couponAmount = new BigDecimal(coupon.getPrice());
        // 如果是部分商品适用，则需要比较最多能减的优惠券金额
        if (UseAreaEnum.PRODUCT.getCode().equals(coupon.getUseArea())) {
            // 商品列表数据，根据productId分组，并求和商品总金额
            Map<Long, BigDecimal> orderProductAmountMap = productList.stream()
                    .collect(Collectors.groupingBy(ShopProductBo::getProductId, Collectors.reducing(BigDecimal.ZERO, ShopProductBo::getTotalAmount, BigDecimal::add)));
            Map<Long, Long> couponProductIdMap = coupon.getProductIdList().stream()
                    .collect(Collectors.toMap(Long::longValue, Long::longValue, (o1, o2) -> o2));
            // 商品ID列表，这里前面优惠券已经是当前店铺适用的，理论上一定可以查到商品ID这些，没做判空
            List<Long> suitProductIdList = productList.stream()
                    // 不能直接用false比较，因为有可能是null
                    .filter(sku -> !Boolean.TRUE.equals(sku.getWhetherExclusive()))
                    .map(ShopProductBo::getProductId)
                    .filter(couponProductIdMap::containsKey)
                    // 订单商品是sku维度，这里获取商品ID，可能重复
                    .distinct()
                    .collect(Collectors.toList());
            // 指定商品可用时，重置订单中匹配优惠券商品范围的总金额
            shopAmount = suitProductIdList.stream()
                    .map(orderProductAmountMap::get)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 取匹配优惠券商品范围的总金额和优惠券金额的最小值
        BigDecimal realCouponAmount = shopAmount.compareTo(couponAmount) > 0 ? couponAmount : shopAmount;

        additional.setCouponRecordId(coupon.getId());
        additional.setCouponId(coupon.getCouponId());
        additional.setCouponAmount(realCouponAmount);
        // 重置店铺总金额
        shop.setSelectedTotalAmount(shop.getSelectedTotalAmount().subtract(realCouponAmount));
    }

    protected void resetCouponInfo(ShoppingCartShopBo shop, OrderAdditionalBo additional) {
        // 重置之前的优惠券
        //shop.setSelectedTotalAmount(shop.getSelectedTotalAmount().add(NumberUtil.nullToZero(additional.getCouponAmount())));
        additional.setCouponId(null);
        additional.setCouponRecordId(null);
        additional.setCouponAmount(BigDecimal.ZERO);
    }

    /**
     * 获取有效优惠券列表中与店铺金额最匹配的
     * <p>这个有效优惠券列表已经是满足店铺商品和金额门槛的了，所以这里其实是根据规则返回一张相对最满足用户利益的优惠券</p>
     * <AUTHOR>
     * @param shopAmount 店铺商品金额
	 * @param validCouponList 用户当前有效的优惠券列表
     */
    private CouponRecordSimpleResp getMostMatchCoupon(BigDecimal shopAmount, List<CouponRecordSimpleResp> validCouponList) {
        if (CollUtil.isEmpty(validCouponList)) {
            return null;
        }
        // 优惠券面额已经是根据金额降序的了
        CouponRecordSimpleResp tempCoupon = null;
        for (CouponRecordSimpleResp coupon : validCouponList) {
            if (coupon == null) {
                continue;
            }
            BigDecimal couponAmount = new BigDecimal(coupon.getPrice());
            // 如果店铺金额小于优惠券金额，继续往后面判断是否有优惠券大于店铺金额且更接近店铺金额的
            if (shopAmount.compareTo(couponAmount) < 0) {
                // 如果当前优惠券面值与temp一致，则直接返回，防止有很多同样面值的需要全部遍历完
                if (tempCoupon != null && tempCoupon.getPrice().equals(coupon.getPrice())) {
                    return tempCoupon;
                }
                tempCoupon = coupon;
                continue;
            }
            if (shopAmount.compareTo(couponAmount) == 0) {
                return coupon;
            }
            // 此时是店铺金额大于优惠券金额，如果临时的(面额大于店铺金额的)没有，则返回当前优惠券，否则返回临时优惠券
            if (tempCoupon != null) {
                return tempCoupon;
            }
            return coupon;
        }
        if (tempCoupon != null) {
            return tempCoupon;
        }
        // 前置已经比较了店铺金额与优惠券面额的三种大小关系，理论上不会走到这里，兜底默认选择面额最小的
        return validCouponList.get(validCouponList.size() - 1);
    }

    /**
     * 获取sku图片，如果为空，取商品主图
     * <AUTHOR>
     * @param sku
     * java.lang.String
     */
    protected String getSkuImagePath(RemoteProductSkuBo sku) {
        String skuImagePath = sku.getShowPic();
        if (StrUtil.isNotBlank(skuImagePath)) {
            return skuImagePath;
        }
        return sku.getImagePath();
    }


}
