package com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper;

import java.util.List;

import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.StockImportDto;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/21 22:14
 */
@AllArgsConstructor
public class StockImportWrapper extends DataWrapper<StockImportDto> {

    private List<StockImportDto> dataList;

    @Override
    public Integer getModule() {
        return null;
    }

    @Override
    public String getFileName() {
        return "库存导入错误数据";
    }

    @Override
    public List<StockImportDto> getDataList() {
        return dataList;
    }
}
