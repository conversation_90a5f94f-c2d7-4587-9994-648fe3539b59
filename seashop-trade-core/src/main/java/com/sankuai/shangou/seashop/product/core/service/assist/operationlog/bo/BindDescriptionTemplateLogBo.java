package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.model.BindDescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescription;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 19:28
 */
@Getter
@Setter
public class BindDescriptionTemplateLogBo {

    @ExaminField(description = "商品列表", isChildField = true, entityClassName = "com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.BindDescriptionTemplateItemLogBo")
    private List<BindDescriptionTemplateItemLogBo> productList;

    public static BindDescriptionTemplateLogBo build(List<ProductDescription> dbDescriptionList) {
        BindDescriptionTemplateLogBo bo = new BindDescriptionTemplateLogBo();
        if (CollectionUtils.isEmpty(dbDescriptionList)) {
            return bo;
        }
        bo.setProductList(JsonUtil.copyList(dbDescriptionList, BindDescriptionTemplateItemLogBo.class));
        return bo;
    }

    public static BindDescriptionTemplateLogBo build(BindDescriptionTemplateBo bindBo) {
        BindDescriptionTemplateLogBo bo = new BindDescriptionTemplateLogBo();
        if (CollectionUtils.isEmpty(bindBo.getProductIdList())) {
            return bo;
        }

        List<BindDescriptionTemplateItemLogBo> productList = new ArrayList<>();
        bindBo.getProductIdList().forEach(productId -> {
            BindDescriptionTemplateItemLogBo itemBo = new BindDescriptionTemplateItemLogBo();
            itemBo.setProductId(productId);
            itemBo.setDescriptionPrefixId(bindBo.getDescriptionPrefixId());
            itemBo.setDescriptionSuffixId(bindBo.getDescriptionSuffixId());
            productList.add(itemBo);
        });
        bo.setProductList(productList);
        return bo;
    }
}
