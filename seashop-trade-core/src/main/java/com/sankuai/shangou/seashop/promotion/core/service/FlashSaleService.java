package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.*;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
public interface FlashSaleService {

    /**
     * 新增限时购活动
     *
     * @param request
     */
    void add(FlashSaleAddReq request);

    /**
     * 修改限时购活动
     *
     * @param request
     */
    void update(FlashSaleUpdateReq request);

    /**
     * 审核限时购活动
     *
     * @param request
     */
    void audit(FlashSaleAuditReq request);

    /**
     * 前端显示限时购活动
     *
     * @param request
     */
    void show(FlashSaleShowReq request);

    /**
     * 结束限时购活动
     *
     * @param request
     */
    void endActive(BaseIdReq request);

    /**
     * 分页查询限时购活动列表
     *
     * @param request
     * @return
     */
    BasePageResp<FlashSaleSimpleResp> pageList(FlashSaleQueryReq request);

    /**
     * 通过id查询限时购活动信息
     *
     * @param request
     * @return
     */
    FlashSaleResp getById(BaseIdReq request);

    /**
     * 商城页面限时购活动列表查询
     *
     * @param request
     * @return
     */
    BasePageResp<MallFlashSaleResp> mallPageList(MallFlashSaleQueryReq request);

    /**
     * 根据店铺ID和商品ID集合商城页面限时购活动列表查询
     *
     * @param request
     * @return
     */
    MallAppletFlashSaleListResp mallAppletPageList(MallAppletFlashSaleQueryReq request);

    /**
     * 组件页面限时购活动列表查询
     *
     * @param request
     * @return
     */
    BasePageResp<VisualFlashSaleResp> componentPageList(VisualFlashSaleQueryReq request);

    /**
     * 通过商品id查询限时购活动
     *
     * @param request
     * @return
     */
    FlashSaleResp queryByProductId(ProductAndShopIdReq request);

    /**
     * 通过skuId查询限时购活动
     *
     * @param request
     * @return
     */
    SkuFlashSaleDetailResp queryValidWithSkuId(QuerySkuFlashSaleReq request);

    /**
     * 查询有效限时购活动列表
     *
     * @param request
     * @return
     */
    EffectiveFlashSaleQueryListResp queryEffectiveFlashSaleList(EffectiveFlashSaleQueryReq request);

    /**
     * 限时购活动核销
     *
     * @param request
     */
    void consume(FlashSaleConsumeReq request);

    /**
     * 限时购活动撤销核销
     *
     * @param request
     */
    void cancelConsume(FlashSaleCancelConsumeReq request);

    /**
     * 限时购活动库存回滚
     *
     * @param request
     */
    void stockReturn(FlashSaleStockReturnReq request);

    /**
     * 根据限时购id查询可视化组件限时购活动列表
     *
     * @param req
     * @return
     */
    VisualFlashSaleListResp queryVisualFlashSaleList(FlashSaleQueryByIdReq req);
}
