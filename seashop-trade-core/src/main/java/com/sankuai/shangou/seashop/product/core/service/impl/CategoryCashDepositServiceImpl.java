package com.sankuai.shangou.seashop.product.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.core.service.CategoryCashDepositService;
import com.sankuai.shangou.seashop.product.core.service.CategoryService;
import com.sankuai.shangou.seashop.product.core.service.SkuService;
import com.sankuai.shangou.seashop.product.core.service.assist.CategoryAssist;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.SkuQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.dao.core.domain.CategoryCashDeposit;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryCashDepositRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.*;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositMapResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositResp;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.EnoughCashFlagReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Service
public class CategoryCashDepositServiceImpl implements CategoryCashDepositService {

    @Resource
    private CategoryRepository categoryRepository;
    @Resource
    private CategoryCashDepositRepository categoryCashDepositRepository;
    @Resource
    private CategoryService categoryService;
    @Resource
    private SkuService skuService;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private CategoryAssist categoryAssist;
    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private BaseLogAssist baseLogAssist;

    @Override
    public List<CategoryCashDepositResp> queryAll() {

        List<Category> categoryList = categoryRepository.queryFirst(CommonConstant.DEFAULT_PARENT_ID);
        if (CollUtil.isNotEmpty(categoryList)) {
            List<Long> categoryIdList = categoryList.stream().map(Category::getId).collect(Collectors.toList());
            List<CategoryCashDeposit> categoryCashDeposits = categoryCashDepositRepository.queryByCategoryId(categoryIdList);
            List<CategoryCashDepositResp> resultList = new ArrayList<>(categoryList.size());
            for (Category category : categoryList) {
                CategoryCashDepositResp categoryCashDepositResp = new CategoryCashDepositResp();
                categoryCashDepositResp.setCategoryId(category.getId());
                categoryCashDepositResp.setCategoryName(category.getName());

                CategoryCashDeposit categoryCashDeposit = categoryCashDeposits.stream().filter(c -> c.getCategoryId().equals(category.getId())).findFirst().orElse(null);
                if (null != categoryCashDeposit) {
                    categoryCashDepositResp.setNeedPayCashDeposit(categoryCashDeposit.getNeedPayCashDeposit());
                    categoryCashDepositResp.setEnableNoReasonReturn(categoryCashDeposit.getEnableNoReasonReturn());
                } else {
                    categoryCashDepositResp.setNeedPayCashDeposit(BigDecimal.ZERO);
                    categoryCashDepositResp.setEnableNoReasonReturn(Boolean.FALSE);
                }

                resultList.add(categoryCashDepositResp);
            }
            return resultList;
        }
        return CollUtil.newArrayList();
    }

    @Override
    public ResultDto<BaseResp> save(CategoryCashDepositListReq request) {
        List<CategoryCashDepositReq> list = request.getList();
        List<CategoryCashDeposit> categoryCashDeposits = categoryCashDepositRepository.queryByCategoryId(
                list.stream().map(CategoryCashDepositReq::getCategoryId).collect(Collectors.toList()));
        List<CategoryCashDeposit> insertList = new ArrayList<>(list.size());
        for (CategoryCashDepositReq req : list) {
            CategoryCashDeposit categoryCashDeposit = new CategoryCashDeposit();
            categoryCashDeposit.setCategoryId(req.getCategoryId());
            categoryCashDeposit.setNeedPayCashDeposit(req.getNeedPayCashDeposit());
            categoryCashDeposit.setEnableNoReasonReturn(req.getEnableNoReasonReturn());

            if (CollUtil.isNotEmpty(categoryCashDeposits)) {
                CategoryCashDeposit queryCategoryCashDeposit = categoryCashDeposits.stream().filter(c -> c.getCategoryId().equals(req.getCategoryId())).findFirst().orElse(null);
                if (null != queryCategoryCashDeposit) {
                    categoryCashDeposit.setId(queryCategoryCashDeposit.getId());
                }
            }
            insertList.add(categoryCashDeposit);
        }
        categoryCashDepositRepository.saveOrUpdateBatch(insertList);

        CategoryCashDepositLog newValue = JsonUtil.copy(list.get(0), CategoryCashDepositLog.class);
        if(categoryCashDeposits.size() > 0) {
            CategoryCashDepositLog oldValue = JsonUtil.copy(categoryCashDeposits.get(0), CategoryCashDepositLog.class);
            // 手动写日志
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,"保证金规则设置",
                    request.getOperationUserId(), request.getOperationShopId(),
                    oldValue, newValue);
        } else {
            // 手动写日志
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.INSERT,"保证金规则设置",
                    request.getOperationUserId(), request.getOperationShopId(),
                    null, newValue);
        }

        return ResultDto.newWithData(new BaseResp());
    }

    @Override
    public List<CategoryCashDepositMapResp> queryByCategoryList(QueryFirstCategoryReq request) {
        List<CategoryCashDepositResp> categoryCashDeposits = this.queryAll();

        // 查询类目信息
        CategoryQueryBo categoryQueryBo = new CategoryQueryBo();
        categoryQueryBo.setIds(request.getCategoryIds());
        List<CategoryBo> categoryBos = categoryService.queryCategoryList(categoryQueryBo);

        List<CategoryCashDepositMapResp> resultList = new ArrayList<>(categoryBos.size());
        for (Long categoryId : request.getCategoryIds()) {
            CategoryCashDepositMapResp categoryCashDepositMapResp = new CategoryCashDepositMapResp();
            categoryCashDepositMapResp.setCategoryId(categoryId);

            CategoryBo categoryBo = categoryBos.stream().filter(c -> c.getId().equals(categoryId)).findFirst().orElse(null);
            if (null != categoryBo && CollUtil.isNotEmpty(categoryBo.getFullIds())) {
                Long parentId = categoryBo.getFullIds().get(0);
                CategoryCashDepositResp categoryCashDepositResp = categoryCashDeposits.stream().filter(c -> c.getCategoryId().equals(parentId)).findFirst().orElse(null);
                categoryCashDepositMapResp.setCategoryCashDepositResp(categoryCashDepositResp);
            }
            resultList.add(categoryCashDepositMapResp);
        }

        return resultList;
    }

    @Override
    public List<SkuFitCategoryCashDepositResp> queryBySkuList(QueryDepositConfigBySkuReq request) {
        // 根据SKU获取适配商品的类目信息并转换对象
        List<SkuFitCategoryCashDepositResp> skuCateList = getSkuFitCate(request.getSkuIdList());
        if (CollUtil.isEmpty(skuCateList)) {
            return Collections.emptyList();
        }
        // 获取所有的保证金配置
        List<CategoryCashDepositResp> categoryCashDeposits = this.queryAll();
        if (CollUtil.isEmpty(categoryCashDeposits)) {
            return Collections.emptyList();
        }
        Map<Long, CategoryCashDepositResp> cateCashDepositMap = categoryCashDeposits.stream()
                .collect(Collectors.toMap(CategoryCashDepositResp::getCategoryId, c -> c, (k1, k2) -> k2));
        // 遍历sku类目对象，设置保证金配置
        skuCateList.forEach(sku -> {
            CategoryCashDepositResp categoryCashDepositResp = cateCashDepositMap.get(sku.getCateLevel1Id());
            sku.setCategoryCashDepositResp(categoryCashDepositResp);
            EnoughCashFlagReq enoughCashFlagReq = new EnoughCashFlagReq();
            enoughCashFlagReq.setShopId(sku.getShopId());
            enoughCashFlagReq.setCategoryId(categoryCashDepositResp.getCategoryId());
            sku.setEnoughCashFlag(ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.enoughCashFlag(enoughCashFlagReq)));
        });
        return skuCateList;
    }




    //******************************************************

    private List<SkuFitCategoryCashDepositResp> getSkuFitCate(List<String> skuIdList) {
        ProductSkuQueryReq req=new ProductSkuQueryReq();
        req.setSkuIdList(skuIdList);
        List<Sku> skuList = skuService.querySkuList(req);
        if (CollUtil.isEmpty(skuList)) {
            return Collections.emptyList();
        }
        // 商品map
        List<Long> productIdList = skuList.stream().map(Sku::getProductId).collect(Collectors.toList());
        Map<Long, Product> productMap = productRepository.getProductMap(productIdList);
        if (MapUtil.isEmpty(productMap)) {
            return Collections.emptyList();
        }
        return skuList.stream()
                .map(sku -> {
                    SkuFitCategoryCashDepositResp resp = new SkuFitCategoryCashDepositResp();
                    resp.setSkuId(sku.getSkuId());
                    resp.setProductId(sku.getProductId());

                    Product product = productMap.get(sku.getProductId());
                    resp.setCategoryPath(product.getCategoryPath());
                    resp.setCateLevel3Id(product.getCategoryId());
                    resp.setShopId(product.getShopId());
                    List<Long> cateIds = categoryAssist.getCategoryIds(product.getCategoryPath());
                    if (CollUtil.isNotEmpty(cateIds)) {
                        resp.setCateLevel1Id(cateIds.get(0));
                    }
                    return resp;
                })
                .collect(Collectors.toList());
    }
}
