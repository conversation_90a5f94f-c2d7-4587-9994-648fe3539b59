package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.handler;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.AbsActivityQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 折扣查询处理器
 *
 * <AUTHOR>
 * @date 2023/12/23 10:33
 */
@Component
@Slf4j
public class DiscountActiveQueryHandler extends AbsActivityQueryHandler {

    @Resource
    private PromotionRemoteService promotionRemoteService;

    @Override
    protected void query(ActivityContext context) {
        log.info("查询折扣活动, context: {}", context);
        context.setDiscountActive(promotionRemoteService.getDiscountActive(context.getProductId(), context.getShopId()));
        log.info("查询折扣活动结果, context: {}, activity: {}", context, JsonUtil.toJsonString(context.getDiscountActive()));
    }

    @Override
    protected Class<? extends AbsActivityQueryHandler> nextHandler() {
        return FullReductionQueryHandler.class;
    }

    /**
     * 折扣与专享价、限时抢购、组合购互斥
     * 商品详情页面需要同时展示限时购购买和原价购买, 所以限时购不影响折扣活动
     * 折扣活动和组合购虽然互斥, 但是参加哪种活动由客户选择, 所以也需要查询
     *
     * @param context 活动上下文对象
     * @return true 支持，false 不支持
     */
    @Override
    protected boolean support(ActivityContext context) {
        return context.getExclusivePrice() == null;
        // && context.getFlashSale() == null
        // && context.getCollectionBuy() == null;
    }
}
