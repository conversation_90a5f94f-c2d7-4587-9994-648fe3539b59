package com.sankuai.shangou.seashop.product.core.service.assist;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductModel;
import com.sankuai.shangou.seashop.product.common.remote.base.RemoteSiteSettingService;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteFreightAreaService;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.core.service.CategoryService;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRelationProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductShopCategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopCategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockRepository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2023/12/26 16:28
 */
@Component
public class ProductExtAssist {

    @Resource
    private CategoryService categoryService;
    @Resource
    private BrandRepository brandRepository;
    @Resource
    private ShopCategoryRepository shopCategoryRepository;
    @Resource
    private ProductRelationProductRepository productRelationProductRepository;
    @Resource
    private SkuStockRepository skuStockRepository;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private SkuStockAuditRepository skuStockAuditRepository;
    @Resource
    private ProductAssist productAssist;
    @Resource
    private RemoteFreightAreaService remoteFreightAreaService;
    @Resource
    private ProductShopCategoryRepository productShopCategoryRepository;

    @Resource
    private RemoteSiteSettingService remoteSiteSettingService;

    /**
     * 默认H5地址key
     */
    public static final String DEFAULT_H5_URL_KEY = "productH5Url";

    /**
     * 填充拓展信息
     *
     * @param productList 商品列表
     */
    public void fillProductExtData(ProductQueryBo queryBo, List<ProductPageBo> productList, boolean fromAudit) {
        fillBrand(productList);
        fillCategory(productList);
        fillShopCategory(productList);
        fillStock(productList, fromAudit);
        fillRelationProduct(productList);
        fillShop(productList);
        fillPriceRange(productList);
        fillFreightTemplate(productList);

        if (Boolean.TRUE.equals(queryBo.getNeedH5Url())) {
            fillH5Url(productList);
        }
    }

    private void fillH5Url(List<ProductPageBo> productList) {
        if (CollUtil.isEmpty(productList)) {
            return;
        }

        String hrUrl = remoteSiteSettingService.getSetting(DEFAULT_H5_URL_KEY);
        productList.forEach(product -> {
            product.setH5Url(hrUrl + product.getProductId());
        });
    }

    /**
     * 补充运费模板信息
     *
     * @param productList 商品集合
     */
    private void fillFreightTemplate(List<ProductPageBo> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<Long> templateIds = productList.stream()
                .map(ProductPageBo::getFreightTemplateId)
                .filter(Objects::nonNull).filter(item -> item > 0)
                .collect(Collectors.toList());
        List<QueryFreightTemplateDto> templateList = remoteFreightAreaService.queryTplByTemplateIdList(templateIds);
        productList.forEach(product -> {
            if (product.getFreightTemplateId() != null) {
                boolean found = templateList.stream().filter(t -> t.getId().equals(product.getFreightTemplateId())).findFirst().isPresent();
                if (found) {
                    QueryFreightTemplateDto templateDto = templateList.stream().filter(t -> t.getId().equals(product.getFreightTemplateId())).findFirst().get();
                    product.setFreightTemplateName(templateDto.getName());
                    product.setValuationMethod(templateDto.getValuationMethod());
                }

            }
        });
    }

    /**
     * 填充价格范围
     *
     * @param productList 商品列表
     */
    private void fillPriceRange(List<ProductPageBo> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        productList.forEach(product -> {
            product.setSalePriceRange(productAssist.getSalePriceRange(product.getMinSalePrice(), product.getMaxSalePrice()));
        });
    }

    /**
     * 填充分类信息
     *
     * @param productBo 商品信息
     */
    public void fillCategoryInfo(ProductBo productBo) {
        if (productBo == null) {
            return;
        }

        CategoryBo category = categoryService.getCategoryById(productBo.getCategoryId());
        if (category == null) {
            return;
        }

        productBo.setCategoryName(category.getName());
        productBo.setFullCategoryName(category.getFullCategoryName());
        productBo.setFullCategoryIds(category.getFullIds());
    }

    /**
     * 补充品牌信息
     *
     * @param productList 商品集合
     */
    private void fillBrand(List<ProductPageBo> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        Map<Long, String> brandNameMap = brandRepository.getBrandNameMap(productList.stream().map(ProductPageBo::getBrandId).collect(Collectors.toList()));
        productList.forEach(product -> {
            if (product.getBrandId() != null) {
                product.setBrandName(brandNameMap.get(product.getBrandId()));
            }
        });
    }

    /**
     * 补充类目信息
     *
     * @param productList 商品集合
     */
    private void fillCategory(List<ProductPageBo> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<Long> categoryIds = productList.stream().map(ProductPageBo::getCategoryId).collect(Collectors.toList());
        Map<Long, CategoryBo> categoryMap = categoryService.getCategoryMap(categoryIds);
        productList.forEach(product -> {
            CategoryBo categoryBo = categoryMap.get(product.getCategoryId());
            if (categoryBo == null) {
                return;
            }

            product.setCategoryName(categoryBo.getName());
            product.setCategoryIds(categoryBo.getFullIds());
            product.setCategoryNames(categoryBo.getCategoryNameList());
            product.setFullCategoryName(categoryBo.getFullCategoryName());
        });

    }

    /**
     * 补充店铺分类信息
     *
     * @param productList 商品集合
     */
    private void fillShopCategory(List<ProductPageBo> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        Map<Long, String> shopCategoryNameMap = getShopCategoryNameMap(productList);
        productList.forEach(product -> {
            if (!CollectionUtils.isEmpty(product.getShopCategoryIds())) {
                product.setShopCategoryNames(getShopCategoryNames(product.getShopCategoryIds(), shopCategoryNameMap));
            }
        });
    }

    /**
     * 补充库存信息
     *
     * @param productList 商品集合
     */
    private void fillStock(List<ProductPageBo> productList, boolean fromAudit) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        Map<Long, Long> productStockMap;
        if (fromAudit) {
            productStockMap = getProductAuditStockMap(productList.stream().map(ProductPageBo::getProductId).collect(Collectors.toList()));
        } else {
            productStockMap = getProductStockMap(productList.stream().map(ProductPageBo::getProductId).collect(Collectors.toList()));
        }

        for (ProductPageBo product : productList) {
            if (product.getProductId() != null) {
                product.setStock(productStockMap.get(product.getProductId()));
            }
        }
    }

    /**
     * 填充推荐商品
     *
     * @param esProductList 推荐商品的集合
     */
    private void fillRelationProduct(List<ProductPageBo> esProductList) {
        if (CollectionUtils.isEmpty(esProductList)) {
            return;
        }

        List<Long> productList = esProductList.stream().map(ProductPageBo::getProductId).collect(Collectors.toList());
        Map<Long, List<Long>> relationMap = productRelationProductRepository.getRelationProductMap(productList);
        esProductList.forEach(esProduct -> {
            if (esProduct.getProductId() != null) {
                esProduct.setRelationProductIds(relationMap.getOrDefault(esProduct.getProductId(), Collections.EMPTY_LIST));
            }
        });
    }

    /**
     * 填充店铺信息
     *
     * @param productList 商品列表
     */
    private void fillShop(List<ProductPageBo> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<Long> shopIds = productList.stream().map(ProductPageBo::getShopId).collect(Collectors.toList());
        Map<Long, String> shopNameMap = remoteShopService.getShopNameMap(shopIds);
        productList.forEach(product -> {
            product.setShopName(shopNameMap.get(product.getShopId()));
        });
    }

    /**
     * 获取店铺分类名称map
     *
     * @param esProductList es商品列表
     * @return 店铺名称map
     */
    private Map<Long, String> getShopCategoryNameMap(List<ProductPageBo> esProductList) {
        if (CollectionUtils.isEmpty(esProductList)) {
            return Collections.EMPTY_MAP;
        }
        List<Long> shopCategoryIdList = new ArrayList<>();
        esProductList.forEach(esProduct -> {
            if (!CollectionUtils.isEmpty(esProduct.getShopCategoryIds())) {
                shopCategoryIdList.addAll(esProduct.getShopCategoryIds());
            }
        });

        Map<Long, List<String>> result = shopCategoryRepository.getFullShopCategoryNameMap(shopCategoryIdList);
        return result.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.join(">", entry.getValue())));
    }

    /**
     * 获取店铺分类名称, 逗号隔开
     *
     * @param shopCategoryIds     店铺分类id列表
     * @param shopCategoryNameMap 店铺分类名称map
     * @return 店铺分类名称
     */
    private String getShopCategoryNames(List<Long> shopCategoryIds, Map<Long, String> shopCategoryNameMap) {
        if (CollectionUtils.isEmpty(shopCategoryIds)) {
            return "";
        }
        return shopCategoryIds.stream()
                .map(shopCategoryId -> shopCategoryNameMap.get(shopCategoryId))
                .filter(item -> item != null)
                .collect(Collectors.joining(","));
    }

    /**
     * 获取商品库存 map
     *
     * @param productIdList 商品id列表
     * @return 商品库存map
     */
    private Map<Long, Long> getProductStockMap(List<Long> productIdList) {
        List<SkuStock> skuStocks = skuStockRepository.listByProductIds(productIdList);
        Map<Long, List<SkuStock>> skuStockMap = skuStocks.stream().collect(Collectors.groupingBy(SkuStock::getProductId));

        Map<Long, Long> stockMap = new HashMap<>();
        skuStockMap.forEach((productId, skuStockList) -> {
            long stock = skuStockList.stream().mapToLong(SkuStock::getStock).sum();
            stockMap.put(productId, stock);
        });
        return stockMap;
    }

    /**
     * 获取商品审核库存 map
     *
     * @param productIdList 商品id列表
     * @return 商品库存map
     */
    private Map<Long, Long> getProductAuditStockMap(List<Long> productIdList) {
        List<SkuStockAudit> skuStockAudits = skuStockAuditRepository.listByProductIds(productIdList);
        Map<Long, List<SkuStockAudit>> skuStockMap = skuStockAudits.stream().collect(Collectors.groupingBy(SkuStockAudit::getProductId));

        Map<Long, Long> stockMap = new HashMap<>();
        skuStockMap.forEach((productId, skuStockList) -> {
            long stock = skuStockList.stream().mapToLong(SkuStockAudit::getStock).sum();
            stockMap.put(productId, stock);
        });
        return stockMap;
    }

    public void coverProductData(List<EsProductModel> productList) {
        if (CollUtil.isEmpty(productList)) {
            return;
        }

        List<Long> productIdList = productList.stream().map(EsProductModel::getProductId).collect(Collectors.toList());
        Map<Long, Product> productMap = productAssist.getProductMap(productIdList);

        List<ProductShopCategory> productShopCategoryList = productShopCategoryRepository.listByProductIds(productIdList);
        Map<Long, List<Long>> shopCategoryIdsMap = productShopCategoryList.stream()
                .collect(Collectors.groupingBy(ProductShopCategory::getProductId,
                        Collectors.mapping(ProductShopCategory::getShopCategoryId, Collectors.toList())));

        productList.forEach(esProduct -> {
            Product dbProduct = productMap.get(esProduct.getProductId());
            if (dbProduct != null) {
                BeanUtil.copyProperties(dbProduct, esProduct);
            }

            esProduct.setShopCategoryIds(shopCategoryIdsMap.getOrDefault(esProduct.getProductId(), Collections.emptyList()));
        });

    }

}
