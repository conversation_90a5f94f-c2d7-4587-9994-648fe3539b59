package com.sankuai.shangou.seashop.product.core.service.excel.read.dto;

import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/01/11 12:00
 */
@Getter
@Setter
public class ProductUpdateImportDto extends RowReadResult {

    @ExcelProperty(value = "*商品ID")
    // @ExcelField(required = true, regex = ParameterConstant.PRODUCT_ID_CHECK_REGEX, regexMsg = "商品ID格式错误")
    private String productId;

    @ExcelIgnore
    private Long productIdLong;

    @ExcelProperty(value = "商品货号")
    private String productCode;

    @ExcelProperty(value = "商品名称")
    private String productName;

    @ExcelProperty(value = "平台一级类目")
    private String firstCategoryName;

    @ExcelIgnore
    private Long firstCategoryId;

    @ExcelProperty(value = "平台二级类目")
    private String secondCategoryName;

    @ExcelIgnore
    private Long secondCategoryId;

    @ExcelProperty(value = "平台三级类目")
    private String thirdCategoryName;

    @ExcelIgnore
    private Long thirdCategoryId;

    @ExcelIgnore
    private String categoryPath;

    @ExcelProperty(value = "广告词")
    private String shortDescription;

    @ExcelProperty(value = "品牌")
    private String brandName;

    @ExcelIgnore
    private Long brandId;

    @ExcelProperty(value = "市场价")
    private String marketPrice;

    @ExcelProperty(value = "店铺分类")
    private String shopCategoryName;

    @ExcelIgnore
    private Long shopCategoryId;

    @ExcelProperty(value = "限购数")
    // @ExcelField(regexEnum = FieldRegexEnum.NON_NEGATIVE_INTEGER_REGEX)
    private String maxBuyCount;

    @ExcelProperty(value = "倍数起购量")
    // @ExcelField(regexEnum = FieldRegexEnum.POSITIVE_INTEGER_REGEX)
    private String multipleCount;

    @ExcelProperty(value = "商品主图")
    private String imagePath;

    @ExcelProperty(value = "运费模板")
    private String freightTemplateName;

    @ExcelIgnore
    private Long freightTemplateId;

    @ExcelProperty(value = "商品详情")
    private String description;

    @ExcelProperty(value = "*规格ID")
    private String skuAutoId;

    @ExcelProperty(value = "计量单位")
    private String measureUnit;

    @ExcelProperty(value = "商城价")
    // @ExcelField(regexEnum = FieldRegexEnum.AMOUNT_REGEX)
    private String salePrice;

    @ExcelProperty(value = "库存")
    // @ExcelField(regexEnum = FieldRegexEnum.NON_NEGATIVE_INTEGER_REGEX)
    private String stock;

    @ExcelProperty(value = "货号")
    private String skuCode;

    @ExcelProperty(value = "警戒库存")
    // @ExcelField(regexEnum = FieldRegexEnum.NON_NEGATIVE_INTEGER_REGEX)
    private String safeStock;

    @ExcelIgnore
    private Integer source;

    @ExcelIgnore
    private List<ProductSkuBo> skuList;

    @ExcelIgnore
    private Long shopId;

    @ExcelIgnore
    private StringBuilder errBuilder = new StringBuilder();

    @ExcelIgnore
    private List<String> imageList;

    @ExcelIgnore
    private Long skuAutoIdLong;

}
