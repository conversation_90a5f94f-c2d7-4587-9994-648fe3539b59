package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopFlashSaleConfigResp;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleDetailBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductSkuBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 限时购活动处理
 *
 * <AUTHOR>
 * @date 2023/12/25 9:18
 */
@Slf4j
@Component
public class FlashSaleHandler extends AbsActivityHandler {

    @Resource
    private PromotionRemoteService promotionRemoteService;

    @Override
    public void handle(DealActivityContext context) {
        ActivityContext activityContext = context.getActivityContext();
        RemoteFlashSaleBo flashSale = activityContext.getFlashSale();
        if (flashSale == null || CollectionUtils.isEmpty(flashSale.getDetailList())) {
            return;
        }

        // 如果启动了限时购
        ShopFlashSaleConfigResp shopConfig = promotionRemoteService.getShopConfig(context.getShopId());
        // 获取预热时间
        Long preheat = shopConfig == null || shopConfig.getPreheat() == null ? 0l : shopConfig.getPreheat();
        // 还没到预热时间, 则无需返回限时购信息
        if (flashSale.getStartCountDown() > preheat * 60 * 60 * 1000) {
            log.info("限时购未到达预热时间，无需返回, productId: {}, flashSaleId: {}", context.getProductId(), flashSale.getId());
            return;
        }

        List<ProductSkuBo> skuList = context.getSkuList();
        Map<String, RemoteFlashSaleDetailBo> flashSaleMap = flashSale.getDetailList()
                .stream().collect(Collectors.toMap(RemoteFlashSaleDetailBo::getSkuId, Function.identity(), (k1, k2) -> k2));
        BigDecimal estimatePrice = context.getEstimatePrice();
        for (ProductSkuBo sku : skuList) {
            RemoteFlashSaleDetailBo flashSaleDetail = flashSaleMap.get(sku.getSkuId());
            if (flashSaleDetail == null) {
                continue;
            }

            sku.setFlashSalePrice(flashSaleDetail.getPrice());
            sku.setFlashSaleStock(flashSaleDetail.getTotalCount());
            sku.setFlashSaleLimit(flashSaleDetail.getLimitCount());
            estimatePrice = estimatePrice.min(flashSaleDetail.getPrice());
        }

        ProductActivityInfoBo activityInfo = context.getActivityInfo();
        activityInfo.setHasFlashSale(true);
        activityInfo.setFlashSaleRule(flashSale);
        context.setEstimatePrice(estimatePrice);
    }

    @Override
    public int order() {
        return 1;
    }
}
