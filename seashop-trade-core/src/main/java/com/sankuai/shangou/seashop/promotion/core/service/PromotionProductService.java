package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.promotion.thrift.core.request.CollocationFlashSaleProductReq;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/3/7/007
 * @description:
 */
public interface PromotionProductService {

    /**
     * 获取参与了组合购和限时购的所有商品ID
     *
     * @param shopId
     * @return
     */
    List<Long> collocationFlashSaleProductIdList(Long shopId);

    /**
     * 获取参与了组合购和限时购的所有商品ID(可以包括组合购的附商品)
     *
     * @param req
     * @return
     */
    List<Long> collocationFlashSaleProductIdIncludeSub(CollocationFlashSaleProductReq req);
}
