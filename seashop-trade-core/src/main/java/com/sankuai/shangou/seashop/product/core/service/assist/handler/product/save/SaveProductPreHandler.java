package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 保存商品前置处理器
 *
 * <AUTHOR>
 * @date 2023/11/15 18:28
 */
@Component
@Slf4j
public class SaveProductPreHandler extends AbsProductHandler {

    @Resource
    private ProductService productService;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】前置处理器, 初始化上下文【start】, context:{}", context);

        ProductBo saveProductBo = context.getSaveProductBo();
        boolean draftFlag = context.isDraftFlag();
        context.setNeedAudit(Boolean.FALSE);

        if (context.isEditFlag()) {
            ProductBo oldProductBo = productService.queryProductDetail(saveProductBo.getProductId(), context.getShopId());
            context.setOldProductBo(oldProductBo);
            saveProductBo.setSource(oldProductBo.getSource());
            // 如果是部分更新, 如果原来是草稿状态还是只能为草稿状态
            if (context.isPartSave()) {
                draftFlag = ProductEnum.SaleStatusEnum.DRAFT.getCode().equals(oldProductBo.getSaleStatus());
            }

            // 如果是由草稿状态调整为非草稿状态 肯定需要审核
            if (ProductEnum.SaleStatusEnum.DRAFT.getCode().equals(oldProductBo.getSaleStatus()) && !draftFlag) {
                context.setNeedAudit(Boolean.TRUE);
            }
        }
        else {
            context.setNeedAudit(!draftFlag);
            saveProductBo.setProductId(context.getProductId());
            // 新增是将商品标记为新商品 并且设置来源
            saveProductBo.setWhetherNewProduct(Boolean.TRUE);
            saveProductBo.setSource(context.getChangeSource().getCode());
        }

        context.setDraftFlag(draftFlag);
        context.setAuditProductBo(JsonUtil.copy(context.getSaveProductBo(), ProductBo.class));
        log.info("【保存商品】前置处理器, 初始化上下文【end】, context:{}", context);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SAVE_PRODUCT_PRE;
    }

}
