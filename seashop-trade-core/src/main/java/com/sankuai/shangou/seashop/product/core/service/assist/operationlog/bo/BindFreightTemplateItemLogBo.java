package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 16:18
 */
@Getter
@Setter
public class BindFreightTemplateItemLogBo {

    @PrimaryField
    @ExaminField(description = "商品id")
    private Long productId;

    @ExaminField(description = "运费模板id")
    private Long freightTemplateId;

}
