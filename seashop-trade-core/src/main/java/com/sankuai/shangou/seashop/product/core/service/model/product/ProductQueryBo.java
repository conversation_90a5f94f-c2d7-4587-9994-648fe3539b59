package com.sankuai.shangou.seashop.product.core.service.model.product;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.product.common.es.model.product.StatusDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/16 12:43
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductQueryBo {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * sku自增id
     */
    private Long skuAutoId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 店铺分类id
     */
    private Long shopCategoryId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 商品状态 1-销售中 2-仓库中 3-违规下架 4-草稿箱
     */
    private ProductStatusEnum status;

    /**
     * 商品状态列表
     */
    private List<ProductStatusEnum> inStatus;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 审核状态
     */
    private ProductEnum.AuditStatusEnum auditStatus;

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    private Integer saleStatusCode;

    /**
     * 审核状态 1-审核中 2-审核通过 3-审核不通过 4-违规下架
     */
    private Integer auditStatusCode;

    /**
     * 销售状态集合
     */
    private List<Integer> saleStatusCodeList;

    /**
     * 审核状态集合
     */
    private List<Integer> auditStatusCodeList;


    /**
     * 排序字段
     */
    private List<FieldSortReq> sortList;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 品牌id列表
     */
    private List<Long> brandIds;

    /**
     * 类目path
     */
    private String categoryPath;

    /**
     * 运费模板id
     */
    private Long freightTemplateId;

    /**
     * 是否低于安全库存
     */
    private Boolean whetherBelowSafeStock;

    /**
     * 只查询的字段
     */
    private String[] includeFields;

    /**
     * 商品id列表
     */
    private List<Long> productIds;

    /**
     * 类目id列表
     */
    private List<Long> categoryIds;

    /**
     * 创建开始时间筛选
     */
    private Date createTimeStart;

    /**
     * 创建结束时间筛选
     */
    private Date createTimeEnd;

    /**
     * 更新开始时间筛选
     */
    private Date updateTimeStart;

    /**
     * 更新结束时间筛选
     */
    private Date updateTimeEnd;

    /**
     * 销售状态筛选
     */
    private List<StatusDto> statusList;

    /**
     * 来源 1-商城 2-牵牛花 3-易久批
     */
    private Integer source;

    /**
     * sku编码列表
     */
    private List<String> skuCodes;

    /**
     * sku自增id列表
     */
    private List<Long> skuAutoIds;

    /**
     * 是否开启阶梯价
     */
    private Boolean whetherOpenLadder;

    /**
     * 排除商品id列表
     */
    private List<Long> excludeProductIds;

    /**
     * 店铺ID集合
     */
    private List<Long> shopIds;

    /**
     * 是否有库存
     */
    private Boolean hasStock;

    /**
     * 店铺分类id的集合
     */
    private List<Long> shopCategoryIds;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 规格名称id的集合
     */
    private List<Long> specNameIds;

    /**
     * 是否需要h5链接
     */
    private Boolean needH5Url;

    /**
     * 父类目id
     */
    private Long parentCategoryId;

    /**
     * OE号
     */
    private String oeCode;

    /**
     * 品牌号
     */
    private String brandCode;
    /**
     * 搜索关键字
     */
    private String searchKey;
}
