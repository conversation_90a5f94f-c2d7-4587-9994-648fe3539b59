package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.common.remote.base.RemoteSiteSettingService;
import com.sankuai.shangou.seashop.product.common.remote.base.model.RemoteProductSettingBo;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.common.remote.user.model.RemoteShopBo;
import com.sankuai.shangou.seashop.product.core.service.BrandService;
import com.sankuai.shangou.seashop.product.core.service.ProductAuditService;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAuditAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.ShopCategoryAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerProcessor;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendProductChangeEvent;
import com.sankuai.shangou.seashop.product.core.service.hepler.StringHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditCompareBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import cn.hutool.core.util.StrUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:06
 */
@Service
@Slf4j
public class ProductAuditServiceImpl implements ProductAuditService {

    @Resource
    private ProductAuditRepository productAuditRepository;
    @Resource
    private ProductDescriptionAuditRepository productDescriptionAuditRepository;
    @Resource
    private ProductAuditAssist productAuditAssist;
    
    @Resource
    private ProductHandlerProcessor productHandlerProcessor;
    @Resource
    private TransactionEventPublisher transactionEventPublisher;
    @Resource
    private RemoteSiteSettingService remoteSiteSettingService;
    @Resource
    private ProductAssist productAssist;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private BrandService brandService;
    @Resource
    private ShopCategoryAssist shopCategoryAssist;

    @Override
    public void batchAuditProduct(ProductAuditBo auditBo) {
        List<Long> productList = auditBo.getProductIdList();
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        // 已经在入参处校验不能超过50个
        String[] lockArr = productList.stream().map(productId -> LockConstant.PRODUCT_AUDIT_LOCK + productId).toArray(String[]::new);
        LockHelper.lock(lockArr, () -> {
            List<ProductAudit> productAuditList = productAuditRepository.listByProductIds(productList);
            AssertUtil.throwIfTrue(CollectionUtils.isEmpty(productAuditList), "待审核记录不存在, 请刷新后重试");

            // 只能审核已经审核并且风控审核通过的商品
            long count = productAuditList.stream().filter(item -> !item.getAuditStatus().equals(ProductEnum.AuditStatusEnum.WAIT_AUDIT.getCode())).count();
            AssertUtil.throwIfTrue(count > 0, "存在已审核的记录, 请刷新后重试");

            TransactionHelper.doInTransaction(() -> {
                // 审核通过
                if (auditBo.isPass()) {
                    passAudit(productAuditList, auditBo.getAuditReason());
                    return;
                }

                // 审核拒绝
                rejectAudit(productAuditList, auditBo.getAuditReason());
            });
        });
    }

    @Override
    public BasePageResp<ProductPageBo> pageProductAudit(BasePageParam pageParam, ProductQueryBo queryBo) {
        if (queryBo.getAuditStatus() != null) {
            queryBo.setAuditStatusCode(queryBo.getAuditStatus().getCode());
        }
        if (CollectionUtils.isNotEmpty(queryBo.getCategoryIds())) {
            queryBo.setCategoryPath(StrUtil.join(CommonConstant.CATEGORY_PATH_SPLIT_NO_ESCAPE, queryBo.getCategoryIds()));
        }
        // 处理传了店铺名称的情况
        if(StringUtils.isNotEmpty(queryBo.getShopName())){
            List<Long> shopIds = remoteShopService.getShopIdsByName(StringHelper.escapeSpecialChar(queryBo.getShopName()));
            if(CollectionUtils.isEmpty(shopIds)){
                return PageResultHelper.defaultEmpty(pageParam);
            }
            queryBo.setShopIds(shopIds);
        }
        if (queryBo.getShopCategoryId() != null) {
            queryBo.setShopCategoryIds(shopCategoryAssist.getChildShopCategoryIdsAndSelf(queryBo.getShopCategoryId()));
            queryBo.setShopCategoryId(null);
        }
        return productAuditAssist.pageEsProduct(pageParam, queryBo);
    }

    /**
     * 尝试自动通过平台审核(当平台关闭了审核, 风控成功后自动通过审核)
     *
     * @param productAudit 商品审核
     */
    private void tryAutoAudit(@NonNull ProductAudit productAudit) {
        RemoteShopBo shop = remoteShopService.getByShopId(productAudit.getShopId());
        if (shop != null && shop.getWhetherSelf()) {
            log.info("店铺是自营, 自动通过平台审核, productId:{}", productAudit.getProductId());
            passAudit(Collections.singletonList(productAudit), CommonConstant.AUTO_PASS_AUDIT_REASON);
            return;
        }

        RemoteProductSettingBo setting = remoteSiteSettingService.getProductSetting();
        if (setting == null || setting.isProductAuditOnOff()) {
            log.info("平台开启了审核, 等待平台审核, productId:{}", productAudit.getProductId());
            return;
        }

        passAudit(Collections.singletonList(productAudit), CommonConstant.AUTO_PASS_AUDIT_REASON);
        log.info("平台关闭了审核, 风控审核通过, 自动通过平台审核, productId:{}", productAudit.getProductId());
    }

    @Override
    public ProductAuditCompareBo queryProductAuditDetail(ProductAuditQueryBo queryBo) {
        // 查询商品审核详情
        ProductContext context = ProductContext.builder().shopId(queryBo.getShopId()).productId(queryBo.getProductId()).build();
        productHandlerProcessor.handle(ProductHandlerType.QUERY_PRODUCT_AUDIT, context);
        ProductBo productAudit = context.getOldProductBo();

        ProductAuditCompareBo compareBo = new ProductAuditCompareBo();
        compareBo.setProductAudit(productAudit);
        compareBo.setShowCompare(Boolean.FALSE);

        if (ObjectUtils.notEqual(productAudit.getWhetherNewProduct(), Boolean.TRUE) && queryBo.isNeedOriginProduct()) {
            productHandlerProcessor.handle(ProductHandlerType.QUERY_PRODUCT_DETAIL, context);
            compareBo.setOriginProduct(context.getOldProductBo());
            compareBo.setShowCompare(Boolean.TRUE);
        }
        return compareBo;
    }

    @Override
    public void tryAutoAudit(Long productId) {
        ProductAudit productAudit = productAuditRepository.getByProductId(productId);
        if (productAudit == null) {
            log.warn("未找到待审核记录: productId:{}", productId);
        }

        tryAutoAudit(productAudit);
    }

    /**
     * 通过审核
     *
     * @param productAuditList 待审核的商品
     * @param auditReason      备注
     */
    private void passAudit(List<ProductAudit> productAuditList, String auditReason) {
        dealAuditResult(productAuditList, true, auditReason);

        productAuditList.forEach(productAudit -> {
            productAuditAssist.passAudit(productAudit.getProductId());
        });
    }

    /**
     * 拒绝审核
     *
     * @param productAuditList 待审核的商品
     * @param auditReason      备注
     */
    private void rejectAudit(List<ProductAudit> productAuditList, String auditReason) {
        dealAuditResult(productAuditList, false, auditReason);

        // 发送审核拒绝事件
        SendProductChangeEvent event = SendProductChangeEvent.build(
                JsonUtil.copyList(productAuditList, Product.class), ProductSourceEnum.MALL, ProductChangeType.AUDIT_REJECT);
        event.getEventBody().forEach(item -> item.setAuditReason(auditReason));
        transactionEventPublisher.publish(event);
    }

    /**
     * 处理审核结果（修改审核状态 记录审核原因）
     *
     * @param productAuditList 待审核的商品
     * @param pass             是否通过
     * @param auditReason      备注
     */
    private void dealAuditResult(List<ProductAudit> productAuditList, boolean pass, String auditReason) {
        List<ProductAudit> updProductAuditList = new ArrayList<>();
        List<ProductDescriptionAudit> updProductDescriptionAuditList = new ArrayList<>();
        Integer auditStatus = pass ? ProductEnum.AuditStatusEnum.ON_SALE.getCode() : ProductEnum.AuditStatusEnum.NOT_PASS.getCode();

        List<Long> productIds = productAuditList.stream().map(ProductAudit::getProductId).collect(Collectors.toList());
        Map<Long, Product> productMap = productAssist.getProductMap(productIds);

        productAuditList.forEach(item -> {
            Product product = productMap.get(item.getProductId());
            AssertUtil.throwIfTrue(product == null || product.getWhetherDelete(), String.format("[%s]商品不存在", item.getProductId()));

            // 更新主表的待审核权限
            if (product.getAuditStatus().equals(ProductEnum.AuditStatusEnum.WAIT_AUDIT.getCode())) {
                Product updProduct = new Product();
                updProduct.setId(product.getId());
                updProduct.setAuditStatus(auditStatus);
                updProduct.setVirtualSaleCounts(product.getVirtualSaleCounts());
                productRepository.updateById(updProduct);
            }

            ProductAudit updProductAudit = new ProductAudit();
            updProductAudit.setId(item.getId());
            updProductAudit.setAuditStatus(auditStatus);
            updProductAuditList.add(updProductAudit);

            ProductDescriptionAudit updProductDescriptionAudit = new ProductDescriptionAudit();
            updProductDescriptionAudit.setProductId(item.getProductId());
            updProductDescriptionAudit.setAuditReason(auditReason);
            updProductDescriptionAuditList.add(updProductDescriptionAudit);
        });

        productAuditRepository.updateBatchById(updProductAuditList);
        productDescriptionAuditRepository.updateBatchByProductId(updProductDescriptionAuditList);
    }
}
