package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;

/**
 * 抽象活动查询处理器
 *
 * <AUTHOR>
 * @date 2023/12/23 9:32
 */
public abstract class AbsActivityQueryHandler {

    /**
     * 查询具体活动的方法
     *
     * @param context 活动上下文对象
     */
    protected abstract void query(ActivityContext context);

    /**
     * 下一个活动查询处理器
     * 该方法来确定活动的优先级
     */
    protected abstract Class<? extends AbsActivityQueryHandler> nextHandler();

    /**
     * 是否支持当前活动查询
     * 该方法用来确定活动的互斥关系
     *
     * @param context 活动上下文对象
     * @return true 支持，false 不支持
     */
    protected abstract boolean support(ActivityContext context);

}
