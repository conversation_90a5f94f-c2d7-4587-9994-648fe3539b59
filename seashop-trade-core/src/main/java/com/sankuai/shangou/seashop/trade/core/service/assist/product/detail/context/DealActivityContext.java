package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context;

import java.math.BigDecimal;
import java.util.List;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductSkuBo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/25 9:15
 */
@Setter
@Getter
public class DealActivityContext extends ProductContext {

    /**
     * 商品sku信息
     */
    private List<ProductSkuBo> skuList;

    /**
     * 是否开启阶梯价
     */
    private boolean whetherOpenLadder;

    /**
     * 活动信息(内部计算使用, 不返回给前端)
     */
    private ActivityContext activityContext;

    /**
     * 活动信息(返回给前端)
     */
    private ProductActivityInfoBo activityInfo;

    /**
     * 预估价
     */
    private BigDecimal estimatePrice;

    /**
     * 使用了优惠券后的预估到手价(优惠券和满减活动是互斥关系)
     */
    private BigDecimal couponEstimatePrice;

    /**
     * 满减活动预估到手价
     */
    private BigDecimal fullReductionEstimatePrice;

    /**
     * 价格区间
     */
    private String salePriceRange;

    /**
     * 最低售价
     */
    private BigDecimal minSalePrice;

    /**
     * 最高售价
     */
    private BigDecimal maxSalePrice;
}
