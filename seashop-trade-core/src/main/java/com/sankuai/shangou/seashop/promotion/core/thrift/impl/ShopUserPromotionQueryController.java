package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.model.bo.QueryShopUserPromotionBo;
import com.sankuai.shangou.seashop.promotion.core.service.PromotionService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryProductPromotionReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryShopUserPromotionReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductPromotionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopReductionOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopUserPromotionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ShopUserPromotionQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/16/016
 * @description:
 */
@RestController
@RequestMapping("/shopUserPromotion")
public class ShopUserPromotionQueryController implements ShopUserPromotionQueryFeign {

    @Resource
    private PromotionService promotionService;

    @PostMapping(value = "/queryShopValidPromotionWithUser", consumes = "application/json")
    @Override
    public ResultDto<ShopUserPromotionResp> queryShopValidPromotionWithUser(@RequestBody QueryShopUserPromotionReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopValidPromotionWithUser", request, req -> {
            req.checkParameter();
            QueryShopUserPromotionBo queryBo = JsonUtil.copy(req, QueryShopUserPromotionBo.class);
            return promotionService.queryPromotionData(queryBo);
        });
    }

    @PostMapping(value = "/queryShopPromotionByOrder", consumes = "application/json")
    @Override
    public ResultDto<ShopReductionOrderListResp> queryShopPromotionByOrder(@RequestBody PromotionRecordOrderQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopPromotionByOrder", request, req -> {
            req.checkParameter();
            return promotionService.queryShopPromotionByOrder(req);
        });
    }

    @PostMapping(value = "/queryProductPromotion", consumes = "application/json")
    @Override
    public ResultDto<ProductPromotionResp> queryProductPromotion(@RequestBody QueryProductPromotionReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductPromotion", request, req -> {
            req.checkParameter();

            return promotionService.queryProductPromotion(req);
        });
    }
}
