package com.sankuai.shangou.seashop.product.core.service.hepler;

import java.net.URI;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.hishop.starter.storage.client.StorageClient;

/**
 * <AUTHOR>
 * @date 2024/03/08 16:35
 */
@Component
public class S3Helper {

    @Resource
    private StorageClient storageClient;


    public String getFullPath(String path) {
        if (StringUtils.isNotEmpty(URI.create(path).getHost())) {
            return path;
        }

        return storageClient.formatUrl(path);
    }
}
