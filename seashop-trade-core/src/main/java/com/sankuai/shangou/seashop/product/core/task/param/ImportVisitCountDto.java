package com.sankuai.shangou.seashop.product.core.task.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/18 10:27
 */
@Getter
@Setter
public class ImportVisitCountDto extends RowReadResult {

    @ExcelProperty("商品id")
    private Long productId;

    @ExcelProperty("访问量")
    private Integer visitCounts;

}
