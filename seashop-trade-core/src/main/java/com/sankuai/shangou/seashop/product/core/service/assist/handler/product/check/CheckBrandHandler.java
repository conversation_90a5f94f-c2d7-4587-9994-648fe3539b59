package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.BrandAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;

import lombok.extern.slf4j.Slf4j;

/**
 * 品牌检查处理器
 *
 * <AUTHOR>
 * @date 2023/11/15 19:41
 */
@Component
@Slf4j
public class CheckBrandHandler extends AbsProductHandler {

    @Resource
    private BrandAssist brandAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】校验品牌属性【start】, context:{}", context);

        ProductBo productBo = context.getSaveProductBo();
        Brand brand = brandAssist.checkBrandAuth(productBo.getBrandId(), context.getCurrentShop());
        productBo.setBrandName(brand.getName());

        log.info("【保存商品】校验品牌属性【end】, context:{}", context);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getBrandId() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_BRAND;
    }
}
