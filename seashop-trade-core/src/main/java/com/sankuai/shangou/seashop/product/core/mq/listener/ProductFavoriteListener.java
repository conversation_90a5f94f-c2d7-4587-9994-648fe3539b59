package com.sankuai.shangou.seashop.product.core.mq.listener;

import java.util.List;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.core.mq.model.ProductFavoriteMessage;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/10/30 11:22
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_FAVORITE_PRODUCT + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.GROUP_FAVORITE_PRODUCT + "_${spring.profiles.active}")
public class ProductFavoriteListener implements RocketMQListener<String> {

    @Resource
    private ProductEsBuildService productEsBuildService;

    @Override
    public void onMessage(String message) {
        try {
            log.info("[商品收藏] 接收到商品收藏事件, body: {}", JsonUtil.toJsonString(message));

            ProductFavoriteMessage body = JsonUtil.parseObject(message, ProductFavoriteMessage.class);
            List<Long> productIds = body.getProductIds();
            if (CollUtil.isEmpty(productIds)) {
                log.warn("[商品收藏] 收到无效的消息, body: {}", JsonUtil.toJsonString(message));
                return;
            }

            productIds.forEach(productId -> {
                productEsBuildService.buildProductFavoriteEs(productId);
            });
            log.info("[商品收藏] 消费成功, body: {}", JsonUtil.toJsonString(message));
        } catch (Exception e) {
            log.error("[商品收藏] 消费失败, body: {}", JsonUtil.toJsonString(message), e);
            throw e;
        }
    }
}
