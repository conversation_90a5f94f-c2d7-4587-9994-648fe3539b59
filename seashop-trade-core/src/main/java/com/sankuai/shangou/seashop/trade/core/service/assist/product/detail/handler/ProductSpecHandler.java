package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductSpecBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductSpecGroupBo;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品规格处理器
 *
 * <AUTHOR>
 * @date 2023/12/25 15:03
 */
@Component
@Slf4j
public class ProductSpecHandler extends AbsProductQueryHandler {

    /**
     * sku拼接符
     */
    private static final String SKU_SPLIT = "_";
    /**
     * sku拆分后的数组长度
     */
    private static final int SKU_ARR_LENGTH = 4;
    /**
     * 最小规格
     */
    private Integer MIN_SPEC = 1;
    /**
     * 最大规格
     */
    private Integer MAX_SPEC = 3;

    @Override
    public void handle(ProductBaseContext context) {
        log.info("商品规格处理: context: {}", context);

        // 只处理多规格
        ProductBaseInfoBo product = context.getProduct();
        if (!product.isHasSku() || CollectionUtils.isEmpty(product.getSkuList())) {
            product.setSpecGroupList(Collections.emptyList());
            return;
        }

        List<String> specAliasArr = getSpecAliasArr(product);
        List<ProductSpecGroupBo> specGroupList = new ArrayList<>();
        List<ProductSkuBo> skuList = product.getSkuList();
        for (int i = MIN_SPEC; i <= MAX_SPEC; i++) {
            ProductSpecGroupBo specGroup = specGroupBuild(i, specAliasArr.get(i), skuList);
            if (specGroup != null && CollectionUtils.isNotEmpty(specGroup.getSpecValueList())) {
                specGroupList.add(specGroup);
            }
        }

        // 只有第一层规格保留图片
        if (CollectionUtils.isNotEmpty(specGroupList) && specGroupList.size() > 1) {
            for (int i = 1; i < specGroupList.size(); i++) {
                clearShowPic(specGroupList.get(i).getSpecValueList());
            }
        }
        product.setSpecGroupList(specGroupList);
    }

    private void clearShowPic(List<ProductSpecBo> specList) {
        if (CollectionUtils.isEmpty(specList)) {
            return;
        }
        specList.forEach(spec -> spec.setShowPic(StrUtil.EMPTY));
    }

    private ProductSpecGroupBo specGroupBuild(Integer spec, String specAlias, List<ProductSkuBo> skuList) {
        if (StringUtils.isEmpty(specAlias)) {
            return null;
        }

        ProductSpecGroupBo specGroup = new ProductSpecGroupBo();
        specGroup.setSpec(spec);
        specGroup.setSpecAlias(specAlias);
        List<ProductSpecBo> specList = new ArrayList<>();
        Set<Long> existSpecIds = new HashSet<>();
        for (ProductSkuBo sku : skuList) {
            // 已经添加过的规格不再添加
            ProductSpecBo specBo = specBuild(spec, sku);
            if (!existSpecIds.add(specBo.getSpecValueId())) {
                continue;
            }
            if (specBo != null && specBo.getSpecValueId() > 0) {
                specList.add(specBo);
            }
        }
        specGroup.setSpecValueList(specList);
        return specGroup;
    }

    private ProductSpecBo specBuild(Integer spec, ProductSkuBo sku) {
        List<Long> specIdArr = getSpecValueIdArr(sku.getSkuId());
        List<String> specValueArr = getSpecValueArr(sku);

        // 前面的方法已经保证了id和value 数组的长度为4, 所以不会出现越界
        ProductSpecBo specBo = new ProductSpecBo();
        specBo.setSpecValueId(specIdArr.get(spec));
        specBo.setValue(specValueArr.get(spec));
        // 只要商品库存和限时购库存有一个大于0, 就可以选择
        specBo.setSelectAble(sku.getStock() > 0 || sku.getFlashSaleStock() > 0);
        specBo.setShowPic(sku.getShowPic());
        return specBo;
    }

    /**
     * 获取规格别名数组
     *
     * @param product 商品
     * @return 规格别名数组
     */
    private List<String> getSpecAliasArr(ProductBaseInfoBo product) {
        List<String> specAliasArr = new ArrayList<>();
        specAliasArr.add(StrUtil.EMPTY);
        specAliasArr.add(product.getSpec1Alias());
        specAliasArr.add(product.getSpec2Alias());
        specAliasArr.add(product.getSpec3Alias());
        return specAliasArr;
    }

    /**
     * 获取规格id数组
     *
     * @param skuId
     * @return 规格id数组
     */
    private List<Long> getSpecValueIdArr(String skuId) {
        if (StringUtils.isEmpty(skuId)) {
            return null;
        }

        List<String> skuIdArr = Arrays.asList(skuId.split(SKU_SPLIT));
        if (skuIdArr.size() != SKU_ARR_LENGTH) {
            throw new SystemException("规格信息异常");
        }

        return skuIdArr.stream().map(Long::parseLong).collect(Collectors.toList());
    }

    /**
     * 获取规格名称数组
     *
     * @param sku sku
     * @return 规格名称数组
     */
    private List<String> getSpecValueArr(ProductSkuBo sku) {
        List<String> specNameArr = new ArrayList<>();
        specNameArr.add(StrUtil.EMPTY);
        specNameArr.add(sku.getSpec1Value());
        specNameArr.add(sku.getSpec2Value());
        specNameArr.add(sku.getSpec3Value());
        return specNameArr;
    }

    @Override
    public int order() {
        return 100;
    }
}
