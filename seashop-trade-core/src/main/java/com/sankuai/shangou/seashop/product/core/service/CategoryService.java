package com.sankuai.shangou.seashop.product.core.service;

import java.util.List;
import java.util.Map;

import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryCacheBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.SaveCategoryBo;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryByIdsReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:23
 */
public interface CategoryService {

    /**
     * 新增/编辑类目
     *
     * @param categoryBo 类目参数
     */
    void saveCategory(SaveCategoryBo categoryBo);

    /**
     * 更新类目属性
     *
     * @param categoryBo 更新类目属性
     */
    void updateCategoryParam(SaveCategoryBo categoryBo);

    /**
     * 删除类目
     *
     * @param id 类目id
     */
    void deleteCategory(Long id);

    /**
     * 查询类目树
     *
     * @param queryBo 查询参数
     * @return 类目树
     */
    List<CategoryBo> queryCategoryTree(CategoryQueryBo queryBo);

    List<CategoryBo> queryCategoryTreeHideNoThreeLevel(CategoryQueryBo queryBo);

    /**
     * 查询类目树(申请类目时使用)
     *
     * @param queryBo
     * @param shopId
     * @return
     */
    List<CategoryBo> queryCategoryTreeForApply(CategoryQueryBo queryBo, Long shopId);

    /**
     * 查询类目列表(包含保证金信息)
     *
     * @param queryBo 查询参数
     * @return 类目列表
     */
    List<CategoryBo> queryCategoryList(CategoryQueryBo queryBo);

    /**
     * 根据类目id查询类目map
     *
     * @param ids 类目id的集合
     * @return 类目map
     */
    Map<Long, CategoryBo> getCategoryMap(List<Long> ids);

    /**
     * 获取当前类目下最后一级的类目(包含保证金信息)
     *
     * @param id 类目id
     * @return 最后一级类目列表
     */
    List<CategoryBo> queryLastCategoryList(Long id);

    /**
     * 查询第一级类目的集合
     *
     * @param categoryIds 类目id集合
     * @return 第一级类目的集合
     */
    List<CategoryBo> queryFirstCategoryList(List<Long> categoryIds);

    /**
     * 绑定自定义表单
     *
     * @param categoryBo 绑定自定义表单参数
     */
    void bindCustomForm(CategoryBo categoryBo);

    /**
     * 根据类目id查询类目
     *
     * @param id 类目id
     * @return 类目
     */
    CategoryBo getCategoryById(Long id);

    /**
     * 查询类目树(补齐上级类目)
     *
     * @param queryBo 查询参数
     * @return 类目树
     */
    List<CategoryBo> queryCategoryTreeWithParent(CategoryQueryBo queryBo);

    /**
     * 根据三级类目id查询类目全称
     *
     * @param threeIds 三级类目id集合
     * @return 结果
     */
    List<CategoryResp> getAllCategoryPath(List<Long> threeIds);

    /**
     * 查询类目列表(从缓存中读取)
     *
     * @return 类目列表
     */
    List<CategoryCacheBo> listCategoryCache();

    /**
     * 清除类目缓存
     */
    void removeCategoryCache();

    /**
     * 查询类目信息(从缓存中读取)
     *
     * @param req 查询参数
     * @return 类目列表
     */
    List<CategoryBo> getCategoryList(QueryCategoryByIdsReq req);

    /**
     * 查询子类目id集合(包含自身)
     *
     * @param parentId 父类目id
     * @return 类目列表
     */
    List<Long> getRecursiveChildIds(Long parentId);

    /**
     * 按名称与深度查询类目
     *
     * @param queryBo
     * @return
     */
    CategoryBo queryCategoryByNameDepth(CategoryQueryBo queryBo);

    /**
     * 查询已经删除的类目
     *
     * @return
     */
    List<Long> queryAlreadyRemoveCategoryIds();
}
