package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model;

import java.util.List;

import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCouponSimpleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountRuleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteReductionBo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Setter
@Getter
public class ProductActivityInfoBo {

    /**
     * 是否参加了限时购
     */
    private boolean hasFlashSale;

    /**
     * 是否参加了专享价
     */
    private boolean hasExclusivePrice;

    /**
     * 是否参加了阶梯价
     */
    private boolean hasLadderPrice;

    /**
     * 是否参加了组合购
     */
    private boolean hasCollectionBuy;

    /**
     * 是否参加了折扣
     */
    private boolean hasDiscountActive;

    /**
     * 是否参加了满减
     */
    private boolean hasFullReduction;

    /**
     * 是否有优惠券
     */
    private boolean hasCoupon;

    /**
     * 限时购规则
     */
    private RemoteFlashSaleBo flashSaleRule;

    /**
     * 阶梯价格
     */
    private List<ProductLadderPriceBo> ladderPriceRuleList;

    /**
     * 折扣规则
     */
    private List<RemoteDiscountRuleBo> discountRuleList;

    /**
     * 满减规则
     */
    private RemoteReductionBo fullReductionRule;

    /**
     * 优惠券
     */
    private List<RemoteCouponSimpleBo> couponList;

    /**
     * 组合购
     */
    private List<RemoteCollocationBo> collectionBuyList;
}
