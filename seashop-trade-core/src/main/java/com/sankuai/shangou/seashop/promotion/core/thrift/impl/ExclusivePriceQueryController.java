package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.ExclusivePriceService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndMemberIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceDetailResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@RestController
@RequestMapping("/exclusivePrice")
public class ExclusivePriceQueryController implements ExclusivePriceQueryFeign {

    @Resource
    private ExclusivePriceService exclusivePriceService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ExclusivePriceSimpleResp>> pageList(@RequestBody ExclusivePriceQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req ->
                exclusivePriceService.pageList(req)
        );
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @Override
    public ResultDto<ExclusivePriceResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            return exclusivePriceService.getById(req.getId());
        });
    }

    @PostMapping(value = "/queryByProductAndMemberId", consumes = "application/json")
    @Override
    public ResultDto<ExclusivePriceResp> queryByProductAndMemberId(@RequestBody ProductAndMemberIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryByProductAndMemberId", request, req -> {
            req.checkParameter();
            return exclusivePriceService.queryByProductAndMemberId(req);
        });
    }

    @PostMapping(value = "/detailPageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ExclusivePriceDetailResp>> detailPageList(@RequestBody ExclusivePriceQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("detailPageList", request, req -> {
            return exclusivePriceService.detailPageList(req);
        });
    }
}
