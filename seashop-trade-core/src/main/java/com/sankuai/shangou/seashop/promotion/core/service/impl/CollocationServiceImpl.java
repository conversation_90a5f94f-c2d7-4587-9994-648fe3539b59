package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.core.remote.SkuRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductBasicReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductByIdsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.ActiveStatusEnum;
import com.sankuai.shangou.seashop.promotion.common.enums.PromotionResultCodeEnum;
import com.sankuai.shangou.seashop.promotion.common.remote.ShopRemoteService2;
import com.sankuai.shangou.seashop.promotion.core.service.CollocationService;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.Collocation;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationSku;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSale;
import com.sankuai.shangou.seashop.promotion.dao.core.model.*;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CollocationProductRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CollocationRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CollocationSkuRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.FlashSaleRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.collocation.PageCollocationResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 13:37
 */
@Service
@Slf4j
public class CollocationServiceImpl implements CollocationService {

    @Resource
    private CollocationRepository collocationRepository;

    @Resource
    private CollocationProductRepository collocationProductRepository;

    @Resource
    private CollocationSkuRepository collocationSkuRepository;

    @Resource
    private ProductRemoteService productRemoteService;

    @Resource
    private ShopRemoteService2 shopRemoteService2;

    @Resource
    private SkuRemoteService skuRemoteService;

    @Resource
    private FlashSaleRepository flashSaleRepository;

    @Resource
    private BaseLogAssist baseLogAssist;

    @Override
    public BasePageResp<PageCollocationResp> pageSellerCollocation(PageSellerCollocationReq request) {
        PageSellerCollocationDto pageSellerCollocationDaoReq = JsonUtil.copy(request, PageSellerCollocationDto.class);
        // 处理主商品名称模糊查询
        if (StringUtils.isNotBlank(request.getMainProductName())) {
            QueryProductReq queryProductReq = new QueryProductReq();
            queryProductReq.setProductName(request.getMainProductName());
            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
            if (null == productPage || CollUtil.isEmpty(productPage.getData())) {
                // 说明模糊没查到数据
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            pageSellerCollocationDaoReq.setMainFlag(true);
            pageSellerCollocationDaoReq.setMainProductIds(productPage.getData().stream().map(ProductPageResp::getProductId).distinct().collect(Collectors.toList()));
        }
        Page<PageCollocationDto> pageDao = collocationRepository.pageSellerCollocation(pageSellerCollocationDaoReq, request.getPageNo(), request.getPageSize());
        List<PageCollocationDto> listDao = pageDao.getResult();
        if(CollectionUtil.isEmpty(listDao)){
            // 说明模糊没查到数据
            return PageResultHelper.defaultEmpty(request.buildPage());
        }
        this.dealData(listDao);
        return PageResultHelper.transfer(pageDao, PageCollocationResp.class);
    }

    @Override
    public BasePageResp<PageCollocationResp> pageMCollocation(PageMCollocationReq request) {
        PageMCollocationDto pageMCollocationDaoReq = JsonUtil.copy(request, PageMCollocationDto.class);
        // 处理店铺名称模糊查询
        if (StringUtils.isNotBlank(request.getShopName())) {
            ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(ShopSimpleQueryReq.builder().shopName(request.getShopName()).build());
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                // 说明模糊没查到数据
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            if (shopSimpleListResp.getList().size() > PromotionConstant.MAX_LIKE_QUERY) {
                throw new BusinessException(PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getCode(),
                        PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getMsg());
            }
            pageMCollocationDaoReq.setShopIdList(shopSimpleListResp.getList().stream().map(ShopSimpleResp::getId).distinct().collect(Collectors.toList()));
        }
        // 处理主商品名称模糊查询
//        if (StringUtils.isNotBlank(request.getProductName())) {
//            QueryProductReq queryProductReq = new QueryProductReq();
//            queryProductReq.setProductName(request.getProductName());
//            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
//            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
//            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
//            List<Long> productIds = Lists.newArrayList();
//            if (null == productPage || CollUtil.isEmpty(productPage.getData())) {
//                // 说明模糊没查到数据,查mysql
//                return PageResultHelper.defaultEmpty(request.buildPage());
//            }
//            pageMCollocationDaoReq.setProductIdList(productPage.getData().stream().map(ProductPageResp::getProductId).distinct().collect(Collectors.toList()));
//        }
        if (StringUtils.isNotBlank(request.getProductName())) {
            // 说明模糊没查到数据,查mysql
            QueryProductByIdsReq queryProductByIdsReq = new QueryProductByIdsReq();
            queryProductByIdsReq.setProductName(request.getProductName());
            List<ProductPageResp> mysqlProducts = productRemoteService.queryProductByIds(queryProductByIdsReq);
            if (CollectionUtil.isEmpty(mysqlProducts)) {
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            pageMCollocationDaoReq.setProductIdList(mysqlProducts.stream().map(ProductPageResp::getProductId).distinct().collect(Collectors.toList()));
        }
        Page<PageCollocationDto> pageDao = collocationRepository.pageMCollocation(pageMCollocationDaoReq, request.getPageNo(), request.getPageSize());
        List<PageCollocationDto> listDao = pageDao.getResult();
        if(CollectionUtil.isEmpty(listDao)){
            // 说明模糊没查到数据
            return PageResultHelper.defaultEmpty(request.buildPage());
        }
        this.dealData(listDao);
        return PageResultHelper.transfer(pageDao, PageCollocationResp.class);
    }

    private void dealData(List<PageCollocationDto> listDao){
        List<Long> shopIds = listDao.stream().map(PageCollocationDto::getShopId).distinct().collect(Collectors.toList());
        ShopQueryReq shopQueryReq = new ShopQueryReq();
        shopQueryReq.setShopIds(shopIds);
        List<ShopResp> shopRespList = shopRemoteService2.queryShopsByIds(shopQueryReq);
        Map<Long, ShopResp> shopRespMap = shopRespList.stream().collect(Collectors.toMap(ShopResp::getId, Function.identity(), (k1, k2) -> k2));

        List<Long> productIds = listDao.stream().map(PageCollocationDto::getMainProductId).distinct().collect(Collectors.toList());
        QueryProductReq queryProductReq = new QueryProductReq();
        queryProductReq.setProductIds(productIds);
        List<ProductPageResp> productRespList = productRemoteService.queryProduct(queryProductReq).getData();

        if (CollectionUtil.isEmpty(productRespList)) {
            productRespList =new ArrayList<>();
        }
        // 有些商品被删除了，需要从mysql里面去查
        List<Long> notDeleteProductIds = productRespList.stream().map(ProductPageResp::getProductId).collect(Collectors.toList());
        List<Long> deleteProductIds = productIds.stream().filter(t -> !notDeleteProductIds.contains(t)).collect(Collectors.toList());
        if(!CollectionUtil.isEmpty(deleteProductIds)){
            QueryProductByIdsReq param = new QueryProductByIdsReq();
            param.setProductIdList(deleteProductIds.stream().map(String::valueOf).collect(Collectors.toList()));
            productRespList.addAll(productRemoteService.queryProductByIds(param));
        }

        Map<Long, ProductPageResp> productRespMap = productRespList.stream().collect(Collectors.toMap(ProductPageResp::getProductId, Function.identity(), (k1, k2) -> k2));
        listDao.forEach(dao -> {
            if(!Objects.isNull(shopRespMap) && !Objects.isNull(shopRespMap.get(dao.getShopId()))){
                dao.setShopName(shopRespMap.get(dao.getShopId()).getShopName());
            }
            if(!Objects.isNull(productRespMap) && !Objects.isNull(productRespMap.get(dao.getMainProductId()))){
                dao.setMainProductName(productRespMap.get(dao.getMainProductId()).getProductName());
            }
            Date now = new Date();
            Date startTime = dao.getStartTime();
            Date endTime = dao.getEndTime();
            if(DateUtil.compare(startTime,now) > 0 && DateUtil.compare(endTime,now) > 0){
                dao.setStatus(ActiveStatusEnum.NOT_START.getCode());
                dao.setStatusName(ActiveStatusEnum.NOT_START.getMsg());
            } else if (DateUtil.compare(startTime,now) < 0 && DateUtil.compare(endTime,now) > 0) {
                dao.setStatus(ActiveStatusEnum.START.getCode());
                dao.setStatusName(ActiveStatusEnum.START.getMsg());
            } else if (DateUtil.compare(endTime,now) < 0) {
                dao.setStatus(ActiveStatusEnum.END.getCode());
                dao.setStatusName(ActiveStatusEnum.END.getMsg());
            }
        });
    }

    @Override
    /*@ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,
            actionName = "新增组合购", processModel = ExaminModelEnum.PROMOTION,
            processType = ExaProEnum.INSERT, serviceMethod = "addCollocation",
            dto = AddCollocationReq.class, entity = Collocation.class)*/
    public void addCollocation(AddCollocationReq request) {
        List<AddCollocationProductReq> productReqList = request.getProductReqList();
        List<Long> productIdList = productReqList.stream().map(AddCollocationProductReq::getProductId).map(Long::parseLong).distinct().collect(Collectors.toList());
        // 鉴权这些商品是否属于当前店铺
        QueryProductBasicReq basicReq = new QueryProductBasicReq();
        basicReq.setProductIds(productIdList);
        List<ProductBasicDto> productBasicList = productRemoteService.queryProductBase(basicReq);
        if(CollectionUtil.isEmpty(productBasicList) || productIdList.size() != productBasicList.size()){
            throw new BusinessException("保存失败，请选择正确有效的商品");
        }
        List<ProductBasicDto> filterProductBasicList = productBasicList.stream().filter(v -> !v.getShopId().equals(request.getShopId())).collect(Collectors.toList());
        AssertUtil.throwIfTrue(!CollectionUtil.isEmpty(filterProductBasicList), "保存失败，存在不属于当前登录店铺的商品");
        this.determineActivityRules(productIdList, null);

        List<String> skuIdList = Lists.newArrayList();
        request.getProductReqList().forEach(v -> {
            skuIdList.addAll(v.getSkuReqList().stream().map(AddCollocationSkuReq::getSkuId).collect(Collectors.toList()));
        });
        SkuQueryReq skuQueryReq = new SkuQueryReq();
        skuQueryReq.setSkuIdList(skuIdList);
        SkuListResp skuListResp = productRemoteService.querySkuList(skuQueryReq);
        List<SkuQueryResp> skuList = skuListResp.getSkuList();
        Map<String,SkuQueryResp> map = skuList.stream().collect(Collectors.toMap(SkuQueryResp::getSkuId, Function.identity(), (v1,v2) -> v2));
        for(AddCollocationProductReq product : request.getProductReqList()){
            for(AddCollocationSkuReq sku : product.getSkuReqList()){
                if(!product.getProductId().equals(sku.getProductId().toString())){
                    throw new BusinessException("规格与商品不匹配，规格："+sku.getSkuId()+",商品："+product.getProductId());
                }
                SkuQueryResp skuQueryResp = map.get(sku.getSkuId());
                if(!skuQueryResp.getProductId().equals(sku.getProductId())){
                    throw new BusinessException("该规格不属于该商品，规格："+sku.getSkuId()+",商品："+sku.getProductId());
                }
            }
        }
        Collocation collocation = JsonUtil.copy(request, Collocation.class);
        collocation.setCreateTime(new Date());
        collocation.setUpdateTime(new Date());
        TransactionHelper.doInTransaction(() -> {
            Long collocationId = collocationRepository.insertCollocation(collocation);
            for(int i=0; i<productReqList.size(); i++){
                AddCollocationProductReq product = productReqList.get(i);
                CollocationProduct collocationProduct = JsonUtil.copy(product, CollocationProduct.class);
                collocationProduct.setColloId(collocationId);
                collocationProduct.setCreateTime(new Date());
                collocationProduct.setUpdateTime(new Date());
                collocationProduct.setDisplaySequence(i);
                collocationProductRepository.insertCollocationProduct(collocationProduct);
                List<AddCollocationSkuReq> skuReqList = product.getSkuReqList();
                skuReqList.forEach(sku -> {
                    CollocationSku collocationSku = JsonUtil.copy(sku, CollocationSku.class);
                    collocationSku.setColloProductId(collocationProduct.getId());
                    collocationSku.setCreateTime(new Date());
                    collocationSku.setUpdateTime(new Date());
                    collocationSkuRepository.insertCollocationSku(collocationSku);
                });
            }
        });
    }

    private void determineActivityRules(List<Long> productIdList, Long colloId){
        List<LadderPriceDto> priceRespList = productRemoteService.getLadderPriceBoList(productIdList);
        if(!CollectionUtil.isEmpty(priceRespList)){
            List<Long> productIds = priceRespList.stream().map(LadderPriceDto::getProductId).distinct().collect(Collectors.toList());
            QueryProductBasicReq request = new QueryProductBasicReq();
            request.setProductIds(productIds);
            List<String> productNameList = productRemoteService.queryProductBase(request).stream().map(ProductBasicDto::getProductName).collect(Collectors.toList());
            throw new BusinessException(productNameList + "这些商品设置了阶梯价，不能再参加组合购");
        }
        List<String> productIdStrList = productIdList.stream().map(String::valueOf).collect(Collectors.toList());
        List<CollocationActivityRespDto> colloRespList = collocationRepository.queryCollocationByProductIdsAndStatus(productIdStrList, 1);
        if(!CollectionUtil.isEmpty(colloRespList)){
            if(colloId != null){
                colloRespList = colloRespList.stream().filter(collo -> !collo.getId().equals(String.valueOf(colloId))).collect(Collectors.toList());
            }
            if (!CollectionUtil.isEmpty(colloRespList)) {
                colloRespList = colloRespList.stream().filter(collo -> collo.getMainFlag()).collect(Collectors.toList());
                if(!CollectionUtil.isEmpty(colloRespList)){
                    List<String> productIds = colloRespList.stream().map(CollocationActivityRespDto::getProductId).distinct().collect(Collectors.toList());
                    QueryProductBasicReq request = new QueryProductBasicReq();
                    request.setProductIds(productIds.stream().map(Long::parseLong).collect(Collectors.toList()));
                    List<String> productNameList = productRemoteService.queryProductBase(request).stream().map(ProductBasicDto::getProductName).collect(Collectors.toList());
                    throw new BusinessException(productNameList + "这些商品参加了组合购，不能再参加组合购");
                }
            }
        }
        FlashSaleParamDto paramDto = new FlashSaleParamDto();
        paramDto.setProductIdList(productIdList);
//        paramDto.setStatusList(Arrays.asList(FlashSaleStatusEnum.WAIT_FOR_AUDITING.getStatus(), FlashSaleStatusEnum.ONGOING.getStatus(), FlashSaleStatusEnum.NOT_BEGIN.getStatus()));
        // 根据时间查，不根据状态查
        paramDto.setNormalFlag(true);
        BasePageParam page = new BasePageParam();
        page.setPageNum(PromotionConstant.DEFAULT_PAGE);
        page.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
        Page<FlashSale> flashSalePage = flashSaleRepository.pageList(page, paramDto, null);
        List<FlashSale> flashSaleList = flashSalePage.getResult();
        if(!CollectionUtil.isEmpty(flashSaleList)){
            List<Long> productIds = flashSaleList.stream().map(FlashSale::getProductId).distinct().collect(Collectors.toList());
            QueryProductBasicReq request = new QueryProductBasicReq();
            request.setProductIds(productIds);
            List<String> productNameList = productRemoteService.queryProductBase(request).stream().map(ProductBasicDto::getProductName).collect(Collectors.toList());
            throw new BusinessException(productNameList + "这些商品参加了限时购，不能再参加组合购");
        }
    }

    @Override
    /*@ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,
            actionName = "修改组合购", processModel = ExaminModelEnum.PROMOTION,
            processType = ExaProEnum.MODIFY, serviceMethod = "updateCollocation",
            dto = UpdateCollocationReq.class, entity = Collocation.class)*/
    public void updateCollocation(UpdateCollocationReq request) {
        List<UpdateCollocationProductReq> updateProductReqList = request.getProductReqList();
        List<Long> updateProductIds = updateProductReqList.stream().map(UpdateCollocationProductReq::getProductId).map(Long::parseLong).distinct().collect(Collectors.toList());
        // 鉴权这些商品是否属于当前店铺
        QueryProductBasicReq basicReq = new QueryProductBasicReq();
        basicReq.setProductIds(updateProductIds);
        List<ProductBasicDto> productBasicList = productRemoteService.queryProductBase(basicReq);
        if(CollectionUtil.isEmpty(productBasicList) || updateProductIds.size() != productBasicList.size()){
            throw new BusinessException("保存失败，请选择正确有效的商品");
        }
        List<ProductBasicDto> filterProductBasicList = productBasicList.stream().filter(v -> !v.getShopId().equals(request.getShopId())).collect(Collectors.toList());
        AssertUtil.throwIfTrue(!CollectionUtil.isEmpty(filterProductBasicList), "保存失败，存在不属于当前登录店铺的商品");
        this.determineActivityRules(updateProductIds, request.getId());

        List<String> skuIdList = Lists.newArrayList();
        request.getProductReqList().forEach(v -> {
            skuIdList.addAll(v.getSkuReqList().stream().map(UpdateCollocationSkuReq::getSkuId).collect(Collectors.toList()));
        });
        SkuQueryReq skuQueryReq = new SkuQueryReq();
        skuQueryReq.setSkuIdList(skuIdList);
        SkuListResp skuListResp = productRemoteService.querySkuList(skuQueryReq);
        List<SkuQueryResp> skuList = skuListResp.getSkuList();
        Map<String,SkuQueryResp> map = skuList.stream().collect(Collectors.toMap(SkuQueryResp::getSkuId, Function.identity(), (v1,v2) -> v2));
        for(UpdateCollocationProductReq product : request.getProductReqList()){
            for(UpdateCollocationSkuReq sku : product.getSkuReqList()){
                if(!product.getProductId().equals(sku.getProductId().toString())){
                    throw new BusinessException("规格与商品不匹配，规格："+sku.getSkuId()+",商品："+product.getProductId());
                }
                SkuQueryResp skuQueryResp = map.get(sku.getSkuId());
                if(!skuQueryResp.getProductId().equals(sku.getProductId())){
                    throw new BusinessException("该规格不属于该商品，规格："+sku.getSkuId()+",商品："+sku.getProductId());
                }
            }
        }
        Collocation collocation = JsonUtil.copy(request, Collocation.class);
        collocation.setUpdateTime(new Date());
        // 副表先删后增
        CollocationProduct collocationProduct = new CollocationProduct();
        collocationProduct.setColloId(request.getId());
        List<CollocationProduct> productList = collocationProductRepository.queryCollocationProductList(collocationProduct);
        List<Long> colloProductIds = productList.stream().map(CollocationProduct::getProductId).distinct().collect(Collectors.toList());

        TransactionHelper.doInTransaction(() -> {
            collocationProductRepository.deleteCollocationProductByColloId(request.getId());
            collocationSkuRepository.deleteCollocationSkuByColloProductIds(colloProductIds);
            // 主表update
            Long colloId = collocationRepository.updateCollocationById(collocation);
            List<UpdateCollocationProductReq> productReqList = request.getProductReqList();
            for(int i=0; i<productReqList.size(); i++) {
                UpdateCollocationProductReq product = productReqList.get(i);
                CollocationProduct productParam = JsonUtil.copy(product, CollocationProduct.class);
                productParam.setColloId(colloId);
                productParam.setUpdateTime(new Date());
                productParam.setId(null);
                productParam.setDisplaySequence(i);
                Long colloProductId = collocationProductRepository.insertCollocationProduct(productParam);
                List<UpdateCollocationSkuReq> skuReqList = product.getSkuReqList();
                skuReqList.forEach(sku -> {
                    CollocationSku collocationSku = JsonUtil.copy(sku, CollocationSku.class);
                    collocationSku.setId(null);
                    collocationSku.setColloProductId(colloProductId);
                    collocationSku.setUpdateTime(new Date());
                    collocationSkuRepository.insertCollocationSku(collocationSku);
                });
            }
        });
    }

    public Map<Long, CollocationResp> queryCollocationDetailMap(List<Long> ids) {
        // 第一步，查主表
        List<Collocation> collocationList = collocationRepository.selectByIds(ids);
        if(CollectionUtil.isEmpty(collocationList)){
            return null;
        }
        List<CollocationResp> result = JsonUtil.copyList(collocationList, CollocationResp.class);
        // 第二步，查组合购商品信息
        List<Long> colloIds = result.stream().map(CollocationResp::getId).collect(Collectors.toList());
        List<CollocationProduct> collocationProductList = collocationProductRepository.selectByColloIds(colloIds);
        if(CollectionUtil.isEmpty(collocationProductList)){
            return result.stream().collect(Collectors.toMap(CollocationResp::getId, Function.identity(), (k1, k2) -> k2));
        }
        // 第三步，查出所有的sku商品，以及补全里面的数据
        List<Long> colloProductIdList = collocationProductList.stream().map(CollocationProduct::getId).distinct().collect(Collectors.toList());
        CollocationSkuDto collocationSkuDaoDto = new CollocationSkuDto();
        collocationSkuDaoDto.setColloProductIdList(colloProductIdList);
        List<CollocationSku> collocationSkuList = collocationSkuRepository.queryCollocationSkuList(collocationSkuDaoDto);
        List<String> skuIdList = collocationSkuList.stream().map(CollocationSku::getSkuId).distinct().collect(Collectors.toList());
        // 查出所有的skuId对应的sku规格1、规格2、规格3名称
        SkuQueryReq skuQueryReq = new SkuQueryReq();
        skuQueryReq.setSkuIdList(skuIdList);
        List<SkuQueryResp> skuQueryRespList = skuRemoteService.querySkuList(skuQueryReq);
        Map<String, SkuQueryResp> skuMap = skuQueryRespList.stream().collect(Collectors.toMap(SkuQueryResp::getSkuId, Function.identity(), (k1, k2) -> k2));
        // 循环所有的sku商品补齐sku规格1、规格2、规格3名称字段值
        List<CollocationSkuResp> allSkuRespList = new ArrayList<>();
        collocationSkuList.forEach(v -> {
            CollocationSkuResp collocationSkuResp = JsonUtil.copy(v, CollocationSkuResp.class);
            if(!Objects.isNull(skuMap.get(collocationSkuResp.getSkuId()))){
                String specValue = this.getSpecValue(skuMap.get(collocationSkuResp.getSkuId()));
                if(StringUtils.isEmpty(specValue)){
                    specValue = "无规格";
                }
                collocationSkuResp.setSpecName(specValue);
                collocationSkuResp.setStock(skuMap.get(collocationSkuResp.getSkuId()).getStock());
            }
            allSkuRespList.add(collocationSkuResp);
        });
        // 查出所有商品的主图
        List<Long> productIdList = collocationProductList.stream().map(CollocationProduct::getProductId).distinct().collect(Collectors.toList());
        QueryProductBasicReq request = new QueryProductBasicReq();
        request.setProductIds(productIdList);
        List<ProductBasicDto> productList = productRemoteService.queryProductBase(request);
        Map<Long, ProductBasicDto> productMap = productList.stream().collect(Collectors.toMap(ProductBasicDto::getProductId, Function.identity(), (k1, k2) -> k2));
        List<CollocationProductResp> productRespList = new ArrayList<>();
        collocationProductList.forEach(v -> {
            CollocationProductResp productResp = JsonUtil.copy(v, CollocationProductResp.class);
            ProductBasicDto product = productMap.get(v.getProductId());
            if (product != null) {
                // 商品主图
                productResp.setImagePath(product.getImagePath());
                // 商品名称
                productResp.setProductName(product.getProductName());
                // 商品的原价
                productResp.setOriginalPrice(product.getMinSalePrice());
                // 是否多规格
                productResp.setHasSku(product.getHasSku());
            }
            // sku信息
            productResp.setSkuRespList(allSkuRespList.stream().filter(sku -> v.getId().equals(sku.getColloProductId())).distinct().collect(Collectors.toList()));
            // 获取组合购的底价
            productResp.setMinSalePrice(productResp.getSkuRespList().stream().map(CollocationSkuResp::getPrice).filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            productRespList.add(productResp);
        });
        for (CollocationResp collocationResp : result) {
            collocationResp.setProductRespList(productRespList.stream().filter(v -> collocationResp.getId().equals(v.getColloId())).collect(Collectors.toList()));
        }
        return result.stream().collect(Collectors.toMap(CollocationResp::getId, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    public CollocationResp queryCollocationDetail(CollocationDetailReq req) {
        // 查主表
        CollocationDto collocationDaoDto = new CollocationDto();
        collocationDaoDto.setId(req.getId());
        List<Collocation> collocationList = collocationRepository.queryCollocationList(collocationDaoDto);
        if(CollectionUtil.isEmpty(collocationList)){
            throw new BusinessException("活动不存在");
        }
        CollocationResp result = JsonUtil.copy(collocationList.get(0), CollocationResp.class);
        if(req.getSourceFrom() != null && req.getSourceFrom() == 2 && !result.getShopId().equals(req.getShopId())){
            throw new BusinessException("该活动不属于当前用户");
        }
        // 组合购商品信息
        CollocationProduct collocationProduct = new CollocationProduct();
        collocationProduct.setColloId(result.getId());
        List<CollocationProduct> collocationProductList = collocationProductRepository.queryCollocationProductList(collocationProduct);
        if(CollectionUtil.isEmpty(collocationProductList)){
            return result;
        }
        // 查出所有的sku商品
        List<Long> colloProductIdList = collocationProductList.stream().map(CollocationProduct::getId).distinct().collect(Collectors.toList());
        CollocationSkuDto collocationSkuDaoDto = new CollocationSkuDto();
        collocationSkuDaoDto.setColloProductIdList(colloProductIdList);
        List<CollocationSku> collocationSkuList = collocationSkuRepository.queryCollocationSkuList(collocationSkuDaoDto);
        List<String> skuIdList = collocationSkuList.stream().map(CollocationSku::getSkuId).distinct().collect(Collectors.toList());
        // 查出所有的skuId对应的sku规格1、规格2、规格3名称
        SkuQueryReq skuQueryReq = new SkuQueryReq();
        skuQueryReq.setSkuIdList(skuIdList);
        List<SkuQueryResp> skuQueryRespList = skuRemoteService.querySkuList(skuQueryReq);
        Map<String, SkuQueryResp> skuMap = skuQueryRespList.stream().collect(Collectors.toMap(SkuQueryResp::getSkuId, Function.identity(), (k1, k2) -> k2));
        // 循环所有的sku商品补齐sku规格1、规格2、规格3名称字段值
        List<CollocationSkuResp> allSkuRespList = new ArrayList<>();
        collocationSkuList.forEach(v -> {
            CollocationSkuResp collocationSkuResp = JsonUtil.copy(v, CollocationSkuResp.class);
            if(!Objects.isNull(skuMap.get(collocationSkuResp.getSkuId()))){
                String specValue = this.getSpecValue(skuMap.get(collocationSkuResp.getSkuId()));
                if(StringUtils.isEmpty(specValue)){
                    specValue = "无规格";
                }
                collocationSkuResp.setSpecName(specValue);
                collocationSkuResp.setStock(skuMap.get(collocationSkuResp.getSkuId()).getStock());
            }
            allSkuRespList.add(collocationSkuResp);
        });
        result.setSkuRespList(allSkuRespList);
        // 查出所有商品的主图
        List<Long> productIdList = collocationProductList.stream().map(CollocationProduct::getProductId).distinct().collect(Collectors.toList());
        QueryProductBasicReq request = new QueryProductBasicReq();
        request.setProductIds(productIdList);
        List<ProductBasicDto> productList = productRemoteService.queryProductBase(request);
        Map<Long, ProductBasicDto> productMap = productList.stream().collect(Collectors.toMap(ProductBasicDto::getProductId, Function.identity(), (k1, k2) -> k2));
        List<CollocationProductResp> productRespList = new ArrayList<>();
        collocationProductList.forEach(v -> {
            CollocationProductResp productResp = JsonUtil.copy(v, CollocationProductResp.class);
            ProductBasicDto product = productMap.get(v.getProductId());
            if (product != null) {
                // 商品主图
                productResp.setImagePath(product.getImagePath());
                // 商品名称
                productResp.setProductName(product.getProductName());
                // 商品的原价
                productResp.setOriginalPrice(product.getMinSalePrice());
                // 是否多规格
                productResp.setHasSku(product.getHasSku());
            }
            // sku信息
            productResp.setSkuRespList(allSkuRespList.stream().filter(sku -> v.getId().equals(sku.getColloProductId())).distinct().collect(Collectors.toList()));
            // 获取组合购的底价
            productResp.setMinSalePrice(productResp.getSkuRespList().stream().map(CollocationSkuResp::getPrice).filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            productRespList.add(productResp);
        });
        result.setProductRespList(productRespList);
        return result;
    }

    @Override
    public void cancelCollocation(CancelCollocationReq req) {
        CollocationDto collocationDaoDto = new CollocationDto();
        collocationDaoDto.setId(req.getId());
        List<Collocation> collocationList = collocationRepository.queryCollocationList(collocationDaoDto);
        if(CollectionUtil.isEmpty(collocationList)){
            throw new BusinessException("活动不存在");
        }
        CollocationResp result = JsonUtil.copy(collocationList.get(0), CollocationResp.class);
        if(!result.getShopId().equals(req.getShopId())){
            throw new BusinessException("该活动不属于当前用户");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CancelCollocationLog oldValue = new CancelCollocationLog();
        oldValue.setId(result.getId());
        oldValue.setShopId(result.getShopId());
        oldValue.setEndTime(simpleDateFormat.format(result.getEndTime()));
        CancelCollocationLog newValue = new CancelCollocationLog();
        newValue.setId(req.getId());
        newValue.setShopId(result.getShopId());
        newValue.setEndTime(simpleDateFormat.format(new Date()));
        baseLogAssist.recordLog(ExaminModelEnum.PROMOTION, ExaProEnum.MODIFY, "结束组合购活动",
                req.getOperationUserId(), req.getShopId(),
                oldValue, newValue);

        Collocation collocation = new Collocation();
        collocation.setId(req.getId());
        collocation.setEndTime(new Date());
        collocationRepository.updateCollocationById(collocation);
    }

    @Override
    public MallCollocationResp queryMallCollocationList(MallCollocationReq request) {
        MallCollocationResp result = new MallCollocationResp();
        List<CollocationResp> listResp = new ArrayList<>();
        // 第一步，根据产品ID查询这个产品涉及到那些组合购活动
        CollocationProduct collocationProduct = new CollocationProduct();
        collocationProduct.setProductId(request.getProductId());
        List<CollocationProduct> collocationProductList = collocationProductRepository.queryCollocationProductList(collocationProduct);
        if(CollectionUtil.isEmpty(collocationProductList)){
            return result;
        }
        List<Long> colloIdList = collocationProductList.stream().map(CollocationProduct::getColloId).distinct().collect(Collectors.toList());
        CollocationDto collocationDaoDto = new CollocationDto();
        collocationDaoDto.setColloIdList(colloIdList);
        collocationDaoDto.setShopId(request.getShopId());
        List<Collocation> collocationList = collocationRepository.queryCollocationList(collocationDaoDto);
        if(CollectionUtil.isEmpty(collocationList)){
            return result;
        }
        // 过滤出有效的组合购活动
        Date now = new Date();
        List<Collocation> filterCollocationList = collocationList.stream().filter(c -> c.getStartTime().before(now) && c.getEndTime().after(now)).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(filterCollocationList)){
            return result;
        }
        List<Long> colloIdListResp = filterCollocationList.stream().map(Collocation::getId).distinct().collect(Collectors.toList());
        Map<Long, CollocationResp> map = this.queryCollocationDetailMap(colloIdListResp);
        filterCollocationList.forEach(v -> {
            listResp.add(map.get(v.getId()));
        });
        result.setCollocationRespList(listResp);
        return result;
    }

    @Override
    public List<CollocationActivityResp> queryCollocationByProductIdsAndStatus(CollocationActivityReq request) {
        List<CollocationActivityRespDto> list = collocationRepository.queryCollocationByProductIdsAndStatus(request.getProductIds(), request.getFlag());
        return JsonUtil.copyList(list, CollocationActivityResp.class);
    }

    /**
     * 获取sku规格值
     * 逗号拼接 spec1Value,spec2Value,spec3Value
     * 如果值为空的跳过
     *
     * @param sku sku
     * @return 规格值
     */
    public String getSpecValue(SkuQueryResp sku) {
        if (sku == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotEmpty(sku.getSpec1Value())) {
            sb.append(sku.getSpec1Value()).append(",");
        }
        if (StringUtils.isNotEmpty(sku.getSpec2Value())) {
            sb.append(sku.getSpec2Value()).append(",");
        }
        if (StringUtils.isNotEmpty(sku.getSpec3Value())) {
            sb.append(sku.getSpec3Value()).append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
}
