package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleConfigService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.PlatFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.ShopFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleConfigCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@RestController
@RequestMapping("/flashSaleConfig")
public class FlashSaleConfigCmdController implements FlashSaleConfigCmdFeign {

    @Resource
    private FlashSaleConfigService flashSaleConfigService;

    @PostMapping(value = "/updatePlatConfig", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updatePlatConfig(@RequestBody PlatFlashSaleConfigReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updatePlatConfig", request, req -> {
            req.checkParameter();
            flashSaleConfigService.updatePlatConfig(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/updateShopConfig", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateShopConfig(@RequestBody ShopFlashSaleConfigReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateShopConfig", request, req -> {
            req.checkParameter();
            flashSaleConfigService.updateShopConfig(req);
            return new BaseResp();
        });
    }
}
