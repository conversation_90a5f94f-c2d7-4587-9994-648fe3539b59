package com.sankuai.shangou.seashop.product.core.mq.publisher;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.thrift.core.event.category.CategoryChangeEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/03/08 16:50
 */
@Service
@Slf4j
public class CategoryChangePublisher {

    @Resource
    private DefaultRocketMq defaultRocketMq;

    public void sendMessage(CategoryChangeEvent event) {
        String body = JsonUtil.toJsonString(event);
        try {
           log.info("[MQ] send category change message-data：{}", body);
//           producerProcessor.sendMessage(body);
            defaultRocketMq.convertAndSend(MafkaConstant.TOPIC_CATEGORY_CHANGE, body);
       } catch (Exception e) {
           log.error("[MQ] send category change message-data：{}", body, e);
           throw new SystemException("发送类目变更消息失败");
       }
    }

}
