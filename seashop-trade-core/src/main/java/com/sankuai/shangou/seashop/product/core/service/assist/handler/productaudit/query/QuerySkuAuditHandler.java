package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.query;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;

import lombok.extern.slf4j.Slf4j;

/**
 * sku 审核查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:27
 */
@Component
@Slf4j
public class QuerySkuAuditHandler extends AbsQueryProductAuditHandler {

    @Resource
    private SkuAssist skuAssist;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();
        ProductBo productBo = context.getOldProductBo();

        // 查询sku集合
        List<ProductSkuBo> skuList = skuAssist.getSkuAuditBoList(productId);
        productBo.setSkuList(skuList);
        productBo.setStock(skuAssist.getSumStock(productBo.getSkuList()));
        productBo.setSafeStock(skuAssist.getFirstSafeStock(productBo.getSkuList()));
        if (CollectionUtils.isNotEmpty(skuList)) {
            productBo.setSkuAutoId(skuList.get(0).getSkuAutoId());
        }

    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_SKU_AUDIT;
    }

}
