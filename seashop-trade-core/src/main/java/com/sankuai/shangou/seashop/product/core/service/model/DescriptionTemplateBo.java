package com.sankuai.shangou.seashop.product.core.service.model;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:49
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DescriptionTemplateBo extends BaseParamReq {

    /**
     * 版式id
     */
    @ExaminField(description = "版式id")
    @PrimaryField(title = "版式id")
    private Long id;

    /**
     * 版式名称
     */
    @ExaminField(description = "版式名称")
    private String name;

    /**
     * 版式位置
     */
    private DescriptionTemplatePositionEnum position;

    /**
     * 版式位置 1-顶部 2-底部
     */
    private Integer positionCode;

    /**
     * 版式位置描述
     */
    @ExaminField(description = "版式位置")
    private String positionDesc;

    /**
     * PC端版式内容
     */
    @ExaminField(description = "PC端版式内容")
    private String content;

    /**
     * 移动端版式内容
     */
    @ExaminField(description = "移动端版式内容")
    private String mobileContent;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 关联商品数
     */
    private long relateGoodsCount;

}
