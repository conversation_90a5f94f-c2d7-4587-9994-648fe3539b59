package com.sankuai.shangou.seashop.trade.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.trade.core.service.model.AddShoppingCartBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ChangeShoppingCartSkuCntBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ClearInvalidShoppingCartSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.DeleteShoppingCartSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewBuyNowOrderParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewCollocationOrderParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewFlashSaleOrderParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewOrderBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewOrderSelectSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.SelectShopBo;
import com.sankuai.shangou.seashop.trade.core.service.model.SelectShoppingCartSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.UserShoppingCartBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.AddFromAddonBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.AddFromAddonResultBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.SelectAllBo;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewOrderReq;

/**
 * <AUTHOR>
 */
public interface ShoppingCartService {

    /**
     * 获取用户购物车列表
     * <AUTHOR>
     * @param userId 商家用户ID
     */
    UserShoppingCartBo getUserShoppingCartList(Long userId);

    /**
     * 获取用户购物车商品SKU数量
     * <AUTHOR>
     * @param userId 商家用户ID
     * java.lang.Long
     */
    int getUserShoppingCartCount(Long userId);

    /**
     * 添加购物车商品SKU
     * <AUTHOR>
     * @param addBo
     * void
     */
    void addShoppingCart(AddShoppingCartBo addBo);

    /**
     * 批量添加购物车商品SKU
     * @param shoppingCartList
     */
    void addShoppingCartBatch(List<AddShoppingCartBo> shoppingCartList);

    /**
     * 删除购物车SKU，批量删除，单个删除自己处理成数组
     * <AUTHOR>
     * @param deleteBo
     * java.lang.Integer
     */
    void deleteShoppingCart(DeleteShoppingCartSkuBo deleteBo);

    /**
     * 删除购物车SKU，批量删除，单个删除自己处理成数组
     * <AUTHOR>
     * @param clearBo
     * java.lang.Integer
     */
    void clearInvalid(ClearInvalidShoppingCartSkuBo clearBo);

    /**
     * 变更购物车sku数量，
     * <p>因为可能涉及到优惠的变更，所以需要返回当前店铺的商品列表，以及最新的优惠信息；也要区分商品是否是勾选的</p>
     * <AUTHOR>
     * @param changeBo 变更的购物车sku数量请求入参
     */
    ShopProductListBo changeShoppingCartSkuCnt(ChangeShoppingCartSkuCntBo changeBo);

    /**
     * 获取订单预览信息，基于前端选择提交的sku获取预览订单信息
     * <AUTHOR>
     * @param userId 商家用户ID
     * @param selectedSkuList 选择的购物车sku
     * @return PreviewOrderResp 订单预览信息
     */
    PreviewOrderBo previewOrder(Long userId, List<PreviewOrderSelectSkuBo> selectedSkuList);

    /**
     * 选中sku
     * <p>因为可能涉及到优惠的变更，所以需要返回当前店铺的商品列表，以及最新的优惠信息；也要区分商品是否是勾选的</p>
     * <AUTHOR>
     * @param selectBo 变更的购物车sku数量请求入参
     */
    ShopProductListBo selectShopSku(SelectShoppingCartSkuBo selectBo);

    /**
     * 删除购物车指定SKU
     * <p>目前场景是下单成功后异步删除对应的购物车SKU数据</p>
     * @param userId 用户ID
     * @param skuIdList skuId列表
     */
    void removeShoppingCart(Long userId, List<String> skuIdList);

    /**
     * 凑单页面加购
     * @param addFromAddonBo 加购入参
     * @return 凑单汇总
     */
    AddFromAddonResultBo addFromAddon(AddFromAddonBo addFromAddonBo);

    /**
     * 获取限时购订单预览信息
     * 直接从详情页选择 立即购买 进入订单预览页
     * <AUTHOR>
     * @param paramBo 限时购预览入参
     * @return PreviewOrderResp 订单预览信息
     */
    PreviewOrderBo previewFlashSaleOrder(PreviewFlashSaleOrderParamBo paramBo);

    /**
     * 获取组合购订单预览信息
     * 直接从详情页选择 组合购的立即购买 进入订单预览页
     * <AUTHOR>
     * @param paramBo 组合购预览入参
     * @return PreviewOrderResp 订单预览信息
     */
    PreviewOrderBo previewCollocationOrder(PreviewCollocationOrderParamBo paramBo);

    /**
     * 获取立即购买订单预览信息
     * 直接从详情页选择 立即购买 进入订单预览页
     * <AUTHOR>
     * @param paramBo 限时购预览入参
     * @return PreviewOrderResp 订单预览信息
     */
    PreviewOrderBo previewBuyNowOrder(PreviewBuyNowOrderParamBo paramBo);

    /**
     * 购物车页面选中店铺
     * <p>因为可能涉及到优惠的变更，所以需要返回当前店铺的商品列表，以及最新的优惠信息；也要区分商品是否是勾选的</p>
     * <AUTHOR>
     * @param selectBo 变更的购物车sku数量请求入参
     */
    ShopProductListBo selectShop(SelectShopBo selectBo);

    /**
     * 全选购物车
     * <p>因为可能涉及到优惠的变更，所以需要返回当前店铺的商品列表，以及最新的优惠信息；也要区分商品是否是勾选的</p>
     * <AUTHOR>
     * @param selectBo 变更的购物车sku数量请求入参
     */
    UserShoppingCartBo selectAll(SelectAllBo selectBo);

    /**
     * 校验购物车是否可以提交，当前提交会提交用户的整个购物车
     *
     * @param previewOrderReq 待提交的购物车商品
     */
    void checkCanSubmit(PreviewOrderReq previewOrderReq);
}
