package com.sankuai.shangou.seashop.product.core.service.assist.comparator;

import java.math.BigDecimal;
import java.util.Objects;

import lombok.Getter;
import lombok.Setter;

/**
 * 默认比较器(根据类型比较字段)
 *
 * <AUTHOR>
 * @date 2024/02/22 10:06
 */
@Setter
@Getter
public class DefaultComparator extends AbsFieldComparator<Object> {

    @Override
    public boolean compareField(Object newObj, Object oldObj) {
        if (Objects.equals(newObj, oldObj)) {
            return true;
        }
        if ((newObj == null && oldObj != null) || (oldObj == null && newObj != null)) {
            return false;
        }

        Class<?> fieldType = fieldAnno.type();
        if (fieldType.equals(Integer.class)) {
            return Objects.equals(Integer.parseInt(String.valueOf(newObj)), Integer.parseInt(String.valueOf(oldObj)));
        }
        else if (fieldType.equals(Long.class)) {
            return Objects.equals(Long.parseLong(String.valueOf(newObj)), Long.parseLong(String.valueOf(oldObj)));
        }
        else if (fieldType.equals(Double.class)) {
            return Objects.equals(Double.parseDouble(String.valueOf(newObj)), Double.parseDouble(String.valueOf(oldObj)));
        }
        else if (fieldType.equals(Float.class)) {
            return Objects.equals(Float.parseFloat(String.valueOf(newObj)), Float.parseFloat(String.valueOf(oldObj)));
        }
        else if (fieldType.equals(Boolean.class)) {
            return Objects.equals(Boolean.parseBoolean(String.valueOf(newObj)), Boolean.parseBoolean(String.valueOf(oldObj)));
        }
        else if (fieldType.equals(BigDecimal.class)) {
            return BigDecimal.valueOf(Double.parseDouble(String.valueOf(newObj))).compareTo(BigDecimal.valueOf(Double.parseDouble(String.valueOf(oldObj)))) == 0;
        }
        else {
            return String.valueOf(newObj).equals(String.valueOf(oldObj));
        }
    }
}
