package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.service.DescriptionTemplateService;
import com.sankuai.shangou.seashop.product.core.service.converter.DescriptionTemplateConverter;
import com.sankuai.shangou.seashop.product.core.service.model.DescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.core.service.model.DescriptionTemplateQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionTemplate;
import com.sankuai.shangou.seashop.product.dao.core.repository.DescriptionTemplateRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionRepository;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:28
 */
@Service
public class DescriptionTemplateServiceImpl implements DescriptionTemplateService {

    @Resource
    private DescriptionTemplateRepository descriptionTemplateRepository;
    @Resource
    private ProductDescriptionRepository productDescriptionRepository;
    @Resource
    private BaseLogAssist baseLogAssist;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDescriptionTemplate(DescriptionTemplateBo descriptionTemplateBo) {
        boolean editFlag = descriptionTemplateBo.getId() != null && descriptionTemplateBo.getId() > 0;

        DescriptionTemplateBo dbTemplate = null;
        ProductDescriptionTemplate saveTemplate = DescriptionTemplateConverter.convertToEntity(descriptionTemplateBo);

        if (editFlag) {
            dbTemplate = queryDescriptionTemplateDetail(descriptionTemplateBo.getId(), descriptionTemplateBo.getShopId());
            descriptionTemplateRepository.updateById(saveTemplate);
        } else {
            descriptionTemplateRepository.save(saveTemplate);
        }

        // 记录日志
        baseLogAssist.recordLog(ExaminModelEnum.PRODUCT,
                editFlag ? ExaProEnum.MODIFY : ExaProEnum.INSERT,
                editFlag ? "修改商品版式" : "新增商品版式",
                descriptionTemplateBo.getOperationUserId(),
                descriptionTemplateBo.getOperationShopId(),
                dbTemplate,
                DescriptionTemplateConverter.convertToBo(saveTemplate)
        );
    }

    @Override
    public BasePageResp<DescriptionTemplateBo> pageDescriptionTemplate(BasePageParam pageParam, DescriptionTemplateQueryBo queryBo) {
        Page<ProductDescriptionTemplate> pageResult = PageHelper.startPage(pageParam);
        descriptionTemplateRepository.list(commonDescriptionTemplateWrapperBuilder(queryBo));

        BasePageResp<DescriptionTemplateBo> result = PageResultHelper.transfer(pageResult, db -> DescriptionTemplateConverter.convertToBo(db));
        // 查询版式关联的商品数
        countRelateProduct(result.getData());
        return result;
    }

    @Override
    public DescriptionTemplateBo queryDescriptionTemplateDetail(Long id, Long shopId) {
        ProductDescriptionTemplate dbDescriptionTemplate = checkAndGetDescriptionTemplate(id, shopId);
        return DescriptionTemplateConverter.convertToBo(dbDescriptionTemplate);
    }

    @Override
    public void deleteDescriptionTemplate(Long id, Long shopId) {
        checkAndGetDescriptionTemplate(id, shopId);

        ProductDescriptionTemplate updDescriptionTemplate = new ProductDescriptionTemplate();
        updDescriptionTemplate.setId(id);
        updDescriptionTemplate.setWhetherDelete(Boolean.TRUE);
        descriptionTemplateRepository.updateById(updDescriptionTemplate);
    }

    @Override
    public List<DescriptionTemplateBo> listDescriptionTemplate(DescriptionTemplateQueryBo queryBo) {
        List<ProductDescriptionTemplate> templateList = descriptionTemplateRepository.list(commonDescriptionTemplateWrapperBuilder(queryBo));
        List<DescriptionTemplateBo> boList = new ArrayList<>();
        templateList.forEach(template -> {
            boList.add(DescriptionTemplateConverter.convertToBo(template));
        });
        // 查询版式关联的商品数
         countRelateProduct(boList);
        // relateGoodsCount
        return boList;
    }

    /**
     * 检查并获取版式
     *
     * @param id     版式id
     * @param shopId 店铺Id
     * @return 版式
     */
    private ProductDescriptionTemplate checkAndGetDescriptionTemplate(Long id, Long shopId) {
        ProductDescriptionTemplate dbDescriptionTemplate = descriptionTemplateRepository.getById(id);
        AssertUtil.throwIfTrue(dbDescriptionTemplate == null || dbDescriptionTemplate.getWhetherDelete(), "版式不存在");
        AssertUtil.throwIfTrue(!dbDescriptionTemplate.getShopId().equals(shopId), "无权操作");
        return dbDescriptionTemplate;
    }

    /**
     * 构建版式筛选条件
     *
     * @param queryBo 筛选参数
     * @return 筛选条件
     */
    private LambdaQueryWrapper<ProductDescriptionTemplate> commonDescriptionTemplateWrapperBuilder(DescriptionTemplateQueryBo queryBo) {
        LambdaQueryWrapper<ProductDescriptionTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(!StringUtils.isEmpty(queryBo.getName()), ProductDescriptionTemplate::getName, queryBo.getName());
        if (queryBo.getPosition() != null) {
            wrapper.eq(ProductDescriptionTemplate::getPosition, queryBo.getPosition().getCode());
        }
        wrapper.eq(queryBo.getShopId() != null, ProductDescriptionTemplate::getShopId, queryBo.getShopId());
        wrapper.eq(ProductDescriptionTemplate::getWhetherDelete, Boolean.FALSE);
        wrapper.orderByDesc(ProductDescriptionTemplate::getId);
        return wrapper;
    }

    /**
     * 计算关联商品数
     *
     * @param templateList 版式列表
     */
    private void countRelateProduct(List<DescriptionTemplateBo> templateList) {
        if (CollectionUtils.isEmpty(templateList)) {
            return;
        }

        // 根据position 分组并且提取出来id
        List<Long> templateIds = templateList.stream().map(DescriptionTemplateBo::getId).collect(Collectors.toList());
        Map<Long, Long> relateProductMap = productDescriptionRepository.getRelateProductMap(templateIds);

        templateList.forEach(template -> {
            Long relateProductCount = relateProductMap.getOrDefault(template.getId(), 0l);
            template.setRelateGoodsCount(relateProductCount);
        });
    }
}
