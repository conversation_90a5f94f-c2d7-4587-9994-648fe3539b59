package com.sankuai.shangou.seashop.promotion.core.task;//package com.sankuai.shangou.seashop.promotion.core.task;
//
//import com.cip.crane.client.spring.annotation.Crane;
//import com.cip.crane.client.spring.annotation.CraneConfiguration;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * @author: lhx
// * @date: 2024/2/23/023
// * @description:
// */
//@Slf4j
//@CraneConfiguration
//public class PromotionCraneTask {
//
//    /**
//     * 营销活动状态查询和处理
//     */
//    @Crane("PromotionStatusUpdateTask")
//    public void promotionStatusUpdateTask() {
//        log.info("PromotionStatusUpdateTask start");
//
//        log.info("PromotionStatusUpdateTask end");
//    }
//}
