package com.sankuai.shangou.seashop.product.core.service.assist.listener.config;

import java.util.concurrent.Executor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import com.sankuai.shangou.seashop.product.core.service.assist.ThreadPoolUtil;

/**
 * <AUTHOR>
 * @date 2024/04/26 8:57
 */
@EnableAsync
@Configuration
public class ThreadPoolConfig {

    @Bean
    public Executor commonExecutor() {
        return ThreadPoolUtil.COMMON_THREAD_POOL;
    }
}
