package com.sankuai.shangou.seashop.product.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.CategoryService;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.CategoryQueryBo;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.*;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryTreeResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:22
 */
@RestController
@RequestMapping("/category")
public class CategoryQueryController implements CategoryQueryFeign {

    @Resource
    private CategoryService categoryService;


    @PostMapping(value = "/queryCategoryTree", consumes = "application/json")
    @Override
    public ResultDto<CategoryTreeResp> queryCategoryTree(@RequestBody QueryCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTree", request, req -> {

            List<CategoryBo> categoryList = categoryService.queryCategoryTree(JsonUtil.copy(req, CategoryQueryBo.class));
            return CategoryTreeResp.builder().result(JsonUtil.toJsonString(categoryList)).build();
        });
    }

    @PostMapping(value = "/queryCategoryTreeHideNoThreeLevel", consumes = "application/json")
    @Override
    public ResultDto<CategoryTreeResp> queryCategoryTreeHideNoThreeLevel(@RequestBody QueryCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTree", request, req -> {

            List<CategoryBo> categoryList = categoryService.queryCategoryTreeHideNoThreeLevel(JsonUtil.copy(req, CategoryQueryBo.class));
            return CategoryTreeResp.builder().result(JsonUtil.toJsonString(categoryList)).build();
        });
    }

    @PostMapping(value = "/queryCategoryTreeForApply", consumes = "application/json")
    @Override
    public ResultDto<CategoryTreeResp> queryCategoryTreeForApply(@RequestBody QueryCategoryForApplyReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTreeForApply", request, req -> {

            List<CategoryBo> categoryList = categoryService.queryCategoryTreeForApply(JsonUtil.copy(req, CategoryQueryBo.class), req.getShopId());
            return CategoryTreeResp.builder().result(JsonUtil.toJsonString(categoryList)).build();
        });
    }

    @PostMapping(value = "/queryCategoryList", consumes = "application/json")
    @Override
    public ResultDto<CategoryListResp> queryCategoryList(@RequestBody QueryCategoryListReq queryCategoryListReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryList", queryCategoryListReq, req -> {

            List<CategoryBo> categoryList = categoryService.queryCategoryList(JsonUtil.copy(req, CategoryQueryBo.class));
            return CategoryListResp.builder().categoryRespList(JsonUtil.copyList(categoryList, CategoryResp.class)).build();
        });
    }

    @PostMapping(value = "/queryLastCategoryList", consumes = "application/json")
    @Override
    public ResultDto<CategoryListResp> queryLastCategoryList(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryLastCategoryList", request, req -> {
            req.checkParameter();

            List<CategoryBo> categoryList = categoryService.queryLastCategoryList(req.getId());
            return CategoryListResp.builder().categoryRespList(JsonUtil.copyList(categoryList, CategoryResp.class)).build();
        });
    }

    @PostMapping(value = "/queryFirstCategoryList", consumes = "application/json")
    @Override
    public ResultDto<CategoryListResp> queryFirstCategoryList(@RequestBody QueryFirstCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryFirstCategoryList", request, req -> {
            req.checkParameter();

            List<CategoryBo> categoryList = categoryService.queryFirstCategoryList(req.getCategoryIds());
            return CategoryListResp.builder().categoryRespList(JsonUtil.copyList(categoryList, CategoryResp.class)).build();
        });
    }

    @PostMapping(value = "/queryCategoryTreeWithParent", consumes = "application/json")
    @Override
    public ResultDto<CategoryTreeResp> queryCategoryTreeWithParent(@RequestBody QueryCategoryTreeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCategoryTreeWithParent", request, req -> {

            List<CategoryBo> categoryList = categoryService.queryCategoryTreeWithParent(JsonUtil.copy(req, CategoryQueryBo.class));
            return CategoryTreeResp.builder().result(JsonUtil.toJsonString(categoryList)).build();
        });
    }

    @PostMapping(value = "/getAllCategoryPath", consumes = "application/json")
    @Override
    public ResultDto<CategoryListResp> getAllCategoryPath(@RequestBody QueryCategoryTreeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getAllCategoryPath", request, req -> {
            List<CategoryResp> categoryList = categoryService.getAllCategoryPath(req.getIds());
            return CategoryListResp.builder().categoryRespList(JsonUtil.copyList(categoryList, CategoryResp.class)).build();
        });
    }

    @PostMapping(value = "/getCategoryList", consumes = "application/json")
    @Override
    public ResultDto<CategoryListResp> getCategoryList(@RequestBody QueryCategoryByIdsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getCategoryCache", request, req -> {
            req.checkParameter();

            List<CategoryBo> categoryList = categoryService.getCategoryList(req);
            return CategoryListResp.builder().categoryRespList(JsonUtil.copyList(categoryList, CategoryResp.class)).build();
        });
    }

    @Override
    public ResultDto<List<Long>> queryAlreadyRemoveCategoryIds() {
        return ThriftResponseHelper.responseInvoke("queryAlreadyRemoveCategoryIds", null, req -> categoryService.queryAlreadyRemoveCategoryIds());
    }
}
