package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductBasicReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductSkuMergeDto;
import com.sankuai.shangou.seashop.promotion.common.constant.LockConstant;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.PromotionResultCodeEnum;
import com.sankuai.shangou.seashop.promotion.common.remote.ShopRemoteService2;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleConfigService;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleService;
import com.sankuai.shangou.seashop.promotion.core.service.assist.ProductAssistForPromotion;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.PromotionLogAssist;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.bo.FlashSaleAuditLogBo;
import com.sankuai.shangou.seashop.promotion.core.service.assist.sort.SortFieldFactory;
import com.sankuai.shangou.seashop.promotion.core.service.assist.sort.enums.SaleFlashSortEnum;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.*;
import com.sankuai.shangou.seashop.promotion.dao.core.model.*;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.FlashSaleDetailDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.FlashSaleLimitTypeEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.FlashSaleStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.*;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@Service
@Slf4j
public class FlashSaleServiceImpl implements FlashSaleService {

    @Resource
    @Lazy
    private FlashSaleConfigService flashSaleConfigService;
    @Resource
    private FlashSaleCategoryRepository flashSaleCategoryRepository;
    @Resource
    private FlashSaleRepository flashSaleRepository;
    @Resource
    private FlashSaleDetailRepository flashSaleDetailRepository;
    @Value("${flash.sale.url}")
    private String flashSaleUrl;
    @Resource
    private ShopRemoteService2 shopRemoteService2;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private FlashSaleConsumeRecordRepository flashSaleConsumeRecordRepository;
    @Resource
    private FlashSaleStockReturnRepository flashSaleStockReturnRepository;
    @Resource
    private CollocationRepository collocationRepository;
    @Resource
    private PromotionLogAssist promotionLogAssist;
    @Resource
    private ProductAssistForPromotion productAssistForPromotion;

    /**
     * 新增限时购活动
     *
     * @param request
     */
    @Override
//    @ExaminProcess(processModel = ExaminModelEnum.PROMOTION, processType = ExaProEnum.INSERT, serviceMethod = "", dto = FlashSaleAddReq.class, entity = FlashSale.class)
    public void add(FlashSaleAddReq request) {

        // 查询限时购分类是否存在
        Long categoryId = request.getCategoryId();
        FlashSaleCategory flashSaleCategory = flashSaleCategoryRepository.getById(categoryId);
        if (null == flashSaleCategory || flashSaleCategory.getDelFlag().equals(Boolean.TRUE)) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_CATEGORY_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_CATEGORY_NOT_EXIST.getMsg());
        }

        // 判断商品是否已经有参加限时购活动（任何审批中、进行中、未开始的）
        List<FlashSale> flashSales = flashSaleRepository.listByProductIdAndTime(
                FlashSaleParamDto.builder()
                        .productId(request.getProductId())
//                        .beginDate(request.getBeginDate())
//                        .endDate(request.getEndDate())
                        // 待审核和进行中的活动，在过滤范围内
                        .statusList(Arrays.asList(FlashSaleStatusEnum.WAIT_FOR_AUDITING.getStatus(), FlashSaleStatusEnum.ONGOING.getStatus())).build()
        );
        if (CollUtil.isNotEmpty(flashSales)) {
            log.info("商品在时间范围内已经有活动,productId:{},flashSales:{}", request.getProductId(), flashSales);
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_ACTIVE.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_ACTIVE.getMsg());
        }

        // 判断商品是否参加了组合购
        List<String> productIds = new ArrayList<>();
        productIds.add(request.getProductId() + "");
        List<CollocationActivityRespDto> collocationActivityRespModels = collocationRepository.queryCollocationByProductIdsAndStatus(productIds, 1);
        if (CollUtil.isNotEmpty(collocationActivityRespModels)) {
            // 只判断主商品是否参加了组合购
            collocationActivityRespModels = collocationActivityRespModels.stream().filter(c -> c.getMainFlag()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collocationActivityRespModels)) {
                log.info("商品已经参加组合购活动,productId:{},collocationActivityRespModels:{}", request.getProductId(), collocationActivityRespModels);
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_COLLOCATION.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_COLLOCATION.getMsg());
            }
        }

        // 判断是商品是否开启了阶梯价
        QueryProductBasicReq queryProductBasicReq = new QueryProductBasicReq();
        queryProductBasicReq.setProductIds(Arrays.asList(request.getProductId()));
        List<ProductBasicDto> productBasicDtos = productRemoteService.queryProductBase(queryProductBasicReq);
        if (CollUtil.isNotEmpty(productBasicDtos)) {
            ProductBasicDto productBasicDto = productBasicDtos.get(0);
            if (productBasicDto.getWhetherOpenLadder()) {
                log.info("商品已经开启了阶梯价,productId:{},productBasicDtos:{}", request.getProductId(), productBasicDtos);
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_LADDER_PRICE.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_LADDER_PRICE.getMsg());
            }
        }

        // 查询是否需要审核
        PlatFlashSaleConfigResp platConfig = flashSaleConfigService.getPlatConfig();
        Boolean needAuditFlag = platConfig.getNeedAuditFlag();

        FlashSale flashSale = JsonUtil.copy(request, FlashSale.class);
        if (needAuditFlag) {
            flashSale.setStatus(FlashSaleStatusEnum.WAIT_FOR_AUDITING.getStatus());
        }
        else {
            FlashSaleStatusEnum statusEnum = FlashSaleStatusEnum.fromStatusByTime(flashSale.getBeginDate(),flashSale.getEndDate());
            flashSale.setStatus(statusEnum.getStatus());
        }
        flashSale.setCategoryName(flashSaleCategory.getCategoryName());
        // urlPath 拼装规则需要确定(改lion即可)
        flashSale.setUrlPath(flashSaleUrl + request.getProductId());

        List<FlashSaleDetailDto> detailList = request.getDetailList();

        // 获取最低价
        BigDecimal minPrice = detailList.stream().map(FlashSaleDetailDto::getPrice).min(BigDecimal::compareTo).get();
        flashSale.setMinPrice(minPrice);

        // 如果是sku限购，限购数量为明细限购数量的总和
        if (request.getLimitType().equals(FlashSaleLimitTypeEnum.SKU.getType())) {
            Integer limitCount = detailList.stream().mapToInt(FlashSaleDetailDto::getLimitCount).sum();
            flashSale.setLimitCount(limitCount);
        }

        // 校验库存
        List<String> skuList = detailList.stream().map(FlashSaleDetailDto::getSkuId).collect(Collectors.toList());
        productAssistForPromotion.checkSkuStock(skuList);

        TransactionHelper.doInTransaction(() -> {
            // 保存活动
            flashSaleRepository.save(flashSale);

            List<FlashSaleDetail> flashSaleDetails = JsonUtil.copyList(detailList, FlashSaleDetail.class);
            flashSaleDetails.parallelStream().forEach(flashSaleDetail -> {
                flashSaleDetail.setFlashSaleId(flashSale.getId());
                flashSaleDetail.setProductId(request.getProductId());
            });

            flashSaleDetailRepository.saveBatch(flashSaleDetails);

            // 记录操作日志
            BaseIdReq req = new BaseIdReq();
            req.setId(flashSale.getId());
            promotionLogAssist.recordFlashSaleLog(request.getOperationUserId(), request.getOperationShopId(), null, getById(req));
        });
    }

    /**
     * 未开始、进行中：可编辑活动标题、活动商品的价格、活动商品的库存、活动分类、购买限制、结束时间
     * 待审核：可以全部修改
     *
     * @param request
     */
    @Override
//    @ExaminProcess(processModel = ExaminModelEnum.PROMOTION, processType = ExaProEnum.MODIFY, serviceMethod = "", dto = FlashSaleUpdateReq.class, entity = FlashSale.class)
    public void update(FlashSaleUpdateReq request) {

        FlashSale flashSale = flashSaleRepository.getById(request.getId());
        if (null == flashSale) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
        }
        Integer status = flashSale.getStatus();
        // 可以进行修改的状态
        List<Integer> updateStatus = Arrays.asList(
                FlashSaleStatusEnum.WAIT_FOR_AUDITING.getStatus(),
                FlashSaleStatusEnum.ONGOING.getStatus(),
                FlashSaleStatusEnum.NOT_BEGIN.getStatus()
        );
        if (!updateStatus.contains(status)) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_STATUS_NOT_ALLOW_UPDATE.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_STATUS_NOT_ALLOW_UPDATE.getMsg());
        }

        // 如果是进行中的活动，不能修改商品、开始时间
        if (FlashSaleStatusEnum.ONGOING.getStatus().equals(status)) {
            if (!flashSale.getProductId().equals(request.getProductId())) {
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_NOT_ALLOW_UPDATE.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_PRODUCT_NOT_ALLOW_UPDATE.getMsg());
            }
            if (!flashSale.getBeginDate().equals(request.getBeginDate())) {
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_BEGIN_DATE_NOT_ALLOW_UPDATE.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_BEGIN_DATE_NOT_ALLOW_UPDATE.getMsg());
            }
        }

        // 查询限时购分类是否存在
        Long categoryId = request.getCategoryId();
        FlashSaleCategory flashSaleCategory = flashSaleCategoryRepository.getById(categoryId);
        if (null == flashSaleCategory || flashSaleCategory.getDelFlag().equals(Boolean.TRUE)) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_CATEGORY_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_CATEGORY_NOT_EXIST.getMsg());
        }

        // 查询是否需要审核
        PlatFlashSaleConfigResp platConfig = flashSaleConfigService.getPlatConfig();
        Boolean needAuditFlag = platConfig.getNeedAuditFlag();
        if (needAuditFlag) {
            if (status.equals(FlashSaleStatusEnum.ONGOING.getStatus())) {
                flashSale.setStatus(FlashSaleStatusEnum.WAIT_FOR_AUDITING.getStatus());
            }
        }
        else {
            FlashSaleStatusEnum statusEnum = FlashSaleStatusEnum.fromStatusByTime(request.getBeginDate(),request.getEndDate());
            flashSale.setStatus(statusEnum.getStatus());
        }

        // 判断商品是否参加了组合购
        List<String> productIds = new ArrayList<>();
        productIds.add(request.getProductId() + "");
        List<CollocationActivityRespDto> collocationActivityRespModels = collocationRepository.queryCollocationByProductIdsAndStatus(productIds, 1);
        if (CollUtil.isNotEmpty(collocationActivityRespModels)) {
            // 只判断主商品是否参加了组合购
            collocationActivityRespModels = collocationActivityRespModels.stream().filter(c -> c.getMainFlag()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collocationActivityRespModels)) {
                log.info("商品已经参加组合购活动,productId:{},collocationActivityRespModels:{}", request.getProductId(), collocationActivityRespModels);
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_COLLOCATION.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_COLLOCATION.getMsg());
            }
        }

        // 判断是商品是否开启了阶梯价
        QueryProductBasicReq queryProductBasicReq = new QueryProductBasicReq();
        queryProductBasicReq.setProductIds(Arrays.asList(request.getProductId()));
        List<ProductBasicDto> productBasicDtos = productRemoteService.queryProductBase(queryProductBasicReq);
        if (CollUtil.isNotEmpty(productBasicDtos)) {
            ProductBasicDto productBasicDto = productBasicDtos.get(0);
            if (productBasicDto.getWhetherOpenLadder()) {
                log.info("商品已经开启了阶梯价,productId:{},productBasicDtos:{}", request.getProductId(), productBasicDtos);
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_LADDER_PRICE.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_LADDER_PRICE.getMsg());
            }
        }

        // 判断商品是否已经有参加限时购活动（任何审批中、进行中、未开始的）
        List<FlashSale> flashSales = flashSaleRepository.listByProductIdAndTime(
                FlashSaleParamDto.builder()
                        .productId(flashSale.getProductId())
//                        .beginDate(flashSale.getBeginDate())
//                        .endDate(request.getEndDate())
                        // 排除当前活动
                        .excludeId(request.getId())
                        // 待审核和进行中的活动，在过滤范围内
                        .statusList(Arrays.asList(FlashSaleStatusEnum.WAIT_FOR_AUDITING.getStatus(), FlashSaleStatusEnum.ONGOING.getStatus())).build()
        );
        if (CollUtil.isNotEmpty(flashSales)) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_ACTIVE.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_PRODUCT_HAS_ACTIVE.getMsg());
        }

        // 查询老的活动明细
        List<FlashSaleDetail> flashSaleDetails = flashSaleDetailRepository.getByFlashSaleId(flashSale.getId());
        List<FlashSaleDetailDto> detailList = request.getDetailList();

        // 进行中的，不运行修改商品，所以进行数据比较，避免数据对不上
        List<String> oldDetails = flashSaleDetails.stream().map(FlashSaleDetail::getSkuId).collect(Collectors.toList());
        List<String> newDetails = detailList.stream().map(FlashSaleDetailDto::getSkuId).collect(Collectors.toList());
        if (status.equals(FlashSaleStatusEnum.ONGOING.getStatus()) && !oldDetails.containsAll(newDetails)) {
            log.info("限时购活动明细数据修改异常,oldDetails:{},newDetails:{}", oldDetails, newDetails);
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_DETAIL_UPDATE_ERROR.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_DETAIL_UPDATE_ERROR.getMsg());
        }

        // 新老数据，如果新的多，则需要新增，如果老的多，则需要删除
        int oldSize = flashSaleDetails.size();
        int newSize = detailList.size();
        int minSize = Math.min(oldSize, newSize);
        if (oldSize > newSize) {
            opDetail(request, flashSale, flashSaleDetails, detailList, minSize);
            for (int i = minSize; i < oldSize; i++) {
                FlashSaleDetail detail = flashSaleDetails.get(i);
                detail.setDelFlag(Boolean.TRUE);
            }
        }
        else {
            opDetail(request, flashSale, flashSaleDetails, detailList, oldSize);
            for (int i = minSize; i < newSize; i++) {
                FlashSaleDetail detail = JsonUtil.copy(detailList.get(i), FlashSaleDetail.class);
                detail.setFlashSaleId(flashSale.getId());
                detail.setProductId(request.getProductId());
                flashSaleDetails.add(detail);
            }
        }

        flashSale.setTitle(request.getTitle());
        flashSale.setLimitType(request.getLimitType());
        flashSale.setProductId(request.getProductId());
        flashSale.setLimitCount(request.getLimitCount());
        flashSale.setCategoryId(categoryId);
        flashSale.setCategoryName(flashSaleCategory.getCategoryName());
        flashSale.setBeginDate(request.getBeginDate());
        flashSale.setEndDate(request.getEndDate());

        // 获取最低价
        BigDecimal minPrice = detailList.stream().map(FlashSaleDetailDto::getPrice).min(BigDecimal::compareTo).get();
        flashSale.setMinPrice(minPrice);

        // 如果是sku限购，限购数量为明细限购数量的总和
        if (request.getLimitType().equals(FlashSaleLimitTypeEnum.SKU.getType())) {
            Integer limitCount = detailList.stream().mapToInt(FlashSaleDetailDto::getLimitCount).sum();
            flashSale.setLimitCount(limitCount);
        }

        TransactionHelper.doInTransaction(() -> {
            // 查询一下修改前的信息
            BaseIdReq req = new BaseIdReq();
            req.setId(flashSale.getId());
            FlashSaleResp oldFlashSale = getById(req);

            // 更新活动
            flashSaleRepository.updateById(flashSale);
            // 更新活动明细
            flashSaleDetailRepository.saveOrUpdateBatch(flashSaleDetails);

            // 记录操作日志
            promotionLogAssist.recordFlashSaleLog(request.getOperationUserId(), request.getOperationShopId(), oldFlashSale, getById(req));
        });
    }

    private static void opDetail(FlashSaleUpdateReq request, FlashSale flashSale, List<FlashSaleDetail> flashSaleDetails, List<FlashSaleDetailDto> detailList, int minSize) {
        for (int i = 0; i < minSize; i++) {
            FlashSaleDetail detail = flashSaleDetails.get(i);
            FlashSaleDetailDto detailDto = detailList.get(i);
            detail.setFlashSaleId(flashSale.getId());
            detail.setProductId(request.getProductId());
            detail.setSkuId(detailDto.getSkuId());
            detail.setPrice(detailDto.getPrice());
            detail.setTotalCount(detailDto.getTotalCount());
            detail.setLimitCount(detailDto.getLimitCount());
        }
    }

    @Override
    public void audit(FlashSaleAuditReq request) {

        FlashSale flashSale = flashSaleRepository.getById(request.getId());
        if (null == flashSale) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
        }

        // 只有待审核的活动才能进行审核
        Integer status = flashSale.getStatus();
        if (!status.equals(FlashSaleStatusEnum.WAIT_FOR_AUDITING.getStatus())) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_STATUS_NOT_ALLOW_AUDIT.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_STATUS_NOT_ALLOW_AUDIT.getMsg());
        }

        if (request.getPassFlag()) {
            // 审核通过
            flashSale.setStatus(FlashSaleStatusEnum.ONGOING.getStatus());
        }
        else {
            flashSale.setStatus(FlashSaleStatusEnum.AUDIT_FAILED.getStatus());
        }

        flashSaleRepository.updateById(flashSale);

        // 记录操作日志
        promotionLogAssist.recordAuditFlashSaleLog(request.getOperationUserId(),
                request.getOperationShopId(),
                JsonUtil.copy(request, FlashSaleAuditLogBo.class));

    }

    @Override
    public void show(FlashSaleShowReq request) {

        FlashSale flashSale = flashSaleRepository.getById(request.getId());
        if (null == flashSale) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
        }

        Date now = new Date();
        // 状态是进行中，或者结束时间大于当前时间，才能进行上下架操作
        if (!flashSale.getStatus().equals(FlashSaleStatusEnum.ONGOING.getStatus()) || now.after(flashSale.getEndDate())) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_STATUS_NOT_ALLOW_UPDATE.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_STATUS_NOT_ALLOW_UPDATE.getMsg());
        }

        flashSale.setFrontFlag(request.getFrontFlag());
        flashSaleRepository.updateById(flashSale);
    }

    @Override
    public void endActive(BaseIdReq request) {

        FlashSale flashSale = flashSaleRepository.getById(request.getId());
        if (null == flashSale) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
        }

        Date now = new Date();
        Date endTime = flashSale.getEndDate();
        if (endTime.before(now)) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_END.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_END.getMsg());
        }
        flashSale.setEndDate(now);
        flashSale.setStatus(FlashSaleStatusEnum.ENDED.getStatus());
        flashSaleRepository.updateById(flashSale);

    }

    @Override
    public BasePageResp<FlashSaleSimpleResp> pageList(FlashSaleQueryReq request) {
        // 处理开始时间和结束时间
        if (request.getStartTime() != null) {
            request.setStartTime(DateUtil.beginOfDay(request.getStartTime()));
        }
        if (request.getEndTime() != null) {
            request.setEndTime(DateUtil.endOfDay(request.getEndTime()));
        }

        List<ShopSimpleResp> shopSimpleRespList = new ArrayList<>();

        // 处理店铺名称模糊查询
        String shopName = request.getShopName();
        if (StrUtil.isNotBlank(shopName)) {
            ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                    ShopSimpleQueryReq.builder()
                            .shopName(shopName).build()
            );
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            shopSimpleRespList = shopSimpleListResp.getList();
            if (shopSimpleRespList.size() > PromotionConstant.MAX_LIKE_QUERY) {
                throw new BusinessException(PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getCode(),
                        PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getMsg());
            }
        }

        FlashSaleParamDto flashSaleParamModel = JsonUtil.copy(request, FlashSaleParamDto.class);

        if (CollUtil.isNotEmpty(shopSimpleRespList)) {
            // 拿到过滤后的店铺id
            List<Long> shopIds = shopSimpleRespList.stream().map(ShopSimpleResp::getId).collect(Collectors.toList());
            flashSaleParamModel.setShopIdList(shopIds);
        }

        List<ProductPageResp> productList = new ArrayList<>();

        // 处理传入了商品id的情况
        Long productId = request.getProductId();
        String productName = request.getProductName();
        if (null != productId) {
            QueryProductReq queryProductReq = new QueryProductReq();
            queryProductReq.setProductId(productId);
            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
            if (null == productPage || CollUtil.isEmpty(productPage.getData())) {
                // 说明商品id没有查到数据
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            ProductPageResp productPageResp = productPage.getData().get(0);
            if (StrUtil.isNotBlank(productName)) {
                String productPageName = productPageResp.getProductName();
                if (!productPageName.contains(productName)) {
                    // 说明商品ID和模糊查询的数据不匹配
                    return PageResultHelper.defaultEmpty(request.buildPage());
                }
            }
            productList.add(productPageResp);
        }

        // 处理商品名称模糊查询
        if (StrUtil.isNotBlank(productName)) {
            QueryProductReq queryProductReq = new QueryProductReq();
            queryProductReq.setProductName(productName);
            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
            if (null == productPage || CollUtil.isEmpty(productPage.getData())) {
                // 说明模糊没查到数据
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            if (productPage.getTotalCount() > PromotionConstant.MAX_LIKE_QUERY) {
                throw new BusinessException(PromotionResultCodeEnum.PRODUCT_NAME_LIKE_QUERY_TOO_MANY.getCode(),
                        PromotionResultCodeEnum.PRODUCT_NAME_LIKE_QUERY_TOO_MANY.getMsg());
            }
            productList = productPage.getData();
        }

        // 状态条件处理
        opStatus(request, flashSaleParamModel);

        if (CollUtil.isNotEmpty(productList)) {
            flashSaleParamModel.setProductIdList(productList.stream().map(ProductPageResp::getProductId).collect(Collectors.toList()));
        }

        SortFieldFactory.dealSortField(SaleFlashSortEnum.class, request.getSortList());
        Page<FlashSale> flashSalePage = flashSaleRepository.pageList(request.buildPage(), flashSaleParamModel, request.getSortList());
        if (null == flashSalePage || CollUtil.isEmpty(flashSalePage.getResult())) {
            return PageResultHelper.defaultEmpty(request);
        }

        List<FlashSale> flashSaleList = flashSalePage.getResult();
        if (CollUtil.isEmpty(shopSimpleRespList)) {
            // 没数据说明之前没查过店铺信息，需要去查一下店铺信息
            List<Long> shopIds = flashSaleList.stream().map(FlashSale::getShopId).distinct().collect(Collectors.toList());
            ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                    ShopSimpleQueryReq.builder()
                            .shopIdList(shopIds).build()
            );
            shopSimpleRespList = shopSimpleListResp.getList();
        }
        final List<ShopSimpleResp> shopSimpleModelsFinal = shopSimpleRespList;

        //  处理商品名称
        productList = opProductName(productList, flashSaleList);

        final List<ProductPageResp> productListFinal = productList;

        return PageResultHelper.transfer(flashSalePage, FlashSaleSimpleResp.class, (flashSale, flashSaleSimple) -> {
            opFlashSaleReturnModel(shopSimpleModelsFinal, productListFinal, flashSale, flashSaleSimple);
        });
    }

    @Nullable
    private List<ProductPageResp> opProductName(List<ProductPageResp> productList, List<FlashSale> flashSaleList) {
        if (CollUtil.isEmpty(productList)) {
            /*// 说明不是过滤商品名称查询的，需要去查一下商品信息
            List<Long> productIdList = flashSaleList.stream().map(FlashSale::getProductId).distinct().collect(Collectors.toList());
            QueryProductReq queryProductReq = new QueryProductReq();
            queryProductReq.setProductIds(productIdList);
            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
            if (null != productPage && CollUtil.isNotEmpty(productPage.getData())) {
                productList = productPage.getData();
            }*/
            List<Long> productIdList = flashSaleList.stream().map(FlashSale::getProductId).distinct().collect(Collectors.toList());
            List<ProductBasicDto> productBasicList = productRemoteService.queryProductBase(productIdList);
            productList = JsonUtil.copyList(productBasicList, ProductPageResp.class);
        }
        return productList;
    }

    private static void opFlashSaleReturnModel(List<ShopSimpleResp> shopSimpleModelsFinal, List<ProductPageResp> productListFinal, FlashSale flashSale, FlashSaleSimpleResp flashSaleSimple) {
        if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
            shopSimpleModelsFinal.stream().filter(s -> s.getId().equals(flashSaleSimple.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                flashSaleSimple.setShopName(shopSimpleResp.getShopName());
            });
        }

        if (CollUtil.isNotEmpty(productListFinal)) {
            productListFinal.stream().filter(p -> p.getProductId().equals(flashSaleSimple.getProductId())).findFirst().ifPresent(productPageResp -> {
                flashSaleSimple.setProductName(productPageResp.getProductName());
            });
        }

        // 状态处理
        Integer saleStatus = flashSale.getStatus();
        if (saleStatus.equals(FlashSaleStatusEnum.ONGOING.getStatus())) {
            Date now = new Date();
            if (now.before(flashSale.getBeginDate())) {
                flashSaleSimple.setStatus(FlashSaleStatusEnum.NOT_BEGIN.getStatus());
            }
            else if (now.after(flashSale.getEndDate())) {
                flashSaleSimple.setStatus(FlashSaleStatusEnum.ENDED.getStatus());
            }
            else {
                flashSaleSimple.setStatus(FlashSaleStatusEnum.ONGOING.getStatus());
            }
        }
        else {
            flashSaleSimple.setStatus(saleStatus);
        }
        flashSaleSimple.setStatusName(FlashSaleStatusEnum.getDescByStatus(flashSaleSimple.getStatus()));
    }

    private static void opStatus(FlashSaleQueryReq request, FlashSaleParamDto flashSaleParamModel) {
        Integer status = request.getStatus();
        if (status != null) {
            Date now = new Date();
            // PS:这个地方的时间进行了特殊处理
            if (status.equals(FlashSaleStatusEnum.ONGOING.getStatus())) {
                flashSaleParamModel.setBeginDate(now);
                flashSaleParamModel.setEndDate(now);
            }
            else if (status.equals(FlashSaleStatusEnum.ENDED.getStatus())) {
                flashSaleParamModel.setStatus(null);
                flashSaleParamModel.setStatusList(Arrays.asList(FlashSaleStatusEnum.ONGOING.getStatus(), FlashSaleStatusEnum.ENDED.getStatus()));
                flashSaleParamModel.setEndDate(now);
            }
            else if (status.equals(FlashSaleStatusEnum.NOT_BEGIN.getStatus())) {
                flashSaleParamModel.setStatus(null);
                flashSaleParamModel.setStatusList(Arrays.asList(FlashSaleStatusEnum.ONGOING.getStatus(), FlashSaleStatusEnum.NOT_BEGIN.getStatus()));
                flashSaleParamModel.setBeginDate(now);
            }
        }
    }

    @Override
    public FlashSaleResp getById(BaseIdReq request) {

        FlashSale flashSale = flashSaleRepository.getById(request.getId());
        if (null == flashSale) {
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
        }

        List<FlashSaleDetail> flashSaleDetails = flashSaleDetailRepository.getByFlashSaleId(flashSale.getId());

        List<FlashSaleDetailResp> detailList = JsonUtil.copyList(flashSaleDetails, FlashSaleDetailResp.class);

        String productName = "";

        //sku相关信息查询：规格名称、原价格、库存、主图地址
        if (CollUtil.isNotEmpty(detailList)) {
            List<String> skuIdList = detailList.stream().map(FlashSaleDetailResp::getSkuId).collect(Collectors.toList());

            List<ProductSkuMergeDto> productSkuList = productRemoteService.getProductSkuMergeList(skuIdList);
            if (CollUtil.isNotEmpty(productSkuList)) {
                for (ProductSkuMergeDto productSkuMergeDto : productSkuList) {
                    if (StrUtil.isBlank(productName)) {
                        productName = productSkuMergeDto.getProductName();
                    }
                    else {
                        break;
                    }
                }
            }

            Map<String, ProductSkuMergeDto> skuMap = productSkuList.stream().collect(Collectors.toMap(ProductSkuMergeDto::getSkuId, Function.identity(), (k1, k2) -> k1));
            detailList.forEach(detail -> {
                ProductSkuMergeDto sku = skuMap.get(detail.getSkuId());
                if (sku == null) {
                    throw new BusinessException("查询限时购详情失败, 商品规格已经发生变动");
                }

                detail.setSkuName(
                        Optional.ofNullable(sku.getSpec1Value()).orElse("")
                                + Optional.ofNullable(sku.getSpec2Value()).orElse("")
                                + Optional.ofNullable(sku.getSpec3Value()).orElse("")
                );
                detail.setSpec1Alias(Optional.ofNullable(sku.getSpec1Value()).orElse(""));
                detail.setSpec2Alias(Optional.ofNullable(sku.getSpec2Value()).orElse(""));
                detail.setSpec3Alias(Optional.ofNullable(sku.getSpec3Value()).orElse(""));
                detail.setSalePrice(sku.getSalePrice());
                detail.setStock(sku.getStock());
                detail.setImagePath(sku.getImagePath());
            });
        }

        FlashSaleResp flashSaleResp = JsonUtil.copy(flashSale, FlashSaleResp.class);
        flashSaleResp.setProductName(productName);
        flashSaleResp.setDetailList(detailList);

        return flashSaleResp;
    }

    /**
     * 商城页面限时购活动列表查询
     * <p>
     * 说明：主要是查询当前时间段内的限时购活动
     * 1.查询当前时间段内的限时购活动
     * 2.需要考虑预热的情况
     *
     * @param request
     * @return
     */
    @Override
    public BasePageResp<MallFlashSaleResp> mallPageList(MallFlashSaleQueryReq request) {

        List<ProductPageResp> productList = new ArrayList<>();
        String productName = request.getProductName();
        // 处理商品名称模糊查询
        if (StrUtil.isNotBlank(productName)) {
            QueryProductReq queryProductReq = new QueryProductReq();
            queryProductReq.setProductName(productName);
            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
            if (null == productPage || CollUtil.isEmpty(productPage.getData())) {
                // 说明模糊没查到数据
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            if (productPage.getTotalCount() > PromotionConstant.MAX_LIKE_QUERY) {
                throw new BusinessException(PromotionResultCodeEnum.PRODUCT_NAME_LIKE_QUERY_TOO_MANY.getCode(),
                        PromotionResultCodeEnum.PRODUCT_NAME_LIKE_QUERY_TOO_MANY.getMsg());
            }
            productList = productPage.getData();
        }

        MallFlashSaleParamDto paramModel = JsonUtil.copy(request, MallFlashSaleParamDto.class);
        if (CollUtil.isNotEmpty(productList)) {
            paramModel.setProductIds(productList.stream().map(ProductPageResp::getProductId).collect(Collectors.toList()));
        }

        Page<MallFlashSaleDto> mallFlashSalePage = flashSaleRepository.mallPageList(request.buildPage(), paramModel);
        if (null == mallFlashSalePage || CollUtil.isEmpty(mallFlashSalePage.getResult())) {
            return PageResultHelper.defaultEmpty(request.buildPage());
        }

        List<MallFlashSaleDto> flashSaleModelList = mallFlashSalePage.getResult();

        //  处理商品名称
        if (CollUtil.isEmpty(productList)) {
            // 说明不是过滤商品名称查询的，需要去查一下商品信息
            List<Long> productIdList = flashSaleModelList.stream().map(MallFlashSaleDto::getProductId).distinct().collect(Collectors.toList());
            QueryProductReq queryProductReq = new QueryProductReq();
            queryProductReq.setProductIds(productIdList);
            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
            if (null != productPage && CollUtil.isNotEmpty(productPage.getData())) {
                productList = productPage.getData();
            }
        }
        final List<ProductPageResp> productListFinal = productList;
        final Date now = new Date();

        return PageResultHelper.transfer(mallFlashSalePage, MallFlashSaleResp.class, flashSaleSimple -> {
            if (CollUtil.isNotEmpty(productListFinal)) {
                productListFinal.stream().filter(p -> p.getProductId().equals(flashSaleSimple.getProductId())).findFirst().ifPresent(productPageResp -> {
                    flashSaleSimple.setProductName(productPageResp.getProductName());
                    flashSaleSimple.setImagePath(productPageResp.getImagePath());
                    flashSaleSimple.setSalePrice(productPageResp.getMinSalePrice());
                });
            }

            Date beginDate = flashSaleSimple.getBeginDate();
            if (now.before(beginDate)) {
                flashSaleSimple.setStartRemainingTime(DateUtil.between(now, beginDate, DateUnit.SECOND));
            }
        });
    }

    @Override
    public MallAppletFlashSaleListResp mallAppletPageList(MallAppletFlashSaleQueryReq request) {
        MallAppletFlashSaleListResp resp = new MallAppletFlashSaleListResp();
        // 第一步，根据前端传过来的限时购商品ID集合查限时购活动信息
        List<Long> productIdList = null;
        if (CollUtil.isNotEmpty(request.getProductIds())) {
            productIdList = request.getProductIds().stream().map(Long::valueOf).collect(Collectors.toList());
        }
        List<Long> flashSaleIds = request.getFlashSaleIds();

        FlashSaleParamDto paramDto = new FlashSaleParamDto();
        paramDto.setProductIdList(productIdList);
        paramDto.setStatusList(Arrays.asList(FlashSaleStatusEnum.ONGOING.getStatus(), FlashSaleStatusEnum.NOT_BEGIN.getStatus()));
        paramDto.setFlashSaleIds(flashSaleIds);
        BasePageParam page = new BasePageParam();
        page.setPageNum(PromotionConstant.DEFAULT_PAGE);
        page.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
        Page<FlashSale> flashSalePage = flashSaleRepository.pageList(page, paramDto, null);
        List<FlashSale> flashSaleList = flashSalePage.getResult();
        if (CollectionUtils.isEmpty(flashSaleList)) {
            log.info("限时购活动列表为空");
            return resp;
        }
        // 转成map
        Map<Long, FlashSale> flashSaleMap = flashSaleList.stream().collect(Collectors.toMap(FlashSale::getProductId, Function.identity(), (k1, k2) -> k1));
        // 第二步，根据前端传过来的限时购商品ID集合查商品信息
        QueryProductReq queryProductReq = new QueryProductReq();
        queryProductReq.setProductIds(flashSaleMap.keySet().stream().collect(Collectors.toList()));
        queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
        queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
        BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
        List<ProductPageResp> productPageResps = productPage.getData();
        if (CollectionUtils.isEmpty(productPageResps)) {
            log.info("限时购商品列表为空");
            return resp;
        }
        Map<Long, ProductPageResp> productMap = productPageResps.stream().collect(Collectors.toMap(ProductPageResp::getProductId, Function.identity(), (k1, k2) -> k1));
        // 第三步，组装返回结果
        List<MallAppletFlashSaleResp> result = new ArrayList<>();
        Date now = new Date();
        // 循环 productMap
        for (Map.Entry<Long, ProductPageResp> entry : productMap.entrySet()) {
            Long productId = entry.getKey();
            ProductPageResp productPageResp = entry.getValue();
            FlashSale flashSale = flashSaleMap.get(productId);
            if (Objects.isNull(flashSale)) {
                continue;
            }
            MallAppletFlashSaleResp mallAppletFlashSaleResp = JsonUtil.copy(productPageResp, MallAppletFlashSaleResp.class);
            mallAppletFlashSaleResp.setProductId(String.valueOf(productId));
            mallAppletFlashSaleResp.setBeginDate(flashSale.getBeginDate());
            mallAppletFlashSaleResp.setEndDate(flashSale.getEndDate());
            mallAppletFlashSaleResp.setLimitType(flashSale.getLimitType());
            mallAppletFlashSaleResp.setLimitCount(flashSale.getLimitCount());
            mallAppletFlashSaleResp.setSaleCount(flashSale.getSaleCount());
            mallAppletFlashSaleResp.setCategoryId(flashSale.getCategoryId());
            mallAppletFlashSaleResp.setUrlPath(flashSale.getUrlPath());
            mallAppletFlashSaleResp.setMinPrice(flashSale.getMinPrice());
            mallAppletFlashSaleResp.setFlashSaleId(flashSale.getId());

            // 处理时间
            Date beginDate = flashSale.getBeginDate();
            if (now.before(beginDate)) {
                mallAppletFlashSaleResp.setRemStartTime(DateUtil.between(now, beginDate, DateUnit.SECOND));
            }
            Date endDate = flashSale.getEndDate();
            if (now.before(endDate)) {
                mallAppletFlashSaleResp.setRemEndTime(DateUtil.between(now, endDate, DateUnit.SECOND));
            }
            result.add(mallAppletFlashSaleResp);
        }
        resp.setRespList(result);
        return resp;
    }

    @Override
    public BasePageResp<VisualFlashSaleResp> componentPageList(VisualFlashSaleQueryReq request) {

        List<ProductPageResp> productList = new ArrayList<>();
        String shopName = request.getShopName();
        Long shopId = request.getShopId();
        if (StrUtil.isNotBlank(request.getCategoryPath()) || StrUtil.isNotBlank(request.getProductName())
                || (null != request.getShopCategoryId() && request.getShopCategoryId() > 0)
                || CollUtil.isNotEmpty(request.getCategoryIds())
                || CollUtil.isNotEmpty(request.getProductIds())) {
            // 一旦出现这两个条件，需要先去商品那边进行查询处理
            QueryProductReq queryProductReq = new QueryProductReq();
            queryProductReq.setShopId(shopId);
            queryProductReq.setShopName(shopName);
            queryProductReq.setCategoryPath(request.getCategoryPath());
            queryProductReq.setProductName(request.getProductName());
            queryProductReq.setShopCategoryId(request.getShopCategoryId());
            queryProductReq.setCategoryIds(request.getCategoryIds());
            queryProductReq.setProductIds(request.getProductIds());
            // 默认从第一页开始查
            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
            // 只查询销售中 并且有库存的
            queryProductReq.setStatus(ProductStatusEnum.ON_SALE);
            queryProductReq.setHasStock(Boolean.TRUE);
            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
            if (null == productPage || CollUtil.isEmpty(productPage.getData())) {
                // 说明模糊没查到数据
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            // 总列表超过1000，不允许查询
            if (productPage.getTotalCount() > PromotionConstant.MAX_LIST_SIZE) {
                throw new BusinessException(PromotionResultCodeEnum.PRODUCT_NAME_LIKE_QUERY_TOO_MANY.getCode(),
                        PromotionResultCodeEnum.PRODUCT_NAME_LIKE_QUERY_TOO_MANY.getMsg());
            }
            productList.addAll(productPage.getData());
            Integer pages = productPage.getPages();
            while (queryProductReq.getPageNo() + 1 < pages) {
                queryProductReq.setPageNo(queryProductReq.getPageNo() + 1);
                productList.addAll(productRemoteService.queryProduct(queryProductReq).getData());
            }
        }

        final Date now = new Date();

        if (CollUtil.isEmpty(productList)) {
            List<ShopSimpleResp> shopSimpleRespList = new ArrayList<>();
            List<Long> shopIds = new ArrayList<>();
            if (StrUtil.isNotBlank(shopName)) {
                ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                        ShopSimpleQueryReq.builder()
                                .shopName(shopName).build()
                );
                if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                    return PageResultHelper.defaultEmpty(request.buildPage());
                }
                shopSimpleRespList = shopSimpleListResp.getList();
                if (shopSimpleRespList.size() > PromotionConstant.MAX_LIKE_QUERY) {
                    throw new BusinessException(PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getCode(),
                            PromotionResultCodeEnum.SHOP_NAME_LIKE_QUERY_TOO_MANY.getMsg());
                }
                shopIds.addAll(shopSimpleRespList.stream().map(ShopSimpleResp::getId).collect(Collectors.toList()));
                if (null != shopId) {
                    // 同时使用了店铺id和店铺名称，需要判断店铺id是否在店铺名称查询的结果中
                    if (!shopIds.contains(shopId)) {
                        return PageResultHelper.defaultEmpty(request.buildPage());
                    }
                }
                shopSimpleRespList.addAll(shopSimpleRespList);
            }

            if (null != shopId) {
                shopIds.add(shopId);
            }

            Page<FlashSale> flashSalePage = flashSaleRepository.pageList(request.buildPage(), FlashSaleParamDto.builder()
                    .shopIdList(shopIds)
                    .status(FlashSaleStatusEnum.ONGOING.getStatus())
                    .normalFlag(Boolean.TRUE)
                    .flashSaleIds(request.getFlashSaleIds())
                    .frontFlag(Boolean.TRUE).build(), null);
            if (null == flashSalePage || CollUtil.isEmpty(flashSalePage.getResult())) {
                return PageResultHelper.defaultEmpty(request);
            }

            List<FlashSale> flashSaleList = flashSalePage.getResult();
            if (CollUtil.isEmpty(shopSimpleRespList)) {
                // 没数据说明之前没查过店铺信息，需要去查一下店铺信息
                shopIds = flashSaleList.stream().map(FlashSale::getShopId).distinct().collect(Collectors.toList());
                ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                        ShopSimpleQueryReq.builder()
                                .shopIdList(shopIds).build()
                );
                shopSimpleRespList = shopSimpleListResp.getList();
            }
            final List<ShopSimpleResp> shopSimpleModelsFinal = shopSimpleRespList;

            //  处理商品名称
            final List<ProductPageResp> productListFinal = new ArrayList<>();
            List<Long> productIdList = flashSaleList.stream().map(FlashSale::getProductId).distinct().collect(Collectors.toList());
            QueryProductReq queryProductReq = new QueryProductReq();
            queryProductReq.setProductIds(productIdList);
            queryProductReq.setPageNo(PromotionConstant.DEFAULT_PAGE);
            queryProductReq.setPageSize(PromotionConstant.MAX_LIKE_QUERY);
            BasePageResp<ProductPageResp> productPage = productRemoteService.queryProduct(queryProductReq);
            if (null != productPage && CollUtil.isNotEmpty(productPage.getData())) {
                productListFinal.addAll(productPage.getData());
            }

            List<Long> flashSaleIds = flashSaleList.stream().map(FlashSale::getId).collect(Collectors.toList());
            final List<FlashSaleDetailCountDto> flashSaleDetailCountModels = flashSaleDetailRepository.countByFlashSaleId(flashSaleIds);

            return PageResultHelper.transfer(flashSalePage, VisualFlashSaleResp.class, (flashSale, visualFlashSaleResp) -> {
                if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                    shopSimpleModelsFinal.stream().filter(s -> s.getId().equals(visualFlashSaleResp.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                        visualFlashSaleResp.setShopName(shopSimpleResp.getShopName());
                    });
                }

                if (CollUtil.isNotEmpty(productListFinal)) {
                    productListFinal.stream().filter(p -> p.getProductId().equals(visualFlashSaleResp.getProductId())).findFirst().ifPresent(productPageResp -> {
                        visualFlashSaleResp.setProductName(productPageResp.getProductName());
                        visualFlashSaleResp.setImagePath(productPageResp.getImagePath());
                        visualFlashSaleResp.setSalePrice(productPageResp.getMinSalePrice());
                        visualFlashSaleResp.setMallStock(productPageResp.getStock());
                    });
                }

                // 处理时间
                Date beginDate = flashSale.getBeginDate();
                if (now.before(beginDate)) {
                    visualFlashSaleResp.setRemStartTime(DateUtil.between(now, beginDate, DateUnit.SECOND));
                }
                Date endDate = flashSale.getEndDate();
                if (now.before(endDate)) {
                    visualFlashSaleResp.setRemEndTime(DateUtil.between(now, endDate, DateUnit.SECOND));
                }

                // 处理库存
                if (CollUtil.isNotEmpty(flashSaleDetailCountModels)) {
                    flashSaleDetailCountModels.stream().filter(flashSaleDetailCountModel -> flashSaleDetailCountModel.getFlashSaleId().equals(flashSale.getId())).findFirst().ifPresent(flashSaleDetailCountModel -> {
                        visualFlashSaleResp.setActivityStock(flashSaleDetailCountModel.getTotalCount());
                    });
                }
            });
        }

        Page<FlashSale> flashSalePage = flashSaleRepository.pageListByProductId(request.buildPage(), FlashSaleParamDto.builder()
                .productIdList(productList.stream().map(ProductPageResp::getProductId).collect(Collectors.toList()))
                .status(FlashSaleStatusEnum.ONGOING.getStatus())
                .normalFlag(Boolean.TRUE)
                .build());
        if (null == flashSalePage || CollUtil.isEmpty(flashSalePage.getResult())) {
            return PageResultHelper.defaultEmpty(request);
        }

        List<FlashSale> flashSaleList = flashSalePage.getResult();
        // 没数据说明之前没查过店铺信息，需要去查一下店铺信息
        List<Long> shopIds = flashSaleList.stream().map(FlashSale::getShopId).distinct().collect(Collectors.toList());
        ShopSimpleListResp shopSimpleListResp = shopRemoteService2.querySimpleList(
                ShopSimpleQueryReq.builder()
                        .shopIdList(shopIds).build()
        );
        final List<ShopSimpleResp> shopSimpleModelsFinal = shopSimpleListResp.getList();
        //  处理商品名称
        final List<ProductPageResp> productListFinal = productList;

        List<Long> flashSaleIds = flashSaleList.stream().map(FlashSale::getId).collect(Collectors.toList());
        final List<FlashSaleDetailCountDto> flashSaleDetailCountModels = flashSaleDetailRepository.countByFlashSaleId(flashSaleIds);

        return PageResultHelper.transfer(flashSalePage, VisualFlashSaleResp.class, (flashSale, visualFlashSaleResp) -> {
            if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                shopSimpleModelsFinal.stream().filter(s -> s.getId().equals(visualFlashSaleResp.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                    visualFlashSaleResp.setShopName(shopSimpleResp.getShopName());
                });
            }

            if (CollUtil.isNotEmpty(productListFinal)) {
                productListFinal.stream().filter(p -> p.getProductId().equals(visualFlashSaleResp.getProductId())).findFirst().ifPresent(productPageResp -> {
                    visualFlashSaleResp.setProductName(productPageResp.getProductName());
                    visualFlashSaleResp.setImagePath(productPageResp.getImagePath());
                    visualFlashSaleResp.setSalePrice(productPageResp.getMinSalePrice());
                    visualFlashSaleResp.setMallStock(productPageResp.getStock());
                });
            }

            // 处理时间
            Date beginDate = flashSale.getBeginDate();
            if (now.before(beginDate)) {
                visualFlashSaleResp.setRemStartTime(DateUtil.between(now, beginDate, DateUnit.SECOND));
            }
            Date endDate = flashSale.getEndDate();
            if (now.before(endDate)) {
                visualFlashSaleResp.setRemEndTime(DateUtil.between(now, endDate, DateUnit.SECOND));
            }

            // 处理库存
            if (CollUtil.isNotEmpty(flashSaleDetailCountModels)) {
                flashSaleDetailCountModels.stream().filter(flashSaleDetailCountModel -> flashSaleDetailCountModel.getFlashSaleId().equals(flashSale.getId())).findFirst().ifPresent(flashSaleDetailCountModel -> {
                    visualFlashSaleResp.setActivityStock(flashSaleDetailCountModel.getTotalCount());
                });
            }
        });
    }

    @Override
    public FlashSaleResp queryByProductId(ProductAndShopIdReq request) {

        // 查询当前时间段内的限时购活动
        FlashSale flashSale = flashSaleRepository.getByNowTimeAndShopAndProductId(request.getShopId(), request.getProductId(),
                FlashSaleStatusEnum.ONGOING.getStatus(), Boolean.TRUE);
        if (null == flashSale) {
            // 未查询到当前时间段内的限时购活动，查询预热中的活动
            flashSale = flashSaleRepository.getNearlyBeginByShopId(request.getShopId(), request.getProductId(),
                    FlashSaleStatusEnum.ONGOING.getStatus(), Boolean.TRUE);
        }
        if (null == flashSale) {
            return null;
        }

        FlashSaleResp flashSaleResp = JsonUtil.copy(flashSale, FlashSaleResp.class);

        List<FlashSaleDetail> flashSaleDetails = flashSaleDetailRepository.getByFlashSaleId(flashSale.getId());
        if (CollUtil.isNotEmpty(flashSaleDetails)) {
            List<FlashSaleDetailResp> detailList = JsonUtil.copyList(flashSaleDetails, FlashSaleDetailResp.class);
            flashSaleResp.setDetailList(detailList);
        }

        return flashSaleResp;
    }

    @Override
    public SkuFlashSaleDetailResp queryValidWithSkuId(QuerySkuFlashSaleReq request) {

        FlashSaleDetail flashSaleDetail = new FlashSaleDetail();
        flashSaleDetail.setSkuId(request.getSkuId());
        flashSaleDetail.setFlashSaleId(request.getFlashSaleId());
        flashSaleDetail.setProductId(request.getProductId());
        FlashSale flashSale = flashSaleRepository.getActiveByDetailParam(flashSaleDetail);
        if (null == flashSale) {
            return null;
        }

        SkuFlashSaleDetailResp skuFlashSaleDetailResp = new SkuFlashSaleDetailResp();
        skuFlashSaleDetailResp.setFlashSaleId(flashSale.getId());
        skuFlashSaleDetailResp.setFlashSaleName(flashSale.getTitle());
        skuFlashSaleDetailResp.setProductId(flashSale.getProductId());
        skuFlashSaleDetailResp.setSkuId(request.getSkuId());
        skuFlashSaleDetailResp.setShopId(flashSale.getShopId());
        skuFlashSaleDetailResp.setLimitCount(flashSale.getLimitCount());
        skuFlashSaleDetailResp.setLimitType(flashSale.getLimitType());

        FlashSaleDetail detail = flashSaleDetailRepository.getByFlashSaleAndSkuId(request.getFlashSaleId(), request.getSkuId());
        if (null != detail) {
            skuFlashSaleDetailResp.setTotalCount(detail.getTotalCount());
            skuFlashSaleDetailResp.setPrice(detail.getPrice());
            if (FlashSaleLimitTypeEnum.SKU.getType().equals(flashSale.getLimitType())) {
                skuFlashSaleDetailResp.setLimitCount(detail.getLimitCount());
            }
        }
        return skuFlashSaleDetailResp;
    }

    @Override
    public EffectiveFlashSaleQueryListResp queryEffectiveFlashSaleList(EffectiveFlashSaleQueryReq request) {
        FlashSaleParamDto flashSaleParamModel = new FlashSaleParamDto();
        // 状态条件处理
        Integer status = request.getStatus();
        if (status != null) {
            Date now = new Date();
            // PS:这个地方的时间进行了特殊处理
            if (status.equals(FlashSaleStatusEnum.ONGOING.getStatus())) {
                flashSaleParamModel.setBeginDate(now);
                flashSaleParamModel.setEndDate(now);
            }
            else if (status.equals(FlashSaleStatusEnum.ENDED.getStatus())) {
                flashSaleParamModel.setStatus(null);
                flashSaleParamModel.setStatusList(Arrays.asList(FlashSaleStatusEnum.ONGOING.getStatus(), FlashSaleStatusEnum.ENDED.getStatus()));
                flashSaleParamModel.setEndDate(now);
            }
            else if (status.equals(FlashSaleStatusEnum.NOT_BEGIN.getStatus())) {
                flashSaleParamModel.setStatus(null);
                flashSaleParamModel.setStatusList(Arrays.asList(FlashSaleStatusEnum.ONGOING.getStatus(), FlashSaleStatusEnum.NOT_BEGIN.getStatus()));
                flashSaleParamModel.setBeginDate(now);
            }
        }

        flashSaleParamModel.setProductIdList(request.getProductIdList());
        flashSaleParamModel.setFrontFlag(request.getFrontFlag());

        EffectiveFlashSaleQueryListResp resp = new EffectiveFlashSaleQueryListResp();

        List<FlashSale> flashSalePage = flashSaleRepository.pageListByProductId(flashSaleParamModel);
        if (CollUtil.isEmpty(flashSalePage)) {
            return resp;
        }

        List<EffectiveFlashSaleQueryResp> respList = JsonUtil.copyList(flashSalePage, EffectiveFlashSaleQueryResp.class);
        resp.setList(respList);
        return resp;
    }

    @Override
    public void consume(FlashSaleConsumeReq request) {
        log.info("限时购活动consume,request:{}", request);
        final FlashSale flashSale = flashSaleRepository.getByIdForceMaster(request.getFlashSaleId());
        if (null == flashSale || !flashSale.getStatus().equals(FlashSaleStatusEnum.ONGOING.getStatus())) {
            // 判断是否有数据，状态是否正常
            throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                    PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
        }

        // 默认是锁sku维度
        String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_SKU_ID_CONSUME_KEY, request.getFlashSaleId(), request.getSkuId());
        if (flashSale.getLimitType().equals(FlashSaleLimitTypeEnum.PRODUCT.getType())) {
            // 如果限购是在商品维度上的，锁活动（=商品维度锁）
            lockKey = String.format(LockConstant.LIMIT_TIME_BUY_CONSUME_KEY, request.getFlashSaleId());
        }
        LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
            FlashSaleDetail flashSaleDetail = flashSaleDetailRepository.getByFlashSaleAndSkuIdForceMaster(request.getFlashSaleId(), request.getSkuId());
            if (null == flashSaleDetail) {
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
            }

            Integer totalCount = flashSaleDetail.getTotalCount();
            // 限购数为0的话说明不限购
            if (flashSaleDetail.getLimitCount() != 0 && totalCount < request.getConsumeNum()) {
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
            }

            Date now = ObjectUtil.defaultIfNull(request.getConsumeTime(), new Date());
            // 判断当前时间是否在活动开始时间和结束时间中间
            if (now.before(flashSale.getBeginDate()) || now.after(flashSale.getEndDate())) {
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_START_OR_END.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_NOT_START_OR_END.getMsg());
            }

            // 如果是商品维度的限购
            if (flashSale.getLimitType().equals(FlashSaleLimitTypeEnum.PRODUCT.getType())) {
                // 重新查一次，防止并发问题
                FlashSale flashSaleNow = flashSaleRepository.getByIdForceMaster(request.getFlashSaleId());
                Integer limitCount = flashSaleNow.getLimitCount();

                // 判断是否超过限购数量
                Integer count = flashSaleConsumeRecordRepository.countByMemberId(request.getMemberId(), request.getFlashSaleId());
                count = count + request.getConsumeNum();
                if (limitCount != 0 && count > limitCount) {
                    throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                            PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
                }
            }
            else {
                Integer limitCount = flashSaleDetail.getLimitCount();
                Integer count = flashSaleConsumeRecordRepository.countByMemberIdAndSku(request.getMemberId(), request.getFlashSaleId(), request.getSkuId());
                count = count + request.getConsumeNum();
                if (limitCount != 0 && count > limitCount) {
                    throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                            PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
                }
            }

            FlashSaleConsumeRecord flashSaleConsumeRecord = new FlashSaleConsumeRecord();
            flashSaleConsumeRecord.setFlashSaleId(request.getFlashSaleId());
            flashSaleConsumeRecord.setFlashSaleDetailId(flashSaleDetail.getId());
            flashSaleConsumeRecord.setSkuId(request.getSkuId());
            flashSaleConsumeRecord.setProductId(request.getProductId());
            flashSaleConsumeRecord.setMemberId(request.getMemberId());
            flashSaleConsumeRecord.setOrderId(request.getOrderId());
            flashSaleConsumeRecord.setConsumeNum(request.getConsumeNum());
            flashSaleConsumeRecord.setConsumeFlag(Boolean.TRUE);

            final FlashSale finalFlashSale = flashSale;

            TransactionHelper.doInTransaction(() -> {
                flashSaleConsumeRecordRepository.save(flashSaleConsumeRecord);
                int updateSaleCountById = flashSaleRepository.updateSaleCountById(finalFlashSale.getId(), request.getConsumeNum());
                if (updateSaleCountById <= 0) {
                    throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                            PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
                }
                int updateCount = flashSaleDetailRepository.reduceStockByFlashSaleIdAndSkuId(flashSaleDetail.getId(), request.getConsumeNum());
                if (updateCount <= 0) {
                    throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                            PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
                }

            });
        });
    }

    @Override
    public void cancelConsume(FlashSaleCancelConsumeReq request) {
        String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_ORDER_ID_CONSUME_KEY, request.getOrderId());
        LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
            FlashSaleConsumeRecord flashSaleConsumeRecord = flashSaleConsumeRecordRepository.getByOrderId(request.getOrderId());
            if (null == flashSaleConsumeRecord) {
                // 没报错出去，不影响业务
                log.info("限时购活动取消消费异常,flashSaleConsumeRecord is null,orderId:{}", request.getOrderId());
                return;
            }

            TransactionHelper.doInTransaction(() -> {
                flashSaleConsumeRecord.setConsumeFlag(Boolean.FALSE);
                flashSaleConsumeRecordRepository.updateById(flashSaleConsumeRecord);
                int updateSaleCountById = flashSaleRepository.updateSaleCountById(flashSaleConsumeRecord.getFlashSaleId(), -flashSaleConsumeRecord.getConsumeNum());
                if (updateSaleCountById <= 0) {
                    throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                            PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
                }
                int updateCount = flashSaleDetailRepository.reduceStockByFlashSaleIdAndSkuId(flashSaleConsumeRecord.getFlashSaleDetailId(), -flashSaleConsumeRecord.getConsumeNum());
                if (updateCount <= 0) {
                    throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                            PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
                }
            });
        });
    }

    @Override
    public void stockReturn(FlashSaleStockReturnReq request) {
        String lockKey = String.format(LockConstant.LIMIT_TIME_BUY_CONSUME_KEY, request.getFlashSaleId(), request.getSkuId(), request.getOrderId(), request.getRelationId());
        LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
            FlashSale flashSale = flashSaleRepository.getById(request.getFlashSaleId());
            if (null == flashSale) {
                log.info("限时购活动退库存异常,flashSale is null,flashSaleId:{}", request.getFlashSaleId());
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
            }

            FlashSaleDetail flashSaleDetail = flashSaleDetailRepository.getByFlashSaleAndSkuId(request.getFlashSaleId(), request.getSkuId());
            if (null == flashSaleDetail) {
                log.info("限时购活动退库存异常,flashSaleDetail is null,flashSaleId:{},skuId:{}", request.getFlashSaleId(), request.getSkuId());
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_NOT_EXIST.getMsg());
            }

            FlashSaleStockReturn flashSaleStockReturn = flashSaleStockReturnRepository.selectByRelationId(request.getRelationId());
            if (null != flashSaleStockReturn) {
                // 已经退过库存了，不需要再退了
                log.info("限时购活动退库存异常,flashSaleStockReturn is not null,relationId:{}", request.getRelationId());
                return;
            }

            FlashSaleConsumeRecord flashSaleConsumeRecord = flashSaleConsumeRecordRepository.getByOrderId(request.getOrderId());
            if (null == flashSaleConsumeRecord) {
                log.info("限时购活动退库存异常,flashSaleConsumeRecord is null,orderId:{}", request.getOrderId());
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_CONSUME_RECORD_NOT_EXIST.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_CONSUME_RECORD_NOT_EXIST.getMsg());
            }

            Integer consumeNum = flashSaleConsumeRecord.getConsumeNum();
            if (consumeNum < request.getReturnNum()) {
                log.info("限时购活动退库存异常,consumeNum:{},returnNum:{}", consumeNum, request.getReturnNum());
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_RETURN_STOCK_GT_CONSUME_STOCK.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_RETURN_STOCK_GT_CONSUME_STOCK.getMsg());
            }

            List<FlashSaleStockReturn> flashSaleStockReturns = flashSaleStockReturnRepository.selectBySkuIdAndFlashSaleId(request.getSkuId(), request.getFlashSaleId(), request.getOrderId());
            Integer oldReturnNum = 0;
            if (CollUtil.isNotEmpty(flashSaleStockReturns)) {
                oldReturnNum = flashSaleStockReturns.stream().mapToInt(FlashSaleStockReturn::getStockReturnNum).sum();
            }
            if (consumeNum < (oldReturnNum + request.getReturnNum())) {
                log.info("限时购活动退库存异常,consumeNum:{},oldReturnNum:{},returnNum:{}", consumeNum, oldReturnNum, request.getReturnNum());
                throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_RETURN_STOCK_GT_CONSUME_STOCK.getCode(),
                        PromotionResultCodeEnum.FLASH_SALE_RETURN_STOCK_GT_CONSUME_STOCK.getMsg());
            }


            FlashSaleStockReturn stockReturn = new FlashSaleStockReturn();
            stockReturn.setFlashSaleId(request.getFlashSaleId());
            stockReturn.setSkuId(request.getSkuId());
            stockReturn.setOrderId(request.getOrderId());
            stockReturn.setRelationId(request.getRelationId());
            stockReturn.setStockReturnNum(request.getReturnNum());

            TransactionHelper.doInTransaction(() -> {
                flashSaleStockReturnRepository.save(stockReturn);
                int updateSaleCountById = flashSaleRepository.updateSaleCountById(request.getFlashSaleId(), -request.getReturnNum());
                if (updateSaleCountById <= 0) {
                    throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                            PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
                }
                int updateCount = flashSaleDetailRepository.reduceStockByFlashSaleIdAndSkuId(flashSaleDetail.getId(), -request.getReturnNum());
                if (updateCount <= 0) {
                    throw new BusinessException(PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getCode(),
                            PromotionResultCodeEnum.FLASH_SALE_PRODUCT_LIMIT.getMsg());
                }
            });
        });
    }

    @Override
    public VisualFlashSaleListResp queryVisualFlashSaleList(FlashSaleQueryByIdReq req) {
        VisualFlashSaleListResp result = new VisualFlashSaleListResp();
        if (CollUtil.isEmpty(req.getFlashSaleIds())) {
            result.setFlashSaleList(Collections.emptyList());
            return result;
        }


        List<FlashSale> dbFlashSaleList = flashSaleRepository.getByIds(req.getFlashSaleIds());
        List<VisualFlashSaleResp> flashSaleList = JsonUtil.copyList(dbFlashSaleList, VisualFlashSaleResp.class);

        List<Long> shopIds = dbFlashSaleList.stream().map(FlashSale::getShopId).collect(Collectors.toList());
        List<Long> productIds = dbFlashSaleList.stream().map(FlashSale::getProductId).collect(Collectors.toList());

        Map<Long, ShopSimpleResp> shopMap = shopRemoteService2.getShopMapByIds(shopIds);
        Map<Long, ProductPageResp> productMap = productRemoteService.getProductMap(productIds);
        List<FlashSaleDetailCountDto> flashSaleDetailCountModels = flashSaleDetailRepository.countByFlashSaleId(req.getFlashSaleIds());
        Map<Long, Long> stockMap = flashSaleDetailCountModels.stream().collect(Collectors
                .toMap(FlashSaleDetailCountDto::getFlashSaleId, FlashSaleDetailCountDto::getTotalCount, (k1, k2) -> k1));

        Date now = new Date();
        flashSaleList.forEach(flashSale -> {
            ShopSimpleResp shop = shopMap.get(flashSale.getShopId());
            flashSale.setShopName(shop == null ? "" : shop.getShopName());

            ProductPageResp product = productMap.get(flashSale.getProductId());
            if (product != null) {
                flashSale.setProductName(product.getProductName());
                flashSale.setImagePath(product.getImagePath());
                flashSale.setSalePrice(product.getMinSalePrice());
                flashSale.setMallStock(product.getTotalStock());
            }

            flashSale.setActivityStock(stockMap.getOrDefault(flashSale.getId(), 0L));

            // 处理时间
            Date beginDate = flashSale.getBeginDate();
            if (now.before(beginDate)) {
                flashSale.setRemStartTime(DateUtil.between(now, beginDate, DateUnit.SECOND));
            }
            Date endDate = flashSale.getEndDate();
            if (now.before(endDate)) {
                flashSale.setRemEndTime(DateUtil.between(now, endDate, DateUnit.SECOND));
            }
        });

        result.setFlashSaleList(flashSaleList);
        return result;
    }
}
