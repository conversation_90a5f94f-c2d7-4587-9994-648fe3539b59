package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/17 8:52
 */
@Getter
@Setter
public class ProductOnOffSaleLogBo extends BaseParamReq {

    @ExaminField(description = "商品id的集合")
    private List<Long> productIdList;

    private Boolean onSale;

    @ExaminField(description = "是否上架")
    private String onSaleStr;
}
