package com.sankuai.shangou.seashop.product.core.mq.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 商品表变更消息体
 * <AUTHOR>
 */
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ProductMessage implements Serializable {

    /**
     * 主键
     */
    @JsonProperty("id")
    private Long id;
    /**
     * 商品唯一表示(美团Id组件获取)
     */
    @JsonProperty("product_id")
    private Long productId;
    /**
     * 店铺ID
     */
    @JsonProperty("shop_id")
    private Long shopId;

}
