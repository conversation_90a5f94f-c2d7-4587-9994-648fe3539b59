package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.core.model.bo.ExclusivePriceSaveBo;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductUpdateReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndMemberIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceDetailResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceSimpleResp;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
public interface ExclusivePriceService {

    /**
     * 保存专享价活动
     *
     * @param saveBo
     */
    void save(ExclusivePriceSaveBo saveBo);

    /**
     * 结束专享价活动
     *
     * @param baseIdReq
     */
    void endActive(BaseIdReq baseIdReq);

    /**
     * 更新专享价活动商品
     *
     * @param req
     */
    void updateProduct(ExclusivePriceProductUpdateReq req);

    /**
     * 分页查询专享价活动
     *
     * @param request
     * @return
     */
    BasePageResp<ExclusivePriceSimpleResp> pageList(ExclusivePriceQueryReq request);

    /**
     * 根据id查询专享价活动
     *
     * @param id
     * @return
     */
    ExclusivePriceResp getById(Long id);

    /**
     * 根据商品id和会员id查询专享价活动
     *
     * @param request
     * @return
     */
    ExclusivePriceResp queryByProductAndMemberId(ProductAndMemberIdReq request);

    /**
     * 专享价活动列表明细查询(导出使用)
     *
     * @param request
     * @return
     */
    BasePageResp<ExclusivePriceDetailResp> detailPageList(ExclusivePriceQueryReq request);
}
