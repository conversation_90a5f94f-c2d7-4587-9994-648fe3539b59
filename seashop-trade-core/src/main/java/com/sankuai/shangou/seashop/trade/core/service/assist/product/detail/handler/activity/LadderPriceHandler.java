package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductLadderPriceBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 阶梯价活动处理器
 *
 * <AUTHOR>
 * @date 2023/12/25 9:27
 */
@Slf4j
@Component
public class LadderPriceHandler extends AbsActivityHandler {

    @Override
    public void handle(DealActivityContext context) {
        if (!context.isWhetherOpenLadder()) {
            return;
        }

        ProductActivityInfoBo activityInfo = context.getActivityInfo();
        activityInfo.setHasLadderPrice(true);
        List<ProductLadderPriceBo> ladderPriceList = context.getActivityContext().getLadderPriceList();
        activityInfo.setLadderPriceRuleList(ladderPriceList);
        context.setEstimatePrice(ladderPriceList.stream().map(ProductLadderPriceBo::getPrice).min(BigDecimal::compareTo).get());
    }

    @Override
    public int order() {
        return 3;
    }
}
