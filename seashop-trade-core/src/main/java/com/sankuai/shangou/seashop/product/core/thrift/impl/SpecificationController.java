package com.sankuai.shangou.seashop.product.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.product.core.service.SpecificationService;
import com.sankuai.shangou.seashop.product.thrift.core.SpecificationFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateNameReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateValueReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecificationReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationNameResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationValueResp;

@RestController
@RequestMapping("/specification")
public class SpecificationController implements SpecificationFeign {
    @Resource
    private SpecificationService specificationService;
    
    @Override
    @PostMapping(value = "/query", consumes = "application/json")
    public ResultDto<BasePageResp<SpecificationResp>> query(@RequestBody SpecificationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("query", request, req -> {

            return specificationService.query(request);
        });
    }

    @Override
    @PostMapping(value = "/create", consumes = "application/json")
    public ResultDto<BaseResp> create(@RequestBody SpecificationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("create", request, req -> {
            req.checkParameter();
            specificationService.create(request);
            return new BaseResp();
        });
    }
    @Override
    @PostMapping(value = "/save", consumes = "application/json")
    public ResultDto<BaseResp> save(@RequestBody SpecificationReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            req.checkParameter();
            specificationService.save(request);
            return new BaseResp();
        });
    }

    @Override
    @PostMapping(value = "/remove", consumes = "application/json")
    public ResultDto<BaseResp> remove(@RequestParam Long shopId, @RequestParam Long nameId) throws TException {
        return ThriftResponseHelper.responseInvoke("save", null, req -> {
            specificationService.remove(shopId,nameId);
            return new BaseResp();
        });
    }

    @Override
    @PostMapping(value = "/createName", consumes = "application/json")
    public ResultDto<Long> createName(@RequestBody CreateNameReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createName", request, req -> {
            req.checkParameter();
            return specificationService.createName(request);
        });
    }

    @Override
    @PostMapping(value = "/createValue", consumes = "application/json")
    public ResultDto<Long> createValue(@RequestBody CreateValueReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createValue", request, req -> {
            req.checkParameter();
            return specificationService.createValue(request);
        });
    }

    @Override
    @GetMapping(value = "/names", consumes = "application/json")
    public ResultDto<List<SpecificationNameResp>> getNames(@RequestParam Long shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("getNames", null, req -> specificationService.getNames(shopId));
    }

    @Override
    @GetMapping(value = "/values", consumes = "application/json")
    public ResultDto<List<SpecificationValueResp>> getValues(@RequestParam Long nameId) throws TException {
        return ThriftResponseHelper.responseInvoke("getValues", null, req -> specificationService.getValues(nameId));
    }
}
