package com.sankuai.shangou.seashop.product.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.core.service.CategoryCashDepositService;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryCashDepositCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.CategoryCashDepositListReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@RestController
@RequestMapping("/categoryCashDeposit")
public class CategoryCashDepositCmdController implements CategoryCashDepositCmdFeign {

    @Resource
    private CategoryCashDepositService categoryCashDepositService;

    @Override
    @PostMapping(value = "/save", consumes = "application/json")
    public ResultDto<BaseResp> save(CategoryCashDepositListReq request) throws TException {
        request.checkParameter();
        return categoryCashDepositService.save(request);
    }
}
