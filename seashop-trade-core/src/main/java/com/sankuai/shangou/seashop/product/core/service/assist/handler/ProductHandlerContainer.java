package com.sankuai.shangou.seashop.product.core.service.assist.handler;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @date 2023/11/23 19:55
 */
@Component
public class ProductHandlerContainer implements InitializingBean {

    @Resource
    private ApplicationContext applicationContext;

    private final Map<ProductHandlerType, List<AbsProductHandler>> handlerMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, AbsProductHandler> beanMap = applicationContext.getBeansOfType(AbsProductHandler.class);
        beanMap.values().forEach(handler -> {
            handler.types().forEach(type -> {
                List<AbsProductHandler> handlers = handlerMap.get(type);
                if (handlers == null) {
                    handlers = Lists.newArrayList();
                    handlerMap.put(type, handlers);
                }
                handlers.add(handler);
            });
        });
        handlerMap.forEach((k, v) -> v.sort(Comparator.comparingInt(item -> item.config().getOrder())));
        this.handlerMap.putAll(handlerMap);
    }

    public List<AbsProductHandler> getHandlers(ProductHandlerType type) {
        return handlerMap.get(type);
    }
}
