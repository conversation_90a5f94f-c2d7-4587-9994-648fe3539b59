package com.sankuai.shangou.seashop.promotion.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.LockConstant;
import com.sankuai.shangou.seashop.promotion.common.constant.PromotionConstant;
import com.sankuai.shangou.seashop.promotion.common.enums.PromotionResultCodeEnum;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponRecordQueryBo;
import com.sankuai.shangou.seashop.promotion.core.service.CouponRecordService;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.Coupon;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponRecord;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordConsumeDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordSimpleDto;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CouponProductRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CouponRecordRepository;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CouponRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.CouponStatusEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.UseAreaEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderResp;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/10/010
 * @description:
 */
@Service
@Slf4j
public class CouponRecordServiceImpl implements CouponRecordService {

    @Resource
    private CouponRecordRepository couponRecordRepository;
    @Resource
    private CouponRepository couponRepository;
    @Resource
    private CouponProductRepository couponProductRepository;

    @Override
    public BasePageResp<CouponRecordSimpleDto> pageList(CouponRecordQueryBo queryBo) {
        CouponRecordParamDto param = JsonUtil.copy(queryBo, CouponRecordParamDto.class);
        param.setSortSql(sortSqlBuild(queryBo));

        BasePageResp<CouponRecordSimpleDto> dtoBasePageResp = couponRecordRepository.pageList(queryBo.buildPage(), param);
        if (null == dtoBasePageResp || CollUtil.isEmpty(dtoBasePageResp.getData())) {
            return PageResultHelper.defaultEmpty(queryBo);
        }

        final Map<Long, List<Long>> couponProductMap = new HashMap<>();
        // 查询部分商品可用的优惠券
        List<Long> couponIdList = dtoBasePageResp.getData().stream().filter(data -> data.getUseArea().equals(UseAreaEnum.PRODUCT.getCode())).map(CouponRecordSimpleDto::getCouponId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(couponIdList)) {
            List<CouponProduct> couponProductList = couponProductRepository.listByCouponIds(couponIdList);
            Map<Long, List<CouponProduct>> productMap = couponProductList.stream().collect(Collectors.groupingBy(CouponProduct::getCouponId));
            productMap.forEach((k, v) -> {
                List<Long> productIdList = v.stream().map(CouponProduct::getProductId).collect(Collectors.toList());
                couponProductMap.put(k, productIdList);
            });
        }

        Date now = new Date();
        dtoBasePageResp.getData().parallelStream().forEach(
                dto -> {
                    Integer couponStatus = dto.getCouponStatus();
                    if (couponStatus.equals(CouponStatusEnum.NOT_USED.getCode())) {
                        Date endTime = dto.getEndTime();
                        if (now.after(endTime)) {
                            dto.setCouponStatus(CouponStatusEnum.EXPIRED.getCode());
                            dto.setCouponStatusDesc(CouponStatusEnum.EXPIRED.getMsg());
                        }
                        else {
                            dto.setCouponStatusDesc(CouponStatusEnum.NOT_USED.getMsg());
                        }
                    }
                    if (couponStatus.equals(CouponStatusEnum.USED.getCode())) {
                        dto.setCouponStatusDesc(CouponStatusEnum.USED.getMsg());
                    }
                    if (CollUtil.isNotEmpty(couponProductMap)) {
                        List<Long> productIdList = couponProductMap.get(dto.getCouponId());
                        dto.setProductIdList(productIdList);
                    }
                }
        );

        return dtoBasePageResp;
    }

    private String sortSqlBuild(CouponRecordQueryBo queryBo) {
        List<FieldSortReq> fieldSortReqs = null;
        if (Boolean.TRUE.equals(queryBo.getOrderByPriceDesc())) {
            fieldSortReqs = sortPriceBuild();
        }
        if (Boolean.TRUE.equals(queryBo.getAutoOrderByStatus())) {
            fieldSortReqs = sortStatusBuild(queryBo.getStatus());
        }
        return MybatisUtil.getOrderSql(fieldSortReqs);
    }

    private List<FieldSortReq> sortStatusBuild(Integer status) {
        List<FieldSortReq> sortList = new ArrayList<>();
        // 如果没指定状态或者是已过期 默认根据id 降序
        if (status == null || status == 2) {
            FieldSortReq fieldSortReq = new FieldSortReq();
            fieldSortReq.setSort("t1.id");
            fieldSortReq.setIzAsc(Boolean.FALSE);
            sortList.add(fieldSortReq);
            return sortList;
        }

        // 未使用 根据金额降序
        if (status == 0) {
            FieldSortReq fieldSortReq = new FieldSortReq();
            fieldSortReq.setSort("t2.price");
            fieldSortReq.setIzAsc(Boolean.FALSE);
            sortList.add(fieldSortReq);
            return sortList;
        }

        // 已使用 根据使用时间降序
        FieldSortReq fieldSortReq = new FieldSortReq();
        fieldSortReq.setSort("t1.used_time");
        fieldSortReq.setIzAsc(Boolean.FALSE);
        sortList.add(fieldSortReq);
        return sortList;
    }

    private List<FieldSortReq> sortPriceBuild() {
        List<FieldSortReq> sortList = new ArrayList<>();
        FieldSortReq fieldSortReq = new FieldSortReq();
        fieldSortReq.setSort("t2.price");
        fieldSortReq.setIzAsc(Boolean.TRUE);
        sortList.add(fieldSortReq);
        return sortList;
    }

    @Override
    public void consume(CouponRecordConsumeReq request) {
        String lockKey = String.format(LockConstant.COUPON_CONSUME_KEY, request.getMemberId());
        LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
            log.info("核销优惠券, request:{}", request);
            List<CouponRecord> couponRecordList = couponRecordRepository.queryListByIdAndUserId(JsonUtil.copy(request, CouponRecordConsumeDto.class));
            log.info("核销优惠券, couponRecordList:{}", couponRecordList);

            List<com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponRecordConsumeDto> consumeList = request.getConsumeList();

            // 查询传入的数量和查询出来的数量是否一致，如果不一致，抛出异常
            if (couponRecordList.size() != consumeList.size()) {
                throw new BusinessException(PromotionResultCodeEnum.COUPON_PART_NOT_EXIST.getCode(),
                        PromotionResultCodeEnum.COUPON_PART_NOT_EXIST.getMsg());
            }

            // 判断优惠券是否已经被核销，如果已经被核销，抛出异常
            couponRecordList.forEach(couponRecord -> {
                if (couponRecord.getCouponStatus().equals(CouponStatusEnum.USED.getCode())) {
                    throw new BusinessException(PromotionResultCodeEnum.COUPON_PART_CONSUMED.getCode(),
                            PromotionResultCodeEnum.COUPON_PART_CONSUMED.getMsg());
                }
            });

            List<Long> couponIdList = couponRecordList.stream().map(CouponRecord::getCouponId).distinct().collect(Collectors.toList());
            List<Coupon> couponList = couponRepository.listByIdForceMaster(couponIdList);
            Date now = ObjectUtil.defaultIfNull(request.getConsumeTime(), new Date());
            for (Coupon coupon : couponList) {

                // 查询优惠券活动，判断优惠券是否在活动期间内，如果不在活动期间内，抛出异常
                if (now.before(coupon.getStartTime()) || now.after(coupon.getEndTime())) {
                    throw new BusinessException(PromotionResultCodeEnum.COUPON_PART_NOT_IN_ACTIVITY.getCode(),
                            PromotionResultCodeEnum.COUPON_PART_NOT_IN_ACTIVITY.getMsg());
                }
            }

            for (CouponRecord couponRecord : couponRecordList) {

                // 优惠券对应的获取
                Coupon coupon = couponList.stream().filter(c -> c.getId().equals(couponRecord.getCouponId())).findFirst().get();

                // 查询优惠券对应的商品，判断传入的商品是否在优惠券对应的商品中，如果不在，抛出异常
                Integer useArea = coupon.getUseArea();
                if (UseAreaEnum.ALL.getCode().equals(useArea)) {
                    // 全场通用，不需要判断商品
                    continue;
                }

                // 查询优惠券对应的商品
                List<CouponProduct> couponProductList = couponProductRepository.listByCouponId(coupon.getId());
                List<Long> couponProductIdList = couponProductList.stream().map(CouponProduct::getProductId).collect(Collectors.toList());

                com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponRecordConsumeDto couponRecordConsumeDto = consumeList.stream().filter(c -> c.getCouponRecordId().equals(couponRecord.getId())).findFirst().get();
                List<Long> productIds = couponRecordConsumeDto.getProductIds();

                // 判断 productIds 中的 productId 是否在 couponProductIdList 中，如果不在，抛出异常
                for (Long productId : productIds) {
                    if (!couponProductIdList.contains(productId)) {
                        throw new BusinessException(PromotionResultCodeEnum.COUPON_PART_NOT_IN_PRODUCT.getCode(),
                                PromotionResultCodeEnum.COUPON_PART_NOT_IN_PRODUCT.getMsg());
                    }
                }
            }

            // 到这里，该校验的基本已经完成，开始核销优惠券。将orderId设置到couponRecord中，将status设置为已使用
            for (CouponRecord couponRecord : couponRecordList) {
                com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponRecordConsumeDto couponRecordConsumeDto = consumeList.stream().filter(c -> c.getCouponRecordId().equals(couponRecord.getId())).findFirst().get();
                couponRecord.setOrderId(couponRecordConsumeDto.getOrderId());
                couponRecord.setCouponStatus(CouponStatusEnum.USED.getCode());
                couponRecord.setUsedTime(new Date());
            }
            couponRecordRepository.updateBatchById(couponRecordList);
        });
    }

    @Override
    public void cancelConsume(CouponRecordCancelConsumeReq request) {
        String lockKey = String.format(LockConstant.COUPON_CONSUME_KEY, request.getMemberId());
        LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
            log.info("撤销优惠券核销, request:{}", request);
            List<CouponRecord> couponRecordList = new ArrayList<>(request.getOrderIdList().size());
            List<List<String>> orderIdSplit = CollUtil.split(request.getOrderIdList(), PromotionConstant.MAX_IN_SIZE);
            for (List<String> orderIdList : orderIdSplit) {
                List<CouponRecord> couponRecordListTemp = couponRecordRepository.queryListByOrderId(orderIdList);
                couponRecordList.addAll(couponRecordListTemp);
            }
            log.info("撤销优惠券核销, couponRecordList:{}", couponRecordList);
            if (CollUtil.isEmpty(couponRecordList)) {
                return;
            }
            List<Long> recordIdList = couponRecordList.stream().map(CouponRecord::getId).collect(Collectors.toList());
            TransactionHelper.doInTransaction(() -> {
                CollUtil.split(recordIdList, PromotionConstant.MAX_IN_SIZE).forEach(
                        recordIdListTemp -> {
                            couponRecordRepository.cancelConsume(recordIdListTemp, CouponStatusEnum.NOT_USED.getCode());
                        }
                );
            });
        });
    }

    @Override
    public CouponRecordSimpleListResp getByIdOrSn(CouponRecordIdOrSnReq request) {

        List<CouponRecord> couponRecordList = couponRecordRepository.getByIdOrSn(request.getRecordIdList(), request.getCouponSnList());
        if (CollUtil.isEmpty(couponRecordList)) {
            CouponRecordSimpleListResp couponRecordSimpleListResp = new CouponRecordSimpleListResp();
            couponRecordSimpleListResp.setList(CollUtil.newArrayList());
            return couponRecordSimpleListResp;
        }

        List<CouponRecordSimpleResp> couponRecordSimpleRespList = JsonUtil.copyList(couponRecordList, CouponRecordSimpleResp.class);

        couponRecordSimpleRespList.parallelStream().forEach(
                couponRecordSimpleResp -> {
                    opCouponRecordSimpleResp(couponRecordSimpleResp);
                }
        );

        CouponRecordSimpleListResp couponRecordSimpleListResp = new CouponRecordSimpleListResp();
        couponRecordSimpleListResp.setList(couponRecordSimpleRespList);

        return couponRecordSimpleListResp;
    }

    private void opCouponRecordSimpleResp(CouponRecordSimpleResp couponRecordSimpleResp) {
        Coupon coupon = couponRepository.getById(couponRecordSimpleResp.getCouponId());
        couponRecordSimpleResp.setCouponName(coupon.getCouponName());
        couponRecordSimpleResp.setOrderAmount(coupon.getOrderAmount().longValue());
        couponRecordSimpleResp.setPrice(coupon.getPrice().longValue());
        couponRecordSimpleResp.setStartTime(coupon.getStartTime());
        couponRecordSimpleResp.setEndTime(coupon.getEndTime());
        couponRecordSimpleResp.setUseArea(coupon.getUseArea());

        if (UseAreaEnum.PRODUCT.getCode().equals(coupon.getUseArea())) {
            // 指定商品：查询商品ID
            List<CouponProduct> couponProductList = couponProductRepository.listByCouponId(coupon.getId());
            if (CollUtil.isNotEmpty(couponProductList)) {
                List<Long> productIdList = couponProductList.stream().map(CouponProduct::getProductId).collect(Collectors.toList());
                couponRecordSimpleResp.setProductIdList(productIdList);
            }
        }

        Integer couponStatus = couponRecordSimpleResp.getCouponStatus();
        if (couponStatus.equals(CouponStatusEnum.NOT_USED.getCode())) {
            Date endTime = couponRecordSimpleResp.getEndTime();
            if (DateTime.now().after(endTime)) {
                couponRecordSimpleResp.setCouponStatus(CouponStatusEnum.EXPIRED.getCode());
                couponRecordSimpleResp.setCouponStatusDesc(CouponStatusEnum.EXPIRED.getMsg());
            }
            else {
                couponRecordSimpleResp.setCouponStatusDesc(CouponStatusEnum.NOT_USED.getMsg());
            }
        }
        if (couponStatus.equals(CouponStatusEnum.USED.getCode())) {
            couponRecordSimpleResp.setCouponStatusDesc(CouponStatusEnum.USED.getMsg());
        }
    }

    @Override
    public CouponRecordSimpleResp getUserRecordById(CouponRecordIdReq request) {
        CouponRecord couponRecord = couponRecordRepository.getById(request.getCouponRecordId());
        if (null == couponRecord) {
            return new CouponRecordSimpleResp();
        }
        if (!couponRecord.getUserId().equals(request.getUserId())) {
            // 越权处理
            return new CouponRecordSimpleResp();
        }
        CouponRecordSimpleResp couponRecordSimpleResp = JsonUtil.copy(couponRecord, CouponRecordSimpleResp.class);
        opCouponRecordSimpleResp(couponRecordSimpleResp);
        return couponRecordSimpleResp;
    }

    @Override
    public CouponRecordOrderListResp getRecordByOrder(PromotionRecordOrderQueryReq request) {

        CouponRecordOrderListResp couponRecordOrderListResp = new CouponRecordOrderListResp();

        List<OrderQueryReq> orderList = request.getOrderList();
        List<Long> shopIdList = orderList.stream().map(OrderQueryReq::getShopId).collect(Collectors.toList());
        // 查询用户在当前时间内可用的优惠券（店铺维度）
        List<CouponRecordSimpleDto> couponRecordSimpleModels = couponRecordRepository.queryListByUserIdAndShopId(request.getUserId(), shopIdList, CouponStatusEnum.NOT_USED_AND_IN_CURRENT_TIME.getCode());

        if (CollUtil.isEmpty(couponRecordSimpleModels)) {
            couponRecordOrderListResp.setList(CollUtil.newArrayList());
            return couponRecordOrderListResp;
        }

        Map<Long, List<CouponRecordSimpleDto>> recordMap = couponRecordSimpleModels.stream().collect(Collectors.groupingBy(CouponRecordSimpleDto::getShopId));
        List<CouponRecordOrderResp> couponRecordOrderRespList = new ArrayList<>(orderList.size());
        for (OrderQueryReq orderQueryReq : orderList) {

            List<CouponRecordSimpleDto> crsmList = new ArrayList<>();

            CouponRecordOrderResp couponRecordOrderResp = new CouponRecordOrderResp();
            Long shopId = orderQueryReq.getShopId();
            couponRecordOrderResp.setShopId(shopId);
            List<CouponRecordSimpleDto> couponRecordSimpleModelList = recordMap.get(shopId);
            if (CollUtil.isEmpty(couponRecordSimpleModelList)) {
                couponRecordOrderResp.setRecordList(CollUtil.newArrayList());
                couponRecordOrderRespList.add(couponRecordOrderResp);
                continue;
            }

            List<ProductQueryReq> productList = orderQueryReq.getProductList();

            // 过滤全场通用的
            List<CouponRecordSimpleDto> allAreaList = couponRecordSimpleModelList.stream().filter(c -> c.getUseArea().equals(UseAreaEnum.ALL.getCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(allAreaList)) {
                // 过滤出满足订单金额的
                BigDecimal amount = productList.stream().map(ProductQueryReq::getProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                List<CouponRecordSimpleDto> allAreaUseList = allAreaList.stream().filter(c -> c.getOrderAmount().equals(PromotionConstant.COUPON_DEFAULT_ORDER_AMOUNT)
                        || BigDecimal.valueOf(c.getOrderAmount()).compareTo(amount) <= 0).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(allAreaUseList)) {
                    crsmList.addAll(allAreaUseList);
                }
            }

            // 过滤非全场通用的
            List<CouponRecordSimpleDto> notAllAreaList = couponRecordSimpleModelList.stream().filter(c -> c.getUseArea().equals(UseAreaEnum.PRODUCT.getCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(notAllAreaList)) {
                List<Long> orderProductIdList = productList.stream().map(ProductQueryReq::getProductId).collect(Collectors.toList());
                for (CouponRecordSimpleDto model : notAllAreaList) {
                    // 查询优惠券对应的商品
                    List<CouponProduct> couponProductList = couponProductRepository.listByCouponId(model.getCouponId());
                    if (CollUtil.isEmpty(couponProductList)) {
                        continue;
                    }
                    // 优惠券可用的商品ID
                    List<Long> couponProductIdList = couponProductList.stream().map(CouponProduct::getProductId).collect(Collectors.toList());
                    // 取 orderProductIdList 和 couponProductIdList 的交集
                    List<Long> intersectionIdList = (ArrayList) CollUtil.intersection(orderProductIdList, couponProductIdList);
                    if (CollUtil.isEmpty(intersectionIdList)) {
                        continue;
                    }
                    model.setProductIdList(intersectionIdList);
                    BigDecimal amount = productList.stream().filter(p -> intersectionIdList.contains(p.getProductId())).map(ProductQueryReq::getProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (model.getOrderAmount().equals(PromotionConstant.COUPON_DEFAULT_ORDER_AMOUNT)
                            || BigDecimal.valueOf(model.getOrderAmount()).compareTo(amount) <= 0) {
                        crsmList.add(model);
                    }
                }
            }

            if (CollUtil.isNotEmpty(crsmList)) {
                crsmList = crsmList.stream().sorted(Comparator.comparing(CouponRecordSimpleDto::getPrice).reversed()).collect(Collectors.toList());
            }

            couponRecordOrderResp.setRecordList(JsonUtil.copyList(crsmList, CouponRecordSimpleResp.class));

            couponRecordOrderRespList.add(couponRecordOrderResp);
        }

        couponRecordOrderListResp.setList(couponRecordOrderRespList);

        return couponRecordOrderListResp;
    }
}
