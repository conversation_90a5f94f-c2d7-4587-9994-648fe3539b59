package com.sankuai.shangou.seashop.promotion.core.task;

import com.hishop.himall.report.api.request.BatchReportSourceCouponReq;
import com.hishop.himall.report.api.request.ReportSourceCouponReq;
import com.hishop.himall.report.api.service.ReportFeign;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.JobQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JobLogInfoResp;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponRecord;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.CouponRecordRepository;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description:
 */
@Slf4j
@Component
public class ReportCouponTask {


    @Resource
    private CouponRecordRepository couponRecordRepository;

    @Resource
    private JobQueryFeign jobQueryFeign;
    @Resource
    private ReportFeign reportFeignService;

    /**
     * 订单退款状态查询
     */
    @XxlJob("reportCoupon")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步优惠券报表")
    public void reportCoupon(String jobIdStr) {
        log.info("【定时任务】【同步优惠券报表】...start...");
        long jobId;
        if (StringUtils.hasLength(jobIdStr)) {
            jobId = Long.parseLong(jobIdStr.trim());
        } else {
            jobId = XxlJobHelper.getJobId();
        }

//        long jobId = 1L;
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryFeign.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<CouponRecord> carts = couponRecordRepository.getByUpdateTime(triggerTime);
        if (CollectionUtils.isEmpty(carts)) {
            return;
        }
        List<ReportSourceCouponReq> recordList = carts.stream().map(couponRecord -> {
            ReportSourceCouponReq couponReq = new ReportSourceCouponReq();
            couponReq.setId(couponRecord.getId());
            couponReq.setUserId(couponRecord.getUserId());
            couponReq.setShopId(couponRecord.getShopId());
            couponReq.setRecordId(couponRecord.getId());
            couponReq.setReceiveTime(couponRecord.getCouponTime());
            couponReq.setCreateTime(LocalDateTime.ofInstant(couponRecord.getCreateTime().toInstant(), ZoneId.systemDefault()));
            return couponReq;
        }).collect(Collectors.toList());
        reportFeignService.batchCreateSourceCoupon(new BatchReportSourceCouponReq(recordList));
    }


}
