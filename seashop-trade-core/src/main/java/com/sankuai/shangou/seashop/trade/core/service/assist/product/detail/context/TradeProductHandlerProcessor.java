package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.ProductHandlerType;

/**
 * <AUTHOR>
 * @date 2023/12/25 10:06
 */
@Component
public class TradeProductHandlerProcessor {

    @Resource
    private TradeProductHandlerContainer tradeProductHandlerContainer;

    public void handle(ProductHandlerType type, ProductContext context) {
        List<AbsProductHandler> handlers = tradeProductHandlerContainer.getHandlers(type);
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(handlers), "没有找到对应的处理器");
        handlers.forEach(handler -> handler.handle(context));
    }

}
