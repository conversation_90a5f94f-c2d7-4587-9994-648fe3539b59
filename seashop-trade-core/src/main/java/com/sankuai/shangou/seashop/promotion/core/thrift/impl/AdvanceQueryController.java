package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.service.AdvanceService;
import com.sankuai.shangou.seashop.promotion.dao.core.model.AdvanceDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.AdvanceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.AdvanceQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@RestController
@RequestMapping("/advance")
public class AdvanceQueryController implements AdvanceQueryFeign {

    @Resource
    private AdvanceService advanceService;

    @GetMapping(value = "/getOne")
    @Override
    public ResultDto<AdvanceResp> getOne() throws TException {
        return ThriftResponseHelper.responseInvoke("getById", null, req -> {
            AdvanceDto dto = advanceService.getOne();
            return JsonUtil.copy(dto, AdvanceResp.class);
        });
    }
}
