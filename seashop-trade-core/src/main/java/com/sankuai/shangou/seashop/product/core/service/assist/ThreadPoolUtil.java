package com.sankuai.shangou.seashop.product.core.service.assist;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.sankuai.shangou.seashop.product.common.constant.ThreadPoolConst;

/**
 * <AUTHOR>
 */
public class ThreadPoolUtil {

    private final static int PROCESSOR_NUM = Runtime.getRuntime().availableProcessors();

    public static ThreadPoolExecutor COMMON_THREAD_POOL = generateCommonThreadPool(
            ThreadPoolConst.RHINO_KEY_THREAD_POOL_COMMON,
            "product-es-build-pool");

    private static ThreadPoolExecutor generateCommonThreadPool(String rhinoKey, String threadPrefix) {
//        DefaultThreadPoolProperties.Setter setter = DefaultThreadPoolProperties.Setter()
//                .withCoreSize(PROCESSOR_NUM)
//                .withMaxSize(PROCESSOR_NUM * 3)
//                .withKeepAliveTimeMinutes(10)
//                .withKeepAliveTimeUnit(TimeUnit.MINUTES)
//                .withBlockingQueue(new LinkedBlockingQueue<>())
//                .withMaxQueueSize(2000)
//                .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())
//                .withThreadFactory(new ThreadFactoryBuilder().setNameFormat(threadPrefix).build());
//        return Rhino.newThreadPool(rhinoKey, setter);
        return new ThreadPoolExecutor(PROCESSOR_NUM, PROCESSOR_NUM * 3, 10, TimeUnit.MINUTES, new LinkedBlockingQueue<>(2000),
            r -> {
                Thread thread = new Thread(r);
                thread.setName(threadPrefix);
                return thread;
            },
            new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
