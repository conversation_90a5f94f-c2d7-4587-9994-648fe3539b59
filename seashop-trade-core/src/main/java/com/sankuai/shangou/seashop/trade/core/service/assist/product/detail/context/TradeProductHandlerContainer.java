package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.ProductHandlerType;

/**
 * <AUTHOR>
 * @date 2023/12/25 9:42
 */
@Component
public class TradeProductHandlerContainer implements InitializingBean {

    @Resource
    private ApplicationContext applicationContext;

    private final Map<ProductHandlerType, List<AbsProductHandler>> handlerMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, AbsProductHandler> beanMap = applicationContext.getBeansOfType(AbsProductHandler.class);
        beanMap.values().forEach(handler -> {
            List<AbsProductHandler> handlers = handlerMap.getOrDefault(handler.type(), new ArrayList<>());
            handlers.add(handler);
            handlerMap.put(handler.type(), handlers);
        });
        handlerMap.forEach((k, v) -> v.sort(Comparator.comparingInt(AbsProductHandler::order)));
        this.handlerMap.putAll(handlerMap);
    }

    public List<AbsProductHandler> getHandlers(ProductHandlerType type) {
        return handlerMap.get(type);
    }

}
