package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.DescriptionTemplateService;
import com.sankuai.shangou.seashop.product.core.service.model.DescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionTemplate;
import com.sankuai.shangou.seashop.product.thrift.core.DescriptionTemplateCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.DeleteDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.SaveDescriptionTemplateReq;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:26
 */
@RestController
@RequestMapping("/descriptionTemplate")
public class DescriptionTemplateCmdController implements DescriptionTemplateCmdFeign {

    @Resource
    private DescriptionTemplateService descriptionTemplateService;

    @PostMapping(value="/createDescriptionTemplate", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> createDescriptionTemplate(@RequestBody SaveDescriptionTemplateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createDescriptionTemplate", request, req -> {
            req.checkParameter();

            descriptionTemplateService.saveDescriptionTemplate(JsonUtil.copy(req, DescriptionTemplateBo.class));
            return new BaseResp();
        });
    }

    @PostMapping(value="/updateDescriptionTemplate", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> updateDescriptionTemplate(@RequestBody SaveDescriptionTemplateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateDescriptionTemplate", request, req -> {
            req.checkForEdit();

            descriptionTemplateService.saveDescriptionTemplate(JsonUtil.copy(req, DescriptionTemplateBo.class));
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MOVE,
            dto = DeleteDescriptionTemplateReq.class,
            entity = ProductDescriptionTemplate.class,
            actionName = "删除版式")
    @PostMapping(value="/deleteDescriptionTemplate", consumes = "application/json")
    public ResultDto<BaseResp> deleteDescriptionTemplate(@RequestBody DeleteDescriptionTemplateReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteDescriptionTemplate", request, req -> {

            descriptionTemplateService.deleteDescriptionTemplate(req.getId(), req.getShopId());
            return new BaseResp();
        });
    }
}
