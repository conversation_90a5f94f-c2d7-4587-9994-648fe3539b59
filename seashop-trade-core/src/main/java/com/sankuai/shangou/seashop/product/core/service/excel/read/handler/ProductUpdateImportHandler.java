package com.sankuai.shangou.seashop.product.core.service.excel.read.handler;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteFreightAreaService;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.common.remote.user.model.RemoteShopBo;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.excel.read.BizTypeEnum;
import com.sankuai.shangou.seashop.product.core.service.excel.read.context.ProductImportAssist;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductUpdateImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper.ProductUpdateImportWrapper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopBrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopCategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;
import com.sankuai.shangou.seashop.product.thrift.core.helper.ParameterHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品更新导入处理器
 *
 * <AUTHOR>
 * @date 2023/11/21 22:08
 */
@Component
@Slf4j
@SuppressWarnings("all")
public class ProductUpdateImportHandler extends ImportHandler<ProductUpdateImportDto> {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private CategoryRepository categoryRepository;
    @Resource
    private BrandRepository brandRepository;
    @Resource
    private ShopBrandRepository shopBrandRepository;
    @Resource
    private ShopCategoryRepository shopCategoryRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private ProductService productService;
    @Resource
    private ProductAssist productAssist;
    @Resource
    private RemoteFreightAreaService remoteFreightAreaService;
    @Resource
    private RemoteShopService remoteShopService;


    @Override
    public BizType bizType() {
        return BizTypeEnum.PRODUCT_UPDATE_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<ProductUpdateImportDto> importResult) {
        Long shopId = ProductImportAssist.getShopIdOrThrow();
        RemoteShopBo shop = remoteShopService.checkAndGetShop(shopId);

        // 获取数据格式校验通过的数据
        List<ProductUpdateImportDto> productList = importResult.getSuccessDataList();
        checkProduct(productList, shopId);
        checkProductCode(productList, shopId);
        checkProductImage(productList);
        checkCategory(productList, shopId);
        checkBrand(productList, shop);
        checkShopCategory(productList, shopId);
        checkFreightTemplate(productList, shopId);
        checkPrice(productList);
        checkStock(productList);
        checkSku(productList, shopId);
        buildErrMsg(productList);
    }

    @Override
    public void saveImportData(List<ProductUpdateImportDto> productList) {
        productList.forEach(product -> {
            ProductBo productBo = productBoBuild(product);
            try {
                productService.saveProduct(productBo, ProductSourceEnum.MALL, ProductChangeType.IMPORT_EDIT, true, false);
            }
            catch (Exception e) {
                product.setErrMsg(String.format("系统异常: 【%s】", e.getMessage()));
                log.error("商品更新导入失败: product: {}", product, e);
            }
        });
    }

    @Override
    public DataWrapper<ProductUpdateImportDto> wrapData(List<ProductUpdateImportDto> errList) {
        return new ProductUpdateImportWrapper(errList);
    }

    /**
     * 校验商品id
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkProduct(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        // 首先检验一下productId 的格式
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getProductId())) {
                product.getErrBuilder().append("商品id不能为空;");
                return;
            }

            if (!NumberUtil.isLong(product.getProductId())) {
                product.getErrBuilder().append("商品id格式不正确(只能为正整数);");
                return;
            }

            product.setProductIdLong(Long.parseLong(product.getProductId()));
        });


        List<Long> productIdList =
                productList.stream().map(ProductUpdateImportDto::getProductIdLong).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
        Map<Long, Product> productMap = productRepository.getProductMap(productIdList);

        productList.forEach(product -> {
            Long productId = product.getProductIdLong();
            // 如果商品id 正确 则进行相关校验
            if (productId != null) {
                Product dbProduct = productMap.get(productId);

                if (dbProduct == null) {
                    product.getErrBuilder().append("商品id不存在;");
                    return;
                }

                if (!dbProduct.getShopId().equals(shopId)) {
                    product.getErrBuilder().append("商品不属于当前店铺;");
                }

                product.setSource(dbProduct.getSource());
                product.setShopId(dbProduct.getShopId());
            }

            if (StringUtils.isNotEmpty(product.getProductName()) && product.getProductName().length() > ParameterConstant.PRODUCT_NAME_LENGTH) {
                product.getErrBuilder().append("商品名称长度不能超过" + ParameterConstant.PRODUCT_NAME_LENGTH + "个字符;");
            }

            if (StringUtils.isNotEmpty(product.getShortDescription()) && product.getShortDescription().length() > ParameterConstant.PRODUCT_SHORT_DESCRIPTION_LIMIT_LENGTH) {
                product.getErrBuilder().append("广告词长度不能超过" + ParameterConstant.PRODUCT_SHORT_DESCRIPTION_LIMIT_LENGTH + "个字符;");
            }

            // 计量单位里面不能带数字
            if (StringUtils.isNotEmpty(product.getMeasureUnit()) && !ParameterHelper.checkMeasureUnit(product.getMeasureUnit())) {
                product.getErrBuilder().append("计量单位格式不正确(不能包含数字);");
            }

            // 检验限购
            if (StringUtils.isNotEmpty(product.getMaxBuyCount())) {
                if (!NumberUtil.isInteger(product.getMaxBuyCount())) {
                    product.getErrBuilder().append(String.format("限购量格式不正确, 取值范围为%s-%s;", ParameterConstant.MIN_MAX_BUY_COUNT, ParameterConstant.MAX_MAX_BUY_COUNT));
                }
                else if (!ParameterHelper.checkMaxBuyCount(Integer.parseInt(product.getMaxBuyCount()))) {
                    product.getErrBuilder().append(String.format("限购量取值范围为%s-%s;", ParameterConstant.MIN_MAX_BUY_COUNT, ParameterConstant.MAX_MAX_BUY_COUNT));
                }
            }

            // 检验倍数起购量
            if (StringUtils.isNotEmpty(product.getMultipleCount())) {
                if (!NumberUtil.isInteger(product.getMultipleCount())) {
                    product.getErrBuilder().append("倍数起购量格式不正确(只能为整数);");
                }
                else if (!ParameterHelper.checkMultipleCount(Integer.parseInt(product.getMultipleCount()))) {
                    product.getErrBuilder().append(String.format("倍数起购量取值范围为%s-%s;", ParameterConstant.MIN_MULTIPLE_COUNT, ParameterConstant.MAX_MULTIPLE_COUNT));
                }
            }
        });
    }

    private void checkProductImage(List<ProductUpdateImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        Map<String, String> remoteImgMapping = ProductImportAssist.getRemoteImgMapping();
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getImagePath())) {
                return;
            }

            List<String> localImageList = Arrays.asList(product.getImagePath().split(StrUtil.COMMA));
            List<String> imageList = new ArrayList<>();
            // 将本地图片转换为
            localImageList.forEach(localImage -> {
                String remoteImage = remoteImgMapping.get(localImage);
                if (StringUtils.isEmpty(remoteImage)) {
                    product.getErrBuilder().append(String.format("[%s]图片不存在;", localImage));
                    return;
                }

                imageList.add(remoteImage);
            });

            if (product.getErrBuilder().length() == 0) {
                product.setImageList(imageList);
                product.setImagePath(CollectionUtils.isNotEmpty(imageList) ? imageList.get(0) : null);
            }
        });
    }

    /**
     * 校验货号是否存在
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkProductCode(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> productCodeList = productList.stream().map(ProductUpdateImportDto::getProductCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodeList)) {
            return;
        }

        Map<String, Product> productMap = productRepository.getProductMapByProductCode(productCodeList, shopId);

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getProductCode())) {
                return;
            }

            if (productMap.get(product.getProductCode()) != null) {
                product.getErrBuilder().append("商品货号已存在;");
                return;
            }

            if (ProductSourceEnum.QNH.getCode().equals(product.getSource())) {
                product.getErrBuilder().append("牵牛花商品不能修改商品货号;");
            }

            if (product.getProductCode().length() > ParameterConstant.PRODUCT_CODE_LENGTH) {
                product.getErrBuilder().append("商品货号长度不能超过" + ParameterConstant.PRODUCT_CODE_LENGTH + "个字符;");
            }

            if (!ParameterHelper.checkProductCode(product.getProductCode())) {
                product.getErrBuilder().append("商品货号格式不正确(只能为数字、字母或者-);");
            }
        });
    }

    /**
     * 校验商品分类
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkCategory(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        checkFirstCategory(productList, shopId);
        checkSecondCategory(productList, shopId);
        checkThirdCategory(productList, shopId);
    }

    /**
     * 校验一级分类
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkFirstCategory(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> firstCategoryNames =
                productList.stream().map(ProductUpdateImportDto::getFirstCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(firstCategoryNames)) {
            return;
        }
        List<Category> categoryList = categoryRepository.listCategoryByPidAndName(CommonConstant.DEFAULT_PARENT_ID, firstCategoryNames);
        Map<String, Category> categoryMap = categoryList.stream().collect(Collectors.toMap(Category::getName, Function.identity(), (k1, k2) -> k2));
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getFirstCategoryName())) {
                if (StringUtils.isNotEmpty(product.getSecondCategoryName()) || StringUtils.isNotEmpty(product.getThirdCategoryName())) {
                    product.getErrBuilder().append("一级分类不能为空;");
                    return;
                }
                return;
            }

            Category category = categoryMap.get(product.getFirstCategoryName());
            if (category == null) {
                product.getErrBuilder().append("一级分类不存在;");
                return;
            }

            product.setFirstCategoryId(category.getId());
        });
    }

    /**
     * 校验二级分类
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkSecondCategory(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            // 检验二级分类
            if (StringUtils.isEmpty(product.getSecondCategoryName())) {
                if (StringUtils.isNotEmpty(product.getFirstCategoryName()) || StringUtils.isNotEmpty(product.getThirdCategoryName())) {
                    product.getErrBuilder().append("二级分类不能为空;");
                }
                return;
            }

            // 如果没有获取到正确的一级分类 直接返回
            if (product.getFirstCategoryId() == null) {
                return;
            }

            Category category = categoryRepository.getCategoryByPidAndName(product.getFirstCategoryId(), product.getSecondCategoryName());
            if (category == null) {
                product.getErrBuilder().append("二级分类不存在;");
                return;
            }

            product.setSecondCategoryId(category.getId());
        });
    }

    /**
     * 校验三级分类
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkThirdCategory(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            // 校验三级分类
            if (StringUtils.isEmpty(product.getThirdCategoryName())) {
                if (StringUtils.isNotEmpty(product.getFirstCategoryName()) || StringUtils.isNotEmpty(product.getSecondCategoryName())) {
                    product.getErrBuilder().append("三级分类不能为空;");
                }
                return;
            }

            if (product.getSecondCategoryId() == null) {
                return;
            }

            Category category = categoryRepository.getCategoryByPidAndName(product.getSecondCategoryId(), product.getThirdCategoryName());
            if (category == null) {
                product.getErrBuilder().append("三级分类不存在;");
                return;
            }

            product.setThirdCategoryId(category.getId());
            product.setCategoryPath(category.getPath());
            if (ProductSourceEnum.QNH.getCode().equals(product.getSource())) {
                product.getErrBuilder().append("牵牛花商品不能修改商品分类;");
            }
        });
    }

    /**
     * 校验品牌
     *
     * @param productList 导入的数据
     * @param shop        店铺信息
     */
    private void checkBrand(List<ProductUpdateImportDto> productList, RemoteShopBo shop) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> brandNames = productList.stream().map(ProductUpdateImportDto::getBrandName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(brandNames)) {
            return;
        }

        List<Brand> brandList = brandRepository.getByBrandNames(brandNames);
        Map<String, Brand> brandMap = brandList.stream().collect(Collectors.toMap(Brand::getName, Function.identity(), (k1, k2) -> k2));
        List<Long> authBrandIds = shopBrandRepository.listBrandIdsByShopId(shop.getId());

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getBrandName())) {
                return;
            }

            Brand brand = brandMap.get(product.getBrandName());
            if (brand == null) {
                product.getErrBuilder().append("品牌不存在;");
                return;
            }

            if (!authBrandIds.contains(brand.getId()) && !shop.getWhetherSelf()) {
                product.getErrBuilder().append("该供应商不支持此品牌;");
                return;
            }

            product.setBrandId(brand.getId());
        });
    }

    /**
     * 校验店铺品牌
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkShopCategory(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> shopCategoryNames = productList.stream().map(ProductUpdateImportDto::getShopCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopCategoryNames)) {
            return;
        }

        List<ShopCategory> shopCategories = shopCategoryRepository.listShopCategory(shopCategoryNames, shopId);
        Map<String, ShopCategory> shopCategoryMap = shopCategories.stream().collect(Collectors.toMap(ShopCategory::getName, Function.identity(), (k1, k2) -> k2));
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getShopCategoryName())) {
                return;
            }

            ShopCategory shopCategory = shopCategoryMap.get(product.getShopCategoryName());
            if (shopCategory == null) {
                product.getErrBuilder().append("店铺分类不存在;");
                return;
            }

            product.setShopCategoryId(shopCategory.getId());
        });
    }

    /**
     * 校验运费模板
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkFreightTemplate(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> freightTemplateNames = productList.stream()
                .map(ProductUpdateImportDto::getFreightTemplateName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<QueryFreightTemplateDto> templateList = remoteFreightAreaService.queryFreightTemplate(shopId, freightTemplateNames);
        Map<String, QueryFreightTemplateDto> templateMap = templateList.stream().collect(Collectors.toMap(QueryFreightTemplateDto::getName, Function.identity(), (k1, k2) -> k2));

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getFreightTemplateName())) {
                return;
            }

            QueryFreightTemplateDto template = templateMap.get(product.getFreightTemplateName());
            if (template == null) {
                product.getErrBuilder().append("运费模板不存在;");
                return;
            }

            product.setFreightTemplateId(template.getId());
        });
    }

    /**
     * 校验sku
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkSku(List<ProductUpdateImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        // 首先校验一下skuAutoId 的格式
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getSkuAutoId())) {
                product.getErrBuilder().append("规格id不能为空;");
                return;
            }

            if (!NumberUtil.isLong(product.getSkuAutoId())) {
                product.getErrBuilder().append("规格id格式不正确(只能为正整数);");
                return;
            }

            product.setSkuAutoIdLong(Long.parseLong(product.getSkuAutoId()));
        });

        List<Long> skuAutoIds = productList.stream()
                .map(ProductUpdateImportDto::getSkuAutoIdLong).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuAutoIds)) {
            return;
        }
        List<Sku> skuList = skuRepository.listByIds(skuAutoIds);
        Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Function.identity(), (k1, k2) -> k2));
        productList.forEach(product -> {
            Long skuAutoId = product.getSkuAutoIdLong();
            if (skuAutoId == null) {
                return;
            }

            Sku sku = skuMap.get(skuAutoId);

            if (sku == null) {
                product.getErrBuilder().append("规格不存在;");
                return;
            }

            if (!sku.getProductId().equals(Long.parseLong(product.getProductId()))) {
                product.getErrBuilder().append("规格不属于该商品;");
                return;
            }

            if (StringUtils.isNotEmpty(product.getSkuCode()) && ProductSourceEnum.QNH.getCode().equals(product.getSource())) {
                product.getErrBuilder().append("牵牛花商品不能修改规格货号;");
                return;
            }

            if (StringUtils.isNotEmpty(product.getSkuCode()) && !ParameterHelper.checkProductCode(product.getSkuCode())) {
                product.getErrBuilder().append("规格货号格式不正确(只能为数字、字母或者-);");
            }

            // 校验全部通过 则构建skuBo
            if (product.getErrBuilder().length() > 0) {
                return;
            }
            ProductSkuBo skuBo = JsonUtil.copy(product, ProductSkuBo.class);
            skuBo.setSkuId(sku.getSkuId());
            product.setSkuList(Arrays.asList(skuBo));
        });
    }

    /**
     * 校验库存
     *
     * @param productList
     */
    private void checkStock(List<ProductUpdateImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        productList.forEach(product -> {
            if (StringUtils.isNotEmpty(product.getStock())) {
                if (!NumberUtil.isLong(product.getStock())) {
                    product.getErrBuilder().append("库存格式不正确(只能为整数);");
                }
                else if (!ParameterHelper.checkStock(Long.parseLong(product.getStock()))) {
                    product.getErrBuilder().append(String.format("库存取值范围为%s-%s;", ParameterConstant.MIN_STOCK, ParameterConstant.MAX_STOCK));
                }
            }

            if (StringUtils.isNotEmpty(product.getSafeStock())) {
                if (!NumberUtil.isLong(product.getSafeStock())) {
                    product.getErrBuilder().append("警戒库存格式不正确(只能为整数);");
                }
                else if (!ParameterHelper.checkStock(Long.parseLong(product.getSafeStock()))) {
                    product.getErrBuilder().append(String.format("警戒库存取值范围为%s-%s;", ParameterConstant.MIN_STOCK, ParameterConstant.MAX_STOCK));
                }
            }
        });
    }

    /**
     * 校验价格
     *
     * @param productList
     */
    private void checkPrice(List<ProductUpdateImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        productList.forEach(product -> {
            if (StringUtils.isNotEmpty(product.getSalePrice())) {
                // 商城价格
                if (!ParameterHelper.checkAmount(product.getSalePrice())) {
                    product.getErrBuilder().append(String.format("商城价范围为%s-%s, 且最多保留两位小数", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
                }

                // 商城价范围
                else if (!ParameterHelper.checkPrice(BigDecimal.valueOf(Double.parseDouble(product.getSalePrice())))) {
                    product.getErrBuilder().append(String.format("商城价范围为%s-%s;", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
                }
            }

            if (StringUtils.isNotEmpty(product.getMarketPrice())) {
                if (!ParameterHelper.checkAmount(product.getMarketPrice())) {
                    product.getErrBuilder().append(String.format("市场价格式不正确(最多保留两位小数的正数);"));
                }

                // 市场价范围
                else if (!ParameterHelper.checkMarketPrice(BigDecimal.valueOf(Double.parseDouble(product.getMarketPrice())))) {
                    product.getErrBuilder().append(String.format("市场价范围为%s-%s;", ParameterConstant.MIN_MARKET_PRICE, ParameterConstant.MAX_MARKET_PRICE));
                }
            }
        });
    }


    /**
     * 构建商品bo
     *
     * @param productUpdateImportDto 导入的数据
     */
    private ProductBo productBoBuild(ProductUpdateImportDto productUpdateImportDto) {
        Long operationUserId = ProductImportAssist.getOperationUserId();
        Long operationShopId = ProductImportAssist.getOperationShopId();

        ProductBo product = JsonUtil.copy(productUpdateImportDto, ProductBo.class);
        product.setCategoryId(productUpdateImportDto.getThirdCategoryId());
        product.setMobileDescription(productUpdateImportDto.getDescription());
        if (productUpdateImportDto.getShopCategoryId() != null) {
            product.setShopCategoryIdList(Arrays.asList(productUpdateImportDto.getShopCategoryId()));
        }
        product.setOperationShopId(operationShopId);
        product.setOperationUserId(operationUserId);
        return product;
    }

    /**
     * 构建错误信息
     *
     * @param productList 导入的数据
     */
    private void buildErrMsg(List<ProductUpdateImportDto> productList) {
        productList.forEach(product -> {
            String errMsg = StringUtils.defaultString(product.getErrMsg(), StrUtil.EMPTY);
            product.setErrMsg(errMsg + product.getErrBuilder().toString());
        });
    }

}
