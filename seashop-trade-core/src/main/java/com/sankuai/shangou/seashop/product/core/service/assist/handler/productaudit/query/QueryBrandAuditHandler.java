package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.query;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.BrandAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 查询审核属性处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 19:59
 */
@Component
@Slf4j
public class QueryBrandAuditHandler extends AbsQueryProductAuditHandler {

    @Resource
    private BrandAssist brandAssist;

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        productBo.setBrandName(brandAssist.getBrandName(productBo.getBrandId()));
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_BRAND_AUDIT;
    }

}
