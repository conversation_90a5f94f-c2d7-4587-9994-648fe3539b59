package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.collocation.PageCollocationResp;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 13:37
 */
public interface CollocationService {

    /**
     * 分页查询组合购列表(供应商)
     * @param request
     * @return
     */
    BasePageResp<PageCollocationResp> pageSellerCollocation(PageSellerCollocationReq request);

    /**
     * 分页查询组合购列表(平台)
     * @param request
     * @return
     */
    BasePageResp<PageCollocationResp> pageMCollocation(PageMCollocationReq request);

    /**
     * 新增组合购
     * @param request
     * @return
     */
    void addCollocation(AddCollocationReq request);

    /**
     * 修改组合购
     * @param request
     * @return
     */
    void updateCollocation(UpdateCollocationReq request);

    /**
     * 查询组合购详情
     * @param req
     * @return
     */
    CollocationResp queryCollocationDetail(CollocationDetailReq req);

    /**
     * 失效组合购
     * @param req
     * @return
     */
    void cancelCollocation(CancelCollocationReq req);

    /**
     * 商品详情-组合购活动分组-买家端
     * @param request
     * @return
     */
    MallCollocationResp queryMallCollocationList(MallCollocationReq request);

    /**
     * 根据商品ID和状态查询组合购活动
     * @param request
     * @return
     */
    List<CollocationActivityResp> queryCollocationByProductIdsAndStatus(CollocationActivityReq request);
}
