package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationProductBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationSkuBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductSkuBo;

import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 活动后置处理器(选取最佳优惠)
 * 主要是来处理满减和优惠券并存的清空, 选取最佳优惠
 *
 * <AUTHOR>
 * @date 2024/03/22 14:03
 */
@Slf4j
@Component
public class PostActivityHandler extends AbsActivityHandler {

    @Override
    public void handle(DealActivityContext context) {
        // 提取最小优惠金额
        BigDecimal estimatePrice = getMinSalePrice(context.getEstimatePrice(),
                context.getCouponEstimatePrice(), context.getFullReductionEstimatePrice());

        context.setEstimatePrice(estimatePrice.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : estimatePrice);

        // 处理组合购详情页面
        dealCollectionDetail(context);

        // 处理价格区间
        dealSalePriceRange(context);
    }

    @Override
    public int order() {
        return 100;
    }

    private void dealSalePriceRange(DealActivityContext context) {
        ProductActivityInfoBo activityInfo = context.getActivityInfo();
        RemoteFlashSaleBo flashSaleRule = activityInfo.getFlashSaleRule();


        List<BigDecimal> priceList = getPriceList(context);
        BigDecimal minSalePrice = priceList.stream().filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        BigDecimal maxSalePrice = priceList.stream().filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);

        // 计算价格区间 如果minSalePrice和maxSalePrice相等 则显示minSalePrice即可, 否则展示区间
        if (minSalePrice.compareTo(maxSalePrice) == 0) {
            context.setSalePriceRange(NumberUtil.toStr(minSalePrice));
        } else {
            context.setSalePriceRange(NumberUtil.toStr(minSalePrice) + "-" + NumberUtil.toStr(maxSalePrice));
        }
        context.setMinSalePrice(minSalePrice);
        context.setMaxSalePrice(maxSalePrice);
    }

    private List<BigDecimal> getPriceList(DealActivityContext context) {
        List<BigDecimal> priceList = new ArrayList<>();

        ProductActivityInfoBo activityInfo = context.getActivityInfo();
        List<ProductSkuBo> skuList = context.getSkuList();

        // 如果有阶梯价 将阶梯价的所有价格加入价格集合
        if (activityInfo.isHasLadderPrice()) {
            // 判断一下是否有非专享价的sku, 如果hasUnExclusiveSku = false, 则表示所有的sku都是专享价, 就没必要取阶梯价了
            boolean hasUnExclusiveSku = skuList.stream().anyMatch(sku -> !sku.isExclusiveSku());
            if (hasUnExclusiveSku) {
                List<ProductLadderPriceBo> ladderPriceRuleList = activityInfo.getLadderPriceRuleList();
                priceList.addAll(ladderPriceRuleList.stream().map(ProductLadderPriceBo::getPrice).collect(Collectors.toList()));
            }
        }
        // 没有阶梯价 将sku价格加入价格集合
        else {
            priceList.addAll(skuList.stream().map(ProductSkuBo::getSalePrice).collect(Collectors.toList()));
        }

        // 如果有限制购 那么价格区间展示限时购的价格(限时购必须要进行中才计算，预热阶段不展示)
        RemoteFlashSaleBo flashSaleRule = activityInfo.getFlashSaleRule();
        if (activityInfo.isHasFlashSale() && flashSaleRule.getEndCountDown() != 0 && flashSaleRule.getStartCountDown() == 0) {
            priceList.addAll(skuList.stream().map(ProductSkuBo::getFlashSalePrice).filter(Objects::nonNull).collect(Collectors.toList()));
        }

        // 如果有专享价 则将参加专享价的规格加入价格集合
        if (activityInfo.isHasExclusivePrice()) {
            priceList.addAll(skuList.stream().filter(sku -> sku.isExclusiveSku()).map(ProductSkuBo::getSalePrice).collect(Collectors.toList()));
        }
        return priceList;
    }

    private BigDecimal getMinSalePrice(BigDecimal... prices) {
        return Arrays.asList(prices).stream().filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    }

    private void dealCollectionDetail(DealActivityContext context) {
        // 前端传了组合购id 则表示从组合购进入的详情 则需要用组合购的详情来替换
        if (context.getCollocationId() == null) {
            return;
        }

        RemoteCollocationBo collectionBuyConfig = context.getActivityContext().getCollectionBuy().stream()
                .filter(item -> item.getId().equals(context.getCollocationId())).findFirst().orElse(null);
        if (collectionBuyConfig == null) {
            return;
        }

        List<RemoteCollocationSkuBo> skuList = getCollectionBuySkuList(collectionBuyConfig.getProductRespList(), context.getProductId());
        Map<String, RemoteCollocationSkuBo> skuMap = skuList.stream()
                .collect(Collectors.toMap(RemoteCollocationSkuBo::getSkuId, Function.identity(), (k1, k2) -> k1));
        context.getSkuList().forEach(sku -> {
            RemoteCollocationSkuBo collocationSku = skuMap.get(sku.getSkuId());
            if (collocationSku != null) {
                sku.setSalePrice(collocationSku.getPrice());
                sku.setSkuPirce(collocationSku.getSkuPirce());
            }
        });
    }

    private List<RemoteCollocationSkuBo> getCollectionBuySkuList(List<RemoteCollocationProductBo> productList, Long productId) {
        if (CollectionUtils.isEmpty(productList)) {
            return Collections.EMPTY_LIST;
        }

        RemoteCollocationProductBo product = productList.stream()
                .filter(item -> item.getProductId().equals(productId)).findFirst().orElse(null);
        return product == null ? Collections.EMPTY_LIST : product.getSkuRespList();
    }
}
