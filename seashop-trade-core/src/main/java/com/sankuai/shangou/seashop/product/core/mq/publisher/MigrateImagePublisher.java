package com.sankuai.shangou.seashop.product.core.mq.publisher;

import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.mq.model.MigrateImageMessage;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/06 20:06
 */
@Service
@Slf4j
public class MigrateImagePublisher {

//    @MafkaProducer(namespace = MafkaConstant.DEFAULT_NAMESPACE, topic = MafkaConstant.TOPIC_PRODUCT_MIGRATE_IMAGE)
//    private IProducerProcessor producerProcessor;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    /**
     * 发送迁移商品图片消息
     *
     * @param imageMessage 迁移商品图片消息
     */
    public void sendMessage(MigrateImageMessage imageMessage) {
        String msg = JsonUtil.toJsonString(imageMessage);
        log.info("迁移商品图片发送消息-data：{}", msg);
        try {
            SendResult sendResult = defaultRocketMq.syncSend(MafkaConstant.TOPIC_PRODUCT_MIGRATE_IMAGE, msg);
//            ProducerResult producerResult = producerProcessor.sendMessage(msg);
            log.info("迁移商品图片发送消息-result：{}", JsonUtil.toJsonString(sendResult));
        }
        catch (Exception e) {
            log.error("迁移商品图片发送消息-data：{}", msg, e);
            throw new SystemException("迁移商品图片失败");
        }
    }

}
