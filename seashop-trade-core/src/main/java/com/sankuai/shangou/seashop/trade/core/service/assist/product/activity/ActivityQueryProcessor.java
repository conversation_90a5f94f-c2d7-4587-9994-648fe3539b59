package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityHandlerContainer;

/**
 * 活动查询处理器
 *
 * <AUTHOR>
 * @date 2023/12/23 13:53
 */
@Component
public class ActivityQueryProcessor {

    @Resource
    private ActivityHandlerContainer activityHandlerContainer;

    public void query(ActivityContext context) {
        AbsActivityQueryHandler handler = activityHandlerContainer.getHandler(StartActivityQueryHandler.class);
        while (handler != null) {
            if (handler.support(context)) {
                handler.query(context);
            }
            handler = activityHandlerContainer.getHandler(handler.nextHandler());
        }
    }
}
