package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.ReceiveTypeEnum;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCouponSimpleBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/25 9:33
 */
@Slf4j
@Component
public class CouponHandler extends AbsActivityHandler {

    @Override
    public void handle(DealActivityContext context) {
        ActivityContext activityData = context.getActivityContext();
        if (CollectionUtils.isEmpty(activityData.getCouponList())) {
            return;
        }

        ProductActivityInfoBo activityInfo = context.getActivityInfo();
        activityInfo.setHasCoupon(true);
        List<RemoteCouponSimpleBo> couponList = activityData.getCouponList();

        BigDecimal estimatePrice = context.getEstimatePrice();

        // 获取有效的优惠券
        List<RemoteCouponSimpleBo> validCouponList = getValidCouponList(couponList);
        // 从有效的优惠券中匹配最优优惠券
        RemoteCouponSimpleBo bestCoupon = getBestCoupon(validCouponList, estimatePrice);
        if (bestCoupon != null) {
            log.info("[查询商品详情]匹配到最优优惠券:{}", JsonUtil.toJsonString(bestCoupon));
            context.setCouponEstimatePrice(estimatePrice.subtract(bestCoupon.getPrice()));
        }

        // 只给前端返回可以领取的有效优惠券
        activityInfo.setCouponList(validCouponList.stream()
                .filter(item -> ReceiveTypeEnum.SHOP_INDEX.getCode().equals(item.getReceiveType()))
                .collect(Collectors.toList()));
    }

    /**
     * 提取有效的优惠券列表
     * 1.提取出用户未使用的优惠券
     * 2.提取出还可以领取优惠券
     *
     * @param couponList
     * @return
     */
    private List<RemoteCouponSimpleBo> getValidCouponList(List<RemoteCouponSimpleBo> couponList) {
        if (CollectionUtils.isEmpty(couponList)) {
            return Collections.emptyList();
        }

        // 1.提取出用户未使用的优惠券
        List<RemoteCouponSimpleBo> unusedCouponList = couponList.stream()
            .filter(coupon -> coupon.getUserReceivedNum() > coupon.getUserUsedUum())
            .collect(Collectors.toList());

        // 2.提取出还可以领取优惠券
        List<RemoteCouponSimpleBo> availableCouponList = couponList.stream()
            .filter(coupon -> coupon.getNum() > coupon.getReceiveNum()
                && (coupon.getPerMax() == 0 || coupon.getPerMax() > coupon.getUserReceivedNum())
                && coupon.getReceiveType() == 0)
            .collect(Collectors.toList());

        // 将 1.2 优惠券列表合并
        unusedCouponList.addAll(availableCouponList);
        // 3.根据优惠券id 去重
        Map<Long, RemoteCouponSimpleBo> couponMap = unusedCouponList
            .stream().collect(Collectors.toMap(RemoteCouponSimpleBo::getId, Function.identity(), (k1, k2) -> k1));
        // 4.根据优惠券id 排序
        return couponMap.values().stream().sorted(Comparator.comparing(RemoteCouponSimpleBo::getId)).collect(Collectors.toList());
    }

    /**
     * 获取最优优惠券
     * 1.提取出用户未使用的优惠券
     * 2.提取出还可以领取优惠券
     * 3.从1.2提取出来的优惠券中符合条件的最大优惠的优惠券
     *
     * @return 优惠券信息
     */
    private RemoteCouponSimpleBo getBestCoupon(List<RemoteCouponSimpleBo> validCouponList, BigDecimal estimatePrice) {
        if (CollectionUtils.isEmpty(validCouponList) || estimatePrice == null) {
            return null;
        }

        // 3.从1.2提取出来的优惠券中符合条件的最大优惠的优惠券
        return validCouponList.stream()
                .filter(coupon -> coupon.getOrderAmount() != null && estimatePrice.compareTo(coupon.getOrderAmount()) >= 0)
                .max(Comparator.comparing(RemoteCouponSimpleBo::getPrice))
                .orElse(null);
    }

    @Override
    public int order() {
        return 7;
    }
}
