package com.sankuai.shangou.seashop.product.core.service.assist;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.product.core.service.converter.LadderPriceAuditConverter;
import com.sankuai.shangou.seashop.product.core.service.converter.LadderPriceConverter;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPrice;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPriceAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductLadderPriceAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductLadderPriceRepository;

/**
 * <AUTHOR>
 * @date 2023/11/23 22:47
 */
@Component
public class LadderPriceAssist {

    @Resource
    private ProductLadderPriceRepository productLadderPriceRepository;
    @Resource
    private ProductLadderPriceAuditRepository productLadderPriceAuditRepository;

    /**
     * 根据商品id查询阶梯价
     *
     * @param productId 商品id
     * @return 阶梯价集合
     */
    public List<ProductLadderPriceBo> getLadderPriceBoList(Long productId) {
        List<ProductLadderPrice> ladderPriceList = productLadderPriceRepository.list(new LambdaQueryWrapper<ProductLadderPrice>()
                .eq(ProductLadderPrice::getProductId, productId));
        return LadderPriceConverter.convertToBoList(ladderPriceList);
    }

    /**
     * 根据商品id查询阶梯价
     *
     * @param productIdList 商品id
     * @return 阶梯价集合
     */
    public List<ProductLadderPriceBo> queryLadderPriceBoList(List<Long> productIdList) {
        List<ProductLadderPrice> ladderPriceList = productLadderPriceRepository.listByProductIds(productIdList);
        return LadderPriceConverter.convertToBoList(ladderPriceList);
    }

    /**
     * 根据商品id查询阶梯价审核
     *
     * @param productId 商品id
     * @return 阶梯价集合
     */
    public List<ProductLadderPriceBo> getLadderPriceAuditBoList(Long productId) {
        List<ProductLadderPriceAudit> ladderPriceList = productLadderPriceAuditRepository.list(new LambdaQueryWrapper<ProductLadderPriceAudit>()
                .eq(ProductLadderPriceAudit::getProductId, productId));
        return LadderPriceAuditConverter.convertToBoList(ladderPriceList);
    }


    /**
     * 处理阶梯价数组
     */
    public void handleLadderPrice(List<ProductLadderPriceBo> ladderPriceList) {
        if (CollectionUtils.isEmpty(ladderPriceList)) {
            return;
        }

        // 根据minBath 升序排列
        ladderPriceList =
                ladderPriceList.stream().sorted(Comparator.comparingInt(ProductLadderPriceBo::getMinBath)).collect(Collectors.toList());
        // 计算maxBatch
        for (int i = 0; i < ladderPriceList.size(); i++) {
            ProductLadderPriceBo ladderPriceBo = ladderPriceList.get(i);
            if (i == ladderPriceList.size() - 1) {
                ladderPriceBo.setMaxBath(Integer.MAX_VALUE);
            }
            else {
                ladderPriceBo.setMaxBath(ladderPriceList.get(i + 1).getMinBath() - 1);
            }
            if (ladderPriceBo.getMaxBath() < ladderPriceBo.getMinBath()) {
                throw new BusinessException("阶梯价设置错误");
            }
        }
    }

    /**
     * 获取最小阶梯价
     *
     * @param productId 商品id
     * @return 最小阶梯价
     */
    public BigDecimal getMinLadderPrice(Long productId) {
        return productLadderPriceRepository.getMinLadderPrice(productId);
    }

    /**
     * 获取最大阶梯价
     *
     * @param productId 商品id
     * @return 最大阶梯价
     */
    public BigDecimal getMaxLadderPrice(Long productId) {
        return productLadderPriceRepository.getMaxLadderPrice(productId);
    }
}
