package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.EsCommentSummaryBo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品基本信息
 *
 * <AUTHOR>
 * @date 2023/12/22 15:17
 */
@Setter
@Getter
public class ProductBaseInfoBo {

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 货号
     */
    private String productCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌logo
     */
    private String brandLogo;

    /**
     * 广告词
     */
    private String shortDescription;

    /**
     * 分类集合
     */
    private List<ProductCategoryBo> categoryList;

    /**
     * 销量
     */
    private int salesCount;

    /**
     * 商品图片集合
     */
    private List<String> imageList;

    /**
     * 是否收藏
     */
    private boolean collectStatus;

    /**
     * 默认收货地址
     */
    private ShippingAddressBo defaultShippingAddress;

    /**
     * 是否开启阶梯价格
     */
    private boolean whetherOpenLadder;

    /**
     * 是否是多规格
     */
    private boolean hasSku;

    /**
     * 规格1 别名
     */
    private String spec1Alias;

    /**
     * 规格2 别名
     */
    private String spec2Alias;

    /**
     * 规格3 别名
     */
    private String spec3Alias;

    /**
     * 阶梯价格
     */
    private List<ProductLadderPriceBo> ladderPriceList;

    /**
     * 规格组
     */
    private List<ProductSpecGroupBo> specGroupList;

    /**
     * sku集合
     */
    private List<ProductSkuBo> skuList;

    /**
     * 商品属性集合
     */
    private List<ProductAttributeBo> attributeList;

    /**
     * 活动信息(返回给前端)
     */
    private ProductActivityInfoBo activityInfo;

    /**
     * 是否可售
     */
    private boolean saleAble;

    /**
     * 评论汇总
     */
    private EsCommentSummaryBo commentSummary;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 销售价
     */
    private BigDecimal minSalePrice;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 预估价
     */
    private BigDecimal estimatePrice;

    /**
     * 最大购买量
     */
    private Integer maxBuyCount;

    /**
     * 倍数起购量
     */
    private Integer multipleCount;

    /**
     * 运费模板id
     */
    private Long freightTemplateId;

    /**
     * 运费模板信息
     */
    private FreightTemplateBo freightTemplateInfo;

    /**
     * 咨询数
     */
    private Integer consultCount;

    /**
     * 视频地址
     */
    private String videoPath;

    /**
     * 单位
     */
    private String measureUnit;

    /**
     * 最高售价
     */
    private BigDecimal maxSalePrice;

    /**
     * 价格范围
     */
    private String salePriceRange;

    /**
     * 商品状态 1-销售中 2-仓库中 3-草稿箱 4-违规下架 5-待审核 6-未通过
     */
    private Integer status;

    /**
     * 交易商品的状态 0-售罄 1-在售 2-已下架
     */
    private Integer tradeStatus;

    /**
     * 总库存
     */
    private long totalStock;

    /**
     * 限时购库存
     */
    private long flashSaleTotalStock;
    /**
     * 移动端商品描述图片列表
     */
    private List<String> descriptionPicList;
    /**
     * oe码
     */
    private String oeCode;
    /**
     * 品牌号
     */
    private String brandCode;
    /**
     * 零件质量
     */
    private Integer partQuality;
    /**
     * 质保日期
     */
    private Integer warrantyPeriod;
    /**
     * 适用车辆
     */
    private String adaptableCar;
    /**
     * 零件规格
     */
    private String partSpec;
    /**
     * 替换号
     */
    private String replaceNumber;
    /**
     * 备注
     */
    private String remark;
}
