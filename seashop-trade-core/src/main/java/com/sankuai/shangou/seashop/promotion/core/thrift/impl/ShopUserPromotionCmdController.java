package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.LockConstant;
import com.sankuai.shangou.seashop.promotion.core.service.PromotionService;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ShopUserPromotionCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/16/016
 * @description:
 */
@RestController
@RequestMapping("/shopUserPromotion")
public class ShopUserPromotionCmdController implements ShopUserPromotionCmdFeign {

    @Resource
    private PromotionService promotionService;

    @PostMapping(value = "/offSaleAllPromotion", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> offSaleAllPromotion(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("offSaleAllPromotion", request, req -> {
            req.checkParameter();
            String lockKey = String.format(LockConstant.PROMOTION_SHOP_LOCK_KEY, req.getId());
            LockHelper.lock(lockKey, () -> {
                promotionService.offSaleAllPromotion(request);
            });
            return new BaseResp();
        });
    }
}
