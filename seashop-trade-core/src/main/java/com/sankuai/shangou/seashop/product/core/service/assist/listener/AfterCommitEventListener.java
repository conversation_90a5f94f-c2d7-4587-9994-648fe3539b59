package com.sankuai.shangou.seashop.product.core.service.assist.listener;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.AbstractTransactionEvent;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.handler.AbstractHandler;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 事务提交后监听
 *
 * <AUTHOR>
 * @date 2024/01/25 11:46
 */
@Component
@Slf4j
public class AfterCommitEventListener extends AbstractEventListener {

    @Override
    @Async(value = "commonExecutor")
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AbstractTransactionEvent.class)
    public void listen(AbstractTransactionEvent event) {
        Class<? extends AbstractHandler> handlerClass = event.getHandler();
        AbstractHandler handler = SpringUtil.getBean(handlerClass);
        if (handler.support(event, getTransactionPhase())) {
            handler.handle(event);
        }
    }

    @Override
    public TransactionPhase getTransactionPhase() {
        return TransactionPhase.AFTER_COMMIT;
    }
}
