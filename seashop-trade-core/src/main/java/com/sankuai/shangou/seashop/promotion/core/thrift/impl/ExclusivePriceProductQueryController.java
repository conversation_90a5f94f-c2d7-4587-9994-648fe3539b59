package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.ExclusivePriceProductService;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductPageQryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceProductSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.ExclusivePriceProductQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@RestController
@RequestMapping("/exclusivePriceProduct")
public class ExclusivePriceProductQueryController implements ExclusivePriceProductQueryFeign {

    @Resource
    private ExclusivePriceProductService exclusivePriceProductService;

    @PostMapping(value = "/queryByParams", consumes = "application/json")
    @Override
    public ResultDto<ExclusivePriceProductSimpleResp> queryByParams(@RequestBody ExclusivePriceProductQueryReq request) throws TException {
        return null;
    }

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ExclusivePriceProductDto>> pageList(@RequestBody ExclusivePriceProductPageQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            return exclusivePriceProductService.pageList(req);
        });
    }

    @PostMapping(value = "/getShopValidExclusive", consumes = "application/json")
    @Override
    public ResultDto<ExclusivePriceProductSimpleResp> getShopValidExclusive(@RequestBody BaseIdReq shopIdReq) throws TException {
        return ThriftResponseHelper.responseInvoke("获取店铺有效的专享价商品", shopIdReq, req -> {
            shopIdReq.checkParameter();
            return exclusivePriceProductService.getShopValidExclusive(shopIdReq.getId());
        });
    }
}
