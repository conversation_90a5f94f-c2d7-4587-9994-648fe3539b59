package com.sankuai.shangou.seashop.product.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.ShopCategoryService;
import com.sankuai.shangou.seashop.product.core.service.model.ShopCategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.ShopCategoryQueryBo;
import com.sankuai.shangou.seashop.product.thrift.core.ShopCategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.QueryShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory.ShopCategoryIdsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory.ShopCategoryTreeResp;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:12
 */
@RestController
@RequestMapping("/shopCategory")
public class ShopCategoryQueryController implements ShopCategoryQueryFeign {

    @Resource
    private ShopCategoryService shopCategoryService;

    @PostMapping(value = "/queryShopCategory", consumes = "application/json")
    @Override
    public ResultDto<ShopCategoryTreeResp> queryShopCategory(@RequestBody QueryShopCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopCategory", request, req -> {
            req.checkParameter();

            List<ShopCategoryBo> shopBrandBos = shopCategoryService.queryShopCategory(JsonUtil.copy(req, ShopCategoryQueryBo.class));
            return ShopCategoryTreeResp.builder().result(JsonUtil.toJsonString(shopBrandBos)).build();
        });
    }

    @PostMapping(value = "/getSubShopCategoryIds", consumes = "application/json")
    @Override
    public ResultDto<ShopCategoryIdsResp> getSubShopCategoryIds(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getSubShopCategoryIds", request, req -> {
            req.checkParameter();

            List<Long> subShopCategoryIds = shopCategoryService.getSubShopCategoryIds(req.getId());
            return ShopCategoryIdsResp.builder().childIds(subShopCategoryIds).build();
        });
    }
}
