package com.sankuai.shangou.seashop.promotion.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.promotion.core.model.bo.QueryShopUserPromotionBo;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryProductPromotionReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductPromotionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopReductionOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopUserPromotionResp;

/**
 * @author: lhx
 * @date: 2023/11/16/016
 * @description:
 */
public interface PromotionService {

    /**
     * 查询用户可用的优惠券
     *
     * @param queryShopUserPromotionBo
     * @return
     */
    ShopUserPromotionResp queryPromotionData(QueryShopUserPromotionBo queryShopUserPromotionBo);

    /**
     * 查询订单可用的折扣活动
     *
     * @param request 入参
     * @return 订单可用折扣
     */
    ShopReductionOrderListResp queryShopPromotionByOrder(PromotionRecordOrderQueryReq request);

    /**
     * 下架所有的活动
     *
     * @param request
     */
    void offSaleAllPromotion(BaseIdReq request);

    /**
     * 查询商品所有的优惠活动
     *
     * @param req 入参
     * @return 商品优惠活动
     */
    ProductPromotionResp queryProductPromotion(QueryProductPromotionReq req);
}
