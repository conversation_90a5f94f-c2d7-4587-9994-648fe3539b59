package com.sankuai.shangou.seashop.product.core.service.assist.listener;

import javax.annotation.Resource;

import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.AbstractTransactionEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/25 14:25
 */
@Component
@Slf4j
public class TransactionEventPublisher {

    @Resource
    private ApplicationContext applicationContext;

    public void publish(AbstractTransactionEvent event) {
        applicationContext.publishEvent(event);
    }

}
