package com.sankuai.shangou.seashop.product.core.service.converter;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.hepler.ProductStatusHelper;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductEsBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/22 19:56
 */
public class ProductConverter {
    /**
     * 执行json copy 时需要忽略的字段
     */
    private static final String[] IGNORE_FIELDS = {"saleStatus", "auditStatus", "specNameIds"};

    public static Product convertToEntity(ProductBo bo, Long shopId, boolean draft) {
        return Optional.ofNullable(bo).map(item -> {
            Product product = JsonUtil.copy(item, Product.class);
            if (product.getAuditStatus() == null) {
                product.setAuditStatus(ProductEnum.AuditStatusEnum.WAIT_AUDIT.getCode());
            }
            if (product.getSaleStatus() == null) {
                product.setSaleStatus(draft ? ProductEnum.SaleStatusEnum.DRAFT.getCode() : ProductEnum.SaleStatusEnum.ON_SALE.getCode());
            }
            // 当OE码不为空时默认保存大写OE码
            if (StringUtils.isNotBlank(product.getOeCode())) {
                product.setOeCode(product.getOeCode().toUpperCase());
            }
            product.setShopId(shopId);
            product.setAddedDate(new Date());
            return product;
        }).orElse(null);
    }

    public static ProductEsBo convertToEsBo(Product product) {
        return Optional.ofNullable(product).map(item -> {
            ProductEsBo productEsBo = JsonUtil.copy(item, ProductEsBo.class, IGNORE_FIELDS);
            productEsBo.setSaleStatus(ProductEnum.SaleStatusEnum.getByCode(item.getSaleStatus()));
            productEsBo.setAuditStatus(ProductEnum.AuditStatusEnum.getByCode(item.getAuditStatus()));
            return productEsBo;
        }).orElse(null);
    }

    public static ProductEsBo convertToEsBo(ProductAudit product) {
        return Optional.ofNullable(product).map(item -> convertToEsBo(JsonUtil.copy(item, Product.class))).orElse(null);
    }

    public static ErpProductBo convertToErpBo(ProductPageBo product) {
        return Optional.ofNullable(product).map(item -> {
            ErpProductBo productEsBo = JsonUtil.copy(item, ErpProductBo.class, IGNORE_FIELDS);
            productEsBo.setSaleStatus(ProductEnum.SaleStatusEnum.getByCode(item.getSaleStatusCode()));
            productEsBo.setAuditStatus(ProductEnum.AuditStatusEnum.getByCode(item.getAuditStatusCode()));
            productEsBo.setProductStatus(ProductStatusHelper.getProductStatus(item.getSaleStatusCode(), item.getAuditStatusCode()));
            return productEsBo;
        }).orElse(null);
    }
}
