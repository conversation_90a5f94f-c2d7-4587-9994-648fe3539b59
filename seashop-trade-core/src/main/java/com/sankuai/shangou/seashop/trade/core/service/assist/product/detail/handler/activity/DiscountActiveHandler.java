package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountRuleBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/25 9:31
 */
@Slf4j
@Component
public class DiscountActiveHandler extends AbsActivityHandler {

    @Override
    public void handle(DealActivityContext context) {
        ActivityContext activityContext = context.getActivityContext();
        if (activityContext.getDiscountActive() == null) {
            return;
        }

        ProductActivityInfoBo activityInfo = context.getActivityInfo();
        activityInfo.setHasDiscountActive(true);
        List<RemoteDiscountRuleBo> discountRuleList = activityContext.getDiscountActive().getRuleList();
        activityInfo.setDiscountRuleList(discountRuleList);

        if (CollectionUtils.isEmpty(discountRuleList)) {
            return;
        }
        // 处理折扣活动 过滤出符合条件的折扣规则
        RemoteDiscountRuleBo bestDiscount  = discountRuleList.stream()
                .filter(item -> context.getEstimatePrice().compareTo(item.getQuota()) >= 0)
                .min(Comparator.comparing(RemoteDiscountRuleBo::getDiscount))
                .orElse(null);
        if (bestDiscount != null) {
            log.info("选择折扣活动:{}", JsonUtil.toJsonString(bestDiscount));
            context.setEstimatePrice(context.getEstimatePrice()
                    .multiply(bestDiscount.getDiscount()).divide(new BigDecimal(10), 2, BigDecimal.ROUND_HALF_UP));
            log.info("折扣后价格:{}", context.getEstimatePrice());
        }
    }

    @Override
    public int order() {
        return 5;
    }
}
