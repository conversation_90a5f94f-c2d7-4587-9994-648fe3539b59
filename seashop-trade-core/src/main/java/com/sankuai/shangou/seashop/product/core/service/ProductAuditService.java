package com.sankuai.shangou.seashop.product.core.service;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditCompareBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductAuditQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:06
 */
public interface ProductAuditService {

    /**
     * 批量审核商品
     *
     * @param auditBo 审核参数
     */
    void batchAuditProduct(ProductAuditBo auditBo);

    /**
     * 分页查询商品审核列表
     *
     * @param pageParam 分页参数
     * @param queryBo   查询参数
     * @return 商品审核列表
     */
    BasePageResp<ProductPageBo> pageProductAudit(BasePageParam pageParam, ProductQueryBo queryBo);

    /**
     * 根据商品id查询商品审核详情
     *
     * @param queryBo 查询入参
     * @return 商品审核详情
     */
    ProductAuditCompareBo queryProductAuditDetail(ProductAuditQueryBo queryBo);

    /**
     * 尝试自动审核
     *
     * @param productId 商品id
     */
    void tryAutoAudit(Long productId);
}
