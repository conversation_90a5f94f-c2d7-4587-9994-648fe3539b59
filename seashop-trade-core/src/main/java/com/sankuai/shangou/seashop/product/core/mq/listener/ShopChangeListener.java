package com.sankuai.shangou.seashop.product.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.mq.model.ShopChangeMessage;
import com.sankuai.shangou.seashop.product.core.service.ProductService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/20 16:11
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConstant.DEFAULT_NAMESPACE,
//        topic = MafkaConstant.TOPIC_OFF_SALE_SHOP_PRODUCT,
//        group = MafkaConstant.GROUP_OFF_SALE_SHOP_PRODUCT)
@RocketMQMessageListener(topic = MafkaConstant.TOPIC_OFF_SALE_SHOP_PRODUCT + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.GROUP_OFF_SALE_SHOP_PRODUCT + "_${spring.profiles.active}")
public class ShopChangeListener implements RocketMQListener<MessageExt> {

    @Resource
    private ProductService productService;


    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
//            String bodyStr = body.toString();
            log.info("【mafka消费】【店铺商品下架】消息内容为: {}", body);

            ShopChangeMessage shopChangeMessage = JsonUtil.parseObject(body, ShopChangeMessage.class);
            productService.offSaleAllProduct(shopChangeMessage.getShopId());
        }
        catch (Exception e) {
            log.error("【mafka消费】【店铺商品下架】消息内容为: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
