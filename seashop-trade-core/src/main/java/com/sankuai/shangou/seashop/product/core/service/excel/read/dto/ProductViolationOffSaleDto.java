package com.sankuai.shangou.seashop.product.core.service.excel.read.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.sankuai.shangou.seashop.base.eimport.RowReadResult;
import com.sankuai.shangou.seashop.base.eimport.anno.ExcelField;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/23 14:23
 */
@Getter
@Setter
public class ProductViolationOffSaleDto extends RowReadResult {

    @ExcelProperty(value = "*商品ID")
    @ExcelField(required = true, regex = ParameterConstant.PRODUCT_ID_CHECK_REGEX, regexMsg = "商品ID格式错误")
    private String productId;

    @ExcelProperty(value = "*下架原因")
    @ExcelField(required = true)
    private String auditReason;

    @ExcelIgnore
    private StringBuilder errBuilder = new StringBuilder();

}
