package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.math.BigDecimal;
import java.util.Map;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/20 17:36
 */
@Setter
@Getter
@Builder
public class ProductSkuPriceMapBo {

    /**
     * 商城价map
     */
    private Map<String, BigDecimal> salePriceMap;

    /**
     * 最低商城价map
     */
    private Map<Long, BigDecimal> minSalePriceMap;

    /**
     * 市场价map
     */
    private Map<Long, BigDecimal> marketPriceMap;

}
