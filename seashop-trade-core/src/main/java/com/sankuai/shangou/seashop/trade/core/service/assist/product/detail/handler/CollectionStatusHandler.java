package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.common.remote.UserFavoriteRemoteService;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsProductQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.ProductBaseContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 收藏状态查询
 *
 * <AUTHOR>
 * @date 2023/12/23 15:51
 */
@Component
@Slf4j
public class CollectionStatusHandler extends AbsProductQueryHandler {

    @Resource
    private UserFavoriteRemoteService userFavoriteRemoteService;

    @Override
    public void handle(ProductBaseContext context) {
        log.info("查询收藏状态, context: {}", context);
        context.getProduct().setCollectStatus(userFavoriteRemoteService.getFavoriteStatus(context.getProductId(), context.getUserId()));
    }

    @Override
    public int order() {
        return 4;
    }
}
