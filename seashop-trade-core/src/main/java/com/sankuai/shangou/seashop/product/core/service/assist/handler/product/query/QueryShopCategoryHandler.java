package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopCategoryRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 店铺分类查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:22
 */
@Component
@Slf4j
public class QueryShopCategoryHandler extends AbsProductHandler {

    @Resource
    private ProductAssist productAssist;
    @Resource
    private ShopCategoryRepository shopCategoryRepository;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        Long productId = context.getProductId();

        // 查询店铺分类
        List<Long> shopCategoryIds = productAssist.getShopCategoryIds(productId);
        productBo.setShopCategoryIdList(shopCategoryIds);
        productBo.setShopCategoryNameList(shopCategoryRepository.getShopCategoryNameMap(shopCategoryIds).values().stream().collect(Collectors.toList()));
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_SHOP_CATEGORY;
    }

}
