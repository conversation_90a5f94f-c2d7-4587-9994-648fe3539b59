package com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.handler;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.AbsActivityQueryHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.activity.context.ActivityContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 店铺满减查询处理器
 *
 * <AUTHOR>
 * @date 2023/12/23 10:35
 */
@Component
@Slf4j
public class FullReductionQueryHandler extends AbsActivityQueryHandler {

    @Resource
    private PromotionRemoteService promotionRemoteService;

    @Override
    protected void query(ActivityContext context) {
        log.info("查询店铺满减活动, context: {}", context);
        context.setFullReduction(promotionRemoteService.getFullReduction(context.getShopId()));
        log.info("查询店铺满减活动结果, context: {}, activity: {}", context, JsonUtil.toJsonString(context.getFullReduction()));
    }

    @Override
    protected Class<? extends AbsActivityQueryHandler> nextHandler() {
        return CouponQueryHandler.class;
    }

    /**
     * 满减和专享价、优惠券互斥
     * 店铺满减和优惠券虽然互斥, 但是参加哪种活动由客户选择, 所以也需要查询
     *
     * @param context 活动上下文对象
     * @return true 支持，false 不支持
     */
    @Override
    protected boolean support(ActivityContext context) {
        return context.getExclusivePrice() == null;
        // && context.getCouponList() == null;
    }
}
