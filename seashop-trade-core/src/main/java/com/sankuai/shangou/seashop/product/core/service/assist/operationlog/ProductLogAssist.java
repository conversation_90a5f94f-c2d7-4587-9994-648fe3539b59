package com.sankuai.shangou.seashop.product.core.service.assist.operationlog;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.BindDescriptionTemplateLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.BindFreightTemplateLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.BindRecommendProductLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.CategoryBindFormLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.ProductOnOffSaleLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.SaveBrandApplyLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateSafeStockLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateSequenceLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateShopSequenceLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateStockLogBo;
import com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateVirtualSalesLogBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyAuditBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductDeleteReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductOnOffSaleReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductViolationReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/04/11 14:50
 */
@Component
@Slf4j
public class ProductLogAssist {

    @Resource
    private BaseLogAssist baseLogAssist;

    /**
     * 记录商品变动日志
     */
    public void recordProductLog(Long userId,
                                 Long shopId,
                                 ProductBo oldProductBo,
                                 ProductBo newProductBo) {
        try {
            boolean isEdit = oldProductBo != null;

            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, isEdit ? ExaProEnum.MODIFY : ExaProEnum.INSERT,
                    isEdit ? "修改商品" : "新增商品",
                    userId, shopId, oldProductBo, newProductBo);
        }
        catch (Exception e) {
            log.warn("记录商品变动日志失败", e);
        }
    }

    /**
     * 记录库存变动日志
     */
    public void recordStockLog(Long userId,
                               Long shopId,
                               UpdateStockLogBo oldStockBo,
                               UpdateStockLogBo newStockBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "批量修改库存",
                    userId, shopId, oldStockBo, newStockBo);
        }
        catch (Exception e) {
            log.warn("记录批量修改库存失败", e);
        }
    }

    /**
     * 记录运费模板变动日志
     */
    public void recordFreightTemplateLog(Long userId,
                                         Long shopId,
                                         BindFreightTemplateLogBo oldTemplateBo,
                                         BindFreightTemplateLogBo newTemplateBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "批量关联运费模板",
                    userId, shopId, oldTemplateBo, newTemplateBo);
        }
        catch (Exception e) {
            log.warn("记录关联运费模板失败", e);
        }
    }

    /**
     * 记录警戒库存变动
     */
    public void recordSafeStockLog(Long userId,
                                   Long shopId,
                                   UpdateSafeStockLogBo oldSafeStockBo,
                                   UpdateSafeStockLogBo newSafeStockBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "批量修改警戒库存",
                    userId, shopId, oldSafeStockBo, newSafeStockBo);
        }
        catch (Exception e) {
            log.warn("记录批量修改警戒库存失败", e);
        }
    }

    public void recordSequenceLog(Long userId,
                                  Long shopId,
                                  UpdateSequenceLogBo oldSequenceBo,
                                  UpdateSequenceLogBo newSequenceBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "批量修改序号",
                    userId, shopId, oldSequenceBo, newSequenceBo);
        }
        catch (Exception e) {
            log.warn("记录批量修改序列失败", e);
        }
    }

    public void recordShopSequenceLog(Long operationUserId, Long shopId, UpdateShopSequenceLogBo oldSequenceBo, UpdateShopSequenceLogBo newSequenceBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "批量修改店铺序号",
                    operationUserId, shopId, oldSequenceBo, newSequenceBo);

        }
        catch (Exception e) {
            log.warn("记录批量修改店铺序列失败", e);
        }
    }

    public void recordBindDescriptionTemplateLog(Long userId, Long shopId,
                                                 BindDescriptionTemplateLogBo oldDescriptionTemplateBo,
                                                 BindDescriptionTemplateLogBo newDescriptionTemplateBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "批量关联版式",
                    userId, shopId, oldDescriptionTemplateBo, newDescriptionTemplateBo);
        }
        catch (Exception e) {
            log.warn("记录批量关联版式失败", e);
        }
    }

    public void recordBindRecommendProductLog(Long userId, Long shopId, BindRecommendProductLogBo oldRecommendProductBo, BindRecommendProductLogBo newRecommendProductBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "批量关联推荐商品",
                    userId, shopId, oldRecommendProductBo, newRecommendProductBo);
        }
        catch (Exception e) {
            log.warn("记录批量关联推荐商品失败", e);
        }
    }

    public void recordVirtualSalesLog(Long userId, Long shopId, UpdateVirtualSalesLogBo oldVirtualSalesBo, UpdateVirtualSalesLogBo newVirtualSalesBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "修改虚拟销量",
                    userId, shopId, oldVirtualSalesBo, newVirtualSalesBo);
        }
        catch (Exception e) {
            log.warn("记录虚拟销量失败", e);
        }
    }

    public void recordProductDeleteLog(ProductDeleteReq deleteReq) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MOVE,
                    "删除商品",
                    deleteReq.getOperationUserId(), deleteReq.getOperationShopId(), deleteReq, null);
        }
        catch (Exception e) {
            log.warn("记录商品删除日志失败", e);
        }
    }

    public void recordOnOffSaleLog(ProductOnOffSaleReq onOffSaleParam) {
        try {
            ProductOnOffSaleLogBo bo = JsonUtil.copy(onOffSaleParam, ProductOnOffSaleLogBo.class);
            bo.setOnSaleStr(bo.getOnSale() ? "是" : "否");

            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "上下架商品",
                    bo.getOperationUserId(), bo.getOperationShopId(), null, bo);
        }
        catch (Exception e) {
            log.warn("记录上下架商品日志失败", e);
        }
    }

    public void recordAuditBrandApplyLog(BrandApplyAuditBo auditBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "品牌申请审核",
                    auditBo.getOperationUserId(), auditBo.getOperationShopId(), null, auditBo);
        }
        catch (Exception e) {
            log.warn("记录品牌申请审核日志失败", e);
        }
    }

    public void recordBrandApplyLog(Long userId, Long shopId, SaveBrandApplyLogBo oldBrandApplyBo, SaveBrandApplyLogBo newBrandApplyBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT,
                    oldBrandApplyBo == null ? ExaProEnum.INSERT : ExaProEnum.MODIFY,
                    oldBrandApplyBo == null ? "新增品牌申请" : "编辑品牌申请",
                    userId, shopId, oldBrandApplyBo, newBrandApplyBo);
        }
        catch (Exception e) {
            log.warn("记录品牌申请日志失败", e);
        }
    }

    public void recordViolationOffSaleLog(ProductViolationReq violationReq) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT, ExaProEnum.MODIFY,
                    "违规下架商品",
                    violationReq.getOperationUserId(), violationReq.getOperationShopId(), null, violationReq);
        }
        catch (Exception e) {
            log.warn("记录违规下架日志失败");
        }
    }

    public void recordCategoryBindFormLog(Long userId, Long shopId, CategoryBindFormLogBo oldCategoryBindBo, CategoryBindFormLogBo newCategoryBindBo) {
        try {
            baseLogAssist.recordLog(ExaminModelEnum.PRODUCT,
                    ExaProEnum.MODIFY,
                    "类目关联自定义表单",
                    userId, shopId, oldCategoryBindBo, newCategoryBindBo);
        }
        catch (Exception e) {
            log.warn("记录类目关联自定义表单日志失败", e);
        }
    }
}
