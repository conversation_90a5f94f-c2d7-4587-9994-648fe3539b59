package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 19:28
 */
@Getter
@Setter
public class BindDescriptionTemplateItemLogBo {

    @PrimaryField
    @ExaminField(description = "商品ID")
    private Long productId;

    @ExaminField(description = "顶部版式id")
    private Long descriptionPrefixId;

    @ExaminField(description = "底部版式id")
    private Long descriptionSuffixId;

}
