package com.sankuai.shangou.seashop.product.core.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hishop.himall.report.api.request.ReportSourceProductReq;
import com.hishop.himall.report.api.service.ReportFeign;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.JobQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JobLogInfoResp;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.core.service.assist.ThreadPoolUtil;
import com.sankuai.shangou.seashop.product.core.task.param.RefreshProductAuditParam;
import com.sankuai.shangou.seashop.product.core.task.param.RefreshProductParam;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductShopCategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopCategoryRepository;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/04/07 10:43
 */
@Component
@Slf4j
public class ProductEsTask {

    @Resource
    private ProductEsBuildService productEsBuildService;
    @Resource
    private ProductAuditRepository productAuditRepository;
    @Resource
    private ProductRepository productRepository;

    @Resource
    private ReportFeign reportFeignService;

    @Resource
    private JobQueryFeign jobQueryFeign;

    @Resource
    private ProductShopCategoryRepository productShopCategoryRepository;

    @Resource
    private ShopCategoryRepository shopCategoryRepository;
    /**
     * 全量刷新商品es
     */
//    @Crane("refreshProductEs")
    @XxlJob("refreshProductEs")
    @XxlRegister(cron = "0 */10 * * * ?",
            author = "snow",
            jobDesc = "全量同步商品到es-手动")
    public void refreshProductEs(String param) {
        String paramStr = XxlJobHelper.getJobParam();
        RefreshProductParam productParam = JsonUtil.parseObject(paramStr, RefreshProductParam.class);
        Long lastProductId = productParam.getLastProductId();
        List<Long> productIdList = null;
        if (StringUtils.isNotEmpty(productParam.getProductIds())) {
            productIdList = Arrays.stream(productParam.getProductIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        }
        Long shopId = productParam.getShopId();
        Integer nearUpdateMinutes = productParam.getNearUpdateMinutes();
        DateTime now = DateUtil.date();

        Integer pageSize = CommonConstant.ES_QUERY_LIMIT;
        ThreadPoolExecutor executor = ThreadPoolUtil.COMMON_THREAD_POOL;

        boolean isEnd = false;
        while (!isEnd) {
            log.info("refreshProductEs task start, start productId: {}, pageSize:{}", lastProductId, pageSize);

            LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<Product>()
                .gt(lastProductId != null, Product::getProductId, lastProductId)
                .in(CollectionUtils.isNotEmpty(productIdList), Product::getProductId, productIdList)
                .eq(shopId != null, Product::getShopId, shopId);
            if (nearUpdateMinutes != null) {
                wrapper.ge(Product::getUpdateTime, DateUtil.offsetMinute(now, nearUpdateMinutes * -1));
            }
            wrapper.last("limit " + pageSize);

            List<Product> productList = productRepository.list(wrapper);
            CountDownLatch latch = new CountDownLatch(productList.size());
            productList.forEach(product -> {
                executor.execute(() -> {
                    try {
                        productEsBuildService.buildProductEs(product.getProductId());
                        latch.countDown();
                    }
                    catch (Exception e) {
                        log.error("refreshProductEs task error, product:{}", product, e);
                        latch.countDown();
                    }
                });
            });

            if (CollectionUtils.isEmpty(productList) || productList.size() < pageSize) {
                isEnd = true;
            }
            else {
                lastProductId = productList.get(productList.size() - 1).getProductId();
            }

            try {
                latch.await();
            }
            catch (Exception e) {
                log.error("refreshProductEs task error", e);
            }
        }
        log.info("refreshProductEs task end");
    }

    /**
     * 全量刷新商品审核es
     */
//    @Crane("refreshProductAuditEs")
    @XxlJob("refreshProductAuditEs")
    @XxlRegister(cron = "0 */10 * * * ?",
            author = "snow",
            jobDesc = "全量刷新商品审核es--手动")
    public void refreshProductAuditEs(String param) {
        String paramStr = XxlJobHelper.getJobParam();
        RefreshProductAuditParam productAuditParam = JsonUtil.parseObject(paramStr, RefreshProductAuditParam.class);
        Integer pageNo = CommonConstant.DEFAULT_PAGE_NO;
        Integer pageSize = CommonConstant.ES_QUERY_LIMIT;
        Integer nearUpdateMinutes = productAuditParam.getNearUpdateMinutes();
        DateTime now = DateUtil.date();

        // 如果不是全量更新 则查询最近10分钟的进行更新
        LambdaQueryWrapper<ProductAudit> wrapper = new LambdaQueryWrapper<>();
        if (nearUpdateMinutes != null) {
            wrapper.ge(ProductAudit::getUpdateTime, DateUtil.offsetMinute(now, nearUpdateMinutes * -1));
        }

        boolean isEnd = false;
        while (!isEnd) {
            log.info("refreshProductAuditEs task start, pageNo:{}, pageSize:{}", pageNo, pageSize);

            Page<ProductAudit> pageResult = PageHelper.startPage(pageNo, pageSize);
            productAuditRepository.list(wrapper);
            List<ProductAudit> productList = pageResult.getResult();
            productList.forEach(product -> {
                productEsBuildService.buildProductAuditEs(product.getProductId());
            });
            pageNo = pageNo + 1;
            Integer totalPage = pageResult.getPages();
            if (pageNo > totalPage) {
                isEnd = true;
            }
        }
        log.info("refreshProductAuditEs task end");
    }

    @XxlJob("reportProduct")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步商品报表")
    public void reportProduct() {
        log.info("【定时任务】【同步商品报表】...start...");
        long jobId = XxlJobHelper.getJobId();
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryFeign.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<Product> products = productRepository.getByUpdateTime(triggerTime);

        if (CollectionUtils.isNotEmpty(products)) {

            List<Long> productIds = products.stream().map(t -> t.getProductId()).collect(Collectors.toList());
            List<ProductShopCategory> productShopCategories = productShopCategoryRepository.listByProductIds(productIds);

            for (Product product : products) {
                List<ProductShopCategory> productShopCate = productShopCategories.stream().filter(t -> t.getProductId().equals(product.getProductId())).collect(Collectors.toList());
                List<Long> cIds = productShopCate.stream().map(t -> t.getShopCategoryId()).collect(Collectors.toList());
                List<ShopCategory> categories = shopCategoryRepository.listShopCategory(cIds);
                List<Long> parentCIds = categories.stream().map(t -> t.getParentCategoryId()).collect(Collectors.toList());
                List<ShopCategory> parentCategories = shopCategoryRepository.listShopCategory(parentCIds);

                ReportSourceProductReq productReq = new ReportSourceProductReq();
                if (CollectionUtil.isNotEmpty(categories)) {
                    productReq.setCategorySecond(categories.get(0).getName());
                }
                if (CollectionUtil.isNotEmpty(parentCategories)) {
                    productReq.setCategoryFirst(parentCategories.get(0).getName());
                }
                productReq.setProductId(product.getProductId());
                productReq.setProductName(product.getProductName());
                productReq.setProductSpu(product.getProductCode());
                productReq.setThumbnailUrl(product.getImagePath());

                productReq.setUpdateTime(LocalDateTime.ofInstant(product.getUpdateTime().toInstant(), ZoneId.systemDefault()));
                productReq.setCreateTime(LocalDateTime.ofInstant(product.getCreateTime().toInstant(), ZoneId.systemDefault()));
                try {
                    BaseResp res = ThriftResponseHelper.executeThriftCall(() -> reportFeignService.createSourceProduct(productReq));
                } catch (Exception ex) {
                    //日志
                    continue;
                }

            }
        }
    }
}
