package com.sankuai.shangou.seashop.product.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpQueryProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductEsBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductRichTextBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuMergeCombinationBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.QueryProductRichTextBo;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopSaleCountsDto;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.common.EsScrollClearReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.erp.ErpQueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.CountProductTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryLadderPriceReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductBasicReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductByIdReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductByIdsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductEsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductRichTextReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductStockBatchParam;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductStockPageParam;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryShopSaleCountsReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.RecommendProductsReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.erp.ErpProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.CountProductTemplateResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.GenProductCodeResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.MStatisticalProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductBasicResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductEsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductIdsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductLadderPriceResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductPageResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductRichTextResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductSkuMergeQueryResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductSkuStockListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductSkuStockResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.RecommendProductIdsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.RecommendProductsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ShopSaleCountsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.StatisticalProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductEsDto;

/**
 * <AUTHOR>
 * @date 2023/11/06 11:45
 */
@RestController
@RequestMapping("/product")
public class ProductQueryController implements ProductQueryFeign {

    @Resource
    private ProductService productService;
    @Resource
    private ProductEsBuildService productEsBuildService;

    @PostMapping(value = "/queryProductByTemplateId", consumes = "application/json")
    @Override
    public ResultDto<Boolean> queryProductByTemplateId(@RequestBody ProductTemplateReq productTemplateReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductByTemplateId", productTemplateReq, req -> {
            req.checkParameter();
            return productService.queryProductByTemplateId(req);
        });
    }

    @PostMapping(value = "/queryProductCountByTemplateId", consumes = "application/json")
    @Override
    public ResultDto<List<CountProductTemplateResp>> queryProductCountByTemplateId(@RequestBody CountProductTemplateReq template) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductCountByTemplateId", template, req -> productService.queryProductCountByTemplateId(req));
    }

    @PostMapping(value = "/getLadderPriceBoList", consumes = "application/json")
    @Override
    public ResultDto<ProductLadderPriceResp> getLadderPriceBoList(@RequestBody QueryLadderPriceReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getLadderPriceBoList", request, req -> {
            req.checkParameter();

            List<LadderPriceDto> ladderPriceBoList = productService.getLadderPriceBoList(req.getProductIds());
            return ProductLadderPriceResp.builder().ladderPriceList(ladderPriceBoList).build();
        });
    }

    @PostMapping(value = "/queryProduct", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ProductPageResp>> queryProduct(@RequestBody QueryProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProduct", request, req -> {
            req.checkParameter();

            BasePageParam pageParam = req.buildPage();
            pageParam.setScrollId(req.getScrollId());
            pageParam.setUseScroll(req.getUseScroll());
            pageParam.setKeepAliveMinutes(req.getKeepAliveMinutes());
            BasePageResp<ProductPageBo> pageResult = productService.pageProduct(pageParam, JsonUtil.copy(req, ProductQueryBo.class));
            return PageResultHelper.transfer(pageResult, ProductPageResp.class);
        });
    }

    @PostMapping(value = "/queryProductById", consumes = "application/json")
    @Override
    public ResultDto<ProductListResp> queryProductById(@RequestBody QueryProductByIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductById", request, req -> {
            req.checkParameter();

            List<ProductPageBo> productList = productService.queryProductById(req.getProductIds(), req.getShopId());
            return ProductListResp.builder().productList(JsonUtil.copyList(productList, ProductPageResp.class)).build();
        });
    }

    @PostMapping(value = "/queryProductDetail", consumes = "application/json")
    @Override
    public ResultDto<ProductDetailResp> queryProductDetail(@RequestBody ProductQueryDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductDetail", request, req -> {
            req.checkParameter();
            ProductBo productBo = productService.queryProductDetail(req.getProductId(), req.getShopId());
            return ProductDetailResp.builder().result(JsonUtil.copy(productBo, ProductDetailDto.class)).build();
        });
    }

    @PostMapping(value = "/queryProductSkuMerge", consumes = "application/json")
    @Override
    public ResultDto<ProductSkuMergeQueryResp> queryProductSkuMerge(@RequestBody ProductSkuQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductSku", request, req -> {
            req.checkParameter();
            ProductSkuMergeCombinationBo combBo = productService.queryProductSkuMerge(JsonUtil.copy(req, ProductSkuQueryBo.class));
            return JsonUtil.copy(combBo, ProductSkuMergeQueryResp.class);
        });
    }

    @PostMapping(value = "/queryProductForEsBuild", consumes = "application/json")
    @Override
    public ResultDto<ProductEsResp> queryProductForEsBuild(@RequestBody QueryProductEsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductForEsBuild", request, req -> {
            req.checkParameter();

            ProductEsBo productBo = productEsBuildService.queryProductForEsBuild(req.getProductId());
            return ProductEsResp.builder().product(JsonUtil.copy(productBo, ProductEsDto.class)).build();
        });
    }

    @PostMapping(value = "/queryProductForErp", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ErpProductResp>> queryProductForErp(@RequestBody ErpQueryProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductForErp", request, req -> {
            req.checkParameter();

            BasePageResp<ErpProductBo> pageResult = productService.queryProductForErp(req.buildPage(),
                    JsonUtil.copy(req, ErpQueryProductBo.class));
            return PageResultHelper.transfer(pageResult, ErpProductResp.class);
        });
    }

    @PostMapping(value = "/queryRecommendProductIds", consumes = "application/json")
    @Override
    public ResultDto<RecommendProductIdsResp> queryRecommendProductIds(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryRecommendProductIds", request, req -> {
            req.checkParameter();

            List<Long> productIds = productService.queryRecommendProductIds(req.getId());
            return RecommendProductIdsResp.builder().productIds(productIds).build();
        });
    }

    @PostMapping(value = "/queryProductIds", consumes = "application/json")
    @Override
    public ResultDto<ProductIdsResp> queryProductIds(@RequestBody QueryProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductIds", request, req -> {
            req.checkParameter();

            List<Long> productIds = productService.queryProductIds(JsonUtil.copy(req, ProductQueryBo.class));
            return ProductIdsResp.builder().productIds(productIds).build();
        });
    }

    @PostMapping(value = "/queryProductRichText", consumes = "application/json")
    @Override
    public ResultDto<ProductRichTextResp> queryProductRichText(@RequestBody QueryProductRichTextReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductRichText", request, req -> {
            req.checkParameter();

            ProductRichTextBo richTextBo = productService.queryProductRichText(JsonUtil.copy(req, QueryProductRichTextBo.class));
            return JsonUtil.copy(richTextBo, ProductRichTextResp.class);
        });
    }

    @PostMapping(value = "/queryProductBasic", consumes = "application/json")
    @Override
    public ResultDto<ProductBasicResp> queryProductBasic(@RequestBody QueryProductBasicReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductBasic", request, req -> {
            req.checkParameter();

            List<ProductBasicDto> productList = productService.queryProductBasic(req);
            return ProductBasicResp.builder().productList(productList).build();
        });
    }

    @PostMapping(value = "/pageProductBasic", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ProductBasicDto>> pageProductBasic(@RequestBody QueryProductBasicReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageProductBasic", request, req -> {
            // req.checkParameter();

            BasePageResp<ProductBasicDto> pageResult = productService.pageProductBasic(req);
            return pageResult;
        });
    }

    @GetMapping(value = "/generateProductCode")
    @Override
    public ResultDto<GenProductCodeResp> generateProductCode() throws TException {
        return ThriftResponseHelper.responseInvoke("generateProductCode", null, req -> {

            String productCode = productService.generateProductCode();
            return GenProductCodeResp.builder().productCode(productCode).build();
        });
    }

    @GetMapping(value = "/querySellerStatisticalProduct")
    @Override
    public ResultDto<StatisticalProductResp> querySellerStatisticalProduct(@RequestParam Long shopId) throws TException {
        if (shopId == null) {
            return ResultDto.newWithData(new StatisticalProductResp());
        }
        return ThriftResponseHelper.responseInvoke("queryStatisticalProduct", shopId, req -> productService.queryStatisticalProduct(req));
    }

    @GetMapping(value = "/queryMStatisticalProduct")
    @Override
    public ResultDto<MStatisticalProductResp> queryMStatisticalProduct() throws TException {
        return ThriftResponseHelper.responseInvoke("queryMStatisticalProduct", null, req -> productService.queryMStatisticalProduct());
    }

    @PostMapping(value = "/queryRecommendProducts", consumes = "application/json")
    @Override
    public ResultDto<RecommendProductsResp> queryRecommendProducts(@RequestBody RecommendProductsReq recommendProductsReq) throws TException {
        return ThriftResponseHelper.responseInvoke("queryRecommendProducts", recommendProductsReq, req -> productService.queryRecommendProducts(req));
    }

    @PostMapping(value = "/queryBatchStocks", consumes = "application/json")
    @Override
    public ResultDto<ProductSkuStockListResp> queryBatchStocks(@RequestBody QueryProductStockBatchParam req) throws TException {
        return ThriftResponseHelper.responseInvoke("queryBatchStocks", req, param -> {
            param.checkParameter();

            List<ProductSkuStockResp> skuStockList = productService.queryBatchStocks(param.getSkuAutoIds(), param.getSkuCodes(), param.getShopId());
            return ProductSkuStockListResp.builder().skuStockList(skuStockList).build();
        });
    }

    @PostMapping(value = "/queryPageStocks", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ProductSkuStockResp>> queryPageStocks(@RequestBody QueryProductStockPageParam req) throws TException {
        BasePageParam pageParam = new BasePageParam();
        pageParam.setPageNum(req.getPageNo());
        pageParam.setPageSize(req.getPageSize());
        return ThriftResponseHelper.responseInvoke("queryPageStocks", req,
                queryProductStockBatchParam -> {
                    queryProductStockBatchParam.checkParameter();
                    return productService.queryShopStocksPage(pageParam, queryProductStockBatchParam.getShopId());
                });
    }

    @PostMapping(value = "/queryShopSaleCounts", consumes = "application/json")
    @Override
    public ResultDto<ShopSaleCountsResp> queryShopSaleCounts(@RequestBody QueryShopSaleCountsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopSaleCounts", request, req -> {
            req.checkParameter();

            ShopSaleCountsDto shopSaleCountsDto = productService.queryShopSaleCounts(req.getShopId());
            return JsonUtil.copy(shopSaleCountsDto, ShopSaleCountsResp.class);
        });
    }

    @PostMapping(value = "/queryProductByIds", consumes = "application/json")
    @Override
    public ResultDto<List<ProductPageResp>> queryProductByIds(@RequestBody QueryProductByIdsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductByIds", request, req -> {
            req.checkParameter();
            return productService.queryProductByIds(req);
        });
    }

    @PostMapping(value = "/clearScrollId", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> clearScrollId(@RequestBody EsScrollClearReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("clearScrollId", request, req -> {
            req.checkParameter();

            productService.clearScrollId(req.getScrollId());
            return new BaseResp();
        });
    }
}
