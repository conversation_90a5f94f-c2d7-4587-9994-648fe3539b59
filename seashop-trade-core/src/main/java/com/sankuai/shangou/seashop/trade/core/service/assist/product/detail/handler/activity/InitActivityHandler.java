package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.handler.activity;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.AbsActivityHandler;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.context.DealActivityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductActivityInfoBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 初始化活动处理器
 *
 * <AUTHOR>
 * @date 2023/12/25 9:23
 */
@Slf4j
@Component
public class InitActivityHandler extends AbsActivityHandler {

    @Override
    public void handle(DealActivityContext context) {
        // 初始化活动信息
        context.setActivityInfo(new ProductActivityInfoBo());
    }

    @Override
    public int order() {
        return -100;
    }
}
