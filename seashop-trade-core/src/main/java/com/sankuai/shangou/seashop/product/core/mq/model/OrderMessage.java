package com.sankuai.shangou.seashop.product.core.mq.model;

import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderMessage {

    private String orderId;
    /**
     * 订单事件。{@link  OrderMessageEventEnum}
     */
    private String orderEventName;

}
