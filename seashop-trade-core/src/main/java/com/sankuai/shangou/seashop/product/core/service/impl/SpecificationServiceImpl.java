package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductService;
import com.sankuai.shangou.seashop.product.core.service.SpecificationService;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.SpecName;
import com.sankuai.shangou.seashop.product.dao.core.domain.SpecValue;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductMapper;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SpecNameMapper;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SpecValueMapper;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SpecificationRepository;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateNameReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateValueReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecValueDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecificationReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationNameResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationValueResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class SpecificationServiceImpl implements SpecificationService {

    @Resource
    private SpecificationRepository specificationRepository;
    @Resource
    private SpecValueMapper specValueMapper;
    @Resource
    private SpecNameMapper specNameMapper;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private EsProductService esProductService;

    @Override
    @Transactional
    public void create(SpecificationReq request) {
        SpecName name = new SpecName();
        name.setShopId(request.getShopId());
        name.setSpecName(request.getName());
        name.setSpecAlias(request.getAlias());
        specNameMapper.insert(name);
        List<SpecValue> values = request.getValues().stream().map(item -> {
            SpecValue value = new SpecValue();
            value.setShopId(name.getShopId());
            value.setNameId(name.getId());
            value.setValue(item.getValue());
            return value;
        }).collect(Collectors.toList());
        // 批量插入SpecValue
        specValueMapper.insertBatchSomeColumn(values);
    }

    @Override
    @Transactional
    public void save(SpecificationReq req) {
        SpecName name = specNameMapper.selectById(req.getId());
        name.setSpecName(req.getName());
        name.setSpecAlias(req.getAlias());
        specNameMapper.updateById(name);
        List<SpecValue> values = specValueMapper.selectList(new QueryWrapper<SpecValue>().eq("name_id", req.getId()));
        List<String> new_str = req.getValues().stream().map(SpecValueDto::getValue).collect(Collectors.toList());
        List<String> old_str = values.stream().map(SpecValue::getValue).collect(Collectors.toList());
        List<SpecValue> new_list = req.getValues().stream().filter(item -> !old_str.contains(item.getValue()))
            .map(item -> {
                SpecValue value = new SpecValue();
                value.setNameId(name.getId());
                value.setValue(item.getValue());
                return value;
            }).collect(Collectors.toList());
        List<Long> old_list = values.stream().filter(item -> !new_str.contains(item.getValue())).map(item -> item.getId()).collect(Collectors.toList());
        if (!new_list.isEmpty()) {
            new_list.forEach(item -> {
                item.setShopId(name.getShopId());
                item.setNameId(name.getId());
                specValueMapper.insert(item);
            });
        }
        if (!old_list.isEmpty()) {
            specValueMapper.deleteBatchIds(old_list);
        }
    }

    @Override
    public BasePageResp<SpecificationResp> query(SpecificationReq request) {
        Page<SpecName> result = PageHelper.startPage(request.buildPage());
        QueryWrapper<SpecName> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shop_id", request.getShopId());
        specNameMapper.selectList(queryWrapper);
        List<Long> list = result.stream().map(SpecName::getId).collect(Collectors.toList());
        List<SpecValue> values;
        if (CollUtil.isNotEmpty(list)) {
            values = specValueMapper.selectList(new QueryWrapper<SpecValue>().in("name_id", list));
        }
        else {
            values = new ArrayList<>();
        }
        // 从ES 中去聚合，这种数据可以允许延迟
        Map<Long, Long> productCountMap = countBySpecNameIds(list);
        return PageResultHelper.transfer(result, item -> {
            SpecificationResp resp = JsonUtil.copy(item, SpecificationResp.class);
            resp.setName(item.getSpecName());
            resp.setAlias(item.getSpecAlias());
            List<SpecValue> myValues = values.stream().filter(p -> p.getNameId().equals(item.getId())).collect(Collectors.toList());
            resp.setValues(JsonUtil.copyList(myValues, SpecValueDto.class));
            Long products = productCountMap.getOrDefault(item.getId(), 0L);
            resp.setProducts(products);
            return resp;
        });
    }

    private Map<Long, Long> countBySpecNameIds(List<Long> specNameIds) {
        try {
            return esProductService.countBySpecNameIds(specNameIds);
        }
        catch (Exception e) {
            log.error("[规格查询] es 聚合商品数量查询失败", e);
            return Collections.emptyMap();
        }
    }

    @Override
    public List<SpecificationNameResp> getNames(Long shopId) {
        QueryWrapper<SpecName> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shop_id", shopId);
        List<SpecName> names = specNameMapper.selectList(queryWrapper);
        return JsonUtil.copyList(names, SpecificationNameResp.class, (source, target) -> {
            target.setName(source.getSpecName());
            target.setAlias(source.getSpecAlias());
        });
    }

    @Override
    public List<SpecificationValueResp> getValues(Long nameId) {
        QueryWrapper<SpecValue> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name_id", nameId);
        List<SpecValue> values = specValueMapper.selectList(queryWrapper);
        return JsonUtil.copyList(values, SpecificationValueResp.class);
    }


    @Override
    public void remove(Long shopId, Long nameId) {
        specNameMapper.deleteById(nameId);
        QueryWrapper<SpecValue> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name_id", nameId);
        specValueMapper.delete(queryWrapper);
    }


    @Override
    public Long createName(CreateNameReq req) {
        boolean exist = specificationRepository.existName(req.getShopId(), req.getName());
        AssertUtil.throwIfTrue(exist, "规格名已存在");

        return specificationRepository.createName(req.getShopId(), req.getName());
    }

    @Override
    public Long createValue(CreateValueReq req) {
        boolean exist = specificationRepository.existValue(req.getNameId(), req.getValue());
        AssertUtil.throwIfTrue(exist, "规格值已存在");

        // todo 此时可能specName 已经被删除了, 但是新增一条 specValue 也没有问题
        return specificationRepository.createValue(req.getShopId(), req.getNameId(), req.getValue());
    }


}
