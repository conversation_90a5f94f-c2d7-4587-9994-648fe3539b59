package com.sankuai.shangou.seashop.promotion.core.service.impl;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.common.enums.PromotionResultCodeEnum;
import com.sankuai.shangou.seashop.promotion.core.model.bo.FullReductionQueryBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.FullReductionSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.FullReductionService;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.PromotionLogAssist;
import com.sankuai.shangou.seashop.promotion.core.service.assist.operationLog.bo.EndFullReduceLogBo;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FullReduction;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ActiveBaseDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.FullReductionSimpleDto;
import com.sankuai.shangou.seashop.promotion.dao.core.repository.FullReductionRepository;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@Service
@Slf4j
public class FullReductionServiceImpl implements FullReductionService {

    @Resource
    private FullReductionRepository fullReductionRepository;
    @Resource
    private PromotionLogAssist promotionLogAssist;

    @Override
//    @ExaminProcess(processModel = ExaminModelEnum.PROMOTION, processType = ExaProEnum.MODIFY, serviceMethod = "", dto = FullReductionSaveBo.class, entity = FullReduction.class)
    public void save(FullReductionSaveBo saveBo) {
        log.info("save-saveBo:{}", saveBo);

        FullReduction saveFullReduction;
        FullReductionDto oldFullReductionModel = null;
        if (null != saveBo.getId()) {
            // 修改
            saveFullReduction = fullReductionRepository.getById(saveBo.getId());
            if (null == saveFullReduction) {
                throw new BusinessException(PromotionResultCodeEnum.FULL_REDUCTION_NOT_FIND.getCode(), PromotionResultCodeEnum.FULL_REDUCTION_NOT_FIND.getMsg());
            }
            // 判断结束时间是否小于当前时间，如果小于则无法修改该活动
            Date endTime = saveFullReduction.getEndTime();
            if (endTime.before(new Date())) {
                throw new BusinessException(PromotionResultCodeEnum.FULL_REDUCTION_HAS_END.getCode(), PromotionResultCodeEnum.FULL_REDUCTION_HAS_END.getMsg());
            }
            oldFullReductionModel = JsonUtil.copy(saveFullReduction, FullReductionDto.class);
        } else {
            // 新增
            saveFullReduction = new FullReduction();
        }

        BeanUtils.copyProperties(saveBo, saveFullReduction, "id");

        // 判断改时间段是否有活动
        FullReductionParamDto paramDto = FullReductionParamDto.builder()
                .notEqId(saveFullReduction.getId())
                .startTime(saveBo.getStartTime())
                .endTime(saveBo.getEndTime())
                .shopId(saveBo.getShopId()).build();
        int count = fullReductionRepository.countByTime(paramDto);
        if (count > 0) {
            throw new BusinessException(PromotionResultCodeEnum.FULL_REDUCTION_HAS_ACTIVE.getCode(), PromotionResultCodeEnum.FULL_REDUCTION_HAS_ACTIVE.getMsg());
        }
        fullReductionRepository.saveOrUpdate(saveFullReduction);

        // 记录操作日志
        promotionLogAssist.recordSaveFullReductionLog(saveBo.getOperationUserId(), saveBo.getOperationShopId(),
                oldFullReductionModel, JsonUtil.copy(saveBo, FullReductionDto.class));
    }

    @Override
    public void endActive(BaseIdReq idReq) {
        FullReduction fullReduction = fullReductionRepository.getById(idReq.getId());
        if (null == fullReduction) {
            throw new BusinessException(PromotionResultCodeEnum.FULL_REDUCTION_NOT_FIND.getCode(), PromotionResultCodeEnum.FULL_REDUCTION_NOT_FIND.getMsg());
        }
        Date now = new Date();
        Date endTime = fullReduction.getEndTime();
        if (now.after(endTime)) {
            throw new BusinessException(PromotionResultCodeEnum.FULL_REDUCTION_HAS_END.getCode(), PromotionResultCodeEnum.FULL_REDUCTION_HAS_END.getMsg());
        }
        fullReduction.setEndTime(now);
        fullReductionRepository.saveOrUpdate(fullReduction);

        // 记录操作日志
        promotionLogAssist.recordEndFullReductionLog(idReq.getOperationUserId(),
                idReq.getOperationShopId(), JsonUtil.copy(idReq, EndFullReduceLogBo.class));
    }

    @Override
    public BasePageResp<FullReductionSimpleDto> pageList(FullReductionQueryBo queryBo) {
        BasePageResp<FullReductionSimpleDto> pageResp = fullReductionRepository.pageList(queryBo.buildPage(),
                JsonUtil.copy(queryBo, FullReductionParamDto.class));
        pageResp.getData().parallelStream().forEach(
                data -> {
                    ActiveBaseDto.opStatus(data);
                }
        );
        return pageResp;
    }

    @Override
    public FullReductionDto getById(Long id, Long shopId) {
        FullReduction fullReduction = fullReductionRepository.getById(id);
        if (null == fullReduction) {
            throw new BusinessException(PromotionResultCodeEnum.FULL_REDUCTION_NOT_FIND.getCode(), PromotionResultCodeEnum.FULL_REDUCTION_NOT_FIND.getMsg());
        }
        if (shopId != null && shopId > 0 && !fullReduction.getShopId().equals(shopId)) {
            throw new BusinessException(PromotionResultCodeEnum.AUTHORITY_ERROR.getCode(), PromotionResultCodeEnum.AUTHORITY_ERROR.getMsg());
        }
        FullReductionDto dto = JsonUtil.copy(fullReduction, FullReductionDto.class);
        return dto;
    }

    @Override
    public FullReductionResp queryByShopId(ShopIdReq request) {
        FullReduction fullReduction = fullReductionRepository.getByNowAndShopId(request.getShopId());
        if (null == fullReduction) {
            return null;
        }
        return JsonUtil.copy(fullReduction, FullReductionResp.class);
    }

    @Override
    public FullReductionResp currentEnableFullReduction() {
        FullReduction fullReduction = fullReductionRepository.currentEnableFullReduction();
        if (null == fullReduction) {
            return null;
        }
        return JsonUtil.copy(fullReduction, FullReductionResp.class);
    }
}
