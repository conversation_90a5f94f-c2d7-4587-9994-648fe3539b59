package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.pass;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendProductChangeEvent;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImage;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImageAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/24 16:13
 */
@Component
@Slf4j
public class ProductImageAuditPassHandler extends AbsProductAuditPassHandler {

    @Resource
    private ProductImageRepository productImageRepository;
    @Resource
    private ProductImageAuditRepository productImageAuditRepository;
    @Resource
    private ProductAssist productAssist;
    @Resource
    private TransactionEventPublisher transactionEventPublisher;

    @Override
    protected void handle(ProductContext context) {
        log.info("【商品审核通过】保存商品图片==开始, context={}", context);

        Long productId = context.getProductId();

        // 获取旧的图片
        List<String> oldImageList = productImageRepository.listImagesByProductId(productId);

        // 获取新的图片
        List<ProductImageAudit> imgList = productImageAuditRepository
                .list(new LambdaQueryWrapper<ProductImageAudit>().eq(ProductImageAudit::getProductId, productId));
        List<String> newImageList = imgList.stream().map(ProductImageAudit::getImageUrl).collect(Collectors.toList());
        boolean flag = productAssist.compareImgList(newImageList, oldImageList);
        if (flag) {
            log.info("【商品审核通过】图片未变动, 不需要保存, context={}", context);
            return;
        }

        // 发送图片变动事件
        SendProductChangeEvent event = SendProductChangeEvent
                .build(productId, context.getShopId(), ProductSourceEnum.MALL, ProductChangeType.CHANGE_MAIN_PIC);
        transactionEventPublisher.publish(event);

        // 先删除 后新增
        productImageRepository.remove(new LambdaQueryWrapper<ProductImage>().eq(ProductImage::getProductId, productId));
        if (!CollectionUtils.isEmpty(imgList)) {
            productImageRepository.saveBatch(JsonUtil.copyList(imgList, ProductImage.class));
        }
        log.info("【商品审核通过】保存商品图片==结束, context={}", context);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.PASS_PRODUCT_IMAGE_AUDIT;
    }

}
