package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询类目入参")
public class QueryCategoryForApplyReq extends BaseThriftDto {

    @Schema(description = "最大深度")
    private Integer maxDepth;

    @Schema(description = "父类目id")
    private Long parentId;

    @Schema(description = "展示状态 0-全部 1-展示开启 2-展示关闭")
    private Integer showStatus;

    @Schema(description = "排除没有下级的类目")
    private Boolean filterNoChildren;

    @Schema(description = "店铺id")
    private Long shopId;


}
