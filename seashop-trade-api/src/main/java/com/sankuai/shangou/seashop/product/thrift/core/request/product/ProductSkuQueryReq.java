package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/06 11:48
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询商品集合入参")
public class ProductSkuQueryReq extends BaseParamReq {

    @Schema(description = "skuId的集合")
    private List<String> skuIdList;

    @Schema(description = "是否需要阶梯价")
    private Boolean needLadderFlag;

    @Schema(description = "sku自增id集合")
    private List<Long> skuAutoIds;

    @Schema(description = "商品id集合")
    private List<Long> productIds;

    @Schema(description = "是否过滤掉阶梯价商品")
    private Boolean filterLadderPrice;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "skuId的集合 跟自增id,规格编号至少传一个")
    private List<String> skuCodes;

    @Override
    public void checkParameter() {
        if (CollectionUtils.isNotEmpty(skuIdList)) {
            AssertUtil.throwInvalidParamIfTrue(skuIdList.size() > ParameterConstant.QUERY_LIST_LIMIT,
                    String.format("【skuIdList】不能超过%s个", ParameterConstant.QUERY_LIST_LIMIT));
        }
        if (CollectionUtils.isNotEmpty(skuAutoIds)) {
            AssertUtil.throwInvalidParamIfTrue(skuAutoIds.size() > ParameterConstant.QUERY_LIST_LIMIT,
                    String.format("【skuAutoIds】不能超过%s个", ParameterConstant.QUERY_LIST_LIMIT));
        }
        if (CollectionUtils.isNotEmpty(skuCodes)) {
            AssertUtil.throwInvalidParamIfTrue(skuCodes.size() > ParameterConstant.QUERY_LIST_LIMIT,
                    String.format("【skuCodes】不能超过%s个", ParameterConstant.QUERY_LIST_LIMIT));
        }
        if (CollectionUtils.isNotEmpty(productIds)) {
            AssertUtil.throwInvalidParamIfTrue(productIds.size() > ParameterConstant.QUERY_LIST_LIMIT,
                    String.format("【productIds】不能超过%s个", ParameterConstant.QUERY_LIST_LIMIT));
        }
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(skuIdList) && CollectionUtil.isEmpty(skuAutoIds)
                        && CollectionUtil.isEmpty(skuCodes) && CollectionUtil.isEmpty(productIds),
                "skuIdList/skuAutoIds/skuCodes/productIds 不能同时为空");
    }


}
