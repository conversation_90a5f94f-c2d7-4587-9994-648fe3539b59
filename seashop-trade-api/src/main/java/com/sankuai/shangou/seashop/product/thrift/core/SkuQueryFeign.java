package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 14:51
 * @Description: sku查询服务
 */
@FeignClient(name = "himall-trade", contextId = "SkuQueryFeign", path = "/himall-trade/sku", url = "${himall-trade.dev.url:}")
public interface SkuQueryFeign {

    /**
     * 查询sku规格数据
     */
    @PostMapping(value = "/querySkuList", consumes = "application/json")
    ResultDto<SkuListResp> querySkuList(@RequestBody SkuQueryReq request) throws TException;
}
