package com.sankuai.shangou.seashop.product.thrift.core.response.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "保证金配置列表")
public class CategoryCashDepositListResp {

    @Schema(description = "保证金配置列表")
    private List<CategoryCashDepositResp> list;


}
