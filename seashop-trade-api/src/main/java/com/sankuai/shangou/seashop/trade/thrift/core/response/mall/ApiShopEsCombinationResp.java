package com.sankuai.shangou.seashop.trade.thrift.core.response.mall;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Data
@ToString
@Schema(description = "店铺ES信息返回参数列表")
@AllArgsConstructor
@NoArgsConstructor
public class ApiShopEsCombinationResp {

    @Schema(description = "店铺列表")
    private BasePageResp<ApiShopEsResp> shopList;

    @Schema(description = "店铺品牌列表")
    private List<ApiShopBrandEsResp> shopBrandList;

    @Schema(description = "店铺经营类目列表")
    private List<ApiBusinessCategoryEsResp> businessCategoryList;

    @Schema(description = "店铺品牌id列表")
    private List<Long> shopBrandIds;

    @Schema(description = "店铺经营类目id列表")
    private List<Long> categoryIds;


}
