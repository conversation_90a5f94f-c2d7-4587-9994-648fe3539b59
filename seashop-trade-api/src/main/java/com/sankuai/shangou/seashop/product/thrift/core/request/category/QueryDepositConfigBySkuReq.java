package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "根据SKU查询保证金配置入参")
public class QueryDepositConfigBySkuReq extends BaseParamReq {

    @Schema(description = "skuId", required = true)
    private List<String> skuIdList;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(skuIdList)) {
            throw new InvalidParamException("skuIdList不能为空");
        }
    }


}
