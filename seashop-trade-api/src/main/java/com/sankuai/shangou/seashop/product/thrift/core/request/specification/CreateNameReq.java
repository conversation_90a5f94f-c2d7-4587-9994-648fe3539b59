package com.sankuai.shangou.seashop.product.thrift.core.request.specification;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

@Data
public class CreateNameReq {
    private Long shopId;
    private String name;

    public void checkParameter() {
        AssertUtil.throwIfNull(shopId, "shopId 不能为空");
        AssertUtil.throwIfTrue(StrUtil.isEmpty(name), "规格名称不能为空");
    }
}
