package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@Schema(description = "限时购活动响应体")
@Data
public class MallFlashSaleResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "活动名称")
    private String title;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "跳转地址")
    private String urlPath;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "商品主图")
    private String imagePath;

    @Schema(description = "活动开始日期")
    private Date beginDate;

    @Schema(description = "活动结束日期")
    private Date endDate;

    @Schema(description = "预热时间（店铺配置）")
    private Integer preheat;

    @Schema(description = "是否允许正常购买（店铺配置）")
    private Boolean normalPurchaseFlag;

    @Schema(description = "商城价格")
    private BigDecimal salePrice;

    @Schema(description = "最小价格")
    private BigDecimal minPrice;

    @Schema(description = "销售数量")
    private Integer saleCount;

    @Schema(description = "活动开始剩余时间（单位：秒）")
    private Long startRemainingTime = 0L;


}
