package com.sankuai.shangou.seashop.trade.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewBuyNowReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewCollocationOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewFlashSaleOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.UserShoppingCartResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * 提供商家购物车查询功能
 */
@FeignClient(name = "himall-trade",contextId = "ShoppingCartQueryFeign", path = "/himall-trade/shoppingCart", url = "${himall-trade.dev.url:}")
public interface ShoppingCartQueryFeign {

    /**
     * 获取指定商家的购物车列表
     *
     * @param userId 商家用户ID，必填
     * @return 商家购物车列表，按照店铺分组返回，并且失效的商品单独分组
     */
    @GetMapping(value = "/getUserShoppingCartList")
    ResultDto<UserShoppingCartResp> getUserShoppingCartList(@RequestParam Long userId) throws TException;

    /**
     * 查询商家购物车数量
     * <p>这里的数量是sku的数量，包括非销售中的，不是商品的数量，因为商品的数量ToB端的数据可能比较大，不好显示</p>
     */
    @GetMapping(value = "/getUserShoppingCartCount")
    ResultDto<Integer> getUserShoppingCartCount(@RequestParam Long userId) throws TException;

    /**
     * 预览订单
     * <p>基于前端选择提交的sku获取预览订单信息</p>
     */
    @PostMapping(value = "/previewOrder", consumes = "application/json")
    ResultDto<PreviewOrderResp> previewOrder(@RequestBody PreviewOrderReq previewOrderReq) throws TException;

    /**
     * 限时购预览订单
     * <p>限时购直接从商品详情页加购进入预览订单</p>
     */
    @PostMapping(value = "/previewFlashSaleOrder", consumes = "application/json")
    ResultDto<PreviewOrderResp> previewFlashSaleOrder(@RequestBody PreviewFlashSaleOrderReq previewOrderReq) throws TException;

    /**
     * 组合购预览订单
     */
    @PostMapping(value = "/previewCollocationOrder", consumes = "application/json")
    ResultDto<PreviewOrderResp> previewCollocationOrder(@RequestBody PreviewCollocationOrderReq previewOrderReq) throws TException;

    /**
     * 立即购买预览订单
     */
    @PostMapping(value = "/previewBuyNowOrder", consumes = "application/json")
    ResultDto<PreviewOrderResp> previewBuyNowOrder(@RequestBody PreviewBuyNowReq previewOrderReq) throws TException;

    /**
     *  校验购物车是否可以提交
     */
    @PostMapping(value = "/checkCanSubmit", consumes = "application/json")
    ResultDto<BaseResp> checkCanSubmit(@RequestBody PreviewOrderReq previewOrderReq) throws TException;
}
