package com.sankuai.shangou.seashop.product.thrift.core.event.product;

import java.io.Serializable;
import java.util.Date;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/01/03 12:00
 */
@Setter
@Getter
public class ProductChangeEvent implements Serializable {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品变更类型
     */
    private ProductChangeType changeType;

    /**
     * 变动来源
     */
    private ProductSourceEnum source;

    /**
     * 变更时间
     */
    private Date date;

    /**
     * 审核原因
     */
    private String auditReason;

}
