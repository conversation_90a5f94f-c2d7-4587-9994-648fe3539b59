package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.LadderPriceDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/22 17:36
 */
@Data
@Schema(description = "阶梯价")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductLadderPriceResp extends BaseThriftDto {

    @Schema(description = "阶梯价集合")
    private List<LadderPriceDto> ladderPriceList;


}
