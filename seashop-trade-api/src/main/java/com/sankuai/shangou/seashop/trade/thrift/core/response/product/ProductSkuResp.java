package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:46
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "sku返回值")
public class ProductSkuResp extends BaseThriftDto {

    @Schema(description = "sku自增id")
    private Long skuAutoId;

    @Schema(description = "skuId")
    private String skuId;

    @Schema(description = "规格1值")
    private String spec1Value;

    @Schema(description = "规格2值")
    private String spec2Value;

    @Schema(description = "规格3值")
    private String spec3Value;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "upc/69码")
    private String barCode;

    @Schema(description = "货号")
    private String skuCode;

    @Schema(description = "限时购价格")
    private BigDecimal flashSalePrice;

    @Schema(description = "限时购库存")
    private Long flashSaleStock;

    @Schema(description = "限时购限购数量")
    private Integer flashSaleLimit;

    @Schema(description = "sku图片")
    private String showPic;


    public String getSpec1Value() {
        return spec1Value;
    }

    public void setSpec1Value(String spec1Value) {
        this.spec1Value = spec1Value;
    }

    public String getSpec2Value() {
        return spec2Value;
    }

    public void setSpec2Value(String spec2Value) {
        this.spec2Value = spec2Value;
    }

    public String getSpec3Value() {
        return spec3Value;
    }

    public void setSpec3Value(String spec3Value) {
        this.spec3Value = spec3Value;
    }


}
