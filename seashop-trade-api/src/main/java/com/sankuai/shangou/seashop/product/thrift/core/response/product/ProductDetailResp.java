package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 13:43
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品详情返回值")
public class ProductDetailResp extends BaseThriftDto {

    @Schema(description = "商品详情")
    private ProductDetailDto result;

}
