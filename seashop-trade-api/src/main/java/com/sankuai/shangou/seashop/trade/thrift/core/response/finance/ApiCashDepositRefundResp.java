package com.sankuai.shangou.seashop.trade.thrift.core.response.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@Schema(description = "保证金查询响应体")
@ToString
@Data
public class ApiCashDepositRefundResp extends BaseThriftDto {

    @Schema(description = "主键")
    private Long id;

    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID")
    private Long shopId;

    /**
     * 店铺名称
     */
    @Schema(description = "店铺名称")
    private String shopName;

    /**
     * 状态 0，待审核；1，已通过；2，已拒绝；3，退款处理中；
     */
    @Schema(description = "状态 0，待审核；1，已通过；2，已拒绝；3，退款处理中；")
    private Integer status;

    /**
     * 保证金
     */
    @Schema(description = "保证金")
    private BigDecimal bond;

    /**
     * 扣除金额
     */
    @Schema(description = "扣除金额")
    private BigDecimal deduction;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private BigDecimal refund;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间")
    private Date applyDate;


}
