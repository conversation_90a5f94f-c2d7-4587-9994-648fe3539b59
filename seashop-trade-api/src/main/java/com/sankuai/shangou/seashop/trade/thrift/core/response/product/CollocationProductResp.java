package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "组合购商品返回值")
public class CollocationProductResp extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "最低售价")
    private BigDecimal minSalePrice;

    @Schema(description = "原价")
    private BigDecimal originalPrice;

    @Schema(description = "商品主图")
    private String imagePath;

    @Schema(description = "是否是主商品")
    private Boolean mainFlag;

    @Schema(description = "是否有sku")
    private Boolean hasSku;

    @Schema(description = "skuId集合")
    private List<String> skuIds;
    @Schema(description = "规格集合")
    private List<CollocationSkuResp> skuRespList;

}
