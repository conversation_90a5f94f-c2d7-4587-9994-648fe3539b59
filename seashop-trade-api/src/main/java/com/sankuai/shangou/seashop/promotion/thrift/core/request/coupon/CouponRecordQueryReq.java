package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券查询请求对象")
public class CouponRecordQueryReq extends BasePageReq {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "优惠券活动ID")
    private Long couponId;

    @Schema(description = "状态 0-未使用 1-已使用 2-已过期")
    private Integer status;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "订单ID")
    private String orderId;

    @Schema(description = "领用人账号")
    private String userName;

    @Schema(description = "领取时间-开始")
    private Date startReceiveTime;

    @Schema(description = "领取时间-结束")
    private Date endReceiveTime;

    @Schema(description = "使用时间-开始")
    private Date startUseTime;

    @Schema(description = "使用时间-结束")
    private Date endUseTime;

    @Schema(description = "按价值倒序排序")
    private Boolean orderByPriceDesc;

    @Schema(description = "根据状态不同进行排序")
    private Boolean autoOrderByStatus;


}
