package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "组合购返回值")
public class CollectionBuyResp extends BaseThriftDto {

    @Schema(description = "组合购ID")
    private Long id;

    @Schema(description = "组合购标题")
    private String title;

    @Schema(description = "组合购副标题")
    private String shortDesc;

    @Schema(description = "组合购商品集合")
    private List<CollocationProductResp> productRespList;


}
