package com.sankuai.shangou.seashop.trade.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.mall.ApiShopMallQueryReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.mall.ApiShopEsCombinationResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 供应商服务类
 */
@FeignClient(name = "himall-trade",contextId = "ShopQueryFeign", path = "/himall-trade/shopQuery", url = "${himall-trade.dev.url:}")
public interface ShopQueryFeign {
    /**
     * 商城店铺搜索功能
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryMallShopList", consumes = "application/json")
    ResultDto<ApiShopEsCombinationResp> queryMallShopList(@RequestBody ApiShopMallQueryReq request) throws TException;

}
