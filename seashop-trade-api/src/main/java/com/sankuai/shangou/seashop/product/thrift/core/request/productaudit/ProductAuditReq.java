package com.sankuai.shangou.seashop.product.thrift.core.request.productaudit;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:00
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品审核入参")
public class ProductAuditReq extends BaseParamReq {

    @Schema(description = "商品id的集合", required = true)
    @ExaminField(description = "商品id的集合")
    private List<Long> productIdList;

    @Schema(description = "是否通过", required = true)
    @ExaminField(description = "是否通过")
    private Boolean pass;

    @Schema(description = "备注")
    @ExaminField(description = "备注")
    private String auditReason;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIdList), "请至少选择一条待审核记录");
        AssertUtil.throwIfTrue(productIdList.size() > ParameterConstant.BATCH_AUDIT_PRODUCT_SIZE,
                String.format("批量审核商品数量不能超过%d", ParameterConstant.BATCH_AUDIT_PRODUCT_SIZE));
        AssertUtil.throwInvalidParamIfNull(pass, "请选择审核结果");
        if (!pass) {
            AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(auditReason), "请填写拒绝原因");
        }
    }


}
