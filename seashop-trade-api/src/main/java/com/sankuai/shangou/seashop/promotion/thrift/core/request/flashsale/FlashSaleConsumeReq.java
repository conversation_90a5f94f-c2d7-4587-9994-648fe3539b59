package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "核销限时购请求对象")
public class FlashSaleConsumeReq extends BaseParamReq {

    /**
     * 限时购ID
     */
    @Schema(description = "限时购ID", required = true)
    private Long flashSaleId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", required = true)
    private Long memberId;

    /**
     * 商品ID
     */
    @Schema(description = "商品ID", required = true)
    private Long productId;

    /**
     * skuId
     */
    @Schema(description = "skuId", required = true)
    private String skuId;

    /**
     * 订单ID
     */
    @Schema(description = "订单ID", required = true)
    private String orderId;

    /**
     * 核销数量
     */
    @Schema(description = "核销数量", required = true)
    private Integer consumeNum;

    @Schema(description = "核销时间(不传取当前服务器时间)")
    private Date consumeTime;

    @Override
    public void checkParameter() {
        if (this.flashSaleId == null || this.flashSaleId <= 0) {
            throw new InvalidParamException("限时购ID不能为空");
        }
        if (this.memberId == null || this.memberId <= 0) {
            throw new InvalidParamException("用户ID不能为空");
        }
        if (this.productId == null || this.productId <= 0) {
            throw new InvalidParamException("商品ID不能为空");
        }
        if (StrUtil.isBlank(this.skuId)) {
            throw new InvalidParamException("skuId不能为空");
        }
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("订单ID不能为空");
        }
        if (null == this.consumeNum || this.consumeNum <= 0) {
            throw new InvalidParamException("核销数量不能为空");
        }
    }


}
