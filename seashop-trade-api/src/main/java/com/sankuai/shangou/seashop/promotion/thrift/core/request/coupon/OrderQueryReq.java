package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "订单信息查询请求对象")
public class OrderQueryReq extends BaseParamReq {

    @Schema(description = "店铺ID", required = true)
    private Long shopId;

    @Schema(description = "商品ID列表", required = true)
    private List<ProductQueryReq> productList;

    @Override
    public void checkParameter() {
        if (this.shopId == null || this.shopId <= 0) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (CollUtil.isEmpty(this.productList)) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
