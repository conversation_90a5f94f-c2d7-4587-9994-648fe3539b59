package com.sankuai.shangou.seashop.promotion.thrift.core.enums;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
public enum FlashSaleLimitTypeEnum {

    /**
     * 限购类型:1商品;2规格
     */

    PRODUCT(1, "商品"),
    SKU(2, "规格"),
    ;

    private Integer type;
    private String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 通过type获取枚举
     */
    public static FlashSaleLimitTypeEnum getEnumByType(Integer type) {
        for (FlashSaleLimitTypeEnum e : FlashSaleLimitTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return null;
    }

    FlashSaleLimitTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
