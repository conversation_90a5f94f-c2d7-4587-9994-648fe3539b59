package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@Schema(description = "限时购活动响应体")
@Data
public class FlashSaleResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    @ExaminField(description = "限时购ID")
    private Long id;

    @Schema(description = "活动名称")
    @ExaminField(description = "活动名称")
    private String title;

    @Schema(description = "店铺ID")
    @ExaminField(description = "店铺ID")
    private Long shopId;

    @Schema(description = "产品ID")
    @ExaminField(description = "产品ID")
    private Long productId;

    @Schema(description = "产品名称")
    @ExaminField(description = "产品名称")
    private String productName;

    @Schema(description = "活动开始日期")
    @ExaminField(description = "活动开始日期")
    private Date beginDate;

    @Schema(description = "活动结束日期")
    @ExaminField(description = "活动结束日期")
    private Date endDate;

    @Schema(description = "限购类型:1商品;2规格")
    @ExaminField(description = "限购类型:1商品;2规格")
    private Integer limitType;

    @Schema(description = "限购数量")
    @ExaminField(description = "限购数量")
    private Integer limitCount;

    @Schema(description = "活动id")
    @ExaminField(description = "活动分类id")
    private Long categoryId;

    @Schema(description = "活动分类：默认是限时购分类")
    @ExaminField(description = "活动分类：默认是限时购分类")
    private String categoryName;

    @Schema(description = "商品主图")
    @ExaminField(description = "商品主图")
    private String imagePath;

    @Schema(description = "明细列表")
    @ExaminField(description = "明细列表", isChildField = true)
    private List<FlashSaleDetailResp> detailList;

    @Schema(description = "状态")
    @ExaminField(description = "状态")
    private Integer status;
}
