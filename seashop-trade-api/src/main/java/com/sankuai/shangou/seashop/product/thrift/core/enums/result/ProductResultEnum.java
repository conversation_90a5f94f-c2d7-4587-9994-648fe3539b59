package com.sankuai.shangou.seashop.product.thrift.core.enums.result;

import com.sankuai.shangou.seashop.base.boot.enums.Code;

/**
 * <AUTHOR>
 * @date 2024/01/24 11:51
 */
public enum ProductResultEnum implements Code {

    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * 4xx-公共异常
     * 2xx-商家相关
     * 0xx-店铺相关
     * 5xx-管理员相关
     * </pre>
     */

    /**
     * 品牌不存在
     */
    BRAND_NOT_EXIST(40060401, "品牌不存在"),
    /**
     * 没有品牌经营权限
     */
    NO_BRAND_AUTH(40060402, "没有品牌经营权限"),

    /**
     * 类目不存在
     */
    CATEGORY_NOT_EXIST(40060403, "类目不存在"),
    /**
     * 没有类目经营权限
     */
    NO_CATEGORY_AUTH(40060404, "没有类目经营权限"),


    /**
     * 商品不存在
     */
    PRODUCT_NOT_EXIST(40060405, "商品不存在"),
    /**
     * 没有商品权限
     */
    NO_PRODUCT_AUTH(40060406, "没有商品权限"),
    /**
     * 商品货号已存在
     */
    PRODUCT_CODE_EXIST(40060407, "商品货号已存在"),


    /**
     * SKU不存在
     */
    SKU_NOT_EXIST(40060408, "规格不存在"),
    /**
     * SKU_CODE 已存在
     */
    SKU_CODE_EXIST(40060409, "【%s】规格货号已存在"),
    /**
     * SKU_ID 已存在(规格重复)
     */
    SKU_ID_EXIST(40060410, "【%s】规格重复"),
    /**
     * 库存不足
     */
    STOCK_NOT_ENOUGH(40060411, "【%s】库存不足"),

    /**
     * 店铺分类不存在
     */
    SHOP_CATEGORY_NOT_EXIST(40060412, "店铺分类不存在"),


    /**
     * 运费模板不存在
     */
    FREIGHT_TEMPLATE_NOT_EXIST(40060413, "运费模板不存在"),

    /**
     * 规格模板不存在
     */
    SPEC_TEMPLATE_NOT_EXIST(40060414, "规格模板不存在"),
    /**
     * 规格未开启
     */
    SPEC_NOT_SUPPORT(40060415, "【%s】规格未开启"),

    /**
     * 商品审核记录不存在
     */
    PRODUCT_AUDIT_NOT_EXIST(40060416, "商品审核记录不存在"),
    /**
     * 商品审核状态不正确
     */
    PRODUCT_AUDIT_STATUS_ERROR(40060417, "商品审核状态不正确"),

    /**
     * 商品货号格式错误 只能为数字、字母、-
     */
    PRODUCT_CODE_FORMAT_ERROR(40060418, "商品货号格式错误 只能为数字、字母、-"),

    /**
     * 规格货号格式错误 只能为数字、字母、-
     */
    SKU_CODE_FORMAT_ERROR(40060419, "规格货号格式错误 只能为数字、字母、-"),

    /**
     * 运费模板有变动
     */
    FREIGHT_TEMPLATE_CHANGED(40060420, "运费模板有变动, 请重新选择运费模板"),
    /**
     * 规格模板发生变动
     */
    SPEC_TEMPLATE_CHANGED(40060421, "规格模板发生变动, 请重新选择规格模板"),

    /**
     * 属性不存在
     */
    ATTRIBUTE_NOT_EXIST(40060422, "商品属性不存在"),
    /**
     * 属性值不存在
     */
    ATTRIBUTE_VALUE_NOT_EXIST(40060423, "商品属性值不存在"),

    /**
     * 由OE号+品牌号+品牌ID组成的商品已存在
     */
    PRODUCT_OE_BRAND_EXIST(40060424, "由OE号+品牌号+品牌ID组成的商品已存在"),


    ;


    private int code;
    private String msg;

    ProductResultEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    @Override
    public Integer value() {
        return code;
    }

    @Override
    public String desc() {
        return msg;
    }
}
