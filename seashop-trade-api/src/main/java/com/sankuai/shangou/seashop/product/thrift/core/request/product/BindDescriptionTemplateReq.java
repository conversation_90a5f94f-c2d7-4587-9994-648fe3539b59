package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/20 14:41
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "关联版式入参")
public class BindDescriptionTemplateReq extends BaseParamReq {

    @Schema(description = "商品id的集合", required = true)
    @ExaminField(description = "商品id的集合")
    private List<Long> productIdList;

    @Schema(description = "顶部版式id 不关联传0", required = true)
    @ExaminField(description = "顶部版式id")
    private Long descriptionPrefixId;

    @Schema(description = "底部版式id 不关联传0", required = true)
    @ExaminField(description = "底部版式id")
    private Long descriptionSuffixId;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIdList), "请至少选择一个需要操作的商品");
        AssertUtil.throwIfTrue(descriptionPrefixId == null && descriptionSuffixId == null, "请至少选择一个版式");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
