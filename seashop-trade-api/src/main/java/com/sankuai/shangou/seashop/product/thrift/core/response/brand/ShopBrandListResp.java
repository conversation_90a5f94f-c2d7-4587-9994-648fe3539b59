package com.sankuai.shangou.seashop.product.thrift.core.response.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.ShopBrandDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/14 10:48
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商家品牌返回值")
public class ShopBrandListResp extends BaseThriftDto {

    @Schema(description = "品牌列表")
    private List<ShopBrandDto> brandList;


}
