package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2024/2/1/001
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券查询请求对象")
public class CouponRecordIdReq extends BaseParamReq {

    @Schema(description = "优惠券记录ID")
    private Long couponRecordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Override
    public void checkParameter() {
        if (null == this.couponRecordId || this.couponRecordId <= 0) {
            throw new InvalidParamException("优惠券记录ID不能为空");
        }
        if (null == this.userId || this.userId <= 0) {
            throw new InvalidParamException("用户ID不能为空");
        }
    }


}
