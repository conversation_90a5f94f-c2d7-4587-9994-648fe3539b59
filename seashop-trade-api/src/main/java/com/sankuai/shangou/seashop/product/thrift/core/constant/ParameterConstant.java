package com.sankuai.shangou.seashop.product.thrift.core.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/01/11 14:32
 */
public class ParameterConstant {

    /**
     * es搜索结果限制
     */
    public static final Long ES_SEARCH_LIMIT = 10000L;

    /**
     * 零
     */
    public static final Long ZERO = 0L;

    /**
     * 通过集合查询的长度限制
     */
    public static final Integer QUERY_LIST_LIMIT = 200;

    /**
     * 页码最小值
     */
    public static final Integer PAGE_NO_MIN = 1;

    /**
     * 商品名称长度
     */
    public static final Integer PRODUCT_NAME_LENGTH = 100;

    /**
     * 商品广告词长度
     */
    public static final Integer PRODUCT_SHORT_DESCRIPTION_LIMIT_LENGTH = 200;

    /**
     * 品牌拒绝原因长度
     */
    public static final Integer BRAND_AUDIT_REASONS_LENGTH = 100;

    /**
     * 品牌名称长度
     */
    public static final Integer BRAND_NAME_LENGTH = 20;

    /**
     * 品牌描述长度
     */
    public static final Integer BRAND_DESCRIPTION_LENGTH = 200;

    /**
     * 类目名称长度
     */
    public static final Integer CATEGORY_NAME_LENGTH = 12;

    /**
     * 版式名称长度
     */
    public static final Integer DESCRIPTION_TEMPLATE_NAME_LENGTH = 30;

    /**
     * 推荐商品数量
     */
    public static final Integer RECOMMEND_PRODUCT_NUM = 12;

    /**
     * 商品序号最小值
     */
    public static final Long PRODUCT_SEQUENCE_MIN = 0L;

    /**
     * 安全库存最小值
     */
    public static final Long SAFE_STOCK_MIN = 0L;

    /**
     * 库存最小值
     */
    public static final Long STOCK_MIN = 0L;

    /**
     * 销量最小值
     */
    public static final Long SALE_COUNTS_MIN = 0L;

    /**
     * 商品编码长度
     */
    public static final Integer PRODUCT_CODE_LENGTH = 25;

    /**
     * 倍数起购量最小值
     */
    public static final Integer PRODUCT_MULTIPLE_COUNT_MIN = 1;

    /**
     * skuId分隔符
     */
    public static final String SKU_ID_SPLIT = "_";

    /**
     * skuId分隔符长度
     */
    public static final Integer SKU_ID_SPLIT_LENGTH = 4;

    /**
     * 规格值长度
     */
    public static final Integer SPEC_VALUE_LENGTH = 100;

    /**
     * 规格别名长度
     */
    public static final Integer SPEC_ALIAS_LENGTH = 50;

    /**
     * 批次审核商品限制
     */
    public static final Integer BATCH_AUDIT_PRODUCT_SIZE = 50;

    /**
     * 店铺类目名称长度限制
     */
    public static final Integer SHOP_CATEGORY_NAME_LENGTH = 12;

    /**
     * 品牌授权书最大数量
     */
    public static final Integer BRAND_AUTH_CERTIFICATE_MAX_SIZE = 10;

    /**
     * 货号校验正则 只能为数字、字母或者-
     */
    public static final String PRODUCT_CODE_CHECK_REGEX = "^[a-zA-Z0-9-]+$";

    /**
     * 规格货号校验正则 只能为数字、字母或者-
     */
    public static final String SKU_CODE_CHECK_REGEX = "^[a-zA-Z0-9-]+$";

    /**
     * 计量单位校验正则 不能包含数字
     */
    public static final String MEASURE_UNIT_CHECK_REGEX = "^[^0-9]+$";

    /**
     * 最小库存数
     */
    public static final Long MIN_STOCK = 0L;

    /**
     * 最大库存数
     */
    public static final Long MAX_STOCK = 999999l;

    /**
     * 最小限购数
     */
    public static final Integer MIN_MAX_BUY_COUNT = 0 ;

    /**
     * 最大限购数
     */
    public static final Integer MAX_MAX_BUY_COUNT = 999999 ;

    /**
     * 最小价格
     */
    public static final BigDecimal MIN_PRICE = BigDecimal.valueOf(0.01);

    /**
     * 最大价格
     */
    public static final BigDecimal MAX_PRICE = BigDecimal.valueOf(999999);

    /**
     * 最小市场价
     */
    public static final BigDecimal MIN_MARKET_PRICE = BigDecimal.valueOf(0);

    /**
     * 最大市场价
     */
    public static final BigDecimal MAX_MARKET_PRICE = BigDecimal.valueOf(999999);

    /**
     * 最小倍数起购量
     */
    public static final Integer MIN_MULTIPLE_COUNT = 1;

    /**
     * 最大倍数起购量
     */
    public static final Integer MAX_MULTIPLE_COUNT = 999999;

    /**
     * 检验productId 格式 不能超过19位
     */
    public static final String PRODUCT_ID_CHECK_REGEX = "^[0-9]{1,19}$";

    /**
     * 金额校验 (最多保留两位小数 大于0)
     */
    public static final String AMOUNT_CHECK_REGEX = "^(([1-9]\\d{0,9})|0)(\\.\\d{1,2})?$";

    /**
     * 阶梯价最大数量
     */
    public static final Integer MAX_LADDER_PRICE_SIZE = 3;

    /**
     * 最大规格层级
     */
    public static final Integer MAX_SPEC_SIZE = 3;

    /**
     * 默认规格值ID
     */
    public static final Long DEFAULT_SPEC_VALUE_ID = 0L;
}
