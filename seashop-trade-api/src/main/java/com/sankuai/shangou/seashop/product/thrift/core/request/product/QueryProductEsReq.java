package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/27 9:58
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询商品入参(ES构建)")
public class QueryProductEsReq extends BaseParamReq {

    @Schema(description = "商品id的集合", required = true)
    private Long productId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(productId == null || productId <= 0, "商品id不能为空");
    }


}
