package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "查询凑单活动参数")
public class QueryShopAddonActivityReq extends BaseParamReq {

    @Schema(description = "用户id", required = true)
    private Long userId;
    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(userId, "用户id不能为空");
        AssertUtil.throwIfNull(shopId, "店铺id不能为空");
    }


}
