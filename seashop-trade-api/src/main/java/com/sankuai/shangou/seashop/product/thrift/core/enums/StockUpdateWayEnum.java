package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * <AUTHOR>
 * @date 2023/12/18 14:59
 */
public enum StockUpdateWayEnum {

    /**
     * 库存修改方式 1-调整库存 2-覆盖库存
     */
    CHANGE(1, "调整库存"),
    COVER(2, "覆盖库存");

    private Integer code;
    private String desc;

    StockUpdateWayEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code 查询枚举
     */
    public static StockUpdateWayEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StockUpdateWayEnum stockUpdateWayEnum : StockUpdateWayEnum.values()) {
            if (stockUpdateWayEnum.getCode().equals(code)) {
                return stockUpdateWayEnum;
            }
        }
        return null;
    }
}
