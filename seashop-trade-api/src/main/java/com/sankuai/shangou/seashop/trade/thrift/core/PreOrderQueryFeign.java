package com.sankuai.shangou.seashop.trade.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SubmitOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * 预订单查询
 */
@FeignClient(name = "himall-trade",contextId = "PreOrderQueryFeign", path = "/himall-trade/preOrder", url = "${himall-trade.dev.url:}")
public interface PreOrderQueryFeign {

    /**
     * 获取订单提交token
     * 作用是防止用户重复点击导致的重复下单，保证提交订单接口的幂等性
     */
    @GetMapping(value = "/getSubmitToken")
    ResultDto<String> getSubmitToken(@RequestParam Long userId) throws TException;

    /**
     * ERP预览订单,不需要经过购物车, 只需要校验商品上下架，库存
     */
    @PostMapping(value = "/previewErpOrder", consumes = "application/json")
    ResultDto<PreviewOrderResp> previewErpOrder(@RequestBody SubmitOrderReq req) throws TException;
}
