package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/11 9:37
 */
@Data
@Schema(description = "供应商首页统计商品信息")
public class StatisticalProductResp extends BaseThriftDto {
    @Schema(description = "发布商品数量")
    private Integer productsNumber;

    @Schema(description = "出售中")
    private Integer productsOnSale;

    @Schema(description = "草稿箱")
    private Integer productsInDraft;

    @Schema(description = "待审核")
    private Integer productsWaitForAuditing;

    @Schema(description = "审核未通过")
    private Integer productsAuditFailed;

    @Schema(description = "违规下架")
    private Integer productsInfractionSaleOff;

    @Schema(description = "仓库中")
    private Integer productsInStock;

    @Schema(description = "警戒库存数")
    private Integer overSafeStockProducts;

    @Schema(description = "授权品牌")
    private Integer productsBrands;


}
