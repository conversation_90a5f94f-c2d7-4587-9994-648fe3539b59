package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * <AUTHOR>
 * @date 2023/11/06 14:09
 */
public class ProductEnum {

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    public enum SaleStatusEnum {
        /**
         * 销售中
         */
        ON_SALE(1, "销售中"),
        /**
         * 仓库中
         */
        IN_STOCK(2, "仓库中"),
        /**
         * 草稿箱
         */
        DRAFT(3, "草稿箱");

        private Integer code;
        private String desc;

        SaleStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static SaleStatusEnum getByCode(Integer code) {
            for (SaleStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }

        public static String getDescByCode(Integer code) {
            for (SaleStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return "";
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 审核状态 1-待审核 2-销售中 3-未通过 4-违规下架
     */
    public enum AuditStatusEnum {
        /**
         * 待审核
         */
        WAIT_AUDIT(1, "待审核"),
        /**
         * 销售中
         */
        ON_SALE(2, "销售中"),
        /**
         * 未通过
         */
        NOT_PASS(3, "未通过"),
        /**
         * 违规下架
         */
        VIOLATION(4, "违规下架");

        private Integer code;
        private String desc;

        AuditStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static AuditStatusEnum getByCode(Integer code) {
            for (AuditStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }

        public static String getDescByCode(Integer code) {
            for (AuditStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return "";
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
