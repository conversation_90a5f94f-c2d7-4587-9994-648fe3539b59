package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import java.math.BigDecimal;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/11 14:06
 */
@Data
@Schema(description = "商品返回参数")
public class QueryProductByIdListResp extends BaseThriftDto {

    @Schema(description = "商品ID")
    private String productId;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "审核状态 1-审核中 2-审核通过 3-审核不通过 4-违规下架")
    private Integer auditStatus;

    @Schema(description = "审核状态")
    private String auditStatusName;

    @Schema(description = "商品主图")
    private String mainImagePath;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "评价总数")
    private Integer totalCount;

    @Schema(description = "最少销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "销售状态 1-销售中 2-仓库中（人为下架） 3-草稿箱")
    private Integer saleStatus;

    @Schema(description = "销售状态")
    private String saleStatusName;

    @Schema(description = "库存总数")
    private Long totalStock;

    @Schema(description = "是否单价格商品")
    private Boolean singlePrice;


}
