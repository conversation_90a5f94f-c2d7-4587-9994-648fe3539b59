package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@Schema(description = "专享价活动响应体")
@Data
public class ExclusivePriceSimpleResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "活动名称")
    private String name;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusDesc;

    @Schema(description = "商品数量")
    private Integer productCount;


}
