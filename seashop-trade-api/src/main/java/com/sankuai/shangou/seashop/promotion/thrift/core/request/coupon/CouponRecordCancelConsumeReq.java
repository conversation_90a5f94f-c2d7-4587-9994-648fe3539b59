package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券撤销核销请求对象")
public class CouponRecordCancelConsumeReq extends BaseParamReq {

    @Schema(description = "订单ID", required = true)
    private List<String> orderIdList;

    @Schema(description = "用户ID", required = true)
    private Long memberId;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(this.orderIdList)) {
            throw new InvalidParamException("订单ID列表不能为空");
        }
        if (this.memberId == null) {
            throw new InvalidParamException("用户ID不能为空");
        }
    }


}
