package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 11:07
 */
@Data
@Schema(description = "组合购查询列表入参(平台端)")
public class PageMCollocationReq extends BasePageReq {

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "组合购活动名称")
    private String title;

    @Schema(description = "主商品ID")
    private Long mainproductId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;


}
