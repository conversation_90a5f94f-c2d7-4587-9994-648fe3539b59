package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "保证金配置列表")
public class CategoryCashDepositListReq extends BaseParamReq {

    @Schema(description = "保证金配置列表")
    private List<CategoryCashDepositReq> list;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(this.list)) {
            throw new InvalidParamException("list不能为空");
        }
        this.list.parallelStream().forEach(CategoryCashDepositReq::checkParameter);
    }


}
