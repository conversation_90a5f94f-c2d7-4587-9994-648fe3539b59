package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.constant.PromotionApiConstant;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.FlashSaleDetailDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.FlashSaleLimitTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "修改限时购活动请求对象")
public class FlashSaleUpdateReq extends BaseParamReq {

    @PrimaryField(title = "活动ID")
    @Schema(description = "活动ID", required = true)
    private Long id;

    /**
     * 标题
     */
    @ExaminField(description = "标题")
    @Schema(description = "标题")
    private String title;

    /**
     * 产品ID
     */
    @ExaminField(description = "产品ID")
    @Schema(description = "产品ID", required = true)
    private Long productId;

    /**
     * 活动开始日期
     */
    @ExaminField(description = "活动开始日期")
    @Schema(description = "活动开始日期", required = true)
    private Date beginDate;

    /**
     * 活动结束日期
     */
    @ExaminField(description = "活动结束日期")
    @Schema(description = "活动结束日期", required = true)
    private Date endDate;

    /**
     * 限购类型:1商品;2规格
     */
    @ExaminField(description = "限购类型")
    @Schema(description = "限购类型:1商品;2规格", required = true)
    private Integer limitType;

    /**
     * 限购数量
     */
    @ExaminField(description = "限购数量")
    @Schema(description = "限购数量")
    private Integer limitCount;

    /**
     * 活动类型id
     */
    @ExaminField(description = "活动类型id")
    @Schema(description = "活动类型id", required = true)
    private Long categoryId;

    /**
     * 活动明细信息
     */
//    @ExaminField
    @Schema(description = "活动明细信息", required = true)
    private List<FlashSaleDetailDto> detailList;

    @Override
    public void valueInit() {
        if (StrUtil.isBlank(this.title)) {
            this.title = PromotionApiConstant.LIMIT_TIME_ACTIVE_TITLE;
        }
        if (null == this.limitCount) {
            // 默认限购数量为0,说明不限购
            this.limitCount = 0;
        }
        if (CollUtil.isNotEmpty(this.detailList)) {
            this.detailList.forEach(FlashSaleDetailDto::valueInit);
        }
    }

    @Override
    public void checkParameter() {
        if (null == this.id) {
            throw new InvalidParamException("活动id不能为空");
        }
        if (null == this.endDate) {
            throw new InvalidParamException("活动结束日期不能为空");
        }
        if (null == this.limitType || this.limitType <= 0) {
            throw new InvalidParamException("限购类型不能为空");
        }
        if (null == FlashSaleLimitTypeEnum.getEnumByType(this.limitType)) {
            throw new InvalidParamException("限购类型不正确");
        }
        if (null == this.categoryId || this.categoryId <= 0) {
            throw new InvalidParamException("活动类型id不能为空");
        }
        if (CollUtil.isEmpty(this.detailList)) {
            throw new InvalidParamException("活动明细信息不能为空");
        }
        this.detailList.forEach(FlashSaleDetailDto::checkParameter);
    }


}
