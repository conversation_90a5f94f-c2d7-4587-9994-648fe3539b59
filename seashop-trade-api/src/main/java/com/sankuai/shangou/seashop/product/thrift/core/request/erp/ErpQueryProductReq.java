package com.sankuai.shangou.seashop.product.thrift.core.request.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.erp.ErpTimeType;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/07 8:48
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "erp查询商品信息入参")
public class ErpQueryProductReq extends BasePageReq {

    @Schema(description = "erp状态 ALL=null ON_SALE=1 OFF_SALE= 3 4")
    private List<ProductStatusEnum> erpStatus;

    @Schema(description = "时间类型")
    private ErpTimeType timeType;

    @Setter
    @Getter
    @Schema(description = "开始时间")
    private Date startTime;

    @Setter
    @Getter
    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "货号")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "sku编码")
    private List<String> skuCodes;

    @Schema(description = "sku自增id")
    private List<Long> skuAutoIds;

    @Schema(description = "销售状态")
    private ProductEnum.SaleStatusEnum saleStatus;


}
