package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询类目入参")
public class QueryCategoryByIdsReq extends BaseParamReq {

    @Schema(description = "类目id的集合")
    private List<Long> categoryIds;

    @Schema(description = "是否使用缓存")
    private Boolean useCache;


}
