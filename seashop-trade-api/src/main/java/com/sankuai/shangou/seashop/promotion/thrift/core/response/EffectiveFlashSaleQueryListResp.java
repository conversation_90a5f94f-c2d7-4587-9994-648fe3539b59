package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/4/1/001
 * @description:
 */
@Schema(description = "有效限时购活动返回列表")
@Data
public class EffectiveFlashSaleQueryListResp extends BaseThriftDto {

    @Schema(description = "列表")
    private List<EffectiveFlashSaleQueryResp> list = new ArrayList<>();


}
