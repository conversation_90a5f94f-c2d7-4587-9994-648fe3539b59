package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.PurchaseProductDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "预览限时购订单请求入参")
@AllArgsConstructor
@NoArgsConstructor
public class PreviewCollocationOrderReq extends BaseParamReq {

    @Schema(description = "商家用户ID", required = true)
    private Long userId;
    /**
     * 组合购活动ID
     */
    @Schema(description = "组合购活动ID", required = true)
    private Long collocationId;
    /**
     * 勾选购买的组合购商品列表
     */
    private List<PurchaseProductDto> skuList;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.collocationId == null || this.collocationId <= 0) {
            throw new InvalidParamException("collocationId不能为空");
        }
        if (CollUtil.isEmpty(this.skuList)) {
            throw new InvalidParamException("skuList不能为空");
        }
        this.skuList.forEach(PurchaseProductDto::checkParameter);
    }


}
