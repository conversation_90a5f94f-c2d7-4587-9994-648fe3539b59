package com.sankuai.shangou.seashop.product.thrift.core.request.stock;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/18 14:58
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "回滚库存入参")
public class RollBackStockReq extends BaseParamReq {

    @Schema(description = "原修改类型", required = true)
    private StockUpdateTypeEnum orgUpdateType;

    @Schema(description = "原业务编码", required = true)
    private List<String> orgBizCodes;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(orgUpdateType == null, "原修改类型不能为空");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(orgBizCodes), "原业务编码不能为空");
        AssertUtil.throwInvalidParamIfNull(orgUpdateType.getRollBackCode(), "该类型不支持回滚库存");
    }


}
