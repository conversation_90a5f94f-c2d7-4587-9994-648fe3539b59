package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/17 17:56
 */
@Data
@Schema(description = "限时购查询请求对象")
public class MallAppletFlashSaleQueryReq extends BaseThriftDto {

    @Schema(description = "商品ID集合")
    private List<String> productIds;

    @Schema(description = "限时购id列表")
    private List<Long> flashSaleIds;


    public void checkParamter() {
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(productIds), "productIds不能为空");
    }


}
