package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.enums.VirtualSaleCountsTypeEnum;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 17:22
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品设置虚拟销量入参")
public class ProductUpdateVirtualSaleCountsReq extends BaseParamReq {

    @Schema(description = "商品id的集合", required = true)
    @ExaminField(description = "商品id的集合")
    private List<Long> productIdList;

    @Schema(description = "虚拟销量类型", required = true)
    @ExaminField(description = "虚拟销量类型")
    private VirtualSaleCountsTypeEnum virtualSaleCountsType;

    @Schema(description = "固定数")
    @ExaminField(description = "固定数")
    private Long fixedNum;

    @Schema(description = "最小随机数")
    @ExaminField(description = "最小随机数")
    private Long minRandomNum;

    @Schema(description = "最大随机数")
    @ExaminField(description = "最大随机数")
    private Long maxRandomNum;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIdList), "请至少选择一个需要操作的商品");
        AssertUtil.throwInvalidParamIfNull(virtualSaleCountsType, "请选择虚拟销量类型");
        if (VirtualSaleCountsTypeEnum.FIXED.equals(virtualSaleCountsType)) {
            AssertUtil.throwInvalidParamIfTrue(fixedNum == null, "请填写固定数");
            AssertUtil.throwInvalidParamIfTrue(fixedNum < ParameterConstant.SALE_COUNTS_MIN,
                    String.format("固定数不能小于%s", ParameterConstant.SALE_COUNTS_MIN));
        } else {
            AssertUtil.throwInvalidParamIfTrue(minRandomNum == null, "请填写最小随机数");
            AssertUtil.throwInvalidParamIfTrue(minRandomNum < ParameterConstant.SALE_COUNTS_MIN,
                    String.format("最小随机数不能小于%s", ParameterConstant.SALE_COUNTS_MIN));
            AssertUtil.throwInvalidParamIfTrue(maxRandomNum == null, "请填写最大随机数");
            AssertUtil.throwInvalidParamIfTrue(maxRandomNum < ParameterConstant.SALE_COUNTS_MIN,
                    String.format("最大随机数不能小于%s", ParameterConstant.SALE_COUNTS_MIN));
            AssertUtil.throwInvalidParamIfTrue(minRandomNum > maxRandomNum, "最小随机数不能大于最大随机数");
        }
    }


}
