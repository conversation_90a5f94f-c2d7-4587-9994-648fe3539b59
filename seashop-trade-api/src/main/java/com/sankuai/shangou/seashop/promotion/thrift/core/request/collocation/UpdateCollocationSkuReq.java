package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 14:11
 */
@Data
@Schema(description = "新增组合购商品SKU表入参")
public class UpdateCollocationSkuReq extends BaseParamReq {

    @Schema(description = "ID自增")
    @PrimaryField
    private Long id;

    @Schema(description = "商品ID")
    @ExaminField(description = "商品ID")
    private Long productId;

    @Schema(description = "商品SkuId")
    @ExaminField(description = "商品SkuId")
    private String skuId;

    @Schema(description = "组合商品表ID")
    @ExaminField(description = "组合商品表ID")
    private Long colloProductId;

    @Schema(description = "组合购价格")
    @ExaminField(description = "组合购价格")
    private BigDecimal price;

    @Schema(description = "原始价格")
    @ExaminField(description = "原始价格")
    private BigDecimal skuPirce;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;


}
