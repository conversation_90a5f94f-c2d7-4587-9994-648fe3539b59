package com.sankuai.shangou.seashop.product.thrift.core.response.product.model;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:46
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品详情sku")
public class ProductDetailSkuDto extends BaseThriftDto {

    @Schema(description = "skuId 新增时不填/填0")
    private Long skuAutoId;

    @Schema(description = "sku 拼接id 规格1ID_规格2ID_规格3_ID")
    private String skuId;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "货号")
    private String skuCode;

    @Schema(description = "警戒库存")
    private Long safeStock;

    @Schema(description = "计量单位")
    private String measureUnit;

    @Schema(description = "sku图片")
    private String showPic;

    @Schema(description = "规格值json")
    private String specValueJson;

    @Schema(description = "规格1 值")
    private String spec1Value;

    @Schema(description = "规格2 值")
    private String spec2Value;

    @Schema(description = "规格3 值")
    private String spec3Value;


    public String getSpec1Value() {
        return spec1Value;
    }

    public void setSpec1Value(String spec1Value) {
        this.spec1Value = spec1Value;
    }

    public String getSpec2Value() {
        return spec2Value;
    }

    public void setSpec2Value(String spec2Value) {
        this.spec2Value = spec2Value;
    }

    public String getSpec3Value() {
        return spec3Value;
    }

    public void setSpec3Value(String spec3Value) {
        this.spec3Value = spec3Value;
    }
}
