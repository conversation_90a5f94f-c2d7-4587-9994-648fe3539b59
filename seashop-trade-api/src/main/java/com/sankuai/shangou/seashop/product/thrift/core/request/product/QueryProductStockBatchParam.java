package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "商品库存批量查询入参")
public class QueryProductStockBatchParam extends BaseParamReq {
    @Schema(description = "店铺ID", required = true)
    private Long shopId;

    @Schema(description = "skuAutoId列表", required = true)
    private List<Long> skuAutoIds;

    @Schema(description = "skuAutoId列表", required = true)
    private List<String> skuCodes;

    @Override
    public void checkParameter() {
        if (shopId == null) {
            throw new IllegalArgumentException("店铺编号不能为空");
        }
        if (CollectionUtils.size(skuAutoIds) > ParameterConstant.QUERY_LIST_LIMIT) {
            throw new IllegalArgumentException(String.format("skuAutoIds列表不能超过%s个", ParameterConstant.QUERY_LIST_LIMIT));
        }
        if (CollectionUtils.size(skuCodes) > ParameterConstant.QUERY_LIST_LIMIT) {
            throw new IllegalArgumentException(String.format("skuCodes列表不能超过%s个", ParameterConstant.QUERY_LIST_LIMIT));
        }
    }


}
