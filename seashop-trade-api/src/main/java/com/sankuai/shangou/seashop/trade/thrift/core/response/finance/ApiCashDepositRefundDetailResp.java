package com.sankuai.shangou.seashop.trade.thrift.core.response.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/7 16:34
 */
@Schema(description = "保证金查询响应体")
@ToString
@Data
public class ApiCashDepositRefundDetailResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "拒绝原因")
    private String remark;


}
