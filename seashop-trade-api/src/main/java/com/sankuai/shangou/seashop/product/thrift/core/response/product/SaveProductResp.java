package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/11 14:50
 */
@Data
@ToString
@Schema(description = "保存商品返回值")
public class SaveProductResp extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "销售状态 1-销售中 2-仓库中 3-草稿")
    private Integer saleStatus;

    @Schema(description = "审核状态 1-待审核 2-销售中 3-未通过 4-违规下架")
    private Integer auditStatus;

    @Schema(description = "sku集合")
    private List<SaveProductSkuResp> skuList;


}
