package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.SendCouponCmdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponReceiveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponSaveReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: liuhao
 * @date: 2023/11/7/007
 * @description: 提供优惠券操作功能
 */
@FeignClient(name = "himall-trade",contextId = "CouponCmdFeign", path = "/himall-trade/coupon", url = "${himall-trade.dev.url:}")
public interface CouponCmdFeign {

    /**
     * 保存优惠券
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/save", consumes = "application/json")
    ResultDto<BaseResp> save(@RequestBody CouponSaveReq request) throws TException;

    /**
     * 结束优惠券活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/endActive", consumes = "application/json")
    ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException;

    /**
     * 领取优惠券
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/receiveCoupon", consumes = "application/json")
    ResultDto<BaseResp> receiveCoupon(@RequestBody CouponReceiveReq request) throws TException;

    /**
     * 发送优惠券
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/sendCoupon", consumes = "application/json")
    ResultDto<BaseResp> sendCoupon(@RequestBody SendCouponCmdReq request) throws TException;
}
