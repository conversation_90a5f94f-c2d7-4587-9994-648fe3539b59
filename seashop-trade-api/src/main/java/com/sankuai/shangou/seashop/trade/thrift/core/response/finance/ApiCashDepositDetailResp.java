package com.sankuai.shangou.seashop.trade.thrift.core.response.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@Schema(description = "保证金支付明细")
@ToString
@Data
public class ApiCashDepositDetailResp extends BaseParamReq {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 保证金表id
     */
    @Schema(description = "保证金表id")
    private Long cashDepositId;

    /**
     * 时间
     */
    @Schema(description = "时间")
    private Date addDate;

    /**
     * 缴费天数
     */
    @Schema(description = "缴费天数")
    private Integer paymentDays;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal balance;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operator;

    /**
     * 说明
     */
    @Schema(description = "说明")
    private String description;

    /**
     * 充值类型（银联、支付宝之类的）
     */
    @Schema(description = "充值类型（银联、支付宝之类的）")
    private Integer rechargeWay;

    /**
     * 类型；1，付款；2，扣款；3，退款
     */
    @Schema(description = "类型；1，付款；2，扣款；3，退款")
    private Integer operatorType;

    /**
     * 渠道支付单号
     */
    @Schema(description = "渠道支付单号")
    private String channelOrderId;

    /**
     * 平台扣款金额
     */
    @Schema(description = "平台扣款金额")
    private BigDecimal platformDeduction;

    /**
     * 冻结金额
     */
    @Schema(description = "冻结金额")
    private BigDecimal forzenAmount;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 是否有退款记录
     */
    @Schema(description = "是否有退款记录")
    private Boolean flagRefund;


}
