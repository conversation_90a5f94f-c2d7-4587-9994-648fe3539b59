package com.sankuai.shangou.seashop.product.thrift.core.response.productaudit;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDetailDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 13:43
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品审核详情返回值")
public class ProductAuditDetailResp extends BaseThriftDto {

    @Schema(description = "商品审核详情")
    private ProductDetailDto productAudit;

    @Schema(description = "商品原始详情")
    private ProductDetailDto originProduct;

    @Schema(description = "是否展示对比")
    private Boolean showCompare;


}
