package com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/08 11:37
 */
@Schema(description = "商家品牌返回值")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ShopBrandDto extends BaseThriftDto {

    @Schema(description = "商家品牌关联表id")
    private Long id;

    @Schema(description = "商家id")
    private Long shopId;

    @Schema(description = "品牌id")
    private Long brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "排序")
    private Long displaySequence;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "品牌简介")
    private String description;


}
