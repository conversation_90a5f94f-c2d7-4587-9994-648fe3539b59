package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/25 14:29
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Builder
@Schema(description = "统计运费模板关联商品数入参")
public class CountProductTemplateReq extends BaseParamReq {

    @Schema(description = "运费模版ID集合")
    private List<Long> templateIds;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Override
    public void checkParameter() {
        if (CollectionUtils.isEmpty(templateIds)) {
            throw new IllegalArgumentException("运费模版ID集合不能为空");
        }
    }


}
