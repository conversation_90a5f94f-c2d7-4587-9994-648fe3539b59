package com.sankuai.shangou.seashop.product.thrift.core.response.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:02
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "平台类目集合")
public class CategoryListResp {

    @Schema(description = "类目集合")
    private List<CategoryResp> categoryRespList;


}
