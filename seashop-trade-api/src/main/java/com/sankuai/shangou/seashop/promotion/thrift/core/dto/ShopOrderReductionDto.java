package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "店铺订单满减活动对象")
public class ShopOrderReductionDto extends BaseThriftDto {

    @Schema(description = "满减活动ID")
    private Long activeId;
    @Schema(description = "满减活动名称")
    private String activeName;
    @Setter
    @Getter
    @Schema(description = "单笔订单满减金额门槛")
    private BigDecimal moneyOffCondition;
    @Setter
    @Getter
    @Schema(description = "单笔订单满减金额")
    private BigDecimal moneyOffFee;
    @Schema(description = "叠加优惠次数")
    private Integer moneyOffOverLayNum;
    @Setter
    @Getter
    @Schema(description = "共计满减金额")
    private BigDecimal moneyOffTotalFee;

    @Schema(description = "店铺ID")
    private Long shopId;


}
