package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/11/02 13:50
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class CreateBrandReq extends BaseParamReq {

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称", required = true)
    @ExaminField(description = "品牌名称")
    private String name;

    /**
     * 品牌图片
     */
    @Schema(description = "品牌图片", required = true)
    @ExaminField(description = "品牌图片")
    private String logo;

    /**
     * 品牌简介
     */
    @Schema(description = "品牌简介")
    @ExaminField(description = "品牌简介")
    private String description;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(name), "请输入品牌名称");
        AssertUtil.throwInvalidParamIfTrue(name.length() > ParameterConstant.BRAND_NAME_LENGTH,
                String.format("品牌名称不能超过%d个字符", ParameterConstant.BRAND_NAME_LENGTH));
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(logo), "请输入品牌图片");
    }


}
