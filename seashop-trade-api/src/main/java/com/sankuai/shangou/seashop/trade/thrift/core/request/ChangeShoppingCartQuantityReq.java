package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "变更购物车sku数量请求入参")
@NoArgsConstructor
public class ChangeShoppingCartQuantityReq extends BaseParamReq {

    @Schema(description = "商家会员ID，用于校验是否操作的是自己的数据", required = true)
    private Long userId;
    @Schema(description = "购物车主键ID", required = true)
    private Long id;
    @Schema(description = "变更后的数量(不管是增减还是输入框改数字，前端先计算后把数量传到后端)", required = true)
    private Integer quantity;
    @Schema(description = "店铺信息", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表", required = true)
    private List<ShoppingCartProductDto> productList;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.id == null || this.id <= 0) {
            throw new InvalidParamException("id不能为空");
        }
        if (this.quantity == null || this.quantity <= 0) {
            throw new InvalidParamException("quantity不能为空");
        }
    }


}
