package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "满减规则返回值")
public class ProductReductionRuleResp extends BaseThriftDto {

    @Schema(description = "满减活动id")
    private Long activeId;

    @Schema(description = "满减活动名称")
    private String activeName;

    @Schema(description = "满减门槛")
    private BigDecimal moneyOffCondition;

    @Schema(description = "单笔订单满减金额")
    private BigDecimal moneyOffFee;

    @Schema(description = "是否叠加优惠")
    private Boolean moneyOffOverLay;

    @Schema(description = "活动开始时间")
    private Date startTime;

    @Schema(description = "活动结束时间")
    private Date endTime;


}
