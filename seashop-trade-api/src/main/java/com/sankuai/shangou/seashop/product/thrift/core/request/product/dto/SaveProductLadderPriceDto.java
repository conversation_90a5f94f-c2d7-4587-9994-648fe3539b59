package com.sankuai.shangou.seashop.product.thrift.core.request.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:19
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保存阶梯价入参")
public class SaveProductLadderPriceDto extends BaseParamReq {

    @Schema(description = "阶梯价id 新增时不填/填0")
    private Long ladderPriceId;

    @Schema(description = "阶梯价起购量", required = true)
    private Integer minBath;

    @Schema(description = "阶梯价产品单价", required = true)
    private BigDecimal price;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(minBath, "阶梯价起购量不能为空");
        AssertUtil.throwInvalidParamIfTrue(minBath <= 0, "阶梯价起购量必须大于0");
        AssertUtil.throwInvalidParamIfNull(price, "阶梯价产品单价不能为空");
        AssertUtil.throwInvalidParamIfTrue(price.compareTo(BigDecimal.ZERO) <= 0, "阶梯价产品单价必须大于0");
    }


}
