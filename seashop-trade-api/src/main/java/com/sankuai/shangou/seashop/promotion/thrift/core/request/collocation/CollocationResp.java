package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Data
@ToString
@Schema(description = "组合购信息")
public class CollocationResp extends BaseParamReq {

    @Schema(description = "组合购主表ID")
    private Long id;

    @Schema(description = "组合购标题")
    private String title;

    @Schema(description = "开始日期")
    private Date startTime;

    @Schema(description = "结束日期")
    private Date endTime;

    @Schema(description = "组合描述")
    private String shortDesc;

    @Schema(description = "组合购店铺ID")
    private Long shopId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "组合购商品集合")
    private List<CollocationProductResp> productRespList;

    @Schema(description = "组合购SKU集合")
    private List<CollocationSkuResp> skuRespList;
}
