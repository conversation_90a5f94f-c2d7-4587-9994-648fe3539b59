package com.sankuai.shangou.seashop.product.thrift.core.request.stock;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateWayEnum;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/18 14:58
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "修改库存入参 优先级")
public class SkuStockReq extends BaseParamReq {

    @Schema(description = "skuId")
    private String skuId;

    @Schema(description = "skuAutoId")
    private Long skuAutoId;

    @Schema(description = "货号")
    private String skuCode;

    @Schema(description = "库存 如果是覆盖不能为负数", required = true)
    private Long stock;

    @Schema(description = "店铺ID 如果是根据skuCode修改则该值必填")
    private Long shopId;

    @Schema(description = "更新时间")
    private Date updateTime;

    public void checkParameter(StockUpdateKeyEnum updateKey, StockUpdateWayEnum updateWay) {
        AssertUtil.throwInvalidParamIfNull(stock, "库存不能为空");
        if (StockUpdateWayEnum.COVER.equals(updateWay)) {
            AssertUtil.throwInvalidParamIfTrue(stock < 0, "库存不能为负数");
        }

        if (StockUpdateKeyEnum.SKU_ID.equals(updateKey)) {
            AssertUtil.throwInvalidParamIfNull(skuId, "skuId 不能为空");
        } else if (StockUpdateKeyEnum.SKU_AUTO_ID.equals(updateKey)) {
            AssertUtil.throwInvalidParamIfNull(skuAutoId, "skuAutoId 不能为空");
        } else if (StockUpdateKeyEnum.SKU_CODE.equals(updateKey)) {
            AssertUtil.throwInvalidParamIfNull(shopId, "shopId 不能为空");
            AssertUtil.throwInvalidParamIfNull(skuCode, "skuCode 不能为空");
        }
    }


}
