package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponRecordConsumeDto;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券核销请求对象")
public class CouponRecordConsumeReq extends BaseParamReq {

    @Schema(description = "用户ID", required = true)
    private Long memberId;

    @Schema(description = "核销列表", required = true)
    private List<CouponRecordConsumeDto> consumeList;

    @Schema(description = "核销时间(不传会使用当前服务器时间)")
    private Date consumeTime;

    @Override
    public void checkParameter() {
        // 用户ID不能为空
        if (this.memberId == null) {
            throw new InvalidParamException("用户ID不能为空");
        }
        // 核销列表不能为空
        if (CollUtil.isEmpty(this.consumeList)) {
            throw new InvalidParamException("核销列表不能为空");
        }
        consumeList.forEach(CouponRecordConsumeDto::checkParameter);
    }


}
