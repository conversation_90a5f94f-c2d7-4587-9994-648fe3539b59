package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductPageQryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceProductSimpleResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:提供专享价活动查询功能
 */
@FeignClient(name = "himall-trade",contextId = "ExclusivePriceProductQueryFeign", path = "/himall-trade/exclusivePriceProduct", url = "${himall-trade.dev.url:}")
public interface ExclusivePriceProductQueryFeign {

    /**
     * 根据参数查询专享价商品
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryByParams", consumes = "application/json")
    ResultDto<ExclusivePriceProductSimpleResp> queryByParams(@RequestBody ExclusivePriceProductQueryReq request) throws TException;

    /**
     * 分页查询专项价商品
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<ExclusivePriceProductDto>> pageList(@RequestBody ExclusivePriceProductPageQryReq request) throws TException;

    /**
     * 查询店铺下当前有效的专享价SKU
     */
    @PostMapping(value = "/getShopValidExclusive", consumes = "application/json")
    ResultDto<ExclusivePriceProductSimpleResp> getShopValidExclusive(@RequestBody BaseIdReq request) throws TException;
}
