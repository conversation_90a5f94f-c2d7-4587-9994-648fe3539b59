package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:32
 */
@Data
@Schema(description = "打印订单项出参")
public class OrderItemResp extends BaseThriftDto {

    @Schema(description = "序号")
    private Integer num;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "规格(color+size+version)")
    private String specName;

    @Schema(description = "数量")
    private Long quantity;

    @Schema(description = "单价")
    private BigDecimal salePrice;

    @Schema(description = "总价")
    private BigDecimal realTotalPrice;


}
