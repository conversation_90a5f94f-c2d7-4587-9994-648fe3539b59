package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "商品库存分页查询入参")
public class QueryProductStockPageParam extends BaseParamReq {
    @Schema(description = "店铺ID", required = true)
    private Long shopId;

    @Schema(description = "页码", required = true)
    private Integer pageNo;

    @Schema(description = "每页条数", required = true)
    private Integer pageSize;

    @Override
    public void checkParameter() {
        if (shopId == null) {
            throw new IllegalArgumentException("店铺编号不能为空");
        }
        if (pageNo == null || pageNo < ParameterConstant.PAGE_NO_MIN) {
            throw new IllegalArgumentException(String.format("页码不能为空且不能小于%s", ParameterConstant.PAGE_NO_MIN));
        }
        if (pageSize == null || pageSize < ParameterConstant.PAGE_NO_MIN || pageSize > ParameterConstant.QUERY_LIST_LIMIT) {
            throw new IllegalArgumentException(String.format("每页条数不能为空且不能小于%s且不能大于%s", ParameterConstant.PAGE_NO_MIN, ParameterConstant.QUERY_LIST_LIMIT));
        }
    }


}
