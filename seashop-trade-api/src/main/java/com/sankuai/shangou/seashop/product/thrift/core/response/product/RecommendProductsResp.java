package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/13 14:08
 */
@Data
@Schema(description = "商品推荐基本信息集合")
public class RecommendProductsResp extends BaseThriftDto {

    @Schema(description = "商品推荐基本信息集合")
    private List<RecommendProductsDto> productsDtoList;


}
