package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商品sku库存分页信息
 *
 * <AUTHOR>
 */
@Schema(description = "商品sku库存分页信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuStockListResp extends BaseThriftDto {

    @Schema(description = "商品sku库存分页信息", required = true)
    private List<ProductSkuStockResp> skuStockList;


}
