package com.sankuai.shangou.seashop.trade.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.*;
import com.sankuai.shangou.seashop.trade.thrift.core.response.AddFromAddonResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.ShopProductResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.UserShoppingCartResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * 提供商家购物车增删改查功能
 */
@FeignClient(name = "himall-trade",contextId = "ShoppingCartCmdFeign", path = "/himall-trade/shoppingCart", url = "${himall-trade.dev.url:}")
public interface ShoppingCartCmdFeign {

    /**
     * 添加sku维度的商品到购物车
     * <AUTHOR>
     * @param addReq 加购参数对象，包括用户ID、商品ID、skuId、加购数量
     */
    @PostMapping(value = "/addShoppingCart", consumes = "application/json")
    ResultDto<BaseResp> addShoppingCart(@RequestBody AddShoppingCartReq addReq) throws TException;

    /**
     * 添加sku维度的商品到购物车
     * <AUTHOR>
     * @param addReq 加购参数对象，包括用户ID、商品ID、skuId、加购数量
     */
    @PostMapping(value = "/addShoppingCartBatch", consumes = "application/json")
    ResultDto<BaseResp> addShoppingCartBatch(@RequestBody AddShoppingCartBatchReq addReq) throws TException;

    /**
     * 删除购物车sku
     * <AUTHOR>
     * @param deleteReq 删除数据的参数对象，包括购物车数据的唯一标识、商家会员ID，其中ID是数组，支持批量删除
     */
    @PostMapping(value = "/deleteShoppingCart", consumes = "application/json")
    ResultDto<BaseResp> deleteShoppingCart(@RequestBody DeleteShoppingCartSkuReq deleteReq) throws TException;

    /**
     * 清除购物车中的失效商品
     * <p>目前直接删除前端传入的ID，这些数据是用户页面已经看到的需要删除的数据，
     * 暂不考虑看到失效数据到点击清除按钮的过程中，失效数据发生了变化 的情况</p>
     * <AUTHOR>
     * @param clearReq 清除数据的参数对象
     */
    @PostMapping(value = "/clearInvalid", consumes = "application/json")
    ResultDto<BaseResp> clearInvalid(@RequestBody ClearInvalidShoppingCartSkuReq clearReq) throws TException;

    /**
     * 调整购物车sku数量
     * <p>调整数量时，可能需要调整优惠，所以传入店铺的购物车列表，并返回店铺的购物车列表</p>
     * <AUTHOR>
     * @param changeReq 调整数据的参数对象
     */
    @PostMapping(value = "/changeShoppingCartSkuCnt", consumes = "application/json")
    ResultDto<ShopProductResp> changeShoppingCartSkuCnt(@RequestBody ChangeShoppingCartQuantityReq changeReq) throws TException;

    /**
     * 选中购物车sku
     * <p>选中时，可能需要调整优惠，所以传入店铺的购物车列表，并返回店铺的购物车列表</p>
     * <AUTHOR>
     * @param selectReq 调整数据的参数对象
     */
    @PostMapping(value = "/selectShopSku", consumes = "application/json")
    ResultDto<ShopProductResp> selectShopSku(@RequestBody SelectShoppingCartSkuReq selectReq) throws TException;

    /**
     * 凑单页面加入购物车，需要返回返回凑单汇总信息
     * <AUTHOR>
     * @param addReq 加购入参
     */
    @PostMapping(value = "/addFromAddon", consumes = "application/json")
    ResultDto<AddFromAddonResp> addFromAddon(@RequestBody AddFromAddonReq addReq) throws TException;

    /**
     * 选中购物车sku
     * <p>选中时，可能需要调整优惠，所以传入店铺的购物车列表，并返回店铺的购物车列表</p>
     * <AUTHOR>
     * @param selectReq 调整数据的参数对象
     */
    @PostMapping(value = "/selectShop", consumes = "application/json")
    ResultDto<ShopProductResp> selectShop(@RequestBody SelectShopReq selectReq) throws TException;

    /**
     * 选中整个购物车
     * <p>选中时，可能需要调整优惠，所以传入店铺的购物车列表，并返回店铺的购物车列表</p>
     * <AUTHOR>
     * @param selectReq 调整数据的参数对象
     */
    @PostMapping(value = "/selectAll", consumes = "application/json")
    ResultDto<UserShoppingCartResp> selectAll(@RequestBody SelectAllReq selectReq) throws TException;

}
