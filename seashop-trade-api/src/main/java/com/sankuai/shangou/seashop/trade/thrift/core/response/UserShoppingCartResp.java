package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopProductDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "商家购物车返回对象")
@ToString
@Data
public class UserShoppingCartResp extends BaseThriftDto {

    @Schema(description = "按店铺分组的商品列表")
    private List<ShoppingCartShopProductDto> shopProductList;
    @Schema(description = "失效的商品列表")
    private List<ShoppingCartProductDto> invalidProductList;
    @Schema(description = "所有勾选的商品总金额")
    private BigDecimal totalAmount;
    /**
     * 购物车中所有商品是否选中，也是所有店铺是否是选中
     */
    @Schema(description = "购物车中所有商品是否选中，也是所有店铺是否是选中")
    private Boolean whetherAllSelected;
    /**
     * 所有勾选的商品总数量
     */
    @Schema(description = "所有勾选的商品总数量")
    private Long totalSelectedQuantity;


}
