package com.sankuai.shangou.seashop.product.thrift.core.response.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/07 9:10
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "erp商品信息返回值")
public class ErpProductResp extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "商品编码(货号)")
    private String productCode;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品状态")
    private ProductStatusEnum productStatus;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "商品图片")
    private List<String> imageList;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "最小销售价(所有sku中的最低价格)")
    private BigDecimal minSalePrice;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "pc端详情地址(暂无)")
    private String webUrl;

    @Schema(description = "h5端详情地址(暂无)")
    private String h5Url;

    @Schema(description = "商品单位")
    private String measureUnit;

    @Schema(description = "销量")
    private Long saleCounts;

    @Schema(description = "限购数")
    private Integer maxBuyCount;

    @Schema(description = "销售状态")
    private ProductEnum.SaleStatusEnum saleStatus;

    @Schema(description = "审核状态")
    private ProductEnum.AuditStatusEnum auditStatus;

    @Schema(description = "是否开启阶梯价")
    private Boolean whetherOpenLadder;

    @Schema(description = "阶梯价")
    private List<ErpLadderPriceResp> ladderPriceList;

    @Schema(description = "倍数起购量")
    private Integer multipleCount;

    @Schema(description = "商品规格")
    private List<ErpSkuResp> skuList;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "来源 1-商城 2-牵牛花 3-易久批")
    private Integer source;

    @Schema(description = "商品描述；包含图文")
    private String description;


    public String getH5Url() {
        return h5Url;
    }

    public void setH5Url(String h5Url) {
        this.h5Url = h5Url;
    }


}
