package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * <AUTHOR>
 * @date 2023/12/19 17:05
 */
public enum StockUpdateKeyEnum {

    /**
     * 库存更新key
     */
    SKU_ID(1, "根据skuId更新"),
    SKU_AUTO_ID(2, "根据skuAutoId更新"),
    SKU_CODE(3, "根据skuCode更新");

    private Integer code;
    private String desc;

    StockUpdateKeyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code 查询枚举
     */
    public static StockUpdateKeyEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StockUpdateKeyEnum stockUpdateKeyEnum : StockUpdateKeyEnum.values()) {
            if (stockUpdateKeyEnum.getCode().equals(code)) {
                return stockUpdateKeyEnum;
            }
        }
        return null;
    }

}
