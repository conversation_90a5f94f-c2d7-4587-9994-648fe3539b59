package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "限时购返回值")
public class ProductFlashSaleResp extends BaseThriftDto {

    @Schema(description = "活动名称")
    private String title;

    @Schema(description = "活动开始日期")
    private Date beginDate;

    @Schema(description = "活动结束日期")
    private Date endDate;

    @Schema(description = "开始倒计时 为0表示活动已经开始")
    private Long startCountDown;

    @Schema(description = "结束倒计时 为0表示活动已经结束")
    private Long endCountDown;

    @Schema(description = "活动id")
    private Long flashSaleActivityId;


}
