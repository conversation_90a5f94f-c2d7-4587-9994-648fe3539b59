package com.sankuai.shangou.seashop.product.thrift.core.request.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/03/05 19:19
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "增加销量入参")
public class AddProductSaleCountDto extends BaseParamReq {

    @Schema(description = "商品id", required = true)
    private Long productId;

    @Schema(description = "增加的销量", required = true)
    private Integer addSaleCount;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(productId, "商品id不能为空");
        AssertUtil.throwInvalidParamIfNull(addSaleCount, "增加的销量不能为空");
        AssertUtil.throwInvalidParamIfTrue(addSaleCount <= 0, "增加的销量必须大于0");
    }


}
