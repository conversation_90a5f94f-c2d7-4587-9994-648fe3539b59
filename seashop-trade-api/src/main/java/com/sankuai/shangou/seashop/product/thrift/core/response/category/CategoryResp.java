package com.sankuai.shangou.seashop.product.thrift.core.response.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:02
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "平台类目信息")
public class CategoryResp extends BaseParamReq {
    /**
     * 主键
     */
    @Schema(description = "id")
    private Long id;

    /**
     * 类目名称
     */
    @Schema(description = "类目名称")
    private String name;

    /**
     * 类目图标
     */
    @Schema(description = "类目图标")
    private String icon;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Long displaySequence;

    /**
     * 上级类目id
     */
    @Schema(description = "上级类目id")
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    @Schema(description = "分佣比例")
    private BigDecimal commissionRate;

    /**
     * 类目的深度
     */
    @Schema(description = "类目的深度")
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    @Schema(description = "类目的路径")
    private String path;

    /**
     * 是否已删除
     */
    @Schema(description = "是否已删除")
    private Boolean whetherDelete = false;

    /**
     * 自定义表单Id
     */
    @Schema(description = "自定义表单Id")
    private Long customFormId;

    /**
     * 类目全路径
     */
    @Schema(description = "类目全路径名称")
    private String fullCategoryName;

    /**
     * 保证金
     */
    @Schema(description = "保证金")
    private BigDecimal cashDeposit;

    /**
     * 是否有下级
     */
    @Schema(description = "是否有下级")
    private Boolean hasChildren;

    /**
     * 是否允许无理由退货
     */
    @Schema(description = "是否允许无理由退货")
    private Boolean enableNoReasonReturn;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示")
    private Boolean whetherShow;


}
