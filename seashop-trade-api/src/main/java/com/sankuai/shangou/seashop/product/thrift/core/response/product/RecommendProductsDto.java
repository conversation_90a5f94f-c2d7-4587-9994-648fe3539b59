package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/13 14:23
 */
@Data
@Schema(description = "商品推荐基本信息")
public class RecommendProductsDto extends BaseThriftDto {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "商品ID")
    private String productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品主图")
    private String imagePath;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "最小销售价(展示这个价格)")
    private BigDecimal minSalePrice;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "实际销量")
    private Long saleCounts;

    @Schema(description = "虚拟销量")
    private Long virtualSaleCounts;


}
