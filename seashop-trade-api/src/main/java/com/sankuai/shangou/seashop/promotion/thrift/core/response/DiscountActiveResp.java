package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveRuleReq;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
@Schema(description = "折扣活动响应体")
@Data
public class DiscountActiveResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    @ExaminField(description = "折扣活动id")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "活动名称")
    @ExaminField(description = "活动名称")
    private String activeName;

    @Schema(description = "开始时间")
    @ExaminField(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    @ExaminField(description = "结束时间")
    private Date endTime;

    @Schema(description = "是否全部商品")
    @ExaminField(description = "是否全部商品")
    private Boolean izAllProduct;

    @Schema(description = "折扣规则")
    @ExaminField(description = "折扣规格", isChildField = true)
    private List<DiscountActiveRuleReq> ruleList;

    @Schema(description = "商品列表")
    @ExaminField(description = "商品列表", isChildField = true)
    private List<DiscountActiveProductDto> productList;


}
