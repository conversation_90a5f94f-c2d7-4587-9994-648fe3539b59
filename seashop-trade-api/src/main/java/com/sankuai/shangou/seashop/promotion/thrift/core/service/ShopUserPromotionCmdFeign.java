package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/23/013
 * @description: 提供营销活动功能
 */
@FeignClient(name = "himall-trade",contextId = "ShopUserPromotionCmdFeign", path = "/himall-trade/shopUserPromotion", url = "${himall-trade.dev.url:}")
public interface ShopUserPromotionCmdFeign {

    /**
     * 结束店铺所有的活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/offSaleAllPromotion", consumes = "application/json")
    ResultDto<BaseResp> offSaleAllPromotion(@RequestBody BaseIdReq request) throws TException;
}
