package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/4/1/001
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "有效活动查询请求对象")
public class EffectiveFlashSaleQueryReq extends BaseParamReq {

    @Schema(description = "商品ID列表")
    private List<Long> productIdList;

    /**
     * 状态：1待审核 2进行中 3未通过 4已结束 5已取消 6未开始
     */
    @Schema(description = "状态（默认进行中的）")
    private Integer status = 2;

    private Boolean frontFlag;


}
