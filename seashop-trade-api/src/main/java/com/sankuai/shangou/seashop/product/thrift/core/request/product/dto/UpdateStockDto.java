package com.sankuai.shangou.seashop.product.thrift.core.request.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/21 13:48
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "批量更新库存入参")
public class UpdateStockDto extends BaseParamReq {

    @Schema(description = "skuAutoId", required = true)
    @PrimaryField(title = "规格id")
    @ExaminField(description = "规格id")
    private Long skuAutoId;

    @Schema(description = "库存 不能为负数", required = true)
    @ExaminField(description = "库存")
    private Long stock;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(skuAutoId == null || skuAutoId <= 0, "skuAutoId不能为空");
        AssertUtil.throwInvalidParamIfTrue(stock == null || stock < ParameterConstant.STOCK_MIN, "库存不能为负数");
    }


}
