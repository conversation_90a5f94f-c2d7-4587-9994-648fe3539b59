package com.sankuai.shangou.seashop.product.thrift.core.response.sku;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 14:54
 */
@Data
@Schema(description = "sku规格数据返参")
public class SkuQueryResp extends BaseThriftDto {

    @Schema(description = "自增主键Id")
    private Long id;

    @Schema(description = "商品ID_规格1ID_规格2ID_规格3ID")
    private String skuId;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "规格1")
    private String spec1Value;

    @Schema(description = "规格2")
    private String spec2Value;

    @Schema(description = "规格3")
    private String spec3Value;

    @Schema(description = "货号")
    private String skuCode;

    @Schema(description = "成本价")
    private BigDecimal costPrice;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "显示图片")
    private String showPic;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "规格信息")
    private String specName;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "是否删除")
    private Boolean whetherDelete;

    @Schema(description = "单位")
    private String measureUnit;


    public String getSpec1Value() {
        return spec1Value;
    }

    public void setSpec1Value(String spec1Value) {
        this.spec1Value = spec1Value;
    }

    public String getSpec2Value() {
        return spec2Value;
    }

    public void setSpec2Value(String spec2Value) {
        this.spec2Value = spec2Value;
    }

    public String getSpec3Value() {
        return spec3Value;
    }

    public void setSpec3Value(String spec3Value) {
        this.spec3Value = spec3Value;
    }


}
