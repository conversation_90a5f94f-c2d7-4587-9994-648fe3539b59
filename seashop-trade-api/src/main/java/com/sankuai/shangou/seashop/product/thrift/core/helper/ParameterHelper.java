package com.sankuai.shangou.seashop.product.thrift.core.helper;

import java.math.BigDecimal;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;

/**
 * <AUTHOR>
 * @date 2024/03/14 11:59
 */
public class ParameterHelper {

    /**
     * 检验商品货号的格式
     */
    public static boolean checkProductCode(String productCode) {
        return productCode.matches(ParameterConstant.PRODUCT_CODE_CHECK_REGEX);
    }

    /**
     * 校验规格货号的格式
     */
    public static boolean checkSkuCode(String skuCode) {
        return skuCode.matches(ParameterConstant.SKU_CODE_CHECK_REGEX);
    }

    /**
     * 校验计量单位
     * 写一个正则校验字符串里面不能带数字
     */
    public static boolean checkMeasureUnit(String measureUnit) {
        return measureUnit.matches(ParameterConstant.MEASURE_UNIT_CHECK_REGEX);
    }

    /**
     * 校验库存
     */
    public static boolean checkStock(Long stock) {
        return stock >= ParameterConstant.MIN_STOCK && stock <= ParameterConstant.MAX_STOCK;
    }

    /**
     * 检验限购数
     */
    public static boolean checkMaxBuyCount(Integer maxBuyCount) {
        return maxBuyCount >= ParameterConstant.MIN_MAX_BUY_COUNT && maxBuyCount <= ParameterConstant.MAX_MAX_BUY_COUNT;
    }


    /**
     * 检验价格
     */
    public static boolean checkPrice(BigDecimal price) {
        return price.compareTo(ParameterConstant.MIN_PRICE) >= 0 && price.compareTo(ParameterConstant.MAX_PRICE) <= 0;
    }

    /**
     * 校验市场价 市场价可以为0
     */
    public static boolean checkMarketPrice(BigDecimal markerPrice) {
        return markerPrice.compareTo(ParameterConstant.MIN_MARKET_PRICE) >= 0 && markerPrice.compareTo(ParameterConstant.MAX_MARKET_PRICE) <= 0;
    }

    /**
     * 校验倍数起购量
     */
    public static boolean checkMultipleCount(Integer multipleCount) {
        return multipleCount >= ParameterConstant.MIN_MULTIPLE_COUNT && multipleCount <= ParameterConstant.MAX_MULTIPLE_COUNT;
    }

    public static boolean checkAmount(String amount) {
        return amount.matches(ParameterConstant.AMOUNT_CHECK_REGEX);
    }
}
