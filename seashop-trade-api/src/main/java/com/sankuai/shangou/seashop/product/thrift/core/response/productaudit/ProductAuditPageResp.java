package com.sankuai.shangou.seashop.product.thrift.core.response.productaudit;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/16 13:56
 */
@Schema(description = "商品审核分页信息")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ProductAuditPageResp extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "序号")
    private Long displaySequence;

    @Schema(description = "商品图片")
    private String imagePath;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "最小销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "品牌id")
    private Long brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "审核状态 1-待审核 2-销售中 3-未通过 4-违规下架")
    private Integer auditStatusCode;

    @Schema(description = "审核状态描述")
    private String auditStatusDesc;

    @Schema(description = "货号")
    private String productCode;

    @Schema(description = "类目id")
    private Long categoryId;

    @Schema(description = "类目名称")
    private String categoryName;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "限购")
    private Integer maxBuyCount;

    @Schema(description = "实际销量")
    private Long saleCounts;

    @Schema(description = "虚拟销量")
    private Long virtualSaleCounts;

    @Schema(description = "发布时间")
    private Date addedDate;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "审核时间")
    private Date checkTime;

    @Schema(description = "拒绝原因")
    private String auditReason;

    @Schema(description = "来源 1-商城 2-牵牛花 3-易久批")
    private Integer source;

    @Schema(description = "来源描述")
    private String sourceDesc;

    @Schema(description = "完整的类目名称")
    private String fullCategoryName;

    @Schema(description = "店铺分类名称集合 逗号隔开")
    private String shopCategoryNames;

    @Schema(description = "最大售价")
    private BigDecimal maxSalePrice;

    @Schema(description = "价格区间")
    private String salePriceRange;

    @Schema(description = "OE号")
    private String oeCode;

    @Schema(description = "品牌号")
    private String brandCode;


}
