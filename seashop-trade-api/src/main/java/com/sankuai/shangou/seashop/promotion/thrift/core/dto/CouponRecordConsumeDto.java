package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券核销请求对象")
public class CouponRecordConsumeDto extends BaseParamReq {

    @Schema(description = "优惠券记录ID", required = true)
    private Long couponRecordId;

    @Schema(description = "订单ID", required = true)
    private String orderId;

    @Schema(description = "店铺ID", required = true)
    private Long shopId;

    @Schema(description = "商品ID列表", required = true)
    private List<Long> productIds;

    @Override
    public void checkParameter() {
        // id和SN必须有一个
        if (this.couponRecordId == null) {
            throw new InvalidParamException("优惠券记录ID不能为空");
        }
        // 订单ID不能为空
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("订单ID不能为空");
        }
        // 店铺ID不能为空
        if (this.shopId == null) {
            throw new InvalidParamException("店铺ID不能为空");
        }
        // 商品ID列表不能为空
        if (CollUtil.isEmpty(this.productIds)) {
            throw new InvalidParamException("商品ID列表不能为空");
        }
    }


}
