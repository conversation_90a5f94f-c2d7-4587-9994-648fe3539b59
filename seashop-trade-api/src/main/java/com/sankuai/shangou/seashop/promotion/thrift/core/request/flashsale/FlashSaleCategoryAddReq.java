package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "新增限时购分类请求对象")
public class FlashSaleCategoryAddReq extends BaseParamReq {

    @Schema(description = "分类名称", required = true)
    private String categoryName;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.categoryName)) {
            throw new InvalidParamException("分类名称不能为空");
        }
    }


}
