package com.sankuai.shangou.seashop.product.thrift.core.response.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/2/28/028
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "类目对应保证金配置列表")
public class CategoryCashDepositMapListResp {

    @Schema(description = "类目对应保证金配置列表")
    private List<CategoryCashDepositMapResp> list;


}
