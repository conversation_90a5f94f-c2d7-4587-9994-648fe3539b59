package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * <AUTHOR>
 * @date 2023/12/20 9:59
 */
public enum StockUpdateStatusEnum {

    /**
     * 执行状态 0-待执行 1-执行中 2-执行成功 3-执行失败
     */
    WAIT(0, "待执行"),

    EXECUTING(1, "执行中"),

    SUCCESS(2, "执行成功"),

    FAIL(3, "执行失败");

    private Integer code;
    private String desc;

    StockUpdateStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
