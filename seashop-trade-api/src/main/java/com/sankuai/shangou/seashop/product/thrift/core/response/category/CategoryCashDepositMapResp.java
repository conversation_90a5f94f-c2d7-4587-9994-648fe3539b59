package com.sankuai.shangou.seashop.product.thrift.core.response.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * @author: lhx
 * @date: 2024/2/28/028
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "类目保证金配置对应关系（非一级类目对应关系）")
public class CategoryCashDepositMapResp extends BaseThriftDto {

    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "类目保证金配置列表")
    private CategoryCashDepositResp categoryCashDepositResp;


}
