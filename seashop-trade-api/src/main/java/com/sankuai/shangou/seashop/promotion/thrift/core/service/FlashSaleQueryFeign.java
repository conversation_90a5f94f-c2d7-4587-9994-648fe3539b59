package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.*;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description: 限时购活动查询相关服务
 */
@FeignClient(name = "himall-trade",contextId = "FlashSaleQueryFeign", path = "/himall-trade/flashSale", url = "${himall-trade.dev.url:}")
public interface FlashSaleQueryFeign {

    /**
     * 限时购活动列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<FlashSaleSimpleResp>> pageList(@RequestBody FlashSaleQueryReq request) throws TException;

    /**
     * 通过id查询限时购活动信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getById", consumes = "application/json")
    ResultDto<FlashSaleResp> getById(@RequestBody BaseIdReq request) throws TException;

    /**
     * 商城页面限时购活动列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/mallPageList", consumes = "application/json")
    ResultDto<BasePageResp<MallFlashSaleResp>> mallPageList(@RequestBody MallFlashSaleQueryReq request) throws TException;

    /**
     * 根据店铺ID和商品ID集合小程序商城页面限时购活动列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/mallAppletPageList", consumes = "application/json")
    ResultDto<MallAppletFlashSaleListResp> mallAppletPageList(@RequestBody MallAppletFlashSaleQueryReq request) throws TException;

    /**
     * 前端可视化组件限时购活动列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/componentPageList", consumes = "application/json")
    ResultDto<BasePageResp<VisualFlashSaleResp>> componentPageList(@RequestBody VisualFlashSaleQueryReq request) throws TException;

    /**
     * 通过商品id查询限时购活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryByProductId", consumes = "application/json")
    ResultDto<FlashSaleResp> queryByProductId(@RequestBody ProductAndShopIdReq request) throws TException;

    /**
     * 查询SKU维度的限时购活动信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryValidWithSkuId", consumes = "application/json")
    ResultDto<SkuFlashSaleDetailResp> queryValidWithSkuId(@RequestBody QuerySkuFlashSaleReq request) throws TException;

    /**
     * 查询有效限时购活动列表
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryEffectiveFlashSaleList", consumes = "application/json")
    ResultDto<EffectiveFlashSaleQueryListResp> queryEffectiveFlashSaleList(@RequestBody EffectiveFlashSaleQueryReq request) throws TException;

    /**
     * 根据限时购id查询可视化组件限时购活动列表
     */
    @PostMapping(value = "/queryVisualFlashSaleList", consumes = "application/json")
    ResultDto<VisualFlashSaleListResp> queryVisualFlashSaleList(@RequestBody FlashSaleQueryByIdReq request) throws TException;
}
