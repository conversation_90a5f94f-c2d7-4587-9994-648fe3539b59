package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.PlatFlashSaleConfigReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.ShopFlashSaleConfigReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description: 限时购配置操作服务
 */
@FeignClient(name = "himall-trade",contextId = "FlashSaleConfigCmdFeign", path = "/himall-trade/flashSaleConfig", url = "${himall-trade.dev.url:}")
public interface FlashSaleConfigCmdFeign {

    /**
     * 修改平台限时购配置
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updatePlatConfig", consumes = "application/json")
    ResultDto<BaseResp> updatePlatConfig(@RequestBody PlatFlashSaleConfigReq request) throws TException;

    /**
     * 修改店铺限时购配置
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateShopConfig", consumes = "application/json")
    ResultDto<BaseResp> updateShopConfig(@RequestBody ShopFlashSaleConfigReq request) throws TException;
}
