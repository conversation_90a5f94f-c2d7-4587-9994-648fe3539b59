package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 18:05
 */
@Data
@Schema(description = "组合购失效入参")
public class CancelCollocationReq extends BaseParamReq {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;


}
