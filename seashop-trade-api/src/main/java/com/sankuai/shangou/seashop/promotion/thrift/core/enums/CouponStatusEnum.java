package com.sankuai.shangou.seashop.promotion.thrift.core.enums;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
public enum CouponStatusEnum {

    // 未使用
    NOT_USED(0, "未使用"),
    // 已使用
    USED(1, "已使用"),
    // 已过期
    EXPIRED(2, "已过期"),
    // 未使用，且在当前时间内
    NOT_USED_AND_IN_CURRENT_TIME(10, "未使用，且在当前时间内")
    ;

    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    CouponStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
