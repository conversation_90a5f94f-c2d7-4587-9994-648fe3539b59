package com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:19
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "版式详情")
public class DescriptionTemplateDetailDto extends BaseThriftDto {

    @Schema(description = "版式id 新增不传/传0")
    private Long id;

    @Schema(description = "版式名称 最多30个字")
    private String name;

    @Schema(description = "版式位置")
    private DescriptionTemplatePositionEnum position;

    @Schema(description = "版式位置 1-顶部 2-底部")
    private Integer positionCode;

    @Schema(description = "版式位置描述")
    private String positionDesc;

    @Schema(description = "PC端版式内容")
    private String content;

    @Schema(description = "移动端版式内容")
    private String mobileContent;


}
