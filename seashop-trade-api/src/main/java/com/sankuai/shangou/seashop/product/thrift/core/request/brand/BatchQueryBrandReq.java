package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class BatchQueryBrandReq extends BaseParamReq {

    @Schema(description = "品牌id", required = true)
    private List<Long> idList;

    @Schema(description = "是否使用缓存")
    private Boolean useCache;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(CollUtil.isEmpty(idList), "品牌id不能为空");
    }


}
