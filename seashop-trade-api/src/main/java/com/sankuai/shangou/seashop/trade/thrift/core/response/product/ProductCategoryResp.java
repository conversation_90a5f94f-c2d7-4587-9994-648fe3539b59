package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/22 15:19
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "分类信息返回值")
public class ProductCategoryResp extends BaseThriftDto {

    @Schema(description = "商品分类id")
    private Long categoryId;

    @Schema(description = "商品分类名称")
    private String categoryName;


}
