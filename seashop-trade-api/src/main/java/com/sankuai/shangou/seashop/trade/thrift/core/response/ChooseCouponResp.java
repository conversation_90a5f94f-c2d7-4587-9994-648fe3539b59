package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopProductDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "选择优惠券时的返回")
@ToString
@Data
public class ChooseCouponResp extends BaseThriftDto {

    @Schema(description = "当前店铺及商品信息")
    private ShoppingCartShopProductDto shopProduct;
    @Schema(description = "预览订单页所有店铺以及对应商品总金额")
    private BigDecimal totalAmount;
    @Schema(description = "限时购活动id。如果不为空，代表是限时购，商品列表只会有一条记录")
    private Long flashSaleId;


}
