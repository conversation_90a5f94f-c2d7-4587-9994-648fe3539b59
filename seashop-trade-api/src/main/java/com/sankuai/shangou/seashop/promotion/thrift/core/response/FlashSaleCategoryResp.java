package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Schema(description = "限时购分类响应体")
@Data
public class FlashSaleCategoryResp {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "分类名称")
    private String categoryName;


}
