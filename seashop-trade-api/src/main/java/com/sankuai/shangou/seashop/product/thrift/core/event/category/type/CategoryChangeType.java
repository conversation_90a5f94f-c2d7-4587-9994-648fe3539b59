package com.sankuai.shangou.seashop.product.thrift.core.event.category.type;

import com.sankuai.shangou.seashop.product.thrift.core.event.ChangeType;
import lombok.Getter;

/**
 * 类目变动类型
 *
 * <AUTHOR>
 * @date 2024/03/06 9:05
 */
@Getter
public enum CategoryChangeType {

    /**
     * 业务编码规格 共五位
     * 前两位表示操作类型 10-创建 11-编辑 12-删除
     * 后三位表示具体操作
     *
     * <p>
     * 创建: 1.创建
     * <p>
     * 更新: 1.编辑
     * <p>
     * 删除: 1.删除
     */

    // 创建
    CREATE(10001, ChangeType.CREATE),

    // 更新

    EDIT(11001, ChangeType.UPDATE),

    // 删除

    DELETE(12001, ChangeType.DELETE);

    private final Integer code;
    private final ChangeType type;

    CategoryChangeType(Integer code, ChangeType type) {
        this.code = code;
        this.type = type;
    }

}
