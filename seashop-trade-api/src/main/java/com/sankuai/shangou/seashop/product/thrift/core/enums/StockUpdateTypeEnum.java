package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * <AUTHOR>
 * @date 2023/12/18 14:29
 */
public enum StockUpdateTypeEnum {

    /**
     * 1-编辑商品 2-批量调整库存 3-批量导入库存 4-生成订单 5-生成订单失败 6-牵牛花API生成订单 7-售后退回 8-订单关闭 9-聚水潭同步 10-牵牛花API关闭订单 11-旺店通同步 12-牵牛花API同步库存 13-网店管家/吉客云同步库存
     */
    NONE(0, "无", null),
    EDIT_PRODUCT(1, "编辑商品", null),
    LIST_BATCH_UPDATE(2, "批量调整库存", null),
    EXCEL_IMPORT_UPDATE(3, "批量导入库存", null),
    CREATE_ORDER(4, "生成订单", 5),
    CREATE_ORDER_FAIL(5, "生成订单失败", null),
    MT_API_CREATE_ORDER(6, "牵牛花API生成订单", 10),
    RETURN_STOCK(7, "售后退回", null),
    CLOSE_ORDER(8, "订单关闭", null),
    JST_SYNC(9, "聚水潭同步", null),
    MT_API_CLOSE_ORDER(10, "牵牛花API关闭订单", null),
    WDT_SYNC(11, "旺店通同步", null),
    MT_API_CHANNEL_SYNC(12, "牵牛花API同步库存", null),
    JKY_SYNC(13, "网店管家/吉客云同步库存", null),
    YJP_SYNC(14, "易久批同步库存", null);

    private Integer code;
    private String desc;
    private Integer rollBackCode;

    StockUpdateTypeEnum(Integer code, String desc, Integer rollBackCode) {
        this.code = code;
        this.desc = desc;
        this.rollBackCode = rollBackCode;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getRollBackCode() {
        return rollBackCode;
    }

    public void setRollBackCode(Integer rollBackCode) {
        this.rollBackCode = rollBackCode;
    }

    public static StockUpdateTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StockUpdateTypeEnum stockUpdateTypeEnum : StockUpdateTypeEnum.values()) {
            if (stockUpdateTypeEnum.getCode().equals(code)) {
                return stockUpdateTypeEnum;
            }
        }
        return null;
    }
}
