package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * <AUTHOR>
 * @date 2023/12/05 11:53
 */
public enum ReplyStatusEnum {

    ALL(0, "全部"),
    UN_HANDLE(1, "未处理");

    private final Integer code;
    private final String desc;

    ReplyStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
