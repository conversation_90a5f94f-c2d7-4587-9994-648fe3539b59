package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * 商品版式模板位置枚举
 * 位置（1-上、2-下）
 *
 * <AUTHOR>
 * @date 2023/11/20 10:15
 */
public enum DescriptionTemplatePositionEnum {

    /**
     * 位置（1-上、2-下）
     */
    UP(1, "顶部"),
    DOWN(2, "底部");

    private Integer code;
    private String desc;

    DescriptionTemplatePositionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DescriptionTemplatePositionEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DescriptionTemplatePositionEnum value : DescriptionTemplatePositionEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
