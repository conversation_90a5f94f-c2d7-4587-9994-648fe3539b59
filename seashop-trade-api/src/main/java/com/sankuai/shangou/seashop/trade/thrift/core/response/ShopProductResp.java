package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "商家购物车SKU数量返回对象")
@ToString
@Data
public class ShopProductResp extends BaseThriftDto {

    @Schema(description = "店铺信息")
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表")
    private List<ShoppingCartProductDto> productList;


}
