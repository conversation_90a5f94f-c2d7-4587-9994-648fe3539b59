package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "店铺满减活动对象")
public class ShopReductionDto extends BaseThriftDto {

    @Schema(description = "满减活动ID")
    private Long activeId;
    @Schema(description = "满减活动名称")
    private String activeName;
    @Schema(description = "单笔订单满减金额门槛")
    private BigDecimal moneyOffCondition;
    @Schema(description = "单笔订单满减金额")
    private BigDecimal moneyOffFee;
    @Schema(description = "是否叠加优惠")
    private Boolean moneyOffOverLay;
    @Schema(description = "开始时间")
    private Date startTime;
    @Schema(description = "结束时间")
    private Date endTime;


}
