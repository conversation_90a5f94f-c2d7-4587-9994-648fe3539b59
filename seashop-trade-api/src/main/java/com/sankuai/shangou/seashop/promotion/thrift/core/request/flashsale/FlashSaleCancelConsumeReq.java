package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "撤销核销限时购请求对象")
public class FlashSaleCancelConsumeReq extends BaseParamReq {

    @Schema(description = "订单ID", required = true)
    private String orderId;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("订单ID不能为空");
        }
    }


}
