package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.AddonActivityDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "凑单页面对应的活动对象")
@ToString
@Data
public class AddonActivityResp extends BaseThriftDto {

    @Schema(description = "活动列表")
    private List<AddonActivityDto> activityList;


}
