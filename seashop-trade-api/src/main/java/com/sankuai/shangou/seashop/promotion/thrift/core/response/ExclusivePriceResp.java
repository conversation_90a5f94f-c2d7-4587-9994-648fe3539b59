package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@Schema(description = "专享价活动响应体")
@Data
public class ExclusivePriceResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    @ExaminField(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "活动名称")
    @ExaminField(description = "活动名称")
    private String name;

    @Schema(description = "开始时间")
    @ExaminField(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    @ExaminField(description = "结束时间")
    private Date endTime;

    @Schema(description = "状态")
    @ExaminField(description = "状态：-1已失效,0未开始,1进行中,2已结束")
    private Integer status;

    @Schema(description = "专享价商品列表")
    @ExaminField(description = "专享价商品列表", isChildField = true)
    private List<ExclusivePriceProductDto> productList;


}
