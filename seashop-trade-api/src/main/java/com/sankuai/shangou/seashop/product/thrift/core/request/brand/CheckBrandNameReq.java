package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/02/21 9:03
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class CheckBrandNameReq extends BaseParamReq {

    @Schema(description = "品牌名称", required = true)
    private String brandName;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(brandName), "品牌名称不能为空");
    }


}
