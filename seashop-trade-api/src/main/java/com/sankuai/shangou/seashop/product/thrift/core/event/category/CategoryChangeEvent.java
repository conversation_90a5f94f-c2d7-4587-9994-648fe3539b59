package com.sankuai.shangou.seashop.product.thrift.core.event.category;

import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.product.thrift.core.event.category.type.CategoryChangeType;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/03/06 9:08
 */
@Setter
@Getter
public class CategoryChangeEvent {

    /**
     * 类目id
     */
    private List<Long> categoryIds;

    /**
     * 变更时间
     */
    private Date date;

    /**
     * 商品变更类型
     */
    private CategoryChangeType changeType;

    public static CategoryChangeEvent build(List<Long> categoryIds, CategoryChangeType changeType) {
        CategoryChangeEvent event = new CategoryChangeEvent();
        event.setCategoryIds(categoryIds);
        event.setDate(new Date());
        event.setChangeType(changeType);
        return event;
    }

}
