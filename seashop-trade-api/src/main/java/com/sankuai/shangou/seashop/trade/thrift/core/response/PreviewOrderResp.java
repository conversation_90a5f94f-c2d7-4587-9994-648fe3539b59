package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.PreviewOrderSummaryDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopProductDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "预览订单返回对象")
@ToString
@Data
public class PreviewOrderResp extends BaseThriftDto {

    @Schema(description = "用户收货地址，可能为空")
    private ShippingAddressDto shippingAddress;
    @Schema(description = "按店铺分组的商品列表")
    private List<ShoppingCartShopProductDto> shopProductList;
    @Schema(description = "限时购活动id。如果不为空，代表是限时购，商品列表只会有一条记录")
    private Long flashSaleId;
    /**
     * 组合购活动id。如果不为空，代表是组合购
     */
    @Schema(description = "组合购活动id。如果不为空，代表是组合购")
    private Long collocationId;
    @Schema(description = "预览订单页汇总信息")
    private PreviewOrderSummaryDto summary;
    @Schema(description = "订单ID，创建订单成功会有")
    private List<String> orderIdList;
    @Schema(description = "是否立即购买")
    private Boolean whetherBuyNow;
    /**
     * 预览订单页所有商品总金额
     */
    @Schema(description = "预览订单页所有商品总金额")
    private BigDecimal totalAmount;
    @Schema(description = "是否需要支付。如果实付金额时0元，则不需要调起支付")
    private Boolean needPay;
    @Schema(description = "是否成功")
    private boolean success;

    private List<String> allOrderList;


    public boolean isSuccess() {
        return success;
    }

}
