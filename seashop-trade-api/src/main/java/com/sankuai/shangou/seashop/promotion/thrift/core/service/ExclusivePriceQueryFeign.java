package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndMemberIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceDetailResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ExclusivePriceSimpleResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description: 提供专享价活动查询功能
 */
@FeignClient(name = "himall-trade", contextId = "ExclusivePriceQueryFeign", path = "/himall-trade/exclusivePrice", url = "${himall-trade.dev.url:}")
public interface ExclusivePriceQueryFeign {

    /**
     * 专享价活动列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<ExclusivePriceSimpleResp>> pageList(@RequestBody ExclusivePriceQueryReq request) throws TException;

    /**
     * 通过id查询专享价活动信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getById", consumes = "application/json")
    ResultDto<ExclusivePriceResp> getById(@RequestBody BaseIdReq request) throws TException;

    /**
     * 通过商品和会员ID查询专享价活动信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryByProductAndMemberId", consumes = "application/json")
    ResultDto<ExclusivePriceResp> queryByProductAndMemberId(@RequestBody ProductAndMemberIdReq request) throws TException;

    /**
     * 专享价活动列表明细查询(导出使用)
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/detailPageList", consumes = "application/json")
    ResultDto<BasePageResp<ExclusivePriceDetailResp>> detailPageList(@RequestBody ExclusivePriceQueryReq request) throws TException;
}
