package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/27 16:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class QueryBrandApplyDetailReq extends BaseParamReq {

    @Schema(description = "品牌申请id", required = true)
    private Long id;

    @Schema(description = "店铺id(供应商端必填)")
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "品牌申请id不能为空");
    }

    public void checkForSeller() {
        checkParameter();
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
