package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/12/22/022
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品和会员ID请求体")
public class ProductAndMemberIdReq extends BaseParamReq {

    @Schema(description = "商品ID", required = true)
    private Long productId;

    @Schema(description = "会员ID", required = true)
    private Long memberId;

    @Override
    public void checkParameter() {
        if (null == this.productId) {
            throw new InvalidParamException("productId不能为空");
        }
        if (null == this.memberId) {
            throw new InvalidParamException("memberId不能为空");
        }
    }


}
