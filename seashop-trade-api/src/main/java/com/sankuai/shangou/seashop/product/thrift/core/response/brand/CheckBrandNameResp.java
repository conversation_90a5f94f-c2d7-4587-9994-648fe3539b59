package com.sankuai.shangou.seashop.product.thrift.core.response.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/02/21 9:05
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "校验品牌名称返回值")
public class CheckBrandNameResp extends BaseThriftDto {

    @Schema(description = "提示信息(不为空则展示提示信息)")
    private String tips;


}
