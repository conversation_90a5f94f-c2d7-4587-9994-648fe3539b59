package com.sankuai.shangou.seashop.product.thrift.core.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/11/07 14:37
 */
public class BrandEnum {

    /**
     * 申请类型（apply_mode）枚举 1-平台已有品牌 2-供应商新增品牌
     */
    @Getter
    public enum ApplyModeEnum {

        EXISTING_BRAND(1, "平台已有品牌"),
        NEW_BRAND(2, "供应商新增品牌");

        private Integer code;
        private String desc;


        ApplyModeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ApplyModeEnum getByCode(Integer code) {
            for (ApplyModeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 审核状态 0-未审核 1-审核通过 2-审核拒绝
     */
    public enum AuditStatusEnum {
        UNAUDITED(0, "待审核"),
        AUDITED(1, "审核通过"),
        AUDIT_REFUSED(2, "审核拒绝");

        private Integer code;
        private String desc;

        AuditStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static AuditStatusEnum getByCode(Integer code) {
            for (AuditStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
