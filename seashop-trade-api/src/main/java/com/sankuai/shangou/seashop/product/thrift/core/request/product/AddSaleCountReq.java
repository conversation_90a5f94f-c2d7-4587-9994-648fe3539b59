package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.AddProductSaleCountDto;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/05 19:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "增加销量入参")
public class AddSaleCountReq extends BaseParamReq {

    @Schema(description = "增加销量的商品集合", required = true)
    private List<AddProductSaleCountDto> productList;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productList), "增加销量的商品集合不能为空");
        productList.forEach(AddProductSaleCountDto::checkParameter);
    }


}
