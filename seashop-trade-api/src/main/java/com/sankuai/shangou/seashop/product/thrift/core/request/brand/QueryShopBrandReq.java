package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/08 11:11
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class QueryShopBrandReq extends BasePageReq {

    @Schema(description = "商家id", required = true)
    private Long shopId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "商家id不能为空");
    }


}
