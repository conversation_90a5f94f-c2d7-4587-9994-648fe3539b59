package com.sankuai.shangou.seashop.product.thrift.core.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/02/17 14:13
 */
@Getter
public enum ProductRichTextEnum {

    /**
     * 1-PC端 2-移动端
     */
    PC(1, "PC端"),

    MOBILE(2, "移动端");

    private Integer code;
    private String desc;

    ProductRichTextEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductRichTextEnum getByCode(Integer code) {
        for (ProductRichTextEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
