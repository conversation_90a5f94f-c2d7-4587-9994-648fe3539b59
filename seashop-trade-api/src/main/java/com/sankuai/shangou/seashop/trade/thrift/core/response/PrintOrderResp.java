package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:16
 */
@Data
@Schema(description = "打印订单总出参")
public class PrintOrderResp extends BaseThriftDto {

    @Schema(description = "订单集合")
    private List<OrderResp> orderRespList;


}
