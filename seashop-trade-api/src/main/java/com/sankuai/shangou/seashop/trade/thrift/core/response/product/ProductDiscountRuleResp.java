package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "折扣规则返回值")
public class ProductDiscountRuleResp extends BaseThriftDto {

    @Schema(description = "折扣门槛(订单金额需要满足多少)")
    private BigDecimal quota;

    @Schema(description = "折扣比例(达到门槛后商品单价的优惠比例)")
    private BigDecimal discount;


}
