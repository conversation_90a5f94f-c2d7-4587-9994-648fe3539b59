package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 17:48
 */
@Data
@Schema(description = "组合购详情入参")
public class CollocationDetailReq extends BaseParamReq {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "1平台，2供应商，3商家端")
    private Integer sourceFrom;


}
