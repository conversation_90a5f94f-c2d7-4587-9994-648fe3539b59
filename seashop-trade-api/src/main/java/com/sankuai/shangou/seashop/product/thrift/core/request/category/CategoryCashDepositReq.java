package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "保证金配置")
public class CategoryCashDepositReq extends BaseParamReq {

    /**
     * 类目id
     */
    @Schema(description = "类目id")
    private Long categoryId;

    /**
     * 类目名称
     */
    @Schema(description = "类目名称")
    private String categoryName;

    /**
     * 需要缴纳保证金
     */
    @Schema(description = "需要缴纳保证金")
    private BigDecimal needPayCashDeposit;

    /**
     * 允许七天无理由退货
     */
    @Schema(description = "允许七天无理由退货")
    private Boolean enableNoReasonReturn;

    @Override
    public void checkParameter() {
        if (categoryId == null) {
            throw new InvalidParamException("categoryId不能为空");
        }
        if (needPayCashDeposit == null && needPayCashDeposit.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("needPayCashDeposit不能为空且不能小于0");
        }
        if (this.needPayCashDeposit.compareTo(new BigDecimal("1000000")) >= 0) {
            throw new InvalidParamException("needPayCashDeposit不能大于等于1000000");
        }
        if (enableNoReasonReturn == null) {
            throw new InvalidParamException("enableNoReasonReturn不能为空");
        }
    }


}
