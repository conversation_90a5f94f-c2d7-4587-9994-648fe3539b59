package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/17 18:25
 */
@Schema(description = "限时购活动响应体")
@Data
public class MallAppletFlashSaleResp extends BaseThriftDto {

    /**
     * 商品信息
     */
    @Schema(description = "商品id")
    private String productId;

    @Schema(description = "商品图片")
    private String imagePath;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "最小销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "状态码")
    private Integer status;

    @Schema(description = "状态")
    private String statusDesc;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    /**
     * 限时购活动信息
     */
    @Schema(description = "活动开始日期")
    private Date beginDate;

    @Schema(description = "活动结束日期")
    private Date endDate;

    @Schema(description = "限购类型:1商品;2规格")
    private Integer limitType;

    @Schema(description = "限购数量")
    private Integer limitCount;

    @Schema(description = "仅仅只计算在限时购里的销售数")
    private Integer saleCount;

    @Schema(description = "活动id")
    private Long categoryId;

    @Schema(description = "跳转路径")
    private String urlPath;

    @Schema(description = "最小价格（多规格价格可能不一样）")
    private BigDecimal minPrice;

    @Schema(description = "限时购id")
    private Long flashSaleId;

    @Schema(description = "距离开始的时间（单位秒）")
    private Long remStartTime = 0L;

    @Schema(description = "距离结束的时间（单位秒）")
    private Long remEndTime = 0L;


}
