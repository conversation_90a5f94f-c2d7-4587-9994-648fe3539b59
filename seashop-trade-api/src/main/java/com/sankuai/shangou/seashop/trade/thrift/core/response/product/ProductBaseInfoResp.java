package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.response.CommentSummaryResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/22 15:17
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品基本信息返回值")
public class ProductBaseInfoResp extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "货号")
    private String productCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "广告词")
    private String shortDescription;

    @Schema(description = "分类集合")
    private List<ProductCategoryResp> categoryList;

    @Schema(description = "销量")
    private Integer salesCount;

    @Schema(description = "商品图片集合")
    private List<String> imageList;

    @Schema(description = "是否收藏")
    private Boolean collectStatus;

    @Schema(description = "默认收货地址")
    private UserShippingAddressResp defaultShippingAddress;

    @Schema(description = "是否有多规格")
    private Boolean hasSku;

    @Schema(description = "规格组")
    private List<ProductSpecGroupResp> specGroupList;

    @Schema(description = "规格集合")
    private List<ProductSkuResp> skuList;

    @Schema(description = "属性集合")
    private List<ProductAttributeResp> attributeList;

    @Schema(description = "商品活动信息")
    private ProductActivityInfoResp activityInfo;

    @Schema(description = "是否可售")
    private Boolean saleAble;

    @Schema(description = "商品评价信息")
    private CommentSummaryResp commentSummary;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "预估价")
    private BigDecimal estimatePrice;

    @Schema(description = "最大购买量")
    private Integer maxBuyCount;

    @Schema(description = "倍数起购量")
    private Integer multipleCount;

    @Schema(description = "运费模板信息")
    private FreightTemplateResp freightTemplateInfo;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "咨询数量")
    private Integer consultCount;

    @Schema(description = "视频地址")
    private String videoPath;

    @Schema(description = "计量单位")
    private String measureUnit;

    @Schema(description = "最大售价")
    private BigDecimal maxSalePrice;

    @Schema(description = "销售价格范围")
    private String salePriceRange;

    @Schema(description = "品牌logo")
    private String brandLogo;

    @Schema(description = "交易状态 0-已售罄 1-销售中 2-已下架")
    private Integer tradeStatus;

    @Schema(description = "总库存")
    private Long totalStock;

    @Schema(description = "限时购库存")
    private Long flashSaleTotalStock;

    @Schema(description = "移动端商品描述图片列表")
    private List<String> descriptionPicList;

    @Schema(description = "OE号")
    private String oeCode;

    @Schema(description = "品牌号")
    private String brandCode;

    @Schema(description = "零件品质 [0 4S-原厂原包,1 进口-原厂原包 2 国产-原厂原包 3 原厂无包 4 品牌件]")
    private Integer partQuality;

    @Schema(description = "质保时间")
    private Integer warrantyPeriod;

    @Schema(description = "适用车型")
    private String adaptableCar;

    @Schema(description = "零件规格")
    private String partSpec;

    @Schema(description = "替换号")
    private String replaceNumber;

    @Schema(description = "备注")
    private String remark;
}
