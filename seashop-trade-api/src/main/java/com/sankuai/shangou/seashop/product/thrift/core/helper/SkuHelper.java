package com.sankuai.shangou.seashop.product.thrift.core.helper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.dto.SpecDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSpecEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecValueDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2024/01/10 18:48
 */
public class SkuHelper {

    private static final Long[] SKU_ID_ARR = {0L, 0L, 0L, 0L};
    private static final String SKU_ID_SPLIT = "_";

    public static String generateSkuId(Long productId, Integer spec, Long specValueId) {
        ProductSpecEnum specEnum = ProductSpecEnum.getByCode(spec);
        AssertUtil.throwIfNull(specEnum, "规格不存在");

        Long[] skuIdArr = SKU_ID_ARR.clone();
        skuIdArr[0] = productId;
        skuIdArr[spec] = specValueId;
        return StrUtil.join(SKU_ID_SPLIT, skuIdArr);
    }

    public static String generateDefaultSkuId(Long productId) {
        return generateSkuId(productId, ProductSpecEnum.SPEC_1.getCode(), 0L);
    }

    public static String generateSkuId(Long productId, List<Long> specValueIds) {
        Long[] skuIdArr = SKU_ID_ARR.clone();
        skuIdArr[0] = productId;

        if (CollUtil.isNotEmpty(specValueIds)) {
            int size = NumberUtil.min(specValueIds.size(), ParameterConstant.MAX_SPEC_SIZE);
            for (int i = 0; i < size; i++) {
                skuIdArr[i + 1] = specValueIds.get(i);
            }
        }
        return StrUtil.join(SKU_ID_SPLIT, skuIdArr);
    }

    public static List<SpecDto> parseSpecJson(String specValueJson) {
        if (StrUtil.isEmpty(specValueJson)) {
            return Collections.emptyList();
        }

        return JsonUtil.parseArray(specValueJson, SpecDto.class);
    }

    public static List<SpecificationResp> parseSpecSelect(List<SpecDto> specList, Map<Long, String> imageMap) {
        if (CollUtil.isEmpty(specList)) {
            return Collections.emptyList();
        }

        if (imageMap == null) {
            imageMap = Collections.emptyMap();
        }

        Map<Long, SpecificationResp> specSelectMap = new LinkedHashMap<>();

        for (SpecDto spec : specList) {
            SpecificationResp specSelect = specSelectMap.get(spec.getNameId());
            if (specSelect == null) {
                specSelect = new SpecificationResp();
                specSelect.setId(spec.getNameId());
                specSelect.setName(spec.getSpecName());
                specSelect.setAlias(spec.getSpecAlias());
                specSelect.setValues(new ArrayList<>());
            }

            // 前端已经设置过, 所以vales 肯定不会为null
            List<SpecValueDto> values = specSelect.getValues();
            boolean exist = values.stream().anyMatch(value -> value.getId().equals(spec.getValueId()));
            if (exist) {
                continue;
            }

            SpecValueDto value = new SpecValueDto();
            value.setId(spec.getValueId());
            value.setValue(spec.getSpecValue());
            value.setShowPic(imageMap.get(spec.getValueId()));
            values.add(value);
            specSelectMap.put(spec.getNameId(), specSelect);
        }

        return specSelectMap.values().stream().collect(Collectors.toList());
    }

}
