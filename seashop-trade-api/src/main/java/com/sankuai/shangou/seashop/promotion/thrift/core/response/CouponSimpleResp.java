package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@Schema(description = "优惠券活动响应体")
@Data
public class CouponSimpleResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "优惠劵金额")
    private BigDecimal price;

    @Schema(description = "最大可领取张数")
    private Integer perMax;

    @Schema(description = "订单金额（满足多少钱才能使用）")
    private BigDecimal orderAmount;

    @Schema(description = "发行张数")
    private Integer num;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "领用人数")
    private Integer receiveCount;

    @Schema(description = "领用张数")
    private Integer receiveNum;

    @Schema(description = "已使用数量")
    private Integer useCount;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusDesc;

    @Schema(description = "商品数量")
    private Long productCount;

    @Schema(description = "使用范围：0=全场通用，1=部分商品可用")
    private Integer useArea;

    @Schema(description = "是否已领取")
    private Boolean userReceived = false;

    @Schema(description = "用户领取的数量")
    private Integer userReceivedNum = 0;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "用户已使用数量")
    private Integer userUsedUum = 0;

    @Schema(description = "领取方式 0 店铺首页 1 积分兑换 2 主动发放")
    private Integer receiveType;

    private Long productId;


}
