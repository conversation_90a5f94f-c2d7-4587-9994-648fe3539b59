package com.sankuai.shangou.seashop.product.thrift.core.response.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/28 10:44
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "品牌列表返回值")
public class BrandListResp extends BaseThriftDto {

    @Schema(description = "品牌列表")
    private List<BrandDto> brandList;


}
