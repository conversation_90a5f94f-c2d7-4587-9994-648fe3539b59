package com.sankuai.shangou.seashop.product.thrift.core.response.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "保证金配置")
public class CategoryCashDepositResp extends BaseThriftDto {

    /**
     * 类目id
     */
    @Schema(description = "类目id")
    private Long categoryId;

    /**
     * 类目名称
     */
    @Schema(description = "类目名称")
    private String categoryName;

    /**
     * 需要缴纳保证金
     */
    @Schema(description = "需要缴纳保证金")
    private BigDecimal needPayCashDeposit;

    /**
     * 允许七天无理由退货
     */
    @Schema(description = "允许七天无理由退货")
    private Boolean enableNoReasonReturn;


}
