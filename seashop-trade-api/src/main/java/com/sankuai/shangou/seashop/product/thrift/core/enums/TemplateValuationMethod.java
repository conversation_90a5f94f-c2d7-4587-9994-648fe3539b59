package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * <AUTHOR>
 * @date 2024/04/16 17:50
 */
public enum TemplateValuationMethod {

    WEIGHT(1, "重量"),
    VOLUME(2, "体积"),
    QUANTITY(0, "件数"),

    ;

    private final Integer code;
    private final String desc;

    TemplateValuationMethod(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static TemplateValuationMethod getByCode(Integer code) {
        for (TemplateValuationMethod valuationMethod : TemplateValuationMethod.values()) {
            if (valuationMethod.getCode().equals(code)) {
                return valuationMethod;
            }
        }
        return QUANTITY;
    }
}
