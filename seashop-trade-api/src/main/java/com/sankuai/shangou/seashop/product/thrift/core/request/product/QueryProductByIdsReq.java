package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/18 13:40
 */
@Schema(description = "商品分页信息")
@ToString
@Data
public class QueryProductByIdsReq extends BaseParamReq {

    @Schema(description = "商品ID集合")
    private List<String> productIdList;

    @Schema(description = "商品名称")
    private String productName;

    public void checkParameter() {
        if (CollectionUtils.isEmpty(productIdList) && StringUtils.isEmpty(productName)) {
            throw new BusinessException("商品ID集合和商品名称不能同时为空");
        }
    }


}
