package com.sankuai.shangou.seashop.product.thrift.core.request.specification;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

import java.util.List;

@Data
public class SpecificationReq extends BasePageReq {
    private Long id;
    private Long shopId;
    private String name;
    private String alias;
    private List<SpecValueDto> values;
    private List<Long> ids;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(shopId, "shopId 不能为空");
        AssertUtil.throwIfTrue(StrUtil.isEmpty(name), "规格名称不能为空");
        AssertUtil.throwIfTrue(StrUtil.isEmpty(alias), "规格别名不能为空");

        if (CollUtil.isNotEmpty(values)) {
            values.forEach(value -> {
                AssertUtil.throwIfTrue(StrUtil.isEmpty(value.getValue()), "规格值不能为空");
            });
        }
    }
}
