package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/29 17:21
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class QueryRecommendBrandReq extends BaseParamReq {

    @Schema(description = "分类id")
    private Long categoryId;


}
