package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * 虚拟销量类型枚举 1-固定数 2-随机数
 *
 * <AUTHOR>
 * @date 2023/11/21 17:23
 */
public enum VirtualSaleCountsTypeEnum {

    /**
     * 固定数
     */
    FIXED(1, "固定数"),
    /**
     * 随机数
     */
    RANDOM(2, "随机数");

    private Integer code;
    private String desc;

    VirtualSaleCountsTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VirtualSaleCountsTypeEnum getByCode(Integer code) {
        for (VirtualSaleCountsTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
