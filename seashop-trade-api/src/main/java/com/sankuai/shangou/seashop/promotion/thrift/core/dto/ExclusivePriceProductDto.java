package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "专享价商品响应对象")
public class ExclusivePriceProductDto extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "活动ID")
    private Long activeId;

    @Schema(description = "商品ID")
    @ExaminField(description = "商品ID")
    private Long productId;

    @Schema(description = "商品名称")
    @ExaminField(description = "商品名称")
    private String productName;

    @Schema(description = "skuId")
    private String skuId;

    @Schema(description = "sku自增id")
    @ExaminField(description = "规格id")
    private Long skuAutoId;

    @Schema(description = "sku名称")
    @ExaminField(description = "规格名称")
    private String skuName;

    @Schema(description = "商城价格")
    @ExaminField(description = "商城价格")
    private BigDecimal mallPrice;

    @Schema(description = "专享价格")
    @ExaminField(description = "专享价格")
    private BigDecimal price;

    @Schema(description = "会员id")
    @ExaminField(description = "会员id")
    private Long memberId;

    @Schema(description = "会员名称")
    @ExaminField(description = "会员名称")
    private String userName;


}
