package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordIdOrSnReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description: 提供优惠券记录查询功能
 */
@FeignClient(name = "himall-trade",contextId = "CouponRecordQueryFeign", path = "/himall-trade/couponRecord", url = "${himall-trade.dev.url:}")
public interface CouponRecordQueryFeign {

    /**
     * 优惠券记录列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<CouponRecordSimpleResp>> pageList(@RequestBody CouponRecordQueryReq request) throws TException;

    /**
     * 通过id或者sn码查询优惠券记录信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getByIdOrSn", consumes = "application/json")
    ResultDto<CouponRecordSimpleListResp> getByIdOrSn(@RequestBody CouponRecordIdOrSnReq request) throws TException;

    /**
     * 通过id查询优惠券记录信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getUserRecordById", consumes = "application/json")
    ResultDto<CouponRecordSimpleResp> getUserRecordById(@RequestBody CouponRecordIdReq request) throws TException;

    /**
     * 查询订单可用优惠券列表
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getRecordByOrder", consumes = "application/json")
    ResultDto<CouponRecordOrderListResp> getRecordByOrder(@RequestBody PromotionRecordOrderQueryReq request) throws TException;



}
