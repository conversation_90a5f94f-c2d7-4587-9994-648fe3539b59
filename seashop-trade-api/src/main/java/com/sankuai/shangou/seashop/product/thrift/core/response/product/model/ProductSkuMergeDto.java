package com.sankuai.shangou.seashop.product.thrift.core.response.product.model;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "SKU维度的商品信息")
@ToString
@Data
public class ProductSkuMergeDto extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "类目id")
    private Long categoryId;

    @Schema(description = "类目名称")
    private String categoryName;

    @Schema(description = "类目路径")
    private String categoryPath;

    @Schema(description = "类目全路径")
    private String fullCategoryName;

    @Schema(description = "品牌id")
    private Long brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "销售状态 1-销售中 2-仓库中 3-草稿箱")
    private ProductEnum.SaleStatusEnum saleStatus;

    @Schema(description = "审核状态 1-待审核 2-销售中 3-未通过 4-违规下架")
    private ProductEnum.AuditStatusEnum auditStatus;

    @Setter
    @Getter
    @Schema(description = "添加时间")
    private Date addedDate;

    @Setter
    @Getter
    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Setter
    @Getter
    @Schema(description = "最小销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "是否有sku")
    private Boolean hasSku;

    @Schema(description = "运费模板ID")
    private Long freightTemplateId;

    @Setter
    @Getter
    @Schema(description = "重量")
    private BigDecimal weight;

    @Setter
    @Getter
    @Schema(description = "体积")
    private BigDecimal volume;

    @Schema(description = "最大购买数")
    private Integer maxBuyCount;

    @Schema(description = "是否开启阶梯价")
    private Boolean whetherOpenLadder;

    @Schema(description = "规格1别名")
    private String spec1Alias;

    @Schema(description = "规格2别名")
    private String spec2Alias;

    @Schema(description = "规格3别名")
    private String spec3Alias;

    @Schema(description = "商品主图")
    private String imagePath;

    @Schema(description = "倍数起购量")
    private Integer multipleCount;
    @Schema(description = "是否删除")
    private Boolean whetherDelete;

    @Schema(description = "sku自增id")
    private Long skuAutoId;

    @Schema(description = "sku 拼接id 商品ID_规格1ID_规格2ID_规格3ID")
    private String skuId;

    @Schema(description = "规格1")
    private String spec1Value;

    @Schema(description = "规格2")
    private String spec2Value;

    @Schema(description = "规格3")
    private String spec3Value;

    @Setter
    @Getter
    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "安全库存")
    private Long safeStock;

    @Schema(description = "阶梯价信息")
    private List<LadderPriceDto> ladderPriceList;

    @Schema(description = "spec名称")
    private String specName;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "显示图片")
    private String showPic;

    @Schema(description = "规格货号")
    private String skuCode;

    @Schema(description = "类目id的集合")
    private List<Long> categoryIds;

    @Schema(description = "合并数量")
    private Long mergeCount;

    @Schema(description = "计量单位 优先取sku的 没有返回product的")
    private String measureUnit;

    @Schema(description = "商品规格")
    private String skuName;


    public String getSpec1Alias() {
        return spec1Alias;
    }

    public void setSpec1Alias(String spec1Alias) {
        this.spec1Alias = spec1Alias;
    }

    public String getSpec2Alias() {
        return spec2Alias;
    }

    public void setSpec2Alias(String spec2Alias) {
        this.spec2Alias = spec2Alias;
    }

    public String getSpec3Alias() {
        return spec3Alias;
    }

    public void setSpec3Alias(String spec3Alias) {
        this.spec3Alias = spec3Alias;
    }


    public String getSpec1Value() {
        return spec1Value;
    }

    public void setSpec1Value(String spec1Value) {
        this.spec1Value = spec1Value;
    }

    public String getSpec2Value() {
        return spec2Value;
    }

    public void setSpec2Value(String spec2Value) {
        this.spec2Value = spec2Value;
    }

    public String getSpec3Value() {
        return spec3Value;
    }

    public void setSpec3Value(String spec3Value) {
        this.spec3Value = spec3Value;
    }


}
