package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/12 11:32
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询热门关注商品入参")
public class QueryHotAttentionProductReq extends BaseParamReq {

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "用户id")
    private Long userId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
