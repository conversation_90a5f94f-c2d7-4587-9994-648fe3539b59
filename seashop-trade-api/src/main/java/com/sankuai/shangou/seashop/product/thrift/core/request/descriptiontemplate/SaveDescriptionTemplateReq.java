package com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate;

import io.swagger.v3.oas.annotations.media.Schema;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:19
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保存版式模板入参")
public class SaveDescriptionTemplateReq extends BaseParamReq {

    @Schema(description = "版式id 新增不传/传0")
    @PrimaryField(title = "版式id")
    @ExaminField(description = "版式id")
    private Long id;

    @Schema(description = "版式名称 最多30个字", required = true)
    @ExaminField(description = "版式名称")
    private String name;

    @Schema(description = "版式位置", required = true)
    @ExaminField(description = "版式位置")
    private DescriptionTemplatePositionEnum position;

    @Schema(description = "PC端版式内容", required = true)
    @ExaminField(description = "PC端版式内容")
    private String content;

    @Schema(description = "移动端版式内容", required = true)
    @ExaminField(description = "移动端版式内容")
    private String mobileContent;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(name), "版式名称不能为空");
        AssertUtil.throwInvalidParamIfTrue(name.length() > ParameterConstant.DESCRIPTION_TEMPLATE_NAME_LENGTH,
                String.format("版式名称不能超过%d个字", ParameterConstant.DESCRIPTION_TEMPLATE_NAME_LENGTH));
        AssertUtil.throwInvalidParamIfTrue(position == null, "版式位置不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(content), "PC端版式内容不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(mobileContent), "移动端版式内容不能为空");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }

    public void checkForEdit() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "版式id不能为空");
        checkParameter();
    }


}
