package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.DiscountActiveProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.DiscountActiveQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryDiscountActiveProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.DiscountActiveSimpleResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description: 提供折扣活动查询功能
 */
@FeignClient(name = "himall-trade",contextId = "DiscountActiveQueryFeign", path = "/himall-trade/discountActive", url = "${himall-trade.dev.url:}")
public interface DiscountActiveQueryFeign {

    /**
     * 折扣活动列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<DiscountActiveSimpleResp>> pageList(@RequestBody DiscountActiveQueryReq request) throws TException;

    /**
     * 通过id查询折扣活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getById", consumes = "application/json")
    ResultDto<DiscountActiveResp> getById(@RequestBody BaseIdReq request) throws TException;

    /**
     * 通过商品id查询折扣活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryByProductId", consumes = "application/json")
    ResultDto<DiscountActiveResp> queryByProductId(@RequestBody ProductAndShopIdReq request) throws TException;

    /**
     * 查询折扣商品
     */
    @PostMapping(value = "/queryDiscountActiveProduct", consumes = "application/json")
    ResultDto<BasePageResp<DiscountActiveProductDto>> queryDiscountActiveProduct(@RequestBody QueryDiscountActiveProductReq request);
}
