package com.sankuai.shangou.seashop.product.thrift.core.request.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.dto.SpecDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.SkuUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.helper.ParameterHelper;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:46
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保存sku入参")
public class SaveProductSkuDto extends BaseParamReq {

    @Schema(description = "skuId 新增时不填/填0")
    private Long skuAutoId;

    @Schema(description = "skuId 商品ID_规格1ID_规格2ID_规格3ID", required = true)
    private String skuId;

    @Schema(description = "销售价", required = true)
    private BigDecimal salePrice;

    @Schema(description = "库存", required = true)
    private Long stock;

    @Schema(description = "货号")
    private String skuCode;

    @Schema(description = "警戒库存")
    private Long safeStock;

    @Schema(description = "计量单位", required = true)
    private String measureUnit;

    @Schema(description = "规格值集合(单规格时为空)")
    private List<SpecDto> specList;

    @Schema(description = "规格图片")
    private String showPic;

    public void checkParameter(boolean whetherOpenLadderPrice) {
        if (!whetherOpenLadderPrice) {
            AssertUtil.throwInvalidParamIfNull(salePrice, "销售价不能为空");
            AssertUtil.throwInvalidParamIfTrue(salePrice.compareTo(BigDecimal.ZERO) < 0, "销售价必须大于0");
        }
        AssertUtil.throwInvalidParamIfNull(stock, "库存不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(skuCode), "货号不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(measureUnit), "计量单位不能为空");
    }

    public void checkCoreParam(SkuUpdateKeyEnum skuUpdateKey) {
        switch (skuUpdateKey) {
            case SKU_ID:
                AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(skuId), "skuId不能为空");
                AssertUtil.throwInvalidParamIfTrue(skuId.split(ParameterConstant.SKU_ID_SPLIT).length != ParameterConstant.SKU_ID_SPLIT_LENGTH,
                        "skuId格式不正确");
                break;
            case SKU_CODE:
                AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(skuCode), "货号不能为空");
                break;
            default:
                AssertUtil.throwInvalidParamIfNull(skuAutoId, "skuAutoId不能为空");
                break;
        }

        if (StringUtils.isNotEmpty(skuCode)) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkSkuCode(skuCode), "规格货号格式错误, 只能为数字、字母、-");
        }

        if (StringUtils.isNotEmpty(measureUnit)) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkMeasureUnit(measureUnit), "计量单位格式错误, 不能包含数字");
        }

        if (salePrice != null) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkPrice(salePrice), String.format("销售价格范围为%s-%s", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
        }

        if (stock != null) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkStock(stock), String.format("库存请输入%s-%s", ParameterConstant.MIN_STOCK, ParameterConstant.MAX_STOCK));
        }

        if (safeStock != null) {
            AssertUtil.throwInvalidParamIfTrue(!ParameterHelper.checkStock(safeStock), String.format("警戒库存请输入%s-%s", ParameterConstant.MIN_STOCK, ParameterConstant.MAX_STOCK));
        }
    }


}
