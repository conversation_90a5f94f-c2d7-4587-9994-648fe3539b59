package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/19/019
 * @description:
 */
@Schema(description = "优惠券活动响应体")
@Data
public class CouponSimpleListResp extends BaseThriftDto {

    @Schema(description = "优惠券列表")
    private List<CouponSimpleResp> couponList;


}
