package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.MallCollocationResp;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/04/16 10:42
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品营销活动返回值")
public class ProductPromotionResp extends BaseThriftDto {

    @Schema(description = "组合购")
    private MallCollocationResp mallCollocationResp;

    @Schema(description = "优惠券列表")
    private CouponSimpleListResp couponSimpleListResp;

    @Schema(description = "折扣")
    private DiscountActiveResp discountActiveResp;

    @Schema(description = "专享价")
    private ExclusivePriceResp exclusivePriceResp;

    @Schema(description = "限时购")
    private FlashSaleResp flashSaleResp;

    @Schema(description = "满减")
    private FullReductionResp fullReductionResp;


}
