package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/01/11 14:50
 */
@Data
@ToString
@Schema(description = "保存商品库存返回值")
public class SaveProductSkuResp extends BaseThriftDto {

    @Schema(description = "规格自增id")
    private String skuAutoId;

    @Schema(description = "规格名称")
    private String skuName;

    @Schema(description = "规格编码")
    private String skuCode;

    @Schema(description = "计量单位")
    private String measureUnit;


}
