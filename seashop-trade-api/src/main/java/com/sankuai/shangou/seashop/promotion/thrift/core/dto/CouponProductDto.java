package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/20/020
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券对应的商品信息DTO")
public class CouponProductDto extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品图片")
    private String imagePath;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "最小销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "货号")
    private String productCode;

    @Schema(description = "商品条码")
    private String barCodesStr;


}
