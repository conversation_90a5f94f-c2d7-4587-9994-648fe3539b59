package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 8:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品上下架入参")
public class ProductOnOffSaleReq extends BaseParamReq {

    @Schema(description = "商品id的集合", required = true)
    @ExaminField(description = "商品id的集合")
    private List<Long> productIdList;

    @Schema(description = "是否上架", required = true)
    @ExaminField(description = "是否上架 true-上架 false-下架")
    private Boolean onSale;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Schema(description = "变动来源(默认商城) 1-商城 2-牵牛花 3-易久批")
    private Integer changeSource;

    @Schema(description = "是否来自导入(默认否)")
    private Boolean fromImport;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIdList), "请至少选择一个需要操作的商品");
        AssertUtil.throwInvalidParamIfNull(onSale, "请选择上下架状态");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
