package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:07
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品属性返回值")
public class ProductAttributeResp extends BaseThriftDto {

    @Schema(description = "属性名称")
    private String attributeName;

    @Schema(description = "属性值集合")
    private List<String> attributeValueList;


}
