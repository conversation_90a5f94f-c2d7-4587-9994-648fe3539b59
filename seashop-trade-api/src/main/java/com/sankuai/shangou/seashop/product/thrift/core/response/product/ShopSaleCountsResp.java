package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/03/25 15:09
 */
@Data
@ToString
@Schema(description = "店铺销量返回值")
public class ShopSaleCountsResp extends BaseThriftDto {

    @Schema(description = "店铺Id")
    private Long shopId;

    @Schema(description = "销量")
    private Long saleCounts;

    @Schema(description = "虚拟销量")
    private Long virtualSaleCounts;


}
