package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.Date;

/**
 * <p>
 * 首页广告设置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保存弹窗广告请求对象")
public class AdvanceSaveReq extends BaseParamReq {

    /**
     * 是否开启弹窗广告
     */
    @Schema(description = "是否开启弹窗广告", required = true)
    @ExaminField(description = "是否开启弹窗广告")
    private Boolean isEnable;

    /**
     * 广告位图片
     */
    @Schema(description = "广告位图片", required = true)
    @ExaminField(description = "广告位图片")
    private String img;

    /**
     * 图片外联链接
     */
    @Schema(description = "图片外联链接", required = true)
    @ExaminField(description = "图片外联链接")
    private String link;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", required = true)
    @ExaminField(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", required = true)
    @ExaminField(description = "结束时间")
    private Date endTime;

    /**
     * 是否重复播放
     */
    @Schema(description = "是否重复播放", required = true)
    @ExaminField(description = "是否重复播放")
    private Boolean isReplay;

    @Override
    public void checkParameter() {
    }


}
