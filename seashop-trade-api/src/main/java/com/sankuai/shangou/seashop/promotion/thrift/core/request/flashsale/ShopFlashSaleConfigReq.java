package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "店铺限时购配置请求对象")
public class ShopFlashSaleConfigReq extends BaseParamReq {

    /**
     * 店铺ID（平台的ID默认=0）
     */
    @Schema(description = "店铺ID", required = true)
    private Long shopId;

    /**
     * 预热时间（店铺配置）
     */
    @Schema(description = "预热时间", required = true)
    private Integer preheat;

    /**
     * 是否允许正常购买（店铺配置）
     */
    @Schema(description = "是否允许正常购买", required = true)
    private Boolean normalPurchaseFlag;

    @Override
    public void checkParameter() {
        if (null == this.shopId) {
            throw new InvalidParamException("店铺ID不能为空");
        }
        if (null == this.preheat) {
            throw new InvalidParamException("预热时间不能为空");
        }
        if (null == this.normalPurchaseFlag) {
            throw new InvalidParamException("是否允许正常购买不能为空");
        }
    }


}
