package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * @author: lhx
 * @date: 2024/4/1/001
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品可用优惠券查询")
public class ProductAvailableQueryReq extends BaseParamReq {

    @Schema(description = "店铺ID和对应的商品ID列表", required = true)
    private Map<Long, List<Long>> productMap;

    @Schema(description = "用户ID")
    private Long userId;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(productMap)) {
            throw new IllegalArgumentException("productMap不能为空");
        }
    }


}
