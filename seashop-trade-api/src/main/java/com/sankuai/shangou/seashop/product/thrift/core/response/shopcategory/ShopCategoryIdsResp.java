package com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/04/02 17:56
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "下级店铺分类id的集合")
public class ShopCategoryIdsResp extends BaseThriftDto {

    @Schema(description = "类目id的集合")
    private List<Long> childIds;


}
