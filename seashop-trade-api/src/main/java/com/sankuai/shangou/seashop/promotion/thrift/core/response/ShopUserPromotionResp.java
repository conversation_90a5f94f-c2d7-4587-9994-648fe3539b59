package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ShopUserPromotionDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "店铺对于用户的营销对象")
public class ShopUserPromotionResp {

    @Schema(description = "商家用户ID")
    private Long userId;
    @Schema(description = "店铺营销对象集合")
    private List<ShopUserPromotionDto> shopUserPromotionList;


}
