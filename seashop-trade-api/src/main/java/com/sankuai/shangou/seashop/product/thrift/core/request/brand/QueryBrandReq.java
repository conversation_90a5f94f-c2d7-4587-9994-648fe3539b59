package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class QueryBrandReq extends BasePageReq {

    @Schema(description = "品牌id")
    private Long id;

    @Schema(description = "品牌名称")
    private String name;


}
