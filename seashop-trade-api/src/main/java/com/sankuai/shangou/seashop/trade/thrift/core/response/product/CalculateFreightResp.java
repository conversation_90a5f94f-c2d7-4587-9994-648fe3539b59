package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/01/04 19:02
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "组合购返回值")
public class CalculateFreightResp extends BaseThriftDto {

    @Schema(description = "运费")
    private BigDecimal freight;

    @Schema(description = "是否在限购区域")
    private Boolean inRestrictedRegion;


}
