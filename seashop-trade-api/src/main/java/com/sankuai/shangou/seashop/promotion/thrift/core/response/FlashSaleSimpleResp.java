package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@Schema(description = "限时购响应体")
@Data
public class FlashSaleSimpleResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "活动名称")
    private String title;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "跳转地址")
    private String urlPath;

    /**
     * 状态：1待审核,2进行中,3未通过,4已结束,5已取消,6未开始
     */
    @Schema(description = "状态：1待审核 2进行中 3未通过 4已结束 5已取消 6未开始")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "活动开始日期")
    private Date beginDate;

    @Schema(description = "活动结束日期")
    private Date endDate;

    @Schema(description = "限购数量")
    private Integer limitCount;

    @Schema(description = "销售数量")
    private Integer saleCount;

    @Schema(description = "最小价格")
    private BigDecimal minPrice;

    @Schema(description = "前端是否显示")
    private Boolean frontFlag;


}
