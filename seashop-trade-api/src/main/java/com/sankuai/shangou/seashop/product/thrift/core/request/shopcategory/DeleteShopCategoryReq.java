package com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/01 19:30
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "移除店铺分类入参")
public class DeleteShopCategoryReq extends BaseParamReq {

    @Schema(description = "店铺分类id", required = true)
    @PrimaryField(title = "店铺分类id")
    @ExaminField(description = "店铺分类id")
    private Long id;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "店铺分类id不能为空");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
