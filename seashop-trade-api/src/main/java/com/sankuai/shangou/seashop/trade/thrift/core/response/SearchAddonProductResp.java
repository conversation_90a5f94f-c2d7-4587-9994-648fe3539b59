package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.AddonSummaryDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "搜索凑单商品返回对象")
@ToString
@Data
public class SearchAddonProductResp extends BaseThriftDto {

    @Schema(description = "商品分页列表")
    private BasePageResp<TradeProductDto> productPage;
    @Schema(description = "凑单汇总描述")
    private AddonSummaryDto addonSummary;


}
