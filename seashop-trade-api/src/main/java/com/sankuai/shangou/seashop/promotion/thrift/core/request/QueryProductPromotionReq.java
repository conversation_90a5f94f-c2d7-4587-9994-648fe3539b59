package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/04/16 10:17
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询商品所有的营销活动")
public class QueryProductPromotionReq extends BaseParamReq {

    @Schema(description = "商品id", required = true)
    private Long productId;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "用户id")
    private Long userId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(productId, "商品id不能为空");
    }


}
