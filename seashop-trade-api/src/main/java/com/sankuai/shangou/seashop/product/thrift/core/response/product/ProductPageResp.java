package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/16 13:56
 */
@Schema(description = "商品分页信息")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ProductPageResp extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "序号")
    private Long displaySequence;

    @Schema(description = "商品图片")
    private String imagePath;

    @Schema(description = "商品名称")
    private String productName;

    @Setter
    @Getter
    @Schema(description = "最小销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "品牌id")
    private Long brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "状态码")
    private Integer status;

    @Schema(description = "状态")
    private String statusDesc;

    @Schema(description = "货号")
    private String productCode;

    @Schema(description = "类目id")
    private Long categoryId;

    @Schema(description = "类目名称")
    private String categoryName;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "限购")
    private Integer maxBuyCount;

    @Schema(description = "实际销量")
    private Long saleCounts;

    @Schema(description = "虚拟销量")
    private Long virtualSaleCounts;

    @Setter
    @Getter
    @Schema(description = "发布时间")
    private Date addedDate;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Setter
    @Getter
    @Schema(description = "审核时间")
    private Date checkTime;

    @Schema(description = "关联商品id集合")
    private List<Long> relationProductIds;

    @Schema(description = "店铺分类名称")
    private String shopCategoryNames;

    @Schema(description = "是否低于安全库存")
    private Boolean whetherBelowSafeStock;

    @Getter
    @Setter
    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "规格1别名")
    private String spec1Alias;

    @Schema(description = "规格2别名")
    private String spec2Alias;

    @Schema(description = "规格3别名")
    private String spec3Alias;

    @Schema(description = "是否有sku")
    private Boolean hasSku;

    @Schema(description = "销售状态 1-销售中 2-仓库中 3-草稿箱")
    private Integer saleStatusCode;

    @Schema(description = "销售状态描述")
    private String saleStatusDesc;

    @Schema(description = "审核状态 1-待审核 2-销售中 3-未通过 4-违规下架")
    private Integer auditStatusCode;

    @Schema(description = "审核状态描述")
    private String auditStatusDesc;

    @Schema(description = "商品分类id集合")
    private List<Long> categoryIds;

    @Schema(description = "商品分类名称集合")
    private List<String> categoryNames;

    @Schema(description = "运费模板id")
    private Long freightTemplateId;

    @Schema(description = "运费模板名称")
    private String freightTemplateName;

    @Schema(description = "审核备注")
    private String auditReason;

    @Schema(description = "计量单位")
    private String measureUnit;

    @Schema(description = "是否开启阶梯价")
    private Boolean whetherOpenLadder;

    @Schema(description = "来源 1-商城 2-牵牛花 3-易久批")
    private Integer source;

    @Schema(description = "来源描述")
    private String sourceDesc;

    @Schema(description = "店铺展示序号")
    private Integer shopDisplaySequence;

    @Schema(description = "完整的类目名称")
    private String fullCategoryName;

    @Schema(description = "最大售价")
    private BigDecimal maxSalePrice;

    @Schema(description = "价格区间")
    private String salePriceRange;

    @Schema(description = "是否删除")
    private Boolean whetherDelete;

    @Schema(description = "总库存(ES)")
    private Long totalStock;

    @Schema(description = "skuId集合")
    private List<String> skuIds;

    @Schema(description = "广告词")
    private String shortDescription;

    @Schema(description = "h5链接")
    private String h5Url;

    @Schema(description = "更新时间")
    private Date updateTime;


    @Schema(description = "运费模板类型")
    private Integer valuationMethod;
    public String getSpec1Alias() {
        return spec1Alias;
    }

    @Schema(description = "图片集合")
    private List<String> imageList;


    @Schema(description = "上架时间")
    private Date onsaleTime;

    @Schema(description = "店铺分类id集合")
    private List<Long> shopCategoryIds;

    @Schema(description = "OE号")
    private String oeCode;
    @Schema(description = "品牌号")
    private String brandCode;


}
