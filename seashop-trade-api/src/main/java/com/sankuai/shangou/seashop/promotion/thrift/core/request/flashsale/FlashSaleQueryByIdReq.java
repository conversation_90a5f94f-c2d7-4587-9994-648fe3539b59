package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/04/19 14:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "根据id查询限时购详情")
public class FlashSaleQueryByIdReq extends BaseParamReq {

    @Schema(description = "限时购id列表", required = true)
    private List<Long> flashSaleIds;

    @Override
    public void checkParameter() {
        /* AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isNotEmpty(flashSaleIds) && flashSaleIds.size() > 200, "限时购id列表长度不能超过200");*/
    }


}
