package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.ProductPriceDto;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/20 17:35
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "批量设置价格入参")
public class ProductSkuUpdatePriceReq extends BaseParamReq {

    @Schema(description = "sku集合", required = true)
    @ExaminField(description = "sku集合", isChildField = true)
    private List<ProductPriceDto> skuList;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(skuList), "请至少选择一个需要操作的商品");
        skuList.forEach(sku -> sku.checkParameter());
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
