package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.BindCustomFormReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.SaveCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.UpdateCategoryParamReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 * @description:类目操作服务
 */
@FeignClient(name = "himall-trade", contextId = "CategoryCmdFeign", path = "/himall-trade/category", url = "${himall-trade.dev.url:}")
public interface CategoryCmdFeign {

    /**
     * 创建类目
     */
    @PostMapping(value = "/createCategory", consumes = "application/json")
    ResultDto<BaseResp> createCategory(@RequestBody SaveCategoryReq request) throws TException;

    /**
     * 编辑类目
     */
    @PostMapping(value = "/updateCategory", consumes = "application/json")
    ResultDto<BaseResp> updateCategory(@RequestBody SaveCategoryReq request) throws TException;

    /**
     * 更新类目属性
     */
    @PostMapping(value = "/updateCategoryParam", consumes = "application/json")
    ResultDto<BaseResp> updateCategoryParam(@RequestBody UpdateCategoryParamReq request) throws TException;


    /**
     * 删除类目
     */
    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    ResultDto<BaseResp> deleteCategory(@RequestBody BaseIdReq request) throws TException;

    /**
     * 关联自定义表单
     */
    @PostMapping(value = "/bindCustomForm", consumes = "application/json")
    ResultDto<BaseResp> bindCustomForm(@RequestBody BindCustomFormReq request) throws TException;

}
