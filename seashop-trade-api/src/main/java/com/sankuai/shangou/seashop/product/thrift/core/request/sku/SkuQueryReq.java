package com.sankuai.shangou.seashop.product.thrift.core.request.sku;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 14:53
 */
@Data
@Schema(description = "查询sku的入参")
public class SkuQueryReq extends BaseThriftDto {

    @Schema(description = "skuId集合")
    private List<String> skuIdList;

    @Schema(description = "商品id集合")
    private List<Long> productIds;

    public void checkParameter() {
        AssertUtil.throwIfTrue(CollectionUtils.isNotEmpty(skuIdList) && skuIdList.size() > ParameterConstant.QUERY_LIST_LIMIT,
                String.format("skuId集合大小不能超过%d", ParameterConstant.QUERY_LIST_LIMIT));
        AssertUtil.throwIfTrue(CollectionUtils.isNotEmpty(productIds) && productIds.size() > ParameterConstant.QUERY_LIST_LIMIT,
                String.format("商品id集合大小不能超过%d", ParameterConstant.QUERY_LIST_LIMIT));
    }


}
