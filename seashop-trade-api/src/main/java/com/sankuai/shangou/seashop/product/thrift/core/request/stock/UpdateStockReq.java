package com.sankuai.shangou.seashop.product.thrift.core.request.stock;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateWayEnum;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/18 14:58
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "修改库存入参")
public class UpdateStockReq extends BaseParamReq {

    @Schema(description = "修改类型", required = true)
    private StockUpdateTypeEnum updateType;

    @Schema(description = "修改key 支持skuId、skuAutoId、skuCode", required = true)
    private StockUpdateKeyEnum updateKey;

    @Schema(description = "批次号 1-调整库存 2-覆盖库存")
    private StockUpdateWayEnum updateWay;

    @Schema(description = "库存变动集合", required = true)
    private List<SkuStockReq> stockList;

    @Schema(description = "业务code", required = true)
    private String bizCode;

    @Schema(description = "业务序号")
    private String seqCode;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(updateType == null, "updateType 不能为空");
        AssertUtil.throwInvalidParamIfNull(updateKey == null, "updateKey 不能为空");
        AssertUtil.throwInvalidParamIfNull(updateWay == null, "updateWay 不能为空");
        AssertUtil.throwIfTrue(StringUtils.isEmpty(bizCode), "bizCode 不能为空");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(stockList), "库存变动不能为空");

        stockList.forEach(stock -> {
            stock.checkParameter(updateKey, updateWay);
        });
    }


}
