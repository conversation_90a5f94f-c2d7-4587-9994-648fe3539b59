package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/12/012
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商城搜索店铺商品返回值")
public class MallShopProductResp {

    @Schema(description = "商品列表")
    private List<TradeProductDto> productList;

    @Schema(description = "商品总数")
    private Long productCount;


}
