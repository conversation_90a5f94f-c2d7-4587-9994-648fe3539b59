package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券关联商品查询请求对象")
public class CouponProductQueryReq extends BasePageReq {

    @Schema(description = "优惠券ID", required = true)
    private Long couponId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(couponId, "优惠券id不能为空");
    }


}
