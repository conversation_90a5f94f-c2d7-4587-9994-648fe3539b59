package com.sankuai.shangou.seashop.product.thrift.core.enums.erp;


/**
 * <AUTHOR>
 * @date 2023/12/07 9:06
 */
public enum ErpTimeType {

    CREATE_TIME(1, "创建时间"),
    UPDATE_TIME(2, "修改时间");

    private Integer code;
    private String desc;

    ErpTimeType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ErpTimeType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ErpTimeType value : values()) {
            if (!value.getCode().equals(code)) {
                continue;
            }
            return value;
        }
        return null;
    }
}
