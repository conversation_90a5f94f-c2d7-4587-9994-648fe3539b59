package com.sankuai.shangou.seashop.product.thrift.inventory.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.inventory.response.model.SkuInventoryDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/06 15:14
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "查询库存响应体")
public class InventoryQueryResp extends BaseThriftDto {

    @Schema(description = "库存信息的集合")
    private List<SkuInventoryDto> skuInventoryList;

}
