package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.stock.RollBackStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.stock.UpdateStockReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 库存变动服务
 *
 * author HuBiao
 * date 2023/12/18 14:57
 */
@FeignClient(name = "himall-trade", contextId = "SkuStockCmdFeign", path = "/himall-trade/skuStock", url = "${himall-trade.dev.url:}")
public interface SkuStockCmdFeign {

    /**
     * 同步修改库存
     */
    @PostMapping(value = "/syncChangeStock", consumes = "application/json")
    ResultDto<BaseResp> syncChangeStock(@RequestBody UpdateStockReq request) throws TException;

    /**
     * 异步修改库存
     */
    @PostMapping(value = "/aSyncChangeStock", consumes = "application/json")
    ResultDto<BaseResp> aSyncChangeStock(@RequestBody UpdateStockReq request) throws TException;

    /**
     * 回滚库存
     */
    @PostMapping(value = "/rollBackSkuStock", consumes = "application/json")
    ResultDto<BaseResp> rollBackSkuStock(@RequestBody RollBackStockReq request) throws TException;

}
