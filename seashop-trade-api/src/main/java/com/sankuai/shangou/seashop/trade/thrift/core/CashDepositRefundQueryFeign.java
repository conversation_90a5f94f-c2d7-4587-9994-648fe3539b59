package com.sankuai.shangou.seashop.trade.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.finance.ApiCashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositRefundResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金退款相关查询服务
 */
@FeignClient(name = "himall-trade", contextId = "ApiCashDepositRefundQueryFeign", path = "/himall-trade/cashDepositRefund", url = "${himall-trade.dev.url:}")
public interface CashDepositRefundQueryFeign {

    /**
     * 通过条件查询保证金退款明细信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/refundList", consumes = "application/json")
    ResultDto<BasePageResp<ApiCashDepositRefundResp>> refundList(@RequestBody ApiCashDepositRefundQueryReq request) throws TException;

    /**
     * 通过ID查询审核信息
     */
    @GetMapping(value = "/refundDetail")
    ResultDto<ApiCashDepositRefundDetailResp> refundDetail(Long id) throws TException;
}
