package com.sankuai.shangou.seashop.product.thrift.core.response.product.model;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/06 14:57
 */
@Schema(description = "阶梯价信息")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class LadderPriceDto extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "最小批量")
    private Integer minBath;

    @Schema(description = "最大批量")
    private Integer maxBath;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "阶梯价Id")
    private Long ladderPriceId;


}
