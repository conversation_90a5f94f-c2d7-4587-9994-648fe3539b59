package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.ProductFieldDto;
import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/05 17:11
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品导入入参")
public class ProductImportReq extends BaseParamReq {

    @Schema(description = "文件路径", required = true)
    @ExaminField(description = "文件路径")
    private String filePath;

    @Schema(description = "店铺id")
    @ExaminField(description = "店铺id")
    private Long shopId;

    @Schema(description = "销售状态 1-销售中 2-仓库中")
    private Integer saleStatus;

    @Schema(description = "关联列表")
    private List<ProductFieldDto> fields;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(filePath), "文件路径不能为空");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(fields), "关联列表不能为空");
    }


}
