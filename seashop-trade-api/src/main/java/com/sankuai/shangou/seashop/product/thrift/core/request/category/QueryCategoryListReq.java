package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询类目入参")
public class QueryCategoryListReq extends BaseParamReq {

    @Schema(description = "ids")
    private List<Long> ids;

    @Schema(description = "类目名称")
    private String nameLike;

    @Schema(description = "深度")
    private Integer depth;

    @Schema(description = "父类目id")
    private Long parentId;

    @Schema(description = "是否显示")
    private Boolean whetherShow;

    @Schema(description = "是否有子类目")
    private Boolean hasChildren;

    @Schema(description = "自定义表单id的集合")
    private List<Long> customerFormIds;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isNotEmpty(ids) && ids.size() > ParameterConstant.QUERY_LIST_LIMIT,
                String.format("类目最多只能查询%d条", ParameterConstant.QUERY_LIST_LIMIT));
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isNotEmpty(customerFormIds) && customerFormIds.size() > ParameterConstant.QUERY_LIST_LIMIT,
                String.format("自定义表单最多只能查询%d条", ParameterConstant.QUERY_LIST_LIMIT));
    }


}
