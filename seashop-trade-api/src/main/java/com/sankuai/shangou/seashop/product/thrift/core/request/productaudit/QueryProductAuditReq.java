package com.sankuai.shangou.seashop.product.thrift.core.request.productaudit;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/16 12:43
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询商品审核入参")
public class QueryProductAuditReq extends BasePageReq {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "规格自增id")
    private Long skuAutoId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "店铺分类id")
    private Long shopCategoryId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "警戒库存")
    private Boolean whetherBelowSafeStock;

    @Schema(description = "审核状态 1-待审核 2-销售中 3-未通过 4-违规下架")
    private ProductEnum.AuditStatusEnum auditStatus;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "类目路径 | 隔开")
    private String categoryPath;

    @Schema(description = "排序字段")
    private List<FieldSortReq> sortList;

    @Schema(description = "类目id列表")
    private List<Long> categoryIds;


}
