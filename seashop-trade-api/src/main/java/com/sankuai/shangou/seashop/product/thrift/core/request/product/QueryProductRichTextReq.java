package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/25 16:45
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询商品版式入参")
public class QueryProductRichTextReq extends BaseParamReq {

    @Schema(description = "商品id", required = true)
    private Long productId;

    @Schema(description = "版式类型 1-pc端 2-移动端(默认1)")
    private Integer type;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productId == null || productId <= 0, "商品id不能为空");
    }


}
