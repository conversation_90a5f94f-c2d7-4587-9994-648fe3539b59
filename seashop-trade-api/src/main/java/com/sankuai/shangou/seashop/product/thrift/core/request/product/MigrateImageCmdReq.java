package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Schema(description = "迁移图片请求体")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MigrateImageCmdReq extends BaseParamReq {
    @Schema(description = "商品Id列表")
    private List<Long> product;

    @Override
    public void checkParameter() {
    }


}
