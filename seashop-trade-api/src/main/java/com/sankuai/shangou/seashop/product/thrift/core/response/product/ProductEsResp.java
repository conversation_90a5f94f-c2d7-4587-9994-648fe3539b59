package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductEsDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/27 9:41
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品es查询返回值")
public class ProductEsResp extends BaseThriftDto {

    @Schema(description = "商品信息")
    private ProductEsDto product;


}
