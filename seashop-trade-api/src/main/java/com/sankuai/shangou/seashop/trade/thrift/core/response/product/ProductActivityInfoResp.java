package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "活动信息返回值")
public class ProductActivityInfoResp extends BaseThriftDto {

    @Schema(description = "是否参加了限时购")
    private Boolean hasFlashSale;

    @Schema(description = "是否参加了专享价")
    private boolean hasExclusivePrice;

    @Schema(description = "是否参加了阶梯价格")
    private boolean hasLadderPrice;

    @Schema(description = "是否参加了组合购")
    private boolean hasCollectionBuy;

    @Schema(description = "是否参加了折扣")
    private boolean hasDiscountActive;

    @Schema(description = "是否参加了满减")
    private boolean hasFullReduction;

    @Schema(description = "是否有优惠券")
    private boolean hasCoupon;

    @Schema(description = "限时购规则")
    private ProductFlashSaleResp flashSaleRule;

    @Schema(description = "阶梯价规则")
    private List<ProductLadderPriceResp> ladderPriceRuleList;

    @Schema(description = "折扣规则")
    private List<ProductDiscountRuleResp> discountRuleList;

    @Schema(description = "满减规则")
    private ProductReductionRuleResp fullReductionRule;

    @Schema(description = "优惠券列表")
    private List<ProductCouponResp> couponList;

    @Schema(description = "组合购列表")
    private List<CollectionBuyResp> collectionBuyList;


    public boolean isHasExclusivePrice() {
        return hasExclusivePrice;
    }


    public boolean isHasLadderPrice() {
        return hasLadderPrice;
    }


    public boolean isHasCollectionBuy() {
        return hasCollectionBuy;
    }


    public boolean isHasDiscountActive() {
        return hasDiscountActive;
    }


    public boolean isHasFullReduction() {
        return hasFullReduction;
    }


    public boolean isHasCoupon() {
        return hasCoupon;
    }


}
