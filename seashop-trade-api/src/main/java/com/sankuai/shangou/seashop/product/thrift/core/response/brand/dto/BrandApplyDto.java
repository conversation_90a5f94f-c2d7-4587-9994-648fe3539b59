package com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:42
 */
@Schema(description = "品牌申请信息")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class BrandApplyDto extends BaseThriftDto {

    @Schema(description = "申请记录id")
    private Integer id;

    @Schema(description = "供应商id")
    private Long shopId;

    @Schema(description = "供应商名称")
    private String shopName;

    @Schema(description = "品牌id")
    private Long brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "品牌logo")
    private String logo;

    @Schema(description = "品牌简介")
    private String description;

    @Schema(description = "品牌授权书")
    private List<String> authCertificateList;

    @Schema(description = "申请类型")
    private BrandEnum.ApplyModeEnum applyMode;

    @Schema(description = "申请类型 1-平台已有品牌 2-新品牌")
    private Integer applyModeCode;

    @Schema(description = "申请类型描述")
    private String applyModeDesc;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "审核状态")
    private BrandEnum.AuditStatusEnum auditStatus;

    @Schema(description = "审核状态 0-未审核 1-审核通过 2-审核拒绝")
    private Integer auditStatusCode;

    @Schema(description = "审核状态描述")
    private String auditStatusDesc;

    @Schema(description = "申请时间")
    private Date applyTime;

    @Schema(description = "审核时间")
    private Date auditTime;

    @Schema(description = "平台备注")
    private String platRemark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人")
    private Long createUser;

    @Schema(description = "更新人")
    private Long updateUser;


}
