package com.sankuai.shangou.seashop.trade.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ChooseCouponReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ChooseShippingAddressReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewChangeSkuQuantityReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SubmitOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.ChooseCouponResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewChangeSkuQuantityResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * 预订单操作相关
 */
@FeignClient(name = "himall-trade",contextId = "PreOrderCmdFeign", path = "/himall-trade/preOrder", url = "${himall-trade.dev.url:}")
public interface PreOrderCmdFeign {

    /**
     * 提交订单
     * <p>点击提交按钮会先进行校验，校验的方向为：商品价格和数量规则是否发生变化；店铺订单总价是否发生变化。如果校验通过则生成订单，否则返回数据以及异常提示</p>
     */
    @PostMapping(value = "/submitOrder", consumes = "application/json")
    ResultDto<PreviewOrderResp> submitOrder(@RequestBody SubmitOrderReq orderReq) throws TException;

    /**
     * 提交ERP订单
     * <p>跟预览前部分逻辑一致，校验的方向为：商品存在，上下架正常，否则返回数据以及异常提示</p>
     */
    @PostMapping(value = "/submitErpOrder", consumes = "application/json")
    ResultDto<PreviewOrderResp> submitErpOrder(@RequestBody SubmitOrderReq req) throws TException;

    /**
     * 选择优惠券
     * <p>选择优惠券后，改变的是订单的总额，且优惠券与满减互斥-店铺和数据一起提交是为了保持用户看到的数据一致
     */
    @PostMapping(value = "/chooseCoupon", consumes = "application/json")
    ResultDto<ChooseCouponResp> chooseCoupon(@RequestBody ChooseCouponReq chooseCouponReq) throws TException;

    /**
     * 选择收货地址
     * <p>入参，会将页面的店铺和商品数据一起提交，返回的时候，提交的数据只会调整运费，目的是保持用户看到的数据前后一致，
     * 因为这个过程中购物车和价格等数据可能发生了变化，如果实时取和计算，可能用户看到的数据前后会不一样</p>
     * <p>返回的结果，会以提交的数据为基础，调整运费值。</p>
     * @param chooseShippingAddressReq 选择收货地址入参
     */
    @PostMapping(value = "/chooseShippingAddress", consumes = "application/json")
    ResultDto<PreviewOrderResp> chooseShippingAddress(@RequestBody ChooseShippingAddressReq chooseShippingAddressReq) throws TException;

    /**
     * 订单预览页-修改sku数量
     */
    @PostMapping(value = "/changePreviewSkuCount", consumes = "application/json")
    ResultDto<PreviewChangeSkuQuantityResp> changePreviewSkuCount(@RequestBody PreviewChangeSkuQuantityReq changeQuantityReq) throws TException;

}
