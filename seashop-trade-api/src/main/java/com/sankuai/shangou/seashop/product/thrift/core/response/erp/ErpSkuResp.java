package com.sankuai.shangou.seashop.product.thrift.core.response.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/07 9:27
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "erp sku信息返回值")
public class ErpSkuResp extends BaseThriftDto {

    @Schema(description = "skuId")
    private String skuId;

    @Schema(description = "sku编码")
    private String skuCode;

    @Schema(description = "sku名称")
    private String skuName;

    @Setter
    @Getter
    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "规格属性(暂无)")
    private String specValue;

    @Schema(description = "规格图片url")
    private String showPic;

    @Schema(description = "sku自增id")
    private Long skuAutoId;

    @Schema(description = "单位")
    private String measureUnit;


}
