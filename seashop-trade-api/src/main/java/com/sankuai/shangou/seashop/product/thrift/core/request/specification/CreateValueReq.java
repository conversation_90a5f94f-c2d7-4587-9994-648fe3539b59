package com.sankuai.shangou.seashop.product.thrift.core.request.specification;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

@Data
public class CreateValueReq {
    private Long shopId;
    private Long nameId;
    @JsonAlias("Value")
    private String Value;

    public void checkParameter() {
        AssertUtil.throwIfNull(shopId, "shopId 不能为空");
        AssertUtil.throwIfNull(nameId, "nameId 不能为空");
        AssertUtil.throwIfTrue(StrUtil.isEmpty(Value), "规格值不能为空");
    }
}
