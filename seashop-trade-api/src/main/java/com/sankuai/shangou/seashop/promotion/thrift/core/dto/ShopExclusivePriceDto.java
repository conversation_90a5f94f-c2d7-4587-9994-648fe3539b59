package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "店铺专享价对象")
public class ShopExclusivePriceDto extends BaseThriftDto {

    @Schema(description = "专享价活动ID")
    private Long id;
    @Schema(description = "专享价活动名称")
    private String name;
    @Schema(description = "专享价适用商品")
    private List<ShopExclusivePriceProductDto> productList;


}
