package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponProductDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@Schema(description = "优惠券活动响应体")
@Data
public class CouponResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "面值(价格)")
    private Long price;

    @Schema(description = "最大可领取张数")
    private BigDecimal perMax;

    @Schema(description = "订单金额（满足多少钱才能使用）")
    private Long orderAmount;

    @Schema(description = "发行张数")
    private BigDecimal num;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "领取方式 0 店铺首页 1 积分兑换 2 主动发放")
    private Integer receiveType;

    @Schema(description = "使用范围：0=全场通用，1=部分商品可用")
    private Integer useArea;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "产品ID列表")
    private List<Long> productIdList;

    @Schema(description = "推广方式：0 平台；4 移动端(小程序)")
    private List<Integer> platForm;

    @Schema(description = "优惠券对应的商品信息")
    private List<CouponProductDto> productList;


}
