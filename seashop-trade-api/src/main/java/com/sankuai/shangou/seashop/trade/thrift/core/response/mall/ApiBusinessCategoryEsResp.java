package com.sankuai.shangou.seashop.trade.thrift.core.response.mall;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "店铺ES经营类目列表响应体")
public class ApiBusinessCategoryEsResp extends BaseThriftDto {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 类目ID
     */
    @Schema(description = "类目ID")
    private Long categoryId;

    /**
     * 类目名称
     */
    @Schema(description = "类目名称")
    private String categoryName;

    /**
     * 类目全名(包含上级)
     */
    @Schema(description = "类目全名(包含上级)")
    private String fullCategoryName;

    /**
     * 是否冻结
     */
    @Schema(description = "是否冻结")
    private Boolean whetherFrozen;


}
