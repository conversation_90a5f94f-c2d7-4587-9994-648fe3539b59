package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryDepositConfigBySkuReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryFirstCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryCashDepositMapListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:保证金配置查询服务
 */
@FeignClient(name = "himall-trade", contextId = "CategoryCashDepositQueryFeign", path = "/himall-trade/categoryCashDeposit", url = "${himall-trade.dev.url:}")
public interface CategoryCashDepositQueryFeign {

    /**
     * 通过条件查询支付渠道配置信息
     */
    @GetMapping("/queryAll")
    ResultDto<CategoryCashDepositListResp> queryAll() throws TException;

    /**
     * 查询一级类目保证金配置
     */
    @PostMapping(value = "/queryByCategoryList", consumes = "application/json")
    ResultDto<CategoryCashDepositMapListResp> queryByCategoryList(@RequestBody QueryFirstCategoryReq request) throws TException;


    /**
     * 根据skuID查询对应的类目的保证金配置
     */
    @PostMapping(value = "/queryBySkuList", consumes = "application/json")
    ResultDto<SkuFitCategoryCashDepositListResp> queryBySkuList(@RequestBody QueryDepositConfigBySkuReq request) throws TException;
}
