package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryProductPromotionReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryShopUserPromotionReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductPromotionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopReductionOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopUserPromotionResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * 店铺关联用户营销接口
 */
@FeignClient(name = "himall-trade",contextId = "ShopUserPromotionQueryFeign", path = "/himall-trade/shopUserPromotion", url = "${himall-trade.dev.url:}")
public interface ShopUserPromotionQueryFeign {

    /**
     * 查询店铺有效的营销活动,如果指定了用户ID，则会进一步过滤对用户有效的
     */
    @PostMapping(value = "/queryShopValidPromotionWithUser", consumes = "application/json")
    ResultDto<ShopUserPromotionResp> queryShopValidPromotionWithUser(@RequestBody QueryShopUserPromotionReq request) throws TException;

    /**
     * 查询订单可用的折扣活动
     */
    @PostMapping(value = "/queryShopPromotionByOrder", consumes = "application/json")
    ResultDto<ShopReductionOrderListResp> queryShopPromotionByOrder(@RequestBody PromotionRecordOrderQueryReq req) throws TException;

    /**
     * 查询商品所有的有效有效活动

     */
    @PostMapping(value = "/queryProductPromotion", consumes = "application/json")
    ResultDto<ProductPromotionResp> queryProductPromotion(@RequestBody QueryProductPromotionReq request) throws TException;
}
