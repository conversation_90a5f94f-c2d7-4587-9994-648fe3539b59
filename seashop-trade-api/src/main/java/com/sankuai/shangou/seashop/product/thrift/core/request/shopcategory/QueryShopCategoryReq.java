package com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询店铺分类入参")
public class QueryShopCategoryReq extends BaseParamReq {

    @Schema(description = "上级类目id")
    private Integer parentId;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Schema(description = "是否显示")
    private Boolean whetherShow;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
