package com.sankuai.shangou.seashop.trade.thrift.core.response.mall;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "店铺ES查询响应体")
public class ApiShopEsResp extends BaseThriftDto {

    /**
     * 店铺id
     */
    @Schema(description = "店铺id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @Schema(description = "店铺名称")
    private String shopName;

    /**
     * 店铺logo
     */
    @Schema(description = "店铺logo")
    private String logo;

    /**
     * 店铺状态
     */
    @Schema(description = "店铺状态")
    private Integer shopStatus;

    /**
     * 是否开启专属商家
     */
    @Schema(description = "是否开启专属商家")
    private Boolean whetherOpenExclusiveMember;

    /**
     * 订单量（已完成）
     */
    @Schema(description = "订单量")
    private Long orderSaleCount;

    /**
     * 商品销量（实际销量+虚拟销量）
     */
    @Schema(description = "商品销量（实际销量+虚拟销量）")
    private Long productSaleCount;

    /**
     * 商品实际销量
     */
    @Schema(description = "商品实际销量")
    private Long productRealSaleCount;

    /**
     * 商品虚拟销量
     */
    @Schema(description = "商品虚拟销量")
    private Long productVirtualSaleCount;

    /**
     * 评分数量
     */
    @Schema(description = "评分数量")
    private Long markCount;

    /**
     * 包装评分
     */
    @Schema(description = "包装评分")
    private BigDecimal packMark;

    /**
     * 服务评分
     */
    @Schema(description = "服务评分")
    private BigDecimal serviceMark;

    /**
     * 综合评分
     */
    @Schema(description = "综合评分")
    private BigDecimal comprehensiveMark;

    /**
     * 热门关注商品
     */
    @Schema(description = "热门关注商品")
    private List<TradeProductDto> productList;

    @Schema(description = "商品总数")
    private Long productCount = 0L;

    @Schema(description = "收藏数量")
    private Integer favoriteCount = 0;

    @Schema(description = "是否收藏")
    private Boolean favoriteFlag = false;


}
