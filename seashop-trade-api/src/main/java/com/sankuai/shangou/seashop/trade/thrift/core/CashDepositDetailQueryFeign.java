package com.sankuai.shangou.seashop.trade.thrift.core;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.finance.ApiCashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositDetailResp;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金明细查询相关服务
 */
@FeignClient(name = "himall-trade",contextId = "CashDepositDetailQueryFeign",path = "/himall-trade/cashDepositDetail", url = "${himall-trade.dev.url:}")
public interface CashDepositDetailQueryFeign {

    /**
     * 通过条件查询保证金明细列表
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList",consumes = "application/json")
    ResultDto<BasePageResp<ApiCashDepositDetailResp>> pageList(@RequestBody ApiCashDepositDetailQueryReq request) throws TException;
}
