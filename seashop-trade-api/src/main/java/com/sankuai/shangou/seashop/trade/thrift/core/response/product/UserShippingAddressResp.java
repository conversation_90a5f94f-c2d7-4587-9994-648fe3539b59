package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/22 16:45
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "用户收货地址")
public class UserShippingAddressResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "区域id")
    private Integer regionId;

    @Schema(description = "收货人")
    private String shipTo;

    @Schema(description = "收货地址")
    private String address;

    @Schema(description = "详细地址")
    private String addressDetail;

    @Schema(description = "收货人电话")
    private String phone;

    @Schema(description = "区域全路径")
    private String regionPath;

    @Schema(description = "区域全称")
    private String regionFullName;


}
