package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * 规格 1-规格1 2-规格2 3-规格3
 *
 * <AUTHOR>
 * @date 2023/11/14 17:23
 */
public enum ProductSpecEnum {

    /**
     * 规格1
     */
    SPEC_1(1, "规格1"),
    /**
     * 规格2
     */
    SPEC_2(2, "规格2"),
    /**
     * 规格3
     */
    SPEC_3(3, "规格3");

    private Integer code;
    private String desc;

    ProductSpecEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductSpecEnum getByCode(Integer code) {
        for (ProductSpecEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
