package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/01/17 8:43
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "生成商品货号返回值")
public class GenProductCodeResp extends BaseThriftDto {

    @Schema(description = "商品货号")
    private String productCode;


}
