package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@Schema(description = "弹窗广告响应体")
@Data
public class AdvanceResp extends BaseThriftDto {

    /**
     * 是否开启弹窗广告
     */
    @Schema(description = "是否开启弹窗广告", required = true)
    private Boolean isEnable;

    /**
     * 广告位图片
     */
    @Schema(description = "广告位图片", required = true)
    private String img;

    /**
     * 图片外联链接
     */
    @Schema(description = "图片外联链接", required = true)
    private String link;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", required = true)
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", required = true)
    private Date endTime;

    /**
     * 是否重复播放
     */
    @Schema(description = "是否重复播放", required = true)
    private Boolean isReplay;


}
