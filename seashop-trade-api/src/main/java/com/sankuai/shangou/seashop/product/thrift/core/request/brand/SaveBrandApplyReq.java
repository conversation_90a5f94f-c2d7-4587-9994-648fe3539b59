package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/07 14:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class SaveBrandApplyReq extends BaseParamReq {

    @Schema(description = "申请记录id")
    private Long id;

    @Schema(description = "申请类型", required = true)
    private BrandEnum.ApplyModeEnum applyMode;

    @Schema(description = "申请品牌 applyMode=1 必填")
    private Long brandId;

    @Schema(description = "品牌名称 applyMode=2 必填")
    private String brandName;

    @Schema(description = "logo applyMode=2 必填")
    private String logo;

    @Schema(description = "描述 applyMode=2 必填")
    private String description;

    @Schema(description = "授权证书", required = true)
    private List<String> authCertificateList;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(applyMode, "请选择申请类型");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(authCertificateList), "请上传授权证书");
        // 品牌授权书不能超过10个
        AssertUtil.throwInvalidParamIfTrue(authCertificateList.size() > ParameterConstant.BRAND_AUTH_CERTIFICATE_MAX_SIZE,
                String.format("品牌授权书不能超过%d个", ParameterConstant.BRAND_AUTH_CERTIFICATE_MAX_SIZE));

        // 平台已有品牌
        if (BrandEnum.ApplyModeEnum.EXISTING_BRAND.equals(applyMode)) {
            AssertUtil.throwInvalidParamIfTrue(brandId == null || brandId <= 0, "请选择申请品牌");
            return;
        }

        // 新增品牌
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(brandName), "请输入品牌名称");
        AssertUtil.throwInvalidParamIfTrue(brandName.length() > ParameterConstant.BRAND_NAME_LENGTH,
                String.format("品牌名称不能超过%d个字符", ParameterConstant.BRAND_NAME_LENGTH));
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(logo), "请上传品牌logo");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(description), "请输入品牌简介");
        AssertUtil.throwInvalidParamIfTrue(description.length() > ParameterConstant.BRAND_DESCRIPTION_LENGTH,
                String.format("品牌简介不能超过%d个字符", ParameterConstant.BRAND_DESCRIPTION_LENGTH));
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "请传入shopId");
    }

    public void checkForEdit() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "请选择要编辑的申请记录");
        checkParameter();
    }


}
