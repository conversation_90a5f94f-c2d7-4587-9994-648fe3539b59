package com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:42
 */
@Schema(description = "品牌信息")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class BrandDto {

    @Schema(description = "品牌id")
    private Long id;

    @Schema(description = "品牌名称")
    private String name;

    @Schema(description = "排序")
    private Long displaySequence;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "品牌简介")
    private String description;

    @Schema(description = "品牌来源")
    private String applyMode;


}
