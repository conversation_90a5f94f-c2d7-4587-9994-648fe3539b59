package com.sankuai.shangou.seashop.product.thrift.core.response.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandGroupDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/07 14:08
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "未申请过的品牌分组返回值")
public class NotApplyBrandGroupResp extends BaseThriftDto {

    @Schema(description = "品牌列表")
    private List<BrandGroupDto> groupList;


}
