package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:05
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "更新类目属性参数")
public class UpdateCategoryParamReq extends BaseParamReq {

    /**
     * 主键
     */
    @Schema(description = "类目id", required = true)
    @PrimaryField(title = "类目id")
    @ExaminField(description = "类目id")
    private Long id;

    /**
     * 类目名称
     */
    @Schema(description = "类目名称")
    @ExaminField(description = "类目名称")
    private String name;

    /**
     * 排序
     */
    @Schema(description = "排序")
    @ExaminField(description = "排序")
    private Long displaySequence;

    /**
     * 分佣比例
     */
    @Schema(description = "分佣比例 创建三级类目时才需要设置")
    @ExaminField(description = "分佣比例")
    private BigDecimal commissionRate;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示")
    @ExaminField(description = "是否显示")
    private Boolean whetherShow;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "请传入类目id");
        if (StringUtils.isNotEmpty(name)) {
            AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(name), "请输入类目名称");
            AssertUtil.throwInvalidParamIfTrue(name.length() > ParameterConstant.CATEGORY_NAME_LENGTH,
                    String.format("类目名称最多只能输入%d个字符", ParameterConstant.CATEGORY_NAME_LENGTH));
        }
    }


}
