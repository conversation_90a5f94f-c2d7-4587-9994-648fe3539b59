package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/22 17:03
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "规格选择返回值")
public class ProductSpecGroupResp extends BaseThriftDto {

    @Schema(description = "规格 取值1、2、3")
    private Integer spec;

    @Schema(description = "规格别名")
    private String specAlias;

    @Schema(description = "规格值集合")
    private List<ProductSpecResp> specValueList;


}
