package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/22 16:56
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "分类信息返回值")
public class ProductLadderPriceResp extends BaseThriftDto {

    @Schema(description = "最小批量")
    private Integer minBath;

    @Schema(description = "最大批量")
    private Integer maxBath;

    @Schema(description = "价格")
    private BigDecimal price;


}
