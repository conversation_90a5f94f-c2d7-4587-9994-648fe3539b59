package com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:32
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询版式模板入参")
public class QueryDescriptionTemplateReq extends BasePageReq {

    @Schema(description = "版式名称 最多30个字")
    private String name;

    @Schema(description = "版式位置")
    private DescriptionTemplatePositionEnum position;

    @Schema(description = "店铺id", required = true)
    private Long shopId;


}
