package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/11/07 14:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "审核品牌申请入参对象")
public class AuditBrandApplyReq extends BaseParamReq {

    @Schema(description = "申请记录id", required = true)
    private Long id;

    @Schema(description = "审核是否通过", required = true)
    private Boolean passFlag;

    @Schema(description = "拒绝原因")
    private String rejectReason;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "请选择申请记录");
        AssertUtil.throwInvalidParamIfNull(passFlag, "请选择审核结果");
        // 拒绝的时候需要填写拒绝原因
        if (!passFlag) {
            AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(rejectReason), "请输入拒绝原因");
            AssertUtil.throwInvalidParamIfTrue(rejectReason.length() > ParameterConstant.BRAND_AUDIT_REASONS_LENGTH,
                    String.format("拒绝原因不能超过%d个字符", ParameterConstant.BRAND_AUDIT_REASONS_LENGTH));
        }
    }


}