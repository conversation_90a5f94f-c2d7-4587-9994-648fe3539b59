package com.sankuai.shangou.seashop.product.thrift.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/01/02 14:00
 */
@Getter
public enum ProductSourceEnum {

    /**
     * 商品来源枚举 1-商城 2-牵牛花 3-易久批
     */
    MALL(1, "商城"),
    QNH(2, "牵牛花"),
    YJP(3, "易久批");

    private Integer code;
    private String desc;

    ProductSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductSourceEnum getByCode(Integer code) {
        for (ProductSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        for (ProductSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

}
