package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Schema(description = "平台限时购配置响应体")
@Data
public class PlatFlashSaleConfigResp extends BaseThriftDto {

    @Schema(description = "是否需要审核（平台配置）")
    private Boolean needAuditFlag;


}
