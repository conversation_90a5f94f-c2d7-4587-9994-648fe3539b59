package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:32
 */
@Data
@Schema(description = "打印订单出参")
public class OrderResp extends BaseThriftDto {

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "下单时间")
    private Date orderDate;

    @Schema(description = "收货人姓名")
    private String shipTo;

    @Schema(description = "联系方式")
    private String cellPhone;

    @Schema(description = "地址(全路径详细地址regionFullName+address)")
    private String address;

    @Schema(description = "商品总价")
    private BigDecimal productTotalAmount;

    @Schema(description = "运费")
    private BigDecimal freight;

    @Schema(description = "实付金额")
    private BigDecimal totalAmount;

    @Schema(description = "备注")
    private String orderRemarks;

    @Schema(description = "订单项集合")
    private List<OrderItemResp> orderItemRespList;


}
