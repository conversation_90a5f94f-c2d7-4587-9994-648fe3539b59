package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * 评价汇总数据
 *
 * <AUTHOR>
 * @date 2023/12/25 11:01
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品评价信息返回值")
public class CommentSummaryResp extends BaseThriftDto {

    @Schema(description = "评价总数")
    private Integer totalCount;

    @Schema(description = "5分评价数")
    private Integer fiveStarCount;

    @Schema(description = "4分评价数")
    private Integer fourStarCount;

    @Schema(description = "3分评价数")
    private Integer threeStarCount;

    @Schema(description = "2分评价数")
    private Integer twoStarCount;

    @Schema(description = "1分评价数")
    private Integer oneStarCount;

    @Schema(description = "有图评价数")
    private Integer hasImageCount;

    @Schema(description = "追评数")
    private Integer appendCount;

    @Schema(description = "好评率")
    private BigDecimal goodRate;

    @Schema(description = "中评率")
    private BigDecimal middleRate;

    @Schema(description = "差评率")
    private BigDecimal badRate;

    @Schema(description = "综合评分")
    private BigDecimal score;


}
