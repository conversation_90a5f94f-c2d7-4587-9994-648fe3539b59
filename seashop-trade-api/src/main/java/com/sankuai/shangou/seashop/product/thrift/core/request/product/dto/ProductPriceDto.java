package com.sankuai.shangou.seashop.product.thrift.core.request.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/20 17:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "产品价格入参")
public class ProductPriceDto extends BaseParamReq {

    @Schema(description = "sku自增id", required = true)
    @ExaminField(description = "sku自增id")
    private Long skuAutoId;

    @Schema(description = "市场价")
    @ExaminField(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "商城价")
    @ExaminField(description = "商城价")
    private BigDecimal salePrice;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(skuAutoId == null || skuAutoId <= 0, "skuAutoId不能为空");
        // 市场价和商城价不为空的时候 必须大于0
        if (marketPrice != null && marketPrice.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("市场价必须大于0");
        }
        if (salePrice != null && salePrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("商城价必须大于0");
        }
    }


}
