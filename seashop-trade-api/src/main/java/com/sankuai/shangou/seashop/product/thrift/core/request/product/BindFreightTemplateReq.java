package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/20 17:11
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "关联运费模板入参")
public class BindFreightTemplateReq extends BaseParamReq {

    @Schema(description = "商品id的集合", required = true)
    @ExaminField(description = "商品id的集合")
    private List<Long> productIdList;

    @Schema(description = "运费模板id", required = true)
    @ExaminField(description = "运费模板id")
    private Long freightTemplateId;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIdList), "请至少选择一个需要操作的商品");
        AssertUtil.throwInvalidParamIfTrue(freightTemplateId == null || freightTemplateId <= 0, "请选择运费模板");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
