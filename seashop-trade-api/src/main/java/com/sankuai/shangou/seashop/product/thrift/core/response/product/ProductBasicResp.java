package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/08 18:59
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品基本信息返回值")
public class ProductBasicResp {

    @Schema(description = "商品基本信息")
    private List<ProductBasicDto> productList;


}
