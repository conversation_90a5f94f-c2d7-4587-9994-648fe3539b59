package com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/15 17:04
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "品牌分组返回值")
public class BrandGroupDto extends BaseThriftDto {

    @Schema(description = "分组名称")
    private String groupKey;

    @Schema(description = "品牌列表")
    private List<BrandDto> brandList;

    @Schema(description = "是否可选")
    private Boolean selectAble;


}
