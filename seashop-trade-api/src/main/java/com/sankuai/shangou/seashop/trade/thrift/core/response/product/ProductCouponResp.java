package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/23 16:26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品优惠券返回值")
public class ProductCouponResp extends BaseThriftDto {

    @Schema(description = "优惠券id")
    private Long id;

    @Schema(description = "优惠券金额")
    private BigDecimal price;

    @Schema(description = "最大可领取张数")
    private Integer perMax;

    @Schema(description = "订单金额（满足多少钱才能使用）")
    private BigDecimal orderAmount;

    @Schema(description = "发行张数")
    private Integer num;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "使用范围：0=全场通用，1=部分商品可用")
    private Integer useArea;

    @Schema(description = "是否已领取")
    private Boolean userReceived;

    @Schema(description = "用户领取的数量")
    private Integer userReceivedNum;


}
