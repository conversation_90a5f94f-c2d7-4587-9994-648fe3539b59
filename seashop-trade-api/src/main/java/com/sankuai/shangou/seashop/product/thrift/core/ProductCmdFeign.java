package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.SaveProductResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/06 11:45
 * @Description: 商品操作服务
 */
@FeignClient(name = "himall-trade", contextId = "ProductCmdFeign", path = "/himall-trade/product", url = "${himall-trade.dev.url:}")
public interface ProductCmdFeign {

    /**
     * 发布商品
     */
    @PostMapping(value = "/createProduct", consumes = "application/json")
    ResultDto<BaseResp> createProduct(@RequestBody SaveProductReq request) throws TException;

    /**
     * 编辑商品
     */
    @PostMapping(value = "/updateProduct", consumes = "application/json")
    ResultDto<BaseResp> updateProduct(@RequestBody SaveProductReq request) throws TException;

    /**
     * 部分保存商品
     */
    @PostMapping(value = "/partSaveProduct", consumes = "application/json")
    ResultDto<SaveProductResp> partSaveProduct(@RequestBody SaveProductReq request) throws TException;

    /**
     * 商品上下架
     */
    @PostMapping(value = "/batchOnOffSaleProduct", consumes = "application/json")
    ResultDto<BaseResp> batchOnOffSaleProduct(@RequestBody ProductOnOffSaleReq request) throws TException;

    /**
     * 商品删除
     */
    @PostMapping(value = "/batchDeleteProduct", consumes = "application/json")
    ResultDto<BaseResp> batchDeleteProduct(@RequestBody ProductDeleteReq request) throws TException;

    /**
     * 批量保存平台序号
     */
    @PostMapping(value = "/batchSaveProductSequence", consumes = "application/json")
    ResultDto<BaseResp> batchSaveProductSequence(@RequestBody ProductUpdateSequenceReq request) throws TException;

    /**
     * 批量保存商家序号
     */
    @PostMapping(value = "/batchSaveProductShopSequence", consumes = "application/json")
    ResultDto<BaseResp> batchSaveProductShopSequence(@RequestBody ProductUpdateSequenceReq request) throws TException;

    /**
     * 批量关联版式
     */
    @PostMapping(value = "/batchBindDescriptionTemplate", consumes = "application/json")
    ResultDto<BaseResp> batchBindDescriptionTemplate(@RequestBody BindDescriptionTemplateReq request) throws TException;

    /**
     * 关联推荐商品
     */
    @PostMapping(value = "/bindRecommendProduct", consumes = "application/json")
    ResultDto<BaseResp> bindRecommendProduct(@RequestBody BindRecommendProductReq request) throws TException;

    /**
     * 批量关联运费模板
     */
    @PostMapping(value = "/batchBindFreightTemplate", consumes = "application/json")
    ResultDto<BaseResp> batchBindFreightTemplate(@RequestBody BindFreightTemplateReq request) throws TException;

    /**
     * 批量修改商城/市场价
     */
    @PostMapping(value = "/batchUpdateProductPrice", consumes = "application/json")
    ResultDto<BaseResp> batchUpdateProductPrice(@RequestBody ProductSkuUpdatePriceReq request) throws TException;

    /**
     * 批量设置警戒库存
     */
    @PostMapping(value = "/batchUpdateSafeStock", consumes = "application/json")
    ResultDto<BaseResp> batchUpdateSafeStock(@RequestBody ProductUpdateSafeStockReq request) throws TException;

    /**
     * 批量设置库存
     */
    @PostMapping(value = "/batchUpdateStock", consumes = "application/json")
    ResultDto<BaseResp> batchUpdateStock(@RequestBody ProductUpdateStockReq request) throws TException;

    /**
     * 批量设置虚拟销量
     */
    @PostMapping(value = "/batchUpdateVirtualSales", consumes = "application/json")
    ResultDto<BaseResp> batchUpdateVirtualSales(@RequestBody ProductUpdateVirtualSaleCountsReq request) throws TException;

    /**
     * 批量违规下架
     */
    @PostMapping(value = "/batchViolationOffSale", consumes = "application/json")
    ResultDto<BaseResp> batchViolationOffSale(@RequestBody ProductViolationReq request) throws TException;

    /**
     * 库存导入
     */
    @PostMapping(value = "/importStock", consumes = "application/json")
    ResultDto<BaseImportResp> importStock(@RequestBody ProductImportReq request) throws TException;

    /**
     * 导入下架
     */
    @PostMapping(value = "/importOffSale", consumes = "application/json")
    ResultDto<BaseImportResp> importOffSale(@RequestBody ProductImportReq request) throws TException;

    /**
     * 导入违规下架
     */
    @PostMapping(value = "/importViolationOffSale", consumes = "application/json")
    ResultDto<BaseImportResp> importViolationOffSale(@RequestBody ProductImportReq request) throws TException;

    /**
     * 导入商品数据
     */
    @PostMapping(value = "/importProduct", consumes = "application/json")
    ResultDto<BaseImportResp> importProduct(@RequestBody ProductImportReq request) throws TException;

    /**
     * 平台导入商品数据
     */
    @PostMapping(value = "/platformImportProduct", consumes = "application/json")
    ResultDto<BaseImportResp> platformImportProduct(@RequestBody ProductImportReq request) throws TException;

    /**
     * 导入价格
     */
    @PostMapping(value = "/importPrice", consumes = "application/json")
    ResultDto<BaseImportResp> importPrice(@RequestBody ProductImportReq request) throws TException;

    /**
     * 商品导入修改
     */
    @PostMapping(value = "/importProductUpdate", consumes = "application/json")
    ResultDto<BaseImportResp> importProductUpdate(@RequestBody ProductImportReq request) throws TException;

    /**
     * 下架供应商所有商品(冻结店铺时调用)
     */
    @PostMapping(value = "/offSaleAllProduct", consumes = "application/json")
    ResultDto<BaseResp> offSaleAllProduct(@RequestBody BaseIdReq shopId) throws TException;

    /**
     * 增加销量
     */
    @PostMapping(value = "/addSales", consumes = "application/json")
    ResultDto<BaseResp> addSales(@RequestBody AddSaleCountReq request) throws TException;

    @PostMapping(value = "/initProductES", consumes = "application/json")
    ResultDto<BaseResp> initProductES() throws TException;
    @PostMapping(value = "/initProductAuditES", consumes = "application/json")
    ResultDto<BaseResp> initProductAuditES() throws TException;
}
