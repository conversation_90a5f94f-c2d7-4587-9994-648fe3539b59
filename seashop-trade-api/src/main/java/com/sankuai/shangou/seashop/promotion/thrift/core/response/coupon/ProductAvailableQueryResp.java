package com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: lhx
 * @date: 2024/4/1/001
 * @description:
 */
@Schema(description = "订单优惠券记录响应体")
@Data
public class ProductAvailableQueryResp extends BaseThriftDto {

    @Schema(description = "商品ID-优惠券列表")
    private Map<Long, List<CouponSimpleResp>> productCouponMap;


}
