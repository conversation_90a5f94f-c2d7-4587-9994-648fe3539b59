package com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/13 14:23
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "店铺分类树节点")
public class ShopCategoryTreeResp extends BaseThriftDto {

    @Schema(description = "类目集合")
    private String result;


}
