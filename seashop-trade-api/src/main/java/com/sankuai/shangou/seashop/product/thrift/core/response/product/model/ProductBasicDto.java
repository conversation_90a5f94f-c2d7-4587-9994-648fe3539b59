package com.sankuai.shangou.seashop.product.thrift.core.response.product.model;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/06 13:43
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "商品基本信息返回值")
public class ProductBasicDto extends BaseThriftDto {

    @Schema(description = "商品唯一表示(美团Id组件获取)")
    private Long productId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "类目id")
    private Long categoryId;

    @Schema(description = "类目路径")
    private String categoryPath;

    @Schema(description = "品牌ID")
    private Long brandId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品编码")
    private String productCode;

    @Schema(description = "广告词")
    private String shortDescription;

    @Schema(description = "销售状态 1-销售中 2-仓库中 3-草稿箱")
    private Integer saleStatus;

    @Schema(description = "审核状态 1-待审核 2-销售中 3-未通过 4-违规下架")
    private Integer auditStatus;

    @Schema(description = "添加日期")
    private Date addedDate;

    @Schema(description = "显示顺序")
    private Long displaySequence;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "最小销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "是否有sku")
    private Boolean hasSku;

    @Schema(description = "销售量")
    private Long saleCounts;

    @Schema(description = "运费模板ID")
    private Long freightTemplateId;

    @Schema(description = "重量")
    private BigDecimal weight;

    @Schema(description = "体积")
    private BigDecimal volume;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "计量单位")
    private String measureUnit;

    @Schema(description = "是否删除")
    private Boolean whetherDelete;

    @Schema(description = "最大购买数")
    private Integer maxBuyCount;

    @Schema(description = "是否开启阶梯价格")
    private Boolean whetherOpenLadder;

    @Schema(description = "规格1别名")
    private String spec1Alias;

    @Schema(description = "规格2别名")
    private String spec2Alias;

    @Schema(description = "规格3别名")
    private String spec3Alias;

    @Schema(description = "商家商品序号")
    private Integer shopDisplaySequence;

    @Schema(description = "虚拟销量")
    private Long virtualSaleCounts;

    @Schema(description = "商品主图")
    private String imagePath;

    @Schema(description = "商品主图视频")
    private String videoPath;

    @Schema(description = "提交审核时间")
    private Date submitAuditTime;

    @Schema(description = "商品审核时间")
    private Date checkTime;

    @Schema(description = "倍数起购量")
    private Integer multipleCount;

    @Schema(description = "是否新商品")
    private Boolean whetherNewProduct;

    @Schema(description = "来源 1-商城 2-牵牛花 3-易久批")
    private Integer source;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人Id")
    private Long createUser;

    @Schema(description = "更新人Id")
    private Long updateUser;


    public String getSpec1Alias() {
        return spec1Alias;
    }

    public void setSpec1Alias(String spec1Alias) {
        this.spec1Alias = spec1Alias;
    }

    public String getSpec2Alias() {
        return spec2Alias;
    }

    public void setSpec2Alias(String spec2Alias) {
        this.spec2Alias = spec2Alias;
    }

    public String getSpec3Alias() {
        return spec3Alias;
    }

    public void setSpec3Alias(String spec3Alias) {
        this.spec3Alias = spec3Alias;
    }


}
