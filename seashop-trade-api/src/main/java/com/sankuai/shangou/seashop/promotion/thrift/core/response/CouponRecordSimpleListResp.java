package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@Schema(description = "优惠券记录响应体")
@Data
public class CouponRecordSimpleListResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private List<CouponRecordSimpleResp> list;


}
