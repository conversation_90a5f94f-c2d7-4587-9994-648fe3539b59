package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.CateLevel1Dto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ProductAttributeDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductBrandDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "搜索交易商品返回对象")
@ToString
@Data
public class SearchTradeProductResp extends BaseThriftDto {

    @Schema(description = "品牌列表")
    private List<TradeProductBrandDto> brandList;
    @Schema(description = "属性列表")
    private List<ProductAttributeDto> attributeList;
    @Schema(description = "类目树列表")
    private List<CateLevel1Dto> categoryTreeList;
    @Schema(description = "商品分页列表")
    private BasePageResp<TradeProductDto> productPage;


}
