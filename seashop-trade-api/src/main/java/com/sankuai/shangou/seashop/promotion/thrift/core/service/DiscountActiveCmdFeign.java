package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.DiscountActiveSaveReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description: 提供折扣活动操作功能
 */
@FeignClient(name = "himall-trade",contextId = "DiscountActiveCmdFeign", path = "/himall-trade/discountActive", url = "${himall-trade.dev.url:}")
public interface DiscountActiveCmdFeign {

    /**
     * 保存折扣活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/save", consumes = "application/json")
    ResultDto<BaseResp> save(@RequestBody DiscountActiveSaveReq request) throws TException;

    /**
     * 结束折扣活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/endActive", consumes = "application/json")
    ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException;
}
