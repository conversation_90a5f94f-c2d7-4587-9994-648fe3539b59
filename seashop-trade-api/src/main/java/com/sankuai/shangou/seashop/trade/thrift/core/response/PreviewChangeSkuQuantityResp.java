package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.OrderAdditionalDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.UserCouponRecordDto;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "预览订单-修改数量返回对象")
@ToString
@Data
public class PreviewChangeSkuQuantityResp {

    @Schema(description = "店铺信息", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表", required = true)
    private List<ShoppingCartProductDto> productList;
    @Schema(description = "附加信息")
    private OrderAdditionalDto additional;
    @Schema(description = "有效的优惠券信息")
    private List<UserCouponRecordDto> validCouponList;
    @Schema(description = "异常时可能适合的数量。错误码等于50030002有值")
    private Long errSuitQuantity;
    @Schema(description = "异常提示")
    private String errDesc;


}
