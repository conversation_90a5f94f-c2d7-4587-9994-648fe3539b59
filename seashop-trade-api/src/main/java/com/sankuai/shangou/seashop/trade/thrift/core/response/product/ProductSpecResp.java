package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/22 17:03
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "规格选择返回值")
public class ProductSpecResp extends BaseThriftDto {

    @Schema(description = "specId")
    private Long specValueId;

    @Schema(description = "规格值")
    private String value;

    @Schema(description = "是否可选")
    private Boolean selectAble;

    @Schema(description = "展示图片")
    private String showPic;


}
