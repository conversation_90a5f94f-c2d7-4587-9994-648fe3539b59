package com.sankuai.shangou.seashop.trade.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PrintOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PrintOrderResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:15
 * 订单查询
 */
@FeignClient(name = "himall-trade",contextId = "PrintOrderQueryFeign", path = "/himall-trade/printOrder", url = "${himall-trade.dev.url:}")
public interface PrintOrderQueryFeign {

    /**
     * 打印订单内容
     */
    @PostMapping(value = "/getOrderPrint", consumes = "application/json")
    ResultDto<PrintOrderResp> getOrderPrint(@RequestBody PrintOrderReq req) throws TException;
}
