package com.sankuai.shangou.seashop.trade.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.*;
import com.sankuai.shangou.seashop.trade.thrift.core.response.*;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.CalculateFreightResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.ProductBaseInfoResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.QueryProductByIdListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * 交易商品查询thrift服务
 * 主要应用于商家端搜索查询交易商品的场景
 *
 * author LiGuoQiang
 */
@FeignClient(name = "himall-trade",contextId = "TradeProductQueryFeign", path = "/himall-trade/tradeProduct", url = "${himall-trade.dev.url:}")
public interface TradeProductQueryFeign {

    /**
     * 搜索商品列表
     */
    @PostMapping(value = "/search", consumes = "application/json")
    ResultDto<SearchTradeProductResp> search(@RequestBody SearchTradeProductReq searchReq) throws TException;

    /**
     * 详情页-店铺内搜索商品列表
     */
    @PostMapping(value = "/searchInShop", consumes = "application/json")
    ResultDto<BasePageResp<TradeProductDto>> searchInShop(@RequestBody SearchProductInShopReq searchReq) throws TException;

    /**
     * 查询热门销售
     */
    @PostMapping(value = "/queryHotSaleProduct", consumes = "application/json")
    ResultDto<HotSaleProductResp> queryHotSaleProduct(@RequestBody QueryHotSaleProductReq request) throws TException;

    /**
     * 查询商城店铺搜索商品
     */
    @PostMapping(value = "/queryMallShopProduct", consumes = "application/json")
    ResultDto<MallShopProductResp> queryMallShopProduct(@RequestBody QueryHotSaleProductReq request) throws TException;

    /**
     * 查询热门关注
     */
    @PostMapping(value = "/queryHotAttentionProduct", consumes = "application/json")
    ResultDto<HotAttentionProductResp> queryHotAttentionProduct(@RequestBody QueryHotAttentionProductReq request) throws TException;

    /**
     * 查询猜你喜欢
     */
    @PostMapping(value = "/queryGuessYouLike", consumes = "application/json")
    ResultDto<GuessYouLikeProductResp> queryGuessYouLike(@RequestBody QueryGuessYouLikeReq request) throws TException;

    /**
     * 查询最新上架
     */
    @PostMapping(value = "/queryNewestProduct", consumes = "application/json")
    ResultDto<NewestProductResp> queryNewestProduct(@RequestBody QueryNewestProductReq request) throws TException;

    /**
     * 凑单-商品列表
     */
    @PostMapping(value = "/searchAddonProduct", consumes = "application/json")
    ResultDto<SearchAddonProductResp> searchAddonProduct(@RequestBody QueryAddonProductReq searchReq) throws TException;

    /**
     * 凑单-活动列表
     */
    @PostMapping(value = "/getShopAddonActivity", consumes = "application/json")
    ResultDto<AddonActivityResp> getShopAddonActivity(@RequestBody QueryShopAddonActivityReq req) throws TException;

    /**
     * 查询商品基本信息
     */
    @PostMapping(value = "/queryProductBaseInfo", consumes = "application/json")
    ResultDto<ProductBaseInfoResp> queryProductBaseInfo(@RequestBody QueryProductDetailReq request) throws TException;

    /**
     * 计算运费
     */
    @PostMapping(value = "/calculateFreight", consumes = "application/json")
    ResultDto<CalculateFreightResp> calculateFreight(@RequestBody CalculateFreightReq request) throws TException;

    /**
     * （ES）根据商品ID集合获取商品的基本信息包括评论数
     */
    @PostMapping(value = "/queryProductByIdList", consumes = "application/json")
    ResultDto<List<QueryProductByIdListResp>> queryProductByIdList(@RequestBody ProductIdsReq request) throws TException;
}
