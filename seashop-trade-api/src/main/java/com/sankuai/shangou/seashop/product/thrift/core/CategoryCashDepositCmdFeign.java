package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.CategoryCashDepositListReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:保证金配置操作服务
 */
@FeignClient(name = "himall-trade", contextId = "CategoryCashDepositCmdFeign", path = "/himall-trade/categoryCashDeposit", url = "${himall-trade.dev.url:}")
public interface CategoryCashDepositCmdFeign {

    /**
     * 保证金配置保存
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/save", consumes = "application/json")
    ResultDto<BaseResp> save(@RequestBody CategoryCashDepositListReq request) throws TException;
}
