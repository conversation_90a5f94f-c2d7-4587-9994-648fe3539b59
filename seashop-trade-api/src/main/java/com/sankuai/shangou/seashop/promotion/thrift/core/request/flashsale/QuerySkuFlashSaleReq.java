package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "查询SKU维度的限时购入参")
public class QuerySkuFlashSaleReq extends BaseParamReq {

    @Schema(description = "限时购活动id")
    private Long flashSaleId;
    @Schema(description = "商品id")
    private Long productId;
    @Schema(description = "skuId")
    private String skuId;


}
