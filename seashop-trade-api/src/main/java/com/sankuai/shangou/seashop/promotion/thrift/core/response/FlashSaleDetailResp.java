package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@Schema(description = "限时购活动明细响应体")
@Data
public class FlashSaleDetailResp extends BaseThriftDto {

    @Schema(description = "规格id")
    private String skuId;

    @Schema(description = "规格名称")
    @ExaminField(description = "规格名称")
    private String skuName;

    @Schema(description = "规格1别名")
    @ExaminField(description = "规格1别名")
    private String spec1Alias;

    @Schema(description = "规格2别名")
    @ExaminField(description = "规格2别名")
    private String spec2Alias;

    @Schema(description = "规格3别名")
    @ExaminField(description = "规格3别名")
    private String spec3Alias;

    @Schema(description = "销售价")
    @ExaminField(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "限时购时金额")
    @ExaminField(description = "限时购时金额")
    private BigDecimal price;

    @Schema(description = "商品库存")
    @ExaminField(description = "商品库存")
    private Long stock;

    @Schema(description = "活动库存")
    @ExaminField(description = "活动库存")
    private Integer totalCount;

    @Schema(description = "限购数量")
    @ExaminField(description = "限购数量")
    private Integer limitCount;

    @Schema(description = "商品主图")
    @ExaminField(description = "商品主图")
    private String imagePath;


    public String getSpec1Alias() {
        return spec1Alias;
    }

    public void setSpec1Alias(String spec1Alias) {
        this.spec1Alias = spec1Alias;
    }

    public String getSpec2Alias() {
        return spec2Alias;
    }

    public void setSpec2Alias(String spec2Alias) {
        this.spec2Alias = spec2Alias;
    }

    public String getSpec3Alias() {
        return spec3Alias;
    }

    public void setSpec3Alias(String spec3Alias) {
        this.spec3Alias = spec3Alias;
    }


}
