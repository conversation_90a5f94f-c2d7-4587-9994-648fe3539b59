package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/06 19:10
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "关联自定义表单入参")
public class BindCustomFormReq extends BaseParamReq {

    @Schema(description = "类目Id", required = true)
    @PrimaryField(title = "类目id")
    @ExaminField(description = "类目id")
    private Long id;

    @Schema(description = "自定义表单Id 传0表示取消关联", required = true)
    @ExaminField(description = "自定义表单id")
    private Long customFormId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "类目Id不能为空");
        AssertUtil.throwInvalidParamIfTrue(customFormId == null || customFormId < 0, "请选择关联的表单");
    }


}
