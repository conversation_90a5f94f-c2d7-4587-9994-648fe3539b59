package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.OrderAdditionalDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "预览订单页-修改商品数量入参")
public class PreviewChangeSkuQuantityReq extends BaseParamReq {

    @Schema(description = "用户ID", required = true)
    private Long userId;
    @Schema(description = "用户收货地址，可能为空")
    private Long shippingAddressId;
    @Schema(description = "购物车ID，根据要需改的数据行的ID，有就传", required = true)
    private Long id;
    @Schema(description = "商品ID，当前修改数量的productId", required = true)
    private Long productId;
    @Schema(description = "skuId，当前修改数量的skuId", required = true)
    private String skuId;
    @Schema(description = "变更后的数量，前端根据sku的基本规则先做校验后传入实际要修改的值", required = true)
    private Long quantity;
    @Schema(description = "店铺信息，当前是整个对象传入，数据是为了保持用户前后看到的数据一致", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表，当前是整个对象传入，数据是为了保持用户前后看到的数据一致", required = true)
    private List<ShoppingCartProductDto> productList;
    @Schema(description = "附加信息，店铺订单的附件信息，包括选择的优惠券，发票等")
    private OrderAdditionalDto additional;
    @Schema(description = "限时购活动id。如果不为空，代表是限时购")
    private Long flashSaleId;
    /**
     * 组合购活动id。如果不为空，代表是组合购
     */
    @Schema(description = "组合购活动id。如果不为空，代表是组合购")
    private Long collocationId;
    @Schema(description = "是否立即购买。如果为true，代表是立即购买")
    private Boolean whetherBuyNow;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.userId == null) {
            throw new InvalidParamException("shopProductList不能为空");
        }
        if (this.productId == null) {
            throw new InvalidParamException("productId不能为空");
        }
        if (StrUtil.isBlank(this.skuId)) {
            throw new InvalidParamException("skuId不能为空");
        }
        if (this.quantity == null || this.quantity <= 0) {
            throw new InvalidParamException("quantity不能为空");
        }
        if (shop == null) {
            throw new InvalidParamException("shop不能为空");
        }
        if (CollUtil.isEmpty(productList)) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
