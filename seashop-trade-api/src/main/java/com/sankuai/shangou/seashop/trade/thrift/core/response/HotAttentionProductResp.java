package com.sankuai.shangou.seashop.trade.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/12 11:33
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "热门关注返回值")
public class HotAttentionProductResp {

    @Schema(description = "商品列表")
    private List<TradeProductDto> productList;


}
