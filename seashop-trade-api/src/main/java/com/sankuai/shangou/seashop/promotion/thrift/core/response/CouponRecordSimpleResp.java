package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@Schema(description = "优惠券记录响应体")
@Data
public class CouponRecordSimpleResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "优惠券活动ID")
    private Long couponId;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "优惠券优惠码")
    private String couponSn;

    @Schema(description = "订单金额（满足多少钱才能使用）")
    private Long orderAmount;

    @Schema(description = "面值(价格)")
    private Long price;

    @Schema(description = "优惠券领用时间")
    private Date couponTime;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "优惠券使用时间")
    private Date usedTime;

    @Schema(description = "订单ID")
    private String orderId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "优惠券状态 0-未使用 1-已使用 2-已过期")
    private Integer couponStatus;

    @Schema(description = "优惠券状态名称")
    private String couponStatusDesc;

    @Schema(description = "优惠券创建时间")
    private Date createTime;

    @Schema(description = "优惠券开始时间")
    private Date startTime;

    @Schema(description = "优惠券结束时间")
    private Date endTime;

    @Schema(description = "使用范围：0=全场通用，1=部分商品可用")
    private Integer useArea;

    @Schema(description = "商品ID列表(部分商品可用时使用)")
    private List<Long> productIdList;

    @Schema(description = "备注")
    private String remark;


}
