package com.sankuai.shangou.seashop.trade.thrift.core.response.mall;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "店铺ES品牌列表响应体")
public class ApiShopBrandEsResp extends BaseThriftDto {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    private Long brandId;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    /**
     * 品牌logo
     */
    @Schema(description = "品牌logo")
    private String logo;


}
