package com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/06 19:24
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "转移商品入参")
public class TransferProductReq extends BaseParamReq {

    @Schema(description = "原类目id", required = true)
    @ExaminField(description = "原类目id")
    private Long fromCategoryId;

    @Schema(description = "目标类目id", required = true)
    @ExaminField(description = "目标类目id")
    private Long toCategoryId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(fromCategoryId == null || fromCategoryId <= 0, "原类目id不能为空");
        AssertUtil.throwInvalidParamIfTrue(toCategoryId == null || toCategoryId <= 0, "目标类目id不能为空");
    }


}
