package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/20 15:44
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "关联推荐商品入参")
public class BindRecommendProductReq extends BaseParamReq {

    @Schema(description = "商品id", required = true)
    @ExaminField(description = "商品id")
    private Long productId;

    @Schema(description = "推荐商品id的集合", required = true)
    @ExaminField(description = "关联商品id的集合")
    private List<Long> recommendProductIdList;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productId == null || productId <= 0, "请选择需要操作的商品");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isNotEmpty(recommendProductIdList)
                && recommendProductIdList.size() > ParameterConstant.RECOMMEND_PRODUCT_NUM, String.format("推荐商品不能超过%d个", ParameterConstant.RECOMMEND_PRODUCT_NUM));
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
