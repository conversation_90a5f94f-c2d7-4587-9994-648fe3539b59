package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/25 16:39
 */
@Schema(description = "商品富文本信息")
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
public class ProductRichTextResp extends BaseThriftDto {

    @Schema(description = "详情富文本")
    private String description;

    @Schema(description = "顶部版式")
    private String descriptionPrefix;

    @Schema(description = "底部版式")
    private String descriptionSuffix;


}
