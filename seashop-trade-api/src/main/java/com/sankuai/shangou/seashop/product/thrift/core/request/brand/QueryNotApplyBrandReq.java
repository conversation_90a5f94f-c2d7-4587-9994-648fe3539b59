package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/07 14:12
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class QueryNotApplyBrandReq extends BaseParamReq {

    @Schema(description = "供应商id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "请传入供应商id");
    }


}
