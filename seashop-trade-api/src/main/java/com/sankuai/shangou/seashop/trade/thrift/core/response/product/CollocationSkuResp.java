package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Setter
@Getter
public class CollocationSkuResp {

    /**
     * 组合购商品SKU表主键ID
     */
    private Long id;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品SkuId
     */
    private String skuId;

    /**
     * 规格1
     */
    private String spec1Value;

    /**
     * 规格2
     */
    private String spec2Value;

    /**
     * 规格3
     */
    private String spec3Value;

    /**
     * 组合商品表ID
     */
    private Long colloProductId;

    /**
     * 组合购价格
     */
    private BigDecimal price;

    /**
     * 原始价格
     */
    private BigDecimal skuPirce;

    /**
     * 库存
     */
    private Long stock;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


}
