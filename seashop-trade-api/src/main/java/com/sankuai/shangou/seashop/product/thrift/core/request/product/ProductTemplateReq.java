package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/25 14:29
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Builder
@Schema(description = "查询商品入参")
public class ProductTemplateReq extends BaseParamReq {

    @Schema(description = "运费模版ID")
    private Long templateId;


    @Override
    public void checkParameter() {
        if (templateId == null) {
            throw new IllegalArgumentException("运费模版ID不能为空");
        }
    }
}
