package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/11 9:37
 */
@Data
@Schema(description = "平台首页统计商品信息")
public class MStatisticalProductResp extends BaseThriftDto {

    @Schema(description = "商品总数")
    private Integer totalNum;

    @Schema(description = "出售中")
    private Integer productsOnSale;

    @Schema(description = "商品待审核")
    private Integer productsWaitForAuditing;

    @Schema(description = "授权品牌待审核")
    private Integer productsBrands;

    @Schema(description = "商品评论数")
    private Integer productsComment;


}
