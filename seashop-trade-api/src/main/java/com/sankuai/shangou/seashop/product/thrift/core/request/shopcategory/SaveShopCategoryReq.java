package com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/11/13 11:06
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "新增/编辑店铺分类入参")
public class SaveShopCategoryReq extends BaseParamReq {

    @Schema(description = "店铺分类id 新增时不传/传0")
    @PrimaryField(title = "店铺分类id")
    @ExaminField(description = "店铺分类id")
    private Long id;

    @Schema(description = "店铺分类名称")
    @ExaminField(description = "店铺分类名称")
    private String name;

    @Schema(description = "上级分类id 一级分类不传/传0")
    @ExaminField(description = "上级分类id")
    private Long parentCategoryId;

    @Schema(description = "排序字段(新增时无需传)")
    @ExaminField(description = "排序")
    private Long displaySequence;

    @Schema(description = "是否显示")
    @ExaminField(description = "是否显示")
    private Boolean whetherShow;

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(name), "店铺分类名称不能为空");
        AssertUtil.throwInvalidParamIfTrue(name.length() > ParameterConstant.SHOP_CATEGORY_NAME_LENGTH,
                String.format("店铺分类名称不能超过%d个字符", ParameterConstant.SHOP_CATEGORY_NAME_LENGTH));
        AssertUtil.throwInvalidParamIfTrue(shopId != null && shopId < 0, "店铺id不能为空");
    }

    public void checkForEdit() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "店铺分类id不能为空");
        AssertUtil.throwInvalidParamIfTrue(shopId != null && shopId < 0, "店铺id不能为空");
        if (StringUtils.isNotBlank(name)) {
            AssertUtil.throwInvalidParamIfTrue(name.length() > ParameterConstant.SHOP_CATEGORY_NAME_LENGTH,
                    String.format("店铺分类名称不能超过%d个字符", ParameterConstant.SHOP_CATEGORY_NAME_LENGTH));
        }
    }


}
