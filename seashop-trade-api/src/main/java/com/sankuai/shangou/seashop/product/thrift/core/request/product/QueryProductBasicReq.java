package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 15:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "根据商品id查询商品入参")
public class QueryProductBasicReq extends BasePageReq {

    @Schema(description = "商品id列表", required = true)
    private List<Long> productIds;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIds), "商品id不能为空");
        AssertUtil.throwInvalidParamIfTrue(productIds.size() > ParameterConstant.QUERY_LIST_LIMIT,
                String.format("商品id不能超过%s个", ParameterConstant.QUERY_LIST_LIMIT));
    }


}
