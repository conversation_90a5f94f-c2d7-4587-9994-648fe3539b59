package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "删除购物车sku请求入参")
public class ClearInvalidShoppingCartSkuReq extends BaseParamReq {

    @Schema(description = "购物车唯一标识，目前是数据表主键ID，取购物车列表的ID字段", required = true)
    private List<Long> idList;
    @Schema(description = "商家会员ID，用于校验是否删除的是自己的数据", required = true)
    private Long userId;

    /**
     * 基本参数校验
     */
    @Override
    public void checkParameter() {
        if (this.idList == null || this.idList.isEmpty()) {
            throw new InvalidParamException("id不能为空");
        }
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
    }


}
