package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "订单维度查询券/折扣请求对象")
public class PromotionRecordOrderQueryReq extends BaseParamReq {

    @Schema(description = "用户ID", required = true)
    private Long userId;

    @Schema(description = "订单信息列表", required = true)
    private List<OrderQueryReq> orderList;

    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (CollUtil.isEmpty(this.orderList)) {
            throw new InvalidParamException("orderList不能为空");
        }
        orderList.forEach(OrderQueryReq::checkParameter);
    }


}
