package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:07
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询类目树入参")
public class QueryCategoryTreeReq extends BaseParamReq {

    @Schema(description = "类目id的集合", required = true)
    private List<Long> ids;

    @Schema(description = "是否显示类目")
    private Boolean whetherShow;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(ids), "类目id的集合不能为空");
    }


}
