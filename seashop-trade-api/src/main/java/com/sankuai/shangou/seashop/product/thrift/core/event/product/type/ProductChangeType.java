package com.sankuai.shangou.seashop.product.thrift.core.event.product.type;

import com.sankuai.shangou.seashop.product.thrift.core.event.ChangeType;
import lombok.Getter;

/**
 * 商品变动类型
 *
 * <AUTHOR>
 * @date 2024/01/03 13:37
 */
@Getter
public enum ProductChangeType {

    /**
     * 业务编码规格 共五位
     * 前两位表示操作类型 10-创建 11-编辑 12-删除
     * 后三位表示具体操作
     *
     * <p>
     * 创建: 1.创建 2.导入创建
     * <p>
     * 更新: 1.编辑 2.上架 3.下架 4.保存平台序号 5.保存商家序号
     *      6.关联版式 7.关联推荐商品 8.关联运费模板 9.改价
     *      10.设置警戒库存 11.设置库存 12.设置虚拟销量 13.违规下架
     *      14.库存导入 15.导入下架 16.导入违规下架 17.导入价格
     *      18.店铺冻结下架商品 19.修改主图 20.导入编辑 21.导入69码 22.更新一品多码/一码多品
     *      23.审核通过 24.审核拒绝
     * <p>
     * 删除: 1.删除
     */

    // 创建
    CREATE(10001, ChangeType.CREATE),
    IMPORT_CREATE(10002, ChangeType.CREATE),

    // 更新
    UPDATE(11001, ChangeType.UPDATE),
    // EDIT(11002, ChangeType.UPDATE),
    UP(11003, ChangeType.UPDATE),
    DOWN(11004, ChangeType.UPDATE),
    SAVE_PLATFORM_NO(11005, ChangeType.UPDATE),
    SAVE_MERCHANT_NO(11006, ChangeType.UPDATE),
    BIND_DESCRIPTION_TEMPLATE(11007, ChangeType.UPDATE),
    BIND_RECOMMEND_PRODUCT(11008, ChangeType.UPDATE),
    BIND_FREIGHT_TEMPLATE(11009, ChangeType.UPDATE),
    CHANGE_PRICE(11010, ChangeType.UPDATE),
    SET_WARNING_STOCK(11011, ChangeType.UPDATE),
    SET_STOCK(11012, ChangeType.UPDATE),
    SET_VIRTUAL_SALES(11013, ChangeType.UPDATE),
    VIOLATION_DOWN(11014, ChangeType.UPDATE),
    IMPORT_STOCK(11015, ChangeType.UPDATE),
    IMPORT_DOWN(11016, ChangeType.UPDATE),
    IMPORT_VIOLATION_DOWN(11017, ChangeType.UPDATE),
    IMPORT_PRICE(11018, ChangeType.UPDATE),
    SHOP_FREEZE_DOWN(11019, ChangeType.UPDATE),
    CHANGE_MAIN_PIC(11020, ChangeType.UPDATE),
    IMPORT_EDIT(11021, ChangeType.UPDATE),
    AUDIT_PASS(11024, ChangeType.UPDATE),
    AUDIT_REJECT(11025, ChangeType.UPDATE),
    VIOLATION_DOWN_UP(11026, ChangeType.UPDATE),

    // 商品分类转移
    TRANSFER_CATEGORY(11026, ChangeType.UPDATE),
    // 删除
    DELETE(12001, ChangeType.DELETE);


    private final Integer code;
    private final ChangeType type;

    ProductChangeType(Integer code, ChangeType type) {
        this.code = code;
        this.type = type;
    }

}
