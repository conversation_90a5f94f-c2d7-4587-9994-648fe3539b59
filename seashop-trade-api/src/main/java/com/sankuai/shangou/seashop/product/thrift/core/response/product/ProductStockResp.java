package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/19 14:13
 */
@Schema(description = "商品总库存")
@Data
public class ProductStockResp extends BaseThriftDto {

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "总库存")
    private Long totalStock;


}
