package com.sankuai.shangou.seashop.product.thrift.core.enums;

/**
 * 商品状态 1-销售中 2-仓库中 3-草稿箱 4-违规下架
 * 该状态表示页面上的状态 与销售状态和审核状态存在映射关系
 *
 * <AUTHOR>
 * @date 2023/11/17 10:09
 */
public enum ProductStatusEnum {

    /**
     * 销售中
     */
    ON_SALE(1, "销售中", ProductEnum.SaleStatusEnum.ON_SALE, ProductEnum.AuditStatusEnum.ON_SALE),
    /**
     * 仓库中
     */
    IN_STOCK(2, "仓库中", ProductEnum.SaleStatusEnum.IN_STOCK, ProductEnum.AuditStatusEnum.ON_SALE),
    /**
     * 违规下架
     */
    VIOLATION(3, "违规下架", null, ProductEnum.AuditStatusEnum.VIOLATION),
    /**
     * 草稿箱
     */
    DRAFT(4, "草稿箱", ProductEnum.SaleStatusEnum.DRAFT, null),
    /**
     * 待审核
     */
    WAIT_AUDIT(5, "待审核", null, ProductEnum.AuditStatusEnum.WAIT_AUDIT),
    /**
     * 未通过(审核失败)
     */
    AUDIT_FAIL(6, "未通过", null, ProductEnum.AuditStatusEnum.NOT_PASS),

    /**
     * 默认状态 无意义 该枚举放到最后, 异常状态的数据会匹配到该枚举
     */
    NONE(0, "", null, null);

    private Integer code;
    private String desc;
    private ProductEnum.SaleStatusEnum saleStatus;
    private ProductEnum.AuditStatusEnum auditStatus;

    ProductStatusEnum(Integer code, String desc, ProductEnum.SaleStatusEnum saleStatus, ProductEnum.AuditStatusEnum auditStatus) {
        this.code = code;
        this.desc = desc;
        this.saleStatus = saleStatus;
        this.auditStatus = auditStatus;
    }

    public static ProductStatusEnum getByCode(Integer code) {
        for (ProductStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static ProductStatusEnum getBySaleAuditStatus(Integer saleStatusCode,
                                                         Integer auditStatusCode) {
        ProductStatusEnum[] enumList = ProductStatusEnum.values();
        for (ProductStatusEnum status : enumList) {
            ProductEnum.SaleStatusEnum curSaleStatus = status.saleStatus;
            ProductEnum.AuditStatusEnum curAuditStatus = status.auditStatus;
            if ((curSaleStatus == null || curSaleStatus.getCode().equals(saleStatusCode))
                    && (curAuditStatus == null || curAuditStatus.getCode().equals(auditStatusCode))) {
                return status;
            }
        }
        return NONE;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public ProductEnum.SaleStatusEnum getSaleStatus() {
        return saleStatus;
    }

    public ProductEnum.AuditStatusEnum getAuditStatus() {
        return auditStatus;
    }
}
