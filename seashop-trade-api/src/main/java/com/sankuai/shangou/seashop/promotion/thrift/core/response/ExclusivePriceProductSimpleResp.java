package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import lombok.Data;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@Schema(description = "专享价查询响应对象")
@Data
public class ExclusivePriceProductSimpleResp extends BaseThriftDto {

    @Schema(description = "返回结果")
    private List<ExclusivePriceProductDto> resultList;


}
