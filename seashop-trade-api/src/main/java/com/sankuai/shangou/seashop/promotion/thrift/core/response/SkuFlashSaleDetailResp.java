package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "SKU维度的限时购明细")
public class SkuFlashSaleDetailResp extends BaseThriftDto {

    /**
     * 限时购活动ID
     */
    @Schema(description = "限时购活动ID")
    private Long flashSaleId;
    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
    private Long productId;
    /**
     * skuId
     */
    @Schema(description = "skuId")
    private String skuId;
    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID")
    private Long shopId;
    /**
     * 限时购价格
     */
    @Schema(description = "限时购价格")
    private BigDecimal price;
    /**
     * 限时购库存，如果是商品维度的限时购，这个字段设置为空
     */
    @Schema(description = "限时购库存，如果是商品维度的限时购，这个字段设置为空")
    private Integer totalCount;
    /**
     * 每个用户购买限制数量
     */
    @Schema(description = "每个用户购买限制数量")
    private Integer limitCount;
    /**
     * 限时购类型。1：商品维度；2：sku维度
     */
    @Schema(description = "限时购类型。1：商品维度；2：sku维度")
    private Integer limitType;
    @Schema(description = "限时购名称")
    private String flashSaleName;


}
