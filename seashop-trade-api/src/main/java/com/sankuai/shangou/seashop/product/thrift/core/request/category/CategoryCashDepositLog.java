package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/24 15:58
 */
@Data
@ToString
@Schema(description = "保证金配置")
public class CategoryCashDepositLog extends BaseParamReq {

    /**
     * 类目名称
     */
    @Schema(description = "类目名称")
    @PrimaryField
    @ExaminField(description = "类目名称")
    private String categoryName;

    /**
     * 需要缴纳保证金
     */
    @Schema(description = "需要缴纳保证金")
    @ExaminField(description = "需要缴纳保证金")
    private BigDecimal needPayCashDeposit;

    /**
     * 允许七天无理由退货
     */
    @Schema(description = "允许七天无理由退货")
    @ExaminField(description = "允许七天无理由退货")
    private Boolean enableNoReasonReturn;


}
