package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/27 16:49
 */
@Schema(description = "查询商品模版数量返回")
@Data
public class CountProductTemplateResp extends BaseThriftDto {

    @Schema(description = "模版ID")
    private Long templateId;

    @Schema(description = "关联商品数量")
    private Integer num;


}
