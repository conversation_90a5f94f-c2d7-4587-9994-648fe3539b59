package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductSkuMergeDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "查询商品SKU集合响应体")
public class ProductSkuMergeQueryResp extends BaseThriftDto {

    @Schema(description = "商品SKU集合")
    private List<ProductSkuMergeDto> productSkuList;

    @Schema(description = "失效的商品(主要是针对skuId 已经被删除的商品)")
    private List<ProductSkuMergeDto> invalidSkuList;

}
