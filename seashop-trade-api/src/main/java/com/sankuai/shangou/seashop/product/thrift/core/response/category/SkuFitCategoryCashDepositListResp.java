package com.sankuai.shangou.seashop.product.thrift.core.response.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "SKU适配的一级类目保证金配置列表返回对象")
public class SkuFitCategoryCashDepositListResp extends BaseThriftDto {

    @Schema(description = "SKU适配的一级类目保证金配置列表")
    private List<SkuFitCategoryCashDepositResp> skuCateDepositList;


}
