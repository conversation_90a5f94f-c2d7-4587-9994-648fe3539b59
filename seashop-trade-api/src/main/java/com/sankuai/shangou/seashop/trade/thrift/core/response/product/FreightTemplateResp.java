package com.sankuai.shangou.seashop.trade.thrift.core.response.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/01/04 15:54
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "运费模板相关返回值")
public class FreightTemplateResp extends BaseThriftDto {

    @Schema(description = "运费模板id")
    private Long freightTemplateId;

    @Schema(description = "运费模板名称")
    private String name;

    @Schema(description = "宝贝发货地")
    private Integer sourceAddress;

    @Schema(description = "发送时间")
    private String sendTime;

    @Schema(description = "是否商家负责运费 0-否 1-是")
    private Integer whetherFree;

    @Schema(description = "是否在限购区域")
    private Boolean inRestrictedRegion;


}
