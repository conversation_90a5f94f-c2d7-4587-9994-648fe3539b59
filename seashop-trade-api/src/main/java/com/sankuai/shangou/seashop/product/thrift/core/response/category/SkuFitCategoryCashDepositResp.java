package com.sankuai.shangou.seashop.product.thrift.core.response.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "SKU适配的一级类目保证金配置返回对象")
public class SkuFitCategoryCashDepositResp extends BaseThriftDto {

    @Schema(description = "skuId")
    private String skuId;
    @Schema(description = "商品ID")
    private Long productId;
    @Schema(description = "类目路径")
    private String categoryPath;
    @Schema(description = "一级类目ID")
    private Long cateLevel1Id;
    @Schema(description = "三级类目ID")
    private Long cateLevel3Id;
    @Schema(description = "一级类目保证金配置")
    private CategoryCashDepositResp categoryCashDepositResp;
    @Schema(description = "店铺和经营类目ID查询保证金是否足够,true足够，false不足够")
    private Boolean enoughCashFlag;
    @Schema(description = "店铺ID")
    private Long shopId;


    public Long getCateLevel1Id() {
        return cateLevel1Id;
    }

    public void setCateLevel1Id(Long cateLevel1Id) {
        this.cateLevel1Id = cateLevel1Id;
    }

    public Long getCateLevel3Id() {
        return cateLevel3Id;
    }

    public void setCateLevel3Id(Long cateLevel3Id) {
        this.cateLevel3Id = cateLevel3Id;
    }


}
