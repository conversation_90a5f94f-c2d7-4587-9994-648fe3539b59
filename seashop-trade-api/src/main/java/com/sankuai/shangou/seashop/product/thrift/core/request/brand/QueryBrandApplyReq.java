package com.sankuai.shangou.seashop.product.thrift.core.request.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class QueryBrandApplyReq extends BasePageReq {

    @Schema(description = "品牌id")
    private Long brandId;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "审核状态")
    private BrandEnum.AuditStatusEnum auditStatus;

    @Schema(description = "供应商id")
    private Long shopId;

    @Schema(description = "供应商名称")
    private String shopName;


}
