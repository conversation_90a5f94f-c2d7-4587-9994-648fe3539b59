package com.sankuai.shangou.seashop.product.thrift.core.response.sku;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 14:54
 */
@Data
@Schema(description = "sku规格数据返参")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuListResp extends BaseThriftDto {

    @Schema(description = "sku规格数据")
    private List<SkuQueryResp> skuList;


}
