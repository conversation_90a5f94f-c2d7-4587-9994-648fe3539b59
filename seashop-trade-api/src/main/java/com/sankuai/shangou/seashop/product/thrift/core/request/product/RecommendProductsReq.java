package com.sankuai.shangou.seashop.product.thrift.core.request.product;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/13 17:17
 */
@Data
@Schema(description = "商品推荐店铺ID")
public class RecommendProductsReq extends BaseThriftDto {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "展示数量，默认8个")
    private Integer limitNum = 8;


}
