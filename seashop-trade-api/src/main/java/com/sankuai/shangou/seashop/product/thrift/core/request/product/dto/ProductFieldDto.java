package com.sankuai.shangou.seashop.product.thrift.core.request.product.dto;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "关联对象入参")
public class ProductFieldDto extends BaseParamReq  {

    @Schema(description = "表头", required = true)
    private String name;

    @Schema(description = "关联名称")
    private String fieldName;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(name), "表头不能为空");
    }

}
