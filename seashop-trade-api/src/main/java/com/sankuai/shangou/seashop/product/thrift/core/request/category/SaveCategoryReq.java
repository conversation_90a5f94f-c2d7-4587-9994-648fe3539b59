package com.sankuai.shangou.seashop.product.thrift.core.request.category;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:05
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "新增类目入参")
public class SaveCategoryReq extends BaseParamReq {

    /**
     * 主键
     */
    @Schema(description = "类目id")
    @PrimaryField(title = "类目id")
    @ExaminField(description = "类目id")
    private Long id;

    /**
     * 类目名称
     */
    @Schema(description = "类目名称", required = true)
    @ExaminField(description = "类目名称")
    private String name;

    /**
     * 类目图标
     */
    @Schema(description = "类目图标")
    @ExaminField(description = "类目图标")
    private String icon;

    /**
     * 上级类目id
     */
    @Schema(description = "上级类目id 编辑是不可修改")
    @ExaminField(description = "上级类目id")
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    @Schema(description = "分佣比例 创建三级类目时才需要设置")
    @ExaminField(description = "分佣比例")
    private BigDecimal commissionRate;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(name), "请输入类目名称");
        AssertUtil.throwInvalidParamIfTrue(name.length() > ParameterConstant.CATEGORY_NAME_LENGTH,
                String.format("类目名称最多只能输入%d个字符", ParameterConstant.CATEGORY_NAME_LENGTH));
    }

    public void checkForEdit() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "请传入类目id");
        checkParameter();
    }


}
