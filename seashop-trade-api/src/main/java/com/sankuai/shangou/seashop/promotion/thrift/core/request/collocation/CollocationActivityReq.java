package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/7 9:50
 */
@Schema(description = "组合购活动入参")
@Data
public class CollocationActivityReq extends BaseParamReq {

    @Schema(description = "商品ID集合")
    private List<String> productIds;

    @Schema(description = "是否查询进行中和未开始的活动,1是0否")
    private Integer flag;


    public void checkParameter() {
        if (productIds == null || productIds.isEmpty()) {
            throw new IllegalArgumentException("商品ID集合不能为空");
        }
    }


}
