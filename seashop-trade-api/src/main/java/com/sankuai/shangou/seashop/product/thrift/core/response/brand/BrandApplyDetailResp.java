package com.sankuai.shangou.seashop.product.thrift.core.response.brand;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandApplyDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 19:39
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "品牌详情返回值")
public class BrandApplyDetailResp extends BaseThriftDto {

    @Schema(description = "品牌详情")
    private BrandApplyDto result;


}
