package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 源数据表-订单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Getter
@Setter

public class ReportSourceOrderReq {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 支付时间
     */
    private Date paymentTime;

    private Date finishTime;

    private Date deliveryTime;
    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    private Long provinceId;

    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
