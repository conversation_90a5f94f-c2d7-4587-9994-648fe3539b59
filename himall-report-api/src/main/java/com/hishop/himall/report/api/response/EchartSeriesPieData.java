package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * ECharts饼图数据项类
 * 用于饼图的单个数据项，包含名称和数值
 * 实现Comparable接口，支持按数值大小排序（降序）
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class EchartSeriesPieData implements Comparable<EchartSeriesPieData> {

    /**
     * 数据项名称
     */
    private String name;

    /**
     * 数据项数值
     */
    private BigDecimal value;

    /**
     * 比较方法，用于排序
     * 按数值大小降序排列
     *
     * @param o 比较对象
     * @return 比较结果
     */
    @Override
    public int compareTo(EchartSeriesPieData o) {
        return o.getValue().compareTo( this.getValue());
    }
}