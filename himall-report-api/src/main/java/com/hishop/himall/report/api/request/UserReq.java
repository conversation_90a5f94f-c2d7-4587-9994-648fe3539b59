package com.hishop.himall.report.api.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.himall.report.api.enums.RangeEnum;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.Date;

/**
 * 用户报表查询请求类
 * 用于用户分析相关的查询请求，支持分页查询
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
public class UserReq extends BasePageReq {

    /**
     * 店铺ID，为空时查询全部店铺
     */
    private Long shopId;

    /**
     * 时间维度（日/周/月/年）
     */
    private RangeEnum range;

    /**
     * 查询开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date start;

    /**
     * 查询结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date end;
}
