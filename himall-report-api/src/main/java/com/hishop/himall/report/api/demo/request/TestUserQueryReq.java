package com.hishop.himall.report.api.demo.request;

import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;

/**
 * testuser 查询入参
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TestUserQueryReq {

    /**
     * 数据id
     */
    @NotNull(message = "{100010}")
    private Long id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 当前时间
     */
    private LocalDateTime nowTime;

}
