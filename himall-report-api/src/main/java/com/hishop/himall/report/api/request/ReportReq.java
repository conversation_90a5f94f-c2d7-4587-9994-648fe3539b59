package com.hishop.himall.report.api.request;

import com.hishop.himall.report.api.enums.RangeEnum;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

/**
 * 报表查询基础请求类
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Setter
@Getter
public class ReportReq {

    /**
     * 时间维度（日/周/月/年）
     */
    private RangeEnum range;

    /**
     * 店铺ID，为空时默认为0（全部店铺）
     */
    private Long shopId;

    /**
     * 指定日期（用于周/月维度的起始日期计算）
     */
    private LocalDate day;

    /**
     * 查询开始日期
     */
    private LocalDate start;

    /**
     * 查询结束日期
     */
    private LocalDate end;

    /**
     * 获取上一个周期的时间范围
     * 用于同比分析，将当前时间范围向前推移一个周期
     */
    public void getPrev() {
        switch (this.getRange()) {
            case DAY:
                // 计算 end与start的天数差
                long days = this.getEnd().toEpochDay() - this.getStart().toEpochDay() + 1;
                start = start.minusDays(days);
                end = end.minusDays(days);
                break;
            case WEEK:
                // TODO: 实现周维度的上期计算
                // start = day.minusWeeks(1);
                // end = day.minusWeeks(1);
                break;
            case MONTH:
                // TODO: 实现月维度的上期计算
                // start = day.minusMonths(1);
                // end = day.minusMonths(1);
                break;
        }
    }
    /**
     * 获取店铺ID，如果为空则返回0（表示全部店铺）
     *
     * @return 店铺ID，0表示全部店铺
     */
    public Long getShopId() {
        if(shopId == null){
            return 0L;
        }
        return shopId;
    }

    /**
     * 根据时间维度和指定日期填充开始和结束日期
     * 用于周/月维度时，根据指定的day计算完整的时间范围
     */
    public void FillDate() {
        if (range == RangeEnum.MONTH) {
            if (day != null) {
                setStart(getDay());
            }
            setEnd(getStart().plusMonths(1).minusDays(1));
        } else if (getRange() == RangeEnum.WEEK) {
            if (day != null) {
                setStart(getDay());
            }
            setEnd(getStart().plusWeeks(1).minusDays(1));
        }
    }

    /**
     * 将LocalDate类型的开始日期转换为Date类型
     *
     * @return Date类型的开始日期，如果start为null则返回null
     */
    public Date getStartDate() {
        if (start == null) {
            return null;
        }
        return Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将LocalDate类型的结束日期转换为Date类型
     *
     * @return Date类型的结束日期，如果end为null则返回null
     */
    public Date getEndDate() {
        if (end == null) {
            return null;
        }
        return Date.from(end.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
}

