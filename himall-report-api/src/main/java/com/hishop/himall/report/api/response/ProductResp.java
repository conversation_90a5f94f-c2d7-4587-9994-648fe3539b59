package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 商品报表响应类
 * 包含商品的各项统计指标数据
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class ProductResp {

    /**
     * 商品ID
     */
    private Integer productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 加购人数
     */
    private Integer cartUsers;

    /**
     * 加购件数
     */
    private Integer cartQuantity;

    /**
     * 下单人数
     */
    private Integer orderUsers;

    /**
     * 下单笔数
     */
    private Integer orderOrders;

    /**
     * 下单件数
     */
    private Integer orderQuantity;

    /**
     * 下单金额
     */
    private BigDecimal orderAmount;

    /**
     * 支付人数
     */
    private Integer paymentUsers;

    /**
     * 支付订单数
     */
    private Integer paymentOrders;

    /**
     * 支付件数
     */
    private Integer paymentQuantity;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 申请售后订单数
     */
    private Integer applyOrders;

    /**
     * 申请售后人数
     */
    private Integer applyUsers;

    /**
     * 退款订单数
     */
    private Integer refundOrders;

    /**
     * 退款人数
     */
    private Integer refundUsers;

    /**
     * 退款件数
     */
    private Integer refundQuantity;

    /**
     * 退款金额
     */
    private Integer refundAmount;

    /**
     * 访问人数
     */
    private Integer visitsUsers;

    /**
     * 访问次数
     */
    private Integer visitsCount;

    /**
     * 关注人数
     */
    private Integer followUsers;

    /**
     * 计算退款率
     * 退款率 = 退款订单数 / 支付订单数
     *
     * @return 退款率，保留4位小数
     */
    public BigDecimal getRefundRate(){
        if(paymentOrders==null || paymentOrders==0)
            return BigDecimal.ZERO;
        return  BigDecimal.valueOf(refundOrders).divide(BigDecimal.valueOf(paymentOrders), 4, RoundingMode.HALF_UP);
    }

}
