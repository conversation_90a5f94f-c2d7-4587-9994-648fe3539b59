package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 基础源数据-商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
public class ReportSourceProductReq {

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编码
     */
    private String productSpu;
    /**
     * 商品缩略图
     */
    private String thumbnailUrl;
    /**
     * 一级分类名称
     */
    private String categoryFirst;

    /**
     * 二级分类名称
     */
    private String categorySecond;

    private LocalDateTime createTime;


    private LocalDateTime updateTime;
}
