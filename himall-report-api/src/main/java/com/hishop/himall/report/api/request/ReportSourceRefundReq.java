package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 源数据表-售后表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Getter
@Setter
public class ReportSourceRefundReq {

    private Long refundId;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单ID
     */
    private String orderId;

    private BigDecimal amount;

    /**
     * 订单项ID
     */
    private Long orderItemId;

    /**
     * 门店ID
     */
    private Integer shopId;

    private Integer quantity;
    /**
     * 商品ID
     */
    private Long productId;

    private Integer platform;
    /**
     * 规格ID
     */
    private String skuId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 完成时间
     */
    private Date completionTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
