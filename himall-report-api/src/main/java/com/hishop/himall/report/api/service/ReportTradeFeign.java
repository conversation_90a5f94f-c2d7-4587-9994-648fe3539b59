package com.hishop.himall.report.api.service;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.TradeReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.TradeResp;
import com.hishop.himall.report.api.response.TradeSummaryResp;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.util.model.ResultInfo;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-report", contextId = "ReportTradeFeign", path = "/himall-report/report/trade", url = "${himall-report.dev.url:}")
public interface ReportTradeFeign {
    /**
     * 交易概览
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<TradeSummaryResp> queryProductSummary(@RequestBody ReportReq req);

    /**
     * 交易趋势
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> echarts(@RequestBody ReportReq req);

    /**
     * 交易省份分布
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/province", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> queryProvince(@RequestBody ReportReq req);

    /**
     * 交易分析报表
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<PageResult<TradeResp>> queryTrade(@RequestBody TradeReq req);

    /**
     * 导出交易分析报表
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<PageResult<Object>> export(@RequestBody TradeReq req);
}
