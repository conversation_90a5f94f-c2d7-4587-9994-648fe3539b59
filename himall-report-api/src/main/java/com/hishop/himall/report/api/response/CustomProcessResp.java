package com.hishop.himall.report.api.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 自定义报表处理响应类
 * 包含自定义报表的所有统计指标数据，支持多维度的数据分析
 * 涵盖订单、支付、访问、转化、退款、包裹、用户等各个业务维度
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
public class CustomProcessResp {
    private LocalDate date;

    private Long shopId;
    // 店铺名称
    private String shopName;

    private Long productId;
    // 商品名称
    private String productName;
    // 商品编码
    private String productSpu;

    //下单相关
    // 下单笔数
    private Integer orderOrders;
    // 下单人数
    private Integer orderUsers;
    // 下单金额
    private BigDecimal orderAmount;
    //支付相关
    // 支付笔数
    private Integer paymentOrders;
    // 支付人数
    private Integer paymentUsers;
    // 支付金额
    private BigDecimal paymentAmount;
    // 支付件数
    private Integer paymentQuantity;
    // 支付客单价
    private BigDecimal paymentUnitPrice;
    // 支付商品金额
    private BigDecimal paymentProductAmount;
    // 支付商品件数
    private Integer paymentProductQuantity;


    // 新客支付金额
    private BigDecimal paymentNewAmount;
    // 新客支付笔数
    private Integer paymentNewOrders;
    // 新客支付人数
    private Integer paymentNewUsers;
    // 老客支付金额
    private BigDecimal paymentOldAmount;
    // 老客支付笔数
    private Integer paymentOldOrders;
    // 老客支付人数
    private Integer paymentOldUsers;
    //加购相关
    // 加购件数
    private Integer cartQuantity;
    // 加购人数
    private Integer cartUsers;
    //访问相关
    // 访客数
    private Integer visitsUsers;
    // 访问量
    private Integer visitsCount;
    // 店铺访客数
    private Integer visitsShopUsers;
    // 店铺访问量
    private Integer visitsShopCount;
    // 商品访客数
    private Integer visitsProductUsers;
    // 商品访问量
    private Integer visitsProductCount;
    //转化率相关

    // 访问-下单转化率
    private String visitOrderRate;
    // 访问-支付转化率
    private String visitPaymentRate;
    // 下单-支付转化率
    private String orderPaymentRate;

    // 退款金额
    private BigDecimal refundAmount;
    // 退款笔数
    private Integer refundOrders;
    // 退款件数
    private Integer refundQuantity;
    // 退款人数
    private Integer refundUsers;
    // 退款率
    private String refundRate;
    //包裹相关
    // 包裹发货数
    private Integer packageDelivery;
    // 包裹完成数
    private Integer packageFinish;
    // 发货时长(小时)
    private BigDecimal packageDeliveryHour;
    // 完成时长(小时)
    private BigDecimal packageFinishHour;
    //用户相关
    // 用户总数
    private Integer userTotal;
    // 新增用户数
    private Integer userNew;
    // 支付用户数
    private Integer userPayment;
    // 领券用户数
    private Integer userCoupons;
    // 加购用户数
    private Integer userCart;
    //其他
    // 关注人数
    private Integer followUsers;
    // 复购率
    private String repurchaseRate;
}
