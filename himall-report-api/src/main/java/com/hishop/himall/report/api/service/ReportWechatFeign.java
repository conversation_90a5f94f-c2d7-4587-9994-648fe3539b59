package com.hishop.himall.report.api.service;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.UserReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.UserNewOldSummaryResp;
import com.hishop.himall.report.api.response.UserResp;
import com.hishop.himall.report.api.response.UserSummaryResp;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.util.model.ResultInfo;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-report", contextId = "ReportWechatFeign", path = "/himall-report/report/wechat", url = "${himall-report.dev.url:}")
public interface ReportWechatFeign {
    /**
     * 访问走势图
     *
     * @param req
     * @return
     */
    @PostMapping(value = "visits", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> visits(@RequestBody ReportReq req);

    /**
     * 用户画像饼图
     * @param req
     * @return
     */
    @PostMapping(value = "portrait", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> portrait(@RequestBody ReportReq req);
}
