package com.hishop.himall.report.api.request;

import com.hishop.himall.report.api.enums.Dimension;
import com.hishop.starter.util.model.AbstractPageUtilReq;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CustomPageReq extends AbstractPageUtilReq {
    private Long operationId;
    private Long shopId;
    /**
     * 名称
     */
    private String name;
    /**
     * 维度
     */
    private Dimension dimension;
    /**
     * 时间范围
     */
    private String range;
    /**
     * 是否自动刷新
     */
    private Boolean automatic;

}
