package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * ECharts图表系列数据类
 * 用于配置图表的系列数据
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class EchartSeries {

    /**
     * 系列名称
     */
    private String name;

    /**
     * 系列数据列表
     */
    private List<Comparable> data = new ArrayList<>();

    /**
     * 添加BigDecimal类型的数据
     *
     * @param value 数值，如果为null则添加0
     */
    public void add(BigDecimal value) {
        this.data.add(value == null ?  BigDecimal.ZERO : value);
    }

    /**
     * 添加Integer类型的数据
     *
     * @param value 数值，如果为null则添加0
     */
    public void add(Integer value){
        this.data.add(value == null ?  0 : value);
    }

    /**
     * 添加float类型的数据
     *
     * @param value 数值
     */
    public void add(float value){
        this.data.add(value);
    }

    /**
     * 添加饼图数据（包含名称和数值）
     *
     * @param name 数据项名称
     * @param value 数据项数值，如果为null则使用0
     */
    public void add(String name, BigDecimal value) {
        EchartSeriesPieData pie = new EchartSeriesPieData();
        pie.setName(name);
        pie.setValue(value == null ? BigDecimal.ZERO : value);
        this.data.add(pie);
    }

    /**
     * 对数据进行排序
     */
    public void sort() {
        this.data.sort(Comparator.naturalOrder());
    }
}