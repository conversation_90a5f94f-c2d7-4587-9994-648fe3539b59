package com.hishop.himall.report.api.enums;

import lombok.Getter;

@Getter
public enum RangeEnum {
    DAY(1, "日"),
    WEEK(2, "周"),
    MONTH(3, "月"),
    YEAR(4, "年");

    private int value;
    private String desc;

    RangeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static RangeEnum valueOf(int value) {
        for (RangeEnum enumValue : values()) {
            if (enumValue.value == value) {
                return enumValue;
            }
        }
        throw new IllegalArgumentException("No enum constant with value " + value);
    }
}
