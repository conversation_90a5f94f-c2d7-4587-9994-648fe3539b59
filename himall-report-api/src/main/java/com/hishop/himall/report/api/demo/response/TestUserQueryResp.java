package com.hishop.himall.report.api.demo.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class TestUserQueryResp {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 访问类型，1：商城，2：店铺，3：商品
     */
    private Short accessType;

    /**
     * 访问url
     */
    private String accessUrl;

    /**
     * 渠道来源（0：小程序，1：PC，2：H5，3：APP）
     */
    private Short platform;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人Id
     */
    private Long createUser;

    /**
     * 更新人Id
     */
    private Long updateUser;


    public BigDecimal getNum(){
        return BigDecimal.valueOf(1.11);
    }
}
