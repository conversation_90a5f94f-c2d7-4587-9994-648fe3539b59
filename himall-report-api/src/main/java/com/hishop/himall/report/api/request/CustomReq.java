package com.hishop.himall.report.api.request;

import com.hishop.himall.report.api.enums.Dimension;
import com.hishop.himall.report.api.enums.Platform;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 自定义报表请求类
 * 用于创建和配置自定义报表
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class CustomReq {

    /**
     * 报表ID（更新时使用）
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 平台类型（PC/H5/小程序/APP/全部）
     */
    private Platform platform;

    /**
     * 统计维度（平台/店铺/商品）
     */
    private Dimension dimension;

    /**
     * 分组字段列表
     */
    private List<Integer> groups;

    /**
     * 统计字段列表
     */
    private List<Integer> fields;

    /**
     * 商品ID列表（商品维度时使用）
     */
    private List<Integer> products;

    /**
     * 店铺ID列表（店铺维度时使用）
     */
    private List<Integer> shops;

    /**
     * 时间维度（DAY/WEEK/MONTH/YEAR）
     */
    private String range;

    /**
     * 是否自动更新
     */
    private Boolean automatic;

    /**
     * 自动更新间隔天数
     */
    private Integer times;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;
}
