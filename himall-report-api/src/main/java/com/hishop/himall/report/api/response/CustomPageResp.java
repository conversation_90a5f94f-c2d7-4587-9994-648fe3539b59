package com.hishop.himall.report.api.response;

import com.hishop.himall.report.api.enums.Platform;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 自定义报表分页响应类
 * 用于自定义报表列表的分页查询结果
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class CustomPageResp {

    /**
     * 报表ID
     */
    private Long id;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 统计维度（平台/店铺/商品）
     */
    private String dimension;

    /**
     * 时间维度（DAY/WEEK/MONTH/YEAR）
     */
    private String range;

    /**
     * 是否自动更新
     */
    private Boolean automatic;

    /**
     * 平台类型（0-全部，1-PC，2-H5，3-小程序，4-APP）
     */
    private int platform;

    /**
     * 更新时间
     */
    private Date updateTime;
}
