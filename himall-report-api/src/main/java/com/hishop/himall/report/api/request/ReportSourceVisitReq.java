package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
@Getter
@Setter
public class ReportSourceVisitReq {
    /**
     * 访客标识
     */
    private String visitor;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 访问页面
     */
    private String page;

    /**
     * 访问时间
     */
    private LocalDateTime createTime;
}
