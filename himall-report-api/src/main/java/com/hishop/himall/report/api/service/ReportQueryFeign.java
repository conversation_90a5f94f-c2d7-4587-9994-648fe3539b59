package com.hishop.himall.report.api.service;

import com.hishop.himall.report.api.request.ProductReq;
import com.hishop.himall.report.api.request.TradeReq;
import com.hishop.himall.report.api.request.UserReq;
import com.hishop.himall.report.api.response.ProductResp;
import com.hishop.himall.report.api.response.TradeResp;
import com.hishop.himall.report.api.response.UserResp;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-report", contextId = "ReportQueryFeign", path = "/himall-report/report", url = "${himall-report.dev.url:}")
public interface ReportQueryFeign {

    @PostMapping(value = "/product/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BasePageResp<ProductResp>> queryProducts(@RequestBody ProductReq req);

    @PostMapping(value = "/user/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BasePageResp<UserResp>> queryUsers(@RequestBody UserReq req);

    @PostMapping(value = "/trade/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BasePageResp<TradeResp>> queryTrade(@RequestBody TradeReq req);

}
