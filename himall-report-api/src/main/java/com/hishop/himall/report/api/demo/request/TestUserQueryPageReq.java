package com.hishop.himall.report.api.demo.request;

import com.hishop.starter.util.model.AbstractPageUtilReq;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class TestUserQueryPageReq extends AbstractPageUtilReq {
    /**
     * 数据id
     */
    @NotNull(message = "{100010}")
    private Long id;
    /**
     * 姓名
     */
    private String name;
}
