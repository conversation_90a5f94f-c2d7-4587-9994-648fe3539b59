package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 店铺报表响应类
 * 包含店铺的核心统计指标数据
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class ShopReportResp {

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付订单数
     */
    private Integer paymentOrders;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款订单数
     */
    private Integer refundOrders;

    /**
     * 访问人数
     */
    private Integer visitsUsers;

    /**
     * 访问次数
     */
    private Integer visitsCount;
}
