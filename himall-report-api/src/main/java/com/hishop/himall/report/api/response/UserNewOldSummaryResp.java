package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 用户新老客户概览统计响应类
 * 包含新老客户的对比分析数据
 * 提供新老客户的支付金额、订单数、客单价、占比等指标
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class UserNewOldSummaryResp {
    /**
     * 支付用户数
     */
    private Integer paymentUsers;
    /**
     * 支付订单数
     */
    private Integer paymentOrders;
    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 客单价
     */
    private BigDecimal unitPrice;
    /**
     * 新客支付用户数
     */
    private Integer paymentNewUsers;
    /**
     * 新客支付订单数
     */
    private Integer paymentNewOrders;
    /**
     * 新客支付金额
     */
    private BigDecimal paymentNewAmount;
    /**
     * 新客客单价
     */
    private BigDecimal newUnitPrice;
    /**
     * 新客占比
     */
    private BigDecimal newProportion;
    /**
     * 老客支付用户数
     */
    private Integer paymentOldUsers;
    /**
     * 老客支付订单数
     */
    private Integer paymentOldOrders;
    /**
     * 老客支付金额
     */
    private BigDecimal paymentOldAmount;
    /**
     * 老客客单价
     */
    private BigDecimal oldUnitPrice;
    /**
     * 老客占比
     */
    private BigDecimal oldProportion;
}
