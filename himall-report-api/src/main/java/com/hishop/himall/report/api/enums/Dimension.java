package com.hishop.himall.report.api.enums;

import lombok.Getter;

@Getter
public enum Dimension {
    PLATFORM(1, "平台"),
    SHOP(2, "店铺"),
    PRODUCT(3, "商品");

    private int value;
    private String desc;

    Dimension(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static  Dimension getDimension(int value) {
        for (Dimension dimension : Dimension.values()) {
            if (dimension.getValue() == value) {
                return dimension;
            }
        }
        return null;
    }
}
