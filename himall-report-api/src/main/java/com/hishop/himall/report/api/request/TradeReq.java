package com.hishop.himall.report.api.request;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.himall.report.api.enums.RangeEnum;
import com.hishop.starter.util.model.AbstractPageUtilReq;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 交易报表查询请求类
 * 用于交易分析相关的查询请求，支持分页查询
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class TradeReq extends AbstractPageUtilReq
{
    /**
     * 时间维度（日/周/月/年）
     */
    private RangeEnum range;

    /**
     * 查询开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date start;

    /**
     * 查询结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date end;

    /**
     * 店铺ID，为空时查询全部店铺
     */
    private Long shopId;

    /**
     * 获取结束日期
     * 如果end为null，则根据start时间计算当月最后一天
     *
     * @return 结束日期
     */
    public Date getEndDate() {
        if (end == null) {
            return null;
        }
        return DateUtil.endOfMonth(start);
    }
}
