package com.hishop.himall.report.api.service;

import com.hishop.himall.report.api.request.CustomPageReq;
import com.hishop.himall.report.api.request.CustomProcessReq;
import com.hishop.himall.report.api.request.CustomRecordPageReq;
import com.hishop.himall.report.api.request.CustomReq;
import com.hishop.himall.report.api.request.ReportQueryReq;
import com.hishop.himall.report.api.response.CustomPageResp;
import com.hishop.himall.report.api.response.CustomProcessResp;
import com.hishop.himall.report.api.response.CustomRecordPageResp;
import com.hishop.himall.report.api.response.CustomResp;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.util.model.ResultInfo;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "himall-report", contextId = "ReportCustomFeign", path = "/himall-report/report/custom", url = "${himall-report.dev.url:}")
public interface ReportCustomFeign {

    @PostMapping(value = "/process", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<List<CustomProcessResp>> process(@RequestBody @Valid CustomProcessReq req);

    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Long> export(@RequestBody ReportQueryReq req);

    @GetMapping(value = "/getByRecordId")
    ResultDto<CustomResp> getByRecordId(@RequestParam("id") Long id);

    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BasePageResp<CustomPageResp>> query(@RequestBody CustomPageReq req);

    @PostMapping(value = "/record/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BasePageResp<CustomRecordPageResp>> query(@RequestBody CustomRecordPageReq req);

    @PostMapping(value = "/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Object> save(@RequestBody CustomReq req);

    @DeleteMapping(value = "/delete/{id}")
    ResultDto<Object> delete(@PathVariable Long id);

    @GetMapping(value = "/get/{id}")
    ResultDto<CustomResp> get(@PathVariable Long id);
}
