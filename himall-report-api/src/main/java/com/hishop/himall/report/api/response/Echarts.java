package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * ECharts图表数据响应类
 * 用于封装前端ECharts图表所需的数据结构
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class Echarts {

    /**
     * X轴数据配置
     */
    private EchartXAxis xAxis=new EchartXAxis();

    /**
     * 图表系列数据列表
     */
    private List<Object> series=new ArrayList<>();

    /**
     * 添加X轴数据点
     *
     * @param name X轴标签名称
     */
    public void addXAxis(String name){
        this.xAxis.getData().add(name);
    }

    /**
     * 添加图表系列数据
     *
     * @param name 系列名称
     * @return 创建的系列对象，可用于进一步配置
     */
    public EchartSeries addSeries(String name){
        EchartSeries series = new EchartSeries();
        series.setName(name);
        this.series.add(series);
        return series;
    }

    /**
     * 根据时间范围和维度遍历日期，生成X轴数据
     *
     * @param range 时间维度（DAY/WEEK/MONTH）
     * @param start 开始日期
     * @param end 结束日期
     * @param fun 每个日期的处理函数
     * @return 当前Echarts对象，支持链式调用
     */
    public Echarts each(String range, LocalDate start, LocalDate end, Consumer<LocalDate> fun) {
        switch (range) {
            case "DAY":
                while (start.isBefore(end)) {
                    this.addXAxis(start.toString());
                    fun.accept(start);
                    start = start.plusDays(1);
                }
                break;
            case "WEEK":
                while (start.isBefore(end)) {
                    this.addXAxis(start.toString());
                    fun.accept(start);
                    start = start.plusWeeks(1);
                }
                break;
            case "MONTH":
                while (start.isBefore(end)) {
                    this.addXAxis(start.toString());
                    fun.accept(start);
                    start = start.plusMonths(1);
                }
                break;
        }
        return this;
    }

}






