package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 交易概览统计响应类
 * 包含交易相关的核心统计指标和转化率数据
 * 支持同比分析（包含上期数据）
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class TradeSummaryResp
{
    /**
     * 订单量
     */
    private Integer visitsCount;
    /**
     * 访客数
     */
    private Integer visitsUsers;

    /**
     * 下单量
     */
    private BigDecimal orderAmount;
    /**
     * 下单用户数
     */
    private Integer orderOrders;

    /**
     * 支付量
     */
    private Integer orderUsers;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;
    /**
     * 支付件数
     */
    private Integer paymentQuantity;
    /**
     * 支付订单数
     */
    private Integer paymentOrders;
    /**
     * 支付用户数
     */
    private Integer paymentUsers;
    /**
     * 客单价
     */
    private BigDecimal unitPrice;
    /**
     * 订单-访客转化率
     */
    public BigDecimal orderVisitsRate;
    /**
     * 支付-下单转化率
     */
    public BigDecimal paymentOrderRate;
    /**
     * 支付-访客转化率
     */
    public BigDecimal paymentVisitsRate;
    /**
     * 同比上一期
     */
    private TradeSummaryResp prev;
}
