package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 用户概览统计响应类
 * 包含用户相关的核心统计指标
 * 支持同比分析（包含上期数据）
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class UserSummaryResp {

    /**
     * 用户总数
     */
    private Integer userTotal;

    /**
     * 新增用户数
     */
    private Integer userNew;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付用户数
     */
    private Integer paymentUsers;

    /**
     * 下单用户数
     */
    private Integer orderUsers;

    /**
     * 领券用户数
     */
    private Integer couponUsers;

    /**
     * 客单价
     */
    private BigDecimal unitPrice;

    /**
     * 上期数据（用于同比分析）
     */
    private UserSummaryResp prev;
}
