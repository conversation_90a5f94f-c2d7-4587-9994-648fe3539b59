package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 基础数据源-关注记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
public class ReportSourceFollowShopReq {


    private Long id;
    /**
     * 关注会员
     */
    private Long userId;

    /**
     * 关注门店
     */
    private Long shopId;

    /**
     * 关注时间
     */
    private Date followTime;

    private Date createTime;
}
