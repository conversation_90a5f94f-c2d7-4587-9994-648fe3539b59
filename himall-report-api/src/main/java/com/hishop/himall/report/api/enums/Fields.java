package com.hishop.himall.report.api.enums;

import lombok.Getter;

@Getter
public enum Fields {
    ORDER_ORDERS(11, "下单笔数","orderOrders"),
    ORDER_USERS(12,"下单人数","orderUsers"),
    ORDER_AMOUNT(13,"下单金额","orderAmount"),

    PAYMENT_ORDERS(21,"支付笔数","paymentOrders"),
    PAYMENT_USERS(22,"支付人数","paymentUsers"),
    PAYMENT_AMOUNT(23,"支付金额","paymentAmount"),
    PAYMENT_QUANTITY(24,"支付件数","paymentQuantity"),
    PAYMENT_UNIT_PRICE(25,"支付客单价","paymentUnitPrice"),
    PAYMENT_PRODUCT_AMOUNT(26,"支付商品金额","paymentProductAmount"),
    PAYMENT_PRODUCT_QUANTITY(27,"支付商品件数","paymentProductQuantity"),

    NEW_USER_PAYMENT_AMOUNT(31,"新客支付金额","newUserPaymentAmount"),
    NEW_USER_PAYMENT_ORDERS(32,"新增支付订单数","newUserPaymentOrders"),
    NEW_USER_PAYMENT(33,"新客支付人数","newUserPaymentUsers"),
    OLD_USER_PAYMENT_AMOUNT(34,"老客支付金额","oldUserPaymentAmount"),
    OLD_USER_PAYMENT_ORDERS(35,"老客支付订单数","oldUserPaymentOrders"),
    OLD_USER_PAYMENT(36,"老客支付人数","oldUserPaymentUsers"),

    CART_QUANTITY(41,"加购件数","cartQuantity"),
    CART_USER(42,"加购人数","cartUsers"),

    VISIT_USERS(51,"访客数","visitUsers"),
    VISIT_COUNT(52, "访问量","visitCount"),
    VISIT_SHOP_USERS(53,"店铺访客数","visitShopUsers"),
    VISIT_SHOP_COUNT(54,"店铺访问量","visitShopCount"),
    VISIT_PRODUCT_USERS(55,"商品访客数","visitProductUsers"),
    VISIT_PRODUCT_COUNT(56,"商品访问量","visitProductCount"),

    RATE_VISIT_ORDER(61,"访问下单转化率","visitOrderRate"),
    RATE_VISIT_PAYMENT(62,"访问支付转化率","visitPaymentRate"),
    RATE_ORDER_PAYMENT(63,"下单支付转化率","orderPaymentRate"),

    REFUND_AMOUNT(71,"退款金额","refundAmount"),
    REFUND_ORDERS(72,"退款订单数","refundOrders"),
    REFUND_QUANTITY(73,"退款件数","refundQuantity"),
    REFUND_USER(74,"退款人数","refundUsers"),
    REFUND_RATE(75,"退款率","refundRate"),

    PACKAGE_DELIVERY(81,"包裹发货数","packageDelivery"),
    PACKAGE_FINISH(82,"包裹完成数","packageFinish"),
    PACKAGE_DELIVERY_HOUR(83,"发货时长(小时)","packageDeliveryHour"),
    PACKAGE_FINISH_HOUR(84,"完成时长(小时)","packageFinishHour"),

    USER_TOTAL(91,"用户总数","userTotal"),
    USER_NEW(92,"新增用户数","userNew"),
    USER_PAYMENT(93,"支付用户数","userPayment"),
    USER_COUPONS(94,"领券用户数","userCoupon"),
    USER_CART(95,"加购用户数","userCart"),

    FOLLOW_USERS(101,"关注人数","followUsers"),
    REPURCHASE_RATE(102,"复购率","repurchaseRate");

    private final int value;
    private final String desc;
    private final String fieldName;

    Fields(int value, String desc,String fieldName) {
        this.value = value;
        this.desc = desc;
        this.fieldName = fieldName;
    }
    public static Fields valueOf(int value) {
        for (Fields enumValue : values()) {
            if (enumValue.value == value) {
                return enumValue;
            }
        }
        throw new IllegalArgumentException("No enum constant with value " + value);
    }
}
