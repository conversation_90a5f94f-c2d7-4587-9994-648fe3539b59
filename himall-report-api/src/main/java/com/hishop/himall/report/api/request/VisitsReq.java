package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class VisitsReq {

    /**
     * 访问者
     */
    private String visitor;

    /**
     * 访问记录明细
     */
    private List<Item> Items;


    @Getter
    @Setter
    public static class Item
    {
        /**
         * 访问时间
         */
        private String time;

        /**
         * 访问页面
         */
        private String page;
        /**
         * 访问商品
         */
        private Long productId;
        /**
         * 访问门店
         */
        private Long shopId;
    }

}
