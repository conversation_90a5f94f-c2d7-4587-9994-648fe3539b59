package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * 商品概览统计响应类
 * 包含商品相关的核心统计指标
 * 支持同比分析（包含上期数据）
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class ProductSummaryResp {
    /**
     * 访问商品数
     */
    private Integer visitsProducts;
    /**
     * 销售商品数
     */
    private Integer salesProducts;
    /**
     * 访问量
     */
    private Integer visitsCount;

    /**
     * 访问人数
     */
    private Integer VisitsUsers;
    /**
     * 加购件数
     */
    private Integer cartQuantity;
    /**
     * 下单件数
     */
    private Integer orderQuantity;
    /**
     * 支付件数
     */
    private Integer paymentQuantity;

    /**
     * 上期
     */
    private ProductSummaryResp prev;


}

