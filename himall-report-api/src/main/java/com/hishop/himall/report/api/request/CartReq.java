package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 购物车操作请求类
 * 用于记录用户的加购行为数据
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class CartReq
{
    /**
     * 购物车记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 加购数量
     */
    private Integer count;

    /**
     * 加购时间
     */
    private Date createTime;
}
