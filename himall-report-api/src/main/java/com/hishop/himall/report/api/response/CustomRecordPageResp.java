package com.hishop.himall.report.api.response;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

/**
 * 自定义报表记录分页响应类
 * 用于自定义报表执行记录的分页查询结果
 * 包含报表的执行状态、文件路径、操作人等信息
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class CustomRecordPageResp {
    /**
     * id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 自定义报表id
     */
    private Long customId;
    /**
     * 统计维度
     */
    private int dimension;
    /**
     * 渠道
     */
    private int platform;
    /**
     * 自动更新
     */
    private Boolean automatic;
    /**
     * 时间维度
     */
    private String range;
    /**
     * 开始时间
     */
    private LocalDate startDate;
    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 文件地址
     */
    private String path;
    /**
     * 状态
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operatorName;
    /**
     * 失败原因
     */
    private String failureReason;
}
