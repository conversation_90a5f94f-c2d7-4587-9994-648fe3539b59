package com.hishop.himall.report.api.service;

import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.TradeReq;
import com.hishop.himall.report.api.request.UserReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.TradeResp;
import com.hishop.himall.report.api.response.TradeSummaryResp;
import com.hishop.himall.report.api.response.UserNewOldSummaryResp;
import com.hishop.himall.report.api.response.UserResp;
import com.hishop.himall.report.api.response.UserSummaryResp;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.util.model.ResultInfo;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-report", contextId = "ReportUserFeign", path = "/himall-report/report/user", url = "${himall-report.dev.url:}")
public interface ReportUserFeign {
    /**
     * 交易概览
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<UserSummaryResp> queryProductSummary(@RequestBody ReportReq req);

    /**
     * 用户新增趋势
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/increase/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> queryUserIncrease(@RequestBody ReportReq req);

    /**
     * 用户省份分布
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/province/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> queryProvince(@RequestBody ReportReq req);

    /**
     * 新老会员概览
     * @param req
     * @return
     */
    @PostMapping(value = "/newOld/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<UserNewOldSummaryResp> queryNewOldSummary(@RequestBody ReportReq req);

    /**
     * 新老会员走势图
     * @param req
     * @return
     */
    @PostMapping(value = "/newOld/echarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> queryNewOldEcharts(@RequestBody ReportReq req);

    /**
     * 会员分析列表
     * @param req
     * @return
     */
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<PageResult<UserResp>> queryProducts(@RequestBody UserReq req);

    /**
     * 会员分析导出
     * @param req
     * @return
     */
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> exportProduct(@RequestBody UserReq req);
}
