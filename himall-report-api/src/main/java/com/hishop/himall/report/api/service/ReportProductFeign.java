package com.hishop.himall.report.api.service;

import com.hishop.himall.report.api.request.ProductReq;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.ProductResp;
import com.hishop.himall.report.api.response.ProductSummaryResp;
import com.hishop.starter.util.model.PageResult;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-report", contextId = "ReportProductFeign", path = "/himall-report/report/product", url = "${himall-report.dev.url:}")
public interface ReportProductFeign {
    @PostMapping(value = "/summary", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<ProductSummaryResp> queryProductSummary(@RequestBody ReportReq req);

    /**
     * 商品走势图
     * @param req
     * @return
     */
    @PostMapping(value = "/visitsEcharts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> queryVisits(@RequestBody ReportReq req);

    /**
     * 分类饼图
     * @param req
     * @return
     */
    @PostMapping(value = "/categoryEcharts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<Echarts> queryCategory(@RequestBody ReportReq req);

    /**
     * 商品分析列表
     * @param req
     * @return
     */
    @PostMapping(value = "/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<PageResult<ProductResp>> queryProducts(@RequestBody ProductReq req);

    /**
     * 商品分析导出
     * @param req
     * @return
     */
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> exportProduct(@RequestBody ProductReq req);

}
