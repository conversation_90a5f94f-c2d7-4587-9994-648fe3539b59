package com.hishop.himall.report.api.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname BachReportSourceOrderReq
 * Description //TODO
 * @date 2024/12/5 11:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchReportSourceOrderReq implements Serializable {
    /**
     * 批量订单数据
     */
    @NotNull
    private List<ReportSourceOrderReq> list;
}
