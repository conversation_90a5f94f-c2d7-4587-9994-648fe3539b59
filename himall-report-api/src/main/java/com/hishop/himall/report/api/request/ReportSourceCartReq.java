package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 基础数据源表-加购数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-17
 */

@Getter
@Setter
public class ReportSourceCartReq {

    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 来源平台
     */
    private Integer platform;

    private LocalDateTime createTime;

}
