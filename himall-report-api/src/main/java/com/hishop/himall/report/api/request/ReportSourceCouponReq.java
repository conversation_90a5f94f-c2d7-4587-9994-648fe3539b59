package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
public class ReportSourceCouponReq {


    private Long id;
    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 优惠券记录
     */
    private Long recordId;

    /**
     * 领取时间
     */
    private Date receiveTime;

    private LocalDateTime createTime;
}
