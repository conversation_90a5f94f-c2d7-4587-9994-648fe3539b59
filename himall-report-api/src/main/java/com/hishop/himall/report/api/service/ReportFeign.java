package com.hishop.himall.report.api.service;

import com.hishop.himall.report.api.request.BatchReportSourceCartReq;
import com.hishop.himall.report.api.request.BatchReportSourceCouponReq;
import com.hishop.himall.report.api.request.BatchReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.BatchReportSourceFollowShopReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderBillReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderItemReq;
import com.hishop.himall.report.api.request.BatchReportSourceOrderReq;
import com.hishop.himall.report.api.request.BatchReportSourceProductReq;
import com.hishop.himall.report.api.request.BatchReportSourceRefundReq;
import com.hishop.himall.report.api.request.BatchReportSourceRegionReq;
import com.hishop.himall.report.api.request.BatchReportSourceShopReq;
import com.hishop.himall.report.api.request.BatchReportSourceUserReq;
import com.hishop.himall.report.api.request.BatchReportSourceVisitReq;
import com.hishop.himall.report.api.request.ReportSourceCartReq;
import com.hishop.himall.report.api.request.ReportSourceCouponReq;
import com.hishop.himall.report.api.request.ReportSourceFollowProductReq;
import com.hishop.himall.report.api.request.ReportSourceFollowShopReq;
import com.hishop.himall.report.api.request.ReportSourceOrderBillReq;
import com.hishop.himall.report.api.request.ReportSourceOrderItemReq;
import com.hishop.himall.report.api.request.ReportSourceOrderReq;
import com.hishop.himall.report.api.request.ReportSourceProductReq;
import com.hishop.himall.report.api.request.ReportSourceRefundReq;
import com.hishop.himall.report.api.request.ReportSourceRegionReq;
import com.hishop.himall.report.api.request.ReportSourceShopReq;
import com.hishop.himall.report.api.request.ReportSourceUserReq;
import com.hishop.himall.report.api.request.ReportSourceVisitReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-report", contextId = "ReportFeign", path = "/himall-report/report", url = "${himall-report.dev.url:}")
public interface ReportFeign {

    @PostMapping(value = "createSourceCart", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceCart(@RequestBody ReportSourceCartReq req);

    @PostMapping(value = "createSourceCarts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceCart(@RequestBody BatchReportSourceCartReq req);

    @PostMapping(value = "createSourceVisits", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceVisits(@RequestBody ReportSourceVisitReq req);

    @PostMapping(value = "batchCreateSourceVisits", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceVisits(@RequestBody BatchReportSourceVisitReq req);

    @PostMapping(value = "createSourceCoupon", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceCoupon(@RequestBody ReportSourceCouponReq req);

    @PostMapping(value = "createSourceCoupons", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceCoupon(@RequestBody BatchReportSourceCouponReq req);

    @PostMapping(value = "createSourceFollowProduct", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceFollowProduct(@RequestBody ReportSourceFollowProductReq req);

    @PostMapping(value = "createSourceFollowProducts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceFollowProduct(@RequestBody BatchReportSourceFollowProductReq req);

    @PostMapping(value = "createSourceFollowShop", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceFollowShop(@RequestBody ReportSourceFollowShopReq req);

    @PostMapping(value = "createSourceFollowShops", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceFollowShop(@RequestBody BatchReportSourceFollowShopReq req);

    @PostMapping(value = "createSourceOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceOrder(@RequestBody ReportSourceOrderReq req);

    @PostMapping(value = "createSourceOrders", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceOrder(@RequestBody BatchReportSourceOrderReq req);

    @PostMapping(value = "createSourceOrderItem", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceOrderItem(@RequestBody ReportSourceOrderItemReq req);

    @PostMapping(value = "createSourceOrderItems", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceOrderItem(@RequestBody BatchReportSourceOrderItemReq req);

    @PostMapping(value = "createSourceProduct", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceProduct(@RequestBody ReportSourceProductReq req);

    @PostMapping(value = "createSourceProducts", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceProduct(@RequestBody BatchReportSourceProductReq req);

    @PostMapping(value = "createSourceRefund", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceRefund(@RequestBody ReportSourceRefundReq req);

    @PostMapping(value = "createSourceRefunds", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceRefund(@RequestBody BatchReportSourceRefundReq req);

    @PostMapping(value = "createSourceShop", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceShop(@RequestBody ReportSourceShopReq req);

    @PostMapping(value = "createSourceShops", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceShop(@RequestBody BatchReportSourceShopReq req);

    @PostMapping(value = "createSourceUser", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceUser(@RequestBody ReportSourceUserReq req);

    @PostMapping(value = "createSourceUsers", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceUser(@RequestBody BatchReportSourceUserReq req);

    @PostMapping(value = "createSourceRegion", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceRegion(@RequestBody ReportSourceRegionReq req);

    @PostMapping(value = "createSourceRegions", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceRegion(@RequestBody BatchReportSourceRegionReq req);

    @PostMapping(value = "createSourceOrderBill", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> createSourceOrderBill(@RequestBody ReportSourceOrderBillReq req);

    @PostMapping(value = "createSourceOrderBills", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResultDto<BaseResp> batchCreateSourceOrderBill(@RequestBody BatchReportSourceOrderBillReq req);
}
