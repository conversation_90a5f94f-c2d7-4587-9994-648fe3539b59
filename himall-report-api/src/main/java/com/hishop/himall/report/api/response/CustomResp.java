package com.hishop.himall.report.api.response;

import com.hishop.himall.report.api.enums.Dimension;
import com.hishop.himall.report.api.enums.Platform;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 自定义报表响应类
 * 用于返回自定义报表的配置信息
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Setter
@Getter
public class CustomResp {

    /**
     * 报表ID
     */
    private Long id;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 平台类型（PC/H5/小程序/APP/全部）
     */
    private Platform platform;

    /**
     * 统计维度（平台/店铺/商品）
     */
    private Dimension dimension;

    /**
     * 分组字段列表
     */
    private List<String> groups;

    /**
     * 统计字段列表
     */
    private List<String> fields;

    /**
     * 店铺ID列表
     */
    private List<Long> shops;

    /**
     * 商品ID列表
     */
    private List<Long> products;

    /**
     * 时间维度（DAY/WEEK/MONTH/YEAR）
     */
    private String range;

    /**
     * 是否自动更新
     */
    private Boolean automatic;

    /**
     * 自动更新间隔天数
     */
    private Integer times;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;
}
