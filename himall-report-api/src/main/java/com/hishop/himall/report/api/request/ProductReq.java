package com.hishop.himall.report.api.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.himall.report.api.enums.RangeEnum;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 商品报表查询请求类
 * 用于商品分析相关的查询请求，支持分页查询
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class ProductReq extends BasePageReq {

    /**
     * 店铺ID，为空时默认为0（全部店铺）
     */
    private Long shopId;

    /**
     * 时间维度（日/周/月/年）
     */
    private RangeEnum range;

    /**
     * 查询开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date start;

    /**
     * 查询结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date end;

    /**
     * 商品分类名称（用于筛选）
     */
    private String categoryName;

    /**
     * 商品名称（用于筛选）
     */
    private String productName;

    /**
     * 获取店铺ID，如果为空则返回0（表示全部店铺）
     *
     * @return 店铺ID，0表示全部店铺
     */
    public Long getShopId() {
        if(shopId==null)
            return 0L;
        return shopId;
    }
}
