package com.hishop.himall.report.api.enums;

public enum Platform {
    /**
     * 全部
     */
    ALL(0, "全部"),
    /**
     * PC
     */
    PC(1, "PC"),
    /**
     * H5
     */
    H5(2, "H5"),
    /**
     * 小程序
     */
    MINI_PROGRAM(3, "小程序"),
    /**
     * APP
     */
    APP(4, "APP"),;

    private final int value;
    private final String desc;

    Platform(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }
    public String getDesc() {
        return desc;
    }
    public static Platform getPlatform(int value) {
        for (Platform item : Platform.values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

}
