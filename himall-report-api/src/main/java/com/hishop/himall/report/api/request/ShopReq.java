package com.hishop.himall.report.api.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.himall.report.api.enums.RangeEnum;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ShopReq extends BasePageReq {
    private RangeEnum range;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date start;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date end;
    private Long shopId;
    private String shopName;
}


