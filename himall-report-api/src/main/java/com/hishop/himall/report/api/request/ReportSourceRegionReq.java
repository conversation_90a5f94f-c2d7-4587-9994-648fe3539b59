package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
public class ReportSourceRegionReq {

    private Long id;

    private String name;

    private Long parentId;

    private Integer level;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
