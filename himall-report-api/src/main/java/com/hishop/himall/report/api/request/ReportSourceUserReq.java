package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
public class ReportSourceUserReq {
    private Long userId;

    private String nickname;

    private String phone;
    /**
     * 所属省份
     */
    private Integer provinceId;

    /**
     * 注册时间
     */
    private Date registrationTime;

    /**
     * 首次消费时间
     */
    private Date firstPaymentTime;

    /**
     * 注销用户
     */
    private boolean whetherLogOut;
    /**
     * 创建时间
     */

    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
