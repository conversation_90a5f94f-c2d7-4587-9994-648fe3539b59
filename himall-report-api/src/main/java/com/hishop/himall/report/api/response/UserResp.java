package com.hishop.himall.report.api.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户报表响应类
 * 包含用户的各项统计指标数据
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
public class UserResp
{
    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 注册时间
     */
    private Date registrationTime;

    /**
     * 下单笔数
     */
    private Integer orderOrders;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付订单数
     */
    private Integer paymentOrders;

    /**
     * 支付件数
     */
    private Integer paymentQuantity;

    /**
     * 支付订单金额
     */
    private BigDecimal paymentOrderAmount;

    /**
     * 支付商品金额
     */
    private BigDecimal paymentProductAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款订单数
     */
    private Integer refundOrders;

    /**
     * 消费金额
     */
    public BigDecimal consumptionAmount;
}
