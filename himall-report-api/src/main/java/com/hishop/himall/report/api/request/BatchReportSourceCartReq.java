package com.hishop.himall.report.api.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 批量购物车数据源请求类
 * 用于批量提交购物车行为数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchReportSourceCartReq implements Serializable {

    /**
     * 购物车数据源请求列表
     */
    @NotNull
    private List<ReportSourceCartReq> list;
}
