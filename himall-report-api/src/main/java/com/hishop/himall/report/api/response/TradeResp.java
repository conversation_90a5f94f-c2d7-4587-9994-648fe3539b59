package com.hishop.himall.report.api.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;

/**
 * 交易报表响应类
 * 包含交易相关的统计指标数据
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Getter
@Setter
public class TradeResp {

    /**
     * 统计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 下单笔数
     */
    private Integer orderOrders;

    /**
     * 下单人数
     */
    private Integer orderUsers;

    /**
     * 支付订单数
     */
    private Integer paymentOrders;

    /**
     * 支付人数
     */
    private Integer paymentUsers;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * @return 支付转化率
     */
    public BigDecimal getPaymentRate() {
        if (orderUsers == null || orderUsers == 0)
            return BigDecimal.ZERO;
        return BigDecimal.valueOf(paymentUsers).divide(BigDecimal.valueOf(orderUsers), 4, RoundingMode.HALF_UP);
    }

}
