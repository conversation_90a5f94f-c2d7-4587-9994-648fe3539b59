package com.hishop.himall.report.api.request;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Getter
@Setter
public class ReportSourceOrderItemReq {

    private Long orderItemId;

    private String orderId;

    private Long userId;

    private Integer platform;

    private Long shopId;

    private Long productId;
    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 优惠金额
     */
    private BigDecimal discount;

    /**
     * 商品金额
     */
    private BigDecimal amount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
