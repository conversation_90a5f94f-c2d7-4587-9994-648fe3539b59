# Himall-Trade API接口清单文档

## 文档说明

本文档详细列出了himall-trade项目中所有对外提供的API接口，包括接口路径、请求方法、功能描述、请求参数和响应结果。

## 接口概览

himall-trade项目基于Spring Cloud OpenFeign提供微服务接口，主要分为以下几个模块：
- 商品相关接口
- 购物车相关接口  
- 订单相关接口
- 营销相关接口
- 品牌类目相关接口
- 其他辅助接口

## 1. 商品相关接口

### 1.1 TradeProductQueryFeign - 交易商品查询服务

**服务路径**: `/himall-trade/tradeProduct`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/search` | POST | 商品搜索列表 | SearchTradeProductReq | SearchTradeProductResp |
| `/searchInShop` | POST | 店铺内商品搜索 | SearchProductInShopReq | BasePageResp<TradeProductDto> |
| `/queryProductDetail` | POST | 查询商品详情 | ProductDetailReq | ProductDetailResp |
| `/calculateFreight` | POST | 计算运费 | CalculateFreightReq | CalculateFreightResp |
| `/updateProductVisitCount` | POST | 更新商品访问量 | ProductVisitReq | BaseResp |
| `/updateProductCollectionCount` | POST | 更新商品收藏数 | ProductCollectionReq | BaseResp |

### 1.2 ProductQueryFeign - 商品查询服务

**服务路径**: `/himall-trade/product`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/queryProductByTemplateId` | POST | 根据模板ID查询商品 | ProductTemplateReq | Boolean |
| `/queryProductCountByTemplateId` | POST | 查询模板关联商品数 | CountProductTemplateReq | List<CountProductTemplateResp> |
| `/queryProductDetail` | POST | 查询商品详情 | ProductQueryDetailReq | ProductDetailResp |
| `/queryProductSkuMerge` | POST | 查询SKU维度商品集合 | ProductSkuQueryReq | ProductSkuMergeQueryResp |
| `/queryProductForEsBuild` | POST | 查询商品信息(ES构建) | QueryProductEsReq | ProductEsResp |
| `/queryProductBasic` | POST | 查询商品基本信息 | QueryProductBasicReq | ProductBasicResp |
| `/pageProductBasic` | POST | 分页查询商品基本信息 | QueryProductBasicReq | BasePageResp<ProductBasicDto> |
| `/generateProductCode` | GET | 生成商品货号 | - | GenProductCodeResp |
| `/querySellerStatisticalProduct` | GET | 查询供应商统计商品信息 | shopId | StatisticalProductResp |

### 1.3 ProductCmdFeign - 商品操作服务

**服务路径**: `/himall-trade/product`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/createProduct` | POST | 发布商品 | SaveProductReq | BaseResp |
| `/updateProduct` | POST | 编辑商品 | SaveProductReq | BaseResp |
| `/deleteProduct` | POST | 删除商品 | DeleteProductReq | BaseResp |
| `/auditProduct` | POST | 审核商品 | AuditProductReq | BaseResp |
| `/batchAuditProduct` | POST | 批量审核商品 | BatchAuditProductReq | BaseResp |
| `/offSaleProduct` | POST | 下架商品 | OffSaleProductReq | BaseResp |
| `/onSaleProduct` | POST | 上架商品 | OnSaleProductReq | BaseResp |
| `/importStock` | POST | 导入库存 | ProductImportReq | BaseImportResp |
| `/importPrice` | POST | 导入价格 | ProductImportReq | BaseImportResp |
| `/addSales` | POST | 更新商品销量 | AddSaleCountReq | BaseResp |

## 2. 购物车相关接口

### 2.1 ShoppingCartQueryFeign - 购物车查询服务

**服务路径**: `/himall-trade/shoppingCart`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/getUserShoppingCartList` | GET | 获取用户购物车列表 | userId | UserShoppingCartResp |
| `/getUserShoppingCartCount` | GET | 获取购物车商品数量 | userId | Integer |
| `/previewOrder` | POST | 预览订单 | PreviewOrderReq | PreviewOrderResp |
| `/previewFlashSaleOrder` | POST | 限时购预览订单 | PreviewFlashSaleOrderReq | PreviewOrderResp |
| `/previewCollocationOrder` | POST | 组合购预览订单 | PreviewCollocationOrderReq | PreviewOrderResp |
| `/previewBuyNowOrder` | POST | 立即购买预览订单 | PreviewBuyNowReq | PreviewOrderResp |
| `/checkCanSubmit` | POST | 校验是否可提交 | PreviewOrderReq | BaseResp |

### 2.2 ShoppingCartCmdFeign - 购物车操作服务

**服务路径**: `/himall-trade/shoppingCart`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/addShoppingCart` | POST | 添加商品到购物车 | AddShoppingCartReq | BaseResp |
| `/addShoppingCartBatch` | POST | 批量添加到购物车 | AddShoppingCartBatchReq | BaseResp |
| `/deleteShoppingCart` | POST | 删除购物车商品 | DeleteShoppingCartSkuReq | BaseResp |
| `/clearInvalid` | POST | 清除失效商品 | ClearInvalidShoppingCartSkuReq | BaseResp |
| `/changeShoppingCartSkuCnt` | POST | 修改商品数量 | ChangeShoppingCartQuantityReq | ShopProductResp |
| `/selectShopSku` | POST | 选中商品SKU | SelectShoppingCartSkuReq | ShopProductResp |
| `/selectShop` | POST | 选中店铺 | SelectShopReq | ShopProductResp |
| `/selectAll` | POST | 全选购物车 | SelectAllReq | UserShoppingCartResp |
| `/addFromAddon` | POST | 凑单页面加购 | AddFromAddonReq | AddFromAddonResp |

## 3. 订单相关接口

### 3.1 PreOrderQueryFeign - 预订单查询服务

**服务路径**: `/himall-trade/preOrder`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/getSubmitToken` | GET | 获取提交令牌 | userId | String |
| `/previewErpOrder` | POST | ERP预览订单 | SubmitOrderReq | PreviewOrderResp |

### 3.2 PreOrderCmdFeign - 预订单操作服务

**服务路径**: `/himall-trade/preOrder`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/submitOrder` | POST | 提交订单 | SubmitOrderReq | PreviewOrderResp |
| `/submitErpOrder` | POST | 提交ERP订单 | SubmitOrderReq | PreviewOrderResp |
| `/chooseCoupon` | POST | 选择优惠券 | ChooseCouponReq | ChooseCouponResp |
| `/chooseShippingAddress` | POST | 选择收货地址 | ChooseShippingAddressReq | PreviewOrderResp |
| `/previewChangeSkuQuantity` | POST | 预览修改SKU数量 | PreviewChangeSkuQuantityReq | PreviewChangeSkuQuantityResp |

## 4. 营销相关接口

### 4.1 CouponQueryFeign - 优惠券查询服务

**服务路径**: `/himall-trade/coupon`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/pageList` | POST | 优惠券列表查询 | CouponQueryReq | BasePageResp<CouponSimpleResp> |
| `/getById` | POST | 根据ID查询优惠券 | BaseIdReq | CouponResp |
| `/flagHasCoupons` | GET | 检查店铺是否有优惠券 | shopId | Boolean |
| `/queryCouponProduct` | POST | 查询优惠券商品 | CouponProductQueryReq | BasePageResp<CouponProductDto> |
| `/queryUserCouponList` | POST | 查询用户优惠券列表 | UserCouponQueryReq | BasePageResp<UserCouponDto> |

### 4.2 CouponCmdFeign - 优惠券操作服务

**服务路径**: `/himall-trade/coupon`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/save` | POST | 保存优惠券 | CouponSaveReq | BaseResp |
| `/endActive` | POST | 结束活动 | BaseIdReq | BaseResp |
| `/receiveCoupon` | POST | 领取优惠券 | CouponReceiveReq | BaseResp |
| `/sendCoupon` | POST | 发放优惠券 | SendCouponCmdReq | BaseResp |

### 4.3 FullReductionQueryFeign - 满减查询服务

**服务路径**: `/himall-trade/fullReduction`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/pageList` | POST | 满减活动列表查询 | FullReductionQueryReq | BasePageResp<FullReductionSimpleDto> |
| `/getById` | POST | 根据ID查询满减活动 | BaseIdReq | FullReductionDto |
| `/queryByShopId` | POST | 根据店铺ID查询满减 | ShopIdReq | FullReductionResp |

### 4.4 FlashSaleQueryFeign - 限时购查询服务

**服务路径**: `/himall-trade/flashSale`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/pageList` | POST | 限时购活动列表查询 | FlashSaleQueryReq | BasePageResp<FlashSaleSimpleResp> |
| `/getById` | POST | 根据ID查询限时购活动 | BaseIdReq | FlashSaleResp |
| `/queryFlashSaleProduct` | POST | 查询限时购商品 | FlashSaleProductQueryReq | BasePageResp<FlashSaleProductDto> |

## 5. 品牌类目相关接口

### 5.1 BrandQueryFeign - 品牌查询服务

**服务路径**: `/himall-trade/brand`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/queryBrandForPage` | POST | 分页查询品牌 | QueryBrandReq | BasePageResp<BrandDto> |
| `/queryBrandDetail` | POST | 查询品牌详情 | BaseIdReq | BrandDto |
| `/queryBrandForList` | POST | 查询品牌列表 | QueryBrandReq | List<BrandDto> |

### 5.2 CategoryQueryFeign - 类目查询服务

**服务路径**: `/himall-trade/category`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/queryCategoryTree` | POST | 查询类目树 | QueryCategoryReq | CategoryTreeResp |
| `/queryCategoryDetail` | POST | 查询类目详情 | BaseIdReq | CategoryDetailResp |
| `/queryCategoryByIds` | POST | 根据ID列表查询类目 | QueryCategoryByIdsReq | List<CategoryBo> |

### 5.3 ShopBrandQueryFeign - 商家品牌查询服务

**服务路径**: `/himall-trade/shopBrand`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/queryShopBrandForPage` | POST | 分页查询商家品牌 | QueryShopBrandReq | BasePageResp<ShopBrandDto> |
| `/queryShopBrandForList` | POST | 查询商家品牌列表 | QueryShopBrandReq | ShopBrandListResp |

## 6. 其他辅助接口

### 6.1 SkuQueryFeign - SKU查询服务

**服务路径**: `/himall-trade/sku`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/querySkuList` | POST | 查询SKU规格数据 | SkuQueryReq | SkuListResp |

### 6.2 SpecificationFeign - 规格查询服务

**服务路径**: `/himall-trade/specification`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/query` | POST | 查询规格 | SpecificationReq | BasePageResp<SpecificationResp> |
| `/create` | POST | 创建规格 | SpecificationReq | BaseResp |
| `/save` | POST | 保存规格 | SpecificationReq | BaseResp |

### 6.3 ShopQueryFeign - 店铺查询服务

**服务路径**: `/himall-trade/shopQuery`

| 接口路径 | 方法 | 功能描述 | 请求对象 | 响应对象 |
|---------|------|----------|----------|----------|
| `/queryMallShopList` | POST | 商城店铺搜索 | ApiShopMallQueryReq | ApiShopEsCombinationResp |

## 接口调用说明

### 统一响应格式

所有接口都使用统一的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "success": true,
  "timestamp": 1640995200000
}
```

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 40030001 | 商品不满足下单条件 |
| 40030002 | 修改数量不满足条件 |
| 40030003 | 店铺活动发生变化 |
| 50030101 | 店铺不存在 |
| 50030102 | 店铺状态异常 |
| 50030103 | 商品不存在 |
| 50030104 | 商品状态异常 |
| 50030105 | 商品库存不足 |

### 接口认证

所有接口都需要在请求头中携带认证信息：

```
Authorization: Bearer {token}
Content-Type: application/json
```

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护团队**: 海马汇技术团队
