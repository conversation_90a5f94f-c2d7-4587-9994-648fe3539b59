# Himall-GW 架构图

## 1. 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A1[商家端Web]
        A2[商城端Web]
        A3[管理端Web]
        A4[移动端App]
    end
    
    subgraph "API网关层"
        B1[himall-gw]
        B2[负载均衡器]
    end
    
    subgraph "微服务层"
        C1[用户服务]
        C2[商品服务]
        C3[订单服务]
        C4[支付服务]
        C5[促销服务]
        C6[物流服务]
    end
    
    subgraph "数据层"
        D1[MySQL]
        D2[Redis]
        D3[Elasticsearch]
        D4[文件存储]
    end
    
    subgraph "外部系统"
        E1[ERP系统]
        E2[支付网关]
        E3[物流系统]
        E4[消息服务]
    end
    
    A1 --> B2
    A2 --> B2
    A3 --> B2
    A4 --> B2
    
    B2 --> B1
    
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    B1 --> C5
    B1 --> C6
    
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C1 --> D2
    C2 --> D2
    C2 --> D3
    C1 --> D4
    C2 --> D4
    
    B1 --> E1
    B1 --> E2
    B1 --> E3
    B1 --> E4
```

## 2. 模块依赖关系图

```mermaid
graph TD
    A[seashop-gw-server] --> B[seashop-gw-core]
    A --> C[seashop-gw-common]
    A --> D[seashop-gw-api]
    
    B --> C
    B --> D
    C --> D
    
    B --> E[seashop-base-boot]
    B --> F[seashop-base-security]
    C --> G[seashop-product-api]
    C --> H[seashop-order-api]
    C --> I[seashop-trade-api]
    
    D --> J[seashop-base-api]
```

## 3. API分层架构图

```mermaid
graph TB
    subgraph "Controller层"
        A1[SellerApiController]
        A2[MallApiController]
        A3[MApiController]
    end
    
    subgraph "Service层"
        B1[SellerService]
        B2[MallService]
        B3[MService]
    end
    
    subgraph "Remote层"
        C1[RemoteService]
        C2[FeignClient]
    end
    
    subgraph "外部服务"
        D1[Product Service]
        D2[Order Service]
        D3[User Service]
        D4[Trade Service]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C1
    B3 --> C1
    
    C1 --> C2
    C2 --> D1
    C2 --> D2
    C2 --> D3
    C2 --> D4
```

## 4. 数据流转图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant Service as 业务服务
    participant DB as 数据库
    
    Client->>Gateway: 发送请求
    Gateway->>Auth: 验证Token
    Auth-->>Gateway: 返回用户信息
    Gateway->>Service: 调用业务服务
    Service->>DB: 查询/更新数据
    DB-->>Service: 返回数据
    Service-->>Gateway: 返回业务结果
    Gateway-->>Client: 返回响应
```

## 5. 商品导入流程图

```mermaid
flowchart TD
    A[上传Excel文件] --> B{文件格式检查}
    B -->|格式正确| C[解析文件内容]
    B -->|格式错误| Z[返回错误信息]
    
    C --> D[字段映射验证]
    D --> E{必填字段检查}
    E -->|检查通过| F[数据格式验证]
    E -->|检查失败| Z
    
    F --> G{数据验证}
    G -->|验证通过| H[批量保存商品]
    G -->|验证失败| I[记录错误信息]
    
    H --> J[更新库存]
    J --> K[生成导入报告]
    I --> K
    
    K --> L[返回导入结果]
```

## 6. 订单处理流程图

```mermaid
stateDiagram-v2
    [*] --> 待支付
    待支付 --> 已支付: 支付成功
    待支付 --> 已取消: 超时/用户取消
    
    已支付 --> 待发货: 商家确认
    已支付 --> 退款中: 用户申请退款
    
    待发货 --> 已发货: 商家发货
    待发货 --> 退款中: 用户申请退款
    
    已发货 --> 已收货: 用户确认收货
    已发货 --> 退货中: 用户申请退货
    
    已收货 --> 已完成: 自动完成
    已收货 --> 售后中: 用户申请售后
    
    退款中 --> 已退款: 退款成功
    退货中 --> 已退货: 退货成功
    售后中 --> 已完成: 售后完成
    
    已取消 --> [*]
    已退款 --> [*]
    已退货 --> [*]
    已完成 --> [*]
```

## 7. 系统部署架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器]
    end
    
    subgraph "应用服务层"
        APP1[himall-gw-1]
        APP2[himall-gw-2]
        APP3[himall-gw-3]
    end
    
    subgraph "服务注册中心"
        NACOS[Nacos集群]
    end
    
    subgraph "数据存储层"
        MYSQL[(MySQL主从)]
        REDIS[(Redis集群)]
        ES[(Elasticsearch)]
    end
    
    subgraph "文件存储"
        OBS[华为云OBS]
    end
    
    subgraph "监控系统"
        MONITOR[监控告警]
        LOG[日志系统]
    end
    
    LB --> APP1
    LB --> APP2
    LB --> APP3
    
    APP1 --> NACOS
    APP2 --> NACOS
    APP3 --> NACOS
    
    APP1 --> MYSQL
    APP1 --> REDIS
    APP1 --> ES
    APP1 --> OBS
    
    APP2 --> MYSQL
    APP2 --> REDIS
    APP2 --> ES
    APP2 --> OBS
    
    APP3 --> MYSQL
    APP3 --> REDIS
    APP3 --> ES
    APP3 --> OBS
    
    APP1 --> MONITOR
    APP2 --> MONITOR
    APP3 --> MONITOR
    
    APP1 --> LOG
    APP2 --> LOG
    APP3 --> LOG
```

## 8. 安全架构图

```mermaid
graph TB
    subgraph "安全防护层"
        WAF[Web应用防火墙]
        DDOS[DDoS防护]
    end
    
    subgraph "认证授权层"
        AUTH[认证服务]
        RBAC[权限控制]
        TOKEN[Token管理]
    end
    
    subgraph "API网关层"
        GATEWAY[API网关]
        LIMIT[限流控制]
        VALIDATE[参数校验]
    end
    
    subgraph "应用层"
        APP[应用服务]
        ENCRYPT[数据加密]
        AUDIT[审计日志]
    end
    
    subgraph "数据层"
        DB[(加密数据库)]
        BACKUP[数据备份]
    end
    
    WAF --> DDOS
    DDOS --> AUTH
    AUTH --> RBAC
    RBAC --> TOKEN
    TOKEN --> GATEWAY
    GATEWAY --> LIMIT
    LIMIT --> VALIDATE
    VALIDATE --> APP
    APP --> ENCRYPT
    ENCRYPT --> AUDIT
    AUDIT --> DB
    DB --> BACKUP
```

## 9. 微服务通信图

```mermaid
graph LR
    subgraph "API网关"
        GW[himall-gw]
    end
    
    subgraph "用户域"
        US[用户服务]
        AS[认证服务]
    end
    
    subgraph "商品域"
        PS[商品服务]
        CS[分类服务]
        SS[规格服务]
    end
    
    subgraph "交易域"
        OS[订单服务]
        TS[交易服务]
        PS2[支付服务]
    end
    
    subgraph "营销域"
        PRS[促销服务]
        CPS[优惠券服务]
    end
    
    GW -.->|Feign| US
    GW -.->|Feign| AS
    GW -.->|Feign| PS
    GW -.->|Feign| CS
    GW -.->|Feign| SS
    GW -.->|Feign| OS
    GW -.->|Feign| TS
    GW -.->|Feign| PS2
    GW -.->|Feign| PRS
    GW -.->|Feign| CPS
    
    OS -.->|调用| PS
    OS -.->|调用| US
    TS -.->|调用| PS2
    PRS -.->|调用| PS
```

## 10. 监控体系架构图

```mermaid
graph TB
    subgraph "数据采集层"
        A1[应用指标]
        A2[系统指标]
        A3[业务指标]
        A4[日志数据]
    end
    
    subgraph "数据处理层"
        B1[指标聚合]
        B2[日志解析]
        B3[异常检测]
    end
    
    subgraph "存储层"
        C1[时序数据库]
        C2[日志存储]
        C3[配置存储]
    end
    
    subgraph "展示层"
        D1[监控大屏]
        D2[告警中心]
        D3[报表系统]
    end
    
    subgraph "告警通知"
        E1[邮件通知]
        E2[短信通知]
        E3[钉钉通知]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B2
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D1
    C3 --> D2
    
    D2 --> E1
    D2 --> E2
    D2 --> E3
    
    C1 --> D3
    C2 --> D3
```
