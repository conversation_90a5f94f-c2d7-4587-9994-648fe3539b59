# HiMall-Base API接口清单文档

## 文档概述

本文档详细列出了HiMall-Base项目中所有的API接口，包括Feign接口和REST Controller接口，按照业务模块进行分类整理。

## 接口分类

### 1. 用户认证模块 (Auth)

#### 1.1 AuthUserFeign - 用户认证接口
**服务路径**: `/himall-base/authInfo`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| getManagerUser | POST | `/getManagerUser` | 获取平台用户信息 | LoginBaseDto | ResultDto<LoginManagerDto> |
| getMemberUser | POST | `/getMemberUser` | 获取会员信息 | LoginBaseDto | ResultDto<LoginMemberDto> |
| getShopUser | POST | `/getShopUser` | 获取店铺用户信息 | LoginBaseDto | ResultDto<LoginShopDto> |

#### 1.2 LoginController - 登录控制器
**服务路径**: `/login`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| login | POST | `/doLogin` | 统一登录接口 | LoginReq | ResultDto<LoginResp> |
| logout | POST | `/logout` | 退出登录 | TokenCache | ResultDto<BaseResp> |
| refreshToken | POST | `/refreshToken` | 刷新Token | RefreshTokenReq | ResultDto<LoginResp> |
| sendSms | POST | `/sendSms` | 发送登录短信 | LoginSmsReq | ResultDto<BaseResp> |
| switchRole | POST | `/switchRole` | 角色切换 | SwitchRoleReq | ResultDto<LoginResp> |

### 2. 店铺管理模块 (Shop)

#### 2.1 ShopCmdFeign - 店铺操作接口
**服务路径**: `/himall-base/shop/shop`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| residencyApplication | POST | `/residencyApplication` | 入驻申请 | CmdAgreementReq | ResultDto<String> |
| sendCode | POST | `/sendCode` | 发送验证码 | CmdSendCodeReq | ResultDto<BaseResp> |
| editBaseInfo | POST | `/editBaseInfo` | 编辑基本信息 | CmdShopStepsOneReq | ResultDto<Long> |
| editBankInfo | POST | `/editBankInfo` | 编辑银行信息 | CmdShopStepsTwoReq | ResultDto<Long> |
| editBusinessInfo | POST | `/editBusinessInfo` | 编辑经营信息 | CmdShopStepsThreeReq | ResultDto<Long> |

#### 2.2 ShopErpCmdFeign - 店铺ERP接口
**服务路径**: `/himall-base/shop/shopErp`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| saveShopErp | POST | `/saveShopErp` | 保存店铺ERP配置 | SaveShopErpReq | ResultDto<BaseResp> |

#### 2.3 ShopShipperCmdFeign - 店铺发货地址接口
**服务路径**: `/himall-base/shop/shopShipper`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| addShopShipper | POST | `/addShopShipper` | 添加发货地址 | AddShopShipperReq | ResultDto<BaseResp> |
| updateShopShipper | POST | `/updateShopShipper` | 更新发货地址 | UpdateShopShipperReq | ResultDto<BaseResp> |
| deleteShopShipper | POST | `/deleteShopShipper` | 删除发货地址 | DeleteShopShipperReq | ResultDto<BaseResp> |

### 3. 用户账户模块 (Account)

#### 3.1 ManagerCmdFeign - 管理员操作接口
**服务路径**: `/himall-base/account/manager`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| addManager | POST | `/addManager` | 添加管理员 | CmdManagerReq | ResultDto<Long> |
| updateManager | POST | `/updateManager` | 更新管理员 | CmdManagerReq | ResultDto<Long> |
| deleteManager | POST | `/deleteManager` | 删除管理员 | CmdManagerReq | ResultDto<Long> |

#### 3.2 ManagerQueryFeign - 管理员查询接口
**服务路径**: `/himall-base/account/manager`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| queryManagerPage | POST | `/queryManagerPage` | 分页查询管理员 | QueryManagerPageReq | ResultDto<BasePageResp<ManagerResp>> |
| queryManagerDetail | POST | `/queryManagerDetail` | 查询管理员详情 | BaseIdReq | ResultDto<ManagerResp> |

#### 3.3 ShopMemberCmdFeign - 会员操作接口
**服务路径**: `/himall-base/account/member`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| freezeMember | POST | `/freezeMember` | 冻结会员 | CmdMemberReq | ResultDto<Long> |
| unfreezeMember | POST | `/unfreezeMember` | 解冻会员 | CmdMemberReq | ResultDto<Long> |
| addMember | POST | `/addMember` | 添加会员 | AddMemberReq | ResultDto<MemberResp> |

#### 3.4 ShippingAddressCmdFeign - 收货地址接口
**服务路径**: `/himall-base/account/shippingAddress`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| addAddress | POST | `/addAddress` | 添加收货地址 | AddShippingAddressReq | ResultDto<Long> |
| updateAddress | POST | `/updateAddress` | 更新收货地址 | UpdateShippingAddressReq | ResultDto<BaseResp> |
| deleteAddress | POST | `/deleteAddress` | 删除收货地址 | DeleteShippingAddressReq | ResultDto<BaseResp> |

#### 3.5 UserCenterQueryFeign - 用户中心查询接口
**服务路径**: `/himall-base/account/userCenter`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| queryUserCenterHome | POST | `/queryUserCenterHome` | 查询用户中心首页 | QueryUserCenterHomeReq | ResultDto<QueryUserCenterHomeResp> |

### 4. 基础服务模块 (Base)

#### 4.1 MessageCMDFeign - 消息服务接口
**服务路径**: `/himall-base/message`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| sendEmail | POST | `/sendEmail` | 发送邮件 | EmailBodyReq | ResultDto<Boolean> |
| sendSms | POST | `/sendSms` | 发送短信 | SmsBodyReq | ResultDto<Boolean> |
| querySmsBalance | GET | `/querySmsBalance` | 查询短信余额 | 无 | ResultDto<Long> |

#### 4.2 ImageQueryFeign - 图片查询接口
**服务路径**: `/himall-base/image`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| query | POST | `/query` | 查询图片列表 | BasePhotoSpaceQueryReq | ResultDto<BasePageResp<BasePhotoSpaceRes>> |
| queryAudit | POST | `/queryAudit` | 查询已审核图片 | BasePhotoSpaceQueryReq | ResultDto<BasePageResp<BasePhotoSpaceRes>> |

#### 4.3 ArticleQueryFeign - 文章查询接口
**服务路径**: `/himall-base/article`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| queryWithPage | POST | `/queryWithPage` | 分页查询文章 | BaseArticleQueryReq | ResultDto<BasePageResp<BaseArticleRes>> |
| queryTopByCategoryId | POST | `/queryTopByCategoryId` | 按分类查询置顶文章 | QueryTopArticleReq | ResultDto<List<BaseArticleRes>> |
| queryAllCategory | GET | `/queryAllCategory` | 查询所有分类 | 无 | ResultDto<ArticleCategoryListRes> |

#### 4.4 SiteSettingQueryFeign - 站点设置查询接口
**服务路径**: `/himall-base/siteSetting`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| getSetting | GET | `/getSetting` | 获取站点设置 | 无 | ResultDto<BaseSitSettingRes> |
| getSettled | GET | `/getSettled` | 获取入驻设置 | 无 | ResultDto<BaseSettledRes> |
| getAgreement | GET | `/getAgreement` | 获取协议 | agreementType | ResultDto<BaseAgreementRes> |

### 5. 地区管理模块 (Region)

#### 5.1 RegionQueryFeign - 地区查询接口
**服务路径**: `/himall-base/region`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| getRegionByParentId | GET | `/getRegionByParentId` | 根据父ID查询地区 | parentId | ResultDto<List<BaseRegionRes>> |
| getTreeRegions | GET | `/getTreeRegions` | 获取树形地区结构 | 无 | ResultDto<String> |
| getAllRegions | GET | `/getAllRegions` | 获取所有地区 | 无 | ResultDto<List<BaseRegionRes>> |

#### 5.2 RegionCMDFeign - 地区操作接口
**服务路径**: `/himall-base/region`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| initData | GET | `/initData` | 初始化地区数据 | 无 | ResultDto<Boolean> |
| initHishopData | GET | `/initHishopData` | 初始化海商地区数据 | 无 | ResultDto<Boolean> |
| SynMTRegionCode | GET | `/SynMTRegionCode` | 同步美团地区编码 | 无 | ResultDto<Boolean> |

### 6. 微信服务模块 (WeChat)

#### 6.1 WXMenuQueryFeign - 微信菜单查询接口
**服务路径**: `/himall-base/WXMenu`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| queryWXMenus | GET | `/getWXMenus` | 查询微信菜单列表 | 无 | ResultDto<List<BaseWXMenuListRes>> |
| queryWXMenuById | POST | `/queryWXMenuById` | 根据ID查询微信菜单 | BaseIdReq | ResultDto<BaseWXMenuRes> |
| createQR | POST | `/createQR` | 创建二维码 | CmdCreateQRReq | ResultDto<String> |

#### 6.2 WXMenuCMDFeign - 微信菜单操作接口
**服务路径**: `/himall-base/WXMenu`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| saveWXAccount | POST | `/saveWXAccount` | 保存微信账号 | SaveWXAccountReq | ResultDto<BaseResp> |
| getWXAccount | GET | `/getWXAccount` | 获取微信账号 | 无 | ResultDto<WXAccountResp> |
| saveWXMenu | POST | `/saveWXMenu` | 保存微信菜单 | SaveWXMenuReq | ResultDto<BaseResp> |
| delete | POST | `/delete` | 删除微信菜单 | BaseIdReq | ResultDto<Boolean> |
| syncWXMenu | POST | `/syncWXMenu` | 同步菜单到微信 | 无 | ResultDto<Boolean> |

#### 6.3 WXRequestCMDFeign - 微信请求接口
**服务路径**: `/himall-base/WXRequest`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| getAccessToken | POST | `/getAccessToken` | 获取微信AccessToken | 无 | ResultDto<String> |
| getJsApiSignature | POST | `/getJsApiSignature` | 获取JS-API签名 | JsApiSignatureReq | ResultDto<JsApiSignatureRes> |

### 7. 其他服务模块

#### 7.1 BanksQueryFeign - 银行查询接口
**服务路径**: `/himall-base/bank`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| queryList | GET | `/queryList` | 查询银行列表 | 无 | ResultDto<BanksListResp> |

#### 7.2 ExpressTrackQueryFeign - 物流查询接口
**服务路径**: `/himall-base/expressTrack`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| synchroSupportCompany | GET | `/synchroSupportCompany` | 同步支持的快递公司 | 无 | ResultDto<Boolean> |
| submitExpressSubscribe | POST | `/submitExpressSubscribe` | 订阅物流查询 | ExpressSubscribeReq | ResultDto<Long> |

#### 7.3 MobileFootQueryFeign - 移动端底部导航接口
**服务路径**: `/himall-base/mobileFoot`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| queryFootMenus | POST | `/queryFootMenus` | 查询底部导航菜单 | QueryFootMenusReq | ResultDto<QueryFootMenusResp> |

#### 7.4 MobileFootCmdFeign - 移动端底部导航操作接口
**服务路径**: `/himall-base/mobileFoot`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| addOrUpdateFootMenus | POST | `/addOrUpdateFootMenus` | 新增或编辑底部导航 | AddOrUpdateFootMenusReq | ResultDto<BaseResp> |
| saveFootMenus | POST | `/saveFootMenus` | 保存底部导航 | SaveFootMenusReq | ResultDto<BaseResp> |

#### 7.5 WayBillFeign - 运单接口
**服务路径**: `/himall-base/wayBill`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 请求参数 | 返回类型 |
|---------|---------|------|---------|---------|---------|
| setRegisterState | POST | `/setRegisterState` | 设置注册状态 | 无 | ResultDto<Boolean> |
| goExpressBills | GET | `/goExpressBills` | 获取快递单 | 无 | ResultDto<String> |

## 接口规范

### 请求规范
- 所有POST接口均使用 `application/json` 格式
- 统一使用 `ResultDto<T>` 作为响应格式
- 支持Feign客户端调用

### 响应规范
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "success": true
}
```

### 错误码规范
- 0: 成功
- 400: 客户端请求错误
- 401: 未授权请求
- 500: 服务端异常
- 业务错误码: xx-xxx-xxx 格式

---

*文档版本: 1.0*  
*最后更新: 2025-06-21*
