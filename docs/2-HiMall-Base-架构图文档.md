# HiMall-Base 架构图文档

## 文档概述

本文档通过多种架构图展示HiMall-Base项目的技术架构、模块关系、数据流向和部署架构，帮助开发者快速理解系统设计。

## 1. 系统总体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[PC管理后台]
        B[移动端H5]
        C[小程序]
        D[APP]
    end
    
    subgraph "网关层"
        E[API Gateway]
    end
    
    subgraph "服务层"
        F[himall-base]
        G[himall-product]
        H[himall-order]
        I[himall-trade]
    end
    
    subgraph "中间件层"
        J[Nacos注册中心]
        K[Redis缓存]
        L[RocketMQ消息队列]
        M[Elasticsearch搜索]
    end
    
    subgraph "数据层"
        N[MySQL主库]
        O[MySQL从库]
        P[华为云OBS]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    E --> F
    E --> G
    E --> H
    E --> I
    F --> J
    F --> K
    F --> L
    F --> M
    F --> N
    F --> O
    F --> P
```

## 2. HiMall-Base 模块架构图

```mermaid
graph TB
    subgraph "himall-base 微服务"
        subgraph "API接口层"
            A1[seashop-base-api]
        end
        
        subgraph "Web控制层"
            B1[seashop-base-server]
        end
        
        subgraph "业务服务层"
            C1[seashop-base-core]
            C2[seashop-base-user-auth]
            C3[seashop-base-user-account]
            C4[seashop-base-user-shop]
        end
        
        subgraph "安全认证层"
            D1[seashop-base-security]
        end
        
        subgraph "数据访问层"
            E1[seashop-base-dao]
        end
        
        subgraph "通用组件层"
            F1[seashop-base-common]
            F2[seashop-base-util]
            F3[seashop-base-boot]
            F4[seashop-base-user-common]
        end
    end
    
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C1 --> E1
    C2 --> E1
    C3 --> E1
    C4 --> E1
    D1 --> F1
    E1 --> F1
    C1 --> F2
    C2 --> F2
    C3 --> F2
    C4 --> F2
    A1 --> F3
    C2 --> F4
    C3 --> F4
    C4 --> F4
```

## 3. 技术架构图

```mermaid
graph LR
    subgraph "前端技术栈"
        A[Vue.js/React]
        B[微信小程序]
        C[移动端H5]
    end
    
    subgraph "后端技术栈"
        D[Spring Boot 2.x]
        E[Spring Cloud]
        F[MyBatis]
        G[Spring Security]
    end
    
    subgraph "数据存储"
        H[MySQL 5.7+]
        I[Redis 6.x]
        J[Elasticsearch 7.x]
    end
    
    subgraph "中间件"
        K[Nacos]
        L[RocketMQ]
        M[XXL-Job]
        N[Redisson]
    end
    
    subgraph "基础设施"
        O[华为云OBS]
        P[Docker]
        Q[Kubernetes]
    end
    
    A --> D
    B --> D
    C --> D
    D --> F
    D --> G
    F --> H
    D --> I
    D --> J
    E --> K
    D --> L
    D --> M
    I --> N
    D --> O
    D --> P
    P --> Q
```

## 4. 数据库架构图

```mermaid
erDiagram
    USER_MEMBER {
        bigint id PK
        varchar user_name
        varchar nick
        varchar cell_phone
        varchar password
        varchar password_salt
        datetime create_time
        datetime update_time
    }
    
    USER_MANAGER {
        bigint id PK
        varchar user_name
        varchar real_name
        varchar cell_phone
        varchar password
        varchar password_salt
        bigint shop_id FK
        datetime create_time
    }
    
    USER_SHOP {
        bigint id PK
        varchar shop_name
        varchar contact_user
        varchar contact_phone
        varchar shop_description
        int shop_status
        datetime create_time
        datetime update_time
    }
    
    BASE_PHOTO_SPACE {
        bigint id PK
        bigint photo_category_id
        bigint shop_id FK
        varchar photo_name
        varchar photo_path
        bigint file_size
        datetime upload_time
    }
    
    BASE_ARTICLE {
        bigint id PK
        varchar title
        text content
        bigint category_id
        int is_release
        datetime add_date
        datetime update_date
    }
    
    BASE_REGION {
        bigint id PK
        varchar name
        varchar short_name
        bigint parent_id
        int region_level
        varchar code
    }
    
    USER_MANAGER ||--|| USER_SHOP : belongs_to
    USER_SHOP ||--o{ BASE_PHOTO_SPACE : has_many
    BASE_REGION ||--o{ BASE_REGION : parent_child
```

## 5. 用户认证架构图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant Redis as Redis缓存
    participant DB as 数据库
    
    Client->>Gateway: 1. 登录请求
    Gateway->>Auth: 2. 转发登录请求
    Auth->>DB: 3. 验证用户凭证
    DB-->>Auth: 4. 返回用户信息
    Auth->>Redis: 5. 生成并缓存Token
    Redis-->>Auth: 6. 缓存成功
    Auth-->>Gateway: 7. 返回Token
    Gateway-->>Client: 8. 登录成功
    
    Client->>Gateway: 9. 业务请求(携带Token)
    Gateway->>Auth: 10. Token验证
    Auth->>Redis: 11. 查询Token
    Redis-->>Auth: 12. 返回用户信息
    Auth-->>Gateway: 13. 验证通过
    Gateway->>Auth: 14. 执行业务逻辑
    Auth-->>Gateway: 15. 返回结果
    Gateway-->>Client: 16. 响应结果
```

## 6. 服务调用架构图

```mermaid
graph TB
    subgraph "外部服务调用"
        A[其他微服务]
    end
    
    subgraph "himall-base服务"
        B[Feign接口层]
        C[Controller层]
        D[Service层]
        E[Repository层]
    end
    
    subgraph "数据存储"
        F[MySQL]
        G[Redis]
        H[Elasticsearch]
    end
    
    subgraph "外部依赖"
        I[短信服务]
        J[邮件服务]
        K[对象存储]
        L[微信API]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    D --> G
    D --> H
    D --> I
    D --> J
    D --> K
    D --> L
```

## 7. 部署架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        A[Nginx/SLB]
    end
    
    subgraph "应用服务层"
        B[himall-base-1]
        C[himall-base-2]
        D[himall-base-n]
    end
    
    subgraph "服务治理"
        E[Nacos注册中心]
        F[Nacos配置中心]
    end
    
    subgraph "数据存储层"
        G[MySQL主库]
        H[MySQL从库]
        I[Redis集群]
        J[ES集群]
    end
    
    subgraph "消息队列"
        K[RocketMQ集群]
    end
    
    subgraph "监控告警"
        L[Prometheus]
        M[Grafana]
        N[AlertManager]
    end
    
    A --> B
    A --> C
    A --> D
    B --> E
    C --> E
    D --> E
    B --> F
    C --> F
    D --> F
    B --> G
    C --> G
    D --> G
    B --> H
    C --> H
    D --> H
    B --> I
    C --> I
    D --> I
    B --> J
    C --> J
    D --> J
    B --> K
    C --> K
    D --> K
    B --> L
    C --> L
    D --> L
```

## 8. 数据流向图

```mermaid
flowchart TD
    A[用户请求] --> B{认证检查}
    B -->|未认证| C[返回401]
    B -->|已认证| D[权限检查]
    D -->|无权限| E[返回403]
    D -->|有权限| F[业务处理]
    F --> G{缓存检查}
    G -->|缓存命中| H[返回缓存数据]
    G -->|缓存未命中| I[查询数据库]
    I --> J[更新缓存]
    J --> K[返回数据]
    F --> L{需要异步处理}
    L -->|是| M[发送MQ消息]
    L -->|否| N[同步处理]
    M --> O[异步消费]
    N --> K
    O --> P[处理完成]
```

## 9. 安全架构图

```mermaid
graph TB
    subgraph "安全防护层"
        A[WAF防火墙]
        B[DDoS防护]
        C[SSL证书]
    end
    
    subgraph "认证授权层"
        D[JWT Token]
        E[角色权限]
        F[接口鉴权]
    end
    
    subgraph "数据安全层"
        G[数据加密]
        H[敏感信息脱敏]
        I[SQL注入防护]
    end
    
    subgraph "审计监控层"
        J[操作日志]
        K[安全审计]
        L[异常监控]
    end
    
    A --> D
    B --> D
    C --> D
    D --> G
    E --> G
    F --> G
    G --> J
    H --> J
    I --> J
```

## 10. 缓存架构图

```mermaid
graph LR
    subgraph "应用层"
        A[业务服务]
    end
    
    subgraph "缓存层"
        B[本地缓存]
        C[Redis缓存]
        D[分布式锁]
    end
    
    subgraph "数据层"
        E[MySQL数据库]
    end
    
    A --> B
    A --> C
    A --> D
    B -->|缓存未命中| C
    C -->|缓存未命中| E
    E -->|数据更新| C
    C -->|缓存失效| B
```

## 架构特点说明

### 1. 微服务架构
- 服务拆分合理，职责清晰
- 支持独立部署和扩展
- 服务间通过Feign进行通信

### 2. 分层架构
- API层：对外接口定义
- 控制层：请求处理和路由
- 服务层：业务逻辑实现
- 数据层：数据访问和持久化

### 3. 高可用设计
- 多实例部署
- 负载均衡
- 故障转移
- 熔断降级

### 4. 安全设计
- 多层安全防护
- 统一认证授权
- 数据加密传输
- 操作审计追踪

### 5. 性能优化
- 多级缓存策略
- 数据库读写分离
- 异步消息处理
- 连接池优化

---

*文档版本: 1.0*  
*最后更新: 2025-06-21*
