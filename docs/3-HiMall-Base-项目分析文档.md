# HiMall-Base 项目分析文档

## 文档概述

本文档深入分析HiMall-Base项目的技术实现、业务逻辑、代码结构和设计模式，为开发者提供详细的技术参考。

## 1. 项目基本信息

### 1.1 项目概况
- **项目名称**: himall-base
- **项目版本**: 1.0.1-SNAPSHOT
- **开发语言**: Java 8+
- **构建工具**: Maven 3.6+
- **框架版本**: Spring Boot 2.x
- **架构模式**: 微服务架构

### 1.2 技术栈分析

#### 核心框架
```xml
<!-- Spring Boot 核心 -->
<parent>
    <groupId>com.hishop.starter</groupId>
    <artifactId>hishop-parent</artifactId>
    <version>1.0.1-SNAPSHOT</version>
</parent>

<!-- Spring Cloud 微服务 -->
- Spring Cloud Gateway
- Spring Cloud OpenFeign
- Spring Cloud Nacos Discovery
```

#### 数据访问层
```xml
<!-- MyBatis ORM框架 -->
<dependency>
    <groupId>org.mybatis.spring.boot</groupId>
    <artifactId>mybatis-spring-boot-starter</artifactId>
</dependency>

<!-- MyBatis-Plus 增强 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>

<!-- 分页插件 -->
<dependency>
    <groupId>com.github.pagehelper</groupId>
    <artifactId>pagehelper-spring-boot-starter</artifactId>
</dependency>
```

#### 缓存和中间件
```xml
<!-- Redis 缓存 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- Redisson 分布式锁 -->
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson-spring-boot-starter</artifactId>
</dependency>

<!-- RocketMQ 消息队列 -->
<dependency>
    <groupId>com.hishop.starter</groupId>
    <artifactId>hishop-rocketmq-starter</artifactId>
</dependency>
```

## 2. 模块架构分析

### 2.1 模块依赖关系

```
himall-base (父模块)
├── seashop-base-api          # 对外接口定义
├── seashop-base-boot         # 基础配置和DTO
├── seashop-base-common       # 通用组件
├── seashop-base-core         # 核心业务逻辑
├── seashop-base-dao          # 数据访问层
├── seashop-base-security     # 安全认证
├── seashop-base-server       # 启动入口
├── seashop-base-util         # 工具类
├── seashop-base-user-account # 用户账户管理
├── seashop-base-user-auth    # 用户认证
├── seashop-base-user-shop    # 店铺管理
└── seashop-base-user-common  # 用户通用组件
```

### 2.2 核心模块分析

#### seashop-base-api (接口层)
**设计模式**: Facade模式
**主要职责**:
- 定义Feign客户端接口
- 统一对外API规范
- 服务间通信协议

**关键特性**:
```java
@FeignClient(name = "himall-base", 
             contextId = "AuthUserFeign", 
             url = "${himall-base.dev.url:}", 
             path = "/himall-base/authInfo")
public interface AuthUserFeign {
    @PostMapping(value = "/getManagerUser", consumes = "application/json")
    ResultDto<LoginManagerDto> getManagerUser(@RequestBody LoginBaseDto loginBaseDto);
}
```

#### seashop-base-security (安全层)
**设计模式**: 策略模式 + 模板方法模式
**主要职责**:
- 用户认证和授权
- Token管理
- 权限控制

**核心实现**:
```java
// 抽象认证处理器
public abstract class AbstractAuthenticationHandler {
    public LoginBaseDto getBaseAccountByToken(String token) {
        // 先从ThreadLocal获取
        LoginBaseDto accountDto = TracerUtil.getBaseAccount(getRoleEnum());
        if (Objects.isNull(accountDto)) {
            // 从Redis缓存获取
            Object obj = storage.getObject(LoginCacheKey.getUserTokenKey(getRoleEnum(), token));
            if (Objects.nonNull(obj)) {
                TokenCache tokenCache = JsonUtil.parseObject(JsonUtil.toJsonString(obj), TokenCache.class);
                return tokenCacheToLoginInfo(tokenCache);
            }
        }
        return null;
    }
}
```

#### seashop-base-user-auth (认证服务)
**设计模式**: 策略模式
**主要职责**:
- 多种登录策略实现
- 登录状态管理
- 角色切换

**策略模式实现**:
```java
// 登录策略接口
public interface LoginStrategy {
    LoginBaseDto process(LoginReq req);
}

// 密码登录策略
@Component
public class PasswordLoginStrategy implements LoginStrategy {
    @Override
    public LoginBaseDto process(LoginReq req) {
        // 密码登录逻辑
        String loginName = req.getUserName();
        String password = req.getPassword();
        LoginPlatformEnum loginPlatform = LoginPlatformEnum.valueOf(req.getLoginPlatform());
        // ... 具体实现
    }
}

// 子账户登录策略
@Component
public class SubAccountLoginStrategy implements LoginStrategy {
    @Override
    public LoginBaseDto process(LoginReq req) {
        // 子账户登录逻辑
    }
}
```

## 3. 核心业务分析

### 3.1 用户认证体系

#### 认证流程
1. **用户登录**: 支持用户名/手机号 + 密码登录
2. **凭证验证**: 密码加盐哈希验证
3. **Token生成**: JWT Token生成和管理
4. **权限检查**: 基于角色的权限控制
5. **会话管理**: Redis缓存用户会话

#### 多平台支持
```java
public enum LoginPlatformEnum {
    PLATFORM_BE("平台后台"),
    SELLER_BE("卖家后台"), 
    BUSINESS_FE("买家前台");
}
```

#### 角色权限体系
```java
public enum RoleEnum {
    MANAGER("平台管理员"),
    SHOP("店铺管理员"),
    MEMBER("普通会员");
}
```

### 3.2 店铺管理体系

#### 店铺生命周期
1. **入驻申请**: 商家提交入驻申请
2. **资料完善**: 填写基本信息、银行信息、经营信息
3. **平台审核**: 平台审核商家资质
4. **店铺开通**: 审核通过后开通店铺
5. **运营管理**: 店铺日常运营管理

#### 店铺状态管理
```java
public enum ShopStatusEnum {
    APPLYING(0, "申请中"),
    OPEN(1, "正常营业"),
    CLOSED(2, "店铺关闭"),
    FROZEN(3, "店铺冻结");
}
```

### 3.3 基础数据管理

#### 图片管理
- 图片上传和存储
- 图片分类管理
- 图片审核机制
- 图片空间管理

#### 内容管理
- 文章发布和管理
- 专题页面管理
- 模板配置管理
- SEO优化支持

## 4. 技术实现分析

### 4.1 数据访问层设计

#### Repository模式
```java
@Component
@Slf4j
public class BasePhotoSpaceRepository {
    @Resource
    private BasePhotoSpaceMapper photoSpaceMapper;

    public Long create(BasePhotoSpace photoSpace) {
        return (long) photoSpaceMapper.insert(photoSpace);
    }

    public List<BasePhotoSpace> query(BasePhotoSpaceExample example) {
        return photoSpaceMapper.selectByExample(example);
    }
}
```

#### MyBatis配置
```yaml
mybatis:
  type-aliases-package: com.sankuai.shangou.seashop.base.dao.core.mapper
  mapper-locations: classpath:mapper/*.xml

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
```

### 4.2 缓存设计

#### Redis缓存工具
```java
@Slf4j
public class SquirrelUtil implements InitializingBean {
    @Value("${spring.application.name:'himall'}")
    private String category;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public void set(String key, Object value, int expire) {
        redisTemplate.opsForValue().set(buildStoreKey(key), value, expire, TimeUnit.SECONDS);
    }

    public String buildStoreKey(String key) {
        return category + ":" + key;
    }
}
```

#### 分布式锁实现
```java
public class LockHelper {
    public static <T> T lock(String lockName, long tryTime, Supplier<T> supplier) {
        RLock lock = getRedissonClient().getLock(CommonOptions.name(lockName).timeout(Duration.ofSeconds(tryTime)));
        try {
            boolean flag = lock.tryLock(tryTime, TimeUnit.SECONDS);
            AssertUtil.throwIfTrue(!flag, "服务繁忙, 请稍后再试~");
            return supplier.get();
        } catch (InterruptedException ie) {
            log.error("获取分布式锁失败, error: {}", ie);
            throw new BusinessException("服务繁忙, 请稍后再试~");
        } finally {
            lock.unlock();
        }
    }
}
```

### 4.3 异常处理机制

#### 统一异常码
```java
public enum BaseResultCodeEnum {
    // 业务异常定义：xx-xxx-xxx
    // 020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务
    LIMIT_TIME_BUY_MAX_COUNT(40020001, "限时购轮播图最大数量不能超过%s个"),
    LIMIT_TIME_BUY_DATA_NOT_EXIST(40020002, "限时购轮播图数据不存在");
}
```

#### 统一响应格式
```java
public class ResultDto<T> {
    private Integer code;
    private String message;
    private T data;
    private Boolean success;
}
```

### 4.4 配置管理

#### Nacos配置中心
```yaml
spring:
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:124.71.221.117:8848}
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:hishop}
      config:
        namespace: ${spring.profiles.active}
        group: 1.0.0
      discovery:
        namespace: ${spring.profiles.active}
        group: 1.0.0
  config:
    import:
      - optional:nacos:himall-common.yml
      - optional:nacos:${spring.application.name}.yml
```

## 5. 数据库设计分析

### 5.1 核心表结构

#### 用户相关表
```sql
-- 会员表
CREATE TABLE `user_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `nick` varchar(50) DEFAULT NULL COMMENT '昵称',
  `cell_phone` varchar(200) DEFAULT NULL COMMENT '手机号(加密)',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `password_salt` varchar(10) NOT NULL COMMENT '密码盐',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_name` (`user_name`)
);

-- 管理员表
CREATE TABLE `user_manager` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `cell_phone` varchar(200) DEFAULT NULL COMMENT '手机号',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `shop_id` bigint(20) DEFAULT '0' COMMENT '店铺ID',
  PRIMARY KEY (`id`)
);
```

#### 店铺相关表
```sql
-- 店铺表
CREATE TABLE `user_shop` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_name` varchar(100) NOT NULL COMMENT '店铺名称',
  `contact_user` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `shop_status` int(11) DEFAULT '0' COMMENT '店铺状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 5.2 数据库设计特点

#### 安全性设计
- 敏感信息加密存储（手机号、身份证等）
- 密码加盐哈希存储
- 数据软删除机制

#### 性能优化
- 合理的索引设计
- 分表分库支持
- 读写分离配置

## 6. 安全机制分析

### 6.1 认证安全
- JWT Token机制
- Token过期管理
- 多设备登录控制
- 登录失败次数限制

### 6.2 数据安全
- 敏感数据加密
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护

### 6.3 接口安全
- 接口权限控制
- 请求频率限制
- 参数校验机制
- 操作日志记录

## 7. 性能优化分析

### 7.1 缓存策略
- 多级缓存设计
- 缓存预热机制
- 缓存更新策略
- 缓存穿透防护

### 7.2 数据库优化
- 连接池配置
- 慢查询监控
- 索引优化
- 分页查询优化

### 7.3 并发处理
- 分布式锁机制
- 异步处理
- 线程池配置
- 限流降级

## 8. 监控和运维

### 8.1 日志管理
```xml
<!-- Log4j2 配置 -->
<Configuration>
    <Appenders>
        <RollingFile name="RollingFile">
            <FileName>logs/himall-base.log</FileName>
            <FilePattern>logs/himall-base-%d{yyyy-MM-dd}-%i.log</FilePattern>
        </RollingFile>
    </Appenders>
</Configuration>
```

### 8.2 健康检查
- 应用健康状态监控
- 数据库连接监控
- 缓存服务监控
- 外部依赖监控

### 8.3 性能监控
- JVM性能监控
- 接口响应时间监控
- 数据库性能监控
- 缓存命中率监控

---

*文档版本: 1.0*  
*最后更新: 2025-06-21*
