# Himall-GW API接口清单

## 1. 商家端API (Seller API)

### 1.1 用户管理 (/sellerApi/apiManager)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 管理员登录 | POST | /login | 商家管理员登录 | 无 |
| 刷新Token | POST | /refreshToken | 刷新访问令牌 | 无 |
| 退出登录 | POST | /logout | 管理员退出登录 | SHOP |
| 查询管理员分页 | POST | /queryManagerPage | 分页查询管理员列表 | SHOP |
| 查询管理员详情 | POST | /queryManager | 查询管理员详细信息 | SHOP |
| 添加管理员 | POST | /addManager | 添加新管理员 | SHOP |
| 编辑管理员 | POST | /editManager | 编辑管理员信息 | SHOP |
| 删除管理员 | POST | /deleteManager | 删除管理员 | SHOP |
| 批量删除管理员 | POST | /batchDeleteManager | 批量删除管理员 | SHOP |
| 查询菜单权限 | POST | /queryMenuAuth | 查询用户菜单权限 | SHOP |

### 1.2 商品管理 (/sellerApi/apiProduct)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 创建商品 | POST | /createProduct | 创建新商品 | SHOP |
| 编辑商品 | POST | /editProduct | 编辑商品信息 | SHOP |
| 删除商品 | POST | /deleteProduct | 删除商品 | SHOP |
| 查询商品列表 | POST | /queryProduct | 分页查询商品列表 | SHOP |
| 查询商品详情 | POST | /queryProductDetail | 查询商品详细信息 | SHOP |
| 商品上架 | POST | /onSale | 商品上架操作 | SHOP |
| 商品下架 | POST | /offSale | 商品下架操作 | SHOP |
| 导入商品 | POST | /importProduct | 批量导入商品 | SHOP |
| 导入库存 | POST | /importStock | 批量导入库存 | SHOP |
| 导入价格 | POST | /importPrice | 批量导入价格 | SHOP |
| 导入下架 | POST | /importOffSale | 批量下架商品 | SHOP |
| 更新商品 | POST | /importProductUpdate | 批量更新商品 | SHOP |
| 绑定描述模板 | POST | /bindDescriptionTemplate | 绑定商品描述模板 | SHOP |
| 绑定运费模板 | POST | /bindFreightTemplate | 绑定运费模板 | SHOP |
| 绑定推荐商品 | POST | /bindRecommendProduct | 绑定推荐商品 | SHOP |

### 1.3 订单管理 (/sellerApi/apiOrder)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 查询订单列表 | POST | /queryOrder | 分页查询订单列表 | SHOP |
| 查询订单详情 | POST | /queryOrderDetail | 查询订单详细信息 | SHOP |
| 确认订单 | POST | /confirmOrder | 商家确认订单 | SHOP |
| 发货 | POST | /delivery | 订单发货操作 | SHOP |
| 修改收货地址 | POST | /updateReceiver | 修改订单收货地址 | SHOP |
| 修改运费 | POST | /updateFreight | 修改订单运费 | SHOP |
| 修改商品金额 | POST | /updateItemAmount | 修改订单商品金额 | SHOP |
| 更新快递信息 | POST | /updateExpress | 更新订单快递信息 | SHOP |
| 商家备注 | POST | /sellerRemark | 添加商家备注 | SHOP |
| 代客下单 | POST | /reBuyBySeller | 商家代客下单 | SHOP |
| 导出订单 | POST | /export | 导出订单数据 | SHOP |
| 查询操作日志 | POST | /queryOperationLog | 查询订单操作日志 | SHOP |
| 查询面单列表 | POST | /queryWayBillList | 查询电子面单列表 | SHOP |

### 1.4 促销管理 (/sellerApi/apiPromotion)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 创建满减活动 | POST | /createFullReduction | 创建满减促销活动 | SHOP |
| 编辑满减活动 | POST | /editFullReduction | 编辑满减活动 | SHOP |
| 删除满减活动 | POST | /deleteFullReduction | 删除满减活动 | SHOP |
| 查询满减活动 | POST | /queryFullReduction | 查询满减活动列表 | SHOP |
| 结束满减活动 | POST | /endFullReduction | 结束满减活动 | SHOP |
| 创建专属价格 | POST | /createExclusivePrice | 创建专属价格 | SHOP |
| 导入专属价格 | POST | /importExclusivePrice | 批量导入专属价格 | SHOP |

### 1.5 物流管理 (/sellerApi/wayBill)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 获取面单平台地址 | GET | /goExpressBills | 获取电子面单平台地址 | SHOP |

## 2. 商城端API (Mall API)

### 2.1 店铺管理 (/mallApi/apiShop)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 入驻申请 | POST | /residencyApplication | 供应商入驻申请 | 登录用户 |
| 查询店铺详情 | GET | /queryDetail | 查询店铺详细信息 | 登录用户 |
| 店铺介绍 | POST | /shopIntroduction | 获取店铺介绍信息 | 可选登录 |
| 生成二维码 | POST | /createQrCode | 生成店铺二维码 | 无 |
| 编辑基本信息 | POST | /editProfilesOne | 编辑店铺基本信息 | 登录用户 |
| 编辑银行信息 | POST | /editProfilesTwo | 编辑银行信息 | 登录用户 |
| 编辑经营信息 | POST | /editProfilesThree | 编辑经营信息 | 登录用户 |
| 发送验证码 | POST | /sendCode | 发送手机验证码 | 无 |

### 2.2 商品浏览 (/mallApi/apiProduct)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 查询商品列表 | POST | /queryProduct | 分页查询商品列表 | 无 |
| 查询商品详情 | POST | /queryProductDetail | 查询商品详细信息 | 无 |
| 根据ID查询商品 | POST | /queryProductById | 根据ID批量查询商品 | 无 |
| 商品搜索 | POST | /searchProduct | 商品关键词搜索 | 无 |

### 2.3 订单管理 (/mallApi/apiOrder)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 查询订单列表 | POST | /queryOrder | 查询用户订单列表 | 登录用户 |
| 查询订单详情 | POST | /queryOrderDetail | 查询订单详细信息 | 登录用户 |
| 取消订单 | POST | /cancelOrder | 用户取消订单 | 登录用户 |
| 确认收货 | POST | /confirmReceive | 用户确认收货 | 登录用户 |
| 申请退款 | POST | /applyRefund | 申请订单退款 | 登录用户 |
| 申请退货 | POST | /applyReturn | 申请商品退货 | 登录用户 |
| 导出订单 | POST | /exportOrder | 导出用户订单 | 登录用户 |

### 2.4 交易管理 (/mallApi/apiTrade)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 预览订单 | POST | /previewOrder | 预览订单信息 | 登录用户 |
| 提交订单 | POST | /submitOrder | 提交订单 | 登录用户 |
| 修改商品数量 | POST | /changeSkuQuantity | 修改购物车商品数量 | 登录用户 |
| 查询购物车 | POST | /queryCart | 查询购物车信息 | 登录用户 |

## 3. 管理端API (Management API)

### 3.1 商品管理 (/mApi/apiProduct)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 查询商品列表 | POST | /queryProduct | 平台商品列表查询 | MANAGER |
| 批量保存商品序号 | POST | /batchSaveProductSequence | 批量保存商品排序 | MANAGER |
| 平台导入商品 | POST | /platformImportProduct | 平台批量导入商品 | MANAGER |
| 商品审核 | POST | /auditProduct | 商品审核操作 | MANAGER |
| 违规下架 | POST | /violationOffSale | 违规商品下架 | MANAGER |

### 3.2 订单管理 (/mApi/apiOrder)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 查询平台订单 | POST | /queryPlatformOrder | 查询平台订单列表 | MANAGER |
| 查询订单详情 | POST | /queryOrderDetail | 查询订单详细信息 | MANAGER |
| 导出订单 | POST | /export | 导出平台订单 | MANAGER |
| 导出发票 | POST | /exportOrderInvoice | 导出订单发票 | MANAGER |
| 查询操作日志 | POST | /queryOrderOperationLog | 查询订单操作日志 | MANAGER |
| 平台首页统计 | POST | /statsPlatformIndexTradeData | 平台首页交易统计 | MANAGER |

### 3.3 促销管理 (/mApi/apiPromotion)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 查询促销审核统计 | POST | /auditStatisticsQuery | 查询促销审核统计 | MANAGER |
| 保存预付款活动 | POST | /saveAdvance | 保存预付款活动 | MANAGER |
| 更新限时抢购配置 | POST | /updateFlashSaleConfig | 更新限时抢购配置 | MANAGER |

### 3.4 系统管理 (/mApi/apiSystem)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 图片查询 | POST | /apiImage/query | 查询图片资源 | MANAGER |
| 图片上传 | POST | /apiImage/upload | 上传图片资源 | MANAGER |
| 微信菜单查询 | POST | /apiWXMenu/queryWXMenus | 查询微信菜单 | MANAGER |
| 微信菜单保存 | POST | /apiWXMenu/save | 保存微信菜单 | MANAGER |
| 同步微信菜单 | POST | /apiWXMenu/syncWXMenu | 同步微信菜单 | MANAGER |

## 4. 公共接口

### 4.1 文件上传 (/api/file)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 文件上传 | POST | /upload | 通用文件上传 | 登录用户 |
| 图片上传 | POST | /uploadImage | 图片文件上传 | 登录用户 |

### 4.2 数据字典 (/api/dict)

| 接口名称 | 请求方法 | 路径 | 功能描述 | 权限要求 |
|---------|---------|------|----------|----------|
| 查询字典 | POST | /queryDict | 查询数据字典 | 无 |
| 查询地区 | POST | /queryRegion | 查询地区信息 | 无 |

## 5. 接口规范说明

### 5.1 请求格式
- **Content-Type**: application/json
- **字符编码**: UTF-8
- **请求方法**: 主要使用POST方法

### 5.2 响应格式
```json
{
  "success": true,
  "code": "200",
  "msg": "操作成功",
  "data": {
    // 具体业务数据
  }
}
```

### 5.3 权限说明
- **无**: 无需登录即可访问
- **登录用户**: 需要用户登录
- **可选登录**: 登录和未登录都可访问，但返回数据可能不同
- **SHOP**: 需要商家角色权限
- **MANAGER**: 需要管理员角色权限

### 5.4 分页参数
```json
{
  "pageNo": 1,
  "pageSize": 20,
  "sortField": "createTime",
  "sortOrder": "desc"
}
```

### 5.5 分页响应
```json
{
  "success": true,
  "data": {
    "list": [],
    "total": 100,
    "pageNo": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}
```
