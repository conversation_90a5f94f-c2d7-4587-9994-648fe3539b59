# Himall-Trade 架构图文档

## 文档说明

本文档通过多个架构图展示himall-trade项目的系统架构、技术栈、业务流程和数据模型，帮助读者直观理解项目的整体设计和实现方案。

## 1. 系统整体架构图

### 1.1 分层架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[商家端Web] --> B[移动端App]
        B --> C[管理后台]
    end
    
    subgraph "网关层"
        D[API Gateway]
    end
    
    subgraph "服务层 - himall-trade"
        E[seashop-trade-server<br/>服务启动层]
        
        subgraph "API接口层"
            F[TradeProductQueryFeign<br/>商品查询]
            G[ShoppingCartQueryFeign<br/>购物车查询]
            H[PreOrderQueryFeign<br/>预订单查询]
            I[PromotionQueryFeign<br/>营销查询]
        end
        
        subgraph "业务逻辑层"
            J[TradeProductService<br/>交易商品服务]
            K[ShoppingCartService<br/>购物车服务]
            L[PreOrderService<br/>预订单服务]
            M[PromotionService<br/>营销服务]
            N[ProductService<br/>商品服务]
        end
        
        subgraph "数据访问层"
            O[ProductMapper<br/>商品数据]
            P[ShoppingCartMapper<br/>购物车数据]
            Q[PromotionMapper<br/>营销数据]
        end
    end
    
    subgraph "数据存储层"
        R[(MySQL<br/>主数据库)]
        S[(MySQL<br/>从数据库)]
        T[(Redis<br/>缓存)]
        U[(Elasticsearch<br/>搜索引擎)]
    end
    
    subgraph "消息队列"
        V[RocketMQ<br/>异步消息]
    end
    
    subgraph "外部服务"
        W[订单服务<br/>himall-order]
        X[用户服务<br/>himall-user]
        Y[支付服务<br/>himall-pay]
        Z[物流服务<br/>himall-logistics]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    
    E --> F
    E --> G
    E --> H
    E --> I
    
    F --> J
    G --> K
    H --> L
    I --> M
    J --> N
    
    J --> O
    K --> P
    L --> P
    M --> Q
    N --> O
    
    O --> R
    P --> R
    Q --> R
    O --> S
    P --> S
    Q --> S
    
    J --> T
    K --> T
    L --> T
    M --> T
    
    J --> U
    
    L --> V
    M --> V
    
    L --> W
    K --> X
    L --> Y
    J --> Z
```

### 1.2 模块架构图

```mermaid
graph LR
    subgraph "himall-trade项目结构"
        A[seashop-trade-api<br/>API接口定义层]
        B[seashop-trade-common<br/>公共组件层]
        C[seashop-trade-dao<br/>数据访问层]
        D[seashop-trade-core<br/>业务逻辑层]
        E[seashop-trade-server<br/>服务启动层]
    end
    
    E --> D
    D --> C
    D --> B
    D --> A
    C --> B
    A --> B
```

## 2. 技术栈架构图

### 2.1 完整技术栈图

```mermaid
graph TB
    subgraph "前端层"
        A1[Vue.js/React前端]
        A2[移动端App]
        A3[管理后台]
    end
    
    subgraph "网关层"
        B1[Spring Cloud Gateway]
        B2[Nginx负载均衡]
    end
    
    subgraph "微服务层"
        C1[himall-trade<br/>交易服务]
        C2[himall-order<br/>订单服务]
        C3[himall-user<br/>用户服务]
        C4[himall-pay<br/>支付服务]
    end
    
    subgraph "框架层"
        D1[Spring Boot 2.x]
        D2[Spring Cloud]
        D3[Spring Security]
        D4[MyBatis Plus]
    end
    
    subgraph "中间件层"
        E1[Redis集群<br/>缓存]
        E2[RocketMQ<br/>消息队列]
        E3[Elasticsearch<br/>搜索引擎]
        E4[Nacos<br/>配置中心]
    end
    
    subgraph "数据层"
        F1[MySQL主库]
        F2[MySQL从库]
        F3[MongoDB<br/>文档存储]
    end
    
    subgraph "基础设施"
        G1[Docker容器]
        G2[Kubernetes]
        G3[Jenkins CI/CD]
        G4[Prometheus监控]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    B1 --> B2
    B2 --> C1
    B2 --> C2
    B2 --> C3
    B2 --> C4
    
    C1 --> D1
    C1 --> D2
    C1 --> D3
    C1 --> D4
    
    C1 --> E1
    C1 --> E2
    C1 --> E3
    C1 --> E4
    
    C1 --> F1
    C1 --> F2
    C1 --> F3
    
    C1 --> G1
    G1 --> G2
    G2 --> G3
    G2 --> G4
```

## 3. 核心业务流程图

### 3.1 主要业务流程

```mermaid
graph TD
    subgraph "商品搜索流程"
        A1[用户搜索商品] --> A2[TradeProductQueryController]
        A2 --> A3[TradeProductService.search]
        A3 --> A4[Elasticsearch查询]
        A4 --> A5[商品信息聚合]
        A5 --> A6[返回搜索结果]
    end
    
    subgraph "购物车操作流程"
        B1[添加商品到购物车] --> B2[ShoppingCartCmdController]
        B2 --> B3[ShoppingCartService.addShoppingCart]
        B3 --> B4[商品信息校验]
        B4 --> B5[库存检查]
        B5 --> B6[购物车数据持久化]
        B6 --> B7[返回操作结果]
    end
    
    subgraph "订单提交流程"
        C1[用户提交订单] --> C2[PreOrderCmdController]
        C2 --> C3[PreOrderService.submitOrder]
        C3 --> C4[订单数据校验]
        C4 --> C5[库存扣减]
        C5 --> C6[营销活动计算]
        C6 --> C7[调用订单服务创建订单]
        C7 --> C8[清理购物车]
        C8 --> C9[返回订单信息]
    end
    
    subgraph "营销活动流程"
        D1[查询可用优惠] --> D2[PromotionService]
        D2 --> D3[优惠券查询]
        D2 --> D4[满减活动查询]
        D2 --> D5[限时购查询]
        D3 --> D6[优惠信息聚合]
        D4 --> D6
        D5 --> D6
        D6 --> D7[返回优惠列表]
    end
```

### 3.2 详细订单提交流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as 网关
    participant T as Trade服务
    participant O as Order服务
    participant DB as 数据库
    participant MQ as 消息队列
    
    U->>F: 点击提交订单
    F->>G: 发送订单请求
    G->>T: 路由到Trade服务
    
    T->>T: 获取提交Token
    T->>T: 校验订单数据
    T->>DB: 检查商品库存
    T->>T: 计算营销优惠
    T->>T: 计算订单金额
    
    T->>O: 调用订单服务创建订单
    O->>DB: 保存订单数据
    O-->>T: 返回订单信息
    
    T->>DB: 扣减库存
    T->>DB: 清理购物车
    T->>MQ: 发送订单创建消息
    
    T-->>G: 返回订单结果
    G-->>F: 返回响应
    F-->>U: 显示订单信息
```

## 4. 数据模型架构图

### 4.1 数据库ER关系图

```mermaid
erDiagram
    PRODUCT {
        bigint id PK
        bigint product_id UK
        bigint shop_id FK
        bigint category_id FK
        bigint brand_id FK
        varchar product_name
        varchar product_code
        varchar short_description
        tinyint sale_status
        tinyint audit_status
        decimal market_price
        decimal min_sale_price
        decimal max_sale_price
        boolean whether_delete
        datetime create_time
        datetime update_time
    }
    
    SHOPPING_CART {
        bigint id PK
        bigint user_id
        bigint product_id FK
        varchar sku_id
        bigint quantity
        int platform
        datetime add_time
        boolean whether_select
        datetime create_time
        datetime update_time
    }
    
    SKU_STOCK {
        bigint id PK
        varchar sku_id UK
        bigint product_id FK
        int stock
        int safe_stock
        datetime create_time
        datetime update_time
    }
    
    CATEGORY {
        bigint id PK
        bigint category_id UK
        varchar category_name
        bigint parent_id
        int level
        int sort
        boolean whether_delete
        datetime create_time
        datetime update_time
    }
    
    BRAND {
        bigint id PK
        bigint brand_id UK
        varchar brand_name
        varchar brand_logo
        varchar brand_desc
        boolean whether_delete
        datetime create_time
        datetime update_time
    }
    
    COUPON {
        bigint id PK
        bigint coupon_id UK
        bigint shop_id
        varchar coupon_name
        int coupon_type
        decimal coupon_amount
        decimal min_order_amount
        datetime start_time
        datetime end_time
        int status
        datetime create_time
        datetime update_time
    }
    
    FULL_REDUCTION {
        bigint id PK
        bigint activity_id UK
        bigint shop_id
        varchar activity_name
        decimal min_amount
        decimal reduction_amount
        datetime start_time
        datetime end_time
        int status
        datetime create_time
        datetime update_time
    }
    
    FLASH_SALE {
        bigint id PK
        bigint activity_id UK
        bigint shop_id
        varchar activity_name
        datetime start_time
        datetime end_time
        int status
        datetime create_time
        datetime update_time
    }
    
    FLASH_SALE_PRODUCT {
        bigint id PK
        bigint activity_id FK
        bigint product_id FK
        varchar sku_id
        decimal flash_price
        int flash_stock
        int sold_quantity
        datetime create_time
        datetime update_time
    }
    
    PRODUCT ||--o{ SHOPPING_CART : "has"
    PRODUCT ||--o{ SKU_STOCK : "has"
    PRODUCT }o--|| CATEGORY : "belongs_to"
    PRODUCT }o--|| BRAND : "belongs_to"
    FLASH_SALE ||--o{ FLASH_SALE_PRODUCT : "contains"
    PRODUCT ||--o{ FLASH_SALE_PRODUCT : "participates_in"
```

## 5. 部署架构图

### 5.1 云原生部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器]
    end
    
    subgraph "Kubernetes集群"
        subgraph "Namespace: himall-trade"
            subgraph "Pod副本"
                P1[trade-pod-1]
                P2[trade-pod-2]
                P3[trade-pod-3]
            end
            
            subgraph "Service"
                SVC[trade-service]
            end
            
            subgraph "ConfigMap"
                CM[配置文件]
            end
            
            subgraph "Secret"
                SEC[敏感配置]
            end
        end
        
        subgraph "存储"
            PV[持久化存储]
        end
    end
    
    subgraph "外部服务"
        DB[(数据库集群)]
        REDIS[(Redis集群)]
        ES[(ES集群)]
        MQ[(RocketMQ集群)]
    end
    
    LB --> SVC
    SVC --> P1
    SVC --> P2
    SVC --> P3
    
    P1 --> CM
    P1 --> SEC
    P1 --> PV
    P2 --> CM
    P2 --> SEC
    P3 --> CM
    P3 --> SEC
    
    P1 --> DB
    P1 --> REDIS
    P1 --> ES
    P1 --> MQ
    P2 --> DB
    P2 --> REDIS
    P2 --> ES
    P2 --> MQ
    P3 --> DB
    P3 --> REDIS
    P3 --> ES
    P3 --> MQ
```

## 6. 监控架构图

### 6.1 监控体系架构

```mermaid
graph TB
    subgraph "应用层"
        APP[himall-trade应用]
    end
    
    subgraph "监控采集层"
        PROM[Prometheus]
        JAEGER[Jaeger链路追踪]
        ELK[ELK日志收集]
    end
    
    subgraph "存储层"
        TSDB[(时序数据库)]
        ESDB[(Elasticsearch)]
    end
    
    subgraph "展示层"
        GRAFANA[Grafana仪表板]
        KIBANA[Kibana日志分析]
    end
    
    subgraph "告警层"
        ALERT[AlertManager]
        NOTIFY[通知系统]
    end
    
    APP --> PROM
    APP --> JAEGER
    APP --> ELK
    
    PROM --> TSDB
    JAEGER --> ESDB
    ELK --> ESDB
    
    TSDB --> GRAFANA
    ESDB --> KIBANA
    
    PROM --> ALERT
    ALERT --> NOTIFY
```

## 架构设计原则

### 1. 分层架构原则
- **职责分离**: 每层只负责特定的功能
- **依赖倒置**: 上层依赖下层的抽象接口
- **松耦合**: 层与层之间通过接口交互

### 2. 微服务原则
- **单一职责**: 每个服务只负责一个业务领域
- **自治性**: 服务可以独立开发、部署和扩展
- **去中心化**: 避免单点故障

### 3. 高可用原则
- **冗余设计**: 关键组件都有备份
- **故障隔离**: 局部故障不影响整体
- **快速恢复**: 具备自动恢复能力

### 4. 可扩展原则
- **水平扩展**: 支持增加服务实例
- **垂直扩展**: 支持增加单实例资源
- **弹性伸缩**: 根据负载自动调整

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护团队**: 海马汇技术团队
