<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hishop.starter</groupId>
        <artifactId>hishop-parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
        <relativePath />
    </parent>
    <groupId>com.hishop.himall</groupId>
    <artifactId>himall-bom</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>himall-common</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-boot</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-util</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-security</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-api</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-trade-api</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-pay-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-erp-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>